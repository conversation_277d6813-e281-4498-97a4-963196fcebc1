# File generated from CLDR ver. 25

africaConakry = {"id": "Africa/Conakry", "transitions": [], "names": ["GMT", "Greenwich Mean Time"], "std_offset": 0}
europeAmsterdam = {"id": "Europe/Amsterdam", "transitions": [63577, 60, 67777, 0, 72313, 60, 76681, 0, 81049, 60, 85417, 0, 89953, 60, 94153, 0, 98521, 60, 102889, 0, 107257, 60, 111625, 0, 115993, 60, 120361, 0, 124729, 60, 129265, 0, 133633, 60, 138001, 0, 142369, 60, 146737, 0, 151105, 60, 155473, 0, 159841, 60, 164209, 0, 168577, 60, 172945, 0, 177313, 60, 181849, 0, 186217, 60, 190585, 0, 194953, 60, 199321, 0, 203689, 60, 208057, 0, 212425, 60, 216793, 0, 221161, 60, 225529, 0, 230065, 60, 235105, 0, 238801, 60, 243841, 0, 247537, 60, 252577, 0, 256273, 60, 261481, 0, 265009, 60, 270217, 0, 273745, 60, 278953, 0, 282649, 60, 287689, 0, 291385, 60, 296425, 0, 300121, 60, 305329, 0, 308857, 60, 314065, 0, 317593, 60, 322801, 0, 326329, 60, 331537, 0, 335233, 60, 340273, 0, 343969, 60, 349009, 0, 352705, 60, 357913, 0, 361441, 60, 366649, 0, 370177, 60, 375385, 0, 379081, 60, 384121, 0, 387817, 60, 392857, 0, 396553, 60, 401593, 0, 405289, 60, 410497, 0, 414025, 60, 419233, 0, 422761, 60, 427969, 0, 431665, 60, 436705, 0, 440401, 60, 445441, 0, 449137, 60, 454345, 0, 457873, 60, 463081, 0, 466609, 60, 471817, 0, 475513, 60, 480553, 0, 484249, 60, 489289, 0, 492985, 60, 498025, 0, 501721, 60, 506929, 0, 510457, 60, 515665, 0, 519193, 60, 524401, 0, 528097, 60, 533137, 0, 536833, 60, 541873, 0, 545569, 60, 550777, 0, 554305, 60, 559513, 0, 563041, 60, 568249, 0, 571777, 60, 576985, 0, 580681, 60, 585721, 0, 589417, 60, 594457, 0], "names": ["CET", "Central European Standard Time", "CEST", "Central European Summer Time"], "std_offset": 60}
europeRome = {"id": "Europe/Rome", "transitions": [3599, 60, 6454, 0, 12167, 60, 15191, 0, 21071, 60, 24094, 0, 29975, 60, 32830, 0, 38543, 60, 41566, 0, 47447, 60, 50303, 0, 56183, 60, 59039, 0, 64751, 60, 67775, 0, 73655, 60, 76679, 0, 82391, 60, 85415, 0, 89953, 60, 94153, 0, 98521, 60, 102889, 0, 107257, 60, 111625, 0, 115993, 60, 120361, 0, 124729, 60, 129265, 0, 133633, 60, 138001, 0, 142369, 60, 146737, 0, 151105, 60, 155473, 0, 159841, 60, 164209, 0, 168577, 60, 172945, 0, 177313, 60, 181849, 0, 186217, 60, 190585, 0, 194953, 60, 199321, 0, 203689, 60, 208057, 0, 212425, 60, 216793, 0, 221161, 60, 225529, 0, 230065, 60, 235105, 0, 238801, 60, 243841, 0, 247537, 60, 252577, 0, 256273, 60, 261481, 0, 265009, 60, 270217, 0, 273745, 60, 278953, 0, 282649, 60, 287689, 0, 291385, 60, 296425, 0, 300121, 60, 305329, 0, 308857, 60, 314065, 0, 317593, 60, 322801, 0, 326329, 60, 331537, 0, 335233, 60, 340273, 0, 343969, 60, 349009, 0, 352705, 60, 357913, 0, 361441, 60, 366649, 0, 370177, 60, 375385, 0, 379081, 60, 384121, 0, 387817, 60, 392857, 0, 396553, 60, 401593, 0, 405289, 60, 410497, 0, 414025, 60, 419233, 0, 422761, 60, 427969, 0, 431665, 60, 436705, 0, 440401, 60, 445441, 0, 449137, 60, 454345, 0, 457873, 60, 463081, 0, 466609, 60, 471817, 0, 475513, 60, 480553, 0, 484249, 60, 489289, 0, 492985, 60, 498025, 0, 501721, 60, 506929, 0, 510457, 60, 515665, 0, 519193, 60, 524401, 0, 528097, 60, 533137, 0, 536833, 60, 541873, 0, 545569, 60, 550777, 0, 554305, 60, 559513, 0, 563041, 60, 568249, 0, 571777, 60, 576985, 0, 580681, 60, 585721, 0, 589417, 60, 594457, 0], "names": ["CET", "Central European Standard Time", "CEST", "Central European Summer Time"], "std_offset": 60}
mST7MDT = {"id": "MST7MDT", "transitions": [2769, 60, 7136, 0, 11505, 60, 16040, 0, 20409, 60, 24776, 0, 29145, 60, 33512, 0, 35193, 60, 42248, 0, 45105, 60, 50984, 0, 55353, 60, 59888, 0, 64089, 60, 68624, 0, 72993, 60, 77360, 0, 81729, 60, 86096, 0, 90465, 60, 94832, 0, 99201, 60, 103568, 0, 107937, 60, 112472, 0, 116673, 60, 121208, 0, 125577, 60, 129944, 0, 134313, 60, 138680, 0, 143049, 60, 147416, 0, 151281, 60, 156152, 0, 160017, 60, 165056, 0, 168753, 60, 173792, 0, 177489, 60, 182528, 0, 186393, 60, 191264, 0, 195129, 60, 200000, 0, 203865, 60, 208904, 0, 212601, 60, 217640, 0, 221337, 60, 226376, 0, 230241, 60, 235112, 0, 238977, 60, 243848, 0, 247713, 60, 252584, 0, 256449, 60, 261488, 0, 265185, 60, 270224, 0, 273921, 60, 278960, 0, 282825, 60, 287696, 0, 291561, 60, 296432, 0, 300297, 60, 305336, 0, 309033, 60, 314072, 0, 317769, 60, 322808, 0, 326001, 60, 331712, 0, 334737, 60, 340448, 0, 343473, 60, 349184, 0, 352377, 60, 358088, 0, 361113, 60, 366824, 0, 369849, 60, 375560, 0, 378585, 60, 384296, 0, 387321, 60, 393032, 0, 396057, 60, 401768, 0, 404961, 60, 410672, 0, 413697, 60, 419408, 0, 422433, 60, 428144, 0, 431169, 60, 436880, 0, 439905, 60, 445616, 0, 448809, 60, 454520, 0, 457545, 60, 463256, 0, 466281, 60, 471992, 0, 475017, 60, 480728, 0, 483753, 60, 489464, 0, 492489, 60, 498200, 0, 501393, 60, 507104, 0, 510129, 60, 515840, 0, 518865, 60, 524576, 0, 527601, 60, 533312, 0, 536337, 60, 542048, 0, 545241, 60, 550952, 0, 553977, 60, 559688, 0, 562713, 60, 568424, 0, 571449, 60, 577160, 0, 580185, 60, 585896, 0, 588921, 60, 594632, 0], "names": ["MST", "Mountain Standard Time", "MDT", "Mountain Daylight Time"], "std_offset": -420}
europeZurich = {"id": "Europe/Zurich", "transitions": [98521, 60, 102889, 0, 107257, 60, 111625, 0, 115993, 60, 120361, 0, 124729, 60, 129265, 0, 133633, 60, 138001, 0, 142369, 60, 146737, 0, 151105, 60, 155473, 0, 159841, 60, 164209, 0, 168577, 60, 172945, 0, 177313, 60, 181849, 0, 186217, 60, 190585, 0, 194953, 60, 199321, 0, 203689, 60, 208057, 0, 212425, 60, 216793, 0, 221161, 60, 225529, 0, 230065, 60, 235105, 0, 238801, 60, 243841, 0, 247537, 60, 252577, 0, 256273, 60, 261481, 0, 265009, 60, 270217, 0, 273745, 60, 278953, 0, 282649, 60, 287689, 0, 291385, 60, 296425, 0, 300121, 60, 305329, 0, 308857, 60, 314065, 0, 317593, 60, 322801, 0, 326329, 60, 331537, 0, 335233, 60, 340273, 0, 343969, 60, 349009, 0, 352705, 60, 357913, 0, 361441, 60, 366649, 0, 370177, 60, 375385, 0, 379081, 60, 384121, 0, 387817, 60, 392857, 0, 396553, 60, 401593, 0, 405289, 60, 410497, 0, 414025, 60, 419233, 0, 422761, 60, 427969, 0, 431665, 60, 436705, 0, 440401, 60, 445441, 0, 449137, 60, 454345, 0, 457873, 60, 463081, 0, 466609, 60, 471817, 0, 475513, 60, 480553, 0, 484249, 60, 489289, 0, 492985, 60, 498025, 0, 501721, 60, 506929, 0, 510457, 60, 515665, 0, 519193, 60, 524401, 0, 528097, 60, 533137, 0, 536833, 60, 541873, 0, 545569, 60, 550777, 0, 554305, 60, 559513, 0, 563041, 60, 568249, 0, 571777, 60, 576985, 0, 580681, 60, 585721, 0, 589417, 60, 594457, 0], "names": ["CET", "Central European Standard Time", "CEST", "Central European Summer Time"], "std_offset": 60}
pacificFakaofo = {"id": "Pacific/Fakaofo", "transitions": [], "names": ["TKT", "Tokelau Time"], "std_offset": 780}
asiaKuwait = {"id": "Asia/Kuwait", "transitions": [], "names": ["AST", "Arabian Standard Time", "ADT", "Arabian Daylight Time"], "std_offset": 180}
asiaBishkek = {"id": "Asia/Bishkek", "transitions": [98586, 60, 102977, 0, 107346, 60, 111737, 0, 116106, 60, 120497, 0, 124890, 60, 129260, 0, 133628, 60, 137996, 0, 142364, 60, 146732, 0, 151100, 60, 155468, 0, 159836, 60, 164204, 0, 168572, 60, 172940, 0, 177308, 60, 181844, 0, 186212, 60, 189884, 0, 195283, 60, 199314, 0, 204019, 60, 208050, 0, 212755, 60, 216786, 0, 221491, 60, 225522, 0, 230227, 60, 234426, 0, 238797, 60, 243836, 0, 247533, 60, 252572, 0, 256269, 60, 261476, 0, 265005, 60, 270212, 0, 273741, 60, 278948, 0, 282645, 60, 287684, 0, 291381, 60, 296420, 0, 300117, 60, 305324, 0, 308853, 60, 312162, 0], "names": ["KGT", "Kyrgystan Time"], "std_offset": 360}
americaCancun = {"id": "America/Cancun", "transitions": [230239, 60, 235110, 0, 238975, 60, 243846, 0, 247711, 60, 252583, 0, 256448, 60, 261487, 0, 265184, 60, 270223, 0, 274760, 60, 278287, 0, 282824, 60, 287695, 0, 291560, 60, 296431, 0, 300296, 60, 305335, 0, 309032, 60, 314071, 0, 317768, 60, 322807, 0, 326504, 60, 331543, 0, 335408, 60, 340279, 0, 344144, 60, 349015, 0, 352880, 60, 357919, 0, 361616, 60, 366655, 0, 370352, 60, 375391, 0, 379256, 60, 384127, 0, 387992, 60, 392863, 0, 396728, 60, 401599, 0, 405464, 60, 410503, 0, 414200, 60, 419239, 0, 422936, 60, 427975, 0, 431840, 60, 436711, 0, 440576, 60, 445447, 0, 449312, 60, 454351, 0, 458048, 60, 463087, 0, 466784, 60, 471823, 0, 475688, 60, 480559, 0, 484424, 60, 489295, 0, 493160, 60, 498031, 0, 501896, 60, 506935, 0, 510632, 60, 515671, 0, 519368, 60, 524407, 0, 528272, 60, 533143, 0, 537008, 60, 541879, 0, 545744, 60, 550783, 0, 554480, 60, 559519, 0, 563216, 60, 568255, 0, 571952, 60, 576991, 0, 580856, 60, 585727, 0, 589592, 60, 594463, 0], "names": ["CST", "Central Standard Time", "CDT", "Central Daylight Time"], "std_offset": -360}
europeVolgograd = {"id": "Europe/Volgograd", "transitions": [98588, 60, 102979, 0, 107348, 60, 111739, 0, 116108, 60, 120499, 0, 124892, 60, 129262, 0, 133630, 60, 137998, 0, 142366, 60, 146734, 0, 151102, 60, 155470, 0, 159838, 60, 164206, 0, 168574, 60, 172943, 0, 177311, 60, 181847, 0, 194950, 60, 199315, 0, 203687, 60, 208055, 0, 212423, 60, 216791, 0, 221159, 60, 225527, 0, 230063, 60, 235103, 0, 238799, 60, 243839, 0, 247535, 60, 252575, 0, 256271, 60, 261479, 0, 265007, 60, 270215, 0, 273743, 60, 278951, 0, 282647, 60, 287687, 0, 291383, 60, 296423, 0, 300119, 60, 305327, 0, 308855, 60, 314063, 0, 317591, 60, 322799, 0, 326327, 60, 331535, 0, 335231, 60, 340271, 0, 343967, 60, 349007, 0, 352703, 60, 357911, 0], "names": ["VOLT", "Volgograd Standard Time", "VOLST", "Volgograd Summer Time"], "std_offset": 240}
africaGaborone = {"id": "Africa/Gaborone", "transitions": [], "names": ["CAT", "Central Africa Time"], "std_offset": 120}
africaBlantyre = {"id": "Africa/Blantyre", "transitions": [], "names": ["CAT", "Central Africa Time"], "std_offset": 120}
asiaPhnomPenh = {"id": "Asia/Phnom_Penh", "transitions": [], "names": ["ICT", "Indochina Time"], "std_offset": 420}
asiaJerusalem = {"id": "Asia/Jerusalem", "transitions": [39550, 60, 41901, 0, 46438, 60, 49629, 0, 133966, 60, 137661, 0, 143542, 60, 146229, 0, 151510, 60, 155133, 0, 160150, 60, 163677, 0, 169414, 60, 172437, 0, 177310, 60, 181005, 0, 186046, 60, 189909, 0, 194950, 60, 198813, 0, 203806, 60, 207549, 0, 212542, 60, 216117, 0, 221278, 60, 225021, 0, 229678, 60, 234117, 0, 238582, 60, 242829, 0, 247318, 60, 251397, 0, 256392, 60, 260087, 0, 265464, 60, 269662, 0, 274103, 60, 278134, 0, 282599, 60, 287206, 0, 291335, 60, 295870, 0, 300359, 60, 304390, 0, 308976, 60, 313559, 0, 317712, 60, 322127, 0, 326448, 60, 330527, 0, 335184, 60, 339767, 0, 343920, 60, 348335, 0, 352656, 60, 356735, 0, 361560, 60, 365975, 0, 370296, 60, 374543, 0, 379032, 60, 383615, 0, 387768, 60, 392351, 0, 396504, 60, 401087, 0, 405240, 60, 409823, 0, 413976, 60, 418727, 0, 422712, 60, 427463, 0, 431616, 60, 436199, 0, 440352, 60, 444935, 0, 449088, 60, 453671, 0, 457824, 60, 462407, 0, 466560, 60, 471311, 0, 475464, 60, 480047, 0, 484200, 60, 488783, 0, 492936, 60, 497519, 0, 501672, 60, 506279, 0, 510408, 60, 515159, 0, 519144, 60, 523895, 0, 528048, 60, 532631, 0, 536784, 60, 541367, 0, 545520, 60, 550103, 0, 554256, 60, 558839, 0, 562992, 60, 567743, 0, 571728, 60, 576479, 0, 580632, 60, 585215, 0, 589368, 60, 593951, 0], "names": ["IST", "Israel Standard Time", "IDT", "Israel Daylight Time"], "std_offset": 120}
europeSarajevo = {"id": "Europe/Sarajevo", "transitions": [115993, 60, 120361, 0, 124729, 60, 129265, 0, 133633, 60, 138001, 0, 142369, 60, 146737, 0, 151105, 60, 155473, 0, 159841, 60, 164209, 0, 168577, 60, 172945, 0, 177313, 60, 181849, 0, 186217, 60, 190585, 0, 194953, 60, 199321, 0, 203689, 60, 208057, 0, 212425, 60, 216793, 0, 221161, 60, 225529, 0, 230065, 60, 235105, 0, 238801, 60, 243841, 0, 247537, 60, 252577, 0, 256273, 60, 261481, 0, 265009, 60, 270217, 0, 273745, 60, 278953, 0, 282649, 60, 287689, 0, 291385, 60, 296425, 0, 300121, 60, 305329, 0, 308857, 60, 314065, 0, 317593, 60, 322801, 0, 326329, 60, 331537, 0, 335233, 60, 340273, 0, 343969, 60, 349009, 0, 352705, 60, 357913, 0, 361441, 60, 366649, 0, 370177, 60, 375385, 0, 379081, 60, 384121, 0, 387817, 60, 392857, 0, 396553, 60, 401593, 0, 405289, 60, 410497, 0, 414025, 60, 419233, 0, 422761, 60, 427969, 0, 431665, 60, 436705, 0, 440401, 60, 445441, 0, 449137, 60, 454345, 0, 457873, 60, 463081, 0, 466609, 60, 471817, 0, 475513, 60, 480553, 0, 484249, 60, 489289, 0, 492985, 60, 498025, 0, 501721, 60, 506929, 0, 510457, 60, 515665, 0, 519193, 60, 524401, 0, 528097, 60, 533137, 0, 536833, 60, 541873, 0, 545569, 60, 550777, 0, 554305, 60, 559513, 0, 563041, 60, 568249, 0, 571777, 60, 576985, 0, 580681, 60, 585721, 0, 589417, 60, 594457, 0], "names": ["CET", "Central European Standard Time", "CEST", "Central European Summer Time"], "std_offset": 60}
antarcticaSyowa = {"id": "Antarctica/Syowa", "transitions": [], "names": ["SYOT", "Syowa Time"], "std_offset": 180}
americaBelize = {"id": "America/Belize", "transitions": [34422, 60, 36005, 0, 113622, 60, 114965, 0], "names": ["CST", "Central Standard Time", "CDT", "Central Daylight Time"], "std_offset": -360}
europeCopenhagen = {"id": "Europe/Copenhagen", "transitions": [89953, 60, 94153, 0, 98521, 60, 102889, 0, 107257, 60, 111625, 0, 115993, 60, 120361, 0, 124729, 60, 129265, 0, 133633, 60, 138001, 0, 142369, 60, 146737, 0, 151105, 60, 155473, 0, 159841, 60, 164209, 0, 168577, 60, 172945, 0, 177313, 60, 181849, 0, 186217, 60, 190585, 0, 194953, 60, 199321, 0, 203689, 60, 208057, 0, 212425, 60, 216793, 0, 221161, 60, 225529, 0, 230065, 60, 235105, 0, 238801, 60, 243841, 0, 247537, 60, 252577, 0, 256273, 60, 261481, 0, 265009, 60, 270217, 0, 273745, 60, 278953, 0, 282649, 60, 287689, 0, 291385, 60, 296425, 0, 300121, 60, 305329, 0, 308857, 60, 314065, 0, 317593, 60, 322801, 0, 326329, 60, 331537, 0, 335233, 60, 340273, 0, 343969, 60, 349009, 0, 352705, 60, 357913, 0, 361441, 60, 366649, 0, 370177, 60, 375385, 0, 379081, 60, 384121, 0, 387817, 60, 392857, 0, 396553, 60, 401593, 0, 405289, 60, 410497, 0, 414025, 60, 419233, 0, 422761, 60, 427969, 0, 431665, 60, 436705, 0, 440401, 60, 445441, 0, 449137, 60, 454345, 0, 457873, 60, 463081, 0, 466609, 60, 471817, 0, 475513, 60, 480553, 0, 484249, 60, 489289, 0, 492985, 60, 498025, 0, 501721, 60, 506929, 0, 510457, 60, 515665, 0, 519193, 60, 524401, 0, 528097, 60, 533137, 0, 536833, 60, 541873, 0, 545569, 60, 550777, 0, 554305, 60, 559513, 0, 563041, 60, 568249, 0, 571777, 60, 576985, 0, 580681, 60, 585721, 0, 589417, 60, 594457, 0], "names": ["CET", "Central European Standard Time", "CEST", "Central European Summer Time"], "std_offset": 60}
americaArgentinaRioGallegos = {"id": "America/Argentina/Rio_Gallegos", "transitions": [35595, 60, 37946, 0, 165819, 60, 168074, 0, 173451, 60, 176810, 0, 182355, 60, 185546, 0, 191091, 60, 194282, 0, 199827, 60, 203186, 0, 260811, 60, 264459, 0, 333051, 60, 334898, 0], "names": ["ART", "Argentina Standard Time", "ARST", "Argentina Summer Time"], "std_offset": -180}
indianCocos = {"id": "Indian/Cocos", "transitions": [], "names": ["CCT", "Cocos Islands Time"], "std_offset": 390}
antarcticaDavis = {"id": "Antarctica/Davis", "transitions": [], "names": ["DAVT", "Davis Time"], "std_offset": 420}
americaHermosillo = {"id": "America/Hermosillo", "transitions": [230241, 60, 235112, 0, 238977, 60, 243848, 0, 247713, 60, 252584, 0], "names": ["MST", "Mexican Pacific Standard Time", "MDT", "Mexican Pacific Daylight Time"], "std_offset": -420}
antarcticaVostok = {"id": "Antarctica/Vostok", "transitions": [], "names": ["VOST", "Vostok Time"], "std_offset": 360}
africaHarare = {"id": "Africa/Harare", "transitions": [], "names": ["CAT", "Central Africa Time"], "std_offset": 120}
americaDawson = {"id": "America/Dawson", "transitions": [90466, 60, 94833, 0, 99202, 60, 103569, 0, 107938, 60, 112473, 0, 116674, 60, 121209, 0, 125578, 60, 129945, 0, 134314, 60, 138681, 0, 143050, 60, 147417, 0, 151282, 60, 156153, 0, 160018, 60, 165057, 0, 168754, 60, 173793, 0, 177490, 60, 182529, 0, 186394, 60, 191265, 0, 195130, 60, 200001, 0, 203866, 60, 208905, 0, 212602, 60, 217641, 0, 221338, 60, 226377, 0, 230242, 60, 235113, 0, 238978, 60, 243849, 0, 247714, 60, 252585, 0, 256450, 60, 261489, 0, 265186, 60, 270225, 0, 273922, 60, 278961, 0, 282826, 60, 287697, 0, 291562, 60, 296433, 0, 300298, 60, 305337, 0, 309034, 60, 314073, 0, 317770, 60, 322809, 0, 326002, 60, 331713, 0, 334738, 60, 340449, 0, 343474, 60, 349185, 0, 352378, 60, 358089, 0, 361114, 60, 366825, 0, 369850, 60, 375561, 0, 378586, 60, 384297, 0, 387322, 60, 393033, 0, 396058, 60, 401769, 0, 404962, 60, 410673, 0, 413698, 60, 419409, 0, 422434, 60, 428145, 0, 431170, 60, 436881, 0, 439906, 60, 445617, 0, 448810, 60, 454521, 0, 457546, 60, 463257, 0, 466282, 60, 471993, 0, 475018, 60, 480729, 0, 483754, 60, 489465, 0, 492490, 60, 498201, 0, 501394, 60, 507105, 0, 510130, 60, 515841, 0, 518866, 60, 524577, 0, 527602, 60, 533313, 0, 536338, 60, 542049, 0, 545242, 60, 550953, 0, 553978, 60, 559689, 0, 562714, 60, 568425, 0, 571450, 60, 577161, 0, 580186, 60, 585897, 0, 588922, 60, 594633, 0], "names": ["PST", "Pacific Standard Time", "PDT", "Pacific Daylight Time"], "std_offset": -480}
americaMonterrey = {"id": "America/Monterrey", "transitions": [160016, 60, 165055, 0, 230240, 60, 235111, 0, 238976, 60, 243847, 0, 247712, 60, 252583, 0, 256448, 60, 261487, 0, 265184, 60, 270223, 0, 274760, 60, 278287, 0, 282824, 60, 287695, 0, 291560, 60, 296431, 0, 300296, 60, 305335, 0, 309032, 60, 314071, 0, 317768, 60, 322807, 0, 326504, 60, 331543, 0, 335408, 60, 340279, 0, 344144, 60, 349015, 0, 352880, 60, 357919, 0, 361616, 60, 366655, 0, 370352, 60, 375391, 0, 379256, 60, 384127, 0, 387992, 60, 392863, 0, 396728, 60, 401599, 0, 405464, 60, 410503, 0, 414200, 60, 419239, 0, 422936, 60, 427975, 0, 431840, 60, 436711, 0, 440576, 60, 445447, 0, 449312, 60, 454351, 0, 458048, 60, 463087, 0, 466784, 60, 471823, 0, 475688, 60, 480559, 0, 484424, 60, 489295, 0, 493160, 60, 498031, 0, 501896, 60, 506935, 0, 510632, 60, 515671, 0, 519368, 60, 524407, 0, 528272, 60, 533143, 0, 537008, 60, 541879, 0, 545744, 60, 550783, 0, 554480, 60, 559519, 0, 563216, 60, 568255, 0, 571952, 60, 576991, 0, 580856, 60, 585727, 0, 589592, 60, 594463, 0], "names": ["CST", "Central Standard Time", "CDT", "Central Daylight Time"], "std_offset": -360}
africaBissau = {"id": "Africa/Bissau", "transitions": [], "names": ["GMT", "Greenwich Mean Time"], "std_offset": 0}
asiaNovokuznetsk = {"id": "Asia/Novokuznetsk", "transitions": [98585, 60, 102976, 0, 107345, 60, 111736, 0, 116105, 60, 120496, 0, 124889, 60, 129259, 0, 133627, 60, 137995, 0, 142363, 60, 146731, 0, 151099, 60, 155467, 0, 159835, 60, 164203, 0, 168571, 60, 172939, 0, 177307, 60, 181843, 0, 186211, 60, 190580, 0, 194944, 60, 199311, 0, 203683, 60, 208051, 0, 212419, 60, 216787, 0, 221155, 60, 225523, 0, 230059, 60, 235099, 0, 238795, 60, 243835, 0, 247531, 60, 252571, 0, 256267, 60, 261475, 0, 265003, 60, 270211, 0, 273739, 60, 278947, 0, 282643, 60, 287683, 0, 291379, 60, 296419, 0, 300115, 60, 305323, 0, 308851, 60, 314059, 0, 317587, 60, 322795, 0, 326323, 60, 331531, 0, 335227, 60, 340267, 0, 343963, 60, 349003, 0, 352699, 60, 357908, 0], "names": ["NOVT", "Novosibirsk Standard Time", "NOVST", "Novosibirsk Summer Time"], "std_offset": 420}
pacificAuckland = {"id": "Pacific/Auckland", "transitions": [42398, 60, 45086, 0, 50966, 60, 54158, 0, 59870, 60, 62894, 0, 68606, 60, 71630, 0, 77342, 60, 80366, 0, 86078, 60, 89102, 0, 94814, 60, 97838, 0, 103550, 60, 106742, 0, 112454, 60, 115478, 0, 121190, 60, 124214, 0, 129926, 60, 132950, 0, 138662, 60, 141686, 0, 147398, 60, 150422, 0, 156134, 60, 159326, 0, 165038, 60, 168062, 0, 173270, 60, 177134, 0, 182006, 60, 185870, 0, 190742, 60, 194606, 0, 199478, 60, 203510, 0, 208214, 60, 212246, 0, 216950, 60, 220982, 0, 225686, 60, 229718, 0, 234590, 60, 238454, 0, 243326, 60, 247190, 0, 252062, 60, 256094, 0, 260798, 60, 264830, 0, 269534, 60, 273566, 0, 278438, 60, 282302, 0, 287174, 60, 291038, 0, 295910, 60, 299942, 0, 304646, 60, 308678, 0, 313382, 60, 317414, 0, 322118, 60, 326150, 0, 330854, 60, 335390, 0, 339590, 60, 344126, 0, 348326, 60, 352862, 0, 357062, 60, 361598, 0, 365798, 60, 370334, 0, 374702, 60, 379238, 0, 383438, 60, 387974, 0, 392174, 60, 396710, 0, 400910, 60, 405446, 0, 409646, 60, 414182, 0, 418382, 60, 422918, 0, 427286, 60, 431822, 0, 436022, 60, 440558, 0, 444758, 60, 449294, 0, 453494, 60, 458030, 0, 462230, 60, 466766, 0, 470966, 60, 475670, 0, 479870, 60, 484406, 0, 488606, 60, 493142, 0, 497342, 60, 501878, 0, 506078, 60, 510614, 0, 514814, 60, 519350, 0, 523718, 60, 528254, 0, 532454, 60, 536990, 0, 541190, 60, 545726, 0, 549926, 60, 554462, 0, 558662, 60, 563198, 0, 567398, 60, 571934, 0, 576302, 60, 580838, 0, 585038, 60, 589574, 0, 593774, 60], "names": ["NZST", "New Zealand Standard Time", "NZDT", "New Zealand Daylight Time"], "std_offset": 720}
americaThunderBay = {"id": "America/Thunder_Bay", "transitions": [2767, 60, 7134, 0, 11503, 60, 16038, 0, 20407, 60, 24774, 0, 37879, 60, 42246, 0, 46615, 60, 50982, 0, 55351, 60, 59886, 0, 64087, 60, 68622, 0, 72991, 60, 77358, 0, 81727, 60, 86094, 0, 90463, 60, 94830, 0, 99199, 60, 103566, 0, 107935, 60, 112470, 0, 116671, 60, 121206, 0, 125575, 60, 129942, 0, 134311, 60, 138678, 0, 143047, 60, 147414, 0, 151279, 60, 156150, 0, 160015, 60, 165054, 0, 168751, 60, 173790, 0, 177487, 60, 182526, 0, 186391, 60, 191262, 0, 195127, 60, 199998, 0, 203863, 60, 208902, 0, 212599, 60, 217638, 0, 221335, 60, 226374, 0, 230239, 60, 235110, 0, 238975, 60, 243846, 0, 247711, 60, 252582, 0, 256447, 60, 261486, 0, 265183, 60, 270222, 0, 273919, 60, 278958, 0, 282823, 60, 287694, 0, 291559, 60, 296430, 0, 300295, 60, 305334, 0, 309031, 60, 314070, 0, 317767, 60, 322806, 0, 325999, 60, 331710, 0, 334735, 60, 340446, 0, 343471, 60, 349182, 0, 352375, 60, 358086, 0, 361111, 60, 366822, 0, 369847, 60, 375558, 0, 378583, 60, 384294, 0, 387319, 60, 393030, 0, 396055, 60, 401766, 0, 404959, 60, 410670, 0, 413695, 60, 419406, 0, 422431, 60, 428142, 0, 431167, 60, 436878, 0, 439903, 60, 445614, 0, 448807, 60, 454518, 0, 457543, 60, 463254, 0, 466279, 60, 471990, 0, 475015, 60, 480726, 0, 483751, 60, 489462, 0, 492487, 60, 498198, 0, 501391, 60, 507102, 0, 510127, 60, 515838, 0, 518863, 60, 524574, 0, 527599, 60, 533310, 0, 536335, 60, 542046, 0, 545239, 60, 550950, 0, 553975, 60, 559686, 0, 562711, 60, 568422, 0, 571447, 60, 577158, 0, 580183, 60, 585894, 0, 588919, 60, 594630, 0], "names": ["EST", "Eastern Standard Time", "EDT", "Eastern Daylight Time"], "std_offset": -300}
europeOslo = {"id": "Europe/Oslo", "transitions": [89953, 60, 94153, 0, 98521, 60, 102889, 0, 107257, 60, 111625, 0, 115993, 60, 120361, 0, 124729, 60, 129265, 0, 133633, 60, 138001, 0, 142369, 60, 146737, 0, 151105, 60, 155473, 0, 159841, 60, 164209, 0, 168577, 60, 172945, 0, 177313, 60, 181849, 0, 186217, 60, 190585, 0, 194953, 60, 199321, 0, 203689, 60, 208057, 0, 212425, 60, 216793, 0, 221161, 60, 225529, 0, 230065, 60, 235105, 0, 238801, 60, 243841, 0, 247537, 60, 252577, 0, 256273, 60, 261481, 0, 265009, 60, 270217, 0, 273745, 60, 278953, 0, 282649, 60, 287689, 0, 291385, 60, 296425, 0, 300121, 60, 305329, 0, 308857, 60, 314065, 0, 317593, 60, 322801, 0, 326329, 60, 331537, 0, 335233, 60, 340273, 0, 343969, 60, 349009, 0, 352705, 60, 357913, 0, 361441, 60, 366649, 0, 370177, 60, 375385, 0, 379081, 60, 384121, 0, 387817, 60, 392857, 0, 396553, 60, 401593, 0, 405289, 60, 410497, 0, 414025, 60, 419233, 0, 422761, 60, 427969, 0, 431665, 60, 436705, 0, 440401, 60, 445441, 0, 449137, 60, 454345, 0, 457873, 60, 463081, 0, 466609, 60, 471817, 0, 475513, 60, 480553, 0, 484249, 60, 489289, 0, 492985, 60, 498025, 0, 501721, 60, 506929, 0, 510457, 60, 515665, 0, 519193, 60, 524401, 0, 528097, 60, 533137, 0, 536833, 60, 541873, 0, 545569, 60, 550777, 0, 554305, 60, 559513, 0, 563041, 60, 568249, 0, 571777, 60, 576985, 0, 580681, 60, 585721, 0, 589417, 60, 594457, 0], "names": ["CET", "Central European Standard Time", "CEST", "Central European Summer Time"], "std_offset": 60}
pacificFiji = {"id": "Pacific/Fiji", "transitions": [252734, 60, 255590, 0, 261638, 60, 264326, 0, 349838, 60, 352694, 0, 357734, 60, 360926, 0, 366470, 60, 368654, 0, 375206, 60, 377390, 0, 383942, 60, 386126, 0, 392678, 60, 394862, 0, 401414, 60, 403766, 0, 410318, 60, 412502, 0, 419054, 60, 421238, 0, 427790, 60, 429974, 0, 436526, 60, 438710, 0, 445262, 60, 447614, 0, 454166, 60, 456350, 0, 462902, 60, 465086, 0, 471638, 60, 473822, 0, 480374, 60, 482558, 0, 489110, 60, 491294, 0, 497846, 60, 500198, 0, 506750, 60, 508934, 0, 515486, 60, 517670, 0, 524222, 60, 526406, 0, 532958, 60, 535142, 0, 541694, 60, 543878, 0, 550598, 60, 552782, 0, 559334, 60, 561518, 0, 568070, 60, 570254, 0, 576806, 60, 578990, 0, 585542, 60, 587726, 0, 594278, 60], "names": ["FJT", "Fiji Standard Time", "FJST", "Fiji Summer Time"], "std_offset": 720}
americaLowerPrinces = {"id": "America/Lower_Princes", "transitions": [], "names": ["AST", "Atlantic Standard Time", "ADT", "Atlantic Daylight Time"], "std_offset": -240}
americaBoise = {"id": "America/Boise", "transitions": [2769, 60, 7136, 0, 11505, 60, 16040, 0, 20409, 60, 24776, 0, 29145, 60, 33512, 0, 35865, 60, 42248, 0, 45105, 60, 50984, 0, 55353, 60, 59888, 0, 64089, 60, 68624, 0, 72993, 60, 77360, 0, 81729, 60, 86096, 0, 90465, 60, 94832, 0, 99201, 60, 103568, 0, 107937, 60, 112472, 0, 116673, 60, 121208, 0, 125577, 60, 129944, 0, 134313, 60, 138680, 0, 143049, 60, 147416, 0, 151281, 60, 156152, 0, 160017, 60, 165056, 0, 168753, 60, 173792, 0, 177489, 60, 182528, 0, 186393, 60, 191264, 0, 195129, 60, 200000, 0, 203865, 60, 208904, 0, 212601, 60, 217640, 0, 221337, 60, 226376, 0, 230241, 60, 235112, 0, 238977, 60, 243848, 0, 247713, 60, 252584, 0, 256449, 60, 261488, 0, 265185, 60, 270224, 0, 273921, 60, 278960, 0, 282825, 60, 287696, 0, 291561, 60, 296432, 0, 300297, 60, 305336, 0, 309033, 60, 314072, 0, 317769, 60, 322808, 0, 326001, 60, 331712, 0, 334737, 60, 340448, 0, 343473, 60, 349184, 0, 352377, 60, 358088, 0, 361113, 60, 366824, 0, 369849, 60, 375560, 0, 378585, 60, 384296, 0, 387321, 60, 393032, 0, 396057, 60, 401768, 0, 404961, 60, 410672, 0, 413697, 60, 419408, 0, 422433, 60, 428144, 0, 431169, 60, 436880, 0, 439905, 60, 445616, 0, 448809, 60, 454520, 0, 457545, 60, 463256, 0, 466281, 60, 471992, 0, 475017, 60, 480728, 0, 483753, 60, 489464, 0, 492489, 60, 498200, 0, 501393, 60, 507104, 0, 510129, 60, 515840, 0, 518865, 60, 524576, 0, 527601, 60, 533312, 0, 536337, 60, 542048, 0, 545241, 60, 550952, 0, 553977, 60, 559688, 0, 562713, 60, 568424, 0, 571449, 60, 577160, 0, 580185, 60, 585896, 0, 588921, 60, 594632, 0], "names": ["MST", "Mountain Standard Time", "MDT", "Mountain Daylight Time"], "std_offset": -420}
europeBerlin = {"id": "Europe/Berlin", "transitions": [89953, 60, 94153, 0, 98521, 60, 102889, 0, 107257, 60, 111625, 0, 115993, 60, 120361, 0, 124729, 60, 129265, 0, 133633, 60, 138001, 0, 142369, 60, 146737, 0, 151105, 60, 155473, 0, 159841, 60, 164209, 0, 168577, 60, 172945, 0, 177313, 60, 181849, 0, 186217, 60, 190585, 0, 194953, 60, 199321, 0, 203689, 60, 208057, 0, 212425, 60, 216793, 0, 221161, 60, 225529, 0, 230065, 60, 235105, 0, 238801, 60, 243841, 0, 247537, 60, 252577, 0, 256273, 60, 261481, 0, 265009, 60, 270217, 0, 273745, 60, 278953, 0, 282649, 60, 287689, 0, 291385, 60, 296425, 0, 300121, 60, 305329, 0, 308857, 60, 314065, 0, 317593, 60, 322801, 0, 326329, 60, 331537, 0, 335233, 60, 340273, 0, 343969, 60, 349009, 0, 352705, 60, 357913, 0, 361441, 60, 366649, 0, 370177, 60, 375385, 0, 379081, 60, 384121, 0, 387817, 60, 392857, 0, 396553, 60, 401593, 0, 405289, 60, 410497, 0, 414025, 60, 419233, 0, 422761, 60, 427969, 0, 431665, 60, 436705, 0, 440401, 60, 445441, 0, 449137, 60, 454345, 0, 457873, 60, 463081, 0, 466609, 60, 471817, 0, 475513, 60, 480553, 0, 484249, 60, 489289, 0, 492985, 60, 498025, 0, 501721, 60, 506929, 0, 510457, 60, 515665, 0, 519193, 60, 524401, 0, 528097, 60, 533137, 0, 536833, 60, 541873, 0, 545569, 60, 550777, 0, 554305, 60, 559513, 0, 563041, 60, 568249, 0, 571777, 60, 576985, 0, 580681, 60, 585721, 0, 589417, 60, 594457, 0], "names": ["CET", "Central European Standard Time", "CEST", "Central European Summer Time"], "std_offset": 60}
pacificApia = {"id": "Pacific/Apia", "transitions": [357083, 60, 361598, 0, 365798, 60, 370334, 0, 374702, 60, 379238, 0, 383438, 60, 387974, 0, 392174, 60, 396710, 0, 400910, 60, 405446, 0, 409646, 60, 414182, 0, 418382, 60, 422918, 0, 427286, 60, 431822, 0, 436022, 60, 440558, 0, 444758, 60, 449294, 0, 453494, 60, 458030, 0, 462230, 60, 466766, 0, 470966, 60, 475670, 0, 479870, 60, 484406, 0, 488606, 60, 493142, 0, 497342, 60, 501878, 0, 506078, 60, 510614, 0, 514814, 60, 519350, 0, 523718, 60, 528254, 0, 532454, 60, 536990, 0, 541190, 60, 545726, 0, 549926, 60, 554462, 0, 558662, 60, 563198, 0, 567398, 60, 571934, 0, 576302, 60, 580838, 0, 585038, 60, 589574, 0, 593774, 60], "names": ["SST", "Samoa Standard Time", "Samoa Daylight Time", "Samoa Daylight Time"], "std_offset": 780}
asiaKashgar = {"id": "Asia/Kashgar", "transitions": [143200, 60, 146391, 0, 151432, 60, 155127, 0, 160168, 60, 163863, 0, 169072, 60, 172767, 0, 177808, 60, 181503, 0, 186544, 60, 190239, 0], "names": ["CST", "China Standard Time", "CDT", "China Daylight Time"], "std_offset": 480}
indianMauritius = {"id": "Indian/Mauritius", "transitions": [111956, 60, 115843, 0, 340270, 60, 343965, 0], "names": ["MUT", "Mauritius Standard Time", "MUST", "Mauritius Summer Time"], "std_offset": 240}
americaIndianaWinamac = {"id": "America/Indiana/Winamac", "transitions": [2767, 60, 7134, 0, 317767, 60, 322807, 0, 334735, 60, 340446, 0, 343471, 60, 349182, 0, 352375, 60, 358086, 0, 361111, 60, 366822, 0, 369847, 60, 375558, 0, 378583, 60, 384294, 0, 387319, 60, 393030, 0, 396055, 60, 401766, 0, 404959, 60, 410670, 0, 413695, 60, 419406, 0, 422431, 60, 428142, 0, 431167, 60, 436878, 0, 439903, 60, 445614, 0, 448807, 60, 454518, 0, 457543, 60, 463254, 0, 466279, 60, 471990, 0, 475015, 60, 480726, 0, 483751, 60, 489462, 0, 492487, 60, 498198, 0, 501391, 60, 507102, 0, 510127, 60, 515838, 0, 518863, 60, 524574, 0, 527599, 60, 533310, 0, 536335, 60, 542046, 0, 545239, 60, 550950, 0, 553975, 60, 559686, 0, 562711, 60, 568422, 0, 571447, 60, 577158, 0, 580183, 60, 585894, 0, 588919, 60, 594630, 0], "names": ["EST", "Eastern Standard Time", "EDT", "Eastern Daylight Time"], "std_offset": -300}
africaMaseru = {"id": "Africa/Maseru", "transitions": [], "names": ["SAST", "South Africa Standard Time"], "std_offset": 120}
africaNiamey = {"id": "Africa/Niamey", "transitions": [], "names": ["WAT", "West Africa Standard Time", "WAST", "West Africa Summer Time"], "std_offset": 60}
asiaKrasnoyarsk = {"id": "Asia/Krasnoyarsk", "transitions": [98585, 60, 102976, 0, 107345, 60, 111736, 0, 116105, 60, 120496, 0, 124889, 60, 129259, 0, 133627, 60, 137995, 0, 142363, 60, 146731, 0, 151099, 60, 155467, 0, 159835, 60, 164203, 0, 168571, 60, 172939, 0, 177307, 60, 181843, 0, 186211, 60, 190580, 0, 194944, 60, 199311, 0, 203683, 60, 208051, 0, 212419, 60, 216787, 0, 221155, 60, 225523, 0, 230059, 60, 235099, 0, 238795, 60, 243835, 0, 247531, 60, 252571, 0, 256267, 60, 261475, 0, 265003, 60, 270211, 0, 273739, 60, 278947, 0, 282643, 60, 287683, 0, 291379, 60, 296419, 0, 300115, 60, 305323, 0, 308851, 60, 314059, 0, 317587, 60, 322795, 0, 326323, 60, 331531, 0, 335227, 60, 340267, 0, 343963, 60, 349003, 0, 352699, 60, 357907, 0], "names": ["KRAT", "Krasnoyarsk Standard Time", "KRAST", "Krasnoyarsk Summer Time"], "std_offset": 480}
europeBrussels = {"id": "Europe/Brussels", "transitions": [63577, 60, 67777, 0, 72313, 60, 76681, 0, 81049, 60, 85417, 0, 89953, 60, 94153, 0, 98521, 60, 102889, 0, 107257, 60, 111625, 0, 115993, 60, 120361, 0, 124729, 60, 129265, 0, 133633, 60, 138001, 0, 142369, 60, 146737, 0, 151105, 60, 155473, 0, 159841, 60, 164209, 0, 168577, 60, 172945, 0, 177313, 60, 181849, 0, 186217, 60, 190585, 0, 194953, 60, 199321, 0, 203689, 60, 208057, 0, 212425, 60, 216793, 0, 221161, 60, 225529, 0, 230065, 60, 235105, 0, 238801, 60, 243841, 0, 247537, 60, 252577, 0, 256273, 60, 261481, 0, 265009, 60, 270217, 0, 273745, 60, 278953, 0, 282649, 60, 287689, 0, 291385, 60, 296425, 0, 300121, 60, 305329, 0, 308857, 60, 314065, 0, 317593, 60, 322801, 0, 326329, 60, 331537, 0, 335233, 60, 340273, 0, 343969, 60, 349009, 0, 352705, 60, 357913, 0, 361441, 60, 366649, 0, 370177, 60, 375385, 0, 379081, 60, 384121, 0, 387817, 60, 392857, 0, 396553, 60, 401593, 0, 405289, 60, 410497, 0, 414025, 60, 419233, 0, 422761, 60, 427969, 0, 431665, 60, 436705, 0, 440401, 60, 445441, 0, 449137, 60, 454345, 0, 457873, 60, 463081, 0, 466609, 60, 471817, 0, 475513, 60, 480553, 0, 484249, 60, 489289, 0, 492985, 60, 498025, 0, 501721, 60, 506929, 0, 510457, 60, 515665, 0, 519193, 60, 524401, 0, 528097, 60, 533137, 0, 536833, 60, 541873, 0, 545569, 60, 550777, 0, 554305, 60, 559513, 0, 563041, 60, 568249, 0, 571777, 60, 576985, 0, 580681, 60, 585721, 0, 589417, 60, 594457, 0], "names": ["CET", "Central European Standard Time", "CEST", "Central European Summer Time"], "std_offset": 60}
australiaCurrie = {"id": "Australia/Currie", "transitions": [16024, 60, 18880, 0, 24760, 60, 27784, 0, 33496, 60, 36520, 0, 42232, 60, 45256, 0, 50968, 60, 54160, 0, 59872, 60, 62896, 0, 68608, 60, 71632, 0, 77344, 60, 80368, 0, 86080, 60, 89104, 0, 94816, 60, 97840, 0, 103552, 60, 107248, 0, 112456, 60, 115984, 0, 121192, 60, 124216, 0, 129928, 60, 132952, 0, 138664, 60, 141688, 0, 147232, 60, 150760, 0, 156136, 60, 159664, 0, 165040, 60, 168400, 0, 173776, 60, 177136, 0, 182512, 60, 186208, 0, 190744, 60, 194944, 0, 199480, 60, 203680, 0, 208216, 60, 212416, 0, 216952, 60, 221152, 0, 225688, 60, 230056, 0, 234592, 60, 238792, 0, 243328, 60, 247528, 0, 252064, 60, 256264, 0, 260800, 60, 265000, 0, 268696, 60, 273736, 0, 278440, 60, 282640, 0, 287176, 60, 291376, 0, 295912, 60, 300112, 0, 304648, 60, 308848, 0, 313384, 60, 317752, 0, 322120, 60, 326320, 0, 331024, 60, 335392, 0, 339760, 60, 344128, 0, 348496, 60, 352864, 0, 357232, 60, 361600, 0, 365968, 60, 370336, 0, 374872, 60, 379240, 0, 383608, 60, 387976, 0, 392344, 60, 396712, 0, 401080, 60, 405448, 0, 409816, 60, 414184, 0, 418552, 60, 422920, 0, 427456, 60, 431824, 0, 436192, 60, 440560, 0, 444928, 60, 449296, 0, 453664, 60, 458032, 0, 462400, 60, 466768, 0, 471136, 60, 475672, 0, 480040, 60, 484408, 0, 488776, 60, 493144, 0, 497512, 60, 501880, 0, 506248, 60, 510616, 0, 514984, 60, 519352, 0, 523888, 60, 528256, 0, 532624, 60, 536992, 0, 541360, 60, 545728, 0, 550096, 60, 554464, 0, 558832, 60, 563200, 0, 567568, 60, 571936, 0, 576472, 60, 580840, 0, 585208, 60, 589576, 0, 593944, 60], "names": ["AEST", "Australian Eastern Standard Time", "AEDT", "Australian Eastern Daylight Time"], "std_offset": 600}
americaFortaleza = {"id": "America/Fortaleza", "transitions": [138819, 60, 142010, 0, 147387, 60, 150074, 0, 156147, 60, 158666, 0, 164715, 60, 167234, 0, 173451, 60, 176306, 0, 260811, 60, 264338, 0, 269715, 60, 270050, 0, 278619, 60, 281642, 0], "names": ["BRT", "Brasilia Standard Time", "BRST", "Brasilia Summer Time"], "std_offset": -180}
eST5EDT = {"id": "EST5EDT", "transitions": [2767, 60, 7134, 0, 11503, 60, 16038, 0, 20407, 60, 24774, 0, 29143, 60, 33510, 0, 35191, 60, 42246, 0, 45103, 60, 50982, 0, 55351, 60, 59886, 0, 64087, 60, 68622, 0, 72991, 60, 77358, 0, 81727, 60, 86094, 0, 90463, 60, 94830, 0, 99199, 60, 103566, 0, 107935, 60, 112470, 0, 116671, 60, 121206, 0, 125575, 60, 129942, 0, 134311, 60, 138678, 0, 143047, 60, 147414, 0, 151279, 60, 156150, 0, 160015, 60, 165054, 0, 168751, 60, 173790, 0, 177487, 60, 182526, 0, 186391, 60, 191262, 0, 195127, 60, 199998, 0, 203863, 60, 208902, 0, 212599, 60, 217638, 0, 221335, 60, 226374, 0, 230239, 60, 235110, 0, 238975, 60, 243846, 0, 247711, 60, 252582, 0, 256447, 60, 261486, 0, 265183, 60, 270222, 0, 273919, 60, 278958, 0, 282823, 60, 287694, 0, 291559, 60, 296430, 0, 300295, 60, 305334, 0, 309031, 60, 314070, 0, 317767, 60, 322806, 0, 325999, 60, 331710, 0, 334735, 60, 340446, 0, 343471, 60, 349182, 0, 352375, 60, 358086, 0, 361111, 60, 366822, 0, 369847, 60, 375558, 0, 378583, 60, 384294, 0, 387319, 60, 393030, 0, 396055, 60, 401766, 0, 404959, 60, 410670, 0, 413695, 60, 419406, 0, 422431, 60, 428142, 0, 431167, 60, 436878, 0, 439903, 60, 445614, 0, 448807, 60, 454518, 0, 457543, 60, 463254, 0, 466279, 60, 471990, 0, 475015, 60, 480726, 0, 483751, 60, 489462, 0, 492487, 60, 498198, 0, 501391, 60, 507102, 0, 510127, 60, 515838, 0, 518863, 60, 524574, 0, 527599, 60, 533310, 0, 536335, 60, 542046, 0, 545239, 60, 550950, 0, 553975, 60, 559686, 0, 562711, 60, 568422, 0, 571447, 60, 577158, 0, 580183, 60, 585894, 0, 588919, 60, 594630, 0], "names": ["EST", "Eastern Standard Time", "EDT", "Eastern Daylight Time"], "std_offset": -300}
asiaAlmaty = {"id": "Asia/Almaty", "transitions": [98586, 60, 102977, 0, 107346, 60, 111737, 0, 116106, 60, 120497, 0, 124890, 60, 129260, 0, 133628, 60, 137996, 0, 142364, 60, 146732, 0, 151100, 60, 155468, 0, 159836, 60, 164204, 0, 168572, 60, 172940, 0, 177308, 60, 181844, 0, 194945, 60, 199312, 0, 203684, 60, 208052, 0, 212420, 60, 216788, 0, 221156, 60, 225524, 0, 230060, 60, 235100, 0, 238796, 60, 243836, 0, 247532, 60, 252572, 0, 256268, 60, 261476, 0, 265004, 60, 270212, 0, 273740, 60, 278948, 0, 282644, 60, 287684, 0, 291380, 60, 296420, 0, 300116, 60, 305324, 0], "names": ["EKST", "East Kazakhstan Time"], "std_offset": 360}
americaResolute = {"id": "America/Resolute", "transitions": [90464, 60, 94831, 0, 99200, 60, 103567, 0, 107936, 60, 112471, 0, 116672, 60, 121207, 0, 125576, 60, 129943, 0, 134312, 60, 138679, 0, 143048, 60, 147415, 0, 151280, 60, 156151, 0, 160016, 60, 165055, 0, 168752, 60, 173791, 0, 177488, 60, 182527, 0, 186392, 60, 191263, 0, 195128, 60, 199999, 0, 203864, 60, 208903, 0, 212600, 60, 217639, 0, 221336, 60, 226375, 0, 230240, 60, 235111, 0, 238976, 60, 243847, 0, 247712, 60, 252583, 0, 256448, 60, 261487, 0, 265184, 60, 270223, 0, 273920, 60, 278959, 0, 282824, 60, 287695, 0, 291560, 60, 296431, 0, 300296, 60, 305335, 0, 309032, 60, 314071, 0, 317768, 60, 322807, 0, 326000, 60, 331711, 0, 334736, 60, 340447, 0, 343472, 60, 349183, 0, 352376, 60, 358087, 0, 361112, 60, 366823, 0, 369848, 60, 375559, 0, 378584, 60, 384295, 0, 387320, 60, 393031, 0, 396056, 60, 401767, 0, 404960, 60, 410671, 0, 413696, 60, 419407, 0, 422432, 60, 428143, 0, 431168, 60, 436879, 0, 439904, 60, 445615, 0, 448808, 60, 454519, 0, 457544, 60, 463255, 0, 466280, 60, 471991, 0, 475016, 60, 480727, 0, 483752, 60, 489463, 0, 492488, 60, 498199, 0, 501392, 60, 507103, 0, 510128, 60, 515839, 0, 518864, 60, 524575, 0, 527600, 60, 533311, 0, 536336, 60, 542047, 0, 545240, 60, 550951, 0, 553976, 60, 559687, 0, 562712, 60, 568423, 0, 571448, 60, 577159, 0, 580184, 60, 585895, 0, 588920, 60, 594631, 0], "names": ["CST", "Central Standard Time", "CDT", "Central Daylight Time"], "std_offset": -360}
asiaChoibalsan = {"id": "Asia/Choibalsan", "transitions": [124887, 60, 129254, 0, 133623, 60, 137990, 0, 142359, 60, 146726, 0, 151095, 60, 155462, 0, 159831, 60, 164198, 0, 168567, 60, 172934, 0, 177303, 60, 181838, 0, 186207, 60, 190574, 0, 194943, 60, 199310, 0, 203679, 60, 208046, 0, 212415, 60, 216782, 0, 221151, 60, 225518, 0, 230055, 60, 234422, 0, 238791, 60, 243158, 0, 247527, 60, 251894, 0, 274553, 60, 278248, 0, 282617, 60, 286984, 0, 291353, 60, 295720, 0, 300089, 60, 304456, 0, 308825, 60, 313192, 0, 317561, 60, 322096, 0], "names": ["CHOT", "Choibalsan Standard Time", "CHOST", "Choibalsan Summer Time"], "std_offset": 480}
africaLibreville = {"id": "Africa/Libreville", "transitions": [], "names": ["WAT", "West Africa Standard Time", "WAST", "West Africa Summer Time"], "std_offset": 60}
africaOuagadougou = {"id": "Africa/Ouagadougou", "transitions": [], "names": ["GMT", "Greenwich Mean Time"], "std_offset": 0}
africaElAaiun = {"id": "Africa/El_Aaiun", "transitions": [], "names": ["WET", "Western European Standard Time", "WEST", "Western European Summer Time"], "std_offset": 0}
pacificNauru = {"id": "Pacific/Nauru", "transitions": [], "names": ["NRT", "Nauru Time"], "std_offset": 720}
americaMaceio = {"id": "America/Maceio", "transitions": [138819, 60, 142010, 0, 147387, 60, 150074, 0, 156147, 60, 158666, 0, 164715, 60, 167234, 0, 173451, 60, 176306, 0, 226035, 60, 228890, 0, 260811, 60, 264338, 0, 269715, 60, 270050, 0, 278619, 60, 281642, 0], "names": ["BRT", "Brasilia Standard Time", "BRST", "Brasilia Summer Time"], "std_offset": -180}
australiaLordHowe = {"id": "Australia/Lord_Howe", "transitions": [138663, 30, 142023, 0, 147231, 30, 150759, 0, 156135, 30, 159663, 0, 165039, 30, 168399, 0, 173775, 30, 176799, 0, 182511, 30, 185535, 0, 191247, 30, 194271, 0, 199983, 30, 203175, 0, 208887, 30, 211911, 0, 217623, 30, 220647, 0, 226359, 30, 230055, 0, 235095, 30, 238791, 0, 243831, 30, 247527, 0, 252567, 30, 256263, 0, 261471, 30, 264999, 0, 268695, 30, 273735, 0, 278943, 30, 282639, 0, 287679, 30, 291375, 0, 296415, 30, 300111, 0, 305319, 30, 308847, 0, 314055, 30, 317751, 0, 322791, 30, 326319, 0, 331527, 30, 335391, 0, 339759, 30, 344127, 0, 348495, 30, 352863, 0, 357231, 30, 361599, 0, 365967, 30, 370335, 0, 374871, 30, 379239, 0, 383607, 30, 387975, 0, 392343, 30, 396711, 0, 401079, 30, 405447, 0, 409815, 30, 414183, 0, 418551, 30, 422919, 0, 427455, 30, 431823, 0, 436191, 30, 440559, 0, 444927, 30, 449295, 0, 453663, 30, 458031, 0, 462399, 30, 466767, 0, 471135, 30, 475671, 0, 480039, 30, 484407, 0, 488775, 30, 493143, 0, 497511, 30, 501879, 0, 506247, 30, 510615, 0, 514983, 30, 519351, 0, 523887, 30, 528255, 0, 532623, 30, 536991, 0, 541359, 30, 545727, 0, 550095, 30, 554463, 0, 558831, 30, 563199, 0, 567567, 30, 571935, 0, 576471, 30, 580839, 0, 585207, 30, 589575, 0, 593943, 30], "names": ["LHST", "Lord Howe Standard Time", "LHDT", "Lord Howe Daylight Time"], "std_offset": 630}
americaManagua = {"id": "America/Managua", "transitions": [80718, 60, 83093, 0, 89454, 60, 91829, 0, 309198, 60, 313397, 0, 318440, 60, 322134, 0], "names": ["CST", "Central Standard Time", "CDT", "Central Daylight Time"], "std_offset": -360}
americaCayman = {"id": "America/Cayman", "transitions": [], "names": ["EST", "Eastern Standard Time", "EDT", "Eastern Daylight Time"], "std_offset": -300}
americaWhitehorse = {"id": "America/Whitehorse", "transitions": [90466, 60, 94833, 0, 99202, 60, 103569, 0, 107938, 60, 112473, 0, 116674, 60, 121209, 0, 125578, 60, 129945, 0, 134314, 60, 138681, 0, 143050, 60, 147417, 0, 151282, 60, 156153, 0, 160018, 60, 165057, 0, 168754, 60, 173793, 0, 177490, 60, 182529, 0, 186394, 60, 191265, 0, 195130, 60, 200001, 0, 203866, 60, 208905, 0, 212602, 60, 217641, 0, 221338, 60, 226377, 0, 230242, 60, 235113, 0, 238978, 60, 243849, 0, 247714, 60, 252585, 0, 256450, 60, 261489, 0, 265186, 60, 270225, 0, 273922, 60, 278961, 0, 282826, 60, 287697, 0, 291562, 60, 296433, 0, 300298, 60, 305337, 0, 309034, 60, 314073, 0, 317770, 60, 322809, 0, 326002, 60, 331713, 0, 334738, 60, 340449, 0, 343474, 60, 349185, 0, 352378, 60, 358089, 0, 361114, 60, 366825, 0, 369850, 60, 375561, 0, 378586, 60, 384297, 0, 387322, 60, 393033, 0, 396058, 60, 401769, 0, 404962, 60, 410673, 0, 413698, 60, 419409, 0, 422434, 60, 428145, 0, 431170, 60, 436881, 0, 439906, 60, 445617, 0, 448810, 60, 454521, 0, 457546, 60, 463257, 0, 466282, 60, 471993, 0, 475018, 60, 480729, 0, 483754, 60, 489465, 0, 492490, 60, 498201, 0, 501394, 60, 507105, 0, 510130, 60, 515841, 0, 518866, 60, 524577, 0, 527602, 60, 533313, 0, 536338, 60, 542049, 0, 545242, 60, 550953, 0, 553978, 60, 559689, 0, 562714, 60, 568425, 0, 571450, 60, 577161, 0, 580186, 60, 585897, 0, 588922, 60, 594633, 0], "names": ["PST", "Pacific Standard Time", "PDT", "Pacific Daylight Time"], "std_offset": -480}
americaThule = {"id": "America/Thule", "transitions": [186222, 60, 190589, 0, 194958, 60, 199325, 0, 203862, 60, 208901, 0, 212598, 60, 217637, 0, 221334, 60, 226373, 0, 230238, 60, 235109, 0, 238974, 60, 243845, 0, 247710, 60, 252581, 0, 256446, 60, 261485, 0, 265182, 60, 270221, 0, 273918, 60, 278957, 0, 282822, 60, 287693, 0, 291558, 60, 296429, 0, 300294, 60, 305333, 0, 309030, 60, 314069, 0, 317766, 60, 322805, 0, 325998, 60, 331709, 0, 334734, 60, 340445, 0, 343470, 60, 349181, 0, 352374, 60, 358085, 0, 361110, 60, 366821, 0, 369846, 60, 375557, 0, 378582, 60, 384293, 0, 387318, 60, 393029, 0, 396054, 60, 401765, 0, 404958, 60, 410669, 0, 413694, 60, 419405, 0, 422430, 60, 428141, 0, 431166, 60, 436877, 0, 439902, 60, 445613, 0, 448806, 60, 454517, 0, 457542, 60, 463253, 0, 466278, 60, 471989, 0, 475014, 60, 480725, 0, 483750, 60, 489461, 0, 492486, 60, 498197, 0, 501390, 60, 507101, 0, 510126, 60, 515837, 0, 518862, 60, 524573, 0, 527598, 60, 533309, 0, 536334, 60, 542045, 0, 545238, 60, 550949, 0, 553974, 60, 559685, 0, 562710, 60, 568421, 0, 571446, 60, 577157, 0, 580182, 60, 585893, 0, 588918, 60, 594629, 0], "names": ["AST", "Atlantic Standard Time", "ADT", "Atlantic Daylight Time"], "std_offset": -240}
europeZaporozhye = {"id": "Europe/Zaporozhye", "transitions": [98589, 60, 102980, 0, 107349, 60, 111740, 0, 116109, 60, 120500, 0, 124893, 60, 129263, 0, 133631, 60, 137999, 0, 142367, 60, 146735, 0, 151103, 60, 155471, 0, 159839, 60, 164207, 0, 168575, 60, 172943, 0, 177311, 60, 181847, 0, 186215, 60, 190581, 0, 194950, 60, 199317, 0, 203686, 60, 208053, 0, 212422, 60, 216789, 0, 221161, 60, 225529, 0, 230065, 60, 235105, 0, 238801, 60, 243841, 0, 247537, 60, 252577, 0, 256273, 60, 261481, 0, 265009, 60, 270217, 0, 273745, 60, 278953, 0, 282649, 60, 287689, 0, 291385, 60, 296425, 0, 300121, 60, 305329, 0, 308857, 60, 314065, 0, 317593, 60, 322801, 0, 326329, 60, 331537, 0, 335233, 60, 340273, 0, 343969, 60, 349009, 0, 352705, 60, 357913, 0, 361441, 60, 366649, 0, 370177, 60, 375385, 0, 379081, 60, 384121, 0, 387817, 60, 392857, 0, 396553, 60, 401593, 0, 405289, 60, 410497, 0, 414025, 60, 419233, 0, 422761, 60, 427969, 0, 431665, 60, 436705, 0, 440401, 60, 445441, 0, 449137, 60, 454345, 0, 457873, 60, 463081, 0, 466609, 60, 471817, 0, 475513, 60, 480553, 0, 484249, 60, 489289, 0, 492985, 60, 498025, 0, 501721, 60, 506929, 0, 510457, 60, 515665, 0, 519193, 60, 524401, 0, 528097, 60, 533137, 0, 536833, 60, 541873, 0, 545569, 60, 550777, 0, 554305, 60, 559513, 0, 563041, 60, 568249, 0, 571777, 60, 576985, 0, 580681, 60, 585721, 0, 589417, 60, 594457, 0], "names": ["EET", "Eastern European Standard Time", "EEST", "Eastern European Summer Time"], "std_offset": 120}
americaNorthDakotaCenter = {"id": "America/North_Dakota/Center", "transitions": [2769, 60, 7136, 0, 11505, 60, 16040, 0, 20409, 60, 24776, 0, 29145, 60, 33512, 0, 35193, 60, 42248, 0, 45105, 60, 50984, 0, 55353, 60, 59888, 0, 64089, 60, 68624, 0, 72993, 60, 77360, 0, 81729, 60, 86096, 0, 90465, 60, 94832, 0, 99201, 60, 103568, 0, 107937, 60, 112472, 0, 116673, 60, 121208, 0, 125577, 60, 129944, 0, 134313, 60, 138680, 0, 143049, 60, 147416, 0, 151281, 60, 156152, 0, 160017, 60, 165056, 0, 168753, 60, 173792, 0, 177489, 60, 182528, 0, 186393, 60, 191264, 0, 195129, 60, 200000, 0, 203864, 60, 208903, 0, 212600, 60, 217639, 0, 221336, 60, 226375, 0, 230240, 60, 235111, 0, 238976, 60, 243847, 0, 247712, 60, 252583, 0, 256448, 60, 261487, 0, 265184, 60, 270223, 0, 273920, 60, 278959, 0, 282824, 60, 287695, 0, 291560, 60, 296431, 0, 300296, 60, 305335, 0, 309032, 60, 314071, 0, 317768, 60, 322807, 0, 326000, 60, 331711, 0, 334736, 60, 340447, 0, 343472, 60, 349183, 0, 352376, 60, 358087, 0, 361112, 60, 366823, 0, 369848, 60, 375559, 0, 378584, 60, 384295, 0, 387320, 60, 393031, 0, 396056, 60, 401767, 0, 404960, 60, 410671, 0, 413696, 60, 419407, 0, 422432, 60, 428143, 0, 431168, 60, 436879, 0, 439904, 60, 445615, 0, 448808, 60, 454519, 0, 457544, 60, 463255, 0, 466280, 60, 471991, 0, 475016, 60, 480727, 0, 483752, 60, 489463, 0, 492488, 60, 498199, 0, 501392, 60, 507103, 0, 510128, 60, 515839, 0, 518864, 60, 524575, 0, 527600, 60, 533311, 0, 536336, 60, 542047, 0, 545240, 60, 550951, 0, 553976, 60, 559687, 0, 562712, 60, 568423, 0, 571448, 60, 577159, 0, 580184, 60, 585895, 0, 588920, 60, 594631, 0], "names": ["CST", "Central Standard Time", "CDT", "Central Daylight Time"], "std_offset": -360}
indianChagos = {"id": "Indian/Chagos", "transitions": [], "names": ["IOT", "Indian Ocean Time"], "std_offset": 360}
pacificMidway = {"id": "Pacific/Midway", "transitions": [], "names": ["SST", "Samoa Standard Time", "Samoa Daylight Time", "Samoa Daylight Time"], "std_offset": -660}
pacificPagoPago = {"id": "Pacific/Pago_Pago", "transitions": [], "names": ["SST", "Samoa Standard Time", "Samoa Daylight Time", "Samoa Daylight Time"], "std_offset": -660}
atlanticStHelena = {"id": "Atlantic/St_Helena", "transitions": [], "names": ["GMT", "Greenwich Mean Time"], "std_offset": 0}
europeIsleofMan = {"id": "Europe/Isle_of_Man", "transitions": [19394, 60, 24770, 0, 28130, 60, 33506, 0, 36866, 60, 42242, 0, 45602, 60, 50978, 0, 54506, 60, 59714, 0, 63242, 60, 68450, 0, 71978, 60, 77354, 0, 80714, 60, 86090, 0, 89450, 60, 94826, 0, 98521, 60, 103561, 0, 107257, 60, 112297, 0, 115993, 60, 121033, 0, 124729, 60, 129937, 0, 133633, 60, 138673, 0, 142369, 60, 147409, 0, 151105, 60, 156145, 0, 159841, 60, 164881, 0, 168577, 60, 173785, 0, 177313, 60, 182521, 0, 186217, 60, 191257, 0, 194953, 60, 199993, 0, 203689, 60, 208729, 0, 212425, 60, 217465, 0, 221161, 60, 226201, 0, 230065, 60, 235105, 0, 238801, 60, 243841, 0, 247537, 60, 252577, 0, 256273, 60, 261481, 0, 265009, 60, 270217, 0, 273745, 60, 278953, 0, 282649, 60, 287689, 0, 291385, 60, 296425, 0, 300121, 60, 305329, 0, 308857, 60, 314065, 0, 317593, 60, 322801, 0, 326329, 60, 331537, 0, 335233, 60, 340273, 0, 343969, 60, 349009, 0, 352705, 60, 357913, 0, 361441, 60, 366649, 0, 370177, 60, 375385, 0, 379081, 60, 384121, 0, 387817, 60, 392857, 0, 396553, 60, 401593, 0, 405289, 60, 410497, 0, 414025, 60, 419233, 0, 422761, 60, 427969, 0, 431665, 60, 436705, 0, 440401, 60, 445441, 0, 449137, 60, 454345, 0, 457873, 60, 463081, 0, 466609, 60, 471817, 0, 475513, 60, 480553, 0, 484249, 60, 489289, 0, 492985, 60, 498025, 0, 501721, 60, 506929, 0, 510457, 60, 515665, 0, 519193, 60, 524401, 0, 528097, 60, 533137, 0, 536833, 60, 541873, 0, 545569, 60, 550777, 0, 554305, 60, 559513, 0, 563041, 60, 568249, 0, 571777, 60, 576985, 0, 580681, 60, 585721, 0, 589417, 60, 594457, 0], "names": ["GMT", "Greenwich Mean Time"], "std_offset": 0}
atlanticSouthGeorgia = {"id": "Atlantic/South_Georgia", "transitions": [], "names": ["GST", "South Georgia Time"], "std_offset": -120}
africaCeuta = {"id": "Africa/Ceuta", "transitions": [39240, 60, 40895, 0, 55488, 60, 57695, 0, 64248, 60, 67847, 0, 73752, 60, 75287, 0, 142369, 60, 146737, 0, 151105, 60, 155473, 0, 159841, 60, 164209, 0, 168577, 60, 172945, 0, 177313, 60, 181849, 0, 186217, 60, 190585, 0, 194953, 60, 199321, 0, 203689, 60, 208057, 0, 212425, 60, 216793, 0, 221161, 60, 225529, 0, 230065, 60, 235105, 0, 238801, 60, 243841, 0, 247537, 60, 252577, 0, 256273, 60, 261481, 0, 265009, 60, 270217, 0, 273745, 60, 278953, 0, 282649, 60, 287689, 0, 291385, 60, 296425, 0, 300121, 60, 305329, 0, 308857, 60, 314065, 0, 317593, 60, 322801, 0, 326329, 60, 331537, 0, 335233, 60, 340273, 0, 343969, 60, 349009, 0, 352705, 60, 357913, 0, 361441, 60, 366649, 0, 370177, 60, 375385, 0, 379081, 60, 384121, 0, 387817, 60, 392857, 0, 396553, 60, 401593, 0, 405289, 60, 410497, 0, 414025, 60, 419233, 0, 422761, 60, 427969, 0, 431665, 60, 436705, 0, 440401, 60, 445441, 0, 449137, 60, 454345, 0, 457873, 60, 463081, 0, 466609, 60, 471817, 0, 475513, 60, 480553, 0, 484249, 60, 489289, 0, 492985, 60, 498025, 0, 501721, 60, 506929, 0, 510457, 60, 515665, 0, 519193, 60, 524401, 0, 528097, 60, 533137, 0, 536833, 60, 541873, 0, 545569, 60, 550777, 0, 554305, 60, 559513, 0, 563041, 60, 568249, 0, 571777, 60, 576985, 0, 580681, 60, 585721, 0, 589417, 60, 594457, 0], "names": ["CET", "Central European Standard Time", "CEST", "Central European Summer Time"], "std_offset": 60}
americaArgentinaSalta = {"id": "America/Argentina/Salta", "transitions": [35595, 60, 37946, 0, 165819, 60, 168074, 0, 173451, 60, 176810, 0, 182355, 60, 185546, 0, 199827, 60, 203186, 0, 333051, 60, 334898, 0], "names": ["ART", "Argentina Standard Time", "ARST", "Argentina Summer Time"], "std_offset": -180}
americaChicago = {"id": "America/Chicago", "transitions": [2768, 60, 7135, 0, 11504, 60, 16039, 0, 20408, 60, 24775, 0, 29144, 60, 33511, 0, 35192, 60, 42247, 0, 45104, 60, 50983, 0, 55352, 60, 59887, 0, 64088, 60, 68623, 0, 72992, 60, 77359, 0, 81728, 60, 86095, 0, 90464, 60, 94831, 0, 99200, 60, 103567, 0, 107936, 60, 112471, 0, 116672, 60, 121207, 0, 125576, 60, 129943, 0, 134312, 60, 138679, 0, 143048, 60, 147415, 0, 151280, 60, 156151, 0, 160016, 60, 165055, 0, 168752, 60, 173791, 0, 177488, 60, 182527, 0, 186392, 60, 191263, 0, 195128, 60, 199999, 0, 203864, 60, 208903, 0, 212600, 60, 217639, 0, 221336, 60, 226375, 0, 230240, 60, 235111, 0, 238976, 60, 243847, 0, 247712, 60, 252583, 0, 256448, 60, 261487, 0, 265184, 60, 270223, 0, 273920, 60, 278959, 0, 282824, 60, 287695, 0, 291560, 60, 296431, 0, 300296, 60, 305335, 0, 309032, 60, 314071, 0, 317768, 60, 322807, 0, 326000, 60, 331711, 0, 334736, 60, 340447, 0, 343472, 60, 349183, 0, 352376, 60, 358087, 0, 361112, 60, 366823, 0, 369848, 60, 375559, 0, 378584, 60, 384295, 0, 387320, 60, 393031, 0, 396056, 60, 401767, 0, 404960, 60, 410671, 0, 413696, 60, 419407, 0, 422432, 60, 428143, 0, 431168, 60, 436879, 0, 439904, 60, 445615, 0, 448808, 60, 454519, 0, 457544, 60, 463255, 0, 466280, 60, 471991, 0, 475016, 60, 480727, 0, 483752, 60, 489463, 0, 492488, 60, 498199, 0, 501392, 60, 507103, 0, 510128, 60, 515839, 0, 518864, 60, 524575, 0, 527600, 60, 533311, 0, 536336, 60, 542047, 0, 545240, 60, 550951, 0, 553976, 60, 559687, 0, 562712, 60, 568423, 0, 571448, 60, 577159, 0, 580184, 60, 585895, 0, 588920, 60, 594631, 0], "names": ["CST", "Central Standard Time", "CDT", "Central Daylight Time"], "std_offset": -360}
americaTortola = {"id": "America/Tortola", "transitions": [], "names": ["AST", "Atlantic Standard Time", "ADT", "Atlantic Daylight Time"], "std_offset": -240}
asiaDamascus = {"id": "Asia/Damascus", "transitions": [2880, 60, 6551, 0, 11640, 60, 15311, 0, 20424, 60, 24095, 0, 29184, 60, 32855, 0, 37944, 60, 41615, 0, 46704, 60, 50375, 0, 55488, 60, 59159, 0, 64248, 60, 67199, 0, 73008, 60, 75959, 0, 116304, 60, 120503, 0, 125088, 60, 129287, 0, 141360, 60, 146999, 0, 150432, 60, 156287, 0, 159552, 60, 165071, 0, 168696, 60, 173111, 0, 177480, 60, 181847, 0, 186238, 60, 190629, 0, 195190, 60, 199413, 0, 203638, 60, 208029, 0, 212542, 60, 216933, 0, 221302, 60, 225693, 0, 230086, 60, 234477, 0, 238822, 60, 243237, 0, 247558, 60, 251997, 0, 256366, 60, 260757, 0, 265150, 60, 269541, 0, 273910, 60, 278301, 0, 282670, 60, 287061, 0, 291430, 60, 295821, 0, 300214, 60, 304605, 0, 308974, 60, 313365, 0, 317734, 60, 321909, 0, 326446, 60, 331653, 0, 335350, 60, 340413, 0, 343918, 60, 349125, 0, 352822, 60, 357861, 0, 361558, 60, 366597, 0, 370294, 60, 375333, 0, 379030, 60, 384069, 0, 387766, 60, 392973, 0, 396502, 60, 401709, 0, 405238, 60, 410445, 0, 414142, 60, 419181, 0, 422878, 60, 427917, 0, 431614, 60, 436653, 0, 440350, 60, 445557, 0, 449086, 60, 454293, 0, 457822, 60, 463029, 0, 466726, 60, 471765, 0, 475462, 60, 480501, 0, 484198, 60, 489405, 0, 492934, 60, 498141, 0, 501670, 60, 506877, 0, 510574, 60, 515613, 0, 519310, 60, 524349, 0, 528046, 60, 533085, 0, 536782, 60, 541989, 0, 545518, 60, 550725, 0, 554254, 60, 559461, 0, 563158, 60, 568197, 0, 571894, 60, 576933, 0, 580630, 60, 585837, 0, 589366, 60, 594573, 0], "names": ["EET", "Eastern European Standard Time", "EEST", "Eastern European Summer Time"], "std_offset": 120}
americaPortoVelho = {"id": "America/Porto_Velho", "transitions": [138820, 60, 142011, 0, 147388, 60, 150075, 0, 156148, 60, 158667, 0], "names": ["AMT", "Amazon Standard Time", "AMST", "Amazon Summer Time"], "std_offset": -240}
asiaHovd = {"id": "Asia/Hovd", "transitions": [116105, 60, 120496, 0, 124889, 60, 129256, 0, 133625, 60, 137992, 0, 142361, 60, 146728, 0, 151097, 60, 155464, 0, 159833, 60, 164200, 0, 168569, 60, 172936, 0, 177305, 60, 181840, 0, 186209, 60, 190576, 0, 194945, 60, 199312, 0, 203681, 60, 208048, 0, 212417, 60, 216784, 0, 221153, 60, 225520, 0, 230057, 60, 234424, 0, 238793, 60, 243160, 0, 247529, 60, 251896, 0, 274555, 60, 278250, 0, 282619, 60, 286986, 0, 291355, 60, 295722, 0, 300091, 60, 304458, 0, 308827, 60, 313194, 0, 317563, 60, 322098, 0], "names": ["HOVT", "Hovd Standard Time", "HOVST", "Hovd Summer Time"], "std_offset": 420}
americaNome = {"id": "America/Nome", "transitions": [2773, 60, 7140, 0, 11509, 60, 16044, 0, 20413, 60, 24780, 0, 29149, 60, 33516, 0, 35197, 60, 42252, 0, 45109, 60, 50988, 0, 55357, 60, 59892, 0, 64093, 60, 68628, 0, 72997, 60, 77364, 0, 81733, 60, 86100, 0, 90469, 60, 94836, 0, 99205, 60, 103572, 0, 107941, 60, 112476, 0, 116677, 60, 121212, 0, 125579, 60, 129946, 0, 134315, 60, 138682, 0, 143051, 60, 147418, 0, 151283, 60, 156154, 0, 160019, 60, 165058, 0, 168755, 60, 173794, 0, 177491, 60, 182530, 0, 186395, 60, 191266, 0, 195131, 60, 200002, 0, 203867, 60, 208906, 0, 212603, 60, 217642, 0, 221339, 60, 226378, 0, 230243, 60, 235114, 0, 238979, 60, 243850, 0, 247715, 60, 252586, 0, 256451, 60, 261490, 0, 265187, 60, 270226, 0, 273923, 60, 278962, 0, 282827, 60, 287698, 0, 291563, 60, 296434, 0, 300299, 60, 305338, 0, 309035, 60, 314074, 0, 317771, 60, 322810, 0, 326003, 60, 331714, 0, 334739, 60, 340450, 0, 343475, 60, 349186, 0, 352379, 60, 358090, 0, 361115, 60, 366826, 0, 369851, 60, 375562, 0, 378587, 60, 384298, 0, 387323, 60, 393034, 0, 396059, 60, 401770, 0, 404963, 60, 410674, 0, 413699, 60, 419410, 0, 422435, 60, 428146, 0, 431171, 60, 436882, 0, 439907, 60, 445618, 0, 448811, 60, 454522, 0, 457547, 60, 463258, 0, 466283, 60, 471994, 0, 475019, 60, 480730, 0, 483755, 60, 489466, 0, 492491, 60, 498202, 0, 501395, 60, 507106, 0, 510131, 60, 515842, 0, 518867, 60, 524578, 0, 527603, 60, 533314, 0, 536339, 60, 542050, 0, 545243, 60, 550954, 0, 553979, 60, 559690, 0, 562715, 60, 568426, 0, 571451, 60, 577162, 0, 580187, 60, 585898, 0, 588923, 60, 594634, 0], "names": ["AKST", "Alaska Standard Time", "AKDT", "Alaska Daylight Time"], "std_offset": -540}
europeSkopje = {"id": "Europe/Skopje", "transitions": [115993, 60, 120361, 0, 124729, 60, 129265, 0, 133633, 60, 138001, 0, 142369, 60, 146737, 0, 151105, 60, 155473, 0, 159841, 60, 164209, 0, 168577, 60, 172945, 0, 177313, 60, 181849, 0, 186217, 60, 190585, 0, 194953, 60, 199321, 0, 203689, 60, 208057, 0, 212425, 60, 216793, 0, 221161, 60, 225529, 0, 230065, 60, 235105, 0, 238801, 60, 243841, 0, 247537, 60, 252577, 0, 256273, 60, 261481, 0, 265009, 60, 270217, 0, 273745, 60, 278953, 0, 282649, 60, 287689, 0, 291385, 60, 296425, 0, 300121, 60, 305329, 0, 308857, 60, 314065, 0, 317593, 60, 322801, 0, 326329, 60, 331537, 0, 335233, 60, 340273, 0, 343969, 60, 349009, 0, 352705, 60, 357913, 0, 361441, 60, 366649, 0, 370177, 60, 375385, 0, 379081, 60, 384121, 0, 387817, 60, 392857, 0, 396553, 60, 401593, 0, 405289, 60, 410497, 0, 414025, 60, 419233, 0, 422761, 60, 427969, 0, 431665, 60, 436705, 0, 440401, 60, 445441, 0, 449137, 60, 454345, 0, 457873, 60, 463081, 0, 466609, 60, 471817, 0, 475513, 60, 480553, 0, 484249, 60, 489289, 0, 492985, 60, 498025, 0, 501721, 60, 506929, 0, 510457, 60, 515665, 0, 519193, 60, 524401, 0, 528097, 60, 533137, 0, 536833, 60, 541873, 0, 545569, 60, 550777, 0, 554305, 60, 559513, 0, 563041, 60, 568249, 0, 571777, 60, 576985, 0, 580681, 60, 585721, 0, 589417, 60, 594457, 0], "names": ["CET", "Central European Standard Time", "CEST", "Central European Summer Time"], "std_offset": 60}
asiaMagadan = {"id": "Asia/Magadan", "transitions": [98581, 60, 102972, 0, 107341, 60, 111732, 0, 116101, 60, 120492, 0, 124885, 60, 129255, 0, 133623, 60, 137991, 0, 142359, 60, 146727, 0, 151095, 60, 155463, 0, 159831, 60, 164199, 0, 168567, 60, 172935, 0, 177303, 60, 181839, 0, 186207, 60, 190576, 0, 194940, 60, 199307, 0, 203679, 60, 208047, 0, 212415, 60, 216783, 0, 221151, 60, 225519, 0, 230055, 60, 235095, 0, 238791, 60, 243831, 0, 247527, 60, 252567, 0, 256263, 60, 261471, 0, 264999, 60, 270207, 0, 273735, 60, 278943, 0, 282639, 60, 287679, 0, 291375, 60, 296415, 0, 300111, 60, 305319, 0, 308847, 60, 314055, 0, 317583, 60, 322791, 0, 326319, 60, 331527, 0, 335223, 60, 340263, 0, 343959, 60, 348999, 0, 352695, 60, 357903, 0], "names": ["MAGT", "Magadan Standard Time", "MAGST", "Magadan Summer Time"], "std_offset": 720}
europeAndorra = {"id": "Europe/Andorra", "transitions": [133633, 60, 138001, 0, 142369, 60, 146737, 0, 151105, 60, 155473, 0, 159841, 60, 164209, 0, 168577, 60, 172945, 0, 177313, 60, 181849, 0, 186217, 60, 190585, 0, 194953, 60, 199321, 0, 203689, 60, 208057, 0, 212425, 60, 216793, 0, 221161, 60, 225529, 0, 230065, 60, 235105, 0, 238801, 60, 243841, 0, 247537, 60, 252577, 0, 256273, 60, 261481, 0, 265009, 60, 270217, 0, 273745, 60, 278953, 0, 282649, 60, 287689, 0, 291385, 60, 296425, 0, 300121, 60, 305329, 0, 308857, 60, 314065, 0, 317593, 60, 322801, 0, 326329, 60, 331537, 0, 335233, 60, 340273, 0, 343969, 60, 349009, 0, 352705, 60, 357913, 0, 361441, 60, 366649, 0, 370177, 60, 375385, 0, 379081, 60, 384121, 0, 387817, 60, 392857, 0, 396553, 60, 401593, 0, 405289, 60, 410497, 0, 414025, 60, 419233, 0, 422761, 60, 427969, 0, 431665, 60, 436705, 0, 440401, 60, 445441, 0, 449137, 60, 454345, 0, 457873, 60, 463081, 0, 466609, 60, 471817, 0, 475513, 60, 480553, 0, 484249, 60, 489289, 0, 492985, 60, 498025, 0, 501721, 60, 506929, 0, 510457, 60, 515665, 0, 519193, 60, 524401, 0, 528097, 60, 533137, 0, 536833, 60, 541873, 0, 545569, 60, 550777, 0, 554305, 60, 559513, 0, 563041, 60, 568249, 0, 571777, 60, 576985, 0, 580681, 60, 585721, 0, 589417, 60, 594457, 0], "names": ["CET", "Central European Standard Time", "CEST", "Central European Summer Time"], "std_offset": 60}
europeMadrid = {"id": "Europe/Madrid", "transitions": [37534, 60, 41735, 0, 46438, 60, 50471, 0, 54670, 60, 59039, 0, 63574, 60, 67775, 0, 72334, 60, 76679, 0, 81049, 60, 85417, 0, 89953, 60, 94153, 0, 98521, 60, 102889, 0, 107257, 60, 111625, 0, 115993, 60, 120361, 0, 124729, 60, 129265, 0, 133633, 60, 138001, 0, 142369, 60, 146737, 0, 151105, 60, 155473, 0, 159841, 60, 164209, 0, 168577, 60, 172945, 0, 177313, 60, 181849, 0, 186217, 60, 190585, 0, 194953, 60, 199321, 0, 203689, 60, 208057, 0, 212425, 60, 216793, 0, 221161, 60, 225529, 0, 230065, 60, 235105, 0, 238801, 60, 243841, 0, 247537, 60, 252577, 0, 256273, 60, 261481, 0, 265009, 60, 270217, 0, 273745, 60, 278953, 0, 282649, 60, 287689, 0, 291385, 60, 296425, 0, 300121, 60, 305329, 0, 308857, 60, 314065, 0, 317593, 60, 322801, 0, 326329, 60, 331537, 0, 335233, 60, 340273, 0, 343969, 60, 349009, 0, 352705, 60, 357913, 0, 361441, 60, 366649, 0, 370177, 60, 375385, 0, 379081, 60, 384121, 0, 387817, 60, 392857, 0, 396553, 60, 401593, 0, 405289, 60, 410497, 0, 414025, 60, 419233, 0, 422761, 60, 427969, 0, 431665, 60, 436705, 0, 440401, 60, 445441, 0, 449137, 60, 454345, 0, 457873, 60, 463081, 0, 466609, 60, 471817, 0, 475513, 60, 480553, 0, 484249, 60, 489289, 0, 492985, 60, 498025, 0, 501721, 60, 506929, 0, 510457, 60, 515665, 0, 519193, 60, 524401, 0, 528097, 60, 533137, 0, 536833, 60, 541873, 0, 545569, 60, 550777, 0, 554305, 60, 559513, 0, 563041, 60, 568249, 0, 571777, 60, 576985, 0, 580681, 60, 585721, 0, 589417, 60, 594457, 0], "names": ["CET", "Central European Standard Time", "CEST", "Central European Summer Time"], "std_offset": 60}
americaArgentinaTucuman = {"id": "America/Argentina/Tucuman", "transitions": [35595, 60, 37946, 0, 165819, 60, 168074, 0, 173451, 60, 176810, 0, 182355, 60, 185546, 0, 199827, 60, 203186, 0, 260811, 60, 264459, 0, 333051, 60, 334898, 0, 340107, 60, 343634, 0], "names": ["ART", "Argentina Standard Time", "ARST", "Argentina Summer Time"], "std_offset": -180}
pacificTarawa = {"id": "Pacific/Tarawa", "transitions": [], "names": ["GILT", "Gilbert Islands Time"], "std_offset": 720}
americaSantarem = {"id": "America/Santarem", "transitions": [138820, 60, 142011, 0, 147388, 60, 150075, 0, 156148, 60, 158667, 0], "names": ["BRT", "Brasilia Standard Time", "BRST", "Brasilia Summer Time"], "std_offset": -180}
europeVaduz = {"id": "Europe/Vaduz", "transitions": [98521, 60, 102889, 0, 107257, 60, 111625, 0, 115993, 60, 120361, 0, 124729, 60, 129265, 0, 133633, 60, 138001, 0, 142369, 60, 146737, 0, 151105, 60, 155473, 0, 159841, 60, 164209, 0, 168577, 60, 172945, 0, 177313, 60, 181849, 0, 186217, 60, 190585, 0, 194953, 60, 199321, 0, 203689, 60, 208057, 0, 212425, 60, 216793, 0, 221161, 60, 225529, 0, 230065, 60, 235105, 0, 238801, 60, 243841, 0, 247537, 60, 252577, 0, 256273, 60, 261481, 0, 265009, 60, 270217, 0, 273745, 60, 278953, 0, 282649, 60, 287689, 0, 291385, 60, 296425, 0, 300121, 60, 305329, 0, 308857, 60, 314065, 0, 317593, 60, 322801, 0, 326329, 60, 331537, 0, 335233, 60, 340273, 0, 343969, 60, 349009, 0, 352705, 60, 357913, 0, 361441, 60, 366649, 0, 370177, 60, 375385, 0, 379081, 60, 384121, 0, 387817, 60, 392857, 0, 396553, 60, 401593, 0, 405289, 60, 410497, 0, 414025, 60, 419233, 0, 422761, 60, 427969, 0, 431665, 60, 436705, 0, 440401, 60, 445441, 0, 449137, 60, 454345, 0, 457873, 60, 463081, 0, 466609, 60, 471817, 0, 475513, 60, 480553, 0, 484249, 60, 489289, 0, 492985, 60, 498025, 0, 501721, 60, 506929, 0, 510457, 60, 515665, 0, 519193, 60, 524401, 0, 528097, 60, 533137, 0, 536833, 60, 541873, 0, 545569, 60, 550777, 0, 554305, 60, 559513, 0, 563041, 60, 568249, 0, 571777, 60, 576985, 0, 580681, 60, 585721, 0, 589417, 60, 594457, 0], "names": ["CET", "Central European Standard Time", "CEST", "Central European Summer Time"], "std_offset": 60}
cST6CDT = {"id": "CST6CDT", "transitions": [2768, 60, 7135, 0, 11504, 60, 16039, 0, 20408, 60, 24775, 0, 29144, 60, 33511, 0, 35192, 60, 42247, 0, 45104, 60, 50983, 0, 55352, 60, 59887, 0, 64088, 60, 68623, 0, 72992, 60, 77359, 0, 81728, 60, 86095, 0, 90464, 60, 94831, 0, 99200, 60, 103567, 0, 107936, 60, 112471, 0, 116672, 60, 121207, 0, 125576, 60, 129943, 0, 134312, 60, 138679, 0, 143048, 60, 147415, 0, 151280, 60, 156151, 0, 160016, 60, 165055, 0, 168752, 60, 173791, 0, 177488, 60, 182527, 0, 186392, 60, 191263, 0, 195128, 60, 199999, 0, 203864, 60, 208903, 0, 212600, 60, 217639, 0, 221336, 60, 226375, 0, 230240, 60, 235111, 0, 238976, 60, 243847, 0, 247712, 60, 252583, 0, 256448, 60, 261487, 0, 265184, 60, 270223, 0, 273920, 60, 278959, 0, 282824, 60, 287695, 0, 291560, 60, 296431, 0, 300296, 60, 305335, 0, 309032, 60, 314071, 0, 317768, 60, 322807, 0, 326000, 60, 331711, 0, 334736, 60, 340447, 0, 343472, 60, 349183, 0, 352376, 60, 358087, 0, 361112, 60, 366823, 0, 369848, 60, 375559, 0, 378584, 60, 384295, 0, 387320, 60, 393031, 0, 396056, 60, 401767, 0, 404960, 60, 410671, 0, 413696, 60, 419407, 0, 422432, 60, 428143, 0, 431168, 60, 436879, 0, 439904, 60, 445615, 0, 448808, 60, 454519, 0, 457544, 60, 463255, 0, 466280, 60, 471991, 0, 475016, 60, 480727, 0, 483752, 60, 489463, 0, 492488, 60, 498199, 0, 501392, 60, 507103, 0, 510128, 60, 515839, 0, 518864, 60, 524575, 0, 527600, 60, 533311, 0, 536336, 60, 542047, 0, 545240, 60, 550951, 0, 553976, 60, 559687, 0, 562712, 60, 568423, 0, 571448, 60, 577159, 0, 580184, 60, 585895, 0, 588920, 60, 594631, 0], "names": ["CST", "Central Standard Time", "CDT", "Central Daylight Time"], "std_offset": -360}
americaGrandTurk = {"id": "America/Grand_Turk", "transitions": [81727, 60, 86094, 0, 90463, 60, 94830, 0, 99199, 60, 103566, 0, 107935, 60, 112470, 0, 116671, 60, 121206, 0, 125575, 60, 129942, 0, 134311, 60, 138678, 0, 143047, 60, 147414, 0, 151279, 60, 156150, 0, 160015, 60, 165054, 0, 168751, 60, 173790, 0, 177487, 60, 182526, 0, 186391, 60, 191262, 0, 195127, 60, 199998, 0, 203863, 60, 208902, 0, 212599, 60, 217638, 0, 221335, 60, 226374, 0, 230239, 60, 235110, 0, 238975, 60, 243846, 0, 247711, 60, 252582, 0, 256447, 60, 261486, 0, 265183, 60, 270222, 0, 273919, 60, 278958, 0, 282823, 60, 287694, 0, 291559, 60, 296430, 0, 300295, 60, 305334, 0, 309031, 60, 314070, 0, 317767, 60, 322806, 0, 325999, 60, 331710, 0, 334735, 60, 340446, 0, 343471, 60, 349182, 0, 352375, 60, 358086, 0, 361111, 60, 366822, 0, 369847, 60, 375558, 0, 378583, 60, 384294, 0, 387319, 60, 393030, 0, 396055, 60, 401766, 0, 404959, 60, 410670, 0, 413695, 60, 419406, 0, 422431, 60, 428142, 0, 431167, 60, 436878, 0, 439903, 60, 445614, 0, 448807, 60, 454518, 0, 457543, 60, 463254, 0, 466279, 60, 471990, 0, 475015, 60, 480726, 0, 483751, 60, 489462, 0, 492487, 60, 498198, 0, 501391, 60, 507102, 0, 510127, 60, 515838, 0, 518863, 60, 524574, 0, 527599, 60, 533310, 0, 536335, 60, 542046, 0, 545239, 60, 550950, 0, 553975, 60, 559686, 0, 562711, 60, 568422, 0, 571447, 60, 577158, 0, 580183, 60, 585894, 0, 588919, 60, 594630, 0], "names": ["EST", "Eastern Standard Time", "EDT", "Eastern Daylight Time"], "std_offset": -300}
europeTirane = {"id": "Europe/Tirane", "transitions": [38015, 60, 41638, 0, 46703, 60, 50398, 0, 55511, 60, 59206, 0, 64415, 60, 67942, 0, 73127, 60, 76678, 0, 81863, 60, 85414, 0, 90599, 60, 94294, 0, 99191, 60, 102886, 0, 108095, 60, 111790, 0, 116519, 60, 120502, 0, 124895, 60, 129265, 0, 133633, 60, 138001, 0, 142369, 60, 146737, 0, 151105, 60, 155473, 0, 159841, 60, 164209, 0, 168577, 60, 172945, 0, 177313, 60, 181849, 0, 186217, 60, 190585, 0, 194953, 60, 199321, 0, 203689, 60, 208057, 0, 212425, 60, 216793, 0, 221161, 60, 225529, 0, 230065, 60, 235105, 0, 238801, 60, 243841, 0, 247537, 60, 252577, 0, 256273, 60, 261481, 0, 265009, 60, 270217, 0, 273745, 60, 278953, 0, 282649, 60, 287689, 0, 291385, 60, 296425, 0, 300121, 60, 305329, 0, 308857, 60, 314065, 0, 317593, 60, 322801, 0, 326329, 60, 331537, 0, 335233, 60, 340273, 0, 343969, 60, 349009, 0, 352705, 60, 357913, 0, 361441, 60, 366649, 0, 370177, 60, 375385, 0, 379081, 60, 384121, 0, 387817, 60, 392857, 0, 396553, 60, 401593, 0, 405289, 60, 410497, 0, 414025, 60, 419233, 0, 422761, 60, 427969, 0, 431665, 60, 436705, 0, 440401, 60, 445441, 0, 449137, 60, 454345, 0, 457873, 60, 463081, 0, 466609, 60, 471817, 0, 475513, 60, 480553, 0, 484249, 60, 489289, 0, 492985, 60, 498025, 0, 501721, 60, 506929, 0, 510457, 60, 515665, 0, 519193, 60, 524401, 0, 528097, 60, 533137, 0, 536833, 60, 541873, 0, 545569, 60, 550777, 0, 554305, 60, 559513, 0, 563041, 60, 568249, 0, 571777, 60, 576985, 0, 580681, 60, 585721, 0, 589417, 60, 594457, 0], "names": ["CET", "Central European Standard Time", "CEST", "Central European Summer Time"], "std_offset": 60}
africaAddisAbaba = {"id": "Africa/Addis_Ababa", "transitions": [], "names": ["EAT", "East Africa Time"], "std_offset": 180}
americaGuadeloupe = {"id": "America/Guadeloupe", "transitions": [], "names": ["AST", "Atlantic Standard Time", "ADT", "Atlantic Daylight Time"], "std_offset": -240}
pacificPortMoresby = {"id": "Pacific/Port_Moresby", "transitions": [], "names": ["PGT", "Papua New Guinea Time"], "std_offset": 600}
pacificPonape = {"id": "Pacific/Ponape", "transitions": [], "names": ["PONT", "Ponape Time"], "std_offset": 660}
asiaQyzylorda = {"id": "Asia/Qyzylorda", "transitions": [98587, 60, 102978, 0, 107346, 60, 111738, 0, 116107, 60, 120498, 0, 124891, 60, 129261, 0, 133629, 60, 137997, 0, 142365, 60, 146733, 0, 151101, 60, 155469, 0, 159837, 60, 164205, 0, 168573, 60, 172941, 0, 177309, 60, 181845, 0, 194945, 60, 199312, 0, 203684, 60, 208052, 0, 212420, 60, 216788, 0, 221156, 60, 225524, 0, 230060, 60, 235100, 0, 238796, 60, 243836, 0, 247532, 60, 252572, 0, 256268, 60, 261476, 0, 265004, 60, 270212, 0, 273740, 60, 278948, 0, 282644, 60, 287684, 0, 291380, 60, 296420, 0, 300116, 60, 305324, 0], "names": ["EKST", "East Kazakhstan Time"], "std_offset": 360}
pacificNorfolk = {"id": "Pacific/Norfolk", "transitions": [], "names": ["NFT", "Norfolk Island Time"], "std_offset": 690}
africaTripoli = {"id": "Africa/Tripoli", "transitions": [107351, 60, 111742, 0, 116111, 60, 120502, 0, 124895, 60, 129286, 0, 133775, 60, 138046, 0, 142487, 60, 146854, 0, 151175, 60, 155566, 0, 159959, 60, 164350, 0, 168719, 60, 173110, 0, 238919, 60, 243310, 0, 379032, 60, 384072, 0, 387768, 60, 392976, 0, 396504, 60, 401712, 0, 405240, 60, 410448, 0, 414144, 60, 419184, 0, 422880, 60, 427920, 0, 431616, 60, 436656, 0, 440352, 60, 445560, 0, 449088, 60, 454296, 0, 457824, 60, 463032, 0, 466728, 60, 471768, 0, 475464, 60, 480504, 0, 484200, 60, 489408, 0, 492936, 60, 498144, 0, 501672, 60, 506880, 0, 510576, 60, 515616, 0, 519312, 60, 524352, 0, 528048, 60, 533088, 0, 536784, 60, 541992, 0, 545520, 60, 550728, 0, 554256, 60, 559464, 0, 563160, 60, 568200, 0, 571896, 60, 576936, 0, 580632, 60, 585840, 0, 589368, 60, 594576, 0], "names": ["EET", "Eastern European Standard Time", "EEST", "Eastern European Summer Time"], "std_offset": 60}
asiaDili = {"id": "Asia/Dili", "transitions": [], "names": ["TLT", "East Timor Time"], "std_offset": 540}
pacificGuam = {"id": "Pacific/Guam", "transitions": [], "names": ["ChST", "Chamorro Standard Time"], "std_offset": 600}
pST8PDT = {"id": "PST8PDT", "transitions": [2770, 60, 7137, 0, 11506, 60, 16041, 0, 20410, 60, 24777, 0, 29146, 60, 33513, 0, 35194, 60, 42249, 0, 45106, 60, 50985, 0, 55354, 60, 59889, 0, 64090, 60, 68625, 0, 72994, 60, 77361, 0, 81730, 60, 86097, 0, 90466, 60, 94833, 0, 99202, 60, 103569, 0, 107938, 60, 112473, 0, 116674, 60, 121209, 0, 125578, 60, 129945, 0, 134314, 60, 138681, 0, 143050, 60, 147417, 0, 151282, 60, 156153, 0, 160018, 60, 165057, 0, 168754, 60, 173793, 0, 177490, 60, 182529, 0, 186394, 60, 191265, 0, 195130, 60, 200001, 0, 203866, 60, 208905, 0, 212602, 60, 217641, 0, 221338, 60, 226377, 0, 230242, 60, 235113, 0, 238978, 60, 243849, 0, 247714, 60, 252585, 0, 256450, 60, 261489, 0, 265186, 60, 270225, 0, 273922, 60, 278961, 0, 282826, 60, 287697, 0, 291562, 60, 296433, 0, 300298, 60, 305337, 0, 309034, 60, 314073, 0, 317770, 60, 322809, 0, 326002, 60, 331713, 0, 334738, 60, 340449, 0, 343474, 60, 349185, 0, 352378, 60, 358089, 0, 361114, 60, 366825, 0, 369850, 60, 375561, 0, 378586, 60, 384297, 0, 387322, 60, 393033, 0, 396058, 60, 401769, 0, 404962, 60, 410673, 0, 413698, 60, 419409, 0, 422434, 60, 428145, 0, 431170, 60, 436881, 0, 439906, 60, 445617, 0, 448810, 60, 454521, 0, 457546, 60, 463257, 0, 466282, 60, 471993, 0, 475018, 60, 480729, 0, 483754, 60, 489465, 0, 492490, 60, 498201, 0, 501394, 60, 507105, 0, 510130, 60, 515841, 0, 518866, 60, 524577, 0, 527602, 60, 533313, 0, 536338, 60, 542049, 0, 545242, 60, 550953, 0, 553978, 60, 559689, 0, 562714, 60, 568425, 0, 571450, 60, 577161, 0, 580186, 60, 585897, 0, 588922, 60, 594633, 0], "names": ["PST", "Pacific Standard Time", "PDT", "Pacific Daylight Time"], "std_offset": -480}
americaScoresbysund = {"id": "America/Scoresbysund", "transitions": [89956, 60, 94156, 0, 107257, 60, 111625, 0, 115993, 60, 120361, 0, 124729, 60, 129265, 0, 133633, 60, 138001, 0, 142369, 60, 146737, 0, 151105, 60, 155473, 0, 159841, 60, 164209, 0, 168577, 60, 172945, 0, 177313, 60, 181849, 0, 186217, 60, 190585, 0, 194953, 60, 199321, 0, 203689, 60, 208057, 0, 212425, 60, 216793, 0, 221161, 60, 225529, 0, 230065, 60, 235105, 0, 238801, 60, 243841, 0, 247537, 60, 252577, 0, 256273, 60, 261481, 0, 265009, 60, 270217, 0, 273745, 60, 278953, 0, 282649, 60, 287689, 0, 291385, 60, 296425, 0, 300121, 60, 305329, 0, 308857, 60, 314065, 0, 317593, 60, 322801, 0, 326329, 60, 331537, 0, 335233, 60, 340273, 0, 343969, 60, 349009, 0, 352705, 60, 357913, 0, 361441, 60, 366649, 0, 370177, 60, 375385, 0, 379081, 60, 384121, 0, 387817, 60, 392857, 0, 396553, 60, 401593, 0, 405289, 60, 410497, 0, 414025, 60, 419233, 0, 422761, 60, 427969, 0, 431665, 60, 436705, 0, 440401, 60, 445441, 0, 449137, 60, 454345, 0, 457873, 60, 463081, 0, 466609, 60, 471817, 0, 475513, 60, 480553, 0, 484249, 60, 489289, 0, 492985, 60, 498025, 0, 501721, 60, 506929, 0, 510457, 60, 515665, 0, 519193, 60, 524401, 0, 528097, 60, 533137, 0, 536833, 60, 541873, 0, 545569, 60, 550777, 0, 554305, 60, 559513, 0, 563041, 60, 568249, 0, 571777, 60, 576985, 0, 580681, 60, 585721, 0, 589417, 60, 594457, 0], "names": ["EGT", "East Greenland Standard Time", "EGST", "East Greenland Summer Time"], "std_offset": -60}
americaStLucia = {"id": "America/St_Lucia", "transitions": [], "names": ["AST", "Atlantic Standard Time", "ADT", "Atlantic Daylight Time"], "std_offset": -240}
africaCairo = {"id": "Africa/Cairo", "transitions": [2879, 60, 6552, 0, 11639, 60, 15312, 0, 20423, 60, 24096, 0, 29183, 60, 32856, 0, 37943, 60, 41616, 0, 46703, 60, 50376, 0, 55487, 60, 59160, 0, 64247, 60, 67920, 0, 73007, 60, 76680, 0, 81767, 60, 85440, 0, 90551, 60, 94224, 0, 99311, 60, 102984, 0, 110111, 60, 111744, 0, 118559, 60, 120504, 0, 125615, 60, 129288, 0, 134375, 60, 138048, 0, 143135, 60, 146808, 0, 151895, 60, 155568, 0, 160679, 60, 164352, 0, 169559, 60, 173112, 0, 178199, 60, 181872, 0, 186959, 60, 190632, 0, 195743, 60, 199416, 0, 204503, 60, 208176, 0, 213263, 60, 216936, 0, 221950, 60, 225645, 0, 230686, 60, 234381, 0, 239422, 60, 243117, 0, 248158, 60, 251853, 0, 257062, 60, 260757, 0, 265798, 60, 269493, 0, 274534, 60, 278229, 0, 283270, 60, 286965, 0, 292006, 60, 295701, 0, 300910, 60, 304605, 0, 309646, 60, 313341, 0, 318382, 60, 321909, 0, 327118, 60, 330309, 0, 335854, 60, 338877, 0, 344590, 60, 347445, 0, 353494, 60, 355965, 0, 356686, 60, 357189, 0], "names": ["EET", "Eastern European Standard Time", "EEST", "Eastern European Summer Time"], "std_offset": 120}
americaGuatemala = {"id": "America/Guatemala", "transitions": [34182, 60, 36365, 0, 117318, 60, 120293, 0, 186030, 60, 190061, 0, 318438, 60, 322133, 0], "names": ["CST", "Central Standard Time", "CDT", "Central Daylight Time"], "std_offset": -360}
americaPortofSpain = {"id": "America/Port_of_Spain", "transitions": [], "names": ["AST", "Atlantic Standard Time", "ADT", "Atlantic Daylight Time"], "std_offset": -240}
indianMahe = {"id": "Indian/Mahe", "transitions": [], "names": ["SCT", "Seychelles Time"], "std_offset": 240}
asiaYekaterinburg = {"id": "Asia/Yekaterinburg", "transitions": [98587, 60, 102978, 0, 107347, 60, 111738, 0, 116107, 60, 120498, 0, 124891, 60, 129261, 0, 133629, 60, 137997, 0, 142365, 60, 146733, 0, 151101, 60, 155469, 0, 159837, 60, 164205, 0, 168573, 60, 172941, 0, 177309, 60, 181845, 0, 186213, 60, 190582, 0, 194946, 60, 199313, 0, 203685, 60, 208053, 0, 212421, 60, 216789, 0, 221157, 60, 225525, 0, 230061, 60, 235101, 0, 238797, 60, 243837, 0, 247533, 60, 252573, 0, 256269, 60, 261477, 0, 265005, 60, 270213, 0, 273741, 60, 278949, 0, 282645, 60, 287685, 0, 291381, 60, 296421, 0, 300117, 60, 305325, 0, 308853, 60, 314061, 0, 317589, 60, 322797, 0, 326325, 60, 331533, 0, 335229, 60, 340269, 0, 343965, 60, 349005, 0, 352701, 60, 357909, 0], "names": ["YEKT", "Yekaterinburg Standard Time", "YEKST", "Yekaterinburg Summer Time"], "std_offset": 360}
americaLima = {"id": "America/Lima", "transitions": [140261, 60, 142420, 0, 149021, 60, 151180, 0, 175325, 60, 177484, 0, 210389, 60, 212548, 0], "names": ["PET", "Peru Standard Time", "PEST", "Peru Summer Time"], "std_offset": -300}
americaAruba = {"id": "America/Aruba", "transitions": [], "names": ["AST", "Atlantic Standard Time", "ADT", "Atlantic Daylight Time"], "std_offset": -240}
asiaDhaka = {"id": "Asia/Dhaka", "transitions": [345953, 60, 350632, 0], "names": ["BDT", "Bangladesh Standard Time", "BDST", "Bangladesh Summer Time"], "std_offset": 360}
asiaVientiane = {"id": "Asia/Vientiane", "transitions": [], "names": ["ICT", "Indochina Time"], "std_offset": 420}
asiaYakutsk = {"id": "Asia/Yakutsk", "transitions": [98583, 60, 102974, 0, 107343, 60, 111734, 0, 116103, 60, 120494, 0, 124887, 60, 129257, 0, 133625, 60, 137993, 0, 142361, 60, 146729, 0, 151097, 60, 155465, 0, 159833, 60, 164201, 0, 168569, 60, 172937, 0, 177305, 60, 181841, 0, 186209, 60, 190578, 0, 194942, 60, 199309, 0, 203681, 60, 208049, 0, 212417, 60, 216785, 0, 221153, 60, 225521, 0, 230057, 60, 235097, 0, 238793, 60, 243833, 0, 247529, 60, 252569, 0, 256265, 60, 261473, 0, 265001, 60, 270209, 0, 273737, 60, 278945, 0, 282641, 60, 287681, 0, 291377, 60, 296417, 0, 300113, 60, 305321, 0, 308849, 60, 314057, 0, 317585, 60, 322793, 0, 326321, 60, 331529, 0, 335225, 60, 340265, 0, 343961, 60, 349001, 0, 352697, 60, 357905, 0], "names": ["YAKT", "Yakutsk Standard Time", "YAKST", "Yakutsk Summer Time"], "std_offset": 600}
asiaThimphu = {"id": "Asia/Thimphu", "transitions": [], "names": ["BTT", "Bhutan Time"], "std_offset": 360}
americaJuneau = {"id": "America/Juneau", "transitions": [2770, 60, 7137, 0, 11506, 60, 16041, 0, 20410, 60, 24777, 0, 29146, 60, 33513, 0, 35194, 60, 42249, 0, 45106, 60, 50985, 0, 55354, 60, 59889, 0, 64090, 60, 68625, 0, 72994, 60, 77361, 0, 81730, 60, 86097, 0, 90466, 60, 94834, 0, 99202, 60, 103569, 0, 107938, 60, 112473, 0, 116674, 60, 121209, 0, 125579, 60, 129946, 0, 134315, 60, 138682, 0, 143051, 60, 147418, 0, 151283, 60, 156154, 0, 160019, 60, 165058, 0, 168755, 60, 173794, 0, 177491, 60, 182530, 0, 186395, 60, 191266, 0, 195131, 60, 200002, 0, 203867, 60, 208906, 0, 212603, 60, 217642, 0, 221339, 60, 226378, 0, 230243, 60, 235114, 0, 238979, 60, 243850, 0, 247715, 60, 252586, 0, 256451, 60, 261490, 0, 265187, 60, 270226, 0, 273923, 60, 278962, 0, 282827, 60, 287698, 0, 291563, 60, 296434, 0, 300299, 60, 305338, 0, 309035, 60, 314074, 0, 317771, 60, 322810, 0, 326003, 60, 331714, 0, 334739, 60, 340450, 0, 343475, 60, 349186, 0, 352379, 60, 358090, 0, 361115, 60, 366826, 0, 369851, 60, 375562, 0, 378587, 60, 384298, 0, 387323, 60, 393034, 0, 396059, 60, 401770, 0, 404963, 60, 410674, 0, 413699, 60, 419410, 0, 422435, 60, 428146, 0, 431171, 60, 436882, 0, 439907, 60, 445618, 0, 448811, 60, 454522, 0, 457547, 60, 463258, 0, 466283, 60, 471994, 0, 475019, 60, 480730, 0, 483755, 60, 489466, 0, 492491, 60, 498202, 0, 501395, 60, 507106, 0, 510131, 60, 515842, 0, 518867, 60, 524578, 0, 527603, 60, 533314, 0, 536339, 60, 542050, 0, 545243, 60, 550954, 0, 553979, 60, 559690, 0, 562715, 60, 568426, 0, 571451, 60, 577162, 0, 580187, 60, 585898, 0, 588923, 60, 594634, 0], "names": ["AKST", "Alaska Standard Time", "AKDT", "Alaska Daylight Time"], "std_offset": -540}
atlanticAzores = {"id": "Atlantic/Azores", "transitions": [63409, 60, 67777, 0, 72313, 60, 76681, 0, 81049, 60, 85418, 0, 89785, 60, 94154, 0, 98522, 60, 102890, 0, 107258, 60, 111626, 0, 115995, 60, 120362, 0, 124730, 60, 129266, 0, 133634, 60, 138002, 0, 142370, 60, 146738, 0, 151106, 60, 155474, 0, 159842, 60, 164210, 0, 168578, 60, 172946, 0, 177314, 60, 181850, 0, 186218, 60, 190586, 0, 194954, 60, 199322, 0, 203689, 60, 208057, 0, 212425, 60, 216793, 0, 221161, 60, 225529, 0, 230065, 60, 235105, 0, 238801, 60, 243841, 0, 247537, 60, 252577, 0, 256273, 60, 261481, 0, 265009, 60, 270217, 0, 273745, 60, 278953, 0, 282649, 60, 287689, 0, 291385, 60, 296425, 0, 300121, 60, 305329, 0, 308857, 60, 314065, 0, 317593, 60, 322801, 0, 326329, 60, 331537, 0, 335233, 60, 340273, 0, 343969, 60, 349009, 0, 352705, 60, 357913, 0, 361441, 60, 366649, 0, 370177, 60, 375385, 0, 379081, 60, 384121, 0, 387817, 60, 392857, 0, 396553, 60, 401593, 0, 405289, 60, 410497, 0, 414025, 60, 419233, 0, 422761, 60, 427969, 0, 431665, 60, 436705, 0, 440401, 60, 445441, 0, 449137, 60, 454345, 0, 457873, 60, 463081, 0, 466609, 60, 471817, 0, 475513, 60, 480553, 0, 484249, 60, 489289, 0, 492985, 60, 498025, 0, 501721, 60, 506929, 0, 510457, 60, 515665, 0, 519193, 60, 524401, 0, 528097, 60, 533137, 0, 536833, 60, 541873, 0, 545569, 60, 550777, 0, 554305, 60, 559513, 0, 563041, 60, 568249, 0, 571777, 60, 576985, 0, 580681, 60, 585721, 0, 589417, 60, 594457, 0], "names": ["AZOT", "Azores Standard Time", "AZOST", "Azores Summer Time"], "std_offset": -60}
americaSwiftCurrent = {"id": "America/Swift_Current", "transitions": [], "names": ["CST", "Central Standard Time", "CDT", "Central Daylight Time"], "std_offset": -360}
americaPuertoRico = {"id": "America/Puerto_Rico", "transitions": [], "names": ["AST", "Atlantic Standard Time", "ADT", "Atlantic Daylight Time"], "std_offset": -240}
europeLondon = {"id": "Europe/London", "transitions": [19394, 60, 24770, 0, 28130, 60, 33506, 0, 36866, 60, 42242, 0, 45602, 60, 50978, 0, 54506, 60, 59714, 0, 63242, 60, 68450, 0, 71978, 60, 77354, 0, 80714, 60, 86090, 0, 89450, 60, 94826, 0, 98521, 60, 103561, 0, 107257, 60, 112297, 0, 115993, 60, 121033, 0, 124729, 60, 129937, 0, 133633, 60, 138673, 0, 142369, 60, 147409, 0, 151105, 60, 156145, 0, 159841, 60, 164881, 0, 168577, 60, 173785, 0, 177313, 60, 182521, 0, 186217, 60, 191257, 0, 194953, 60, 199993, 0, 203689, 60, 208729, 0, 212425, 60, 217465, 0, 221161, 60, 226201, 0, 230065, 60, 235105, 0, 238801, 60, 243841, 0, 247537, 60, 252577, 0, 256273, 60, 261481, 0, 265009, 60, 270217, 0, 273745, 60, 278953, 0, 282649, 60, 287689, 0, 291385, 60, 296425, 0, 300121, 60, 305329, 0, 308857, 60, 314065, 0, 317593, 60, 322801, 0, 326329, 60, 331537, 0, 335233, 60, 340273, 0, 343969, 60, 349009, 0, 352705, 60, 357913, 0, 361441, 60, 366649, 0, 370177, 60, 375385, 0, 379081, 60, 384121, 0, 387817, 60, 392857, 0, 396553, 60, 401593, 0, 405289, 60, 410497, 0, 414025, 60, 419233, 0, 422761, 60, 427969, 0, 431665, 60, 436705, 0, 440401, 60, 445441, 0, 449137, 60, 454345, 0, 457873, 60, 463081, 0, 466609, 60, 471817, 0, 475513, 60, 480553, 0, 484249, 60, 489289, 0, 492985, 60, 498025, 0, 501721, 60, 506929, 0, 510457, 60, 515665, 0, 519193, 60, 524401, 0, 528097, 60, 533137, 0, 536833, 60, 541873, 0, 545569, 60, 550777, 0, 554305, 60, 559513, 0, 563041, 60, 568249, 0, 571777, 60, 576985, 0, 580681, 60, 585721, 0, 589417, 60, 594457, 0], "names": ["GMT", "Greenwich Mean Time", "BST", "British Summer Time"], "std_offset": 0}
africaLome = {"id": "Africa/Lome", "transitions": [], "names": ["GMT", "Greenwich Mean Time"], "std_offset": 0}
australiaMelbourne = {"id": "Australia/Melbourne", "transitions": [16024, 60, 18880, 0, 24760, 60, 27784, 0, 33496, 60, 36520, 0, 42232, 60, 45256, 0, 50968, 60, 54160, 0, 59872, 60, 62896, 0, 68608, 60, 71632, 0, 77344, 60, 80368, 0, 86080, 60, 89104, 0, 94816, 60, 97840, 0, 103552, 60, 106744, 0, 112456, 60, 115480, 0, 121192, 60, 124216, 0, 129928, 60, 132952, 0, 138664, 60, 142024, 0, 147232, 60, 150760, 0, 155968, 60, 159664, 0, 165040, 60, 168400, 0, 173776, 60, 177136, 0, 182512, 60, 185536, 0, 191248, 60, 194272, 0, 199984, 60, 203176, 0, 208888, 60, 211912, 0, 217624, 60, 221152, 0, 226360, 60, 230056, 0, 235096, 60, 238792, 0, 243832, 60, 247528, 0, 252568, 60, 256264, 0, 261472, 60, 265000, 0, 268696, 60, 273736, 0, 278944, 60, 282640, 0, 287680, 60, 291376, 0, 296416, 60, 300112, 0, 305320, 60, 308848, 0, 314056, 60, 317752, 0, 322792, 60, 326320, 0, 331528, 60, 335392, 0, 339760, 60, 344128, 0, 348496, 60, 352864, 0, 357232, 60, 361600, 0, 365968, 60, 370336, 0, 374872, 60, 379240, 0, 383608, 60, 387976, 0, 392344, 60, 396712, 0, 401080, 60, 405448, 0, 409816, 60, 414184, 0, 418552, 60, 422920, 0, 427456, 60, 431824, 0, 436192, 60, 440560, 0, 444928, 60, 449296, 0, 453664, 60, 458032, 0, 462400, 60, 466768, 0, 471136, 60, 475672, 0, 480040, 60, 484408, 0, 488776, 60, 493144, 0, 497512, 60, 501880, 0, 506248, 60, 510616, 0, 514984, 60, 519352, 0, 523888, 60, 528256, 0, 532624, 60, 536992, 0, 541360, 60, 545728, 0, 550096, 60, 554464, 0, 558832, 60, 563200, 0, 567568, 60, 571936, 0, 576472, 60, 580840, 0, 585208, 60, 589576, 0, 593944, 60], "names": ["AEST", "Australian Eastern Standard Time", "AEDT", "Australian Eastern Daylight Time"], "std_offset": 600}
africaDaresSalaam = {"id": "Africa/Dar_es_Salaam", "transitions": [], "names": ["EAT", "East Africa Time"], "std_offset": 180}
atlanticFaeroe = {"id": "Atlantic/Faeroe", "transitions": [98521, 60, 102889, 0, 107257, 60, 111625, 0, 115993, 60, 120361, 0, 124729, 60, 129265, 0, 133633, 60, 138001, 0, 142369, 60, 146737, 0, 151105, 60, 155473, 0, 159841, 60, 164209, 0, 168577, 60, 172945, 0, 177313, 60, 181849, 0, 186217, 60, 190585, 0, 194953, 60, 199321, 0, 203689, 60, 208057, 0, 212425, 60, 216793, 0, 221161, 60, 225529, 0, 230065, 60, 235105, 0, 238801, 60, 243841, 0, 247537, 60, 252577, 0, 256273, 60, 261481, 0, 265009, 60, 270217, 0, 273745, 60, 278953, 0, 282649, 60, 287689, 0, 291385, 60, 296425, 0, 300121, 60, 305329, 0, 308857, 60, 314065, 0, 317593, 60, 322801, 0, 326329, 60, 331537, 0, 335233, 60, 340273, 0, 343969, 60, 349009, 0, 352705, 60, 357913, 0, 361441, 60, 366649, 0, 370177, 60, 375385, 0, 379081, 60, 384121, 0, 387817, 60, 392857, 0, 396553, 60, 401593, 0, 405289, 60, 410497, 0, 414025, 60, 419233, 0, 422761, 60, 427969, 0, 431665, 60, 436705, 0, 440401, 60, 445441, 0, 449137, 60, 454345, 0, 457873, 60, 463081, 0, 466609, 60, 471817, 0, 475513, 60, 480553, 0, 484249, 60, 489289, 0, 492985, 60, 498025, 0, 501721, 60, 506929, 0, 510457, 60, 515665, 0, 519193, 60, 524401, 0, 528097, 60, 533137, 0, 536833, 60, 541873, 0, 545569, 60, 550777, 0, 554305, 60, 559513, 0, 563041, 60, 568249, 0, 571777, 60, 576985, 0, 580681, 60, 585721, 0, 589417, 60, 594457, 0], "names": ["WET", "Western European Standard Time", "WEST", "Western European Summer Time"], "std_offset": 0}
indianChristmas = {"id": "Indian/Christmas", "transitions": [], "names": ["CXT", "Christmas Island Time"], "std_offset": 420}
europeUzhgorod = {"id": "Europe/Uzhgorod", "transitions": [98589, 60, 102980, 0, 107349, 60, 111740, 0, 116109, 60, 120500, 0, 124893, 60, 129263, 0, 133631, 60, 137999, 0, 142367, 60, 146735, 0, 151103, 60, 155471, 0, 159839, 60, 164207, 0, 168575, 60, 172943, 0, 194950, 60, 199317, 0, 203686, 60, 208053, 0, 212422, 60, 216789, 0, 221161, 60, 225529, 0, 230065, 60, 235105, 0, 238801, 60, 243841, 0, 247537, 60, 252577, 0, 256273, 60, 261481, 0, 265009, 60, 270217, 0, 273745, 60, 278953, 0, 282649, 60, 287689, 0, 291385, 60, 296425, 0, 300121, 60, 305329, 0, 308857, 60, 314065, 0, 317593, 60, 322801, 0, 326329, 60, 331537, 0, 335233, 60, 340273, 0, 343969, 60, 349009, 0, 352705, 60, 357913, 0, 361441, 60, 366649, 0, 370177, 60, 375385, 0, 379081, 60, 384121, 0, 387817, 60, 392857, 0, 396553, 60, 401593, 0, 405289, 60, 410497, 0, 414025, 60, 419233, 0, 422761, 60, 427969, 0, 431665, 60, 436705, 0, 440401, 60, 445441, 0, 449137, 60, 454345, 0, 457873, 60, 463081, 0, 466609, 60, 471817, 0, 475513, 60, 480553, 0, 484249, 60, 489289, 0, 492985, 60, 498025, 0, 501721, 60, 506929, 0, 510457, 60, 515665, 0, 519193, 60, 524401, 0, 528097, 60, 533137, 0, 536833, 60, 541873, 0, 545569, 60, 550777, 0, 554305, 60, 559513, 0, 563041, 60, 568249, 0, 571777, 60, 576985, 0, 580681, 60, 585721, 0, 589417, 60, 594457, 0], "names": ["EET", "Eastern European Standard Time", "EEST", "Eastern European Summer Time"], "std_offset": 120}
pacificTongatapu = {"id": "Pacific/Tongatapu", "transitions": [260893, 60, 264829, 0, 270373, 60, 272388, 0, 279109, 60, 281124, 0], "names": ["TOT", "Tonga Standard Time", "TOST", "Tonga Summer Time"], "std_offset": 780}
americaHalifax = {"id": "America/Halifax", "transitions": [2766, 60, 7133, 0, 11502, 60, 16037, 0, 20406, 60, 24773, 0, 29142, 60, 33509, 0, 37878, 60, 42245, 0, 46614, 60, 50981, 0, 55350, 60, 59885, 0, 64086, 60, 68621, 0, 72990, 60, 77357, 0, 81726, 60, 86093, 0, 90462, 60, 94829, 0, 99198, 60, 103565, 0, 107934, 60, 112469, 0, 116670, 60, 121205, 0, 125574, 60, 129941, 0, 134310, 60, 138677, 0, 143046, 60, 147413, 0, 151278, 60, 156149, 0, 160014, 60, 165053, 0, 168750, 60, 173789, 0, 177486, 60, 182525, 0, 186390, 60, 191261, 0, 195126, 60, 199997, 0, 203862, 60, 208901, 0, 212598, 60, 217637, 0, 221334, 60, 226373, 0, 230238, 60, 235109, 0, 238974, 60, 243845, 0, 247710, 60, 252581, 0, 256446, 60, 261485, 0, 265182, 60, 270221, 0, 273918, 60, 278957, 0, 282822, 60, 287693, 0, 291558, 60, 296429, 0, 300294, 60, 305333, 0, 309030, 60, 314069, 0, 317766, 60, 322805, 0, 325998, 60, 331709, 0, 334734, 60, 340445, 0, 343470, 60, 349181, 0, 352374, 60, 358085, 0, 361110, 60, 366821, 0, 369846, 60, 375557, 0, 378582, 60, 384293, 0, 387318, 60, 393029, 0, 396054, 60, 401765, 0, 404958, 60, 410669, 0, 413694, 60, 419405, 0, 422430, 60, 428141, 0, 431166, 60, 436877, 0, 439902, 60, 445613, 0, 448806, 60, 454517, 0, 457542, 60, 463253, 0, 466278, 60, 471989, 0, 475014, 60, 480725, 0, 483750, 60, 489461, 0, 492486, 60, 498197, 0, 501390, 60, 507101, 0, 510126, 60, 515837, 0, 518862, 60, 524573, 0, 527598, 60, 533309, 0, 536334, 60, 542045, 0, 545238, 60, 550949, 0, 553974, 60, 559685, 0, 562710, 60, 568421, 0, 571446, 60, 577157, 0, 580182, 60, 585893, 0, 588918, 60, 594629, 0], "names": ["AST", "Atlantic Standard Time", "ADT", "Atlantic Daylight Time"], "std_offset": -240}
pacificPalau = {"id": "Pacific/Palau", "transitions": [], "names": ["PWT", "Palau Time"], "std_offset": 540}
americaYakutat = {"id": "America/Yakutat", "transitions": [2771, 60, 7138, 0, 11507, 60, 16042, 0, 20411, 60, 24778, 0, 29147, 60, 33514, 0, 35195, 60, 42250, 0, 45107, 60, 50986, 0, 55355, 60, 59890, 0, 64091, 60, 68626, 0, 72995, 60, 77362, 0, 81731, 60, 86098, 0, 90467, 60, 94834, 0, 99203, 60, 103570, 0, 107939, 60, 112474, 0, 116675, 60, 121210, 0, 125579, 60, 129946, 0, 134315, 60, 138682, 0, 143051, 60, 147418, 0, 151283, 60, 156154, 0, 160019, 60, 165058, 0, 168755, 60, 173794, 0, 177491, 60, 182530, 0, 186395, 60, 191266, 0, 195131, 60, 200002, 0, 203867, 60, 208906, 0, 212603, 60, 217642, 0, 221339, 60, 226378, 0, 230243, 60, 235114, 0, 238979, 60, 243850, 0, 247715, 60, 252586, 0, 256451, 60, 261490, 0, 265187, 60, 270226, 0, 273923, 60, 278962, 0, 282827, 60, 287698, 0, 291563, 60, 296434, 0, 300299, 60, 305338, 0, 309035, 60, 314074, 0, 317771, 60, 322810, 0, 326003, 60, 331714, 0, 334739, 60, 340450, 0, 343475, 60, 349186, 0, 352379, 60, 358090, 0, 361115, 60, 366826, 0, 369851, 60, 375562, 0, 378587, 60, 384298, 0, 387323, 60, 393034, 0, 396059, 60, 401770, 0, 404963, 60, 410674, 0, 413699, 60, 419410, 0, 422435, 60, 428146, 0, 431171, 60, 436882, 0, 439907, 60, 445618, 0, 448811, 60, 454522, 0, 457547, 60, 463258, 0, 466283, 60, 471994, 0, 475019, 60, 480730, 0, 483755, 60, 489466, 0, 492491, 60, 498202, 0, 501395, 60, 507106, 0, 510131, 60, 515842, 0, 518867, 60, 524578, 0, 527603, 60, 533314, 0, 536339, 60, 542050, 0, 545243, 60, 550954, 0, 553979, 60, 559690, 0, 562715, 60, 568426, 0, 571451, 60, 577162, 0, 580187, 60, 585898, 0, 588923, 60, 594634, 0], "names": ["AKST", "Alaska Standard Time", "AKDT", "Alaska Daylight Time"], "std_offset": -540}
americaRainyRiver = {"id": "America/Rainy_River", "transitions": [37880, 60, 42247, 0, 46616, 60, 50983, 0, 55352, 60, 59887, 0, 64088, 60, 68623, 0, 72992, 60, 77359, 0, 81728, 60, 86095, 0, 90464, 60, 94831, 0, 99200, 60, 103567, 0, 107936, 60, 112471, 0, 116672, 60, 121207, 0, 125576, 60, 129943, 0, 134312, 60, 138679, 0, 143048, 60, 147415, 0, 151280, 60, 156151, 0, 160016, 60, 165055, 0, 168752, 60, 173791, 0, 177488, 60, 182527, 0, 186392, 60, 191263, 0, 195128, 60, 199999, 0, 203864, 60, 208903, 0, 212600, 60, 217639, 0, 221336, 60, 226375, 0, 230240, 60, 235111, 0, 238976, 60, 243847, 0, 247712, 60, 252583, 0, 256448, 60, 261487, 0, 265184, 60, 270223, 0, 273920, 60, 278959, 0, 282824, 60, 287695, 0, 291560, 60, 296431, 0, 300296, 60, 305335, 0, 309032, 60, 314071, 0, 317768, 60, 322807, 0, 326000, 60, 331711, 0, 334736, 60, 340447, 0, 343472, 60, 349183, 0, 352376, 60, 358087, 0, 361112, 60, 366823, 0, 369848, 60, 375559, 0, 378584, 60, 384295, 0, 387320, 60, 393031, 0, 396056, 60, 401767, 0, 404960, 60, 410671, 0, 413696, 60, 419407, 0, 422432, 60, 428143, 0, 431168, 60, 436879, 0, 439904, 60, 445615, 0, 448808, 60, 454519, 0, 457544, 60, 463255, 0, 466280, 60, 471991, 0, 475016, 60, 480727, 0, 483752, 60, 489463, 0, 492488, 60, 498199, 0, 501392, 60, 507103, 0, 510128, 60, 515839, 0, 518864, 60, 524575, 0, 527600, 60, 533311, 0, 536336, 60, 542047, 0, 545240, 60, 550951, 0, 553976, 60, 559687, 0, 562712, 60, 568423, 0, 571448, 60, 577159, 0, 580184, 60, 585895, 0, 588920, 60, 594631, 0], "names": ["CST", "Central Standard Time", "CDT", "Central Daylight Time"], "std_offset": -360}
asiaBrunei = {"id": "Asia/Brunei", "transitions": [], "names": ["BNT", "Brunei Darussalam Time"], "std_offset": 480}
asiaIrkutsk = {"id": "Asia/Irkutsk", "transitions": [98584, 60, 102975, 0, 107344, 60, 111735, 0, 116104, 60, 120495, 0, 124888, 60, 129258, 0, 133626, 60, 137994, 0, 142362, 60, 146730, 0, 151098, 60, 155466, 0, 159834, 60, 164202, 0, 168570, 60, 172938, 0, 177306, 60, 181842, 0, 186210, 60, 190579, 0, 194943, 60, 199310, 0, 203682, 60, 208050, 0, 212418, 60, 216786, 0, 221154, 60, 225522, 0, 230058, 60, 235098, 0, 238794, 60, 243834, 0, 247530, 60, 252570, 0, 256266, 60, 261474, 0, 265002, 60, 270210, 0, 273738, 60, 278946, 0, 282642, 60, 287682, 0, 291378, 60, 296418, 0, 300114, 60, 305322, 0, 308850, 60, 314058, 0, 317586, 60, 322794, 0, 326322, 60, 331530, 0, 335226, 60, 340266, 0, 343962, 60, 349002, 0, 352698, 60, 357906, 0], "names": ["IRKT", "Irkutsk Standard Time", "IRKST", "Irkutsk Summer Time"], "std_offset": 540}
americaCostaRica = {"id": "America/Costa_Rica", "transitions": [80214, 60, 82565, 0, 88950, 60, 91301, 0, 184518, 60, 188429, 0, 193254, 60, 194621, 0], "names": ["CST", "Central Standard Time", "CDT", "Central Daylight Time"], "std_offset": -360}
africaAsmera = {"id": "Africa/Asmera", "transitions": [], "names": ["EAT", "East Africa Time"], "std_offset": 180}
pacificGambier = {"id": "Pacific/Gambier", "transitions": [], "names": ["GAMT", "Gambier Time"], "std_offset": -540}
americaArgentinaUshuaia = {"id": "America/Argentina/Ushuaia", "transitions": [35595, 60, 37946, 0, 165819, 60, 168074, 0, 173451, 60, 176810, 0, 182355, 60, 185546, 0, 191091, 60, 194282, 0, 199827, 60, 203186, 0, 260811, 60, 264459, 0, 333051, 60, 334898, 0], "names": ["ART", "Argentina Standard Time", "ARST", "Argentina Summer Time"], "std_offset": -180}
pacificKiritimati = {"id": "Pacific/Kiritimati", "transitions": [], "names": ["LINT", "Line Islands Time"], "std_offset": 840}
americaGooseBay = {"id": "America/Goose_Bay", "transitions": [2766, 60, 7133, 0, 11502, 60, 16037, 0, 20406, 60, 24773, 0, 29142, 60, 33509, 0, 37878, 60, 42245, 0, 46614, 60, 50981, 0, 55350, 60, 59885, 0, 64086, 60, 68621, 0, 72990, 60, 77357, 0, 81726, 60, 86093, 0, 90462, 60, 94829, 0, 99198, 60, 103565, 0, 107934, 60, 112469, 0, 116670, 60, 121205, 0, 125574, 60, 129941, 0, 134310, 60, 138677, 0, 143046, 60, 147413, 0, 151276, 60, 156147, 0, 168748, 60, 173787, 0, 177484, 60, 182523, 0, 186388, 60, 191259, 0, 195124, 60, 199995, 0, 203860, 60, 208899, 0, 212596, 60, 217635, 0, 221332, 60, 226371, 0, 230236, 60, 235107, 0, 238972, 60, 243843, 0, 247708, 60, 252579, 0, 256444, 60, 261483, 0, 265180, 60, 270219, 0, 273916, 60, 278955, 0, 282820, 60, 287691, 0, 291556, 60, 296427, 0, 300292, 60, 305331, 0, 309028, 60, 314067, 0, 317764, 60, 322803, 0, 325996, 60, 331707, 0, 334732, 60, 340443, 0, 343468, 60, 349179, 0, 352372, 60, 358083, 0, 361108, 60, 366821, 0, 369846, 60, 375557, 0, 378582, 60, 384293, 0, 387318, 60, 393029, 0, 396054, 60, 401765, 0, 404958, 60, 410669, 0, 413694, 60, 419405, 0, 422430, 60, 428141, 0, 431166, 60, 436877, 0, 439902, 60, 445613, 0, 448806, 60, 454517, 0, 457542, 60, 463253, 0, 466278, 60, 471989, 0, 475014, 60, 480725, 0, 483750, 60, 489461, 0, 492486, 60, 498197, 0, 501390, 60, 507101, 0, 510126, 60, 515837, 0, 518862, 60, 524573, 0, 527598, 60, 533309, 0, 536334, 60, 542045, 0, 545238, 60, 550949, 0, 553974, 60, 559685, 0, 562710, 60, 568421, 0, 571446, 60, 577157, 0, 580182, 60, 585893, 0, 588918, 60, 594629, 0], "names": ["AST", "Atlantic Standard Time", "ADT", "Atlantic Daylight Time"], "std_offset": -240}
americaRioBranco = {"id": "America/Rio_Branco", "transitions": [138821, 60, 142012, 0, 147389, 60, 150076, 0, 156149, 60, 158668, 0], "names": ["AMT", "Acre Standard Time", "AMST", "Acre Summer Time"], "std_offset": -240}
asiaTehran = {"id": "Asia/Tehran", "transitions": [72020, 60, 77155, 0, 80780, 60, 85147, 0, 89564, 60, 94027, 0, 187004, 60, 190411, 0, 194780, 60, 199195, 0, 203540, 60, 207955, 0, 212300, 60, 216715, 0, 221060, 60, 225475, 0, 229820, 60, 234235, 0, 238604, 60, 243019, 0, 247364, 60, 251779, 0, 256124, 60, 260539, 0, 264884, 60, 269299, 0, 273668, 60, 278083, 0, 282428, 60, 286843, 0, 291188, 60, 295603, 0, 299948, 60, 304363, 0, 308732, 60, 313147, 0, 335012, 60, 339427, 0, 343796, 60, 348211, 0, 352556, 60, 356971, 0, 361316, 60, 365731, 0, 370076, 60, 374491, 0, 378860, 60, 383275, 0, 387620, 60, 392035, 0, 396380, 60, 400795, 0, 405140, 60, 409555, 0, 413924, 60, 418339, 0, 422684, 60, 427099, 0, 431444, 60, 435859, 0, 440204, 60, 444619, 0, 448988, 60, 453403, 0, 457748, 60, 462163, 0, 466508, 60, 470923, 0, 475268, 60, 479683, 0, 484052, 60, 488467, 0, 492812, 60, 497227, 0, 501572, 60, 505987, 0, 510332, 60, 514747, 0, 519092, 60, 523507, 0, 527876, 60, 532291, 0, 536636, 60, 541051, 0, 545396, 60, 549811, 0, 554156, 60, 558571, 0, 562940, 60, 567355, 0, 571700, 60, 576115, 0, 580460, 60, 584875, 0, 589220, 60, 593635, 0], "names": ["IRST", "Iran Standard Time", "IRDT", "Iran Daylight Time"], "std_offset": 210}
americaMontserrat = {"id": "America/Montserrat", "transitions": [], "names": ["AST", "Atlantic Standard Time", "ADT", "Atlantic Daylight Time"], "std_offset": -240}
europeBucharest = {"id": "Europe/Bucharest", "transitions": [82390, 60, 85413, 0, 89949, 60, 94150, 0, 98520, 60, 102888, 0, 107256, 60, 111624, 0, 115992, 60, 120360, 0, 124728, 60, 129264, 0, 133632, 60, 138000, 0, 142368, 60, 146736, 0, 151104, 60, 155472, 0, 159840, 60, 164208, 0, 168576, 60, 172944, 0, 177312, 60, 181848, 0, 186214, 60, 190582, 0, 194950, 60, 199318, 0, 203686, 60, 208054, 0, 212422, 60, 216789, 0, 221158, 60, 225525, 0, 230062, 60, 235101, 0, 238801, 60, 243841, 0, 247537, 60, 252577, 0, 256273, 60, 261481, 0, 265009, 60, 270217, 0, 273745, 60, 278953, 0, 282649, 60, 287689, 0, 291385, 60, 296425, 0, 300121, 60, 305329, 0, 308857, 60, 314065, 0, 317593, 60, 322801, 0, 326329, 60, 331537, 0, 335233, 60, 340273, 0, 343969, 60, 349009, 0, 352705, 60, 357913, 0, 361441, 60, 366649, 0, 370177, 60, 375385, 0, 379081, 60, 384121, 0, 387817, 60, 392857, 0, 396553, 60, 401593, 0, 405289, 60, 410497, 0, 414025, 60, 419233, 0, 422761, 60, 427969, 0, 431665, 60, 436705, 0, 440401, 60, 445441, 0, 449137, 60, 454345, 0, 457873, 60, 463081, 0, 466609, 60, 471817, 0, 475513, 60, 480553, 0, 484249, 60, 489289, 0, 492985, 60, 498025, 0, 501721, 60, 506929, 0, 510457, 60, 515665, 0, 519193, 60, 524401, 0, 528097, 60, 533137, 0, 536833, 60, 541873, 0, 545569, 60, 550777, 0, 554305, 60, 559513, 0, 563041, 60, 568249, 0, 571777, 60, 576985, 0, 580681, 60, 585721, 0, 589417, 60, 594457, 0], "names": ["EET", "Eastern European Standard Time", "EEST", "Eastern European Summer Time"], "std_offset": 120}
americaDanmarkshavn = {"id": "America/Danmarkshavn", "transitions": [89957, 60, 94153, 0, 98521, 60, 102889, 0, 107257, 60, 111625, 0, 115993, 60, 120361, 0, 124729, 60, 129265, 0, 133633, 60, 138001, 0, 142369, 60, 146737, 0, 151105, 60, 155473, 0, 159841, 60, 164209, 0, 168577, 60, 172945, 0, 177313, 60, 181849, 0, 186217, 60, 190585, 0, 194953, 60, 199321, 0, 203689, 60, 208057, 0, 212425, 60, 216793, 0, 221161, 60, 225529, 0], "names": ["GMT", "Greenwich Mean Time"], "std_offset": 0}
europeMalta = {"id": "Europe/Malta", "transitions": [3599, 60, 6454, 0, 12167, 60, 15191, 0, 21071, 60, 24094, 0, 28439, 60, 32807, 0, 37703, 60, 41255, 0, 46441, 60, 50136, 0, 55177, 60, 58872, 0, 63913, 60, 67608, 0, 72649, 60, 76344, 0, 81385, 60, 85080, 0, 89809, 60, 93984, 0, 98521, 60, 102889, 0, 107257, 60, 111625, 0, 115993, 60, 120361, 0, 124729, 60, 129265, 0, 133633, 60, 138001, 0, 142369, 60, 146737, 0, 151105, 60, 155473, 0, 159841, 60, 164209, 0, 168577, 60, 172945, 0, 177313, 60, 181849, 0, 186217, 60, 190585, 0, 194953, 60, 199321, 0, 203689, 60, 208057, 0, 212425, 60, 216793, 0, 221161, 60, 225529, 0, 230065, 60, 235105, 0, 238801, 60, 243841, 0, 247537, 60, 252577, 0, 256273, 60, 261481, 0, 265009, 60, 270217, 0, 273745, 60, 278953, 0, 282649, 60, 287689, 0, 291385, 60, 296425, 0, 300121, 60, 305329, 0, 308857, 60, 314065, 0, 317593, 60, 322801, 0, 326329, 60, 331537, 0, 335233, 60, 340273, 0, 343969, 60, 349009, 0, 352705, 60, 357913, 0, 361441, 60, 366649, 0, 370177, 60, 375385, 0, 379081, 60, 384121, 0, 387817, 60, 392857, 0, 396553, 60, 401593, 0, 405289, 60, 410497, 0, 414025, 60, 419233, 0, 422761, 60, 427969, 0, 431665, 60, 436705, 0, 440401, 60, 445441, 0, 449137, 60, 454345, 0, 457873, 60, 463081, 0, 466609, 60, 471817, 0, 475513, 60, 480553, 0, 484249, 60, 489289, 0, 492985, 60, 498025, 0, 501721, 60, 506929, 0, 510457, 60, 515665, 0, 519193, 60, 524401, 0, 528097, 60, 533137, 0, 536833, 60, 541873, 0, 545569, 60, 550777, 0, 554305, 60, 559513, 0, 563041, 60, 568249, 0, 571777, 60, 576985, 0, 580681, 60, 585721, 0, 589417, 60, 594457, 0], "names": ["CET", "Central European Standard Time", "CEST", "Central European Summer Time"], "std_offset": 60}
americaMexicoCity = {"id": "America/Mexico_City", "transitions": [230240, 60, 235111, 0, 238976, 60, 243847, 0, 247712, 60, 252583, 0, 256448, 60, 261487, 0, 265184, 60, 270223, 0, 274760, 60, 278287, 0, 282824, 60, 287695, 0, 291560, 60, 296431, 0, 300296, 60, 305335, 0, 309032, 60, 314071, 0, 317768, 60, 322807, 0, 326504, 60, 331543, 0, 335408, 60, 340279, 0, 344144, 60, 349015, 0, 352880, 60, 357919, 0, 361616, 60, 366655, 0, 370352, 60, 375391, 0, 379256, 60, 384127, 0, 387992, 60, 392863, 0, 396728, 60, 401599, 0, 405464, 60, 410503, 0, 414200, 60, 419239, 0, 422936, 60, 427975, 0, 431840, 60, 436711, 0, 440576, 60, 445447, 0, 449312, 60, 454351, 0, 458048, 60, 463087, 0, 466784, 60, 471823, 0, 475688, 60, 480559, 0, 484424, 60, 489295, 0, 493160, 60, 498031, 0, 501896, 60, 506935, 0, 510632, 60, 515671, 0, 519368, 60, 524407, 0, 528272, 60, 533143, 0, 537008, 60, 541879, 0, 545744, 60, 550783, 0, 554480, 60, 559519, 0, 563216, 60, 568255, 0, 571952, 60, 576991, 0, 580856, 60, 585727, 0, 589592, 60, 594463, 0], "names": ["CST", "Central Standard Time", "CDT", "Central Daylight Time"], "std_offset": -360}
africaKinshasa = {"id": "Africa/Kinshasa", "transitions": [], "names": ["WAT", "West Africa Standard Time", "WAST", "West Africa Summer Time"], "std_offset": 60}
americaDenver = {"id": "America/Denver", "transitions": [2769, 60, 7136, 0, 11505, 60, 16040, 0, 20409, 60, 24776, 0, 29145, 60, 33512, 0, 35193, 60, 42248, 0, 45105, 60, 50984, 0, 55353, 60, 59888, 0, 64089, 60, 68624, 0, 72993, 60, 77360, 0, 81729, 60, 86096, 0, 90465, 60, 94832, 0, 99201, 60, 103568, 0, 107937, 60, 112472, 0, 116673, 60, 121208, 0, 125577, 60, 129944, 0, 134313, 60, 138680, 0, 143049, 60, 147416, 0, 151281, 60, 156152, 0, 160017, 60, 165056, 0, 168753, 60, 173792, 0, 177489, 60, 182528, 0, 186393, 60, 191264, 0, 195129, 60, 200000, 0, 203865, 60, 208904, 0, 212601, 60, 217640, 0, 221337, 60, 226376, 0, 230241, 60, 235112, 0, 238977, 60, 243848, 0, 247713, 60, 252584, 0, 256449, 60, 261488, 0, 265185, 60, 270224, 0, 273921, 60, 278960, 0, 282825, 60, 287696, 0, 291561, 60, 296432, 0, 300297, 60, 305336, 0, 309033, 60, 314072, 0, 317769, 60, 322808, 0, 326001, 60, 331712, 0, 334737, 60, 340448, 0, 343473, 60, 349184, 0, 352377, 60, 358088, 0, 361113, 60, 366824, 0, 369849, 60, 375560, 0, 378585, 60, 384296, 0, 387321, 60, 393032, 0, 396057, 60, 401768, 0, 404961, 60, 410672, 0, 413697, 60, 419408, 0, 422433, 60, 428144, 0, 431169, 60, 436880, 0, 439905, 60, 445616, 0, 448809, 60, 454520, 0, 457545, 60, 463256, 0, 466281, 60, 471992, 0, 475017, 60, 480728, 0, 483753, 60, 489464, 0, 492489, 60, 498200, 0, 501393, 60, 507104, 0, 510129, 60, 515840, 0, 518865, 60, 524576, 0, 527601, 60, 533312, 0, 536337, 60, 542048, 0, 545241, 60, 550952, 0, 553977, 60, 559688, 0, 562713, 60, 568424, 0, 571449, 60, 577160, 0, 580185, 60, 585896, 0, 588921, 60, 594632, 0], "names": ["MST", "Mountain Standard Time", "MDT", "Mountain Daylight Time"], "std_offset": -420}
asiaOral = {"id": "Asia/Oral", "transitions": [98587, 60, 102978, 0, 107346, 60, 111738, 0, 116107, 60, 120498, 0, 124891, 60, 129261, 0, 133629, 60, 137997, 0, 142365, 60, 146733, 0, 151101, 60, 155469, 0, 159837, 60, 164205, 0, 168573, 60, 172942, 0, 177310, 60, 181846, 0, 194947, 60, 199314, 0, 203686, 60, 208054, 0, 212422, 60, 216790, 0, 221158, 60, 225526, 0, 230062, 60, 235102, 0, 238798, 60, 243838, 0, 247534, 60, 252574, 0, 256270, 60, 261478, 0, 265006, 60, 270214, 0, 273742, 60, 278950, 0, 282646, 60, 287686, 0, 291382, 60, 296422, 0, 300118, 60, 305326, 0], "names": ["WKST", "West Kazakhstan Time"], "std_offset": 300}
americaCayenne = {"id": "America/Cayenne", "transitions": [], "names": ["GFT", "French Guiana Time"], "std_offset": -180}
europeTallinn = {"id": "Europe/Tallinn", "transitions": [98589, 60, 102980, 0, 107349, 60, 111740, 0, 116109, 60, 120500, 0, 124893, 60, 129263, 0, 133631, 60, 137999, 0, 142367, 60, 146735, 0, 151103, 60, 155471, 0, 159839, 60, 164207, 0, 168575, 60, 172944, 0, 177312, 60, 181848, 0, 186216, 60, 190584, 0, 194952, 60, 199320, 0, 203688, 60, 208056, 0, 212424, 60, 216792, 0, 221160, 60, 225528, 0, 230064, 60, 235104, 0, 238800, 60, 243840, 0, 247536, 60, 252577, 0, 256273, 60, 261481, 0, 282649, 60, 287689, 0, 291385, 60, 296425, 0, 300121, 60, 305329, 0, 308857, 60, 314065, 0, 317593, 60, 322801, 0, 326329, 60, 331537, 0, 335233, 60, 340273, 0, 343969, 60, 349009, 0, 352705, 60, 357913, 0, 361441, 60, 366649, 0, 370177, 60, 375385, 0, 379081, 60, 384121, 0, 387817, 60, 392857, 0, 396553, 60, 401593, 0, 405289, 60, 410497, 0, 414025, 60, 419233, 0, 422761, 60, 427969, 0, 431665, 60, 436705, 0, 440401, 60, 445441, 0, 449137, 60, 454345, 0, 457873, 60, 463081, 0, 466609, 60, 471817, 0, 475513, 60, 480553, 0, 484249, 60, 489289, 0, 492985, 60, 498025, 0, 501721, 60, 506929, 0, 510457, 60, 515665, 0, 519193, 60, 524401, 0, 528097, 60, 533137, 0, 536833, 60, 541873, 0, 545569, 60, 550777, 0, 554305, 60, 559513, 0, 563041, 60, 568249, 0, 571777, 60, 576985, 0, 580681, 60, 585721, 0, 589417, 60, 594457, 0], "names": ["EET", "Eastern European Standard Time", "EEST", "Eastern European Summer Time"], "std_offset": 120}
europePrague = {"id": "Europe/Prague", "transitions": [81049, 60, 85417, 0, 89953, 60, 94153, 0, 98521, 60, 102889, 0, 107257, 60, 111625, 0, 115993, 60, 120361, 0, 124729, 60, 129265, 0, 133633, 60, 138001, 0, 142369, 60, 146737, 0, 151105, 60, 155473, 0, 159841, 60, 164209, 0, 168577, 60, 172945, 0, 177313, 60, 181849, 0, 186217, 60, 190585, 0, 194953, 60, 199321, 0, 203689, 60, 208057, 0, 212425, 60, 216793, 0, 221161, 60, 225529, 0, 230065, 60, 235105, 0, 238801, 60, 243841, 0, 247537, 60, 252577, 0, 256273, 60, 261481, 0, 265009, 60, 270217, 0, 273745, 60, 278953, 0, 282649, 60, 287689, 0, 291385, 60, 296425, 0, 300121, 60, 305329, 0, 308857, 60, 314065, 0, 317593, 60, 322801, 0, 326329, 60, 331537, 0, 335233, 60, 340273, 0, 343969, 60, 349009, 0, 352705, 60, 357913, 0, 361441, 60, 366649, 0, 370177, 60, 375385, 0, 379081, 60, 384121, 0, 387817, 60, 392857, 0, 396553, 60, 401593, 0, 405289, 60, 410497, 0, 414025, 60, 419233, 0, 422761, 60, 427969, 0, 431665, 60, 436705, 0, 440401, 60, 445441, 0, 449137, 60, 454345, 0, 457873, 60, 463081, 0, 466609, 60, 471817, 0, 475513, 60, 480553, 0, 484249, 60, 489289, 0, 492985, 60, 498025, 0, 501721, 60, 506929, 0, 510457, 60, 515665, 0, 519193, 60, 524401, 0, 528097, 60, 533137, 0, 536833, 60, 541873, 0, 545569, 60, 550777, 0, 554305, 60, 559513, 0, 563041, 60, 568249, 0, 571777, 60, 576985, 0, 580681, 60, 585721, 0, 589417, 60, 594457, 0], "names": ["CET", "Central European Standard Time", "CEST", "Central European Summer Time"], "std_offset": 60}
asiaUrumqi = {"id": "Asia/Urumqi", "transitions": [143200, 60, 146391, 0, 151432, 60, 155127, 0, 160168, 60, 163863, 0, 169072, 60, 172767, 0, 177808, 60, 181503, 0, 186544, 60, 190239, 0], "names": ["CST", "China Standard Time", "CDT", "China Daylight Time"], "std_offset": 480}
americaSaoPaulo = {"id": "America/Sao_Paulo", "transitions": [138819, 60, 142010, 0, 147387, 60, 150074, 0, 156147, 60, 158666, 0, 164715, 60, 167234, 0, 173451, 60, 176306, 0, 182355, 60, 185210, 0, 191091, 60, 193778, 0, 199995, 60, 202346, 0, 208563, 60, 211586, 0, 217299, 60, 220322, 0, 226035, 60, 228890, 0, 234603, 60, 237794, 0, 243363, 60, 246866, 0, 252243, 60, 255434, 0, 260811, 60, 264338, 0, 269715, 60, 272906, 0, 278619, 60, 281642, 0, 287859, 60, 290378, 0, 296259, 60, 299114, 0, 305379, 60, 308018, 0, 313731, 60, 316754, 0, 322971, 60, 325658, 0, 331203, 60, 334226, 0, 340107, 60, 342962, 0, 348843, 60, 351866, 0, 357579, 60, 360602, 0, 366315, 60, 369506, 0, 375219, 60, 378074, 0, 383955, 60, 386810, 0, 392691, 60, 395714, 0, 401427, 60, 404450, 0, 410163, 60, 413186, 0, 418899, 60, 421922, 0, 427803, 60, 430658, 0, 436539, 60, 439394, 0, 445275, 60, 448298, 0, 454011, 60, 457034, 0, 462747, 60, 465938, 0, 471483, 60, 474506, 0, 480387, 60, 483242, 0, 489123, 60, 492146, 0, 497859, 60, 500882, 0, 506595, 60, 509618, 0, 515331, 60, 518354, 0, 524235, 60, 527090, 0, 532971, 60, 535826, 0, 541707, 60, 544562, 0, 550443, 60, 553466, 0, 559179, 60, 562370, 0, 567915, 60, 570938, 0, 576819, 60, 579674, 0, 585555, 60, 588578, 0, 594291, 60], "names": ["BRT", "Brasilia Standard Time", "BRST", "Brasilia Summer Time"], "std_offset": -180}
americaLouisville = {"id": "America/Louisville", "transitions": [2767, 60, 7134, 0, 11503, 60, 16038, 0, 20407, 60, 24774, 0, 29143, 60, 33510, 0, 45103, 60, 50982, 0, 55351, 60, 59886, 0, 64087, 60, 68622, 0, 72991, 60, 77358, 0, 81727, 60, 86094, 0, 90463, 60, 94830, 0, 99199, 60, 103566, 0, 107935, 60, 112470, 0, 116671, 60, 121206, 0, 125575, 60, 129942, 0, 134311, 60, 138678, 0, 143047, 60, 147414, 0, 151279, 60, 156150, 0, 160015, 60, 165054, 0, 168751, 60, 173790, 0, 177487, 60, 182526, 0, 186391, 60, 191262, 0, 195127, 60, 199998, 0, 203863, 60, 208902, 0, 212599, 60, 217638, 0, 221335, 60, 226374, 0, 230239, 60, 235110, 0, 238975, 60, 243846, 0, 247711, 60, 252582, 0, 256447, 60, 261486, 0, 265183, 60, 270222, 0, 273919, 60, 278958, 0, 282823, 60, 287694, 0, 291559, 60, 296430, 0, 300295, 60, 305334, 0, 309031, 60, 314070, 0, 317767, 60, 322806, 0, 325999, 60, 331710, 0, 334735, 60, 340446, 0, 343471, 60, 349182, 0, 352375, 60, 358086, 0, 361111, 60, 366822, 0, 369847, 60, 375558, 0, 378583, 60, 384294, 0, 387319, 60, 393030, 0, 396055, 60, 401766, 0, 404959, 60, 410670, 0, 413695, 60, 419406, 0, 422431, 60, 428142, 0, 431167, 60, 436878, 0, 439903, 60, 445614, 0, 448807, 60, 454518, 0, 457543, 60, 463254, 0, 466279, 60, 471990, 0, 475015, 60, 480726, 0, 483751, 60, 489462, 0, 492487, 60, 498198, 0, 501391, 60, 507102, 0, 510127, 60, 515838, 0, 518863, 60, 524574, 0, 527599, 60, 533310, 0, 536335, 60, 542046, 0, 545239, 60, 550950, 0, 553975, 60, 559686, 0, 562711, 60, 568422, 0, 571447, 60, 577158, 0, 580183, 60, 585894, 0, 588919, 60, 594630, 0], "names": ["EST", "Eastern Standard Time", "EDT", "Eastern Daylight Time"], "std_offset": -300}
asiaMuscat = {"id": "Asia/Muscat", "transitions": [], "names": ["GST", "Gulf Standard Time"], "std_offset": 240}
americaAnguilla = {"id": "America/Anguilla", "transitions": [], "names": ["AST", "Atlantic Standard Time", "ADT", "Atlantic Daylight Time"], "std_offset": -240}
americaMenominee = {"id": "America/Menominee", "transitions": [29143, 60, 33511, 0, 35192, 60, 42247, 0, 45104, 60, 50983, 0, 55352, 60, 59887, 0, 64088, 60, 68623, 0, 72992, 60, 77359, 0, 81728, 60, 86095, 0, 90464, 60, 94831, 0, 99200, 60, 103567, 0, 107936, 60, 112471, 0, 116672, 60, 121207, 0, 125576, 60, 129943, 0, 134312, 60, 138679, 0, 143048, 60, 147415, 0, 151280, 60, 156151, 0, 160016, 60, 165055, 0, 168752, 60, 173791, 0, 177488, 60, 182527, 0, 186392, 60, 191263, 0, 195128, 60, 199999, 0, 203864, 60, 208903, 0, 212600, 60, 217639, 0, 221336, 60, 226375, 0, 230240, 60, 235111, 0, 238976, 60, 243847, 0, 247712, 60, 252583, 0, 256448, 60, 261487, 0, 265184, 60, 270223, 0, 273920, 60, 278959, 0, 282824, 60, 287695, 0, 291560, 60, 296431, 0, 300296, 60, 305335, 0, 309032, 60, 314071, 0, 317768, 60, 322807, 0, 326000, 60, 331711, 0, 334736, 60, 340447, 0, 343472, 60, 349183, 0, 352376, 60, 358087, 0, 361112, 60, 366823, 0, 369848, 60, 375559, 0, 378584, 60, 384295, 0, 387320, 60, 393031, 0, 396056, 60, 401767, 0, 404960, 60, 410671, 0, 413696, 60, 419407, 0, 422432, 60, 428143, 0, 431168, 60, 436879, 0, 439904, 60, 445615, 0, 448808, 60, 454519, 0, 457544, 60, 463255, 0, 466280, 60, 471991, 0, 475016, 60, 480727, 0, 483752, 60, 489463, 0, 492488, 60, 498199, 0, 501392, 60, 507103, 0, 510128, 60, 515839, 0, 518864, 60, 524575, 0, 527600, 60, 533311, 0, 536336, 60, 542047, 0, 545240, 60, 550951, 0, 553976, 60, 559687, 0, 562712, 60, 568423, 0, 571448, 60, 577159, 0, 580184, 60, 585895, 0, 588920, 60, 594631, 0], "names": ["CST", "Central Standard Time", "CDT", "Central Daylight Time"], "std_offset": -360}
asiaSingapore = {"id": "Asia/Singapore", "transitions": [], "names": ["SGT", "Singapore Standard Time"], "std_offset": 480}
pacificEfate = {"id": "Pacific/Efate", "transitions": [120349, 60, 124716, 0, 129805, 60, 133452, 0, 137989, 60, 142188, 0, 146725, 60, 151092, 0, 155461, 60, 159828, 0, 164197, 60, 168564, 0, 172933, 60, 177300, 0, 181669, 60, 186036, 0, 190573, 60, 193428, 0, 199981, 60, 202164, 0], "names": ["VUT", "Vanuatu Standard Time", "VUST", "Vanuatu Summer Time"], "std_offset": 660}
americaBelem = {"id": "America/Belem", "transitions": [138819, 60, 142010, 0, 147387, 60, 150074, 0, 156147, 60, 158666, 0], "names": ["BRT", "Brasilia Standard Time", "BRST", "Brasilia Summer Time"], "std_offset": -180}
antarcticaDumontDUrville = {"id": "Antarctica/DumontDUrville", "transitions": [], "names": ["DDUT", "Dumont-d\u2019Urville Time"], "std_offset": 600}
americaIndianaVincennes = {"id": "America/Indiana/Vincennes", "transitions": [2767, 60, 7134, 0, 317767, 60, 322807, 0, 326000, 60, 331711, 0, 334735, 60, 340446, 0, 343471, 60, 349182, 0, 352375, 60, 358086, 0, 361111, 60, 366822, 0, 369847, 60, 375558, 0, 378583, 60, 384294, 0, 387319, 60, 393030, 0, 396055, 60, 401766, 0, 404959, 60, 410670, 0, 413695, 60, 419406, 0, 422431, 60, 428142, 0, 431167, 60, 436878, 0, 439903, 60, 445614, 0, 448807, 60, 454518, 0, 457543, 60, 463254, 0, 466279, 60, 471990, 0, 475015, 60, 480726, 0, 483751, 60, 489462, 0, 492487, 60, 498198, 0, 501391, 60, 507102, 0, 510127, 60, 515838, 0, 518863, 60, 524574, 0, 527599, 60, 533310, 0, 536335, 60, 542046, 0, 545239, 60, 550950, 0, 553975, 60, 559686, 0, 562711, 60, 568422, 0, 571447, 60, 577158, 0, 580183, 60, 585894, 0, 588919, 60, 594630, 0], "names": ["EST", "Eastern Standard Time", "EDT", "Eastern Daylight Time"], "std_offset": -300}
asiaYerevan = {"id": "Asia/Yerevan", "transitions": [98588, 60, 102979, 0, 107348, 60, 111739, 0, 116108, 60, 120499, 0, 124892, 60, 129262, 0, 133630, 60, 137998, 0, 142366, 60, 146734, 0, 151102, 60, 155470, 0, 159838, 60, 164206, 0, 168574, 60, 172942, 0, 177310, 60, 181846, 0, 186214, 60, 190583, 0, 194948, 60, 199315, 0, 203687, 60, 208055, 0, 212423, 60, 216791, 0, 221159, 60, 225527, 0, 238798, 60, 243838, 0, 247534, 60, 252574, 0, 256270, 60, 261478, 0, 265006, 60, 270214, 0, 273742, 60, 278950, 0, 282646, 60, 287686, 0, 291382, 60, 296422, 0, 300118, 60, 305326, 0, 308854, 60, 314062, 0, 317590, 60, 322798, 0, 326326, 60, 331534, 0, 335230, 60, 340270, 0, 343966, 60, 349006, 0, 352702, 60, 357910, 0, 361438, 60, 366646, 0], "names": ["AMT", "Armenia Standard Time", "AMST", "Armenia Summer Time"], "std_offset": 240}
europeIstanbul = {"id": "Europe/Istanbul", "transitions": [2926, 60, 6621, 0, 11662, 60, 15357, 0, 20566, 60, 24261, 0, 29975, 60, 33672, 0, 37200, 60, 42410, 0, 45934, 60, 50973, 0, 56230, 60, 59877, 0, 63574, 60, 68277, 0, 72310, 60, 85772, 0, 89952, 60, 94508, 0, 98520, 60, 103244, 0, 107256, 60, 111980, 0, 119013, 60, 120524, 0, 134109, 60, 137973, 0, 142368, 60, 146736, 0, 151104, 60, 155472, 0, 159840, 60, 164208, 0, 168576, 60, 172944, 0, 177312, 60, 181848, 0, 186215, 60, 190583, 0, 194951, 60, 199319, 0, 203687, 60, 208055, 0, 212423, 60, 216791, 0, 221159, 60, 225527, 0, 230063, 60, 235103, 0, 238799, 60, 243839, 0, 247535, 60, 252575, 0, 256271, 60, 261479, 0, 265007, 60, 270215, 0, 273743, 60, 278951, 0, 282647, 60, 287687, 0, 291383, 60, 296423, 0, 300119, 60, 305327, 0, 308855, 60, 314063, 0, 317591, 60, 322799, 0, 326329, 60, 331537, 0, 335233, 60, 340273, 0, 343969, 60, 349009, 0, 352705, 60, 357913, 0, 361465, 60, 366649, 0, 370177, 60, 375385, 0, 379081, 60, 384121, 0, 387817, 60, 392857, 0, 396553, 60, 401593, 0, 405289, 60, 410497, 0, 414025, 60, 419233, 0, 422761, 60, 427969, 0, 431665, 60, 436705, 0, 440401, 60, 445441, 0, 449137, 60, 454345, 0, 457873, 60, 463081, 0, 466609, 60, 471817, 0, 475513, 60, 480553, 0, 484249, 60, 489289, 0, 492985, 60, 498025, 0, 501721, 60, 506929, 0, 510457, 60, 515665, 0, 519193, 60, 524401, 0, 528097, 60, 533137, 0, 536833, 60, 541873, 0, 545569, 60, 550777, 0, 554305, 60, 559513, 0, 563041, 60, 568249, 0, 571777, 60, 576985, 0, 580681, 60, 585721, 0, 589417, 60, 594457, 0], "names": ["EET", "Eastern European Standard Time", "EEST", "Eastern European Summer Time"], "std_offset": 120}
africaKhartoum = {"id": "Africa/Khartoum", "transitions": [2878, 60, 6885, 0, 11614, 60, 15645, 0, 20398, 60, 24429, 0, 29134, 60, 33189, 0, 37870, 60, 41949, 0, 46606, 60, 50709, 0, 55342, 60, 59493, 0, 64078, 60, 68253, 0, 72982, 60, 77013, 0, 81718, 60, 85773, 0, 90454, 60, 94557, 0, 99190, 60, 103317, 0, 107926, 60, 112077, 0, 116662, 60, 120837, 0, 125566, 60, 129621, 0, 134302, 60, 138381, 0], "names": ["EAT", "East Africa Time"], "std_offset": 180}
americaMendoza = {"id": "America/Mendoza", "transitions": [35595, 60, 37946, 0, 165819, 60, 168074, 0, 173451, 60, 176810, 0, 182212, 60, 185499, 0, 190972, 60, 194283, 0, 260811, 60, 264459, 0, 333051, 60, 334898, 0], "names": ["ART", "Argentina Standard Time", "ARST", "Argentina Summer Time"], "std_offset": -180}
antarcticaMcMurdo = {"id": "Antarctica/McMurdo", "transitions": [42398, 60, 45086, 0, 50966, 60, 54158, 0, 59870, 60, 62894, 0, 68606, 60, 71630, 0, 77342, 60, 80366, 0, 86078, 60, 89102, 0, 94814, 60, 97838, 0, 103550, 60, 106742, 0, 112454, 60, 115478, 0, 121190, 60, 124214, 0, 129926, 60, 132950, 0, 138662, 60, 141686, 0, 147398, 60, 150422, 0, 156134, 60, 159326, 0, 165038, 60, 168062, 0, 173270, 60, 177134, 0, 182006, 60, 185870, 0, 190742, 60, 194606, 0, 199478, 60, 203510, 0, 208214, 60, 212246, 0, 216950, 60, 220982, 0, 225686, 60, 229718, 0, 234590, 60, 238454, 0, 243326, 60, 247190, 0, 252062, 60, 256094, 0, 260798, 60, 264830, 0, 269534, 60, 273566, 0, 278438, 60, 282302, 0, 287174, 60, 291038, 0, 295910, 60, 299942, 0, 304646, 60, 308678, 0, 313382, 60, 317414, 0, 322118, 60, 326150, 0, 330854, 60, 335390, 0, 339590, 60, 344126, 0, 348326, 60, 352862, 0, 357062, 60, 361598, 0, 365798, 60, 370334, 0, 374702, 60, 379238, 0, 383438, 60, 387974, 0, 392174, 60, 396710, 0, 400910, 60, 405446, 0, 409646, 60, 414182, 0, 418382, 60, 422918, 0, 427286, 60, 431822, 0, 436022, 60, 440558, 0, 444758, 60, 449294, 0, 453494, 60, 458030, 0, 462230, 60, 466766, 0, 470966, 60, 475670, 0, 479870, 60, 484406, 0, 488606, 60, 493142, 0, 497342, 60, 501878, 0, 506078, 60, 510614, 0, 514814, 60, 519350, 0, 523718, 60, 528254, 0, 532454, 60, 536990, 0, 541190, 60, 545726, 0, 549926, 60, 554462, 0, 558662, 60, 563198, 0, 567398, 60, 571934, 0, 576302, 60, 580838, 0, 585038, 60, 589574, 0, 593774, 60], "names": ["NZST", "New Zealand Standard Time", "NZDT", "New Zealand Daylight Time"], "std_offset": 720}
antarcticaRothera = {"id": "Antarctica/Rothera", "transitions": [], "names": ["ROTT", "Rothera Time"], "std_offset": -180}
americaRecife = {"id": "America/Recife", "transitions": [138819, 60, 142010, 0, 147387, 60, 150074, 0, 156147, 60, 158666, 0, 164715, 60, 167234, 0, 173451, 60, 176306, 0, 260811, 60, 264338, 0, 269715, 60, 269882, 0, 278619, 60, 281642, 0], "names": ["BRT", "Brasilia Standard Time", "BRST", "Brasilia Summer Time"], "std_offset": -180}
americaAnchorage = {"id": "America/Anchorage", "transitions": [2772, 60, 7139, 0, 11508, 60, 16043, 0, 20412, 60, 24779, 0, 29148, 60, 33515, 0, 35196, 60, 42251, 0, 45108, 60, 50987, 0, 55356, 60, 59891, 0, 64092, 60, 68627, 0, 72996, 60, 77363, 0, 81732, 60, 86099, 0, 90468, 60, 94835, 0, 99204, 60, 103571, 0, 107940, 60, 112475, 0, 116676, 60, 121211, 0, 125579, 60, 129946, 0, 134315, 60, 138682, 0, 143051, 60, 147418, 0, 151283, 60, 156154, 0, 160019, 60, 165058, 0, 168755, 60, 173794, 0, 177491, 60, 182530, 0, 186395, 60, 191266, 0, 195131, 60, 200002, 0, 203867, 60, 208906, 0, 212603, 60, 217642, 0, 221339, 60, 226378, 0, 230243, 60, 235114, 0, 238979, 60, 243850, 0, 247715, 60, 252586, 0, 256451, 60, 261490, 0, 265187, 60, 270226, 0, 273923, 60, 278962, 0, 282827, 60, 287698, 0, 291563, 60, 296434, 0, 300299, 60, 305338, 0, 309035, 60, 314074, 0, 317771, 60, 322810, 0, 326003, 60, 331714, 0, 334739, 60, 340450, 0, 343475, 60, 349186, 0, 352379, 60, 358090, 0, 361115, 60, 366826, 0, 369851, 60, 375562, 0, 378587, 60, 384298, 0, 387323, 60, 393034, 0, 396059, 60, 401770, 0, 404963, 60, 410674, 0, 413699, 60, 419410, 0, 422435, 60, 428146, 0, 431171, 60, 436882, 0, 439907, 60, 445618, 0, 448811, 60, 454522, 0, 457547, 60, 463258, 0, 466283, 60, 471994, 0, 475019, 60, 480730, 0, 483755, 60, 489466, 0, 492491, 60, 498202, 0, 501395, 60, 507106, 0, 510131, 60, 515842, 0, 518867, 60, 524578, 0, 527603, 60, 533314, 0, 536339, 60, 542050, 0, 545243, 60, 550954, 0, 553979, 60, 559690, 0, 562715, 60, 568426, 0, 571451, 60, 577162, 0, 580187, 60, 585898, 0, 588923, 60, 594634, 0], "names": ["AKST", "Alaska Standard Time", "AKDT", "Alaska Daylight Time"], "std_offset": -540}
arcticLongyearbyen = {"id": "Arctic/Longyearbyen", "transitions": [89953, 60, 94153, 0, 98521, 60, 102889, 0, 107257, 60, 111625, 0, 115993, 60, 120361, 0, 124729, 60, 129265, 0, 133633, 60, 138001, 0, 142369, 60, 146737, 0, 151105, 60, 155473, 0, 159841, 60, 164209, 0, 168577, 60, 172945, 0, 177313, 60, 181849, 0, 186217, 60, 190585, 0, 194953, 60, 199321, 0, 203689, 60, 208057, 0, 212425, 60, 216793, 0, 221161, 60, 225529, 0, 230065, 60, 235105, 0, 238801, 60, 243841, 0, 247537, 60, 252577, 0, 256273, 60, 261481, 0, 265009, 60, 270217, 0, 273745, 60, 278953, 0, 282649, 60, 287689, 0, 291385, 60, 296425, 0, 300121, 60, 305329, 0, 308857, 60, 314065, 0, 317593, 60, 322801, 0, 326329, 60, 331537, 0, 335233, 60, 340273, 0, 343969, 60, 349009, 0, 352705, 60, 357913, 0, 361441, 60, 366649, 0, 370177, 60, 375385, 0, 379081, 60, 384121, 0, 387817, 60, 392857, 0, 396553, 60, 401593, 0, 405289, 60, 410497, 0, 414025, 60, 419233, 0, 422761, 60, 427969, 0, 431665, 60, 436705, 0, 440401, 60, 445441, 0, 449137, 60, 454345, 0, 457873, 60, 463081, 0, 466609, 60, 471817, 0, 475513, 60, 480553, 0, 484249, 60, 489289, 0, 492985, 60, 498025, 0, 501721, 60, 506929, 0, 510457, 60, 515665, 0, 519193, 60, 524401, 0, 528097, 60, 533137, 0, 536833, 60, 541873, 0, 545569, 60, 550777, 0, 554305, 60, 559513, 0, 563041, 60, 568249, 0, 571777, 60, 576985, 0, 580681, 60, 585721, 0, 589417, 60, 594457, 0], "names": ["CET", "Central European Standard Time", "CEST", "Central European Summer Time"], "std_offset": 60}
americaArgentinaSanJuan = {"id": "America/Argentina/San_Juan", "transitions": [35595, 60, 37946, 0, 165819, 60, 168074, 0, 173451, 60, 176810, 0, 182355, 60, 185498, 0, 191091, 60, 194282, 0, 199827, 60, 203186, 0, 260811, 60, 264459, 0, 333051, 60, 334898, 0], "names": ["ART", "Argentina Standard Time", "ARST", "Argentina Summer Time"], "std_offset": -180}
australiaDarwin = {"id": "Australia/Darwin", "transitions": [], "names": ["ACST", "Australian Central Standard Time", "ACDT", "Australian Central Daylight Time"], "std_offset": 570}
asiaOmsk = {"id": "Asia/Omsk", "transitions": [98586, 60, 102977, 0, 107346, 60, 111737, 0, 116106, 60, 120497, 0, 124890, 60, 129260, 0, 133628, 60, 137996, 0, 142364, 60, 146732, 0, 151100, 60, 155468, 0, 159836, 60, 164204, 0, 168572, 60, 172940, 0, 177308, 60, 181844, 0, 186212, 60, 190581, 0, 194945, 60, 199312, 0, 203684, 60, 208052, 0, 212420, 60, 216788, 0, 221156, 60, 225524, 0, 230060, 60, 235100, 0, 238796, 60, 243836, 0, 247532, 60, 252572, 0, 256268, 60, 261476, 0, 265004, 60, 270212, 0, 273740, 60, 278948, 0, 282644, 60, 287684, 0, 291380, 60, 296420, 0, 300116, 60, 305324, 0, 308852, 60, 314060, 0, 317588, 60, 322796, 0, 326324, 60, 331532, 0, 335228, 60, 340268, 0, 343964, 60, 349004, 0, 352700, 60, 357908, 0], "names": ["OMST", "Omsk Standard Time", "OMSST", "Omsk Summer Time"], "std_offset": 420}
americaBarbados = {"id": "America/Barbados", "transitions": [65262, 60, 67949, 0, 72654, 60, 76685, 0, 81390, 60, 85421, 0, 90294, 60, 94085, 0], "names": ["AST", "Atlantic Standard Time", "ADT", "Atlantic Daylight Time"], "std_offset": -240}
americaMontevideo = {"id": "America/Montevideo", "transitions": [20259, 60, 22970, 0, 43586, 60, 59162, 0, 69459, 60, 72290, 0, 85443, 60, 90554, 0, 157347, 60, 159530, 0, 166059, 60, 168242, 0, 173787, 60, 176810, 0, 182355, 60, 185546, 0, 191259, 60, 194282, 0, 199827, 60, 203018, 0, 304323, 60, 308860, 0, 313565, 60, 317260, 0, 322133, 60, 325996, 0, 331037, 60, 334732, 0, 339773, 60, 343468, 0, 348509, 60, 352372, 0, 357245, 60, 361108, 0, 365981, 60, 369844, 0, 374885, 60, 378580, 0, 383621, 60, 387316, 0, 392357, 60, 396052, 0, 401093, 60, 404956, 0, 409829, 60, 413692, 0, 418565, 60, 422428, 0, 427469, 60, 431164, 0, 436205, 60, 439900, 0, 444941, 60, 448804, 0, 453677, 60, 457540, 0, 462413, 60, 466276, 0, 471149, 60, 475012, 0, 480053, 60, 483748, 0, 488789, 60, 492484, 0, 497525, 60, 501388, 0, 506261, 60, 510124, 0, 514997, 60, 518860, 0, 523901, 60, 527596, 0, 532637, 60, 536332, 0, 541373, 60, 545236, 0, 550109, 60, 553972, 0, 558845, 60, 562708, 0, 567581, 60, 571444, 0, 576485, 60, 580180, 0, 585221, 60, 588916, 0, 593957, 60], "names": ["UYT", "Uruguay Standard Time", "UYST", "Uruguay Summer Time"], "std_offset": -180}
asiaTokyo = {"id": "Asia/Tokyo", "transitions": [], "names": ["JST", "Japan Standard Time", "JDT", "Japan Daylight Time"], "std_offset": 540}
europeParis = {"id": "Europe/Paris", "transitions": [54672, 60, 59039, 0, 63577, 60, 67777, 0, 72313, 60, 76681, 0, 81049, 60, 85417, 0, 89953, 60, 94153, 0, 98521, 60, 102889, 0, 107257, 60, 111625, 0, 115993, 60, 120361, 0, 124729, 60, 129265, 0, 133633, 60, 138001, 0, 142369, 60, 146737, 0, 151105, 60, 155473, 0, 159841, 60, 164209, 0, 168577, 60, 172945, 0, 177313, 60, 181849, 0, 186217, 60, 190585, 0, 194953, 60, 199321, 0, 203689, 60, 208057, 0, 212425, 60, 216793, 0, 221161, 60, 225529, 0, 230065, 60, 235105, 0, 238801, 60, 243841, 0, 247537, 60, 252577, 0, 256273, 60, 261481, 0, 265009, 60, 270217, 0, 273745, 60, 278953, 0, 282649, 60, 287689, 0, 291385, 60, 296425, 0, 300121, 60, 305329, 0, 308857, 60, 314065, 0, 317593, 60, 322801, 0, 326329, 60, 331537, 0, 335233, 60, 340273, 0, 343969, 60, 349009, 0, 352705, 60, 357913, 0, 361441, 60, 366649, 0, 370177, 60, 375385, 0, 379081, 60, 384121, 0, 387817, 60, 392857, 0, 396553, 60, 401593, 0, 405289, 60, 410497, 0, 414025, 60, 419233, 0, 422761, 60, 427969, 0, 431665, 60, 436705, 0, 440401, 60, 445441, 0, 449137, 60, 454345, 0, 457873, 60, 463081, 0, 466609, 60, 471817, 0, 475513, 60, 480553, 0, 484249, 60, 489289, 0, 492985, 60, 498025, 0, 501721, 60, 506929, 0, 510457, 60, 515665, 0, 519193, 60, 524401, 0, 528097, 60, 533137, 0, 536833, 60, 541873, 0, 545569, 60, 550777, 0, 554305, 60, 559513, 0, 563041, 60, 568249, 0, 571777, 60, 576985, 0, 580681, 60, 585721, 0, 589417, 60, 594457, 0], "names": ["CET", "Central European Standard Time", "CEST", "Central European Summer Time"], "std_offset": 60}
asiaCalcutta = {"id": "Asia/Calcutta", "transitions": [], "names": ["IST", "India Standard Time"], "std_offset": 330}
asiaDubai = {"id": "Asia/Dubai", "transitions": [], "names": ["GST", "Gulf Standard Time"], "std_offset": 240}
pacificSaipan = {"id": "Pacific/Saipan", "transitions": [], "names": ["ChST", "Chamorro Standard Time"], "std_offset": 600}
americaCambridgeBay = {"id": "America/Cambridge_Bay", "transitions": [90465, 60, 94832, 0, 99201, 60, 103568, 0, 107937, 60, 112472, 0, 116673, 60, 121208, 0, 125577, 60, 129944, 0, 134313, 60, 138680, 0, 143049, 60, 147416, 0, 151281, 60, 156152, 0, 160017, 60, 165056, 0, 168753, 60, 173792, 0, 177489, 60, 182528, 0, 186393, 60, 191264, 0, 195129, 60, 200000, 0, 203865, 60, 208904, 0, 212601, 60, 217640, 0, 221337, 60, 226376, 0, 230241, 60, 235112, 0, 238977, 60, 243848, 0, 247713, 60, 252584, 0, 256449, 60, 261488, 0, 265184, 60, 270223, 0, 273921, 60, 278960, 0, 282825, 60, 287696, 0, 291561, 60, 296432, 0, 300297, 60, 305336, 0, 309033, 60, 314072, 0, 317769, 60, 322808, 0, 326001, 60, 331712, 0, 334737, 60, 340448, 0, 343473, 60, 349184, 0, 352377, 60, 358088, 0, 361113, 60, 366824, 0, 369849, 60, 375560, 0, 378585, 60, 384296, 0, 387321, 60, 393032, 0, 396057, 60, 401768, 0, 404961, 60, 410672, 0, 413697, 60, 419408, 0, 422433, 60, 428144, 0, 431169, 60, 436880, 0, 439905, 60, 445616, 0, 448809, 60, 454520, 0, 457545, 60, 463256, 0, 466281, 60, 471992, 0, 475017, 60, 480728, 0, 483753, 60, 489464, 0, 492489, 60, 498200, 0, 501393, 60, 507104, 0, 510129, 60, 515840, 0, 518865, 60, 524576, 0, 527601, 60, 533312, 0, 536337, 60, 542048, 0, 545241, 60, 550952, 0, 553977, 60, 559688, 0, 562713, 60, 568424, 0, 571449, 60, 577160, 0, 580185, 60, 585896, 0, 588921, 60, 594632, 0], "names": ["MST", "Mountain Standard Time", "MDT", "Mountain Daylight Time"], "std_offset": -420}
americaGrenada = {"id": "America/Grenada", "transitions": [], "names": ["AST", "Atlantic Standard Time", "ADT", "Atlantic Daylight Time"], "std_offset": -240}
atlanticStanley = {"id": "Atlantic/Stanley", "transitions": [120363, 60, 125570, 0, 128931, 60, 134306, 0, 137667, 60, 142875, 0, 146404, 60, 151611, 0, 155140, 60, 160347, 0, 163876, 60, 169083, 0, 172612, 60, 177987, 0, 181348, 60, 186723, 0, 190252, 60, 195459, 0, 198988, 60, 204195, 0, 207724, 60, 212931, 0, 216460, 60, 221667, 0, 225196, 60, 230571, 0, 234100, 60, 239307, 0, 242836, 60, 248043, 0, 251572, 60, 256779, 0, 260308, 60, 265515, 0, 269044, 60, 274253, 0, 277614, 60, 283157, 0, 286350, 60, 291893, 0, 295254, 60, 300629, 0, 303990, 60, 309365, 0, 312726, 60, 318101, 0, 321462, 60, 326837, 0, 330198, 60, 335741, 0, 339102, 60, 344477, 0, 347838, 60, 353213, 0], "names": ["FKT", "Falkland Islands Standard Time", "FKST", "Falkland Islands Summer Time"], "std_offset": -180}
australiaLindeman = {"id": "Australia/Lindeman", "transitions": [16024, 60, 18880, 0, 173776, 60, 176800, 0, 182512, 60, 185536, 0, 191248, 60, 194272, 0, 199984, 60, 203176, 0, 208888, 60, 211912, 0], "names": ["AEST", "Australian Eastern Standard Time", "AEDT", "Australian Eastern Daylight Time"], "std_offset": 600}
americaManaus = {"id": "America/Manaus", "transitions": [138820, 60, 142011, 0, 147388, 60, 150075, 0, 156148, 60, 158667, 0, 208564, 60, 211587, 0], "names": ["AMT", "Amazon Standard Time", "AMST", "Amazon Summer Time"], "std_offset": -240}
africaJohannesburg = {"id": "Africa/Johannesburg", "transitions": [], "names": ["SAST", "South Africa Standard Time"], "std_offset": 120}
africaPortoNovo = {"id": "Africa/Porto-Novo", "transitions": [], "names": ["WAT", "West Africa Standard Time", "WAST", "West Africa Summer Time"], "std_offset": 60}
americaMiquelon = {"id": "America/Miquelon", "transitions": [151277, 60, 156148, 0, 160013, 60, 165052, 0, 168749, 60, 173788, 0, 177485, 60, 182524, 0, 186389, 60, 191260, 0, 195125, 60, 199996, 0, 203861, 60, 208900, 0, 212597, 60, 217636, 0, 221333, 60, 226372, 0, 230237, 60, 235108, 0, 238973, 60, 243844, 0, 247709, 60, 252580, 0, 256445, 60, 261484, 0, 265181, 60, 270220, 0, 273917, 60, 278956, 0, 282821, 60, 287692, 0, 291557, 60, 296428, 0, 300293, 60, 305332, 0, 309029, 60, 314068, 0, 317765, 60, 322804, 0, 325997, 60, 331708, 0, 334733, 60, 340444, 0, 343469, 60, 349180, 0, 352373, 60, 358084, 0, 361109, 60, 366820, 0, 369845, 60, 375556, 0, 378581, 60, 384292, 0, 387317, 60, 393028, 0, 396053, 60, 401764, 0, 404957, 60, 410668, 0, 413693, 60, 419404, 0, 422429, 60, 428140, 0, 431165, 60, 436876, 0, 439901, 60, 445612, 0, 448805, 60, 454516, 0, 457541, 60, 463252, 0, 466277, 60, 471988, 0, 475013, 60, 480724, 0, 483749, 60, 489460, 0, 492485, 60, 498196, 0, 501389, 60, 507100, 0, 510125, 60, 515836, 0, 518861, 60, 524572, 0, 527597, 60, 533308, 0, 536333, 60, 542044, 0, 545237, 60, 550948, 0, 553973, 60, 559684, 0, 562709, 60, 568420, 0, 571445, 60, 577156, 0, 580181, 60, 585892, 0, 588917, 60, 594628, 0], "names": ["PMST", "Saint Pierre and Miquelon Standard Time", "PMDT", "Saint Pierre and Miquelon Daylight Time"], "std_offset": -180}
americaMerida = {"id": "America/Merida", "transitions": [230240, 60, 235111, 0, 238976, 60, 243847, 0, 247712, 60, 252583, 0, 256448, 60, 261487, 0, 265184, 60, 270223, 0, 274760, 60, 278287, 0, 282824, 60, 287695, 0, 291560, 60, 296431, 0, 300296, 60, 305335, 0, 309032, 60, 314071, 0, 317768, 60, 322807, 0, 326504, 60, 331543, 0, 335408, 60, 340279, 0, 344144, 60, 349015, 0, 352880, 60, 357919, 0, 361616, 60, 366655, 0, 370352, 60, 375391, 0, 379256, 60, 384127, 0, 387992, 60, 392863, 0, 396728, 60, 401599, 0, 405464, 60, 410503, 0, 414200, 60, 419239, 0, 422936, 60, 427975, 0, 431840, 60, 436711, 0, 440576, 60, 445447, 0, 449312, 60, 454351, 0, 458048, 60, 463087, 0, 466784, 60, 471823, 0, 475688, 60, 480559, 0, 484424, 60, 489295, 0, 493160, 60, 498031, 0, 501896, 60, 506935, 0, 510632, 60, 515671, 0, 519368, 60, 524407, 0, 528272, 60, 533143, 0, 537008, 60, 541879, 0, 545744, 60, 550783, 0, 554480, 60, 559519, 0, 563216, 60, 568255, 0, 571952, 60, 576991, 0, 580856, 60, 585727, 0, 589592, 60, 594463, 0], "names": ["CST", "Central Standard Time", "CDT", "Central Daylight Time"], "std_offset": -360}
africaDouala = {"id": "Africa/Douala", "transitions": [], "names": ["WAT", "West Africa Standard Time", "WAST", "West Africa Summer Time"], "std_offset": 60}
asiaHongKong = {"id": "Asia/Hong_Kong", "transitions": [2587, 60, 6954, 0, 11323, 60, 15690, 0, 20059, 60, 24594, 0, 28963, 60, 33330, 0, 35011, 60, 42066, 0, 46435, 60, 50802, 0, 55171, 60, 59538, 0, 82051, 60, 85914, 0], "names": ["HKT", "Hong Kong Standard Time", "HKST", "Hong Kong Summer Time"], "std_offset": 480}
europeMariehamn = {"id": "Europe/Mariehamn", "transitions": [98520, 60, 102888, 0, 107256, 60, 111624, 0, 115993, 60, 120361, 0, 124729, 60, 129265, 0, 133633, 60, 138001, 0, 142369, 60, 146737, 0, 151105, 60, 155473, 0, 159841, 60, 164209, 0, 168577, 60, 172945, 0, 177313, 60, 181849, 0, 186217, 60, 190585, 0, 194953, 60, 199321, 0, 203689, 60, 208057, 0, 212425, 60, 216793, 0, 221161, 60, 225529, 0, 230065, 60, 235105, 0, 238801, 60, 243841, 0, 247537, 60, 252577, 0, 256273, 60, 261481, 0, 265009, 60, 270217, 0, 273745, 60, 278953, 0, 282649, 60, 287689, 0, 291385, 60, 296425, 0, 300121, 60, 305329, 0, 308857, 60, 314065, 0, 317593, 60, 322801, 0, 326329, 60, 331537, 0, 335233, 60, 340273, 0, 343969, 60, 349009, 0, 352705, 60, 357913, 0, 361441, 60, 366649, 0, 370177, 60, 375385, 0, 379081, 60, 384121, 0, 387817, 60, 392857, 0, 396553, 60, 401593, 0, 405289, 60, 410497, 0, 414025, 60, 419233, 0, 422761, 60, 427969, 0, 431665, 60, 436705, 0, 440401, 60, 445441, 0, 449137, 60, 454345, 0, 457873, 60, 463081, 0, 466609, 60, 471817, 0, 475513, 60, 480553, 0, 484249, 60, 489289, 0, 492985, 60, 498025, 0, 501721, 60, 506929, 0, 510457, 60, 515665, 0, 519193, 60, 524401, 0, 528097, 60, 533137, 0, 536833, 60, 541873, 0, 545569, 60, 550777, 0, 554305, 60, 559513, 0, 563041, 60, 568249, 0, 571777, 60, 576985, 0, 580681, 60, 585721, 0, 589417, 60, 594457, 0], "names": ["EET", "Eastern European Standard Time", "EEST", "Eastern European Summer Time"], "std_offset": 120}
asiaNovosibirsk = {"id": "Asia/Novosibirsk", "transitions": [98585, 60, 102976, 0, 107345, 60, 111736, 0, 116105, 60, 120496, 0, 124889, 60, 129259, 0, 133627, 60, 137995, 0, 142363, 60, 146731, 0, 151099, 60, 155467, 0, 159835, 60, 164203, 0, 168571, 60, 172939, 0, 177307, 60, 181843, 0, 186211, 60, 190580, 0, 194944, 60, 199311, 0, 203683, 60, 208052, 0, 212420, 60, 216788, 0, 221156, 60, 225524, 0, 230060, 60, 235100, 0, 238796, 60, 243836, 0, 247532, 60, 252572, 0, 256268, 60, 261476, 0, 265004, 60, 270212, 0, 273740, 60, 278948, 0, 282644, 60, 287684, 0, 291380, 60, 296420, 0, 300116, 60, 305324, 0, 308852, 60, 314060, 0, 317588, 60, 322796, 0, 326324, 60, 331532, 0, 335228, 60, 340268, 0, 343964, 60, 349004, 0, 352700, 60, 357908, 0], "names": ["NOVT", "Novosibirsk Standard Time", "NOVST", "Novosibirsk Summer Time"], "std_offset": 420}
americaCatamarca = {"id": "America/Catamarca", "transitions": [35595, 60, 37946, 0, 165819, 60, 168074, 0, 173451, 60, 176810, 0, 182355, 60, 185546, 0, 199827, 60, 203186, 0, 260811, 60, 264459, 0, 333051, 60, 334898, 0], "names": ["ART", "Argentina Standard Time", "ARST", "Argentina Summer Time"], "std_offset": -180}
americaArgentinaLaRioja = {"id": "America/Argentina/La_Rioja", "transitions": [35595, 60, 37946, 0, 165819, 60, 168074, 0, 173451, 60, 176810, 0, 182355, 60, 185498, 0, 191091, 60, 194282, 0, 199827, 60, 203186, 0, 260811, 60, 264459, 0, 333051, 60, 334898, 0], "names": ["ART", "Argentina Standard Time", "ARST", "Argentina Summer Time"], "std_offset": -180}
indianMaldives = {"id": "Indian/Maldives", "transitions": [], "names": ["MVT", "Maldives Time"], "std_offset": 300}
africaFreetown = {"id": "Africa/Freetown", "transitions": [], "names": ["GMT", "Greenwich Mean Time"], "std_offset": 0}
americaInuvik = {"id": "America/Inuvik", "transitions": [90465, 60, 94832, 0, 99201, 60, 103568, 0, 107937, 60, 112472, 0, 116673, 60, 121208, 0, 125577, 60, 129944, 0, 134313, 60, 138680, 0, 143049, 60, 147416, 0, 151281, 60, 156152, 0, 160017, 60, 165056, 0, 168753, 60, 173792, 0, 177489, 60, 182528, 0, 186393, 60, 191264, 0, 195129, 60, 200000, 0, 203865, 60, 208904, 0, 212601, 60, 217640, 0, 221337, 60, 226376, 0, 230241, 60, 235112, 0, 238977, 60, 243848, 0, 247713, 60, 252584, 0, 256449, 60, 261488, 0, 265185, 60, 270224, 0, 273921, 60, 278960, 0, 282825, 60, 287696, 0, 291561, 60, 296432, 0, 300297, 60, 305336, 0, 309033, 60, 314072, 0, 317769, 60, 322808, 0, 326001, 60, 331712, 0, 334737, 60, 340448, 0, 343473, 60, 349184, 0, 352377, 60, 358088, 0, 361113, 60, 366824, 0, 369849, 60, 375560, 0, 378585, 60, 384296, 0, 387321, 60, 393032, 0, 396057, 60, 401768, 0, 404961, 60, 410672, 0, 413697, 60, 419408, 0, 422433, 60, 428144, 0, 431169, 60, 436880, 0, 439905, 60, 445616, 0, 448809, 60, 454520, 0, 457545, 60, 463256, 0, 466281, 60, 471992, 0, 475017, 60, 480728, 0, 483753, 60, 489464, 0, 492489, 60, 498200, 0, 501393, 60, 507104, 0, 510129, 60, 515840, 0, 518865, 60, 524576, 0, 527601, 60, 533312, 0, 536337, 60, 542048, 0, 545241, 60, 550952, 0, 553977, 60, 559688, 0, 562713, 60, 568424, 0, 571449, 60, 577160, 0, 580185, 60, 585896, 0, 588921, 60, 594632, 0], "names": ["MST", "Mountain Standard Time", "MDT", "Mountain Daylight Time"], "std_offset": -420}
europeMoscow = {"id": "Europe/Moscow", "transitions": [98589, 60, 102980, 0, 107349, 60, 111740, 0, 116109, 60, 120500, 0, 124893, 60, 129263, 0, 133631, 60, 137999, 0, 142367, 60, 146735, 0, 151103, 60, 155471, 0, 159839, 60, 164207, 0, 168575, 60, 172943, 0, 177311, 60, 181847, 0, 186215, 60, 190584, 0, 194948, 60, 199315, 0, 203687, 60, 208055, 0, 212423, 60, 216791, 0, 221159, 60, 225527, 0, 230063, 60, 235103, 0, 238799, 60, 243839, 0, 247535, 60, 252575, 0, 256271, 60, 261479, 0, 265007, 60, 270215, 0, 273743, 60, 278951, 0, 282647, 60, 287687, 0, 291383, 60, 296423, 0, 300119, 60, 305327, 0, 308855, 60, 314063, 0, 317591, 60, 322799, 0, 326327, 60, 331535, 0, 335231, 60, 340271, 0, 343967, 60, 349007, 0, 352703, 60, 357911, 0], "names": ["MSK", "Moscow Standard Time", "MSKS", "Moscow Summer Time"], "std_offset": 240}
americaIndianaVevay = {"id": "America/Indiana/Vevay", "transitions": [2767, 60, 7134, 0, 11503, 60, 16038, 0, 20407, 60, 24774, 0, 317767, 60, 322806, 0, 325999, 60, 331710, 0, 334735, 60, 340446, 0, 343471, 60, 349182, 0, 352375, 60, 358086, 0, 361111, 60, 366822, 0, 369847, 60, 375558, 0, 378583, 60, 384294, 0, 387319, 60, 393030, 0, 396055, 60, 401766, 0, 404959, 60, 410670, 0, 413695, 60, 419406, 0, 422431, 60, 428142, 0, 431167, 60, 436878, 0, 439903, 60, 445614, 0, 448807, 60, 454518, 0, 457543, 60, 463254, 0, 466279, 60, 471990, 0, 475015, 60, 480726, 0, 483751, 60, 489462, 0, 492487, 60, 498198, 0, 501391, 60, 507102, 0, 510127, 60, 515838, 0, 518863, 60, 524574, 0, 527599, 60, 533310, 0, 536335, 60, 542046, 0, 545239, 60, 550950, 0, 553975, 60, 559686, 0, 562711, 60, 568422, 0, 571447, 60, 577158, 0, 580183, 60, 585894, 0, 588919, 60, 594630, 0], "names": ["EST", "Eastern Standard Time", "EDT", "Eastern Daylight Time"], "std_offset": -300}
australiaPerth = {"id": "Australia/Perth", "transitions": [42234, 60, 45258, 0, 121194, 60, 124218, 0, 191754, 60, 194274, 0, 323634, 60, 326322, 0, 331530, 60, 335226, 0, 340266, 60, 343962, 0], "names": ["AWST", "Australian Western Standard Time", "AWDT", "Australian Western Daylight Time"], "std_offset": 480}
africaNouakchott = {"id": "Africa/Nouakchott", "transitions": [], "names": ["GMT", "Greenwich Mean Time"], "std_offset": 0}
europeGibraltar = {"id": "Europe/Gibraltar", "transitions": [107257, 60, 111625, 0, 115993, 60, 120361, 0, 124729, 60, 129265, 0, 133633, 60, 138001, 0, 142369, 60, 146737, 0, 151105, 60, 155473, 0, 159841, 60, 164209, 0, 168577, 60, 172945, 0, 177313, 60, 181849, 0, 186217, 60, 190585, 0, 194953, 60, 199321, 0, 203689, 60, 208057, 0, 212425, 60, 216793, 0, 221161, 60, 225529, 0, 230065, 60, 235105, 0, 238801, 60, 243841, 0, 247537, 60, 252577, 0, 256273, 60, 261481, 0, 265009, 60, 270217, 0, 273745, 60, 278953, 0, 282649, 60, 287689, 0, 291385, 60, 296425, 0, 300121, 60, 305329, 0, 308857, 60, 314065, 0, 317593, 60, 322801, 0, 326329, 60, 331537, 0, 335233, 60, 340273, 0, 343969, 60, 349009, 0, 352705, 60, 357913, 0, 361441, 60, 366649, 0, 370177, 60, 375385, 0, 379081, 60, 384121, 0, 387817, 60, 392857, 0, 396553, 60, 401593, 0, 405289, 60, 410497, 0, 414025, 60, 419233, 0, 422761, 60, 427969, 0, 431665, 60, 436705, 0, 440401, 60, 445441, 0, 449137, 60, 454345, 0, 457873, 60, 463081, 0, 466609, 60, 471817, 0, 475513, 60, 480553, 0, 484249, 60, 489289, 0, 492985, 60, 498025, 0, 501721, 60, 506929, 0, 510457, 60, 515665, 0, 519193, 60, 524401, 0, 528097, 60, 533137, 0, 536833, 60, 541873, 0, 545569, 60, 550777, 0, 554305, 60, 559513, 0, 563041, 60, 568249, 0, 571777, 60, 576985, 0, 580681, 60, 585721, 0, 589417, 60, 594457, 0], "names": ["CET", "Central European Standard Time", "CEST", "Central European Summer Time"], "std_offset": 60}
europeMinsk = {"id": "Europe/Minsk", "transitions": [98589, 60, 102980, 0, 107349, 60, 111740, 0, 116109, 60, 120500, 0, 124893, 60, 129263, 0, 133631, 60, 137999, 0, 142367, 60, 146735, 0, 151103, 60, 155471, 0, 159839, 60, 164207, 0, 168575, 60, 172943, 0, 186215, 60, 190584, 0, 194950, 60, 199318, 0, 203688, 60, 208056, 0, 212424, 60, 216792, 0, 221160, 60, 225528, 0, 230064, 60, 235104, 0, 238800, 60, 243840, 0, 247536, 60, 252576, 0, 256272, 60, 261480, 0, 265008, 60, 270216, 0, 273744, 60, 278952, 0, 282648, 60, 287688, 0, 291384, 60, 296424, 0, 300120, 60, 305328, 0, 308856, 60, 314064, 0, 317592, 60, 322800, 0, 326328, 60, 331536, 0, 335232, 60, 340272, 0, 343968, 60, 349008, 0, 352704, 60, 357912, 0], "names": ["EET", "Eastern European Standard Time", "EEST", "Eastern European Summer Time"], "std_offset": 180}
americaPortauPrince = {"id": "America/Port-au-Prince", "transitions": [117005, 60, 121204, 0, 125573, 60, 129940, 0, 134309, 60, 138676, 0, 143045, 60, 147412, 0, 151781, 60, 156148, 0, 160014, 60, 165054, 0, 168750, 60, 173790, 0, 177486, 60, 182526, 0, 186390, 60, 191262, 0, 195126, 60, 199998, 0, 203862, 60, 208902, 0, 212598, 60, 217638, 0, 221334, 60, 226374, 0, 230238, 60, 235110, 0, 238974, 60, 243846, 0, 309029, 60, 314068, 0, 317765, 60, 322804, 0, 369847, 60, 375558, 0, 378583, 60, 384294, 0, 387319, 60, 393030, 0, 396055, 60, 401766, 0, 404959, 60, 410670, 0, 413695, 60, 419406, 0, 422431, 60, 428142, 0, 431167, 60, 436878, 0, 439903, 60, 445614, 0, 448807, 60, 454518, 0, 457543, 60, 463254, 0, 466279, 60, 471990, 0, 475015, 60, 480726, 0, 483751, 60, 489462, 0, 492487, 60, 498198, 0, 501391, 60, 507102, 0, 510127, 60, 515838, 0, 518863, 60, 524574, 0, 527599, 60, 533310, 0, 536335, 60, 542046, 0, 545239, 60, 550950, 0, 553975, 60, 559686, 0, 562711, 60, 568422, 0, 571447, 60, 577158, 0, 580183, 60, 585894, 0, 588919, 60, 594630, 0], "names": ["EST", "Eastern Standard Time", "EDT", "Eastern Daylight Time"], "std_offset": -300}
africaDjibouti = {"id": "Africa/Djibouti", "transitions": [], "names": ["EAT", "East Africa Time"], "std_offset": 180}
asiaSamarkand = {"id": "Asia/Samarkand", "transitions": [98587, 60, 102978, 0, 107346, 60, 111738, 0, 116107, 60, 120498, 0, 124891, 60, 129261, 0, 133629, 60, 137997, 0, 142365, 60, 146733, 0, 151101, 60, 155469, 0, 159837, 60, 164205, 0, 168573, 60, 172941, 0, 177309, 60, 181845, 0, 186213, 60, 190581, 0], "names": ["UZT", "Uzbekistan Standard Time", "UZST", "Uzbekistan Summer Time"], "std_offset": 300}
americaKralendijk = {"id": "America/Kralendijk", "transitions": [], "names": ["AST", "Atlantic Standard Time", "ADT", "Atlantic Daylight Time"], "std_offset": -240}
indianAntananarivo = {"id": "Indian/Antananarivo", "transitions": [], "names": ["EAT", "East Africa Time"], "std_offset": 180}
americaCuracao = {"id": "America/Curacao", "transitions": [], "names": ["AST", "Atlantic Standard Time", "ADT", "Atlantic Daylight Time"], "std_offset": -240}
americaNassau = {"id": "America/Nassau", "transitions": [2767, 60, 7134, 0, 11503, 60, 16038, 0, 20407, 60, 24774, 0, 29143, 60, 33510, 0, 37879, 60, 42246, 0, 46615, 60, 50982, 0, 55351, 60, 59886, 0, 64087, 60, 68622, 0, 72991, 60, 77358, 0, 81727, 60, 86094, 0, 90463, 60, 94830, 0, 99199, 60, 103566, 0, 107935, 60, 112470, 0, 116671, 60, 121206, 0, 125575, 60, 129942, 0, 134311, 60, 138678, 0, 143047, 60, 147414, 0, 151279, 60, 156150, 0, 160015, 60, 165054, 0, 168751, 60, 173790, 0, 177487, 60, 182526, 0, 186391, 60, 191262, 0, 195127, 60, 199998, 0, 203863, 60, 208902, 0, 212599, 60, 217638, 0, 221335, 60, 226374, 0, 230239, 60, 235110, 0, 238975, 60, 243846, 0, 247711, 60, 252582, 0, 256447, 60, 261486, 0, 265183, 60, 270222, 0, 273919, 60, 278958, 0, 282823, 60, 287694, 0, 291559, 60, 296430, 0, 300295, 60, 305334, 0, 309031, 60, 314070, 0, 317767, 60, 322806, 0, 325999, 60, 331710, 0, 334735, 60, 340446, 0, 343471, 60, 349182, 0, 352375, 60, 358086, 0, 361111, 60, 366822, 0, 369847, 60, 375558, 0, 378583, 60, 384294, 0, 387319, 60, 393030, 0, 396055, 60, 401766, 0, 404959, 60, 410670, 0, 413695, 60, 419406, 0, 422431, 60, 428142, 0, 431167, 60, 436878, 0, 439903, 60, 445614, 0, 448807, 60, 454518, 0, 457543, 60, 463254, 0, 466279, 60, 471990, 0, 475015, 60, 480726, 0, 483751, 60, 489462, 0, 492487, 60, 498198, 0, 501391, 60, 507102, 0, 510127, 60, 515838, 0, 518863, 60, 524574, 0, 527599, 60, 533310, 0, 536335, 60, 542046, 0, 545239, 60, 550950, 0, 553975, 60, 559686, 0, 562711, 60, 568422, 0, 571447, 60, 577158, 0, 580183, 60, 585894, 0, 588919, 60, 594630, 0], "names": ["EST", "Eastern Standard Time", "EDT", "Eastern Daylight Time"], "std_offset": -300}
africaLusaka = {"id": "Africa/Lusaka", "transitions": [], "names": ["CAT", "Central Africa Time"], "std_offset": 120}
americaSantaIsabel = {"id": "America/Santa_Isabel", "transitions": [55354, 60, 59889, 0, 64090, 60, 68625, 0, 72994, 60, 77361, 0, 81730, 60, 86097, 0, 90466, 60, 94833, 0, 99202, 60, 103569, 0, 107938, 60, 112473, 0, 116674, 60, 121209, 0, 125578, 60, 129945, 0, 134314, 60, 138681, 0, 143050, 60, 147417, 0, 151282, 60, 156153, 0, 160018, 60, 165057, 0, 168754, 60, 173793, 0, 177490, 60, 182529, 0, 186394, 60, 191265, 0, 195130, 60, 200001, 0, 203866, 60, 208905, 0, 212602, 60, 217641, 0, 221338, 60, 226377, 0, 230242, 60, 235113, 0, 238978, 60, 243849, 0, 247714, 60, 252585, 0, 256450, 60, 261489, 0, 265186, 60, 270225, 0, 273922, 60, 278961, 0, 282826, 60, 287697, 0, 291562, 60, 296433, 0, 300298, 60, 305337, 0, 309034, 60, 314073, 0, 317770, 60, 322809, 0, 326506, 60, 331545, 0, 335410, 60, 340281, 0, 344146, 60, 349017, 0, 352882, 60, 357921, 0, 361618, 60, 366657, 0, 370354, 60, 375393, 0, 379258, 60, 384129, 0, 387994, 60, 392865, 0, 396730, 60, 401601, 0, 405466, 60, 410505, 0, 414202, 60, 419241, 0, 422938, 60, 427977, 0, 431842, 60, 436713, 0, 440578, 60, 445449, 0, 449314, 60, 454353, 0, 458050, 60, 463089, 0, 466786, 60, 471825, 0, 475690, 60, 480561, 0, 484426, 60, 489297, 0, 493162, 60, 498033, 0, 501898, 60, 506937, 0, 510634, 60, 515673, 0, 519370, 60, 524409, 0, 528274, 60, 533145, 0, 537010, 60, 541881, 0, 545746, 60, 550785, 0, 554482, 60, 559521, 0, 563218, 60, 568257, 0, 571954, 60, 576993, 0, 580858, 60, 585729, 0, 589594, 60, 594465, 0], "names": ["PST", "Northwest Mexico Standard Time", "PDT", "Northwest Mexico Daylight Time"], "std_offset": -480}
africaMbabane = {"id": "Africa/Mbabane", "transitions": [], "names": ["SAST", "South Africa Standard Time"], "std_offset": 120}
pacificHonolulu = {"id": "Pacific/Honolulu", "transitions": [], "names": ["HST", "Hawaii-Aleutian Standard Time", "HDT", "Hawaii-Aleutian Daylight Time"], "std_offset": -600}
americaOjinaga = {"id": "America/Ojinaga", "transitions": [230240, 60, 235111, 0, 238976, 60, 243847, 0, 247713, 60, 252584, 0, 256449, 60, 261488, 0, 265185, 60, 270224, 0, 274761, 60, 278288, 0, 282825, 60, 287696, 0, 291561, 60, 296432, 0, 300297, 60, 305336, 0, 309033, 60, 314072, 0, 317769, 60, 322808, 0, 326505, 60, 331544, 0, 335409, 60, 340280, 0, 344145, 60, 349016, 0, 352377, 60, 358088, 0, 361113, 60, 366824, 0, 369849, 60, 375560, 0, 378585, 60, 384296, 0, 387321, 60, 393032, 0, 396057, 60, 401768, 0, 404961, 60, 410672, 0, 413697, 60, 419408, 0, 422433, 60, 428144, 0, 431169, 60, 436880, 0, 439905, 60, 445616, 0, 448809, 60, 454520, 0, 457545, 60, 463256, 0, 466281, 60, 471992, 0, 475017, 60, 480728, 0, 483753, 60, 489464, 0, 492489, 60, 498200, 0, 501393, 60, 507104, 0, 510129, 60, 515840, 0, 518865, 60, 524576, 0, 527601, 60, 533312, 0, 536337, 60, 542048, 0, 545241, 60, 550952, 0, 553977, 60, 559688, 0, 562713, 60, 568424, 0, 571449, 60, 577160, 0, 580185, 60, 585896, 0, 588921, 60, 594632, 0], "names": ["MST", "Mountain Standard Time", "MDT", "Mountain Daylight Time"], "std_offset": -420}
asiaHarbin = {"id": "Asia/Harbin", "transitions": [143200, 60, 146391, 0, 151432, 60, 155127, 0, 160168, 60, 163863, 0, 169072, 60, 172767, 0, 177808, 60, 181503, 0, 186544, 60, 190239, 0], "names": ["CST", "China Standard Time", "CDT", "China Daylight Time"], "std_offset": 480}
asiaKabul = {"id": "Asia/Kabul", "transitions": [], "names": ["AFT", "Afghanistan Time"], "std_offset": 270}
asiaRangoon = {"id": "Asia/Rangoon", "transitions": [], "names": ["MMT", "Myanmar Time"], "std_offset": 390}
australiaHobart = {"id": "Australia/Hobart", "transitions": [7120, 60, 10480, 0, 16024, 60, 18880, 0, 24760, 60, 27784, 0, 33496, 60, 36520, 0, 42232, 60, 45256, 0, 50968, 60, 54160, 0, 59872, 60, 62896, 0, 68608, 60, 71632, 0, 77344, 60, 80368, 0, 86080, 60, 89104, 0, 94816, 60, 97840, 0, 103552, 60, 107248, 0, 112456, 60, 115984, 0, 121192, 60, 124216, 0, 129928, 60, 132952, 0, 138664, 60, 141688, 0, 147232, 60, 150760, 0, 156136, 60, 159664, 0, 165040, 60, 168400, 0, 173776, 60, 177136, 0, 182512, 60, 186208, 0, 190744, 60, 194944, 0, 199480, 60, 203680, 0, 208216, 60, 212416, 0, 216952, 60, 221152, 0, 225688, 60, 230056, 0, 234592, 60, 238792, 0, 243328, 60, 247528, 0, 252064, 60, 256264, 0, 260800, 60, 265000, 0, 268696, 60, 273736, 0, 278440, 60, 282640, 0, 287176, 60, 291376, 0, 295912, 60, 300112, 0, 304648, 60, 308848, 0, 313384, 60, 317752, 0, 322120, 60, 326320, 0, 331024, 60, 335392, 0, 339760, 60, 344128, 0, 348496, 60, 352864, 0, 357232, 60, 361600, 0, 365968, 60, 370336, 0, 374872, 60, 379240, 0, 383608, 60, 387976, 0, 392344, 60, 396712, 0, 401080, 60, 405448, 0, 409816, 60, 414184, 0, 418552, 60, 422920, 0, 427456, 60, 431824, 0, 436192, 60, 440560, 0, 444928, 60, 449296, 0, 453664, 60, 458032, 0, 462400, 60, 466768, 0, 471136, 60, 475672, 0, 480040, 60, 484408, 0, 488776, 60, 493144, 0, 497512, 60, 501880, 0, 506248, 60, 510616, 0, 514984, 60, 519352, 0, 523888, 60, 528256, 0, 532624, 60, 536992, 0, 541360, 60, 545728, 0, 550096, 60, 554464, 0, 558832, 60, 563200, 0, 567568, 60, 571936, 0, 576472, 60, 580840, 0, 585208, 60, 589576, 0, 593944, 60], "names": ["AEST", "Australian Eastern Standard Time", "AEDT", "Australian Eastern Daylight Time"], "std_offset": 600}
asiaUlaanbaatar = {"id": "Asia/Ulaanbaatar", "transitions": [116104, 60, 120495, 0, 124888, 60, 129255, 0, 133624, 60, 137991, 0, 142360, 60, 146727, 0, 151096, 60, 155463, 0, 159832, 60, 164199, 0, 168568, 60, 172935, 0, 177304, 60, 181839, 0, 186208, 60, 190575, 0, 194944, 60, 199311, 0, 203680, 60, 208047, 0, 212416, 60, 216783, 0, 221152, 60, 225519, 0, 230056, 60, 234423, 0, 238792, 60, 243159, 0, 247528, 60, 251895, 0, 274554, 60, 278249, 0, 282618, 60, 286985, 0, 291354, 60, 295721, 0, 300090, 60, 304457, 0, 308826, 60, 313193, 0, 317562, 60, 322097, 0], "names": ["ULAT", "Ulan Bator Standard Time", "ULAST", "Ulan Bator Summer Time"], "std_offset": 480}
africaDakar = {"id": "Africa/Dakar", "transitions": [], "names": ["GMT", "Greenwich Mean Time"], "std_offset": 0}
asiaAden = {"id": "Asia/Aden", "transitions": [], "names": ["AST", "Arabian Standard Time", "ADT", "Arabian Daylight Time"], "std_offset": 180}
asiaChongqing = {"id": "Asia/Chongqing", "transitions": [143200, 60, 146391, 0, 151432, 60, 155127, 0, 160168, 60, 163863, 0, 169072, 60, 172767, 0, 177808, 60, 181503, 0, 186544, 60, 190239, 0], "names": ["CST", "China Standard Time", "CDT", "China Daylight Time"], "std_offset": 480}
europeJersey = {"id": "Europe/Jersey", "transitions": [19394, 60, 24770, 0, 28130, 60, 33506, 0, 36866, 60, 42242, 0, 45602, 60, 50978, 0, 54506, 60, 59714, 0, 63242, 60, 68450, 0, 71978, 60, 77354, 0, 80714, 60, 86090, 0, 89450, 60, 94826, 0, 98521, 60, 103561, 0, 107257, 60, 112297, 0, 115993, 60, 121033, 0, 124729, 60, 129937, 0, 133633, 60, 138673, 0, 142369, 60, 147409, 0, 151105, 60, 156145, 0, 159841, 60, 164881, 0, 168577, 60, 173785, 0, 177313, 60, 182521, 0, 186217, 60, 191257, 0, 194953, 60, 199993, 0, 203689, 60, 208729, 0, 212425, 60, 217465, 0, 221161, 60, 226201, 0, 230065, 60, 235105, 0, 238801, 60, 243841, 0, 247537, 60, 252577, 0, 256273, 60, 261481, 0, 265009, 60, 270217, 0, 273745, 60, 278953, 0, 282649, 60, 287689, 0, 291385, 60, 296425, 0, 300121, 60, 305329, 0, 308857, 60, 314065, 0, 317593, 60, 322801, 0, 326329, 60, 331537, 0, 335233, 60, 340273, 0, 343969, 60, 349009, 0, 352705, 60, 357913, 0, 361441, 60, 366649, 0, 370177, 60, 375385, 0, 379081, 60, 384121, 0, 387817, 60, 392857, 0, 396553, 60, 401593, 0, 405289, 60, 410497, 0, 414025, 60, 419233, 0, 422761, 60, 427969, 0, 431665, 60, 436705, 0, 440401, 60, 445441, 0, 449137, 60, 454345, 0, 457873, 60, 463081, 0, 466609, 60, 471817, 0, 475513, 60, 480553, 0, 484249, 60, 489289, 0, 492985, 60, 498025, 0, 501721, 60, 506929, 0, 510457, 60, 515665, 0, 519193, 60, 524401, 0, 528097, 60, 533137, 0, 536833, 60, 541873, 0, 545569, 60, 550777, 0, 554305, 60, 559513, 0, 563041, 60, 568249, 0, 571777, 60, 576985, 0, 580681, 60, 585721, 0, 589417, 60, 594457, 0], "names": ["GMT", "Greenwich Mean Time"], "std_offset": 0}
africaAccra = {"id": "Africa/Accra", "transitions": [], "names": ["GMT", "Greenwich Mean Time"], "std_offset": 0}
americaNipigon = {"id": "America/Nipigon", "transitions": [37879, 60, 42246, 0, 46615, 60, 50982, 0, 55351, 60, 59886, 0, 64087, 60, 68622, 0, 72991, 60, 77358, 0, 81727, 60, 86094, 0, 90463, 60, 94830, 0, 99199, 60, 103566, 0, 107935, 60, 112470, 0, 116671, 60, 121206, 0, 125575, 60, 129942, 0, 134311, 60, 138678, 0, 143047, 60, 147414, 0, 151279, 60, 156150, 0, 160015, 60, 165054, 0, 168751, 60, 173790, 0, 177487, 60, 182526, 0, 186391, 60, 191262, 0, 195127, 60, 199998, 0, 203863, 60, 208902, 0, 212599, 60, 217638, 0, 221335, 60, 226374, 0, 230239, 60, 235110, 0, 238975, 60, 243846, 0, 247711, 60, 252582, 0, 256447, 60, 261486, 0, 265183, 60, 270222, 0, 273919, 60, 278958, 0, 282823, 60, 287694, 0, 291559, 60, 296430, 0, 300295, 60, 305334, 0, 309031, 60, 314070, 0, 317767, 60, 322806, 0, 325999, 60, 331710, 0, 334735, 60, 340446, 0, 343471, 60, 349182, 0, 352375, 60, 358086, 0, 361111, 60, 366822, 0, 369847, 60, 375558, 0, 378583, 60, 384294, 0, 387319, 60, 393030, 0, 396055, 60, 401766, 0, 404959, 60, 410670, 0, 413695, 60, 419406, 0, 422431, 60, 428142, 0, 431167, 60, 436878, 0, 439903, 60, 445614, 0, 448807, 60, 454518, 0, 457543, 60, 463254, 0, 466279, 60, 471990, 0, 475015, 60, 480726, 0, 483751, 60, 489462, 0, 492487, 60, 498198, 0, 501391, 60, 507102, 0, 510127, 60, 515838, 0, 518863, 60, 524574, 0, 527599, 60, 533310, 0, 536335, 60, 542046, 0, 545239, 60, 550950, 0, 553975, 60, 559686, 0, 562711, 60, 568422, 0, 571447, 60, 577158, 0, 580183, 60, 585894, 0, 588919, 60, 594630, 0], "names": ["EST", "Eastern Standard Time", "EDT", "Eastern Daylight Time"], "std_offset": -300}
americaChihuahua = {"id": "America/Chihuahua", "transitions": [230240, 60, 235111, 0, 238976, 60, 243847, 0, 247713, 60, 252584, 0, 256449, 60, 261488, 0, 265185, 60, 270224, 0, 274761, 60, 278288, 0, 282825, 60, 287696, 0, 291561, 60, 296432, 0, 300297, 60, 305336, 0, 309033, 60, 314072, 0, 317769, 60, 322808, 0, 326505, 60, 331544, 0, 335409, 60, 340280, 0, 344145, 60, 349016, 0, 352881, 60, 357920, 0, 361617, 60, 366656, 0, 370353, 60, 375392, 0, 379257, 60, 384128, 0, 387993, 60, 392864, 0, 396729, 60, 401600, 0, 405465, 60, 410504, 0, 414201, 60, 419240, 0, 422937, 60, 427976, 0, 431841, 60, 436712, 0, 440577, 60, 445448, 0, 449313, 60, 454352, 0, 458049, 60, 463088, 0, 466785, 60, 471824, 0, 475689, 60, 480560, 0, 484425, 60, 489296, 0, 493161, 60, 498032, 0, 501897, 60, 506936, 0, 510633, 60, 515672, 0, 519369, 60, 524408, 0, 528273, 60, 533144, 0, 537009, 60, 541880, 0, 545745, 60, 550784, 0, 554481, 60, 559520, 0, 563217, 60, 568256, 0, 571953, 60, 576992, 0, 580857, 60, 585728, 0, 589593, 60, 594464, 0], "names": ["MST", "Mexican Pacific Standard Time", "MDT", "Mexican Pacific Daylight Time"], "std_offset": -420}
europeBelgrade = {"id": "Europe/Belgrade", "transitions": [115993, 60, 120361, 0, 124729, 60, 129265, 0, 133633, 60, 138001, 0, 142369, 60, 146737, 0, 151105, 60, 155473, 0, 159841, 60, 164209, 0, 168577, 60, 172945, 0, 177313, 60, 181849, 0, 186217, 60, 190585, 0, 194953, 60, 199321, 0, 203689, 60, 208057, 0, 212425, 60, 216793, 0, 221161, 60, 225529, 0, 230065, 60, 235105, 0, 238801, 60, 243841, 0, 247537, 60, 252577, 0, 256273, 60, 261481, 0, 265009, 60, 270217, 0, 273745, 60, 278953, 0, 282649, 60, 287689, 0, 291385, 60, 296425, 0, 300121, 60, 305329, 0, 308857, 60, 314065, 0, 317593, 60, 322801, 0, 326329, 60, 331537, 0, 335233, 60, 340273, 0, 343969, 60, 349009, 0, 352705, 60, 357913, 0, 361441, 60, 366649, 0, 370177, 60, 375385, 0, 379081, 60, 384121, 0, 387817, 60, 392857, 0, 396553, 60, 401593, 0, 405289, 60, 410497, 0, 414025, 60, 419233, 0, 422761, 60, 427969, 0, 431665, 60, 436705, 0, 440401, 60, 445441, 0, 449137, 60, 454345, 0, 457873, 60, 463081, 0, 466609, 60, 471817, 0, 475513, 60, 480553, 0, 484249, 60, 489289, 0, 492985, 60, 498025, 0, 501721, 60, 506929, 0, 510457, 60, 515665, 0, 519193, 60, 524401, 0, 528097, 60, 533137, 0, 536833, 60, 541873, 0, 545569, 60, 550777, 0, 554305, 60, 559513, 0, 563041, 60, 568249, 0, 571777, 60, 576985, 0, 580681, 60, 585721, 0, 589417, 60, 594457, 0], "names": ["CET", "Central European Standard Time", "CEST", "Central European Summer Time"], "std_offset": 60}
asiaPyongyang = {"id": "Asia/Pyongyang", "transitions": [], "names": ["KST", "Korean Standard Time", "KDT", "Korean Daylight Time"], "std_offset": 540}
americaNoronha = {"id": "America/Noronha", "transitions": [138818, 60, 142009, 0, 147386, 60, 150073, 0, 156146, 60, 158665, 0, 164714, 60, 167233, 0, 173450, 60, 176305, 0, 260810, 60, 264337, 0, 269714, 60, 269881, 0, 278618, 60, 281641, 0], "names": ["FNT", "Fernando de Noronha Standard Time", "FNST", "Fernando de Noronha Summer Time"], "std_offset": -120}
africaBamako = {"id": "Africa/Bamako", "transitions": [], "names": ["GMT", "Greenwich Mean Time"], "std_offset": 0}
americaPanama = {"id": "America/Panama", "transitions": [], "names": ["EST", "Eastern Standard Time", "EDT", "Eastern Daylight Time"], "std_offset": -300}
americaStBarthelemy = {"id": "America/St_Barthelemy", "transitions": [], "names": ["AST", "Atlantic Standard Time", "ADT", "Atlantic Daylight Time"], "std_offset": -240}
americaIndianaMarengo = {"id": "America/Indiana/Marengo", "transitions": [2767, 60, 7134, 0, 11503, 60, 16038, 0, 20407, 60, 24774, 0, 29143, 60, 33510, 0, 45103, 60, 50982, 0, 317767, 60, 322806, 0, 325999, 60, 331710, 0, 334735, 60, 340446, 0, 343471, 60, 349182, 0, 352375, 60, 358086, 0, 361111, 60, 366822, 0, 369847, 60, 375558, 0, 378583, 60, 384294, 0, 387319, 60, 393030, 0, 396055, 60, 401766, 0, 404959, 60, 410670, 0, 413695, 60, 419406, 0, 422431, 60, 428142, 0, 431167, 60, 436878, 0, 439903, 60, 445614, 0, 448807, 60, 454518, 0, 457543, 60, 463254, 0, 466279, 60, 471990, 0, 475015, 60, 480726, 0, 483751, 60, 489462, 0, 492487, 60, 498198, 0, 501391, 60, 507102, 0, 510127, 60, 515838, 0, 518863, 60, 524574, 0, 527599, 60, 533310, 0, 536335, 60, 542046, 0, 545239, 60, 550950, 0, 553975, 60, 559686, 0, 562711, 60, 568422, 0, 571447, 60, 577158, 0, 580183, 60, 585894, 0, 588919, 60, 594630, 0], "names": ["EST", "Eastern Standard Time", "EDT", "Eastern Daylight Time"], "std_offset": -300}
africaMogadishu = {"id": "Africa/Mogadishu", "transitions": [], "names": ["EAT", "East Africa Time"], "std_offset": 180}
americaPhoenix = {"id": "America/Phoenix", "transitions": [], "names": ["MST", "Mountain Standard Time", "MDT", "Mountain Daylight Time"], "std_offset": -420}
americaCaracas = {"id": "America/Caracas", "transitions": [], "names": ["VET", "Venezuela Time"], "std_offset": -270}
americaNorthDakotaNewSalem = {"id": "America/North_Dakota/New_Salem", "transitions": [2769, 60, 7136, 0, 11505, 60, 16040, 0, 20409, 60, 24776, 0, 29145, 60, 33512, 0, 35193, 60, 42248, 0, 45105, 60, 50984, 0, 55353, 60, 59888, 0, 64089, 60, 68624, 0, 72993, 60, 77360, 0, 81729, 60, 86096, 0, 90465, 60, 94832, 0, 99201, 60, 103568, 0, 107937, 60, 112472, 0, 116673, 60, 121208, 0, 125577, 60, 129944, 0, 134313, 60, 138680, 0, 143049, 60, 147416, 0, 151281, 60, 156152, 0, 160017, 60, 165056, 0, 168753, 60, 173792, 0, 177489, 60, 182528, 0, 186393, 60, 191264, 0, 195129, 60, 200000, 0, 203865, 60, 208904, 0, 212601, 60, 217640, 0, 221337, 60, 226376, 0, 230241, 60, 235112, 0, 238977, 60, 243848, 0, 247713, 60, 252584, 0, 256449, 60, 261488, 0, 265185, 60, 270224, 0, 273921, 60, 278960, 0, 282825, 60, 287696, 0, 291561, 60, 296432, 0, 300296, 60, 305335, 0, 309032, 60, 314071, 0, 317768, 60, 322807, 0, 326000, 60, 331711, 0, 334736, 60, 340447, 0, 343472, 60, 349183, 0, 352376, 60, 358087, 0, 361112, 60, 366823, 0, 369848, 60, 375559, 0, 378584, 60, 384295, 0, 387320, 60, 393031, 0, 396056, 60, 401767, 0, 404960, 60, 410671, 0, 413696, 60, 419407, 0, 422432, 60, 428143, 0, 431168, 60, 436879, 0, 439904, 60, 445615, 0, 448808, 60, 454519, 0, 457544, 60, 463255, 0, 466280, 60, 471991, 0, 475016, 60, 480727, 0, 483752, 60, 489463, 0, 492488, 60, 498199, 0, 501392, 60, 507103, 0, 510128, 60, 515839, 0, 518864, 60, 524575, 0, 527600, 60, 533311, 0, 536336, 60, 542047, 0, 545240, 60, 550951, 0, 553976, 60, 559687, 0, 562712, 60, 568423, 0, 571448, 60, 577159, 0, 580184, 60, 585895, 0, 588920, 60, 594631, 0], "names": ["CST", "Central Standard Time", "CDT", "Central Daylight Time"], "std_offset": -360}
asiaQatar = {"id": "Asia/Qatar", "transitions": [], "names": ["AST", "Arabian Standard Time", "ADT", "Arabian Daylight Time"], "std_offset": 180}
asiaGaza = {"id": "Asia/Gaza", "transitions": [39550, 60, 41901, 0, 46438, 60, 49629, 0, 133966, 60, 137661, 0, 143542, 60, 146229, 0, 151510, 60, 155133, 0, 160150, 60, 163677, 0, 169414, 60, 172437, 0, 177310, 60, 181005, 0, 186046, 60, 189909, 0, 194950, 60, 198813, 0, 203806, 60, 207549, 0, 212542, 60, 216117, 0, 221278, 60, 225021, 0, 230182, 60, 234214, 0, 238918, 60, 242950, 0, 247654, 60, 251686, 0, 256726, 60, 261093, 0, 265630, 60, 269997, 0, 274366, 60, 278733, 0, 283102, 60, 287469, 0, 291838, 60, 296205, 0, 300574, 60, 304606, 0, 309310, 60, 313439, 0, 317734, 60, 321909, 0, 326494, 60, 330455, 0, 335278, 60, 338877, 0, 343918, 60, 347783, 0, 352678, 60, 355965, 0, 361594, 60, 364485, 0, 370294, 60, 374494, 0], "names": ["EET", "Eastern European Standard Time", "EEST", "Eastern European Summer Time"], "std_offset": 120}
pacificGuadalcanal = {"id": "Pacific/Guadalcanal", "transitions": [], "names": ["SBT", "Solomon Islands Time"], "std_offset": 660}
pacificNoumea = {"id": "Pacific/Noumea", "transitions": [69445, 60, 71484, 0, 78181, 60, 80244, 0, 235935, 60, 238119, 0], "names": ["NCT", "New Caledonia Standard Time", "NCST", "New Caledonia Summer Time"], "std_offset": 660}
indianComoro = {"id": "Indian/Comoro", "transitions": [], "names": ["EAT", "East Africa Time"], "std_offset": 180}
americaBuenosAires = {"id": "America/Buenos_Aires", "transitions": [35595, 60, 37946, 0, 165819, 60, 168074, 0, 173451, 60, 176810, 0, 182355, 60, 185546, 0, 191091, 60, 194282, 0, 199827, 60, 203186, 0, 333051, 60, 334898, 0, 340107, 60, 343634, 0], "names": ["ART", "Argentina Standard Time", "ARST", "Argentina Summer Time"], "std_offset": -180}
pacificEnderbury = {"id": "Pacific/Enderbury", "transitions": [], "names": ["PHOT", "Phoenix Islands Time"], "std_offset": 780}
asiaBaghdad = {"id": "Asia/Baghdad", "transitions": [108069, 60, 111740, 0, 116085, 60, 120500, 0, 124893, 60, 129284, 0, 133653, 60, 137998, 0, 142366, 60, 146734, 0, 151102, 60, 155470, 0, 159838, 60, 164206, 0, 168574, 60, 172942, 0, 177310, 60, 181846, 0, 186240, 60, 190632, 0, 195024, 60, 199416, 0, 203784, 60, 208176, 0, 212544, 60, 216936, 0, 221304, 60, 225696, 0, 230088, 60, 234480, 0, 238848, 60, 243240, 0, 247608, 60, 252000, 0, 256368, 60, 260760, 0, 265152, 60, 269544, 0, 273912, 60, 278304, 0, 282672, 60, 287064, 0, 291432, 60, 295824, 0, 300216, 60, 304608, 0, 308976, 60, 313368, 0, 317736, 60, 322128, 0, 326496, 60, 330888, 0], "names": ["AST", "Arabian Standard Time", "ADT", "Arabian Daylight Time"], "std_offset": 180}
americaArgentinaSanLuis = {"id": "America/Argentina/San_Luis", "transitions": [35595, 60, 37946, 0, 165819, 60, 168074, 0, 173451, 60, 177050, 0, 182212, 60, 185499, 0, 260811, 60, 264459, 0, 333051, 60, 334731, 0, 339940, 60, 343467, 0, 348676, 60], "names": ["WART", "Western Argentina Standard Time", "Western Argentina Summer Time", "Western Argentina Summer Time"], "std_offset": -240}
indianReunion = {"id": "Indian/Reunion", "transitions": [], "names": ["RET", "Reunion Time"], "std_offset": 240}
europeZagreb = {"id": "Europe/Zagreb", "transitions": [115993, 60, 120361, 0, 124729, 60, 129265, 0, 133633, 60, 138001, 0, 142369, 60, 146737, 0, 151105, 60, 155473, 0, 159841, 60, 164209, 0, 168577, 60, 172945, 0, 177313, 60, 181849, 0, 186217, 60, 190585, 0, 194953, 60, 199321, 0, 203689, 60, 208057, 0, 212425, 60, 216793, 0, 221161, 60, 225529, 0, 230065, 60, 235105, 0, 238801, 60, 243841, 0, 247537, 60, 252577, 0, 256273, 60, 261481, 0, 265009, 60, 270217, 0, 273745, 60, 278953, 0, 282649, 60, 287689, 0, 291385, 60, 296425, 0, 300121, 60, 305329, 0, 308857, 60, 314065, 0, 317593, 60, 322801, 0, 326329, 60, 331537, 0, 335233, 60, 340273, 0, 343969, 60, 349009, 0, 352705, 60, 357913, 0, 361441, 60, 366649, 0, 370177, 60, 375385, 0, 379081, 60, 384121, 0, 387817, 60, 392857, 0, 396553, 60, 401593, 0, 405289, 60, 410497, 0, 414025, 60, 419233, 0, 422761, 60, 427969, 0, 431665, 60, 436705, 0, 440401, 60, 445441, 0, 449137, 60, 454345, 0, 457873, 60, 463081, 0, 466609, 60, 471817, 0, 475513, 60, 480553, 0, 484249, 60, 489289, 0, 492985, 60, 498025, 0, 501721, 60, 506929, 0, 510457, 60, 515665, 0, 519193, 60, 524401, 0, 528097, 60, 533137, 0, 536833, 60, 541873, 0, 545569, 60, 550777, 0, 554305, 60, 559513, 0, 563041, 60, 568249, 0, 571777, 60, 576985, 0, 580681, 60, 585721, 0, 589417, 60, 594457, 0], "names": ["CET", "Central European Standard Time", "CEST", "Central European Summer Time"], "std_offset": 60}
australiaBrokenHill = {"id": "Australia/Broken_Hill", "transitions": [16024, 60, 18880, 0, 24760, 60, 27784, 0, 33496, 60, 36520, 0, 42232, 60, 45256, 0, 50968, 60, 54160, 0, 59872, 60, 62896, 0, 68608, 60, 71632, 0, 77344, 60, 80368, 0, 86080, 60, 89104, 0, 94816, 60, 97840, 0, 103552, 60, 107416, 0, 112456, 60, 115480, 0, 121192, 60, 124216, 0, 129928, 60, 132952, 0, 138664, 60, 142024, 0, 147232, 60, 150760, 0, 156136, 60, 159664, 0, 165040, 60, 168400, 0, 173776, 60, 176800, 0, 182512, 60, 185536, 0, 191248, 60, 194272, 0, 199984, 60, 203176, 0, 208888, 60, 211912, 0, 217624, 60, 220648, 0, 226360, 60, 230056, 0, 235096, 60, 238792, 0, 243832, 60, 247528, 0, 252568, 60, 256264, 0, 261472, 60, 265000, 0, 270208, 60, 273736, 0, 278944, 60, 282640, 0, 287680, 60, 291376, 0, 296416, 60, 300112, 0, 305320, 60, 308848, 0, 314056, 60, 317752, 0, 322792, 60, 326320, 0, 331528, 60, 335392, 0, 339760, 60, 344128, 0, 348496, 60, 352864, 0, 357232, 60, 361600, 0, 365968, 60, 370336, 0, 374872, 60, 379240, 0, 383608, 60, 387976, 0, 392344, 60, 396712, 0, 401080, 60, 405448, 0, 409816, 60, 414184, 0, 418552, 60, 422920, 0, 427456, 60, 431824, 0, 436192, 60, 440560, 0, 444928, 60, 449296, 0, 453664, 60, 458032, 0, 462400, 60, 466768, 0, 471136, 60, 475672, 0, 480040, 60, 484408, 0, 488776, 60, 493144, 0, 497512, 60, 501880, 0, 506248, 60, 510616, 0, 514984, 60, 519352, 0, 523888, 60, 528256, 0, 532624, 60, 536992, 0, 541360, 60, 545728, 0, 550096, 60, 554464, 0, 558832, 60, 563200, 0, 567568, 60, 571936, 0, 576472, 60, 580840, 0, 585208, 60, 589576, 0, 593944, 60], "names": ["ACST", "Australian Central Standard Time", "ACDT", "Australian Central Daylight Time"], "std_offset": 570}
europeGuernsey = {"id": "Europe/Guernsey", "transitions": [19394, 60, 24770, 0, 28130, 60, 33506, 0, 36866, 60, 42242, 0, 45602, 60, 50978, 0, 54506, 60, 59714, 0, 63242, 60, 68450, 0, 71978, 60, 77354, 0, 80714, 60, 86090, 0, 89450, 60, 94826, 0, 98521, 60, 103561, 0, 107257, 60, 112297, 0, 115993, 60, 121033, 0, 124729, 60, 129937, 0, 133633, 60, 138673, 0, 142369, 60, 147409, 0, 151105, 60, 156145, 0, 159841, 60, 164881, 0, 168577, 60, 173785, 0, 177313, 60, 182521, 0, 186217, 60, 191257, 0, 194953, 60, 199993, 0, 203689, 60, 208729, 0, 212425, 60, 217465, 0, 221161, 60, 226201, 0, 230065, 60, 235105, 0, 238801, 60, 243841, 0, 247537, 60, 252577, 0, 256273, 60, 261481, 0, 265009, 60, 270217, 0, 273745, 60, 278953, 0, 282649, 60, 287689, 0, 291385, 60, 296425, 0, 300121, 60, 305329, 0, 308857, 60, 314065, 0, 317593, 60, 322801, 0, 326329, 60, 331537, 0, 335233, 60, 340273, 0, 343969, 60, 349009, 0, 352705, 60, 357913, 0, 361441, 60, 366649, 0, 370177, 60, 375385, 0, 379081, 60, 384121, 0, 387817, 60, 392857, 0, 396553, 60, 401593, 0, 405289, 60, 410497, 0, 414025, 60, 419233, 0, 422761, 60, 427969, 0, 431665, 60, 436705, 0, 440401, 60, 445441, 0, 449137, 60, 454345, 0, 457873, 60, 463081, 0, 466609, 60, 471817, 0, 475513, 60, 480553, 0, 484249, 60, 489289, 0, 492985, 60, 498025, 0, 501721, 60, 506929, 0, 510457, 60, 515665, 0, 519193, 60, 524401, 0, 528097, 60, 533137, 0, 536833, 60, 541873, 0, 545569, 60, 550777, 0, 554305, 60, 559513, 0, 563041, 60, 568249, 0, 571777, 60, 576985, 0, 580681, 60, 585721, 0, 589417, 60, 594457, 0], "names": ["GMT", "Greenwich Mean Time"], "std_offset": 0}
americaMetlakatla = {"id": "America/Metlakatla", "transitions": [2770, 60, 7137, 0, 11506, 60, 16041, 0, 20410, 60, 24777, 0, 29146, 60, 33513, 0, 35194, 60, 42249, 0, 45106, 60, 50985, 0, 55354, 60, 59889, 0, 64090, 60, 68625, 0, 72994, 60, 77361, 0, 81730, 60, 86097, 0, 90466, 60, 94833, 0, 99202, 60, 103569, 0, 107938, 60, 112473, 0, 116674, 60, 121209, 0], "names": ["PST", "Pacific Standard Time", "PDT", "Pacific Daylight Time"], "std_offset": -480}
africaAbidjan = {"id": "Africa/Abidjan", "transitions": [], "names": ["GMT", "Greenwich Mean Time"], "std_offset": 0}
europeChisinau = {"id": "Europe/Chisinau", "transitions": [98589, 60, 102980, 0, 107349, 60, 111740, 0, 116109, 60, 120500, 0, 124893, 60, 129263, 0, 133631, 60, 137999, 0, 142367, 60, 146735, 0, 151103, 60, 155471, 0, 159839, 60, 164207, 0, 168575, 60, 172943, 0, 186216, 60, 190584, 0, 194950, 60, 199317, 0, 203686, 60, 208053, 0, 212422, 60, 216789, 0, 221158, 60, 225525, 0, 230062, 60, 235101, 0, 238801, 60, 243841, 0, 247537, 60, 252577, 0, 256273, 60, 261481, 0, 265009, 60, 270217, 0, 273745, 60, 278953, 0, 282649, 60, 287689, 0, 291385, 60, 296425, 0, 300121, 60, 305329, 0, 308857, 60, 314065, 0, 317593, 60, 322801, 0, 326329, 60, 331537, 0, 335233, 60, 340273, 0, 343969, 60, 349009, 0, 352705, 60, 357913, 0, 361441, 60, 366649, 0, 370177, 60, 375385, 0, 379081, 60, 384121, 0, 387817, 60, 392857, 0, 396553, 60, 401593, 0, 405289, 60, 410497, 0, 414025, 60, 419233, 0, 422761, 60, 427969, 0, 431665, 60, 436705, 0, 440401, 60, 445441, 0, 449137, 60, 454345, 0, 457873, 60, 463081, 0, 466609, 60, 471817, 0, 475513, 60, 480553, 0, 484249, 60, 489289, 0, 492985, 60, 498025, 0, 501721, 60, 506929, 0, 510457, 60, 515665, 0, 519193, 60, 524401, 0, 528097, 60, 533137, 0, 536833, 60, 541873, 0, 545569, 60, 550777, 0, 554305, 60, 559513, 0, 563041, 60, 568249, 0, 571777, 60, 576985, 0, 580681, 60, 585721, 0, 589417, 60, 594457, 0], "names": ["EET", "Eastern European Standard Time", "EEST", "Eastern European Summer Time"], "std_offset": 120}
asiaTashkent = {"id": "Asia/Tashkent", "transitions": [98586, 60, 102977, 0, 107346, 60, 111737, 0, 116106, 60, 120497, 0, 124890, 60, 129260, 0, 133628, 60, 137996, 0, 142364, 60, 146732, 0, 151100, 60, 155468, 0, 159836, 60, 164204, 0, 168572, 60, 172940, 0, 177308, 60, 181844, 0, 186212, 60, 190581, 0], "names": ["UZT", "Uzbekistan Standard Time", "UZST", "Uzbekistan Summer Time"], "std_offset": 300}
asiaSaigon = {"id": "Asia/Saigon", "transitions": [], "names": ["ICT", "Indochina Time"], "std_offset": 420}
africaNairobi = {"id": "Africa/Nairobi", "transitions": [], "names": ["EAT", "East Africa Time"], "std_offset": 180}
asiaBeirut = {"id": "Asia/Beirut", "transitions": [21670, 60, 24093, 0, 29182, 60, 32853, 0, 37942, 60, 41613, 0, 46702, 60, 50373, 0, 55486, 60, 59157, 0, 64246, 60, 67917, 0, 72982, 60, 76653, 0, 125614, 60, 129645, 0, 134374, 60, 138405, 0, 143134, 60, 147165, 0, 151894, 60, 155925, 0, 161422, 60, 164709, 0, 169654, 60, 173469, 0, 178198, 60, 182229, 0, 186958, 60, 190989, 0, 195742, 60, 199485, 0, 203686, 60, 208053, 0, 212422, 60, 216789, 0, 221158, 60, 225525, 0, 230062, 60, 234429, 0, 238798, 60, 243165, 0, 247534, 60, 251901, 0, 256270, 60, 261477, 0, 265006, 60, 270213, 0, 273742, 60, 278949, 0, 282646, 60, 287685, 0, 291382, 60, 296421, 0, 300118, 60, 305325, 0, 308854, 60, 314061, 0, 317590, 60, 322797, 0, 326326, 60, 331533, 0, 335230, 60, 340269, 0, 343966, 60, 349005, 0, 352702, 60, 357909, 0, 361438, 60, 366645, 0, 370174, 60, 375381, 0, 379078, 60, 384117, 0, 387814, 60, 392853, 0, 396550, 60, 401589, 0, 405286, 60, 410493, 0, 414022, 60, 419229, 0, 422758, 60, 427965, 0, 431662, 60, 436701, 0, 440398, 60, 445437, 0, 449134, 60, 454341, 0, 457870, 60, 463077, 0, 466606, 60, 471813, 0, 475510, 60, 480549, 0, 484246, 60, 489285, 0, 492982, 60, 498021, 0, 501718, 60, 506925, 0, 510454, 60, 515661, 0, 519190, 60, 524397, 0, 528094, 60, 533133, 0, 536830, 60, 541869, 0, 545566, 60, 550773, 0, 554302, 60, 559509, 0, 563038, 60, 568245, 0, 571774, 60, 576981, 0, 580678, 60, 585717, 0, 589414, 60, 594453, 0], "names": ["EET", "Eastern European Standard Time", "EEST", "Eastern European Summer Time"], "std_offset": 120}
africaMalabo = {"id": "Africa/Malabo", "transitions": [], "names": ["WAT", "West Africa Standard Time", "WAST", "West Africa Summer Time"], "std_offset": 60}
americaParamaribo = {"id": "America/Paramaribo", "transitions": [], "names": ["SRT", "Suriname Time"], "std_offset": -180}
indianMayotte = {"id": "Indian/Mayotte", "transitions": [], "names": ["EAT", "East Africa Time"], "std_offset": 180}
asiaAnadyr = {"id": "Asia/Anadyr", "transitions": [98579, 60, 102970, 0, 107339, 60, 111731, 0, 116100, 60, 120491, 0, 124884, 60, 129254, 0, 133622, 60, 137990, 0, 142358, 60, 146726, 0, 151094, 60, 155462, 0, 159830, 60, 164198, 0, 168566, 60, 172934, 0, 177302, 60, 181838, 0, 186206, 60, 190575, 0, 194939, 60, 199306, 0, 203678, 60, 208046, 0, 212414, 60, 216782, 0, 221150, 60, 225518, 0, 230054, 60, 235094, 0, 238790, 60, 243830, 0, 247526, 60, 252566, 0, 256262, 60, 261470, 0, 264998, 60, 270206, 0, 273734, 60, 278942, 0, 282638, 60, 287678, 0, 291374, 60, 296414, 0, 300110, 60, 305318, 0, 308846, 60, 314054, 0, 317582, 60, 322790, 0, 326318, 60, 331526, 0, 335222, 60, 340262, 0, 343958, 60, 348998, 0, 352694, 60, 357903, 0], "names": ["ANAT", "Magadan Standard Time", "ANAST", "Magadan Summer Time"], "std_offset": 720}
europeKaliningrad = {"id": "Europe/Kaliningrad", "transitions": [98589, 60, 102980, 0, 107349, 60, 111740, 0, 116109, 60, 120500, 0, 124893, 60, 129263, 0, 133631, 60, 137999, 0, 142367, 60, 146735, 0, 151103, 60, 155471, 0, 159839, 60, 164207, 0, 168575, 60, 172943, 0, 177311, 60, 181847, 0, 186215, 60, 190584, 0, 194949, 60, 199316, 0, 203688, 60, 208056, 0, 212424, 60, 216792, 0, 221160, 60, 225528, 0, 230064, 60, 235104, 0, 238800, 60, 243840, 0, 247536, 60, 252576, 0, 256272, 60, 261480, 0, 265008, 60, 270216, 0, 273744, 60, 278952, 0, 282648, 60, 287688, 0, 291384, 60, 296424, 0, 300120, 60, 305328, 0, 308856, 60, 314064, 0, 317592, 60, 322800, 0, 326328, 60, 331536, 0, 335232, 60, 340272, 0, 343968, 60, 349008, 0, 352704, 60, 357912, 0], "names": ["EET", "Eastern European Standard Time", "EEST", "Eastern European Summer Time"], "std_offset": 180}
asiaBangkok = {"id": "Asia/Bangkok", "transitions": [], "names": ["ICT", "Indochina Time"], "std_offset": 420}
asiaKamchatka = {"id": "Asia/Kamchatka", "transitions": [98580, 60, 102971, 0, 107340, 60, 111731, 0, 116100, 60, 120491, 0, 124884, 60, 129254, 0, 133622, 60, 137990, 0, 142358, 60, 146726, 0, 151094, 60, 155462, 0, 159830, 60, 164198, 0, 168566, 60, 172934, 0, 177302, 60, 181838, 0, 186206, 60, 190575, 0, 194939, 60, 199306, 0, 203678, 60, 208046, 0, 212414, 60, 216782, 0, 221150, 60, 225518, 0, 230054, 60, 235094, 0, 238790, 60, 243830, 0, 247526, 60, 252566, 0, 256262, 60, 261470, 0, 264998, 60, 270206, 0, 273734, 60, 278942, 0, 282638, 60, 287678, 0, 291374, 60, 296414, 0, 300110, 60, 305318, 0, 308846, 60, 314054, 0, 317582, 60, 322790, 0, 326318, 60, 331526, 0, 335222, 60, 340262, 0, 343958, 60, 348998, 0, 352694, 60, 357903, 0], "names": ["PETT", "Magadan Standard Time", "PETST", "Magadan Summer Time"], "std_offset": 720}
europeVilnius = {"id": "Europe/Vilnius", "transitions": [98589, 60, 102980, 0, 107349, 60, 111740, 0, 116109, 60, 120500, 0, 124893, 60, 129263, 0, 133631, 60, 137999, 0, 142367, 60, 146735, 0, 151103, 60, 155471, 0, 159839, 60, 164207, 0, 168575, 60, 172943, 0, 177311, 60, 181847, 0, 186215, 60, 190584, 0, 194952, 60, 199320, 0, 203688, 60, 208056, 0, 212424, 60, 216792, 0, 221160, 60, 225528, 0, 230064, 60, 235104, 0, 238800, 60, 243840, 0, 247537, 60, 252577, 0, 256273, 60, 261481, 0, 291385, 60, 296425, 0, 300121, 60, 305329, 0, 308857, 60, 314065, 0, 317593, 60, 322801, 0, 326329, 60, 331537, 0, 335233, 60, 340273, 0, 343969, 60, 349009, 0, 352705, 60, 357913, 0, 361441, 60, 366649, 0, 370177, 60, 375385, 0, 379081, 60, 384121, 0, 387817, 60, 392857, 0, 396553, 60, 401593, 0, 405289, 60, 410497, 0, 414025, 60, 419233, 0, 422761, 60, 427969, 0, 431665, 60, 436705, 0, 440401, 60, 445441, 0, 449137, 60, 454345, 0, 457873, 60, 463081, 0, 466609, 60, 471817, 0, 475513, 60, 480553, 0, 484249, 60, 489289, 0, 492985, 60, 498025, 0, 501721, 60, 506929, 0, 510457, 60, 515665, 0, 519193, 60, 524401, 0, 528097, 60, 533137, 0, 536833, 60, 541873, 0, 545569, 60, 550777, 0, 554305, 60, 559513, 0, 563041, 60, 568249, 0, 571777, 60, 576985, 0, 580681, 60, 585721, 0, 589417, 60, 594457, 0], "names": ["EET", "Eastern European Standard Time", "EEST", "Eastern European Summer Time"], "std_offset": 120}
americaDetroit = {"id": "America/Detroit", "transitions": [29143, 60, 33510, 0, 35191, 60, 42246, 0, 46615, 60, 50982, 0, 55351, 60, 59886, 0, 64087, 60, 68622, 0, 72991, 60, 77358, 0, 81727, 60, 86094, 0, 90463, 60, 94830, 0, 99199, 60, 103566, 0, 107935, 60, 112470, 0, 116671, 60, 121206, 0, 125575, 60, 129942, 0, 134311, 60, 138678, 0, 143047, 60, 147414, 0, 151279, 60, 156150, 0, 160015, 60, 165054, 0, 168751, 60, 173790, 0, 177487, 60, 182526, 0, 186391, 60, 191262, 0, 195127, 60, 199998, 0, 203863, 60, 208902, 0, 212599, 60, 217638, 0, 221335, 60, 226374, 0, 230239, 60, 235110, 0, 238975, 60, 243846, 0, 247711, 60, 252582, 0, 256447, 60, 261486, 0, 265183, 60, 270222, 0, 273919, 60, 278958, 0, 282823, 60, 287694, 0, 291559, 60, 296430, 0, 300295, 60, 305334, 0, 309031, 60, 314070, 0, 317767, 60, 322806, 0, 325999, 60, 331710, 0, 334735, 60, 340446, 0, 343471, 60, 349182, 0, 352375, 60, 358086, 0, 361111, 60, 366822, 0, 369847, 60, 375558, 0, 378583, 60, 384294, 0, 387319, 60, 393030, 0, 396055, 60, 401766, 0, 404959, 60, 410670, 0, 413695, 60, 419406, 0, 422431, 60, 428142, 0, 431167, 60, 436878, 0, 439903, 60, 445614, 0, 448807, 60, 454518, 0, 457543, 60, 463254, 0, 466279, 60, 471990, 0, 475015, 60, 480726, 0, 483751, 60, 489462, 0, 492487, 60, 498198, 0, 501391, 60, 507102, 0, 510127, 60, 515838, 0, 518863, 60, 524574, 0, 527599, 60, 533310, 0, 536335, 60, 542046, 0, 545239, 60, 550950, 0, 553975, 60, 559686, 0, 562711, 60, 568422, 0, 571447, 60, 577158, 0, 580183, 60, 585894, 0, 588919, 60, 594630, 0], "names": ["EST", "Eastern Standard Time", "EDT", "Eastern Daylight Time"], "std_offset": -300}
pacificChatham = {"id": "Pacific/Chatham", "transitions": [42398, 60, 45086, 0, 50966, 60, 54158, 0, 59870, 60, 62894, 0, 68606, 60, 71630, 0, 77342, 60, 80366, 0, 86078, 60, 89102, 0, 94814, 60, 97838, 0, 103550, 60, 106742, 0, 112454, 60, 115478, 0, 121190, 60, 124214, 0, 129926, 60, 132950, 0, 138662, 60, 141686, 0, 147398, 60, 150422, 0, 156134, 60, 159326, 0, 165038, 60, 168062, 0, 173270, 60, 177134, 0, 182006, 60, 185870, 0, 190742, 60, 194606, 0, 199478, 60, 203510, 0, 208214, 60, 212246, 0, 216950, 60, 220982, 0, 225686, 60, 229718, 0, 234590, 60, 238454, 0, 243326, 60, 247190, 0, 252062, 60, 256094, 0, 260798, 60, 264830, 0, 269534, 60, 273566, 0, 278438, 60, 282302, 0, 287174, 60, 291038, 0, 295910, 60, 299942, 0, 304646, 60, 308678, 0, 313382, 60, 317414, 0, 322118, 60, 326150, 0, 330854, 60, 335390, 0, 339590, 60, 344126, 0, 348326, 60, 352862, 0, 357062, 60, 361598, 0, 365798, 60, 370334, 0, 374702, 60, 379238, 0, 383438, 60, 387974, 0, 392174, 60, 396710, 0, 400910, 60, 405446, 0, 409646, 60, 414182, 0, 418382, 60, 422918, 0, 427286, 60, 431822, 0, 436022, 60, 440558, 0, 444758, 60, 449294, 0, 453494, 60, 458030, 0, 462230, 60, 466766, 0, 470966, 60, 475670, 0, 479870, 60, 484406, 0, 488606, 60, 493142, 0, 497342, 60, 501878, 0, 506078, 60, 510614, 0, 514814, 60, 519350, 0, 523718, 60, 528254, 0, 532454, 60, 536990, 0, 541190, 60, 545726, 0, 549926, 60, 554462, 0, 558662, 60, 563198, 0, 567398, 60, 571934, 0, 576302, 60, 580838, 0, 585038, 60, 589574, 0, 593774, 60], "names": ["CHAST", "Chatham Standard Time", "CHADT", "Chatham Daylight Time"], "std_offset": 765}
americaAdak = {"id": "America/Adak", "transitions": [2773, 60, 7140, 0, 11509, 60, 16044, 0, 20413, 60, 24780, 0, 29149, 60, 33516, 0, 35197, 60, 42252, 0, 45109, 60, 50988, 0, 55357, 60, 59892, 0, 64093, 60, 68628, 0, 72997, 60, 77364, 0, 81733, 60, 86100, 0, 90469, 60, 94836, 0, 99205, 60, 103572, 0, 107941, 60, 112476, 0, 116677, 60, 121212, 0, 125580, 60, 129947, 0, 134316, 60, 138683, 0, 143052, 60, 147419, 0, 151284, 60, 156155, 0, 160020, 60, 165059, 0, 168756, 60, 173795, 0, 177492, 60, 182531, 0, 186396, 60, 191267, 0, 195132, 60, 200003, 0, 203868, 60, 208907, 0, 212604, 60, 217643, 0, 221340, 60, 226379, 0, 230244, 60, 235115, 0, 238980, 60, 243851, 0, 247716, 60, 252587, 0, 256452, 60, 261491, 0, 265188, 60, 270227, 0, 273924, 60, 278963, 0, 282828, 60, 287699, 0, 291564, 60, 296435, 0, 300300, 60, 305339, 0, 309036, 60, 314075, 0, 317772, 60, 322811, 0, 326004, 60, 331715, 0, 334740, 60, 340451, 0, 343476, 60, 349187, 0, 352380, 60, 358091, 0, 361116, 60, 366827, 0, 369852, 60, 375563, 0, 378588, 60, 384299, 0, 387324, 60, 393035, 0, 396060, 60, 401771, 0, 404964, 60, 410675, 0, 413700, 60, 419411, 0, 422436, 60, 428147, 0, 431172, 60, 436883, 0, 439908, 60, 445619, 0, 448812, 60, 454523, 0, 457548, 60, 463259, 0, 466284, 60, 471995, 0, 475020, 60, 480731, 0, 483756, 60, 489467, 0, 492492, 60, 498203, 0, 501396, 60, 507107, 0, 510132, 60, 515843, 0, 518868, 60, 524579, 0, 527604, 60, 533315, 0, 536340, 60, 542051, 0, 545244, 60, 550955, 0, 553980, 60, 559691, 0, 562716, 60, 568427, 0, 571452, 60, 577163, 0, 580188, 60, 585899, 0, 588924, 60, 594635, 0], "names": ["HAST", "Hawaii-Aleutian Standard Time", "HADT", "Hawaii-Aleutian Daylight Time"], "std_offset": -600}
africaAlgiers = {"id": "Africa/Algiers", "transitions": [11519, 60, 15215, 0, 64368, 60, 68399, 0, 72096, 60, 76465, 0, 90408, 60, 94945, 0], "names": ["CET", "Central European Standard Time", "CEST", "Central European Summer Time"], "std_offset": 60}
asiaJakarta = {"id": "Asia/Jakarta", "transitions": [], "names": ["WIT", "Western Indonesia Time"], "std_offset": 420}
europeLjubljana = {"id": "Europe/Ljubljana", "transitions": [115993, 60, 120361, 0, 124729, 60, 129265, 0, 133633, 60, 138001, 0, 142369, 60, 146737, 0, 151105, 60, 155473, 0, 159841, 60, 164209, 0, 168577, 60, 172945, 0, 177313, 60, 181849, 0, 186217, 60, 190585, 0, 194953, 60, 199321, 0, 203689, 60, 208057, 0, 212425, 60, 216793, 0, 221161, 60, 225529, 0, 230065, 60, 235105, 0, 238801, 60, 243841, 0, 247537, 60, 252577, 0, 256273, 60, 261481, 0, 265009, 60, 270217, 0, 273745, 60, 278953, 0, 282649, 60, 287689, 0, 291385, 60, 296425, 0, 300121, 60, 305329, 0, 308857, 60, 314065, 0, 317593, 60, 322801, 0, 326329, 60, 331537, 0, 335233, 60, 340273, 0, 343969, 60, 349009, 0, 352705, 60, 357913, 0, 361441, 60, 366649, 0, 370177, 60, 375385, 0, 379081, 60, 384121, 0, 387817, 60, 392857, 0, 396553, 60, 401593, 0, 405289, 60, 410497, 0, 414025, 60, 419233, 0, 422761, 60, 427969, 0, 431665, 60, 436705, 0, 440401, 60, 445441, 0, 449137, 60, 454345, 0, 457873, 60, 463081, 0, 466609, 60, 471817, 0, 475513, 60, 480553, 0, 484249, 60, 489289, 0, 492985, 60, 498025, 0, 501721, 60, 506929, 0, 510457, 60, 515665, 0, 519193, 60, 524401, 0, 528097, 60, 533137, 0, 536833, 60, 541873, 0, 545569, 60, 550777, 0, 554305, 60, 559513, 0, 563041, 60, 568249, 0, 571777, 60, 576985, 0, 580681, 60, 585721, 0, 589417, 60, 594457, 0], "names": ["CET", "Central European Standard Time", "CEST", "Central European Summer Time"], "std_offset": 60}
asiaTaipei = {"id": "Asia/Taipei", "transitions": [37216, 60, 41607, 0, 45976, 60, 50367, 0, 83200, 60, 85407, 0], "names": ["CST", "Taipei Standard Time", "CDT", "Taipei Daylight Time"], "std_offset": 480}
pacificPitcairn = {"id": "Pacific/Pitcairn", "transitions": [], "names": ["PNT", "Pitcairn Time"], "std_offset": -480}
asiaSeoul = {"id": "Asia/Seoul", "transitions": [152103, 60, 155798, 0, 160839, 60, 164534, 0], "names": ["KST", "Korean Standard Time", "KDT", "Korean Daylight Time"], "std_offset": 540}
americaNorthDakotaBeulah = {"id": "America/North_Dakota/Beulah", "transitions": [2769, 60, 7136, 0, 11505, 60, 16040, 0, 20409, 60, 24776, 0, 29145, 60, 33512, 0, 35193, 60, 42248, 0, 45105, 60, 50984, 0, 55353, 60, 59888, 0, 64089, 60, 68624, 0, 72993, 60, 77360, 0, 81729, 60, 86096, 0, 90465, 60, 94832, 0, 99201, 60, 103568, 0, 107937, 60, 112472, 0, 116673, 60, 121208, 0, 125577, 60, 129944, 0, 134313, 60, 138680, 0, 143049, 60, 147416, 0, 151281, 60, 156152, 0, 160017, 60, 165056, 0, 168753, 60, 173792, 0, 177489, 60, 182528, 0, 186393, 60, 191264, 0, 195129, 60, 200000, 0, 203865, 60, 208904, 0, 212601, 60, 217640, 0, 221337, 60, 226376, 0, 230241, 60, 235112, 0, 238977, 60, 243848, 0, 247713, 60, 252584, 0, 256449, 60, 261488, 0, 265185, 60, 270224, 0, 273921, 60, 278960, 0, 282825, 60, 287696, 0, 291561, 60, 296432, 0, 300297, 60, 305336, 0, 309033, 60, 314072, 0, 317769, 60, 322808, 0, 326001, 60, 331712, 0, 334737, 60, 340448, 0, 343473, 60, 349184, 0, 352377, 60, 358088, 0, 361112, 60, 366823, 0, 369848, 60, 375559, 0, 378584, 60, 384295, 0, 387320, 60, 393031, 0, 396056, 60, 401767, 0, 404960, 60, 410671, 0, 413696, 60, 419407, 0, 422432, 60, 428143, 0, 431168, 60, 436879, 0, 439904, 60, 445615, 0, 448808, 60, 454519, 0, 457544, 60, 463255, 0, 466280, 60, 471991, 0, 475016, 60, 480727, 0, 483752, 60, 489463, 0, 492488, 60, 498199, 0, 501392, 60, 507103, 0, 510128, 60, 515839, 0, 518864, 60, 524575, 0, 527600, 60, 533311, 0, 536336, 60, 542047, 0, 545240, 60, 550951, 0, 553976, 60, 559687, 0, 562712, 60, 568423, 0, 571448, 60, 577159, 0, 580184, 60, 585895, 0, 588920, 60, 594631, 0], "names": ["CST", "Central Standard Time", "CDT", "Central Daylight Time"], "std_offset": -360}
asiaSakhalin = {"id": "Asia/Sakhalin", "transitions": [98581, 60, 102972, 0, 107341, 60, 111732, 0, 116101, 60, 120492, 0, 124885, 60, 129255, 0, 133623, 60, 137991, 0, 142359, 60, 146727, 0, 151095, 60, 155463, 0, 159831, 60, 164199, 0, 168567, 60, 172935, 0, 177303, 60, 181839, 0, 186207, 60, 190576, 0, 194940, 60, 199307, 0, 203679, 60, 208047, 0, 212415, 60, 216783, 0, 221151, 60, 225519, 0, 230055, 60, 235095, 0, 238791, 60, 243832, 0, 247528, 60, 252568, 0, 256264, 60, 261472, 0, 265000, 60, 270208, 0, 273736, 60, 278944, 0, 282640, 60, 287680, 0, 291376, 60, 296416, 0, 300112, 60, 305320, 0, 308848, 60, 314056, 0, 317584, 60, 322792, 0, 326320, 60, 331528, 0, 335224, 60, 340264, 0, 343960, 60, 349000, 0, 352696, 60, 357904, 0], "names": ["SAKT", "Sakhalin Standard Time", "SAKST", "Sakhalin Summer Time"], "std_offset": 660}
americaTegucigalpa = {"id": "America/Tegucigalpa", "transitions": [151950, 60, 155477, 0, 160686, 60, 164213, 0, 318606, 60, 320813, 0], "names": ["CST", "Central Standard Time", "CDT", "Central Daylight Time"], "std_offset": -360}
americaIndianaPetersburg = {"id": "America/Indiana/Petersburg", "transitions": [2768, 60, 7135, 0, 11504, 60, 16039, 0, 20408, 60, 24775, 0, 29144, 60, 33511, 0, 35192, 60, 42247, 0, 45104, 60, 50983, 0, 55352, 60, 59887, 0, 64088, 60, 68623, 0, 317767, 60, 322807, 0, 326000, 60, 331711, 0, 334735, 60, 340446, 0, 343471, 60, 349182, 0, 352375, 60, 358086, 0, 361111, 60, 366822, 0, 369847, 60, 375558, 0, 378583, 60, 384294, 0, 387319, 60, 393030, 0, 396055, 60, 401766, 0, 404959, 60, 410670, 0, 413695, 60, 419406, 0, 422431, 60, 428142, 0, 431167, 60, 436878, 0, 439903, 60, 445614, 0, 448807, 60, 454518, 0, 457543, 60, 463254, 0, 466279, 60, 471990, 0, 475015, 60, 480726, 0, 483751, 60, 489462, 0, 492487, 60, 498198, 0, 501391, 60, 507102, 0, 510127, 60, 515838, 0, 518863, 60, 524574, 0, 527599, 60, 533310, 0, 536335, 60, 542046, 0, 545239, 60, 550950, 0, 553975, 60, 559686, 0, 562711, 60, 568422, 0, 571447, 60, 577158, 0, 580183, 60, 585894, 0, 588919, 60, 594630, 0], "names": ["EST", "Eastern Standard Time", "EDT", "Eastern Daylight Time"], "std_offset": -300}
africaMaputo = {"id": "Africa/Maputo", "transitions": [], "names": ["CAT", "Central Africa Time"], "std_offset": 120}
americaMartinique = {"id": "America/Martinique", "transitions": [89956, 60, 94155, 0], "names": ["AST", "Atlantic Standard Time", "ADT", "Atlantic Daylight Time"], "std_offset": -240}
africaMonrovia = {"id": "Africa/Monrovia", "transitions": [], "names": ["GMT", "Greenwich Mean Time"], "std_offset": 0}
pacificKosrae = {"id": "Pacific/Kosrae", "transitions": [], "names": ["KOST", "Kosrae Time"], "std_offset": 660}
americaSantoDomingo = {"id": "America/Santo_Domingo", "transitions": [7133, 30, 9220, 0, 16037, 30, 18004, 0, 24773, 30, 26788, 0, 33509, 30, 35548, 0], "names": ["AST", "Atlantic Standard Time", "ADT", "Atlantic Daylight Time"], "std_offset": -240}
pacificNiue = {"id": "Pacific/Niue", "transitions": [], "names": ["NUT", "Niue Time"], "std_offset": -660}
americaMoncton = {"id": "America/Moncton", "transitions": [2766, 60, 7133, 0, 11502, 60, 16037, 0, 20406, 60, 24773, 0, 37878, 60, 42245, 0, 46614, 60, 50981, 0, 55350, 60, 59885, 0, 64086, 60, 68621, 0, 72990, 60, 77357, 0, 81726, 60, 86093, 0, 90462, 60, 94829, 0, 99198, 60, 103565, 0, 107934, 60, 112469, 0, 116670, 60, 121205, 0, 125574, 60, 129941, 0, 134310, 60, 138677, 0, 143046, 60, 147413, 0, 151278, 60, 156149, 0, 160014, 60, 165053, 0, 168750, 60, 173789, 0, 177486, 60, 182525, 0, 186390, 60, 191261, 0, 195126, 60, 199997, 0, 203860, 60, 208899, 0, 212596, 60, 217635, 0, 221332, 60, 226371, 0, 230236, 60, 235107, 0, 238972, 60, 243843, 0, 247708, 60, 252579, 0, 256444, 60, 261483, 0, 265180, 60, 270219, 0, 273916, 60, 278955, 0, 282820, 60, 287691, 0, 291556, 60, 296427, 0, 300292, 60, 305331, 0, 309028, 60, 314067, 0, 317764, 60, 322803, 0, 325998, 60, 331709, 0, 334734, 60, 340445, 0, 343470, 60, 349181, 0, 352374, 60, 358085, 0, 361110, 60, 366821, 0, 369846, 60, 375557, 0, 378582, 60, 384293, 0, 387318, 60, 393029, 0, 396054, 60, 401765, 0, 404958, 60, 410669, 0, 413694, 60, 419405, 0, 422430, 60, 428141, 0, 431166, 60, 436877, 0, 439902, 60, 445613, 0, 448806, 60, 454517, 0, 457542, 60, 463253, 0, 466278, 60, 471989, 0, 475014, 60, 480725, 0, 483750, 60, 489461, 0, 492486, 60, 498197, 0, 501390, 60, 507101, 0, 510126, 60, 515837, 0, 518862, 60, 524573, 0, 527598, 60, 533309, 0, 536334, 60, 542045, 0, 545238, 60, 550949, 0, 553974, 60, 559685, 0, 562710, 60, 568421, 0, 571446, 60, 577157, 0, 580182, 60, 585893, 0, 588918, 60, 594629, 0], "names": ["AST", "Atlantic Standard Time", "ADT", "Atlantic Daylight Time"], "std_offset": -240}
americaSantiago = {"id": "America/Santiago", "transitions": [6796, 60, 10491, 0, 15532, 60, 19227, 0, 24436, 60, 27963, 0, 32836, 60, 36699, 0, 41908, 60, 45435, 0, 50644, 60, 54339, 0, 59380, 60, 63075, 0, 68116, 60, 71811, 0, 77020, 60, 80547, 0, 85756, 60, 89283, 0, 94492, 60, 98187, 0, 103228, 60, 106923, 0, 111964, 60, 115659, 0, 120700, 60, 124395, 0, 129604, 60, 133131, 0, 138340, 60, 141867, 0, 147076, 60, 151443, 0, 155812, 60, 159507, 0, 164380, 60, 168243, 0, 173452, 60, 177147, 0, 181516, 60, 185715, 0, 190924, 60, 194619, 0, 199660, 60, 203355, 0, 208396, 60, 212091, 0, 217132, 60, 220827, 0, 226036, 60, 229563, 0, 234772, 60, 238803, 0, 243508, 60, 247203, 0, 251908, 60, 256443, 0, 260980, 60, 264675, 0, 269884, 60, 273411, 0, 278620, 60, 282147, 0, 287356, 60, 290883, 0, 296092, 60, 299787, 0, 304828, 60, 308523, 0, 313564, 60, 317259, 0, 322468, 60, 325995, 0, 331204, 60, 335235, 0, 339940, 60, 343635, 0, 348676, 60, 352875, 0, 357412, 60, 362451, 0, 364972, 60, 371019, 0, 374044, 60, 379755, 0, 382948, 60, 388491, 0, 391684, 60, 397227, 0, 400420, 60, 405963, 0, 409156, 60, 414699, 0, 417892, 60, 423603, 0, 426628, 60, 432339, 0, 435532, 60, 441075, 0, 444268, 60, 449811, 0, 453004, 60, 458547, 0, 461740, 60, 467283, 0, 470476, 60, 476187, 0, 479380, 60, 484923, 0, 488116, 60, 493659, 0, 496852, 60, 502395, 0, 505588, 60, 511131, 0, 514324, 60, 520035, 0, 523060, 60, 528771, 0, 531964, 60, 537507, 0, 540700, 60, 546243, 0, 549436, 60, 554979, 0, 558172, 60, 563715, 0, 566908, 60, 572619, 0, 575644, 60, 581355, 0, 584548, 60, 590091, 0, 593284, 60], "names": ["CLT", "Chile Standard Time", "CLST", "Chile Summer Time"], "std_offset": -240}
americaAraguaina = {"id": "America/Araguaina", "transitions": [138819, 60, 142010, 0, 147387, 60, 150074, 0, 156147, 60, 158666, 0, 164715, 60, 167234, 0, 173451, 60, 176306, 0, 226035, 60, 228890, 0, 234603, 60, 237794, 0, 243363, 60, 246866, 0, 252243, 60, 255434, 0, 260811, 60, 264338, 0, 269715, 60, 272906, 0, 278619, 60, 281642, 0, 287859, 60, 290378, 0, 375219, 60, 378074, 0, 383955, 60, 386810, 0, 392691, 60, 395714, 0, 401427, 60, 404450, 0, 410163, 60, 413186, 0, 418899, 60, 421922, 0, 427803, 60, 430658, 0, 436539, 60, 439394, 0, 445275, 60, 448298, 0, 454011, 60, 457034, 0, 462747, 60, 465938, 0, 471483, 60, 474506, 0, 480387, 60, 483242, 0, 489123, 60, 492146, 0, 497859, 60, 500882, 0, 506595, 60, 509618, 0, 515331, 60, 518354, 0, 524235, 60, 527090, 0, 532971, 60, 535826, 0, 541707, 60, 544562, 0, 550443, 60, 553466, 0, 559179, 60, 562370, 0, 567915, 60, 570938, 0, 576819, 60, 579674, 0, 585555, 60, 588578, 0, 594291, 60], "names": ["BRT", "Brasilia Standard Time", "BRST", "Brasilia Summer Time"], "std_offset": -180}
asiaDushanbe = {"id": "Asia/Dushanbe", "transitions": [98586, 60, 102977, 0, 107346, 60, 111737, 0, 116106, 60, 120497, 0, 124890, 60, 129260, 0, 133628, 60, 137996, 0, 142364, 60, 146732, 0, 151100, 60, 155468, 0, 159836, 60, 164204, 0, 168572, 60, 172940, 0, 177308, 60, 181844, 0, 186212, 60, 190101, 0], "names": ["TJT", "Tajikistan Time"], "std_offset": 300}
americaIndianapolis = {"id": "America/Indianapolis", "transitions": [2767, 60, 7134, 0, 317767, 60, 322806, 0, 325999, 60, 331710, 0, 334735, 60, 340446, 0, 343471, 60, 349182, 0, 352375, 60, 358086, 0, 361111, 60, 366822, 0, 369847, 60, 375558, 0, 378583, 60, 384294, 0, 387319, 60, 393030, 0, 396055, 60, 401766, 0, 404959, 60, 410670, 0, 413695, 60, 419406, 0, 422431, 60, 428142, 0, 431167, 60, 436878, 0, 439903, 60, 445614, 0, 448807, 60, 454518, 0, 457543, 60, 463254, 0, 466279, 60, 471990, 0, 475015, 60, 480726, 0, 483751, 60, 489462, 0, 492487, 60, 498198, 0, 501391, 60, 507102, 0, 510127, 60, 515838, 0, 518863, 60, 524574, 0, 527599, 60, 533310, 0, 536335, 60, 542046, 0, 545239, 60, 550950, 0, 553975, 60, 559686, 0, 562711, 60, 568422, 0, 571447, 60, 577158, 0, 580183, 60, 585894, 0, 588919, 60, 594630, 0], "names": ["EST", "Eastern Standard Time", "EDT", "Eastern Daylight Time"], "std_offset": -300}
americaToronto = {"id": "America/Toronto", "transitions": [2767, 60, 7134, 0, 11503, 60, 16038, 0, 20407, 60, 24774, 0, 29143, 60, 33510, 0, 37879, 60, 42246, 0, 46615, 60, 50982, 0, 55351, 60, 59886, 0, 64087, 60, 68622, 0, 72991, 60, 77358, 0, 81727, 60, 86094, 0, 90463, 60, 94830, 0, 99199, 60, 103566, 0, 107935, 60, 112470, 0, 116671, 60, 121206, 0, 125575, 60, 129942, 0, 134311, 60, 138678, 0, 143047, 60, 147414, 0, 151279, 60, 156150, 0, 160015, 60, 165054, 0, 168751, 60, 173790, 0, 177487, 60, 182526, 0, 186391, 60, 191262, 0, 195127, 60, 199998, 0, 203863, 60, 208902, 0, 212599, 60, 217638, 0, 221335, 60, 226374, 0, 230239, 60, 235110, 0, 238975, 60, 243846, 0, 247711, 60, 252582, 0, 256447, 60, 261486, 0, 265183, 60, 270222, 0, 273919, 60, 278958, 0, 282823, 60, 287694, 0, 291559, 60, 296430, 0, 300295, 60, 305334, 0, 309031, 60, 314070, 0, 317767, 60, 322806, 0, 325999, 60, 331710, 0, 334735, 60, 340446, 0, 343471, 60, 349182, 0, 352375, 60, 358086, 0, 361111, 60, 366822, 0, 369847, 60, 375558, 0, 378583, 60, 384294, 0, 387319, 60, 393030, 0, 396055, 60, 401766, 0, 404959, 60, 410670, 0, 413695, 60, 419406, 0, 422431, 60, 428142, 0, 431167, 60, 436878, 0, 439903, 60, 445614, 0, 448807, 60, 454518, 0, 457543, 60, 463254, 0, 466279, 60, 471990, 0, 475015, 60, 480726, 0, 483751, 60, 489462, 0, 492487, 60, 498198, 0, 501391, 60, 507102, 0, 510127, 60, 515838, 0, 518863, 60, 524574, 0, 527599, 60, 533310, 0, 536335, 60, 542046, 0, 545239, 60, 550950, 0, 553975, 60, 559686, 0, 562711, 60, 568422, 0, 571447, 60, 577158, 0, 580183, 60, 585894, 0, 588919, 60, 594630, 0], "names": ["EST", "Eastern Standard Time", "EDT", "Eastern Daylight Time"], "std_offset": -300}
americaCordoba = {"id": "America/Cordoba", "transitions": [35595, 60, 37946, 0, 165819, 60, 168074, 0, 173451, 60, 176810, 0, 182355, 60, 185546, 0, 199827, 60, 203186, 0, 333051, 60, 334898, 0, 340107, 60, 343634, 0], "names": ["ART", "Argentina Standard Time", "ARST", "Argentina Summer Time"], "std_offset": -180}
europePodgorica = {"id": "Europe/Podgorica", "transitions": [115993, 60, 120361, 0, 124729, 60, 129265, 0, 133633, 60, 138001, 0, 142369, 60, 146737, 0, 151105, 60, 155473, 0, 159841, 60, 164209, 0, 168577, 60, 172945, 0, 177313, 60, 181849, 0, 186217, 60, 190585, 0, 194953, 60, 199321, 0, 203689, 60, 208057, 0, 212425, 60, 216793, 0, 221161, 60, 225529, 0, 230065, 60, 235105, 0, 238801, 60, 243841, 0, 247537, 60, 252577, 0, 256273, 60, 261481, 0, 265009, 60, 270217, 0, 273745, 60, 278953, 0, 282649, 60, 287689, 0, 291385, 60, 296425, 0, 300121, 60, 305329, 0, 308857, 60, 314065, 0, 317593, 60, 322801, 0, 326329, 60, 331537, 0, 335233, 60, 340273, 0, 343969, 60, 349009, 0, 352705, 60, 357913, 0, 361441, 60, 366649, 0, 370177, 60, 375385, 0, 379081, 60, 384121, 0, 387817, 60, 392857, 0, 396553, 60, 401593, 0, 405289, 60, 410497, 0, 414025, 60, 419233, 0, 422761, 60, 427969, 0, 431665, 60, 436705, 0, 440401, 60, 445441, 0, 449137, 60, 454345, 0, 457873, 60, 463081, 0, 466609, 60, 471817, 0, 475513, 60, 480553, 0, 484249, 60, 489289, 0, 492985, 60, 498025, 0, 501721, 60, 506929, 0, 510457, 60, 515665, 0, 519193, 60, 524401, 0, 528097, 60, 533137, 0, 536833, 60, 541873, 0, 545569, 60, 550777, 0, 554305, 60, 559513, 0, 563041, 60, 568249, 0, 571777, 60, 576985, 0, 580681, 60, 585721, 0, 589417, 60, 594457, 0], "names": ["CET", "Central European Standard Time", "CEST", "Central European Summer Time"], "std_offset": 60}
asiaPontianak = {"id": "Asia/Pontianak", "transitions": [], "names": ["WIT", "Western Indonesia Time"], "std_offset": 420}
americaGlaceBay = {"id": "America/Glace_Bay", "transitions": [20406, 60, 24773, 0, 29142, 60, 33509, 0, 37878, 60, 42245, 0, 46614, 60, 50981, 0, 55350, 60, 59885, 0, 64086, 60, 68621, 0, 72990, 60, 77357, 0, 81726, 60, 86093, 0, 90462, 60, 94829, 0, 99198, 60, 103565, 0, 107934, 60, 112469, 0, 116670, 60, 121205, 0, 125574, 60, 129941, 0, 134310, 60, 138677, 0, 143046, 60, 147413, 0, 151278, 60, 156149, 0, 160014, 60, 165053, 0, 168750, 60, 173789, 0, 177486, 60, 182525, 0, 186390, 60, 191261, 0, 195126, 60, 199997, 0, 203862, 60, 208901, 0, 212598, 60, 217637, 0, 221334, 60, 226373, 0, 230238, 60, 235109, 0, 238974, 60, 243845, 0, 247710, 60, 252581, 0, 256446, 60, 261485, 0, 265182, 60, 270221, 0, 273918, 60, 278957, 0, 282822, 60, 287693, 0, 291558, 60, 296429, 0, 300294, 60, 305333, 0, 309030, 60, 314069, 0, 317766, 60, 322805, 0, 325998, 60, 331709, 0, 334734, 60, 340445, 0, 343470, 60, 349181, 0, 352374, 60, 358085, 0, 361110, 60, 366821, 0, 369846, 60, 375557, 0, 378582, 60, 384293, 0, 387318, 60, 393029, 0, 396054, 60, 401765, 0, 404958, 60, 410669, 0, 413694, 60, 419405, 0, 422430, 60, 428141, 0, 431166, 60, 436877, 0, 439902, 60, 445613, 0, 448806, 60, 454517, 0, 457542, 60, 463253, 0, 466278, 60, 471989, 0, 475014, 60, 480725, 0, 483750, 60, 489461, 0, 492486, 60, 498197, 0, 501390, 60, 507101, 0, 510126, 60, 515837, 0, 518862, 60, 524573, 0, 527598, 60, 533309, 0, 536334, 60, 542045, 0, 545238, 60, 550949, 0, 553974, 60, 559685, 0, 562710, 60, 568421, 0, 571446, 60, 577157, 0, 580182, 60, 585893, 0, 588918, 60, 594629, 0], "names": ["AST", "Atlantic Standard Time", "ADT", "Atlantic Daylight Time"], "std_offset": -240}
australiaEucla = {"id": "Australia/Eucla", "transitions": [42233, 60, 45257, 0, 121193, 60, 124217, 0, 191753, 60, 194273, 0, 323633, 60, 326321, 0, 331529, 60, 335225, 0, 340265, 60, 343961, 0], "names": ["ACWST", "Australian Central Western Standard Time", "ACWDT", "Australian Central Western Daylight Time"], "std_offset": 525}
europeHelsinki = {"id": "Europe/Helsinki", "transitions": [98520, 60, 102888, 0, 107256, 60, 111624, 0, 115993, 60, 120361, 0, 124729, 60, 129265, 0, 133633, 60, 138001, 0, 142369, 60, 146737, 0, 151105, 60, 155473, 0, 159841, 60, 164209, 0, 168577, 60, 172945, 0, 177313, 60, 181849, 0, 186217, 60, 190585, 0, 194953, 60, 199321, 0, 203689, 60, 208057, 0, 212425, 60, 216793, 0, 221161, 60, 225529, 0, 230065, 60, 235105, 0, 238801, 60, 243841, 0, 247537, 60, 252577, 0, 256273, 60, 261481, 0, 265009, 60, 270217, 0, 273745, 60, 278953, 0, 282649, 60, 287689, 0, 291385, 60, 296425, 0, 300121, 60, 305329, 0, 308857, 60, 314065, 0, 317593, 60, 322801, 0, 326329, 60, 331537, 0, 335233, 60, 340273, 0, 343969, 60, 349009, 0, 352705, 60, 357913, 0, 361441, 60, 366649, 0, 370177, 60, 375385, 0, 379081, 60, 384121, 0, 387817, 60, 392857, 0, 396553, 60, 401593, 0, 405289, 60, 410497, 0, 414025, 60, 419233, 0, 422761, 60, 427969, 0, 431665, 60, 436705, 0, 440401, 60, 445441, 0, 449137, 60, 454345, 0, 457873, 60, 463081, 0, 466609, 60, 471817, 0, 475513, 60, 480553, 0, 484249, 60, 489289, 0, 492985, 60, 498025, 0, 501721, 60, 506929, 0, 510457, 60, 515665, 0, 519193, 60, 524401, 0, 528097, 60, 533137, 0, 536833, 60, 541873, 0, 545569, 60, 550777, 0, 554305, 60, 559513, 0, 563041, 60, 568249, 0, 571777, 60, 576985, 0, 580681, 60, 585721, 0, 589417, 60, 594457, 0], "names": ["EET", "Eastern European Standard Time", "EEST", "Eastern European Summer Time"], "std_offset": 120}
americaIqaluit = {"id": "America/Iqaluit", "transitions": [90463, 60, 94830, 0, 99199, 60, 103566, 0, 107935, 60, 112470, 0, 116671, 60, 121206, 0, 125575, 60, 129942, 0, 134311, 60, 138678, 0, 143047, 60, 147414, 0, 151279, 60, 156150, 0, 160015, 60, 165054, 0, 168751, 60, 173790, 0, 177487, 60, 182526, 0, 186391, 60, 191262, 0, 195127, 60, 199998, 0, 203863, 60, 208902, 0, 212599, 60, 217638, 0, 221335, 60, 226374, 0, 230239, 60, 235110, 0, 238975, 60, 243846, 0, 247711, 60, 252582, 0, 256447, 60, 261486, 0, 265184, 60, 270223, 0, 273919, 60, 278958, 0, 282823, 60, 287694, 0, 291559, 60, 296430, 0, 300295, 60, 305334, 0, 309031, 60, 314070, 0, 317767, 60, 322806, 0, 325999, 60, 331710, 0, 334735, 60, 340446, 0, 343471, 60, 349182, 0, 352375, 60, 358086, 0, 361111, 60, 366822, 0, 369847, 60, 375558, 0, 378583, 60, 384294, 0, 387319, 60, 393030, 0, 396055, 60, 401766, 0, 404959, 60, 410670, 0, 413695, 60, 419406, 0, 422431, 60, 428142, 0, 431167, 60, 436878, 0, 439903, 60, 445614, 0, 448807, 60, 454518, 0, 457543, 60, 463254, 0, 466279, 60, 471990, 0, 475015, 60, 480726, 0, 483751, 60, 489462, 0, 492487, 60, 498198, 0, 501391, 60, 507102, 0, 510127, 60, 515838, 0, 518863, 60, 524574, 0, 527599, 60, 533310, 0, 536335, 60, 542046, 0, 545239, 60, 550950, 0, 553975, 60, 559686, 0, 562711, 60, 568422, 0, 571447, 60, 577158, 0, 580183, 60, 585894, 0, 588919, 60, 594630, 0], "names": ["EST", "Eastern Standard Time", "EDT", "Eastern Daylight Time"], "std_offset": -300}
americaRegina = {"id": "America/Regina", "transitions": [], "names": ["CST", "Central Standard Time", "CDT", "Central Daylight Time"], "std_offset": -360}
pacificJohnston = {"id": "Pacific/Johnston", "transitions": [], "names": ["HAST", "Hawaii-Aleutian Standard Time", "HADT", "Hawaii-Aleutian Daylight Time"], "std_offset": -600}
antarcticaMawson = {"id": "Antarctica/Mawson", "transitions": [], "names": ["MAWT", "Mawson Time"], "std_offset": 300}
europeRiga = {"id": "Europe/Riga", "transitions": [98589, 60, 102980, 0, 107349, 60, 111740, 0, 116109, 60, 120500, 0, 124893, 60, 129263, 0, 133631, 60, 137999, 0, 142367, 60, 146735, 0, 151103, 60, 155471, 0, 159839, 60, 164207, 0, 168575, 60, 172944, 0, 177312, 60, 181848, 0, 186216, 60, 190584, 0, 194952, 60, 199320, 0, 203688, 60, 208056, 0, 212424, 60, 216792, 0, 221160, 60, 225528, 0, 230064, 60, 234432, 0, 238801, 60, 243841, 0, 247537, 60, 252577, 0, 256273, 60, 261481, 0, 273745, 60, 278953, 0, 282649, 60, 287689, 0, 291385, 60, 296425, 0, 300121, 60, 305329, 0, 308857, 60, 314065, 0, 317593, 60, 322801, 0, 326329, 60, 331537, 0, 335233, 60, 340273, 0, 343969, 60, 349009, 0, 352705, 60, 357913, 0, 361441, 60, 366649, 0, 370177, 60, 375385, 0, 379081, 60, 384121, 0, 387817, 60, 392857, 0, 396553, 60, 401593, 0, 405289, 60, 410497, 0, 414025, 60, 419233, 0, 422761, 60, 427969, 0, 431665, 60, 436705, 0, 440401, 60, 445441, 0, 449137, 60, 454345, 0, 457873, 60, 463081, 0, 466609, 60, 471817, 0, 475513, 60, 480553, 0, 484249, 60, 489289, 0, 492985, 60, 498025, 0, 501721, 60, 506929, 0, 510457, 60, 515665, 0, 519193, 60, 524401, 0, 528097, 60, 533137, 0, 536833, 60, 541873, 0, 545569, 60, 550777, 0, 554305, 60, 559513, 0, 563041, 60, 568249, 0, 571777, 60, 576985, 0, 580681, 60, 585721, 0, 589417, 60, 594457, 0], "names": ["EET", "Eastern European Standard Time", "EEST", "Eastern European Summer Time"], "std_offset": 120}
americaCampoGrande = {"id": "America/Campo_Grande", "transitions": [138820, 60, 142011, 0, 147388, 60, 150075, 0, 156148, 60, 158667, 0, 164716, 60, 167235, 0, 173452, 60, 176307, 0, 182356, 60, 185211, 0, 191092, 60, 193779, 0, 199996, 60, 202347, 0, 208564, 60, 211587, 0, 217300, 60, 220323, 0, 226036, 60, 228891, 0, 234604, 60, 237795, 0, 243364, 60, 246867, 0, 252244, 60, 255435, 0, 260812, 60, 264339, 0, 269716, 60, 272907, 0, 278620, 60, 281643, 0, 287860, 60, 290379, 0, 296260, 60, 299115, 0, 305380, 60, 308019, 0, 313732, 60, 316755, 0, 322972, 60, 325659, 0, 331204, 60, 334227, 0, 340108, 60, 342963, 0, 348844, 60, 351867, 0, 357580, 60, 360603, 0, 366316, 60, 369507, 0, 375220, 60, 378075, 0, 383956, 60, 386811, 0, 392692, 60, 395715, 0, 401428, 60, 404451, 0, 410164, 60, 413187, 0, 418900, 60, 421923, 0, 427804, 60, 430659, 0, 436540, 60, 439395, 0, 445276, 60, 448299, 0, 454012, 60, 457035, 0, 462748, 60, 465939, 0, 471484, 60, 474507, 0, 480388, 60, 483243, 0, 489124, 60, 492147, 0, 497860, 60, 500883, 0, 506596, 60, 509619, 0, 515332, 60, 518355, 0, 524236, 60, 527091, 0, 532972, 60, 535827, 0, 541708, 60, 544563, 0, 550444, 60, 553467, 0, 559180, 60, 562371, 0, 567916, 60, 570939, 0, 576820, 60, 579675, 0, 585556, 60, 588579, 0, 594292, 60], "names": ["AMT", "Amazon Standard Time", "AMST", "Amazon Summer Time"], "std_offset": -240}
pacificWallis = {"id": "Pacific/Wallis", "transitions": [], "names": ["WFT", "Wallis and Futuna Time"], "std_offset": 720}
pacificTahiti = {"id": "Pacific/Tahiti", "transitions": [], "names": ["TAHT", "Tahiti Time"], "std_offset": -600}
americaJamaica = {"id": "America/Jamaica", "transitions": [37879, 60, 42246, 0, 45103, 60, 50982, 0, 55351, 60, 59886, 0, 64087, 60, 68622, 0, 72991, 60, 77358, 0, 81727, 60, 86094, 0, 90463, 60, 94830, 0, 99199, 60, 103566, 0, 107935, 60, 112470, 0, 116671, 60, 121206, 0], "names": ["EST", "Eastern Standard Time", "EDT", "Eastern Daylight Time"], "std_offset": -300}
americaBlancSablon = {"id": "America/Blanc-Sablon", "transitions": [], "names": ["AST", "Atlantic Standard Time", "ADT", "Atlantic Daylight Time"], "std_offset": -240}
africaWindhoek = {"id": "Africa/Windhoek", "transitions": [216289, 60, 221328, 0, 225025, 60, 230232, 0, 233761, 60, 238968, 0, 242665, 60, 247704, 0, 251401, 60, 256440, 0, 260137, 60, 265176, 0, 268873, 60, 273912, 0, 277609, 60, 282816, 0, 286345, 60, 291552, 0, 295249, 60, 300288, 0, 303985, 60, 309024, 0, 312721, 60, 317760, 0, 321457, 60, 326496, 0, 330193, 60, 335400, 0, 339097, 60, 344136, 0, 347833, 60, 352872, 0, 356569, 60, 361608, 0, 365305, 60, 370344, 0, 374041, 60, 379248, 0, 382777, 60, 387984, 0, 391681, 60, 396720, 0, 400417, 60, 405456, 0, 409153, 60, 414192, 0, 417889, 60, 422928, 0, 426625, 60, 431832, 0, 435361, 60, 440568, 0, 444265, 60, 449304, 0, 453001, 60, 458040, 0, 461737, 60, 466776, 0, 470473, 60, 475680, 0, 479209, 60, 484416, 0, 488113, 60, 493152, 0, 496849, 60, 501888, 0, 505585, 60, 510624, 0, 514321, 60, 519360, 0, 523057, 60, 528264, 0, 531793, 60, 537000, 0, 540697, 60, 545736, 0, 549433, 60, 554472, 0, 558169, 60, 563208, 0, 566905, 60, 571944, 0, 575641, 60, 580848, 0, 584545, 60, 589584, 0, 593281, 60], "names": ["WAT", "West Africa Standard Time", "WAST", "West Africa Summer Time"], "std_offset": 60}
asiaManila = {"id": "Asia/Manila", "transitions": [72040, 60, 76431, 0], "names": ["PHT", "Philippine Standard Time", "PHST", "Philippine Summer Time"], "std_offset": 480}
asiaMakassar = {"id": "Asia/Makassar", "transitions": [], "names": ["CIT", "Central Indonesia Time"], "std_offset": 480}
asiaNicosia = {"id": "Asia/Nicosia", "transitions": [46270, 60, 50637, 0, 55822, 60, 59397, 0, 63574, 60, 67773, 0, 72310, 60, 76701, 0, 81046, 60, 85413, 0, 89950, 60, 94149, 0, 98518, 60, 102885, 0, 107254, 60, 111621, 0, 115990, 60, 120357, 0, 124726, 60, 129261, 0, 133630, 60, 137997, 0, 142366, 60, 146733, 0, 151102, 60, 155469, 0, 159838, 60, 164205, 0, 168574, 60, 172941, 0, 177310, 60, 181845, 0, 186214, 60, 190581, 0, 194950, 60, 199317, 0, 203686, 60, 208053, 0, 212422, 60, 216789, 0, 221158, 60, 225525, 0, 230062, 60, 234429, 0, 238798, 60, 243165, 0, 247534, 60, 252577, 0, 256273, 60, 261481, 0, 265009, 60, 270217, 0, 273745, 60, 278953, 0, 282649, 60, 287689, 0, 291385, 60, 296425, 0, 300121, 60, 305329, 0, 308857, 60, 314065, 0, 317593, 60, 322801, 0, 326329, 60, 331537, 0, 335233, 60, 340273, 0, 343969, 60, 349009, 0, 352705, 60, 357913, 0, 361441, 60, 366649, 0, 370177, 60, 375385, 0, 379081, 60, 384121, 0, 387817, 60, 392857, 0, 396553, 60, 401593, 0, 405289, 60, 410497, 0, 414025, 60, 419233, 0, 422761, 60, 427969, 0, 431665, 60, 436705, 0, 440401, 60, 445441, 0, 449137, 60, 454345, 0, 457873, 60, 463081, 0, 466609, 60, 471817, 0, 475513, 60, 480553, 0, 484249, 60, 489289, 0, 492985, 60, 498025, 0, 501721, 60, 506929, 0, 510457, 60, 515665, 0, 519193, 60, 524401, 0, 528097, 60, 533137, 0, 536833, 60, 541873, 0, 545569, 60, 550777, 0, 554305, 60, 559513, 0, 563041, 60, 568249, 0, 571777, 60, 576985, 0, 580681, 60, 585721, 0, 589417, 60, 594457, 0], "names": ["EET", "Eastern European Standard Time", "EEST", "Eastern European Summer Time"], "std_offset": 120}
americaBogota = {"id": "America/Bogota", "transitions": [195797, 60, 203860, 0], "names": ["COT", "Colombia Standard Time", "COST", "Colombia Summer Time"], "std_offset": -300}
africaLagos = {"id": "Africa/Lagos", "transitions": [], "names": ["WAT", "West Africa Standard Time", "WAST", "West Africa Summer Time"], "std_offset": 60}
asiaKuching = {"id": "Asia/Kuching", "transitions": [], "names": ["MYT", "Malaysia Time"], "std_offset": 480}
americaBahia = {"id": "America/Bahia", "transitions": [138819, 60, 142010, 0, 147387, 60, 150074, 0, 156147, 60, 158666, 0, 164715, 60, 167234, 0, 173451, 60, 176306, 0, 182355, 60, 185210, 0, 191091, 60, 193778, 0, 199995, 60, 202346, 0, 208563, 60, 211586, 0, 217299, 60, 220322, 0, 226035, 60, 228890, 0, 234603, 60, 237794, 0, 243363, 60, 246866, 0, 252243, 60, 255434, 0, 260811, 60, 264338, 0, 269715, 60, 272906, 0, 278619, 60, 281642, 0, 287859, 60, 290378, 0, 366315, 60, 369506, 0], "names": ["BRT", "Brasilia Standard Time", "BRST", "Brasilia Summer Time"], "std_offset": -180}
asiaKatmandu = {"id": "Asia/Katmandu", "transitions": [], "names": ["NPT", "Nepal Time"], "std_offset": 345}
australiaBrisbane = {"id": "Australia/Brisbane", "transitions": [16024, 60, 18880, 0, 173776, 60, 176800, 0, 182512, 60, 185536, 0, 191248, 60, 194272, 0], "names": ["AEST", "Australian Eastern Standard Time", "AEDT", "Australian Eastern Daylight Time"], "std_offset": 600}
asiaRiyadh = {"id": "Asia/Riyadh", "transitions": [], "names": ["AST", "Arabian Standard Time", "ADT", "Arabian Daylight Time"], "std_offset": 180}
americaMatamoros = {"id": "America/Matamoros", "transitions": [160016, 60, 165055, 0, 230240, 60, 235111, 0, 238976, 60, 243847, 0, 247712, 60, 252583, 0, 256448, 60, 261487, 0, 265184, 60, 270223, 0, 274760, 60, 278287, 0, 282824, 60, 287695, 0, 291560, 60, 296431, 0, 300296, 60, 305335, 0, 309032, 60, 314071, 0, 317768, 60, 322807, 0, 326504, 60, 331543, 0, 335408, 60, 340279, 0, 344144, 60, 349015, 0, 352376, 60, 358087, 0, 361112, 60, 366823, 0, 369848, 60, 375559, 0, 378584, 60, 384295, 0, 387320, 60, 393031, 0, 396056, 60, 401767, 0, 404960, 60, 410671, 0, 413696, 60, 419407, 0, 422432, 60, 428143, 0, 431168, 60, 436879, 0, 439904, 60, 445615, 0, 448808, 60, 454519, 0, 457544, 60, 463255, 0, 466280, 60, 471991, 0, 475016, 60, 480727, 0, 483752, 60, 489463, 0, 492488, 60, 498199, 0, 501392, 60, 507103, 0, 510128, 60, 515839, 0, 518864, 60, 524575, 0, 527600, 60, 533311, 0, 536336, 60, 542047, 0, 545240, 60, 550951, 0, 553976, 60, 559687, 0, 562712, 60, 568423, 0, 571448, 60, 577159, 0, 580184, 60, 585895, 0, 588920, 60, 594631, 0], "names": ["CST", "Central Standard Time", "CDT", "Central Daylight Time"], "std_offset": -360}
americaNewYork = {"id": "America/New_York", "transitions": [2767, 60, 7134, 0, 11503, 60, 16038, 0, 20407, 60, 24774, 0, 29143, 60, 33510, 0, 35191, 60, 42246, 0, 45103, 60, 50982, 0, 55351, 60, 59886, 0, 64087, 60, 68622, 0, 72991, 60, 77358, 0, 81727, 60, 86094, 0, 90463, 60, 94830, 0, 99199, 60, 103566, 0, 107935, 60, 112470, 0, 116671, 60, 121206, 0, 125575, 60, 129942, 0, 134311, 60, 138678, 0, 143047, 60, 147414, 0, 151279, 60, 156150, 0, 160015, 60, 165054, 0, 168751, 60, 173790, 0, 177487, 60, 182526, 0, 186391, 60, 191262, 0, 195127, 60, 199998, 0, 203863, 60, 208902, 0, 212599, 60, 217638, 0, 221335, 60, 226374, 0, 230239, 60, 235110, 0, 238975, 60, 243846, 0, 247711, 60, 252582, 0, 256447, 60, 261486, 0, 265183, 60, 270222, 0, 273919, 60, 278958, 0, 282823, 60, 287694, 0, 291559, 60, 296430, 0, 300295, 60, 305334, 0, 309031, 60, 314070, 0, 317767, 60, 322806, 0, 325999, 60, 331710, 0, 334735, 60, 340446, 0, 343471, 60, 349182, 0, 352375, 60, 358086, 0, 361111, 60, 366822, 0, 369847, 60, 375558, 0, 378583, 60, 384294, 0, 387319, 60, 393030, 0, 396055, 60, 401766, 0, 404959, 60, 410670, 0, 413695, 60, 419406, 0, 422431, 60, 428142, 0, 431167, 60, 436878, 0, 439903, 60, 445614, 0, 448807, 60, 454518, 0, 457543, 60, 463254, 0, 466279, 60, 471990, 0, 475015, 60, 480726, 0, 483751, 60, 489462, 0, 492487, 60, 498198, 0, 501391, 60, 507102, 0, 510127, 60, 515838, 0, 518863, 60, 524574, 0, 527599, 60, 533310, 0, 536335, 60, 542046, 0, 545239, 60, 550950, 0, 553975, 60, 559686, 0, 562711, 60, 568422, 0, 571447, 60, 577158, 0, 580183, 60, 585894, 0, 588919, 60, 594630, 0], "names": ["EST", "Eastern Standard Time", "EDT", "Eastern Daylight Time"], "std_offset": -300}
europeDublin = {"id": "Europe/Dublin", "transitions": [19394, 60, 24770, 0, 28130, 60, 33506, 0, 36866, 60, 42242, 0, 45602, 60, 50978, 0, 54506, 60, 59714, 0, 63242, 60, 68450, 0, 71978, 60, 77354, 0, 80714, 60, 86090, 0, 89450, 60, 94826, 0, 98521, 60, 103561, 0, 107257, 60, 112297, 0, 115993, 60, 121033, 0, 124729, 60, 129937, 0, 133633, 60, 138673, 0, 142369, 60, 147409, 0, 151105, 60, 156145, 0, 159841, 60, 164881, 0, 168577, 60, 173785, 0, 177313, 60, 182521, 0, 186217, 60, 191257, 0, 194953, 60, 199993, 0, 203689, 60, 208729, 0, 212425, 60, 217465, 0, 221161, 60, 226201, 0, 230065, 60, 235105, 0, 238801, 60, 243841, 0, 247537, 60, 252577, 0, 256273, 60, 261481, 0, 265009, 60, 270217, 0, 273745, 60, 278953, 0, 282649, 60, 287689, 0, 291385, 60, 296425, 0, 300121, 60, 305329, 0, 308857, 60, 314065, 0, 317593, 60, 322801, 0, 326329, 60, 331537, 0, 335233, 60, 340273, 0, 343969, 60, 349009, 0, 352705, 60, 357913, 0, 361441, 60, 366649, 0, 370177, 60, 375385, 0, 379081, 60, 384121, 0, 387817, 60, 392857, 0, 396553, 60, 401593, 0, 405289, 60, 410497, 0, 414025, 60, 419233, 0, 422761, 60, 427969, 0, 431665, 60, 436705, 0, 440401, 60, 445441, 0, 449137, 60, 454345, 0, 457873, 60, 463081, 0, 466609, 60, 471817, 0, 475513, 60, 480553, 0, 484249, 60, 489289, 0, 492985, 60, 498025, 0, 501721, 60, 506929, 0, 510457, 60, 515665, 0, 519193, 60, 524401, 0, 528097, 60, 533137, 0, 536833, 60, 541873, 0, 545569, 60, 550777, 0, 554305, 60, 559513, 0, 563041, 60, 568249, 0, 571777, 60, 576985, 0, 580681, 60, 585721, 0, 589417, 60, 594457, 0], "names": ["GMT", "Greenwich Mean Time", "IST", "Irish Summer Time"], "std_offset": 0}
americaJujuy = {"id": "America/Jujuy", "transitions": [35595, 60, 37946, 0, 165819, 60, 168074, 0, 173451, 60, 176810, 0, 182524, 60, 185883, 0, 199827, 60, 203186, 0, 333051, 60, 334898, 0], "names": ["ART", "Argentina Standard Time", "ARST", "Argentina Summer Time"], "std_offset": -180}
americaGodthab = {"id": "America/Godthab", "transitions": [89957, 60, 94153, 0, 98521, 60, 102889, 0, 107257, 60, 111625, 0, 115993, 60, 120361, 0, 124729, 60, 129265, 0, 133633, 60, 138001, 0, 142369, 60, 146737, 0, 151105, 60, 155473, 0, 159841, 60, 164209, 0, 168577, 60, 172945, 0, 177313, 60, 181849, 0, 186217, 60, 190585, 0, 194953, 60, 199321, 0, 203689, 60, 208057, 0, 212425, 60, 216793, 0, 221161, 60, 225529, 0, 230065, 60, 235105, 0, 238801, 60, 243841, 0, 247537, 60, 252577, 0, 256273, 60, 261481, 0, 265009, 60, 270217, 0, 273745, 60, 278953, 0, 282649, 60, 287689, 0, 291385, 60, 296425, 0, 300121, 60, 305329, 0, 308857, 60, 314065, 0, 317593, 60, 322801, 0, 326329, 60, 331537, 0, 335233, 60, 340273, 0, 343969, 60, 349009, 0, 352705, 60, 357913, 0, 361441, 60, 366649, 0, 370177, 60, 375385, 0, 379081, 60, 384121, 0, 387817, 60, 392857, 0, 396553, 60, 401593, 0, 405289, 60, 410497, 0, 414025, 60, 419233, 0, 422761, 60, 427969, 0, 431665, 60, 436705, 0, 440401, 60, 445441, 0, 449137, 60, 454345, 0, 457873, 60, 463081, 0, 466609, 60, 471817, 0, 475513, 60, 480553, 0, 484249, 60, 489289, 0, 492985, 60, 498025, 0, 501721, 60, 506929, 0, 510457, 60, 515665, 0, 519193, 60, 524401, 0, 528097, 60, 533137, 0, 536833, 60, 541873, 0, 545569, 60, 550777, 0, 554305, 60, 559513, 0, 563041, 60, 568249, 0, 571777, 60, 576985, 0, 580681, 60, 585721, 0, 589417, 60, 594457, 0], "names": ["WGT", "West Greenland Standard Time", "WGST", "West Greenland Summer Time"], "std_offset": -180}
europeStockholm = {"id": "Europe/Stockholm", "transitions": [89953, 60, 94153, 0, 98521, 60, 102889, 0, 107257, 60, 111625, 0, 115993, 60, 120361, 0, 124729, 60, 129265, 0, 133633, 60, 138001, 0, 142369, 60, 146737, 0, 151105, 60, 155473, 0, 159841, 60, 164209, 0, 168577, 60, 172945, 0, 177313, 60, 181849, 0, 186217, 60, 190585, 0, 194953, 60, 199321, 0, 203689, 60, 208057, 0, 212425, 60, 216793, 0, 221161, 60, 225529, 0, 230065, 60, 235105, 0, 238801, 60, 243841, 0, 247537, 60, 252577, 0, 256273, 60, 261481, 0, 265009, 60, 270217, 0, 273745, 60, 278953, 0, 282649, 60, 287689, 0, 291385, 60, 296425, 0, 300121, 60, 305329, 0, 308857, 60, 314065, 0, 317593, 60, 322801, 0, 326329, 60, 331537, 0, 335233, 60, 340273, 0, 343969, 60, 349009, 0, 352705, 60, 357913, 0, 361441, 60, 366649, 0, 370177, 60, 375385, 0, 379081, 60, 384121, 0, 387817, 60, 392857, 0, 396553, 60, 401593, 0, 405289, 60, 410497, 0, 414025, 60, 419233, 0, 422761, 60, 427969, 0, 431665, 60, 436705, 0, 440401, 60, 445441, 0, 449137, 60, 454345, 0, 457873, 60, 463081, 0, 466609, 60, 471817, 0, 475513, 60, 480553, 0, 484249, 60, 489289, 0, 492985, 60, 498025, 0, 501721, 60, 506929, 0, 510457, 60, 515665, 0, 519193, 60, 524401, 0, 528097, 60, 533137, 0, 536833, 60, 541873, 0, 545569, 60, 550777, 0, 554305, 60, 559513, 0, 563041, 60, 568249, 0, 571777, 60, 576985, 0, 580681, 60, 585721, 0, 589417, 60, 594457, 0], "names": ["CET", "Central European Standard Time", "CEST", "Central European Summer Time"], "std_offset": 60}
antarcticaMacquarie = {"id": "Antarctica/Macquarie", "transitions": [7120, 60, 10480, 0, 16024, 60, 18880, 0, 24760, 60, 27784, 0, 33496, 60, 36520, 0, 42232, 60, 45256, 0, 50968, 60, 54160, 0, 59872, 60, 62896, 0, 68608, 60, 71632, 0, 77344, 60, 80368, 0, 86080, 60, 89104, 0, 94816, 60, 97840, 0, 103552, 60, 107248, 0, 112456, 60, 115984, 0, 121192, 60, 124216, 0, 129928, 60, 132952, 0, 138664, 60, 141688, 0, 147232, 60, 150760, 0, 156136, 60, 159664, 0, 165040, 60, 168400, 0, 173776, 60, 177136, 0, 182512, 60, 186208, 0, 190744, 60, 194944, 0, 199480, 60, 203680, 0, 208216, 60, 212416, 0, 216952, 60, 221152, 0, 225688, 60, 230056, 0, 234592, 60, 238792, 0, 243328, 60, 247528, 0, 252064, 60, 256264, 0, 260800, 60, 265000, 0, 268696, 60, 273736, 0, 278440, 60, 282640, 0, 287176, 60, 291376, 0, 295912, 60, 300112, 0, 304648, 60, 308848, 0, 313384, 60, 317752, 0, 322120, 60, 326320, 0, 331024, 60, 335392, 0, 339760, 60, 344128, 0, 348496, 60, 352864, 0], "names": ["AEST", "Macquarie Island Time"], "std_offset": 660}
americaWinnipeg = {"id": "America/Winnipeg", "transitions": [2768, 60, 7136, 0, 11504, 60, 16040, 0, 20408, 60, 24776, 0, 29144, 60, 33512, 0, 37880, 60, 42248, 0, 46616, 60, 50984, 0, 55352, 60, 59888, 0, 64088, 60, 68624, 0, 72992, 60, 77360, 0, 81728, 60, 86096, 0, 90464, 60, 94832, 0, 99200, 60, 103568, 0, 107936, 60, 112472, 0, 116672, 60, 121208, 0, 125576, 60, 129944, 0, 134312, 60, 138680, 0, 143048, 60, 147416, 0, 151280, 60, 156152, 0, 160016, 60, 165056, 0, 168752, 60, 173792, 0, 177488, 60, 182528, 0, 186392, 60, 191264, 0, 195128, 60, 200000, 0, 203864, 60, 208904, 0, 212600, 60, 217640, 0, 221336, 60, 226376, 0, 230240, 60, 235112, 0, 238976, 60, 243848, 0, 247712, 60, 252584, 0, 256448, 60, 261488, 0, 265184, 60, 270224, 0, 273920, 60, 278960, 0, 282824, 60, 287696, 0, 291560, 60, 296432, 0, 300296, 60, 305336, 0, 309032, 60, 314072, 0, 317768, 60, 322807, 0, 326000, 60, 331711, 0, 334736, 60, 340447, 0, 343472, 60, 349183, 0, 352376, 60, 358087, 0, 361112, 60, 366823, 0, 369848, 60, 375559, 0, 378584, 60, 384295, 0, 387320, 60, 393031, 0, 396056, 60, 401767, 0, 404960, 60, 410671, 0, 413696, 60, 419407, 0, 422432, 60, 428143, 0, 431168, 60, 436879, 0, 439904, 60, 445615, 0, 448808, 60, 454519, 0, 457544, 60, 463255, 0, 466280, 60, 471991, 0, 475016, 60, 480727, 0, 483752, 60, 489463, 0, 492488, 60, 498199, 0, 501392, 60, 507103, 0, 510128, 60, 515839, 0, 518864, 60, 524575, 0, 527600, 60, 533311, 0, 536336, 60, 542047, 0, 545240, 60, 550951, 0, 553976, 60, 559687, 0, 562712, 60, 568423, 0, 571448, 60, 577159, 0, 580184, 60, 585895, 0, 588920, 60, 594631, 0], "names": ["CST", "Central Standard Time", "CDT", "Central Daylight Time"], "std_offset": -360}
americaMazatlan = {"id": "America/Mazatlan", "transitions": [230241, 60, 235112, 0, 238977, 60, 243848, 0, 247713, 60, 252584, 0, 256449, 60, 261488, 0, 265185, 60, 270224, 0, 274761, 60, 278288, 0, 282825, 60, 287696, 0, 291561, 60, 296432, 0, 300297, 60, 305336, 0, 309033, 60, 314072, 0, 317769, 60, 322808, 0, 326505, 60, 331544, 0, 335409, 60, 340280, 0, 344145, 60, 349016, 0, 352881, 60, 357920, 0, 361617, 60, 366656, 0, 370353, 60, 375392, 0, 379257, 60, 384128, 0, 387993, 60, 392864, 0, 396729, 60, 401600, 0, 405465, 60, 410504, 0, 414201, 60, 419240, 0, 422937, 60, 427976, 0, 431841, 60, 436712, 0, 440577, 60, 445448, 0, 449313, 60, 454352, 0, 458049, 60, 463088, 0, 466785, 60, 471824, 0, 475689, 60, 480560, 0, 484425, 60, 489296, 0, 493161, 60, 498032, 0, 501897, 60, 506936, 0, 510633, 60, 515672, 0, 519369, 60, 524408, 0, 528273, 60, 533144, 0, 537009, 60, 541880, 0, 545745, 60, 550784, 0, 554481, 60, 559520, 0, 563217, 60, 568256, 0, 571953, 60, 576992, 0, 580857, 60, 585728, 0, 589593, 60, 594464, 0], "names": ["MST", "Mexican Pacific Standard Time", "MDT", "Mexican Pacific Daylight Time"], "std_offset": -420}
americaBahiaBanderas = {"id": "America/Bahia_Banderas", "transitions": [230241, 60, 235112, 0, 238977, 60, 243848, 0, 247713, 60, 252584, 0, 256449, 60, 261488, 0, 265185, 60, 270224, 0, 274761, 60, 278288, 0, 282825, 60, 287696, 0, 291561, 60, 296432, 0, 300297, 60, 305336, 0, 309033, 60, 314072, 0, 317769, 60, 322808, 0, 326505, 60, 331544, 0, 335409, 60, 340280, 0, 344145, 60, 349016, 0, 361616, 60, 366655, 0, 370352, 60, 375391, 0, 379256, 60, 384127, 0, 387992, 60, 392863, 0, 396728, 60, 401599, 0, 405464, 60, 410503, 0, 414200, 60, 419239, 0, 422936, 60, 427975, 0, 431840, 60, 436711, 0, 440576, 60, 445447, 0, 449312, 60, 454351, 0, 458048, 60, 463087, 0, 466784, 60, 471823, 0, 475688, 60, 480559, 0, 484424, 60, 489295, 0, 493160, 60, 498031, 0, 501896, 60, 506935, 0, 510632, 60, 515671, 0, 519368, 60, 524407, 0, 528272, 60, 533143, 0, 537008, 60, 541879, 0, 545744, 60, 550783, 0, 554480, 60, 559519, 0, 563216, 60, 568255, 0, 571952, 60, 576991, 0, 580856, 60, 585727, 0, 589592, 60, 594463, 0], "names": ["CST", "Central Standard Time", "CDT", "Central Daylight Time"], "std_offset": -360}
americaIndianaKnox = {"id": "America/Indiana/Knox", "transitions": [2768, 60, 7135, 0, 11504, 60, 16039, 0, 20408, 60, 24775, 0, 29144, 60, 33511, 0, 35192, 60, 42247, 0, 45104, 60, 50983, 0, 55352, 60, 59887, 0, 64088, 60, 68623, 0, 72992, 60, 77359, 0, 81728, 60, 86095, 0, 90464, 60, 94831, 0, 99200, 60, 103567, 0, 107936, 60, 112471, 0, 116672, 60, 121207, 0, 125576, 60, 129943, 0, 134312, 60, 138679, 0, 143048, 60, 147415, 0, 151280, 60, 156151, 0, 160016, 60, 165055, 0, 168752, 60, 173791, 0, 177488, 60, 182527, 0, 186392, 60, 191263, 0, 317767, 60, 322807, 0, 326000, 60, 331711, 0, 334736, 60, 340447, 0, 343472, 60, 349183, 0, 352376, 60, 358087, 0, 361112, 60, 366823, 0, 369848, 60, 375559, 0, 378584, 60, 384295, 0, 387320, 60, 393031, 0, 396056, 60, 401767, 0, 404960, 60, 410671, 0, 413696, 60, 419407, 0, 422432, 60, 428143, 0, 431168, 60, 436879, 0, 439904, 60, 445615, 0, 448808, 60, 454519, 0, 457544, 60, 463255, 0, 466280, 60, 471991, 0, 475016, 60, 480727, 0, 483752, 60, 489463, 0, 492488, 60, 498199, 0, 501392, 60, 507103, 0, 510128, 60, 515839, 0, 518864, 60, 524575, 0, 527600, 60, 533311, 0, 536336, 60, 542047, 0, 545240, 60, 550951, 0, 553976, 60, 559687, 0, 562712, 60, 568423, 0, 571448, 60, 577159, 0, 580184, 60, 585895, 0, 588920, 60, 594631, 0], "names": ["CST", "Central Standard Time", "CDT", "Central Daylight Time"], "std_offset": -360}
asiaTbilisi = {"id": "Asia/Tbilisi", "transitions": [98588, 60, 102979, 0, 107348, 60, 111739, 0, 116108, 60, 120499, 0, 124892, 60, 129262, 0, 133630, 60, 137998, 0, 142366, 60, 146734, 0, 151102, 60, 155470, 0, 159838, 60, 164206, 0, 168574, 60, 172942, 0, 177310, 60, 181846, 0, 186214, 60, 190583, 0, 194949, 60, 199316, 0, 203685, 60, 208052, 0, 212421, 60, 216788, 0, 221156, 60, 225523, 0, 230060, 60, 243835, 0, 247532, 60, 252571, 0, 256268, 60, 261475, 0, 265004, 60, 270211, 0, 273740, 60, 278947, 0, 282644, 60, 287683, 0, 291380, 60, 296419, 0, 300116, 60, 305327, 0], "names": ["GET", "Georgia Standard Time", "GEST", "Georgia Summer Time"], "std_offset": 240}
americaGuyana = {"id": "America/Guyana", "transitions": [], "names": ["GYT", "Guyana Time"], "std_offset": -240}
europeSanMarino = {"id": "Europe/San_Marino", "transitions": [3599, 60, 6454, 0, 12167, 60, 15191, 0, 21071, 60, 24094, 0, 29975, 60, 32830, 0, 38543, 60, 41566, 0, 47447, 60, 50303, 0, 56183, 60, 59039, 0, 64751, 60, 67775, 0, 73655, 60, 76679, 0, 82391, 60, 85415, 0, 89953, 60, 94153, 0, 98521, 60, 102889, 0, 107257, 60, 111625, 0, 115993, 60, 120361, 0, 124729, 60, 129265, 0, 133633, 60, 138001, 0, 142369, 60, 146737, 0, 151105, 60, 155473, 0, 159841, 60, 164209, 0, 168577, 60, 172945, 0, 177313, 60, 181849, 0, 186217, 60, 190585, 0, 194953, 60, 199321, 0, 203689, 60, 208057, 0, 212425, 60, 216793, 0, 221161, 60, 225529, 0, 230065, 60, 235105, 0, 238801, 60, 243841, 0, 247537, 60, 252577, 0, 256273, 60, 261481, 0, 265009, 60, 270217, 0, 273745, 60, 278953, 0, 282649, 60, 287689, 0, 291385, 60, 296425, 0, 300121, 60, 305329, 0, 308857, 60, 314065, 0, 317593, 60, 322801, 0, 326329, 60, 331537, 0, 335233, 60, 340273, 0, 343969, 60, 349009, 0, 352705, 60, 357913, 0, 361441, 60, 366649, 0, 370177, 60, 375385, 0, 379081, 60, 384121, 0, 387817, 60, 392857, 0, 396553, 60, 401593, 0, 405289, 60, 410497, 0, 414025, 60, 419233, 0, 422761, 60, 427969, 0, 431665, 60, 436705, 0, 440401, 60, 445441, 0, 449137, 60, 454345, 0, 457873, 60, 463081, 0, 466609, 60, 471817, 0, 475513, 60, 480553, 0, 484249, 60, 489289, 0, 492985, 60, 498025, 0, 501721, 60, 506929, 0, 510457, 60, 515665, 0, 519193, 60, 524401, 0, 528097, 60, 533137, 0, 536833, 60, 541873, 0, 545569, 60, 550777, 0, 554305, 60, 559513, 0, 563041, 60, 568249, 0, 571777, 60, 576985, 0, 580681, 60, 585721, 0, 589417, 60, 594457, 0], "names": ["CET", "Central European Standard Time", "CEST", "Central European Summer Time"], "std_offset": 60}
pacificWake = {"id": "Pacific/Wake", "transitions": [], "names": ["WAKT", "Wake Island Time"], "std_offset": 720}
atlanticMadeira = {"id": "Atlantic/Madeira", "transitions": [63408, 60, 67776, 0, 72312, 60, 76680, 0, 81048, 60, 85417, 0, 89784, 60, 94153, 0, 98521, 60, 102889, 0, 107257, 60, 111625, 0, 115994, 60, 120361, 0, 124729, 60, 129265, 0, 133633, 60, 138001, 0, 142369, 60, 146737, 0, 151105, 60, 155473, 0, 159841, 60, 164209, 0, 168577, 60, 172945, 0, 177313, 60, 181849, 0, 186217, 60, 190585, 0, 194953, 60, 199321, 0, 203689, 60, 208057, 0, 212425, 60, 216793, 0, 221161, 60, 225529, 0, 230065, 60, 235105, 0, 238801, 60, 243841, 0, 247537, 60, 252577, 0, 256273, 60, 261481, 0, 265009, 60, 270217, 0, 273745, 60, 278953, 0, 282649, 60, 287689, 0, 291385, 60, 296425, 0, 300121, 60, 305329, 0, 308857, 60, 314065, 0, 317593, 60, 322801, 0, 326329, 60, 331537, 0, 335233, 60, 340273, 0, 343969, 60, 349009, 0, 352705, 60, 357913, 0, 361441, 60, 366649, 0, 370177, 60, 375385, 0, 379081, 60, 384121, 0, 387817, 60, 392857, 0, 396553, 60, 401593, 0, 405289, 60, 410497, 0, 414025, 60, 419233, 0, 422761, 60, 427969, 0, 431665, 60, 436705, 0, 440401, 60, 445441, 0, 449137, 60, 454345, 0, 457873, 60, 463081, 0, 466609, 60, 471817, 0, 475513, 60, 480553, 0, 484249, 60, 489289, 0, 492985, 60, 498025, 0, 501721, 60, 506929, 0, 510457, 60, 515665, 0, 519193, 60, 524401, 0, 528097, 60, 533137, 0, 536833, 60, 541873, 0, 545569, 60, 550777, 0, 554305, 60, 559513, 0, 563041, 60, 568249, 0, 571777, 60, 576985, 0, 580681, 60, 585721, 0, 589417, 60, 594457, 0], "names": ["WET", "Western European Standard Time", "WEST", "Western European Summer Time"], "std_offset": 0}
africaBrazzaville = {"id": "Africa/Brazzaville", "transitions": [], "names": ["WAT", "West Africa Standard Time", "WAST", "West Africa Summer Time"], "std_offset": 60}
asiaJayapura = {"id": "Asia/Jayapura", "transitions": [], "names": ["EIT", "Eastern Indonesia Time"], "std_offset": 540}
asiaAshgabat = {"id": "Asia/Ashgabat", "transitions": [98587, 60, 102978, 0, 107347, 60, 111738, 0, 116107, 60, 120498, 0, 124891, 60, 129261, 0, 133629, 60, 137997, 0, 142365, 60, 146733, 0, 151101, 60, 155469, 0, 159837, 60, 164205, 0, 168573, 60, 172941, 0, 177309, 60, 181845, 0, 186213, 60, 190582, 0], "names": ["TMT", "Turkmenistan Standard Time", "TMST", "Turkmenistan Summer Time"], "std_offset": 300}
americaStJohns = {"id": "America/St_Johns", "transitions": [2765, 60, 7132, 0, 11501, 60, 16036, 0, 20405, 60, 24772, 0, 29141, 60, 33508, 0, 37877, 60, 42244, 0, 46613, 60, 50980, 0, 55349, 60, 59884, 0, 64085, 60, 68620, 0, 72989, 60, 77356, 0, 81725, 60, 86092, 0, 90461, 60, 94828, 0, 99197, 60, 103564, 0, 107933, 60, 112468, 0, 116669, 60, 121204, 0, 125573, 60, 129940, 0, 134309, 60, 138676, 0, 143045, 60, 147412, 0, 151275, 60, 156146, 0, 168747, 60, 173786, 0, 177483, 60, 182522, 0, 186387, 60, 191258, 0, 195123, 60, 199994, 0, 203859, 60, 208898, 0, 212595, 60, 217634, 0, 221331, 60, 226370, 0, 230235, 60, 235106, 0, 238971, 60, 243842, 0, 247707, 60, 252578, 0, 256443, 60, 261482, 0, 265179, 60, 270218, 0, 273915, 60, 278954, 0, 282819, 60, 287690, 0, 291555, 60, 296426, 0, 300291, 60, 305330, 0, 309027, 60, 314066, 0, 317763, 60, 322802, 0, 325995, 60, 331706, 0, 334731, 60, 340442, 0, 343467, 60, 349178, 0, 352371, 60, 358082, 0, 361107, 60, 366820, 0, 369845, 60, 375556, 0, 378581, 60, 384292, 0, 387317, 60, 393028, 0, 396053, 60, 401764, 0, 404957, 60, 410668, 0, 413693, 60, 419404, 0, 422429, 60, 428140, 0, 431165, 60, 436876, 0, 439901, 60, 445612, 0, 448805, 60, 454516, 0, 457541, 60, 463252, 0, 466277, 60, 471988, 0, 475013, 60, 480724, 0, 483749, 60, 489460, 0, 492485, 60, 498196, 0, 501389, 60, 507100, 0, 510125, 60, 515836, 0, 518861, 60, 524572, 0, 527597, 60, 533308, 0, 536333, 60, 542044, 0, 545237, 60, 550948, 0, 553973, 60, 559684, 0, 562709, 60, 568420, 0, 571445, 60, 577156, 0, 580181, 60, 585892, 0, 588917, 60, 594628, 0], "names": ["NST", "Newfoundland Standard Time", "NDT", "Newfoundland Daylight Time"], "std_offset": -210}
americaKentuckyMonticello = {"id": "America/Kentucky/Monticello", "transitions": [2768, 60, 7135, 0, 11504, 60, 16039, 0, 20408, 60, 24775, 0, 29144, 60, 33511, 0, 35192, 60, 42247, 0, 45104, 60, 50983, 0, 55352, 60, 59887, 0, 64088, 60, 68623, 0, 72992, 60, 77359, 0, 81728, 60, 86095, 0, 90464, 60, 94831, 0, 99200, 60, 103567, 0, 107936, 60, 112471, 0, 116672, 60, 121207, 0, 125576, 60, 129943, 0, 134312, 60, 138679, 0, 143048, 60, 147415, 0, 151280, 60, 156151, 0, 160016, 60, 165055, 0, 168752, 60, 173791, 0, 177488, 60, 182527, 0, 186392, 60, 191263, 0, 195128, 60, 199999, 0, 203864, 60, 208903, 0, 212600, 60, 217639, 0, 221336, 60, 226375, 0, 230240, 60, 235111, 0, 238976, 60, 243847, 0, 247712, 60, 252583, 0, 256448, 60, 261487, 0, 265184, 60, 270223, 0, 273919, 60, 278958, 0, 282823, 60, 287694, 0, 291559, 60, 296430, 0, 300295, 60, 305334, 0, 309031, 60, 314070, 0, 317767, 60, 322806, 0, 325999, 60, 331710, 0, 334735, 60, 340446, 0, 343471, 60, 349182, 0, 352375, 60, 358086, 0, 361111, 60, 366822, 0, 369847, 60, 375558, 0, 378583, 60, 384294, 0, 387319, 60, 393030, 0, 396055, 60, 401766, 0, 404959, 60, 410670, 0, 413695, 60, 419406, 0, 422431, 60, 428142, 0, 431167, 60, 436878, 0, 439903, 60, 445614, 0, 448807, 60, 454518, 0, 457543, 60, 463254, 0, 466279, 60, 471990, 0, 475015, 60, 480726, 0, 483751, 60, 489462, 0, 492487, 60, 498198, 0, 501391, 60, 507102, 0, 510127, 60, 515838, 0, 518863, 60, 524574, 0, 527599, 60, 533310, 0, 536335, 60, 542046, 0, 545239, 60, 550950, 0, 553975, 60, 559686, 0, 562711, 60, 568422, 0, 571447, 60, 577158, 0, 580183, 60, 585894, 0, 588919, 60, 594630, 0], "names": ["EST", "Eastern Standard Time", "EDT", "Eastern Daylight Time"], "std_offset": -300}
africaNdjamena = {"id": "Africa/Ndjamena", "transitions": [85751, 60, 89254, 0], "names": ["WAT", "West Africa Standard Time", "WAST", "West Africa Summer Time"], "std_offset": 60}
americaRankinInlet = {"id": "America/Rankin_Inlet", "transitions": [90464, 60, 94831, 0, 99200, 60, 103567, 0, 107936, 60, 112471, 0, 116672, 60, 121207, 0, 125576, 60, 129943, 0, 134312, 60, 138679, 0, 143048, 60, 147415, 0, 151280, 60, 156151, 0, 160016, 60, 165055, 0, 168752, 60, 173791, 0, 177488, 60, 182527, 0, 186392, 60, 191263, 0, 195128, 60, 199999, 0, 203864, 60, 208903, 0, 212600, 60, 217639, 0, 221336, 60, 226375, 0, 230240, 60, 235111, 0, 238976, 60, 243847, 0, 247712, 60, 252583, 0, 256448, 60, 261487, 0, 265184, 60, 270223, 0, 273920, 60, 278959, 0, 282824, 60, 287695, 0, 291560, 60, 296431, 0, 300296, 60, 305335, 0, 309032, 60, 314071, 0, 317768, 60, 322807, 0, 326000, 60, 331711, 0, 334736, 60, 340447, 0, 343472, 60, 349183, 0, 352376, 60, 358087, 0, 361112, 60, 366823, 0, 369848, 60, 375559, 0, 378584, 60, 384295, 0, 387320, 60, 393031, 0, 396056, 60, 401767, 0, 404960, 60, 410671, 0, 413696, 60, 419407, 0, 422432, 60, 428143, 0, 431168, 60, 436879, 0, 439904, 60, 445615, 0, 448808, 60, 454519, 0, 457544, 60, 463255, 0, 466280, 60, 471991, 0, 475016, 60, 480727, 0, 483752, 60, 489463, 0, 492488, 60, 498199, 0, 501392, 60, 507103, 0, 510128, 60, 515839, 0, 518864, 60, 524575, 0, 527600, 60, 533311, 0, 536336, 60, 542047, 0, 545240, 60, 550951, 0, 553976, 60, 559687, 0, 562712, 60, 568423, 0, 571448, 60, 577159, 0, 580184, 60, 585895, 0, 588920, 60, 594631, 0], "names": ["CST", "Central Standard Time", "CDT", "Central Daylight Time"], "std_offset": -360}
americaEdmonton = {"id": "America/Edmonton", "transitions": [20409, 60, 24776, 0, 29145, 60, 33512, 0, 37881, 60, 42248, 0, 46617, 60, 50984, 0, 55353, 60, 59888, 0, 64089, 60, 68624, 0, 72993, 60, 77360, 0, 81729, 60, 86096, 0, 90465, 60, 94832, 0, 99201, 60, 103568, 0, 107937, 60, 112472, 0, 116673, 60, 121208, 0, 125577, 60, 129944, 0, 134313, 60, 138680, 0, 143049, 60, 147416, 0, 151281, 60, 156152, 0, 160017, 60, 165056, 0, 168753, 60, 173792, 0, 177489, 60, 182528, 0, 186393, 60, 191264, 0, 195129, 60, 200000, 0, 203865, 60, 208904, 0, 212601, 60, 217640, 0, 221337, 60, 226376, 0, 230241, 60, 235112, 0, 238977, 60, 243848, 0, 247713, 60, 252584, 0, 256449, 60, 261488, 0, 265185, 60, 270224, 0, 273921, 60, 278960, 0, 282825, 60, 287696, 0, 291561, 60, 296432, 0, 300297, 60, 305336, 0, 309033, 60, 314072, 0, 317769, 60, 322808, 0, 326001, 60, 331712, 0, 334737, 60, 340448, 0, 343473, 60, 349184, 0, 352377, 60, 358088, 0, 361113, 60, 366824, 0, 369849, 60, 375560, 0, 378585, 60, 384296, 0, 387321, 60, 393032, 0, 396057, 60, 401768, 0, 404961, 60, 410672, 0, 413697, 60, 419408, 0, 422433, 60, 428144, 0, 431169, 60, 436880, 0, 439905, 60, 445616, 0, 448809, 60, 454520, 0, 457545, 60, 463256, 0, 466281, 60, 471992, 0, 475017, 60, 480728, 0, 483753, 60, 489464, 0, 492489, 60, 498200, 0, 501393, 60, 507104, 0, 510129, 60, 515840, 0, 518865, 60, 524576, 0, 527601, 60, 533312, 0, 536337, 60, 542048, 0, 545241, 60, 550952, 0, 553977, 60, 559688, 0, 562713, 60, 568424, 0, 571449, 60, 577160, 0, 580185, 60, 585896, 0, 588921, 60, 594632, 0], "names": ["MST", "Mountain Standard Time", "MDT", "Mountain Daylight Time"], "std_offset": -420}
americaLaPaz = {"id": "America/La_Paz", "transitions": [], "names": ["BOT", "Bolivia Time"], "std_offset": -240}
asiaAqtau = {"id": "Asia/Aqtau", "transitions": [107346, 60, 111738, 0, 116107, 60, 120498, 0, 124891, 60, 129261, 0, 133629, 60, 137997, 0, 142365, 60, 146733, 0, 151101, 60, 155469, 0, 159837, 60, 164205, 0, 168573, 60, 172941, 0, 177309, 60, 181845, 0, 194946, 60, 199313, 0, 203685, 60, 208053, 0, 212421, 60, 216789, 0, 221157, 60, 225526, 0, 230062, 60, 235102, 0, 238798, 60, 243838, 0, 247534, 60, 252574, 0, 256270, 60, 261478, 0, 265006, 60, 270214, 0, 273742, 60, 278950, 0, 282646, 60, 287686, 0, 291382, 60, 296422, 0, 300118, 60, 305326, 0], "names": ["WKST", "West Kazakhstan Time"], "std_offset": 300}
americaElSalvador = {"id": "America/El_Salvador", "transitions": [151950, 60, 155477, 0, 160686, 60, 164213, 0], "names": ["CST", "Central Standard Time", "CDT", "Central Daylight Time"], "std_offset": -360}
europeBudapest = {"id": "Europe/Budapest", "transitions": [89952, 60, 94153, 0, 98521, 60, 102889, 0, 107257, 60, 111625, 0, 115993, 60, 120361, 0, 124729, 60, 129265, 0, 133633, 60, 138001, 0, 142369, 60, 146737, 0, 151105, 60, 155473, 0, 159841, 60, 164209, 0, 168577, 60, 172945, 0, 177313, 60, 181849, 0, 186217, 60, 190585, 0, 194953, 60, 199321, 0, 203689, 60, 208057, 0, 212425, 60, 216793, 0, 221161, 60, 225529, 0, 230065, 60, 235105, 0, 238801, 60, 243841, 0, 247537, 60, 252577, 0, 256273, 60, 261481, 0, 265009, 60, 270217, 0, 273745, 60, 278953, 0, 282649, 60, 287689, 0, 291385, 60, 296425, 0, 300121, 60, 305329, 0, 308857, 60, 314065, 0, 317593, 60, 322801, 0, 326329, 60, 331537, 0, 335233, 60, 340273, 0, 343969, 60, 349009, 0, 352705, 60, 357913, 0, 361441, 60, 366649, 0, 370177, 60, 375385, 0, 379081, 60, 384121, 0, 387817, 60, 392857, 0, 396553, 60, 401593, 0, 405289, 60, 410497, 0, 414025, 60, 419233, 0, 422761, 60, 427969, 0, 431665, 60, 436705, 0, 440401, 60, 445441, 0, 449137, 60, 454345, 0, 457873, 60, 463081, 0, 466609, 60, 471817, 0, 475513, 60, 480553, 0, 484249, 60, 489289, 0, 492985, 60, 498025, 0, 501721, 60, 506929, 0, 510457, 60, 515665, 0, 519193, 60, 524401, 0, 528097, 60, 533137, 0, 536833, 60, 541873, 0, 545569, 60, 550777, 0, 554305, 60, 559513, 0, 563041, 60, 568249, 0, 571777, 60, 576985, 0, 580681, 60, 585721, 0, 589417, 60, 594457, 0], "names": ["CET", "Central European Standard Time", "CEST", "Central European Summer Time"], "std_offset": 60}
americaBoaVista = {"id": "America/Boa_Vista", "transitions": [138820, 60, 142011, 0, 147388, 60, 150075, 0, 156148, 60, 158667, 0, 260812, 60, 264339, 0, 269716, 60, 269883, 0], "names": ["AMT", "Amazon Standard Time", "AMST", "Amazon Summer Time"], "std_offset": -240}
antarcticaCasey = {"id": "Antarctica/Casey", "transitions": [], "names": ["AWST", "Australian Western Standard Time", "AWDT", "Australian Western Daylight Time"], "std_offset": 480}
asiaKualaLumpur = {"id": "Asia/Kuala_Lumpur", "transitions": [], "names": ["MYT", "Malaysia Time"], "std_offset": 480}
americaVancouver = {"id": "America/Vancouver", "transitions": [2770, 60, 7137, 0, 11506, 60, 16041, 0, 20410, 60, 24777, 0, 29146, 60, 33513, 0, 37882, 60, 42249, 0, 46618, 60, 50985, 0, 55354, 60, 59889, 0, 64090, 60, 68625, 0, 72994, 60, 77361, 0, 81730, 60, 86097, 0, 90466, 60, 94833, 0, 99202, 60, 103569, 0, 107938, 60, 112473, 0, 116674, 60, 121209, 0, 125578, 60, 129945, 0, 134314, 60, 138681, 0, 143050, 60, 147417, 0, 151282, 60, 156153, 0, 160018, 60, 165057, 0, 168754, 60, 173793, 0, 177490, 60, 182529, 0, 186394, 60, 191265, 0, 195130, 60, 200001, 0, 203866, 60, 208905, 0, 212602, 60, 217641, 0, 221338, 60, 226377, 0, 230242, 60, 235113, 0, 238978, 60, 243849, 0, 247714, 60, 252585, 0, 256450, 60, 261489, 0, 265186, 60, 270225, 0, 273922, 60, 278961, 0, 282826, 60, 287697, 0, 291562, 60, 296433, 0, 300298, 60, 305337, 0, 309034, 60, 314073, 0, 317770, 60, 322809, 0, 326002, 60, 331713, 0, 334738, 60, 340449, 0, 343474, 60, 349185, 0, 352378, 60, 358089, 0, 361114, 60, 366825, 0, 369850, 60, 375561, 0, 378586, 60, 384297, 0, 387322, 60, 393033, 0, 396058, 60, 401769, 0, 404962, 60, 410673, 0, 413698, 60, 419409, 0, 422434, 60, 428145, 0, 431170, 60, 436881, 0, 439906, 60, 445617, 0, 448810, 60, 454521, 0, 457546, 60, 463257, 0, 466282, 60, 471993, 0, 475018, 60, 480729, 0, 483754, 60, 489465, 0, 492490, 60, 498201, 0, 501394, 60, 507105, 0, 510130, 60, 515841, 0, 518866, 60, 524577, 0, 527602, 60, 533313, 0, 536338, 60, 542049, 0, 545242, 60, 550953, 0, 553978, 60, 559689, 0, 562714, 60, 568425, 0, 571450, 60, 577161, 0, 580186, 60, 585897, 0, 588922, 60, 594633, 0], "names": ["PST", "Pacific Standard Time", "PDT", "Pacific Daylight Time"], "std_offset": -480}
pacificKwajalein = {"id": "Pacific/Kwajalein", "transitions": [], "names": ["MHT", "Marshall Islands Time"], "std_offset": 720}
africaLubumbashi = {"id": "Africa/Lubumbashi", "transitions": [], "names": ["CAT", "Central Africa Time"], "std_offset": 120}
africaTunis = {"id": "Africa/Tunis", "transitions": [64223, 60, 67751, 0, 73007, 60, 76679, 0, 161423, 60, 164207, 0, 168575, 60, 172943, 0, 178199, 60, 181847, 0, 309695, 60, 313344, 0, 317593, 60, 322801, 0, 326329, 60, 331537, 0, 335233, 60, 340273, 0], "names": ["CET", "Central European Standard Time", "CEST", "Central European Summer Time"], "std_offset": 60}
americaStKitts = {"id": "America/St_Kitts", "transitions": [], "names": ["AST", "Atlantic Standard Time", "ADT", "Atlantic Daylight Time"], "std_offset": -240}
africaLuanda = {"id": "Africa/Luanda", "transitions": [], "names": ["WAT", "West Africa Standard Time", "WAST", "West Africa Summer Time"], "std_offset": 60}
asiaKarachi = {"id": "Asia/Karachi", "transitions": [282811, 60, 287178, 0, 336739, 60, 340410, 0, 344371, 60, 349170, 0], "names": ["PKT", "Pakistan Standard Time", "PKST", "Pakistan Summer Time"], "std_offset": 300}
africaKigali = {"id": "Africa/Kigali", "transitions": [], "names": ["CAT", "Central Africa Time"], "std_offset": 120}
africaBanjul = {"id": "Africa/Banjul", "transitions": [], "names": ["GMT", "Greenwich Mean Time"], "std_offset": 0}
atlanticBermuda = {"id": "Atlantic/Bermuda", "transitions": [37878, 60, 42245, 0, 46614, 60, 50981, 0, 55350, 60, 59885, 0, 64086, 60, 68621, 0, 72990, 60, 77357, 0, 81726, 60, 86093, 0, 90462, 60, 94829, 0, 99198, 60, 103565, 0, 107934, 60, 112469, 0, 116670, 60, 121205, 0, 125574, 60, 129941, 0, 134310, 60, 138677, 0, 143046, 60, 147413, 0, 151278, 60, 156149, 0, 160014, 60, 165053, 0, 168750, 60, 173789, 0, 177486, 60, 182525, 0, 186390, 60, 191261, 0, 195126, 60, 199997, 0, 203862, 60, 208901, 0, 212598, 60, 217637, 0, 221334, 60, 226373, 0, 230238, 60, 235109, 0, 238974, 60, 243845, 0, 247710, 60, 252581, 0, 256446, 60, 261485, 0, 265182, 60, 270221, 0, 273918, 60, 278957, 0, 282822, 60, 287693, 0, 291558, 60, 296429, 0, 300294, 60, 305333, 0, 309030, 60, 314069, 0, 317766, 60, 322805, 0, 325998, 60, 331709, 0, 334734, 60, 340445, 0, 343470, 60, 349181, 0, 352374, 60, 358085, 0, 361110, 60, 366821, 0, 369846, 60, 375557, 0, 378582, 60, 384293, 0, 387318, 60, 393029, 0, 396054, 60, 401765, 0, 404958, 60, 410669, 0, 413694, 60, 419405, 0, 422430, 60, 428141, 0, 431166, 60, 436877, 0, 439902, 60, 445613, 0, 448806, 60, 454517, 0, 457542, 60, 463253, 0, 466278, 60, 471989, 0, 475014, 60, 480725, 0, 483750, 60, 489461, 0, 492486, 60, 498197, 0, 501390, 60, 507101, 0, 510126, 60, 515837, 0, 518862, 60, 524573, 0, 527598, 60, 533309, 0, 536334, 60, 542045, 0, 545238, 60, 550949, 0, 553974, 60, 559685, 0, 562710, 60, 568421, 0, 571446, 60, 577157, 0, 580182, 60, 585893, 0, 588918, 60, 594629, 0], "names": ["AST", "Atlantic Standard Time", "ADT", "Atlantic Daylight Time"], "std_offset": -240}
americaStThomas = {"id": "America/St_Thomas", "transitions": [], "names": ["AST", "Atlantic Standard Time", "ADT", "Atlantic Daylight Time"], "std_offset": -240}
pacificRarotonga = {"id": "Pacific/Rarotonga", "transitions": [86098, 30, 89121, 0, 94834, 30, 97857, 0, 103570, 30, 106761, 0, 112474, 30, 115497, 0, 121210, 30, 124233, 0, 129946, 30, 132969, 0, 138682, 30, 141705, 0, 147418, 30, 150441, 0, 156154, 30, 159345, 0, 165058, 30, 168081, 0, 173794, 30, 176817, 0, 182530, 30, 185553, 0], "names": ["CKT", "Cook Islands Standard Time", "CKHST", "Cook Islands Half Summer Time"], "std_offset": -600}
pacificFunafuti = {"id": "Pacific/Funafuti", "transitions": [], "names": ["TVT", "Tuvalu Time"], "std_offset": 720}
europeSamara = {"id": "Europe/Samara", "transitions": [98588, 60, 102979, 0, 107348, 60, 111739, 0, 116108, 60, 120499, 0, 124892, 60, 129262, 0, 133630, 60, 137998, 0, 142366, 60, 146734, 0, 151102, 60, 155470, 0, 159838, 60, 164206, 0, 168574, 60, 172943, 0, 177311, 60, 181847, 0, 194947, 60, 199314, 0, 203686, 60, 208054, 0, 212422, 60, 216790, 0, 221158, 60, 225526, 0, 230062, 60, 235102, 0, 238798, 60, 243838, 0, 247534, 60, 252574, 0, 256270, 60, 261478, 0, 265006, 60, 270214, 0, 273742, 60, 278950, 0, 282646, 60, 287686, 0, 291382, 60, 296422, 0, 300118, 60, 305326, 0, 308854, 60, 314062, 0, 317590, 60, 322798, 0, 326326, 60, 331534, 0, 335230, 60, 340270, 0, 343966, 60, 349006, 0, 352702, 60, 357911, 0], "names": ["SAMT", "Moscow Standard Time", "SAMST", "Moscow Summer Time"], "std_offset": 240}
americaPangnirtung = {"id": "America/Pangnirtung", "transitions": [90462, 60, 94829, 0, 99198, 60, 103565, 0, 107934, 60, 112469, 0, 116670, 60, 121205, 0, 125574, 60, 129941, 0, 134310, 60, 138677, 0, 143046, 60, 147413, 0, 151278, 60, 156149, 0, 160014, 60, 165053, 0, 168750, 60, 173789, 0, 177486, 60, 182525, 0, 186390, 60, 191261, 0, 195126, 60, 199997, 0, 203862, 60, 208901, 0, 212598, 60, 217637, 0, 221334, 60, 226374, 0, 230239, 60, 235110, 0, 238975, 60, 243846, 0, 247711, 60, 252582, 0, 256447, 60, 261486, 0, 265184, 60, 270223, 0, 273919, 60, 278958, 0, 282823, 60, 287694, 0, 291559, 60, 296430, 0, 300295, 60, 305334, 0, 309031, 60, 314070, 0, 317767, 60, 322806, 0, 325999, 60, 331710, 0, 334735, 60, 340446, 0, 343471, 60, 349182, 0, 352375, 60, 358086, 0, 361111, 60, 366822, 0, 369847, 60, 375558, 0, 378583, 60, 384294, 0, 387319, 60, 393030, 0, 396055, 60, 401766, 0, 404959, 60, 410670, 0, 413695, 60, 419406, 0, 422431, 60, 428142, 0, 431167, 60, 436878, 0, 439903, 60, 445614, 0, 448807, 60, 454518, 0, 457543, 60, 463254, 0, 466279, 60, 471990, 0, 475015, 60, 480726, 0, 483751, 60, 489462, 0, 492487, 60, 498198, 0, 501391, 60, 507102, 0, 510127, 60, 515838, 0, 518863, 60, 524574, 0, 527599, 60, 533310, 0, 536335, 60, 542046, 0, 545239, 60, 550950, 0, 553975, 60, 559686, 0, 562711, 60, 568422, 0, 571447, 60, 577158, 0, 580183, 60, 585894, 0, 588919, 60, 594630, 0], "names": ["EST", "Eastern Standard Time", "EDT", "Eastern Daylight Time"], "std_offset": -300}
americaDominica = {"id": "America/Dominica", "transitions": [], "names": ["AST", "Atlantic Standard Time", "ADT", "Atlantic Daylight Time"], "std_offset": -240}
africaBujumbura = {"id": "Africa/Bujumbura", "transitions": [], "names": ["CAT", "Central Africa Time"], "std_offset": 120}
pacificGalapagos = {"id": "Pacific/Galapagos", "transitions": [], "names": ["GALT", "Galapagos Time"], "std_offset": -360}
asiaVladivostok = {"id": "Asia/Vladivostok", "transitions": [98582, 60, 102973, 0, 107342, 60, 111733, 0, 116102, 60, 120493, 0, 124886, 60, 129256, 0, 133624, 60, 137992, 0, 142360, 60, 146728, 0, 151096, 60, 155464, 0, 159832, 60, 164200, 0, 168568, 60, 172936, 0, 177304, 60, 181840, 0, 186208, 60, 190577, 0, 194941, 60, 199308, 0, 203680, 60, 208048, 0, 212416, 60, 216784, 0, 221152, 60, 225520, 0, 230056, 60, 235096, 0, 238792, 60, 243832, 0, 247528, 60, 252568, 0, 256264, 60, 261472, 0, 265000, 60, 270208, 0, 273736, 60, 278944, 0, 282640, 60, 287680, 0, 291376, 60, 296416, 0, 300112, 60, 305320, 0, 308848, 60, 314056, 0, 317584, 60, 322792, 0, 326320, 60, 331528, 0, 335224, 60, 340264, 0, 343960, 60, 349000, 0, 352696, 60, 357904, 0], "names": ["VLAT", "Vladivostok Standard Time", "VLAST", "Vladivostok Summer Time"], "std_offset": 660}
americaStVincent = {"id": "America/St_Vincent", "transitions": [], "names": ["AST", "Atlantic Standard Time", "ADT", "Atlantic Daylight Time"], "std_offset": -240}
europeSofia = {"id": "Europe/Sofia", "transitions": [81045, 60, 85438, 0, 89949, 60, 94174, 0, 98685, 60, 102887, 0, 107421, 60, 111624, 0, 115992, 60, 120360, 0, 124728, 60, 129264, 0, 133632, 60, 138000, 0, 142368, 60, 146736, 0, 151104, 60, 155472, 0, 159840, 60, 164208, 0, 168576, 60, 172944, 0, 177312, 60, 181848, 0, 186214, 60, 190581, 0, 194950, 60, 199317, 0, 203686, 60, 208053, 0, 212422, 60, 216789, 0, 221158, 60, 225525, 0, 230062, 60, 235101, 0, 238801, 60, 243841, 0, 247537, 60, 252577, 0, 256273, 60, 261481, 0, 265009, 60, 270217, 0, 273745, 60, 278953, 0, 282649, 60, 287689, 0, 291385, 60, 296425, 0, 300121, 60, 305329, 0, 308857, 60, 314065, 0, 317593, 60, 322801, 0, 326329, 60, 331537, 0, 335233, 60, 340273, 0, 343969, 60, 349009, 0, 352705, 60, 357913, 0, 361441, 60, 366649, 0, 370177, 60, 375385, 0, 379081, 60, 384121, 0, 387817, 60, 392857, 0, 396553, 60, 401593, 0, 405289, 60, 410497, 0, 414025, 60, 419233, 0, 422761, 60, 427969, 0, 431665, 60, 436705, 0, 440401, 60, 445441, 0, 449137, 60, 454345, 0, 457873, 60, 463081, 0, 466609, 60, 471817, 0, 475513, 60, 480553, 0, 484249, 60, 489289, 0, 492985, 60, 498025, 0, 501721, 60, 506929, 0, 510457, 60, 515665, 0, 519193, 60, 524401, 0, 528097, 60, 533137, 0, 536833, 60, 541873, 0, 545569, 60, 550777, 0, 554305, 60, 559513, 0, 563041, 60, 568249, 0, 571777, 60, 576985, 0, 580681, 60, 585721, 0, 589417, 60, 594457, 0], "names": ["EET", "Eastern European Standard Time", "EEST", "Eastern European Summer Time"], "std_offset": 120}
americaAntigua = {"id": "America/Antigua", "transitions": [], "names": ["AST", "Atlantic Standard Time", "ADT", "Atlantic Daylight Time"], "std_offset": -240}
asiaBaku = {"id": "Asia/Baku", "transitions": [98588, 60, 102979, 0, 107348, 60, 111739, 0, 116108, 60, 120499, 0, 124892, 60, 129262, 0, 133630, 60, 137998, 0, 142366, 60, 146734, 0, 151102, 60, 155470, 0, 159838, 60, 164206, 0, 168574, 60, 172942, 0, 177310, 60, 181846, 0, 186214, 60, 190583, 0, 194948, 60, 199315, 0, 230065, 60, 235105, 0, 238800, 60, 243840, 0, 247536, 60, 252576, 0, 256272, 60, 261480, 0, 265008, 60, 270216, 0, 273744, 60, 278952, 0, 282648, 60, 287688, 0, 291384, 60, 296424, 0, 300120, 60, 305328, 0, 308856, 60, 314064, 0, 317592, 60, 322800, 0, 326328, 60, 331536, 0, 335232, 60, 340272, 0, 343968, 60, 349008, 0, 352704, 60, 357912, 0, 361440, 60, 366648, 0, 370176, 60, 375384, 0, 379080, 60, 384120, 0, 387816, 60, 392856, 0, 396552, 60, 401592, 0, 405288, 60, 410496, 0, 414024, 60, 419232, 0, 422760, 60, 427968, 0, 431664, 60, 436704, 0, 440400, 60, 445440, 0, 449136, 60, 454344, 0, 457872, 60, 463080, 0, 466608, 60, 471816, 0, 475512, 60, 480552, 0, 484248, 60, 489288, 0, 492984, 60, 498024, 0, 501720, 60, 506928, 0, 510456, 60, 515664, 0, 519192, 60, 524400, 0, 528096, 60, 533136, 0, 536832, 60, 541872, 0, 545568, 60, 550776, 0, 554304, 60, 559512, 0, 563040, 60, 568248, 0, 571776, 60, 576984, 0, 580680, 60, 585720, 0, 589416, 60, 594456, 0], "names": ["AZT", "Azerbaijan Standard Time", "AZST", "Azerbaijan Summer Time"], "std_offset": 240}
americaAsuncion = {"id": "America/Asuncion", "transitions": [50380, 60, 54027, 0, 59164, 60, 62787, 0, 67924, 60, 71547, 0, 76684, 60, 81051, 0, 85444, 60, 89835, 0, 94228, 60, 98595, 0, 102988, 60, 107355, 0, 111748, 60, 116115, 0, 120508, 60, 124899, 0, 129292, 60, 133659, 0, 138052, 60, 142419, 0, 146812, 60, 151179, 0, 155572, 60, 159963, 0, 164356, 60, 168723, 0, 173620, 60, 177483, 0, 181876, 60, 186243, 0, 190756, 60, 194283, 0, 199516, 60, 203763, 0, 208180, 60, 211755, 0, 216940, 60, 220491, 0, 225700, 60, 229347, 0, 234604, 60, 237963, 0, 243340, 60, 246867, 0, 252076, 60, 255771, 0, 260812, 60, 264507, 0, 269548, 60, 273243, 0, 278452, 60, 282819, 0, 286348, 60, 291555, 0, 295252, 60, 300291, 0, 304996, 60, 308523, 0, 313732, 60, 317259, 0, 322468, 60, 325995, 0, 331372, 60, 334731, 0, 340108, 60, 343467, 0, 348844, 60, 353043, 0, 357244, 60, 361779, 0, 365980, 60, 370515, 0, 374884, 60, 378915, 0, 383620, 60, 388155, 0, 392356, 60, 396891, 0, 401092, 60, 405627, 0, 409828, 60, 414363, 0, 418564, 60, 423099, 0, 427468, 60, 432003, 0, 436204, 60, 440739, 0, 444940, 60, 449475, 0, 453676, 60, 458211, 0, 462412, 60, 466947, 0, 471148, 60, 475851, 0, 480052, 60, 484587, 0, 488788, 60, 493323, 0, 497524, 60, 502059, 0, 506260, 60, 510795, 0, 514996, 60, 519531, 0, 523900, 60, 528435, 0, 532636, 60, 537171, 0, 541372, 60, 545907, 0, 550108, 60, 554643, 0, 558844, 60, 563379, 0, 567580, 60, 572115, 0, 576484, 60, 581019, 0, 585220, 60, 589755, 0, 593956, 60], "names": ["PYT", "Paraguay Standard Time", "PYST", "Paraguay Summer Time"], "std_offset": -240}
indianKerguelen = {"id": "Indian/Kerguelen", "transitions": [], "names": ["TFT", "French Southern and Antarctic Time"], "std_offset": 300}
europeLisbon = {"id": "Europe/Lisbon", "transitions": [63408, 60, 67776, 0, 72312, 60, 76680, 0, 81048, 60, 85417, 0, 89784, 60, 94153, 0, 98521, 60, 102889, 0, 107257, 60, 111625, 0, 115994, 60, 120361, 0, 124729, 60, 129265, 0, 133633, 60, 138001, 0, 142369, 60, 146737, 0, 151105, 60, 155473, 0, 159841, 60, 164209, 0, 168577, 60, 172945, 0, 177313, 60, 181849, 0, 186217, 60, 190585, 0, 194953, 60, 199321, 0, 203689, 60, 208057, 0, 212425, 60, 216793, 0, 221161, 60, 225529, 0, 230065, 60, 235105, 0, 238801, 60, 243841, 0, 247537, 60, 252577, 0, 256273, 60, 261481, 0, 265009, 60, 270217, 0, 273745, 60, 278953, 0, 282649, 60, 287689, 0, 291385, 60, 296425, 0, 300121, 60, 305329, 0, 308857, 60, 314065, 0, 317593, 60, 322801, 0, 326329, 60, 331537, 0, 335233, 60, 340273, 0, 343969, 60, 349009, 0, 352705, 60, 357913, 0, 361441, 60, 366649, 0, 370177, 60, 375385, 0, 379081, 60, 384121, 0, 387817, 60, 392857, 0, 396553, 60, 401593, 0, 405289, 60, 410497, 0, 414025, 60, 419233, 0, 422761, 60, 427969, 0, 431665, 60, 436705, 0, 440401, 60, 445441, 0, 449137, 60, 454345, 0, 457873, 60, 463081, 0, 466609, 60, 471817, 0, 475513, 60, 480553, 0, 484249, 60, 489289, 0, 492985, 60, 498025, 0, 501721, 60, 506929, 0, 510457, 60, 515665, 0, 519193, 60, 524401, 0, 528097, 60, 533137, 0, 536833, 60, 541873, 0, 545569, 60, 550777, 0, 554305, 60, 559513, 0, 563041, 60, 568249, 0, 571777, 60, 576985, 0, 580681, 60, 585721, 0, 589417, 60, 594457, 0], "names": ["WET", "Western European Standard Time", "WEST", "Western European Summer Time"], "std_offset": 0}
europeVatican = {"id": "Europe/Vatican", "transitions": [3599, 60, 6454, 0, 12167, 60, 15191, 0, 21071, 60, 24094, 0, 29975, 60, 32830, 0, 38543, 60, 41566, 0, 47447, 60, 50303, 0, 56183, 60, 59039, 0, 64751, 60, 67775, 0, 73655, 60, 76679, 0, 82391, 60, 85415, 0, 89953, 60, 94153, 0, 98521, 60, 102889, 0, 107257, 60, 111625, 0, 115993, 60, 120361, 0, 124729, 60, 129265, 0, 133633, 60, 138001, 0, 142369, 60, 146737, 0, 151105, 60, 155473, 0, 159841, 60, 164209, 0, 168577, 60, 172945, 0, 177313, 60, 181849, 0, 186217, 60, 190585, 0, 194953, 60, 199321, 0, 203689, 60, 208057, 0, 212425, 60, 216793, 0, 221161, 60, 225529, 0, 230065, 60, 235105, 0, 238801, 60, 243841, 0, 247537, 60, 252577, 0, 256273, 60, 261481, 0, 265009, 60, 270217, 0, 273745, 60, 278953, 0, 282649, 60, 287689, 0, 291385, 60, 296425, 0, 300121, 60, 305329, 0, 308857, 60, 314065, 0, 317593, 60, 322801, 0, 326329, 60, 331537, 0, 335233, 60, 340273, 0, 343969, 60, 349009, 0, 352705, 60, 357913, 0, 361441, 60, 366649, 0, 370177, 60, 375385, 0, 379081, 60, 384121, 0, 387817, 60, 392857, 0, 396553, 60, 401593, 0, 405289, 60, 410497, 0, 414025, 60, 419233, 0, 422761, 60, 427969, 0, 431665, 60, 436705, 0, 440401, 60, 445441, 0, 449137, 60, 454345, 0, 457873, 60, 463081, 0, 466609, 60, 471817, 0, 475513, 60, 480553, 0, 484249, 60, 489289, 0, 492985, 60, 498025, 0, 501721, 60, 506929, 0, 510457, 60, 515665, 0, 519193, 60, 524401, 0, 528097, 60, 533137, 0, 536833, 60, 541873, 0, 545569, 60, 550777, 0, 554305, 60, 559513, 0, 563041, 60, 568249, 0, 571777, 60, 576985, 0, 580681, 60, 585721, 0, 589417, 60, 594457, 0], "names": ["CET", "Central European Standard Time", "CEST", "Central European Summer Time"], "std_offset": 60}
africaBangui = {"id": "Africa/Bangui", "transitions": [], "names": ["WAT", "West Africa Standard Time", "WAST", "West Africa Summer Time"], "std_offset": 60}
americaEirunepe = {"id": "America/Eirunepe", "transitions": [138821, 60, 142012, 0, 147389, 60, 150076, 0, 156149, 60, 158668, 0, 208565, 60, 211588, 0], "names": ["AMT", "Acre Standard Time", "AMST", "Acre Summer Time"], "std_offset": -240}
australiaSydney = {"id": "Australia/Sydney", "transitions": [16024, 60, 18880, 0, 24760, 60, 27784, 0, 33496, 60, 36520, 0, 42232, 60, 45256, 0, 50968, 60, 54160, 0, 59872, 60, 62896, 0, 68608, 60, 71632, 0, 77344, 60, 80368, 0, 86080, 60, 89104, 0, 94816, 60, 97840, 0, 103552, 60, 107416, 0, 112456, 60, 115480, 0, 121192, 60, 124216, 0, 129928, 60, 132952, 0, 138664, 60, 142024, 0, 147232, 60, 150760, 0, 156136, 60, 159664, 0, 165040, 60, 168400, 0, 173776, 60, 176800, 0, 182512, 60, 185536, 0, 191248, 60, 194272, 0, 199984, 60, 203176, 0, 208888, 60, 211912, 0, 217624, 60, 220648, 0, 226360, 60, 230056, 0, 235096, 60, 238792, 0, 243832, 60, 247528, 0, 252568, 60, 256264, 0, 261472, 60, 265000, 0, 268696, 60, 273736, 0, 278944, 60, 282640, 0, 287680, 60, 291376, 0, 296416, 60, 300112, 0, 305320, 60, 308848, 0, 314056, 60, 317752, 0, 322792, 60, 326320, 0, 331528, 60, 335392, 0, 339760, 60, 344128, 0, 348496, 60, 352864, 0, 357232, 60, 361600, 0, 365968, 60, 370336, 0, 374872, 60, 379240, 0, 383608, 60, 387976, 0, 392344, 60, 396712, 0, 401080, 60, 405448, 0, 409816, 60, 414184, 0, 418552, 60, 422920, 0, 427456, 60, 431824, 0, 436192, 60, 440560, 0, 444928, 60, 449296, 0, 453664, 60, 458032, 0, 462400, 60, 466768, 0, 471136, 60, 475672, 0, 480040, 60, 484408, 0, 488776, 60, 493144, 0, 497512, 60, 501880, 0, 506248, 60, 510616, 0, 514984, 60, 519352, 0, 523888, 60, 528256, 0, 532624, 60, 536992, 0, 541360, 60, 545728, 0, 550096, 60, 554464, 0, 558832, 60, 563200, 0, 567568, 60, 571936, 0, 576472, 60, 580840, 0, 585208, 60, 589576, 0, 593944, 60], "names": ["AEST", "Australian Eastern Standard Time", "AEDT", "Australian Eastern Daylight Time"], "std_offset": 600}
europeSimferopol = {"id": "Europe/Simferopol", "transitions": [98589, 60, 102980, 0, 107349, 60, 111740, 0, 116109, 60, 120500, 0, 124893, 60, 129263, 0, 133631, 60, 137999, 0, 142367, 60, 146735, 0, 151103, 60, 155471, 0, 159839, 60, 164207, 0, 168575, 60, 172943, 0, 194950, 60, 199317, 0, 203686, 60, 208053, 0, 212422, 60, 216788, 0, 221157, 60, 225524, 0, 230061, 60, 235104, 0, 238801, 60, 243841, 0, 247537, 60, 252577, 0, 256273, 60, 261481, 0, 265009, 60, 270217, 0, 273745, 60, 278953, 0, 282649, 60, 287689, 0, 291385, 60, 296425, 0, 300121, 60, 305329, 0, 308857, 60, 314065, 0, 317593, 60, 322801, 0, 326329, 60, 331537, 0, 335233, 60, 340273, 0, 343969, 60, 349009, 0, 352705, 60, 357913, 0, 361441, 60, 366649, 0, 370177, 60, 375385, 0, 379081, 60, 384121, 0, 387817, 60, 392857, 0, 396553, 60, 401593, 0, 405289, 60, 410497, 0, 414025, 60, 419233, 0, 422761, 60, 427969, 0, 431665, 60, 436705, 0, 440401, 60, 445441, 0, 449137, 60, 454345, 0, 457873, 60, 463081, 0, 466609, 60, 471817, 0, 475513, 60, 480553, 0, 484249, 60, 489289, 0, 492985, 60, 498025, 0, 501721, 60, 506929, 0, 510457, 60, 515665, 0, 519193, 60, 524401, 0, 528097, 60, 533137, 0, 536833, 60, 541873, 0, 545569, 60, 550777, 0, 554305, 60, 559513, 0, 563041, 60, 568249, 0, 571777, 60, 576985, 0, 580681, 60, 585721, 0, 589417, 60, 594457, 0], "names": ["EET", "Moscow Standard Time", "EEST", "Moscow Summer Time"], "std_offset": 120}
pacificEaster = {"id": "Pacific/Easter", "transitions": [6796, 60, 10491, 0, 15532, 60, 19227, 0, 24436, 60, 27963, 0, 32836, 60, 36699, 0, 41908, 60, 45435, 0, 50644, 60, 54339, 0, 59380, 60, 63075, 0, 68116, 60, 71811, 0, 77020, 60, 80547, 0, 85756, 60, 89283, 0, 94492, 60, 98187, 0, 103228, 60, 106923, 0, 111964, 60, 115659, 0, 120700, 60, 124395, 0, 129604, 60, 133131, 0, 138340, 60, 141867, 0, 147076, 60, 151443, 0, 155812, 60, 159507, 0, 164380, 60, 168243, 0, 173452, 60, 177147, 0, 181516, 60, 185715, 0, 190924, 60, 194619, 0, 199660, 60, 203355, 0, 208396, 60, 212091, 0, 217132, 60, 220827, 0, 226036, 60, 229563, 0, 234772, 60, 238803, 0, 243508, 60, 247203, 0, 251908, 60, 256443, 0, 260980, 60, 264675, 0, 269884, 60, 273411, 0, 278620, 60, 282147, 0, 287356, 60, 290883, 0, 296092, 60, 299787, 0, 304828, 60, 308523, 0, 313564, 60, 317259, 0, 322468, 60, 325995, 0, 331204, 60, 335235, 0, 339940, 60, 343635, 0, 348676, 60, 352875, 0, 357412, 60, 362451, 0, 364972, 60, 371019, 0, 374044, 60, 379755, 0, 382948, 60, 388491, 0, 391684, 60, 397227, 0, 400420, 60, 405963, 0, 409156, 60, 414699, 0, 417892, 60, 423603, 0, 426628, 60, 432339, 0, 435532, 60, 441075, 0, 444268, 60, 449811, 0, 453004, 60, 458547, 0, 461740, 60, 467283, 0, 470476, 60, 476187, 0, 479380, 60, 484923, 0, 488116, 60, 493659, 0, 496852, 60, 502395, 0, 505588, 60, 511131, 0, 514324, 60, 520035, 0, 523060, 60, 528771, 0, 531964, 60, 537507, 0, 540700, 60, 546243, 0, 549436, 60, 554979, 0, 558172, 60, 563715, 0, 566908, 60, 572619, 0, 575644, 60, 581355, 0, 584548, 60, 590091, 0, 593284, 60], "names": ["EAST", "Easter Island Standard Time", "EASST", "Easter Island Summer Time"], "std_offset": -360}
antarcticaPalmer = {"id": "Antarctica/Palmer", "transitions": [35595, 60, 37946, 0, 111964, 60, 115659, 0, 120700, 60, 124395, 0, 129604, 60, 133131, 0, 138340, 60, 141867, 0, 147076, 60, 151443, 0, 155812, 60, 159507, 0, 164380, 60, 168243, 0, 173452, 60, 177147, 0, 181516, 60, 185715, 0, 190924, 60, 194619, 0, 199660, 60, 203355, 0, 208396, 60, 212091, 0, 217132, 60, 220827, 0, 226036, 60, 229563, 0, 234772, 60, 238803, 0, 243508, 60, 247203, 0, 251908, 60, 256443, 0, 260980, 60, 264675, 0, 269884, 60, 273411, 0, 278620, 60, 282147, 0, 287356, 60, 290883, 0, 296092, 60, 299787, 0, 304828, 60, 308523, 0, 313564, 60, 317259, 0, 322468, 60, 325995, 0, 331204, 60, 335235, 0, 339940, 60, 343635, 0, 348676, 60, 352875, 0, 357412, 60, 362451, 0, 364972, 60, 371019, 0, 374044, 60, 379755, 0, 382948, 60, 388491, 0, 391684, 60, 397227, 0, 400420, 60, 405963, 0, 409156, 60, 414699, 0, 417892, 60, 423603, 0, 426628, 60, 432339, 0, 435532, 60, 441075, 0, 444268, 60, 449811, 0, 453004, 60, 458547, 0, 461740, 60, 467283, 0, 470476, 60, 476187, 0, 479380, 60, 484923, 0, 488116, 60, 493659, 0, 496852, 60, 502395, 0, 505588, 60, 511131, 0, 514324, 60, 520035, 0, 523060, 60, 528771, 0, 531964, 60, 537507, 0, 540700, 60, 546243, 0, 549436, 60, 554979, 0, 558172, 60, 563715, 0, 566908, 60, 572619, 0, 575644, 60, 581355, 0, 584548, 60, 590091, 0, 593284, 60], "names": ["CLT", "Chile Standard Time", "CLST", "Chile Summer Time"], "std_offset": -240}
americaTijuana = {"id": "America/Tijuana", "transitions": [55354, 60, 59889, 0, 64090, 60, 68625, 0, 72994, 60, 77361, 0, 81730, 60, 86097, 0, 90466, 60, 94833, 0, 99202, 60, 103569, 0, 107938, 60, 112473, 0, 116674, 60, 121209, 0, 125578, 60, 129945, 0, 134314, 60, 138681, 0, 143050, 60, 147417, 0, 151282, 60, 156153, 0, 160018, 60, 165057, 0, 168754, 60, 173793, 0, 177490, 60, 182529, 0, 186394, 60, 191265, 0, 195130, 60, 200001, 0, 203866, 60, 208905, 0, 212602, 60, 217641, 0, 221338, 60, 226377, 0, 230242, 60, 235113, 0, 238978, 60, 243849, 0, 247714, 60, 252585, 0, 256450, 60, 261489, 0, 265186, 60, 270225, 0, 273922, 60, 278961, 0, 282826, 60, 287697, 0, 291562, 60, 296433, 0, 300298, 60, 305337, 0, 309034, 60, 314073, 0, 317770, 60, 322809, 0, 326506, 60, 331545, 0, 335410, 60, 340281, 0, 344146, 60, 349017, 0, 352378, 60, 358089, 0, 361114, 60, 366825, 0, 369850, 60, 375561, 0, 378586, 60, 384297, 0, 387322, 60, 393033, 0, 396058, 60, 401769, 0, 404962, 60, 410673, 0, 413698, 60, 419409, 0, 422434, 60, 428145, 0, 431170, 60, 436881, 0, 439906, 60, 445617, 0, 448810, 60, 454521, 0, 457546, 60, 463257, 0, 466282, 60, 471993, 0, 475018, 60, 480729, 0, 483754, 60, 489465, 0, 492490, 60, 498201, 0, 501394, 60, 507105, 0, 510130, 60, 515841, 0, 518866, 60, 524577, 0, 527602, 60, 533313, 0, 536338, 60, 542049, 0, 545242, 60, 550953, 0, 553978, 60, 559689, 0, 562714, 60, 568425, 0, 571450, 60, 577161, 0, 580186, 60, 585897, 0, 588922, 60, 594633, 0], "names": ["PST", "Pacific Standard Time", "PDT", "Pacific Daylight Time"], "std_offset": -480}
asiaMacau = {"id": "Asia/Macau", "transitions": [2587, 60, 6954, 0, 11323, 60, 15690, 0, 20056, 60, 24423, 0, 28792, 60, 33327, 0, 37696, 60, 42066, 0, 46435, 60, 50802, 0, 55171, 60, 59538, 0, 63907, 60, 68274, 0, 72640, 60, 77007, 0, 81376, 60, 85911, 0, 90280, 60, 94647, 0], "names": ["CST", "China Standard Time", "CDT", "China Daylight Time"], "std_offset": 480}
americaCoralHarbour = {"id": "America/Coral_Harbour", "transitions": [], "names": ["EST", "Eastern Standard Time", "EDT", "Eastern Daylight Time"], "std_offset": -300}
europeAthens = {"id": "Europe/Athens", "transitions": [46246, 60, 51718, 0, 55008, 60, 59376, 0, 63576, 60, 67800, 0, 72312, 60, 76513, 0, 81055, 60, 85391, 0, 89830, 60, 94149, 0, 98521, 60, 102889, 0, 107257, 60, 111625, 0, 115993, 60, 120361, 0, 124729, 60, 129265, 0, 133633, 60, 138001, 0, 142369, 60, 146737, 0, 151105, 60, 155473, 0, 159841, 60, 164209, 0, 168577, 60, 172945, 0, 177313, 60, 181849, 0, 186217, 60, 190585, 0, 194953, 60, 199321, 0, 203689, 60, 208057, 0, 212425, 60, 216793, 0, 221161, 60, 225529, 0, 230065, 60, 235105, 0, 238801, 60, 243841, 0, 247537, 60, 252577, 0, 256273, 60, 261481, 0, 265009, 60, 270217, 0, 273745, 60, 278953, 0, 282649, 60, 287689, 0, 291385, 60, 296425, 0, 300121, 60, 305329, 0, 308857, 60, 314065, 0, 317593, 60, 322801, 0, 326329, 60, 331537, 0, 335233, 60, 340273, 0, 343969, 60, 349009, 0, 352705, 60, 357913, 0, 361441, 60, 366649, 0, 370177, 60, 375385, 0, 379081, 60, 384121, 0, 387817, 60, 392857, 0, 396553, 60, 401593, 0, 405289, 60, 410497, 0, 414025, 60, 419233, 0, 422761, 60, 427969, 0, 431665, 60, 436705, 0, 440401, 60, 445441, 0, 449137, 60, 454345, 0, 457873, 60, 463081, 0, 466609, 60, 471817, 0, 475513, 60, 480553, 0, 484249, 60, 489289, 0, 492985, 60, 498025, 0, 501721, 60, 506929, 0, 510457, 60, 515665, 0, 519193, 60, 524401, 0, 528097, 60, 533137, 0, 536833, 60, 541873, 0, 545569, 60, 550777, 0, 554305, 60, 559513, 0, 563041, 60, 568249, 0, 571777, 60, 576985, 0, 580681, 60, 585721, 0, 589417, 60, 594457, 0], "names": ["EET", "Eastern European Standard Time", "EEST", "Eastern European Summer Time"], "std_offset": 120}
americaDawsonCreek = {"id": "America/Dawson_Creek", "transitions": [2770, 60, 7137, 0, 11506, 60, 16041, 0, 20410, 60, 23337, 0], "names": ["MST", "Mountain Standard Time", "MDT", "Mountain Daylight Time"], "std_offset": -420}
americaIndianaTellCity = {"id": "America/Indiana/Tell_City", "transitions": [2767, 60, 7134, 0, 317767, 60, 322807, 0, 326000, 60, 331711, 0, 334736, 60, 340447, 0, 343472, 60, 349183, 0, 352376, 60, 358087, 0, 361112, 60, 366823, 0, 369848, 60, 375559, 0, 378584, 60, 384295, 0, 387320, 60, 393031, 0, 396056, 60, 401767, 0, 404960, 60, 410671, 0, 413696, 60, 419407, 0, 422432, 60, 428143, 0, 431168, 60, 436879, 0, 439904, 60, 445615, 0, 448808, 60, 454519, 0, 457544, 60, 463255, 0, 466280, 60, 471991, 0, 475016, 60, 480727, 0, 483752, 60, 489463, 0, 492488, 60, 498199, 0, 501392, 60, 507103, 0, 510128, 60, 515839, 0, 518864, 60, 524575, 0, 527600, 60, 533311, 0, 536336, 60, 542047, 0, 545240, 60, 550951, 0, 553976, 60, 559687, 0, 562712, 60, 568423, 0, 571448, 60, 577159, 0, 580184, 60, 585895, 0, 588920, 60, 594631, 0], "names": ["CST", "Central Standard Time", "CDT", "Central Daylight Time"], "std_offset": -360}
pacificTruk = {"id": "Pacific/Truk", "transitions": [], "names": ["TRUT", "Chuuk Time"], "std_offset": 600}
pacificMarquesas = {"id": "Pacific/Marquesas", "transitions": [], "names": ["MART", "Marquesas Time"], "std_offset": -570}
asiaAmman = {"id": "Asia/Amman", "transitions": [30046, 60, 32853, 0, 37942, 60, 41613, 0, 46702, 60, 50373, 0, 55486, 60, 59901, 0, 64246, 60, 67917, 0, 72982, 60, 76653, 0, 133654, 60, 138045, 0, 142486, 60, 146853, 0, 151222, 60, 155589, 0, 159958, 60, 164493, 0, 169606, 60, 173229, 0, 178102, 60, 181965, 0, 186622, 60, 190533, 0, 195238, 60, 199437, 0, 203806, 60, 208173, 0, 212542, 60, 216573, 0, 221446, 60, 225310, 0, 230182, 60, 234214, 0, 238918, 60, 242950, 0, 247654, 60, 251686, 0, 258550, 60, 260590, 0, 265102, 60, 269494, 0, 273838, 60, 278230, 0, 282598, 60, 286966, 0, 291334, 60, 296374, 0, 300070, 60, 304942, 0, 308974, 60, 313342, 0, 317710, 60, 322750, 0, 326446, 60, 331486, 0, 335182, 60, 340390, 0, 343918, 60, 349126, 0, 352654, 60, 357862, 0, 361558, 60, 366598, 0, 370294, 60, 384070, 0, 387766, 60, 392974, 0, 396502, 60, 401710, 0, 405406, 60, 410446, 0, 414142, 60, 419182, 0, 422878, 60, 427918, 0, 431614, 60, 436654, 0, 440350, 60, 445558, 0, 449086, 60, 454294, 0, 457990, 60, 463030, 0, 466726, 60, 471766, 0, 475462, 60, 480502, 0, 484198, 60, 489406, 0, 492934, 60, 498142, 0, 501670, 60, 506878, 0, 510574, 60, 515614, 0, 519310, 60, 524350, 0, 528046, 60, 533086, 0, 536782, 60, 541990, 0, 545518, 60, 550726, 0, 554422, 60, 559462, 0, 563158, 60, 568198, 0, 571894, 60, 576934, 0, 580630, 60, 585838, 0, 589366, 60, 594574, 0], "names": ["EET", "Eastern European Standard Time", "EEST", "Eastern European Summer Time"], "std_offset": 120}
atlanticCapeVerde = {"id": "Atlantic/Cape_Verde", "transitions": [], "names": ["CVT", "Cape Verde Standard Time", "CVST", "Cape Verde Summer Time"], "std_offset": -60}
europeBratislava = {"id": "Europe/Bratislava", "transitions": [81049, 60, 85417, 0, 89953, 60, 94153, 0, 98521, 60, 102889, 0, 107257, 60, 111625, 0, 115993, 60, 120361, 0, 124729, 60, 129265, 0, 133633, 60, 138001, 0, 142369, 60, 146737, 0, 151105, 60, 155473, 0, 159841, 60, 164209, 0, 168577, 60, 172945, 0, 177313, 60, 181849, 0, 186217, 60, 190585, 0, 194953, 60, 199321, 0, 203689, 60, 208057, 0, 212425, 60, 216793, 0, 221161, 60, 225529, 0, 230065, 60, 235105, 0, 238801, 60, 243841, 0, 247537, 60, 252577, 0, 256273, 60, 261481, 0, 265009, 60, 270217, 0, 273745, 60, 278953, 0, 282649, 60, 287689, 0, 291385, 60, 296425, 0, 300121, 60, 305329, 0, 308857, 60, 314065, 0, 317593, 60, 322801, 0, 326329, 60, 331537, 0, 335233, 60, 340273, 0, 343969, 60, 349009, 0, 352705, 60, 357913, 0, 361441, 60, 366649, 0, 370177, 60, 375385, 0, 379081, 60, 384121, 0, 387817, 60, 392857, 0, 396553, 60, 401593, 0, 405289, 60, 410497, 0, 414025, 60, 419233, 0, 422761, 60, 427969, 0, 431665, 60, 436705, 0, 440401, 60, 445441, 0, 449137, 60, 454345, 0, 457873, 60, 463081, 0, 466609, 60, 471817, 0, 475513, 60, 480553, 0, 484249, 60, 489289, 0, 492985, 60, 498025, 0, 501721, 60, 506929, 0, 510457, 60, 515665, 0, 519193, 60, 524401, 0, 528097, 60, 533137, 0, 536833, 60, 541873, 0, 545569, 60, 550777, 0, 554305, 60, 559513, 0, 563041, 60, 568249, 0, 571777, 60, 576985, 0, 580681, 60, 585721, 0, 589417, 60, 594457, 0], "names": ["CET", "Central European Standard Time", "CEST", "Central European Summer Time"], "std_offset": 60}
americaCuiaba = {"id": "America/Cuiaba", "transitions": [138820, 60, 142011, 0, 147388, 60, 150075, 0, 156148, 60, 158667, 0, 164716, 60, 167235, 0, 173452, 60, 176307, 0, 182356, 60, 185211, 0, 191092, 60, 193779, 0, 199996, 60, 202347, 0, 208564, 60, 211587, 0, 217300, 60, 220323, 0, 226036, 60, 228891, 0, 234604, 60, 237795, 0, 243364, 60, 246867, 0, 252244, 60, 255435, 0, 260812, 60, 264339, 0, 269716, 60, 272907, 0, 278620, 60, 281643, 0, 287860, 60, 290379, 0, 305380, 60, 308019, 0, 313732, 60, 316755, 0, 322972, 60, 325659, 0, 331204, 60, 334227, 0, 340108, 60, 342963, 0, 348844, 60, 351867, 0, 357580, 60, 360603, 0, 366316, 60, 369507, 0, 375220, 60, 378075, 0, 383956, 60, 386811, 0, 392692, 60, 395715, 0, 401428, 60, 404451, 0, 410164, 60, 413187, 0, 418900, 60, 421923, 0, 427804, 60, 430659, 0, 436540, 60, 439395, 0, 445276, 60, 448299, 0, 454012, 60, 457035, 0, 462748, 60, 465939, 0, 471484, 60, 474507, 0, 480388, 60, 483243, 0, 489124, 60, 492147, 0, 497860, 60, 500883, 0, 506596, 60, 509619, 0, 515332, 60, 518355, 0, 524236, 60, 527091, 0, 532972, 60, 535827, 0, 541708, 60, 544563, 0, 550444, 60, 553467, 0, 559180, 60, 562371, 0, 567916, 60, 570939, 0, 576820, 60, 579675, 0, 585556, 60, 588579, 0, 594292, 60], "names": ["AMT", "Amazon Standard Time", "AMST", "Amazon Summer Time"], "std_offset": -240}
africaSaoTome = {"id": "Africa/Sao_Tome", "transitions": [], "names": ["GMT", "Greenwich Mean Time"], "std_offset": 0}
atlanticCanary = {"id": "Atlantic/Canary", "transitions": [89952, 60, 94153, 0, 98521, 60, 102889, 0, 107257, 60, 111625, 0, 115993, 60, 120361, 0, 124729, 60, 129265, 0, 133633, 60, 138001, 0, 142369, 60, 146737, 0, 151105, 60, 155473, 0, 159841, 60, 164209, 0, 168577, 60, 172945, 0, 177313, 60, 181849, 0, 186217, 60, 190585, 0, 194953, 60, 199321, 0, 203689, 60, 208057, 0, 212425, 60, 216793, 0, 221161, 60, 225529, 0, 230065, 60, 235105, 0, 238801, 60, 243841, 0, 247537, 60, 252577, 0, 256273, 60, 261481, 0, 265009, 60, 270217, 0, 273745, 60, 278953, 0, 282649, 60, 287689, 0, 291385, 60, 296425, 0, 300121, 60, 305329, 0, 308857, 60, 314065, 0, 317593, 60, 322801, 0, 326329, 60, 331537, 0, 335233, 60, 340273, 0, 343969, 60, 349009, 0, 352705, 60, 357913, 0, 361441, 60, 366649, 0, 370177, 60, 375385, 0, 379081, 60, 384121, 0, 387817, 60, 392857, 0, 396553, 60, 401593, 0, 405289, 60, 410497, 0, 414025, 60, 419233, 0, 422761, 60, 427969, 0, 431665, 60, 436705, 0, 440401, 60, 445441, 0, 449137, 60, 454345, 0, 457873, 60, 463081, 0, 466609, 60, 471817, 0, 475513, 60, 480553, 0, 484249, 60, 489289, 0, 492985, 60, 498025, 0, 501721, 60, 506929, 0, 510457, 60, 515665, 0, 519193, 60, 524401, 0, 528097, 60, 533137, 0, 536833, 60, 541873, 0, 545569, 60, 550777, 0, 554305, 60, 559513, 0, 563041, 60, 568249, 0, 571777, 60, 576985, 0, 580681, 60, 585721, 0, 589417, 60, 594457, 0], "names": ["WET", "Western European Standard Time", "WEST", "Western European Summer Time"], "std_offset": 0}
europeLuxembourg = {"id": "Europe/Luxembourg", "transitions": [63577, 60, 67777, 0, 72313, 60, 76681, 0, 81049, 60, 85417, 0, 89953, 60, 94153, 0, 98521, 60, 102889, 0, 107257, 60, 111625, 0, 115993, 60, 120361, 0, 124729, 60, 129265, 0, 133633, 60, 138001, 0, 142369, 60, 146737, 0, 151105, 60, 155473, 0, 159841, 60, 164209, 0, 168577, 60, 172945, 0, 177313, 60, 181849, 0, 186217, 60, 190585, 0, 194953, 60, 199321, 0, 203689, 60, 208057, 0, 212425, 60, 216793, 0, 221161, 60, 225529, 0, 230065, 60, 235105, 0, 238801, 60, 243841, 0, 247537, 60, 252577, 0, 256273, 60, 261481, 0, 265009, 60, 270217, 0, 273745, 60, 278953, 0, 282649, 60, 287689, 0, 291385, 60, 296425, 0, 300121, 60, 305329, 0, 308857, 60, 314065, 0, 317593, 60, 322801, 0, 326329, 60, 331537, 0, 335233, 60, 340273, 0, 343969, 60, 349009, 0, 352705, 60, 357913, 0, 361441, 60, 366649, 0, 370177, 60, 375385, 0, 379081, 60, 384121, 0, 387817, 60, 392857, 0, 396553, 60, 401593, 0, 405289, 60, 410497, 0, 414025, 60, 419233, 0, 422761, 60, 427969, 0, 431665, 60, 436705, 0, 440401, 60, 445441, 0, 449137, 60, 454345, 0, 457873, 60, 463081, 0, 466609, 60, 471817, 0, 475513, 60, 480553, 0, 484249, 60, 489289, 0, 492985, 60, 498025, 0, 501721, 60, 506929, 0, 510457, 60, 515665, 0, 519193, 60, 524401, 0, 528097, 60, 533137, 0, 536833, 60, 541873, 0, 545569, 60, 550777, 0, 554305, 60, 559513, 0, 563041, 60, 568249, 0, 571777, 60, 576985, 0, 580681, 60, 585721, 0, 589417, 60, 594457, 0], "names": ["CET", "Central European Standard Time", "CEST", "Central European Summer Time"], "std_offset": 60}
africaKampala = {"id": "Africa/Kampala", "transitions": [], "names": ["EAT", "East Africa Time"], "std_offset": 180}
americaSitka = {"id": "America/Sitka", "transitions": [2770, 60, 7137, 0, 11506, 60, 16041, 0, 20410, 60, 24777, 0, 29146, 60, 33513, 0, 35194, 60, 42249, 0, 45106, 60, 50985, 0, 55354, 60, 59889, 0, 64090, 60, 68625, 0, 72994, 60, 77361, 0, 81730, 60, 86097, 0, 90466, 60, 94833, 0, 99202, 60, 103569, 0, 107938, 60, 112473, 0, 116674, 60, 121209, 0, 125579, 60, 129946, 0, 134315, 60, 138682, 0, 143051, 60, 147418, 0, 151283, 60, 156154, 0, 160019, 60, 165058, 0, 168755, 60, 173794, 0, 177491, 60, 182530, 0, 186395, 60, 191266, 0, 195131, 60, 200002, 0, 203867, 60, 208906, 0, 212603, 60, 217642, 0, 221339, 60, 226378, 0, 230243, 60, 235114, 0, 238979, 60, 243850, 0, 247715, 60, 252586, 0, 256451, 60, 261490, 0, 265187, 60, 270226, 0, 273923, 60, 278962, 0, 282827, 60, 287698, 0, 291563, 60, 296434, 0, 300299, 60, 305338, 0, 309035, 60, 314074, 0, 317771, 60, 322810, 0, 326003, 60, 331714, 0, 334739, 60, 340450, 0, 343475, 60, 349186, 0, 352379, 60, 358090, 0, 361115, 60, 366826, 0, 369851, 60, 375562, 0, 378587, 60, 384298, 0, 387323, 60, 393034, 0, 396059, 60, 401770, 0, 404963, 60, 410674, 0, 413699, 60, 419410, 0, 422435, 60, 428146, 0, 431171, 60, 436882, 0, 439907, 60, 445618, 0, 448811, 60, 454522, 0, 457547, 60, 463258, 0, 466283, 60, 471994, 0, 475019, 60, 480730, 0, 483755, 60, 489466, 0, 492491, 60, 498202, 0, 501395, 60, 507106, 0, 510131, 60, 515842, 0, 518867, 60, 524578, 0, 527603, 60, 533314, 0, 536339, 60, 542050, 0, 545243, 60, 550954, 0, 553979, 60, 559690, 0, 562715, 60, 568426, 0, 571451, 60, 577162, 0, 580187, 60, 585898, 0, 588923, 60, 594634, 0], "names": ["AKST", "Alaska Standard Time", "AKDT", "Alaska Daylight Time"], "std_offset": -540}
americaHavana = {"id": "America/Havana", "transitions": [2765, 60, 7132, 0, 11501, 60, 16036, 0, 20405, 60, 24268, 0, 29141, 60, 33028, 0, 37877, 60, 41788, 0, 46613, 60, 50980, 0, 55349, 60, 59884, 0, 64085, 60, 68620, 0, 73157, 60, 76852, 0, 80717, 60, 85756, 0, 89453, 60, 94492, 0, 99533, 60, 103228, 0, 108269, 60, 111964, 0, 117005, 60, 120700, 0, 125741, 60, 129604, 0, 134477, 60, 138340, 0, 142037, 60, 147076, 0, 150773, 60, 155812, 0, 159677, 60, 164548, 0, 168413, 60, 173284, 0, 177485, 60, 182188, 0, 186389, 60, 190925, 0, 195125, 60, 199661, 0, 203861, 60, 208397, 0, 212597, 60, 217133, 0, 221333, 60, 225869, 0, 230237, 60, 234605, 0, 238973, 60, 243509, 0, 247541, 60, 252581, 0, 256277, 60, 261485, 0, 265181, 60, 270221, 0, 273917, 60, 278957, 0, 282821, 60, 287693, 0, 291557, 60, 296429, 0, 300293, 60, 322805, 0, 325997, 60, 331541, 0, 334901, 60, 340277, 0, 343469, 60, 349013, 0, 352373, 60, 357917, 0, 361277, 60, 366989, 0, 370349, 60, 375557, 0, 378581, 60, 384293, 0, 387317, 60, 393029, 0, 396053, 60, 401765, 0, 404957, 60, 410669, 0, 413693, 60, 419405, 0, 422429, 60, 428141, 0, 431165, 60, 436877, 0, 439901, 60, 445613, 0, 448805, 60, 454517, 0, 457541, 60, 463253, 0, 466277, 60, 471989, 0, 475013, 60, 480725, 0, 483749, 60, 489461, 0, 492485, 60, 498197, 0, 501389, 60, 507101, 0, 510125, 60, 515837, 0, 518861, 60, 524573, 0, 527597, 60, 533309, 0, 536333, 60, 542045, 0, 545237, 60, 550949, 0, 553973, 60, 559685, 0, 562709, 60, 568421, 0, 571445, 60, 577157, 0, 580181, 60, 585893, 0, 588917, 60, 594629, 0], "names": ["CST", "Cuba Standard Time", "CDT", "Cuba Daylight Time"], "std_offset": -300}
americaGuayaquil = {"id": "America/Guayaquil", "transitions": [], "names": ["ECT", "Ecuador Time"], "std_offset": -300}
europeKiev = {"id": "Europe/Kiev", "transitions": [98589, 60, 102980, 0, 107349, 60, 111740, 0, 116109, 60, 120500, 0, 124893, 60, 129263, 0, 133631, 60, 137999, 0, 142367, 60, 146735, 0, 151103, 60, 155471, 0, 159839, 60, 164207, 0, 168575, 60, 172943, 0, 194950, 60, 199317, 0, 203686, 60, 208053, 0, 212422, 60, 216789, 0, 221161, 60, 225529, 0, 230065, 60, 235105, 0, 238801, 60, 243841, 0, 247537, 60, 252577, 0, 256273, 60, 261481, 0, 265009, 60, 270217, 0, 273745, 60, 278953, 0, 282649, 60, 287689, 0, 291385, 60, 296425, 0, 300121, 60, 305329, 0, 308857, 60, 314065, 0, 317593, 60, 322801, 0, 326329, 60, 331537, 0, 335233, 60, 340273, 0, 343969, 60, 349009, 0, 352705, 60, 357913, 0, 361441, 60, 366649, 0, 370177, 60, 375385, 0, 379081, 60, 384121, 0, 387817, 60, 392857, 0, 396553, 60, 401593, 0, 405289, 60, 410497, 0, 414025, 60, 419233, 0, 422761, 60, 427969, 0, 431665, 60, 436705, 0, 440401, 60, 445441, 0, 449137, 60, 454345, 0, 457873, 60, 463081, 0, 466609, 60, 471817, 0, 475513, 60, 480553, 0, 484249, 60, 489289, 0, 492985, 60, 498025, 0, 501721, 60, 506929, 0, 510457, 60, 515665, 0, 519193, 60, 524401, 0, 528097, 60, 533137, 0, 536833, 60, 541873, 0, 545569, 60, 550777, 0, 554305, 60, 559513, 0, 563041, 60, 568249, 0, 571777, 60, 576985, 0, 580681, 60, 585721, 0, 589417, 60, 594457, 0], "names": ["EET", "Eastern European Standard Time", "EEST", "Eastern European Summer Time"], "std_offset": 120}
asiaAqtobe = {"id": "Asia/Aqtobe", "transitions": [98587, 60, 102978, 0, 107346, 60, 111738, 0, 116107, 60, 120498, 0, 124891, 60, 129261, 0, 133629, 60, 137997, 0, 142365, 60, 146733, 0, 151101, 60, 155469, 0, 159837, 60, 164205, 0, 168573, 60, 172941, 0, 177309, 60, 181845, 0, 194946, 60, 199313, 0, 203685, 60, 208053, 0, 212421, 60, 216789, 0, 221157, 60, 225525, 0, 230061, 60, 235101, 0, 238797, 60, 243837, 0, 247533, 60, 252573, 0, 256269, 60, 261477, 0, 265005, 60, 270213, 0, 273741, 60, 278949, 0, 282645, 60, 287685, 0, 291381, 60, 296421, 0, 300117, 60, 305325, 0], "names": ["WKST", "West Kazakhstan Time"], "std_offset": 300}
americaCreston = {"id": "America/Creston", "transitions": [], "names": ["MST", "Mountain Standard Time", "MDT", "Mountain Daylight Time"], "std_offset": -420}
americaYellowknife = {"id": "America/Yellowknife", "transitions": [90465, 60, 94832, 0, 99201, 60, 103568, 0, 107937, 60, 112472, 0, 116673, 60, 121208, 0, 125577, 60, 129944, 0, 134313, 60, 138680, 0, 143049, 60, 147416, 0, 151281, 60, 156152, 0, 160017, 60, 165056, 0, 168753, 60, 173792, 0, 177489, 60, 182528, 0, 186393, 60, 191264, 0, 195129, 60, 200000, 0, 203865, 60, 208904, 0, 212601, 60, 217640, 0, 221337, 60, 226376, 0, 230241, 60, 235112, 0, 238977, 60, 243848, 0, 247713, 60, 252584, 0, 256449, 60, 261488, 0, 265185, 60, 270224, 0, 273921, 60, 278960, 0, 282825, 60, 287696, 0, 291561, 60, 296432, 0, 300297, 60, 305336, 0, 309033, 60, 314072, 0, 317769, 60, 322808, 0, 326001, 60, 331712, 0, 334737, 60, 340448, 0, 343473, 60, 349184, 0, 352377, 60, 358088, 0, 361113, 60, 366824, 0, 369849, 60, 375560, 0, 378585, 60, 384296, 0, 387321, 60, 393032, 0, 396057, 60, 401768, 0, 404961, 60, 410672, 0, 413697, 60, 419408, 0, 422433, 60, 428144, 0, 431169, 60, 436880, 0, 439905, 60, 445616, 0, 448809, 60, 454520, 0, 457545, 60, 463256, 0, 466281, 60, 471992, 0, 475017, 60, 480728, 0, 483753, 60, 489464, 0, 492489, 60, 498200, 0, 501393, 60, 507104, 0, 510129, 60, 515840, 0, 518865, 60, 524576, 0, 527601, 60, 533312, 0, 536337, 60, 542048, 0, 545241, 60, 550952, 0, 553977, 60, 559688, 0, 562713, 60, 568424, 0, 571449, 60, 577160, 0, 580185, 60, 585896, 0, 588921, 60, 594632, 0], "names": ["MST", "Mountain Standard Time", "MDT", "Mountain Daylight Time"], "std_offset": -420}
asiaBahrain = {"id": "Asia/Bahrain", "transitions": [], "names": ["AST", "Arabian Standard Time", "ADT", "Arabian Daylight Time"], "std_offset": 180}
asiaShanghai = {"id": "Asia/Shanghai", "transitions": [143200, 60, 146391, 0, 151432, 60, 155127, 0, 160168, 60, 163863, 0, 169072, 60, 172767, 0, 177808, 60, 181503, 0, 186544, 60, 190239, 0], "names": ["CST", "China Standard Time", "CDT", "China Daylight Time"], "std_offset": 480}
europeVienna = {"id": "Europe/Vienna", "transitions": [89951, 60, 94150, 0, 98521, 60, 102889, 0, 107257, 60, 111625, 0, 115993, 60, 120361, 0, 124729, 60, 129265, 0, 133633, 60, 138001, 0, 142369, 60, 146737, 0, 151105, 60, 155473, 0, 159841, 60, 164209, 0, 168577, 60, 172945, 0, 177313, 60, 181849, 0, 186217, 60, 190585, 0, 194953, 60, 199321, 0, 203689, 60, 208057, 0, 212425, 60, 216793, 0, 221161, 60, 225529, 0, 230065, 60, 235105, 0, 238801, 60, 243841, 0, 247537, 60, 252577, 0, 256273, 60, 261481, 0, 265009, 60, 270217, 0, 273745, 60, 278953, 0, 282649, 60, 287689, 0, 291385, 60, 296425, 0, 300121, 60, 305329, 0, 308857, 60, 314065, 0, 317593, 60, 322801, 0, 326329, 60, 331537, 0, 335233, 60, 340273, 0, 343969, 60, 349009, 0, 352705, 60, 357913, 0, 361441, 60, 366649, 0, 370177, 60, 375385, 0, 379081, 60, 384121, 0, 387817, 60, 392857, 0, 396553, 60, 401593, 0, 405289, 60, 410497, 0, 414025, 60, 419233, 0, 422761, 60, 427969, 0, 431665, 60, 436705, 0, 440401, 60, 445441, 0, 449137, 60, 454345, 0, 457873, 60, 463081, 0, 466609, 60, 471817, 0, 475513, 60, 480553, 0, 484249, 60, 489289, 0, 492985, 60, 498025, 0, 501721, 60, 506929, 0, 510457, 60, 515665, 0, 519193, 60, 524401, 0, 528097, 60, 533137, 0, 536833, 60, 541873, 0, 545569, 60, 550777, 0, 554305, 60, 559513, 0, 563041, 60, 568249, 0, 571777, 60, 576985, 0, 580681, 60, 585721, 0, 589417, 60, 594457, 0], "names": ["CET", "Central European Standard Time", "CEST", "Central European Summer Time"], "std_offset": 60}
europeWarsaw = {"id": "Europe/Warsaw", "transitions": [63576, 60, 67776, 0, 72312, 60, 76680, 0, 81048, 60, 85416, 0, 89952, 60, 94152, 0, 98520, 60, 102888, 0, 107256, 60, 111624, 0, 115992, 60, 120360, 0, 124728, 60, 129264, 0, 133632, 60, 138000, 0, 142368, 60, 146736, 0, 151104, 60, 155472, 0, 159841, 60, 164209, 0, 168577, 60, 172945, 0, 177313, 60, 181849, 0, 186217, 60, 190585, 0, 194953, 60, 199321, 0, 203689, 60, 208057, 0, 212425, 60, 216793, 0, 221161, 60, 225529, 0, 230065, 60, 235105, 0, 238801, 60, 243841, 0, 247537, 60, 252577, 0, 256273, 60, 261481, 0, 265009, 60, 270217, 0, 273745, 60, 278953, 0, 282649, 60, 287689, 0, 291385, 60, 296425, 0, 300121, 60, 305329, 0, 308857, 60, 314065, 0, 317593, 60, 322801, 0, 326329, 60, 331537, 0, 335233, 60, 340273, 0, 343969, 60, 349009, 0, 352705, 60, 357913, 0, 361441, 60, 366649, 0, 370177, 60, 375385, 0, 379081, 60, 384121, 0, 387817, 60, 392857, 0, 396553, 60, 401593, 0, 405289, 60, 410497, 0, 414025, 60, 419233, 0, 422761, 60, 427969, 0, 431665, 60, 436705, 0, 440401, 60, 445441, 0, 449137, 60, 454345, 0, 457873, 60, 463081, 0, 466609, 60, 471817, 0, 475513, 60, 480553, 0, 484249, 60, 489289, 0, 492985, 60, 498025, 0, 501721, 60, 506929, 0, 510457, 60, 515665, 0, 519193, 60, 524401, 0, 528097, 60, 533137, 0, 536833, 60, 541873, 0, 545569, 60, 550777, 0, 554305, 60, 559513, 0, 563041, 60, 568249, 0, 571777, 60, 576985, 0, 580681, 60, 585721, 0, 589417, 60, 594457, 0], "names": ["CET", "Central European Standard Time", "CEST", "Central European Summer Time"], "std_offset": 60}
asiaColombo = {"id": "Asia/Colombo", "transitions": [], "names": ["IST", "India Standard Time"], "std_offset": 330}
americaLosAngeles = {"id": "America/Los_Angeles", "transitions": [2770, 60, 7137, 0, 11506, 60, 16041, 0, 20410, 60, 24777, 0, 29146, 60, 33513, 0, 35194, 60, 42249, 0, 45106, 60, 50985, 0, 55354, 60, 59889, 0, 64090, 60, 68625, 0, 72994, 60, 77361, 0, 81730, 60, 86097, 0, 90466, 60, 94833, 0, 99202, 60, 103569, 0, 107938, 60, 112473, 0, 116674, 60, 121209, 0, 125578, 60, 129945, 0, 134314, 60, 138681, 0, 143050, 60, 147417, 0, 151282, 60, 156153, 0, 160018, 60, 165057, 0, 168754, 60, 173793, 0, 177490, 60, 182529, 0, 186394, 60, 191265, 0, 195130, 60, 200001, 0, 203866, 60, 208905, 0, 212602, 60, 217641, 0, 221338, 60, 226377, 0, 230242, 60, 235113, 0, 238978, 60, 243849, 0, 247714, 60, 252585, 0, 256450, 60, 261489, 0, 265186, 60, 270225, 0, 273922, 60, 278961, 0, 282826, 60, 287697, 0, 291562, 60, 296433, 0, 300298, 60, 305337, 0, 309034, 60, 314073, 0, 317770, 60, 322809, 0, 326002, 60, 331713, 0, 334738, 60, 340449, 0, 343474, 60, 349185, 0, 352378, 60, 358089, 0, 361114, 60, 366825, 0, 369850, 60, 375561, 0, 378586, 60, 384297, 0, 387322, 60, 393033, 0, 396058, 60, 401769, 0, 404962, 60, 410673, 0, 413698, 60, 419409, 0, 422434, 60, 428145, 0, 431170, 60, 436881, 0, 439906, 60, 445617, 0, 448810, 60, 454521, 0, 457546, 60, 463257, 0, 466282, 60, 471993, 0, 475018, 60, 480729, 0, 483754, 60, 489465, 0, 492490, 60, 498201, 0, 501394, 60, 507105, 0, 510130, 60, 515841, 0, 518866, 60, 524577, 0, 527602, 60, 533313, 0, 536338, 60, 542049, 0, 545242, 60, 550953, 0, 553978, 60, 559689, 0, 562714, 60, 568425, 0, 571450, 60, 577161, 0, 580186, 60, 585897, 0, 588922, 60, 594633, 0], "names": ["PST", "Pacific Standard Time", "PDT", "Pacific Daylight Time"], "std_offset": -480}
atlanticReykjavik = {"id": "Atlantic/Reykjavik", "transitions": [], "names": ["GMT", "Greenwich Mean Time"], "std_offset": 0}
pacificMajuro = {"id": "Pacific/Majuro", "transitions": [], "names": ["MHT", "Marshall Islands Time"], "std_offset": 720}
africaCasablanca = {"id": "Africa/Casablanca", "transitions": [39240, 60, 40895, 0, 55488, 60, 57695, 0, 64248, 60, 67847, 0, 73752, 60, 75287, 0, 336744, 60, 338951, 0, 345504, 60, 347447, 0, 353544, 60, 355895, 0, 361608, 60, 364463, 0, 371018, 60, 372986, 0, 373730, 60, 374714, 0, 379754, 60, 381482, 0, 382202, 60, 383450, 0, 388490, 60, 390002, 0, 390722, 60, 392186, 0, 397226, 60, 398498, 0, 399218, 60, 400922, 0, 405962, 60, 407018, 0, 407738, 60, 409658, 0, 414866, 60, 415514, 0, 416234, 60, 418394, 0, 423602, 60, 424010, 0, 424730, 60, 427298, 0, 432338, 60, 432530, 0, 433250, 60, 436034, 0, 441746, 60, 444770, 0, 450242, 60, 453506, 0, 458762, 60, 462242, 0, 467450, 60, 470978, 0, 476186, 60, 479882, 0, 484922, 60, 488618, 0, 493658, 60, 497354, 0, 502394, 60, 506090, 0, 511298, 60, 514826, 0, 520034, 60, 523730, 0, 528770, 60, 532466, 0, 537506, 60, 541202, 0, 546242, 60, 549938, 0, 554978, 60, 558674, 0, 563882, 60, 567410, 0, 572618, 60, 576314, 0, 581354, 60, 585050, 0, 590090, 60, 593786, 0], "names": ["WET", "Western European Standard Time", "WEST", "Western European Summer Time"], "std_offset": 0}
australiaAdelaide = {"id": "Australia/Adelaide", "transitions": [16024, 60, 18880, 0, 24760, 60, 27784, 0, 33496, 60, 36520, 0, 42232, 60, 45256, 0, 50968, 60, 54160, 0, 59872, 60, 62896, 0, 68608, 60, 71632, 0, 77344, 60, 80368, 0, 86080, 60, 89104, 0, 94816, 60, 97840, 0, 103552, 60, 106744, 0, 112456, 60, 115480, 0, 121192, 60, 124216, 0, 129928, 60, 132952, 0, 138664, 60, 142024, 0, 147232, 60, 150760, 0, 156136, 60, 159664, 0, 165040, 60, 168400, 0, 173776, 60, 177136, 0, 182512, 60, 185536, 0, 191248, 60, 194776, 0, 199984, 60, 203176, 0, 208888, 60, 212248, 0, 217624, 60, 221152, 0, 226360, 60, 230056, 0, 235096, 60, 238792, 0, 243832, 60, 247528, 0, 252568, 60, 256264, 0, 261472, 60, 265000, 0, 270208, 60, 273736, 0, 278944, 60, 282640, 0, 287680, 60, 291376, 0, 296416, 60, 300112, 0, 305320, 60, 308848, 0, 314056, 60, 317752, 0, 322792, 60, 326320, 0, 331528, 60, 335392, 0, 339760, 60, 344128, 0, 348496, 60, 352864, 0, 357232, 60, 361600, 0, 365968, 60, 370336, 0, 374872, 60, 379240, 0, 383608, 60, 387976, 0, 392344, 60, 396712, 0, 401080, 60, 405448, 0, 409816, 60, 414184, 0, 418552, 60, 422920, 0, 427456, 60, 431824, 0, 436192, 60, 440560, 0, 444928, 60, 449296, 0, 453664, 60, 458032, 0, 462400, 60, 466768, 0, 471136, 60, 475672, 0, 480040, 60, 484408, 0, 488776, 60, 493144, 0, 497512, 60, 501880, 0, 506248, 60, 510616, 0, 514984, 60, 519352, 0, 523888, 60, 528256, 0, 532624, 60, 536992, 0, 541360, 60, 545728, 0, 550096, 60, 554464, 0, 558832, 60, 563200, 0, 567568, 60, 571936, 0, 576472, 60, 580840, 0, 585208, 60, 589576, 0, 593944, 60], "names": ["ACST", "Australian Central Standard Time", "ACDT", "Australian Central Daylight Time"], "std_offset": 570}
americaMarigot = {"id": "America/Marigot", "transitions": [], "names": ["AST", "Atlantic Standard Time", "ADT", "Atlantic Daylight Time"], "std_offset": -240}
europeMonaco = {"id": "Europe/Monaco", "transitions": [54672, 60, 59039, 0, 63577, 60, 67777, 0, 72313, 60, 76681, 0, 81049, 60, 85417, 0, 89953, 60, 94153, 0, 98521, 60, 102889, 0, 107257, 60, 111625, 0, 115993, 60, 120361, 0, 124729, 60, 129265, 0, 133633, 60, 138001, 0, 142369, 60, 146737, 0, 151105, 60, 155473, 0, 159841, 60, 164209, 0, 168577, 60, 172945, 0, 177313, 60, 181849, 0, 186217, 60, 190585, 0, 194953, 60, 199321, 0, 203689, 60, 208057, 0, 212425, 60, 216793, 0, 221161, 60, 225529, 0, 230065, 60, 235105, 0, 238801, 60, 243841, 0, 247537, 60, 252577, 0, 256273, 60, 261481, 0, 265009, 60, 270217, 0, 273745, 60, 278953, 0, 282649, 60, 287689, 0, 291385, 60, 296425, 0, 300121, 60, 305329, 0, 308857, 60, 314065, 0, 317593, 60, 322801, 0, 326329, 60, 331537, 0, 335233, 60, 340273, 0, 343969, 60, 349009, 0, 352705, 60, 357913, 0, 361441, 60, 366649, 0, 370177, 60, 375385, 0, 379081, 60, 384121, 0, 387817, 60, 392857, 0, 396553, 60, 401593, 0, 405289, 60, 410497, 0, 414025, 60, 419233, 0, 422761, 60, 427969, 0, 431665, 60, 436705, 0, 440401, 60, 445441, 0, 449137, 60, 454345, 0, 457873, 60, 463081, 0, 466609, 60, 471817, 0, 475513, 60, 480553, 0, 484249, 60, 489289, 0, 492985, 60, 498025, 0, 501721, 60, 506929, 0, 510457, 60, 515665, 0, 519193, 60, 524401, 0, 528097, 60, 533137, 0, 536833, 60, 541873, 0, 545569, 60, 550777, 0, 554305, 60, 559513, 0, 563041, 60, 568249, 0, 571777, 60, 576985, 0, 580681, 60, 585721, 0, 589417, 60, 594457, 0], "names": ["CET", "Central European Standard Time", "CEST", "Central European Summer Time"], "std_offset": 60}
