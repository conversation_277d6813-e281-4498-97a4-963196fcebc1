### !!! CAUTION !!!
### Before adding keys, ensure you have no duplicates. Duplicates could lead to unexpected behaviour.

# zachv: 2025-05-16 | Planio #30009
calendar.specialday.field.date=Date
calendar.specialday.field.date.help=Enter a date for the special day
calendar.specialday.field.year=Year

# jacciedt: 2025-04-25 | Planio #33761
multiUsagePointAccountAdjustmentProcessor.notification.disconnect.email.subject=Account balance has run out for your account
multiUsagePointAccountAdjustmentProcessor.notification.disconnect.email.message=Dear Customer,\n\nYour meters will be disconnected.\n\nYour account status is: \n  Account balance: {7,number,currency}\n  Low balance notification threshold: {8,number,currency}\n\nRegards,\nSupport Team
multiUsagePointAccountAdjustmentProcessor.notification.disconnect.sms.message=Balance for your account has run out and will be disconnected. Balance is {7,number,currency} Regards, Support Team
multiUsagePointAccountAdjustmentProcessor.notification.emergencyCredit.email.subject=Emergency credit threshold for your account
multiUsagePointAccountAdjustmentProcessor.notification.emergencyCredit.email.message=Dear Customer,\n\nYour account status is: \n Account balance: {7,number,currency}\n  Low balance notification threshold: {8,number}\n  Emergency credit threshold: {9,number,currency}\n\nRegards,\nSupport Team
multiUsagePointAccountAdjustmentProcessor.notification.lowBalance.email.subject=Account balance low for your account
multiUsagePointAccountAdjustmentProcessor.notification.lowBalance.email.message=Dear Customer,\n\nYour account status is: \n  Account balance: {7,number,currency}\n  Low balance notification threshold: {8,number,currency}\n\nRegards,\nSupport Team
multiUsagePointAccountAdjustmentProcessor.notification.emergencyCredit.sms.message=Balance for your account is running low and below emergency credit threshold. Balance is {7,number,currency} Regards, Support Team
multiUsagePointAccountAdjustmentProcessor.notification.lowBalance.sms.message=Balance for your account is running low. Balance is {7,number,currency} Regards, Support Team


# michalv: 2025-05-09 | Planio #32735
export.error.too.many.records=Error: Too many records to export (over {0}). Please filter on a smaller date range.

# joelc: 2025-02-06 | Planio #28349
#power.limit.edit.label.prompt=Name
#power.limit.edit.label.help=Name of the power limit value. This is text display
power.limit.edit.value.prompt=Valor de Watt
power.limit.edit.value.help=Valor do limite de potÃªncia. Este valor deve ser numÃ©rico
error.field.powerlimit.value.already=This power limit already exists.

# joelc: 2025-02-13 | Planio #31090
pricingstructure.suggestbox.placeholder = Start typing to filter list


#rodgersn: 2025-02-06 | Planio 29656
engineering.token.display.sgc=Supply Group Code:
engineering.token.display.krn=Key Revision Number:
engineering.token.display.ti=Tariff Index:

#renciac: 2024-08-31 | Planio 17062: MMA: get email from name & address from appsetting not from messages.properties
error.email.invalid=Invalid email address.

# christoe: 2024-07-29 | Planio #28161
import.edit.bulk.mdc.item.update.success=MDC message successfully updated.

# michalv: 2024-10-09 | Planio 25922
meter.models.mdc.required.for.thin.payment=Thin payment mode requires MDC

# christoe: 2024-10-21 | Planio #30157
online.bulk.panel.error.regex.validation1=Regex validation has been configured for UP Name, Agreement Reference or Account Name.
online.bulk.panel.error.regex.validation2=Auto-generation of these fields is disabled and requires manual entry on the Meter tab.

# Michalv: 2024-09-12 | Planio #25404
error.tab.duplicate.auxaccount=AuxAccount open in another tab. Tab: ({0})
error.tab.duplicate.custaccounttrans=Customer Transaction open in another tab. Tab: ({0})

#thomasn: 2023-12-06 | Planio 12164
access_group.lbl=Access Group
access_group.update.header=Update Access Group
access_group.update.group.error=New group has not been selected.
access_group.update.pricing.error=Customer UsagePoint(s) must have GLOBAL Pricing Structure.
access_group.update.future.pricing.clear=This will also clear any future pricing structures.
access_group.update.location.groups.clear=This will clear the usage point and customer location groups.
access_group.update.confirm.lbl=Proceed?
access_group.update.button=Update Access Group
insert.unique.error.meter=The meter details must be unique.
# Used by MMC MapperStaticProxy. e.g. CustomerMapperStaticProxy
insert.unique.error.customer=The customer details must be unique.
insert.unique.error.usagepoint=The usage point details must be unique.
insert.unique.error.devicestore=The device store details must be unique.
insert.unique.error.pricingstructure=The pricing structure details must be unique.
insert.unique.error.auxchargeschedule=The Aux charge schedule details must be unique.

#thomasn: 2023-10-11 | Planio 12164
session_auth.form.submit=Submit
session_auth.form.logout=Logout
session_auth.form.instructions=Choose session authorization details
session_auth.form.role=Role:
session_auth.form.group=Group:
session_auth.form.group_and_role=Group and Role:
session_auth.form.invalid=Login Error. Invalid Form Mode
access_group.error.group_already_cleared=The group has already been cleared on these entities.
access_group.success.updated_group=Successfully updated group
workspace.usagepoint.overview=Overview
workspace.usagepoint.actions=Actions
grouphierarchy.field.is_access_group=Access Group
grouphierarchy.field.is_access_group.help=Whether to connect this group to an access control organisation access group. This will show a list of access control groups on creating groups for this hierarchy level
grouphierarchy.field.is_access_group.error.already_assigned=An organisation access group can only be linked to one hierarchy level.
grouptype.field.location.group=Location Group
groupnode.field.access_group=Access Group
groupnode.field.access_group.help=Connects an access control organisation access group to this group which will restrict access to group users. A group user in a different group would not see this group in selections.
groupnode.field.access_group.error.no_groups=This hierarchy level has enabled organisation access groups but none have been defined.
changegroup.org_group.confirm=Changing your group and/or role selection will reload the application. Would you like to continue?
login.session.reload=You will be redirected to the login screen and the application will be reloaded.
mrid.ui=Unique ID
mrid.ui.help=Enter the unique ID
mrid.ui.external=External Unique ID?
mrid.ui.external.help=Whether unique id is from an external system

# christoe: 2024-06-21 | Planio #28160, #28158
mdc.txn.relay.title=Relay
mdc.txn.relay.help=Select which relay to connect or disconnect.
mdc.txn.relay.main=Main
mdc.txn.relay.aux.one=Auxiliary 1
mdc.txn.relay.aux.two=Auxiliary 2
mdc.txn.power.limit=Power_Limit

# michalv: 2024-06-15 | Planio #29332
error.field.email3=One or multiple email addresses are invalid

# michalv: 2024-07-18 | Planio 28459
online.bulk.panel.encryptionKey.help = Enter the encryption key for this meter. This field is required for activation. To edit an already existing meter, use the UsagePoint Page.

# christoe: 2024-02-06 | Planio #23257 [i-Switch] Send an SMS when an aux charge is loaded
notify.selection.inherit=Inherit ({0})
error.notify.selection.null=It is required to set both preferences.
group.notify.children.change.alert=Children with matching or undefined notification IDs will be updated with these changes. Continue?
group.error.notification.save=Unable to link the notification information to the group.
customer.adjust.aux.accounts=Auxiliary Account Adjustments
customer.new.aux.accounts=New Auxiliary Accounts
customer.notification.types=Notification Types
customer.manage.notification.types=Manage Notification Types

#renciac: 2024-05-20 | Planio 27858: TariffStartDate not on the first of a month at midnight
warning.tariff.start.date.not.on.month.boundary=WARNING: tariff start date is not on a month boundary (the 1st at zero hours).</br>\
Bizswitch must be configured properly for this, as mid-month tariff changes MAY cause billing problems if not handled correctly!</br>\
Please be aware that MONTHLY BILLING Cyclic charges may only start or end on a month boundary.</br>\
Continue?
error.billing.cyclic.change.midmonth.monthly=MONTHLY BILLING Cyclic charges may only be initiated / changed for tariff start ON a month boundary (the 1st of a month at zero hours).

# michalv: 2024-05-22 | Planio 28023
token.reversal.reprinted.error=You do not have permission to reverse a reprinted token.

# rodgersn: 2024-02-20 | Planio #26293
meter.uri.remove.question=Changing the meter model will clear the Meter URI Fields (if captured). Continue?
meter.models.field.uri.present=URI Present
meter.models.field.uri.present.help=This indicates if the meter has URI.
meter.uri.address=Address
meter.uri.address.help=Enter the URI address of the meter.
meter.uri.port=Port
meter.uri.port.help=Enter the URI port number of the meter.
meter.uri.protocol=Protocol
meter.uri.protocol.help=Enter the URI protocol used to communicate with the meter.
meter.uri.params=Parameters
meter.uri.params.help=Enter the URI parameters used when communicating with the meter.
meter.uri.fields=Meter URI Fields
meter.uri.fields.list=Meter URI Fields (if captured): Address, Port, Protocol, Parameters.
meter.uri.port.error=Port must be a number from 0 to 65535.
bulk.upload.meter.uri.address=Meter URI Address
bulk.upload.meter.uri.port=Meter URI Port
bulk.upload.meter.uri.protocol=Meter URI Protocol
bulk.upload.meter.uri.params=Meter URI Parameters
bulk.upload.meter.uri.not.present.address.error=The meter Model can not have a meter URI address.
bulk.upload.meter.uri.not.present.port.error=The meter Model can not have a meter URI port.
bulk.upload.meter.uri.not.present.protocol.error=The meter Model can not have a meter URI protocol.
bulk.upload.meter.uri.not.present.params.error=The meter Model can not have a meter URI parameters.
error.field.meteruriprotocol.max=Meter URI Protocol must not exceed 255 characters.
error.field.meteruriaddress.max=Meter URI Address must not exceed 100 characters.
error.field.meteruriparams.max=Meter URI Parameters value is too long.

# zachv: 2024-05-07 | Planio 28174
tariff.field.bsst.charge_name.title=Charge Name
tariff.field.bsst.charge_name.title.help=Name of the charge that will be displayed on the receipt. 

# joelc: 2024-04-26 | planio-27792
group.type.for.cape.verde.contract = -

# michalv: 2024-04-05 | planio-26839
meter.txn.tokencode1=Token Code/s

# christoe: 2023-12-28 | Planio #25194 Reason Entry for Writing Off Charges in MMA
usagepoint.charge.writeoff.enter.reason=Enter a reason for writing off charges
usagepoint.charge.writeoff.select.reason=Select a reason for writing off charges

# thomasn: 2024-02-21 | planio-25347 [Cape Verde] Analyze and create the Cape Verde tariffs
tariff.blocks.thresholdCharge.error.empty=Where Block Price is set all Threshold Charges must be set or all must be empty.
tariff.blocks.thresholdCharge.error.incomplete=Threshold Charges can be set only where block price exists.

# zachv: 2023-11-25 | Planio #25498
grouptree.show_more = Mostrar mais
grouptree.empty = Vazio
grouptree.search.help = Escreve pelo menos as duas primeiras letras de um item. O item pode estar em qualquer nÃ­vel da hierarquia de dados.
suggestbox.placeholder = Digite para procurar...

# renciac: 2023-11-20 | Planio 25211: [TANESCO UAT] Staff tariff as "FBE" with monthly cyclic charge
tariff.field.subsidised.units.title=Unidades Subsidiadas Mensais
tariff.field.subsidised.units.descrip=DescriÃ§Ã£o
tariff.field.subsidised.units=Unidades
tariff.field.subsidised.units.help=Unidades que sÃ£o emitidas a uma taxa subsidiada.
tariff.field.bsst.charge.title=Tarifa
tariff.field.bsst.charge.title.help=Digite o montante a cobrar pelas unidades subsidiadas 
tariff.error.bsst.charge.positive=Deve ser um valor positivo.

# renciac: 2023-11-15 | Planio 25151 [TANESCO] [MMA] Automatically loading debt for a newly installed meter
tariff.field.meter_debt.title=DÃ©bito do Contador
tariff.field.meter_debt.singlephase.label=Single Phase
tariff.field.meter_debt.singlephase.label.help=Digite o montante da dÃ­vida ao consumidor para unidades monofÃ¡sicas prÃ©-carregadas num novo contador.
tariff.field.meter_debt.threephase.label=Three Phase
tariff.field.meter_debt.threephase.label.help=Digite o montante da dÃ­vida ao consumidor para unidades trifÃ¡sicas prÃ©-carregadas num novo contador.

# marcod: 2023-08-29| planio 13587
import.upload.file.already.uploaded.group=Duplicate Filename. This file was already uploaded by another group

# marcod: 2023-06-21 | Planio 12175 Bulk device store movements
bulk.device.store.movement.help=Select the end device store the meters should be transferred to
bulk.device.store.movement=Select TO Device Store
bulk.device.store.movement.header=Bulk Device Store Movement
bulk.device.store.movement.without.input.file.not.implemented.yet=Bulk Device Store Movement needs input file of meters. Database setting has_input_file only implemented as 'y' for this filetype yet.
bulk.device.store.movement.param=TO Device Store

# renciac: 2023-09-20 | Planio 21358 View & writeoff also billing cyclic charges
usagepoint.last.cyclic.date.info=Ãltima data de cobranÃ§a cÃ­clica
usagepoint.last.cyclic.dates.info=Datas da Ãºltima cobranÃ§a cÃ­clica
usagepoint.last.cyclic.vend.date=Na venda/abastecimento
usagepoint.last.cyclic.billing.date=Na faturaÃ§Ã£o
usagepoint.charge.view.writeoff.date.help=A Ãºltima data cÃ­clica Ã© a data em que os encargos periÃ³dicos, como os encargos diÃ¡rios e mensais, foram pagos pela Ãºltima vez por um consumidor. Os encargos posteriores a esta data ainda tÃªm de ser recuperados e podem ser anulados aqui seleccionando uma nova "Data final" que se torna a nova "Ãltima data cÃ­clica". Dependendo da tarifa e do modo de pagamento, existem potencialmente dois conjuntos de encargos cÃ­clicos, um no momento do fornecimento/abastecimento e outro no momento da faturaÃ§Ã£o.
usagepoint.charge.view.dialog.invalid.date.both=A data selecionada nÃ£o pode ser anterior a AMBAS as Ãºltimas datas cÃ­clicas apresentadas
usagepoint.charge.view.dialog.warning.last.vend.cyclic.date=A Ãºltima taxa cÃ­clica de venda jÃ¡ foi calculada em {0}. NÃ£o hÃ¡ nada a calcular para as taxas cÃ­clicas VEND. Continuar?
usagepoint.charge.view.dialog.warning.last.vend.billing.date=Ãltimo encargo cÃ­clico de faturaÃ§Ã£o jÃ¡ calculado em {0}. NÃ£o hÃ¡ nada a calcular para as taxas cÃ­clicas de faturaÃ§Ã£o. Continuar?
usagepoint.charge.view.filter.date.help=Os encargos pendentes serÃ£o calculados a partir das datas do Ãltimo ciclo acima referidas atÃ© esta nova data selecionada. Tem de ser APÃS a(s) Ãºltima(s) data(s) cÃ­clica(s) anterior(es). 
usagepoint.charge.view.dialog.date=Selecionar a data final dos encargos
usagepoint.charge.writeoff.vend.heading=CARGAS DE VENDA/TOPUP, Data da Ãºltima cobranÃ§a cÃ­clica
usagepoint.charge.writeoff.billing.heading=CARGAS DE FACTURAÃÃO, Data da Ãºltima taxa cÃ­clica de faturaÃ§Ã£o
usagepoint.charge.writeoff.vend.total=O montante total de encargos de venda, incluindo impostos, Ã©
usagepoint.charge.writeoff.billing.total=O montante total dos encargos de faturaÃ§Ã£o, incluindo impostos, Ã©
usagepoint.charge.writeoff.both.total=Montante total incluindo impostos

# christoe: 2023-10-04 | planio 22545
special.action.reason.no.reason=NÃ£o foi dada nenhuma razÃ£o.

# rodgersn: 2023-06-07 | Planio 21001
register.reading.txn.meter=Contador
meterreadings.table.date=Data de criaÃ§Ã£o
usage.point.register.reading.txn.description=Registar leituras de todos os contadores deste ponto de utilizaÃ§Ã£o para o perÃ­odo de tempo selecionado

# christoe: 2023-06-27 | planio 22796 conflict resolved
reprint.default.email.message=Prezado cliente\n\nPor favor, encontre os detalhes do seu recibo abaixo:\n{0}\nAtenciosamente\nMeter Management

# rodgersn: 2023-02-28 | Planio 15160
meter.assign.from.units.warn=A alteraÃ§Ã£o da estrutura de preÃ§os de Thin Units nÃ£o migrarÃ¡ o saldo de Units. Um ajuste manual terÃ¡ que ser feito. Continuar?
ps.paymentmode.change.warn=Essa mudanÃ§a na estrutura de preÃ§os altera a forma de pagamento. Certifique-se de que todas as cobranÃ§as e/ou faturas estejam atualizadas. Continuar?

# joelc: 2022-12-09 | planio 7589
usagepoint.save.license.error=O mÃ¡ximo de pontos de utilizaÃ§Ã£o permitido foi atingido. Por favor, notifique o administrador do sistema.
bulk.import.license.waring =A importaÃ§Ã£o de {0} pontos de utilizaÃ§Ã£o ativos excederÃ¡ o mÃ¡ximo de pontos de utilizaÃ§Ã£o ativos permitidos. <br/>Note que quando o limite for atingido, apenas os pontos de utilizaÃ§Ã£o marcados como Inativos serÃ£o importados.

# marcod: 2023-04-06 | Planio 21668 [ZESCO] Debt collection methods
auxchargeschedule.specific.list.item=CONTA ESPECÃFICA
customer.auxaccount.principle.amt=Montante Principal

# renciac: 2023-04-01 | Planio 18524 New requirements for RegisterReadingThinTariff
billingdet.appliesto.group.label=Apenas para determinantes de facturaÃ§Ã£o que se aplicam a outros
billingdet.discount.label=Desconto
billingdet.discount.help=Seleccione para indicar que este determinante de facturaÃ§Ã£o significa um desconto, por exemplo. desconto solar.
billingdet.charge.type.help=Este determinante de facturaÃ§Ã£o ou Ã© uma cobranÃ§a percentual da cobranÃ§a de outro determinante de faturaÃ§Ã£o ou uma taxa fixa
billingdet.applies.to.label=Aplica-se a
billingdet.applies.to.help=Selecione o determinante principal de faturaÃ§Ã£o ao qual o valor percentual deste determinante de faturaÃ§Ã£o serÃ¡ aplicado.
billingdet.lnk.error.save=NÃ£o Ã© possÃ­vel salvar o link appliesTo do Determinante de FaturaÃ§Ã£o.
billingdet.lnk.error.both=ApplyesTo e Charge_type devem ser ambos capturados (ou nenhum)
billingdet.applies.to.already.in.use=Os determinantes de faturaÃ§Ã£o aos quais este determinante de faturaÃ§Ã£o se aplica jÃ¡ estÃ£o em uso numa tarifa ou template.
billingdet.applies.to.already.in.use.on.mdc=Este Det de faturaÃ§Ã£o estÃ¡ em uso num MDC e nÃ£o pode ser utilizado como um Det de subfaturaÃ§Ã£o que se aplica a outros.
billingDet.in.use.cannot.change.settings=Este Det de faturaÃ§Ã£o jÃ¡ estÃ¡ em uso numa tarifa ou template, nÃ£o pode alterar configuraÃ§Ãµes ou registar estado.
billingDet.in.use.cannot.change.applies.to=Este Det de faturaÃ§Ã£o ou aqueles aos quais ele se aplica jÃ¡ estÃ£o em uso numa tarifa ou modelo, nÃ£o pode alterar os dados de faturaÃ§Ã£o aos quais se aplica.
billingDet.change.regread.panel.open=Atualmente vocÃª tem um RegisterReadingThinTariff em construÃ§Ã£o. Salvar esta alteraÃ§Ã£o billing_det farÃ¡ com que isso seja apagado. Se vocÃª precisar salvar as alteraÃ§Ãµes primeiro, nÃ£o confirme esta mensagem, salve aquela tarifa primeiro e depois volte para salvar este billing_det. Continuar com este "salvar" agora?
billingdet.taxable=AppliesTo Ã© taxÃ¡vel?
billingdet.taxable.help=Desmarque se a taxa nÃ£o for aplicÃ¡vel a este Det de FaturaÃ§Ã£o - SOMENTE para Dets de FaturaÃ§Ã£o que se aplicam a outros
billingdet.lnk.error.taxable=TaxÃ¡vel sÃ³ pode ser desmarcado quando este for um applyTo BillingDet
cyclic.charge.apply.at.lbl=Aplicar a:
cyclic.charge.apply.at.vend.lbl=Venda
cyclic.charge.apply.at.billing.lbl=FacturaÃ§Ã£o
cyclic.charge.apply.at.error.required=VocÃª deve selecionar um.
tariff.blocks.unitcharge.negative=A percentagem da cobranÃ§a unitÃ¡ria de faturaÃ§Ã£o ou desconto nÃ£o podem ser negativos
tariff.save.failed.billingDets.changed.on.database=Falha ao salvar tarifa. Percentagem/Desconto BillingDets foram alterados na base de dados. Incompatibildade com input. Recarregue a pÃ¡gina.
unitcharge.choose.charge.type=Escolha o tipo de cobranÃ§a
unitcharge.type.none=Nenhum
unitcharge.type.percentage=Percentagem
unitcharge.type.flatrate=Taxa Fixa
unitcharge.discount.type.charge.error=O desconto deve ser ou uma percentagem ou uma taxa fixa

# thomasn: 2023-03-21 | Planio 17726
supply.group.in.use.error.service=Supply Group in use by one or more meters. Press cancel to load latest.
supply.group.in.use.error.lbl=Supply Group in use by one or more meters. Some fields are read-only while in this state.

# rodgersn: 2023-02-09 | Planio 20655
register.readings.total.consumption=Consumo Total:

# jacciedt: 2022-12-14 | Planio 19775
export.field.receiptnum=NÃºmero do Recibo

# thomasn: 2023-01-10 | Planio 18459
error.positive.value=O valor deve ser positivo.

# patrickm: 2022-11-08 | Planio #19650
reprint.total.tax=Taxa total
reprint.total.tax-inclusive=Total (taxa incl.)

# thomasn: 2022-11-08 | Planio 17785
aux.account.mrid.external.unique.validation=O ID Ãºnico desta conta aux jÃ¡ estÃ¡ em uso.

# marcod: 2022-10-07 | Planio 19234
error.field.powerlimit.name.range=O nome deve ter entre 1 e 255 caracteres.
error.field.powerlimit.name.required=O nome Ã© obrigatÃ³rio.
error.field.powerlimit.value.required=O valor Ã© obrigatÃ³rio.
error.field.powerlimit.value.type=O valor deve ser numÃ©rico e maior que zero.

# rodgersn: 2022-09-29 | Planio 16034
default.template.bulk.uploads=Download Template

# jacciedt: 2022-05-05 | Planio 15256
bulk.blocking.header=Bloqueio em massa : Nome do ficheiro: {0}
bulk.blocking.without.input.file.not.implemented.yet=A geraÃ§Ã£o de bloqueio em massa necessita de um ficheiro de entrada de contadores. ConfiguraÃ§Ã£o da base de dados tem input_file integrado apenas como 'y' para este tipo de ficheiro.
import.upload.blocking.permission.needed=Este usuÃ¡rio nÃ£o tem permissÃ£o para enviar instruÃ§Ãµes de bloqueio em massa.
bulk.blocking.blocking.type=Tipo de bloqueio
bulk.blocking.blocking.type.required.field.error=Tipo de bloqueio Ã© um campo obrigatÃ³rio
bulk.blocking.not.blocked=nÃ£o bloqueado

# thomasn: 2022-08-31 | Planio 17172
usagepoint.ps.unitstocurrency= Alterar a estrutura de preÃ§os de unidades finas nÃ£o migrarÃ¡ o saldo das unidades. Um ajuste manual terÃ¡ que ser feito. Continuar?

# rodgersn:2022-07-28 | Planio 18092 Missing message on some translation files
customer.txn.error.amount.incl.tax.is.zero=Valor incluindo taxas nÃ£o pode ser zero

# thomasn: 2022-06-07 | Planio 16665
mdc.txn.pandisplay=Pan Display(Limpar balanÃ§o)
remove.meter.pandisplay=*Enviar mensagem MDC para limpar o saldo do contador.

# jacciedt: 2022-02-28 | Planio 12375
bulk.pricing.structure.change.header=alteraÃ§Ã£o da estrutura de preÃ§os em massa: Nome do arquivo: {0}
bulk.pricing.structure.change.without.input.file.not.implemented.yet=A geraÃ§Ã£o de mudanÃ§a de estrutura de preÃ§os em massa precisa de um ficheiro de entrada de contadores. ConfiguraÃ§Ã£o da base de dados has_input_file integrada apenas como 'y' para este tipo de ficheiro ainda.
import.upload.pricing.structure.change.permission.needed=Este utlizador nÃ£o tem permissÃ£o para fazer upload em massa de instruÃ§Ãµes de alteraÃ§Ã£o da estrutura de preÃ§os.
bulk.pricing.structure.change.ps.start.date.error=A data de inÃ­cio deve estar no futuro e ser posterior Ã  data de inÃ­cio da estrutura tarifÃ¡ria atualmente ativa e Ã  data de inÃ­cio da primeira tarifa..
bulk.pricing.structure.change.start.date=Data de inÃ­cio
bulk.pricing.structure.change.start.date.help= Este Ã© um dado em que a estrutura de preÃ§os selecionados. Deve ser datado no futuro e com base em quando a importaÃ§Ã£o Ã© feita.
bulk.pricing.structure.change.pricing.structure=Estrutura de preÃ§os
bulk.pricing.structure.change.pricing.structure.help=Estruturas tarifÃ¡rias que possuem tarifas ativas com base na Data de InÃ­cio selecionada. Mudar de unidades finas nÃ£o migrarÃ¡ o saldo das unidades. Um ajuste manual terÃ¡ que ser feito.
import.items.abort= Falha na importaÃ§Ã£o:

# renciac: 2022-05-05 | Planio 15937 curb validation between PS and billing dets esp for TOU
error.pricingStructure.future.ps.no.tariff.at.date=A estrutura tarifÃ¡ria futura nÃ£o possui tarifa vigente na data de inÃ­cio selecionada.

# rodgersn: 2022-05-17 | Planio #16807
customer.txn.error.tax.less.than.amount=O valor do imposto nÃ£o pode ser menor que o valor incluindo imposto para valores negativos.
customer.txn.error.tax.and.amount.different.sign=Se foram inseridos Valor incluindo imposto e Valor do imposto, eles devem ser ambos positivos ou ambos negativos.

# patrickm: 2022-03-24 | Planio #15899
error.tab.duplicate.customer=Cliente aberto noutro separador. Separador: ({0})
error.tab.duplicate.meter=Contador aberto noutro separador. Separador: ({0})
error.tab.duplicate.usagepoint=Ponto de medida aberto noutro separador. Separador: ({0})

# jacciedt: 2022-02-10 | Planio 14296
confirm.bill.payment.reversal=Confirmar reversÃ£o do pagamento da fatura?
bill.payment.reversal.success=ReversÃ£o de pagamento de fatura bem-sucedida. Ref Original = {0}, Ref ReversÃ£o = {1}
reverse.payment=ReversÃ£o do pagamento

# renciac: 2022-01-25 | Planio 12812 New pricing_structure history
usagepoint.hist.pricing.structure=estrutura de preÃ§os
usagepoint.hist.pricing.start=Data de inÃ­cio
usagepoint.hist.pricing.end=Data final
usagepoint.hist.ps.change.reason=motivo da mudanÃ§a
up_pricing_structure.header=HistÃ³rico da estrutura de preÃ§os no ponto de medida
up_pricing_structure.sub.header=AlteraÃ§Ãµes anteriores na estrutura de preÃ§os feitas neste ponto de medida
usagepoint.current.pricing.change.enter.reason=Insira um motivo para alterar a estrutura de preÃ§os atuais
usagepoint.current.pricing.change.select.reason=Selecione um motivo para alterar a estrutura actual de preÃ§os
usagepoint.future.pricing.change.enter.reason=Insira um motivo para alterar a estrutura de preÃ§os futuros
usagepoint.future.pricing.change.select.reason=Selecione um motivo para alterar a estrutura de preÃ§os futuros
usagepoint.field.pricingstructure=Estrutura de preÃ§os Atual
usagepoint.field.future.pricingstructure=Estrutura de preÃ§os Futura
usagepoint.ps.date.modified.hd=Data da Ãºltima alteraÃ§Ã£o
usagepoint.ps.user.modified.hd=Utilizador
usagepoint.ps.change.reason.hd=Motivo da alteraÃ§Ã£o

# jacciedt: 2022-01-19 | Planio 14121
reprint.key.change.notice.line.1=O seu token de recurso estÃ¡ abaixo, mas o seu contador requer uma alteraÃ§Ã£o de chave antes de inseri-lo.
reprint.key.change.notice.line.2=Para alterar a chave do seu contador, insira os tokens listados abaixo:

# thomasn: 2022-01-31 | planio-15259 [BizSwitch & MeterMngCommon] Add support for codes in BlockTariff
tariff.blocks.unitcharge.error.empty=Quando o preÃ§o do bloco Ã© definido, todos os encargos unitÃ¡rios devem ser definidos ou todos devem ser vazios.
tariff.blocks.unitcharge.error.incomplete=Os Encargos UnitÃ¡rios podem ser definidos apenas quando existe um preÃ§o de bloco.

# marcod: 2021-11-18 | Planio 14768 Indra integration UI changes
bulk.upload.cust.ref=ReferÃªncia do cliente
bulk.upload.external.unique.id=Id Ãnico Externo do Contador
bulk.upload.external.cust.unique.id=Id Unico externo do cliente
bulk.upload.external.up.unique.id=Id externo Unico externo UP
mrid.component.error=ID Unico Ã© um campo obrigatÃ³rio
group.edit=editar
customer.ref.label=ReferÃªncia do cliente
customer.ref.help=Insira um nÃºmero de referÃªncia Ãºnico para o cliente. Esta referÃªncia refere-se a este cliente em particular no Sistema de GestÃ£o de contadores.
error.field.customerreference.null=ReferÃªncia do cliente Ã© um campo obrigatÃ³rio
error.field.customerreference.range=Uma referÃªncia do cliente deve ter entre {min} e {max} caracteres.
cust.mrid.external.unique.validation=O ID Ãºnico deste cliente jÃ¡ estÃ¡ em uso
cust.ref.external.unique.validation=A referÃªncia do cliente deste cliente jÃ¡ estÃ¡ em uso
up.mrid.external.unique.validation=O ID Ãºnico deste ponto de medida jÃ¡ estÃ¡ em uso.
gen.group.mrid.external.unique.validation=O ID Ãºnico deste grupo jÃ¡ estÃ¡ em uso.
meter.model.mrid.external.unique.validation=O ID Ãºnico deste modelo de contador jÃ¡ estÃ¡ em uso.
gen.group.mrid.external.length.validation=O ID Ãºnico deve ter entre 1 e 100 caracteres
aux.type.mrid.external.unique.validation=O ID Ãºnico deste tipo aux jÃ¡ estÃ¡ em uso.
special.action.reason.mrid.external.unique.validation=O ID Ãºnico deste motivo de aÃ§Ã£o especial jÃ¡ estÃ¡ em uso.
pricing.structure.mrid.external.unique.validation=O ID Ãºnico desta estrutura de preÃ§os jÃ¡ estÃ¡ em uso.

# renciac: 2021-12-30 | Planio 15152 Cater for changing payment mode in Pricing Structure
usagepoint.error.new.installdate.before.last.sts.vend.date=data de instalaÃ§Ã£o nÃ£o pode ser ANTERIOR Ã  Ãºltima data de venda/recarga: {0}
usagepoint.error.new.installdate.before.current.ps.start.date=A data de instalaÃ§Ã£o nÃ£o pode ser ANTERIOR Ã  data de inÃ­cio da Estrutura de PreÃ§os atual neste ponto de medida {0}.

# renciac: 2021-12-06 | Planio 14852 [EPC] Zero value in block tariff
tariff.blocks.zero.error=Apenas o primeiro bloco poderÃ¡ ter preÃ§o unitÃ¡rio = 0

# renciac: 2021-10-18 | Planio 14521 Aux Account Specific charge schedule
auxspecchargeschedule.title=Cronograma de CobranÃ§as EspecÃ­ficas da Conta Aux
auxspecchargeschedule.title.add=Adicionar ProgramaÃ§Ã£o de Carga EspecÃ­fica Aux
auxspecchargeschedule.title.update=Atualizar ProgramaÃ§Ã£o de Carga EspecÃ­fica Aux
customer.debt.status.lbl=Estado da dÃ­vida
customer.debt.status.help=STATUS DA DÃVIDA:<br/><b>Ativo:</b> saldo nÃ£o Ã© zero e positivo<br/><b>Liquidado:</b> saldo Ã© zero <br/><b>Cobrado em excesso:</b > saldo Ã© negativo (ou seja, Reembolso)<br/><b>Suspenso:</b> suspensÃ£o atÃ© a data futura<br/><b>Anulado:</b> A ÃLTIMA transaÃ§Ã£o para a conta Aux Ã© do tipo WRITE_OFF e saldo = 0
customer.chargeschedule.cycle=Ciclo de Carga
customer.chargeschedule.chamt=Montante do ciclo de carga

# renciac: 2021-09-05 | Planio 11634, 11636, 13969 ViewOutstandingCharges bugs
usagepoint.charge.view.activation.in.future=A data de ativaÃ§Ã£o do ponto de medida estÃ¡ no futuro, ainda nÃ£o possui cargas cÃ­clicas.
# already translated below usagepoint.charge.writeoff.dialog.heading=Lista de cobranÃ§as pendentes em {0}

# patrickm: 2021-08-31 | Missing key
pricingstructure.error.active=Estrutura de PreÃ§os nÃ£o tem Tarifa. NÃ£o pode ser ativo.

# patrickm: 2021-08-20 | Planio 9834
meter.model.in.use=O modelo do contador tem um ou mais contadores conectados. Alguns campos sÃ£o apenas de leitura enquanto estÃ£o neste estado
meter.models.paymentmodes.preselected=Os modos de pagamento ativos nÃ£o podem ser removidos enquanto o modelo de contador tiver contadores conectados.

# renciac: 2021-06-01 | Planio 11646 Bulk Keychange
supply.group.target.label=Supply group alvo / RevisÃ£o Principal
supply.group.target.label.help= O prÃ³ximo Supply Group/RevisÃ£o de Chave para o qual este Supply Group serÃ¡ alterado.
supply.group.target.error.same=O Supply Group alvo nÃ£o pode ser o mesmo que o atual
supply.group.target.validation.error=BaseDate alvo Ã© menor que o SGC atual
supply.group.target.validation.error.expired=ExpiraÃ§Ã£o ou emissÃ£o do SGC de destino atÃ© que a data seja expirada
supply.group.target.validation.nulls= NOTA: A validaÃ§Ã£o entre o SGC e o SGC de destino nÃ£o foi concluÃ­da porque uma ou mais datas de base, expiraÃ§Ã£o ou emissÃ£o atÃ© a data ainda sÃ£o nulas, aguardando atualizaÃ§Ã£o do HSM. Necessita verificaÃ§Ã£o manual.
supplygroup.field.issued.until.date.label=Emitido atÃ©
supplygroup.field.expiry.date.label=Data de validade
supplygroup.field.target=SGC/KRN de destino:
supplygroup.base.date.label=Data base
supplygroup.base.date.label.help=Data base usada para geraÃ§Ã£o de tokens STS6
supplygroup.target.deactivate.question=Este SGC/KRN estÃ¡ sendo desativado agora, mas ainda estÃ¡ sendo usado como um SGC/KRN alvo para outros. Continuar?
supplygroup.base.dates.no.check=Um SGC de destino estÃ¡ em jogo, mas uma ou ambas as datas base ainda nÃ£o foram atualizadas pelo HSM, portanto, nÃ£o podem ser validadas uma contra a outra. Continuar?
supplygroup.target.base.date.smaller.question= Data base do SGC > data base do SGC de destino. O alvo Ã© o SGC mais antigo. Continuar?
bulk.Keychange.extract.label.meterNum= NÃºmero do contador
bulk.Keychange.extract.label.userRef= ReferÃªncia do usuÃ¡rio
bulk.Keychange.extract.label.token1= Token1
bulk.Keychange.extract.label.token2= Token2
bulk.Keychange.extract.label.token3= Token3
bulk.Keychange.extract.label.token4= Token4
bulk.Keychange.extract.label.fromSupGroup= De SupGroup
bulk.Keychange.extract.label.fromKeyRev= De KeyRev
bulk.Keychange.extract.label.fromTariffIdx= De TariffIdx
bulk.Keychange.extract.label.fromBaseDate= De DataBase
bulk.Keychange.extract.label.toSupGroup= Para SupGroup
bulk.Keychange.extract.label.toKeyRev= Para Rev chave
bulk.Keychange.extract.label.toTariffIdx= Para Tarifa Idx
bulk.Keychange.extract.label.toBaseDate= atÃ© a data base
bulk.Keychange.extract.label.transDate=Data transacÃ§Ã£o
bulk.Keychange.extract.label.userRecEntered= Gerado pelo usuÃ¡rio
bulk.Keychange.extract.label.importFileName=Nome do ficheiro de importaÃ§Ã£o
bulk.Keychange.extract.label.bulkRef= ReferÃªncia em massa
bulk.Keychange.extract.none=Nenhuma alteraÃ§Ã£o de chave encontrada para este ficheiro de importaÃ§Ã£o
button.submit=Enviar
action.params.header.label=ParÃ¢metros
import.file.explanation=Carregar ficheiro contendo dados e/ou parÃ¢metros para aÃ§Ã£o
bulk.keychange.header=AlteraÃ§Ã£o de chave em massa: Nome do arquivo: {0}
bulk.keychange.to.header=KeyChange TO:
bulk.keychange.use.target= Use SGC/KRN de destino prÃ©-capturado
bulk.keychange.use.targetHelp= SGC/KRN alvo Ã© capturado na pÃ¡gina Supply Group no Menu de contadores. Se selecionado, o SGC/KRN de cada contador serÃ¡ mapeado para seu alvo. Se nÃ£o houver alvo para um SGC/KRN especÃ­fico, ocorrerÃ¡ um erro, a menos que tambÃ©m insira um SGC/KRN especÃ­fico no TO, entÃ£o ISSO serÃ¡ usado quando nÃ£o houver alvo.
bulk.keychange.use.target.selected.message= Pode selecionar 'Use Target', bem como selecionar valores de Supply Group. Se nÃ£o houver valores e um SGC/KRN nÃ£o tiver alvo, ocorrerÃ¡ um erro. Se inserir valores tambÃ©m, estes serÃ£o usados para aqueles sem alvo.
bulk.keychange.supplygroupcode=Novo CÃ³digo de Supply Group
bulk.keychange.supplygroupcode.help= Selecione o novo cÃ³digo supply group
bulk.keychange.supplygroupcode.error.required.no.target=Pelo menos, o CÃ³digo Supply Group deve ser selecionado.
bulk.keychange.tariffindex.required=O Ã­ndice tarifÃ¡rio Ã© obrigatÃ³rio
bulk.keychange.tariffindex=Novo Ãndice TarifÃ¡rio
bulk.keychange.tariffindex.help=Insira o Ã­ndice tarifÃ¡rio a ser alterado. ObrigatÃ³rio. Se um contador tiver atualmente um Ã­ndice tarifÃ¡rio diferente, uma alteraÃ§Ã£o de chave serÃ¡ gerada.
bulk.keychange.instruction.label=Gerar InstruÃ§Ã£o de Tokens de AlteraÃ§Ã£o de Chave
bulk.keychange.instruction.help=Emita instruÃ§Ãµes sobre quando os tokens de mudanÃ§a de chave devem ser gerados.
bulk.keychange.instruction.generate.keychanges.now=Gere e atualize Key Tokens agora
bulk.keychange.instruction.generate.keychanges.next.vend=Definido para gerar key tokens com a prÃ³xima venda
bulk.keychange.instruction.generate.keychanges.next.vend.after.date=Definido para gerar key tokens com a prÃ³xima venda DEPOIS de uma data....
bulk.keychange.generate.keychanges.next.vend.after.date=MudanÃ§a de chave com venda APÃS a data
bulk.keychange.generate.keychanges.next.vend.after.date.help=MudanÃ§as de chave serÃ£o geradas em vend_time, mas somente DEPOIS desta data
bulk.keychange.after.date.error=A data posterior nÃ£o pode estar no passado. CÃ³digo verificado contra now() sendo {0}
bulk.keychange.after.date.required=A data posterior Ã© necessÃ¡ria para a instruÃ§Ã£o em massa selecionada
bulk.keychange.overwrite.existing=Substituir os detalhes existentes do Novo SGC/KRN?
bulk.keychange.overwrite.existing.help=Deve selecionar o botÃ£o de opÃ§Ã£o para lidar com o caso quando jÃ¡ existem novos detalhes do Supply Group em um contador: Substituir e continuar, ou deixar os detalhes existentes e abortar a instruÃ§Ã£o de mudanÃ§a de chave para tais contadores.
bulk.keychange.overwrite.existing.error=Deve escolher um, substituir ou nÃ£o.
import.upload.keychange.permission.needed=Este usuÃ¡rio nÃ£o tem permissÃ£o para fazer upload em massa de instruÃ§Ãµes KeyChange.
import.upload.cannot.change.action.params.now=Uma importaÃ§Ã£o foi registrada, os parÃ¢metros de aÃ§Ã£o nÃ£o podem mais ser editados, apenas visualizados. Para refazer itens com parÃ¢metros de aÃ§Ã£o diferentes, serÃ¡ necessÃ¡rio reimportÃ¡-los em um novo ficheiro.
import.upload.view.params.label=ParÃ¢metros
button.process.selected=Processo selecionado
button.process.all=Processar tudo
button.import.extract.keychanges=Extrair alteraÃ§Ãµes de chave geradas
bulk.keychange.without.input.file.not.implemented.yet=geraÃ§Ã£o de chave em massa precisa de um ficheiro de entrada de contadores. ConfiguraÃ§Ã£o do banco de dados has_input_file implementada apenas como 'y' para este tipo de ficheiro ainda.
import.items.selected.success=Itens selecionados processados com sucesso.
import.items.errors= Nem todos os itens foram processados com sucesso.
import.items.all.success=Todos os itens ImportFile foram processados com sucesso.
import.extract.items.non=Nenhum item para extrair
import.extract.items.exception=Falha na extraÃ§Ã£o. Entre em contato com o Suporte.
import.upload.twirly.waiting.text.keychange=As solicitaÃ§Ãµes de mudanÃ§a de chave podem demorar um pouco
import.upload.keychange.bulkref.label= KeyChange BulkRef
meter.txn.bulk.import.file.name=Nome do ficheiro de importaÃ§Ã£o

# renciac: 2021-06-01 | Planio 12963 Extension to Generic upload framework for action params
import.file.parameters.needed= Por favor, confirme e envie os parÃ¢metros para a importaÃ§Ã£o desses dados.
import.file.parameters.needed.no.data=Confirme e envie parÃ¢metros para alteraÃ§Ãµes em massa.\nUm nome de ficheiro fictÃ­cio serÃ¡ gerado.\nSe os dados forem necessÃ¡rios, bem como a importaÃ§Ã£o de parÃ¢metros, serÃ¡ rejeitada.
import.file.param.no.data.dummy.filename= O nome do ficheiro gerado Ã© {0}
import.file.no.parameters=Este tipo de ficheiro {0} nÃ£o tem UI definida para parÃ¢metros de aÃ§Ã£o. Entre em contato com o suporte. Ficheiro fictÃ­cio criado.
import.file.no.parameters.or.setting=Este tipo de ficheiro {0} nÃ£o tem interface do usuÃ¡rio definida para parÃ¢metros de aÃ§Ã£o ou a configuraÃ§Ã£o do banco de dados para has_input_file deve ser {1}. Entre em contato com o suporte.
import.file.params.save.error=NÃ£o Ã© possÃ­vel salvar o ficheiro de importaÃ§Ã£o com parÃ¢metros de aÃ§Ã£o.
import.file.no.params.converter.error=NÃ£o hÃ¡ conversor de parÃ¢metro JSON para os parÃ¢metros de aÃ§Ã£o deste ficheiro de importaÃ§Ã£o
import.file.get.params.fail=A configuraÃ§Ã£o do painel de parÃ¢metros falhou. Verifique o nome do ficheiro e o tipo de ficheiro.
import.file.parameters.updated=Os parÃ¢metros foram atualizados para o nome do arquivo: {0}
import.upload.file.settings.conflict=Nenhum ficheiro foi selecionado para ser carregado e hÃ¡ conflito nas configuraÃ§Ãµes de tipo de ficheiro em massa: os dados de entrada sÃ£o n ou b, mas os parÃ¢metros de aÃ§Ã£o sÃ£o falsos
import.upload.file.no.input.data=ConfiguraÃ§Ã£o de tipo de ficheiro em massa has_input_file = 'n', nenhum nome de ficheiro Ã© necessÃ¡rio.
import.upload.file.needs.action.params=Este tipo de ficheiro requer parÃ¢metros de aÃ§Ã£o. Capture na pÃ¡gina de upload.

# thomasn: 2021-08-16 | planio-10426
usagepoint.ps.start.date.lbl=Data de inÃ­cio
usagepoint.ps.name.lbl=Estrutura de preÃ§os
usagepoint.ps.start.date.help=A data em que essa estrutura de preÃ§os serÃ¡ ativada. Esta data de inÃ­cio Ã© Ãºnica.
usagepoint.ps.start.date.error=A Data de inÃ­cio deve estar no futuro e ser posterior Ã  data de inÃ­cio da estrutura tarifÃ¡ria atualmente ativa e Ã  data de inÃ­cio da primeira tarifa.
usagepoint.ps.start.date.error.unique=Data de inÃ­cio deve ser Ãºnica. JÃ¡ existe uma estrutura de preÃ§os com esta data de inÃ­cio.
usagepoint.ps.view.all.btn=Ver tudo
usagepoint.ps.delete.btn=Apagar
usagepoint.ps.delete.btn.confirm=Tem certeza de que deseja excluir a futura estrutura de preÃ§os?
usagepoint.ps.save.error=Falha ao salvar a estrutura de preÃ§o do ponto de medida
usagepoint.ps.delete.error=Falha ao excluir a estrutura de preÃ§o do ponto de medida
usagepoint.ps.delete.success=Estrutura de preÃ§o de ponto de medida excluÃ­da com sucesso.
usagepoint.ps.required=Estrutura de preÃ§os necessÃ¡ria.
usagepoint.ps.future.required=A estrutura de preÃ§os futura nÃ£o deve ser a mesma que a atual acima.
usagepoint.ps.future.list.help=Selecione a estrutura de preÃ§os e defina uma data de inÃ­cio para ela.
usagepoint.ps.future.date.help=Data de inÃ­cio para a estrutura de preÃ§o futura selecionada.
usagepoint.ps.future.lbl=Estrutura futura de preÃ§os
usagepoint.ps.future.start.date.lbl=Data de InÃ­cio Futura da Estrutura de PreÃ§os
usagepoint.ps.historic.error=O ponto de medida tem uma estrutura de preÃ§os histÃ³rica que nÃ£o Ã© compatÃ­vel com o modelo do contador. Use o novo ponto de medida.
meter.new.current.pricingstructure.required=O contador ({0}) nÃ£o suporta a estrutura de preÃ§os atual - {1}.
meter.new.current.pricingstructure.select=Estrutura de preÃ§os atual\n(se alterada, todas as futuras serÃ£o excluÃ­das)
tariff.error.save.up.ps.start.date.conflict=NÃ£o Ã© possÃ­vel atualizar a data de inÃ­cio da tarifa, estrutura de preÃ§os jÃ¡ adicionada ao ponto de medida com data de inÃ­cio {0}.
usagepointworkspace.error.meter.unsupported.model.current=O modelo do contador {0} nÃ£o suporta a estrutura de preÃ§o atual do ponto de medida.
warning.pricingStructure.billingDets.notsame.asmetermodel.mdc=AVISO: O(s) Determinante(s) de Faturamento coberto(s) pela(s) estrutura(s) tarifÃ¡ria(s) atual(is) Ã©(sÃ£o) uma correspondÃªncia PARCIAL aos utilizados pelos Canais MDC conectados ao Modelo de contador. Continuar?
warning.pricingStructure.billingDets.notsame.asmetermodel.mdc.save.UP=AVISO AO SALVAR: O(s) Determinante(s) de Faturamento coberto(s) pela(s) estrutura(s) tarifÃ¡ria(s) atual(is) Ã©(sÃ£o) uma correspondÃªncia PARCIAL daquela(s) utilizada(s) pelos Canais MDC conectados ao Modelo de contador. Continuar?
warning.pricingStructure.billingDets.notsame.asmetermodel.mdc.activate.UP=AVISO AO ACTIVAR: O(s) Determinante(s) de FacturaÃ§Ã£o coberto(s) pela(s) estrutura(s) tarifÃ¡ria(s) actual(is) corresponde(m) PARCIALMENTE aos utilizados pelos Canais MDC ligados ao Modelo de contador. Continuar?
error.pricingStructure.billingDets.notsame.asmetermodel.mdc=ERRO: O(s) Determinante(s) de Faturamento abrangido(s) pela(s) estrutura(s) tarifÃ¡ria(s) vigente(s) NÃO TEM CORRESPONDÃNCIA aos utilizados pelos Canais MDC conectados ao Modelo de contador. Se nÃ£o for uma correspondÃªncia exata, deve haver pelo menos uma correspondÃªncia.
error.pricingStructure.billingDets.notsame.asmetermodel.mdc.save.UP=ERRO AO SALVAR: O(s) Determinante(s) de Faturamento abrangido(s) pela(s) estrutura(s) tarifÃ¡ria(s) vigente(s) NÃO TEM CORRESPONDÃNCIA aos utilizados pelos Canais MDC conectados ao Modelo de contador. Se nÃ£o for uma correspondÃªncia exata, deve haver pelo menos uma correspondÃªncia.
error.pricingStructure.billingDets.notsame.asmetermodel.mdc.activate.UP=ERRO AO ACTIVAR: O(s) Determinante(s) de Faturamento abrangidos(as) pela(s) estrutura(s) tarifÃ¡ria(s) vigente(s) NÃO TEM CORRESPONDÃNCIA aos utilizados pelos Canais MDC conectados ao Modelo de contador. Se nÃ£o for uma correspondÃªncia exata, deve haver pelo menos uma correspondÃªncia.
question.confirm.installation.date.3=A alteraÃ§Ã£o da data de ativaÃ§Ã£o entra em conflito com as estruturas de preÃ§os. O PS atual serÃ¡ excluÃ­do e o PS futuro serÃ¡ atualizado.

# marcod: 2021-07-27 | Planio 12179
supplygroup.field.kmc.expirydate=Data de ExpiraÃ§Ã£o do KMC
supplygroup.panel.kmc.expirydate.help=O sistema enviarÃ¡ automaticamente um aviso por e-mail todos os dias para a venda de chaves que estÃ£o prestes a expirar.

# renciac: 2021-07-21 | Planio 13124 Debt Instalments
aux.charge.sched.cycle.label=Ciclo de Carga
aux.charge.sched.cycle.label.help=Para Ad-hoc, carregue a cada venda. Para DiÃ¡ria/Mensal determina o ciclo de quando as cobranÃ§as sÃ£o cobradas
aux.charge.sched.cycle.amount.label=Quantidade do ciclo
aux.charge.sched.cycle.amount.label.help=Um valor ad hoc Ã© cobrado em cada venda. Os valores do ciclo diÃ¡rio ou mensal serÃ£o cobrados uma vez ao dia ou uma vez ao mÃªs, dependendo do ciclo; atÃ© que a DÃ­vida seja paga.
aux.charge.sched.cycle.select.error= A seleÃ§Ã£o do ciclo de carga deve acompanhar a entrada do valor do ciclo de carga
aux.charge.sched.cycle.instalment.label=As parcelas do ciclo diÃ¡rio ou mensal sÃ£o entradas de valor independentes
customer.auxaccount.start.date.lbl=Data de inÃ­cio
customer.auxaccount.start.date.help=Selecione a data de inÃ­cio da conta auxiliar
customer.auxaccount.suspend.until.help=Suspenda temporariamente uma conta auxiliar atÃ© a data definida neste campo. Para programaÃ§Ãµes de cobranÃ§a de Parcelas de DÃ©bitos, observe a configuraÃ§Ã£o do aplicativo para 'Acumular parcelas auxiliares durante a suspensÃ£o'.
customer.auxaccount.last.charge.date.lbl=Data da Ãºltima cobranÃ§a
customer.auxaccount.last.charge.date.help=Ãltima data em que foi feita uma transaÃ§Ã£o de venda paga para esta conta. NÃ£o reflete pagamentos manuais ou ajustes de conta, apenas pagamentos reais por meio de vendas/recargas. Observe que, para cronogramas de cobranÃ§a que NÃO sÃ£o parcelados, isso pode ter sido um pagamento parcial, nÃ£o uma cobranÃ§a total.
error.field.value.monthly.charge= O valor deve ser PRO_RATA, FREE ou FULL
customer.auxaccount.suspend.until.smlr.start= A data de suspensÃ£o nÃ£o deve ser anterior Ã  data de inÃ­cio
customer.auxaccount.install.suspend.info= programaÃ§Ã£o de cobranÃ§a usando o Parcelamento segue um comportamento especÃ­fico apÃ³s a suspensÃ£o. \r\nConsulte appSetting 'accumulate_aux_during_suspension'
vend.older.trans.info=Observe que ao reverter uma transaÃ§Ã£o que NÃO Ã© a Ãºltima transaÃ§Ã£o do contrato do cliente, os detalhes da Ãºltima compra nÃ£o sÃ£o redefinidos e a intervenÃ§Ã£o manual Ã© necessÃ¡ria para transaÃ§Ãµes futuras no mesmo mÃªs. A data do Ãºltimo pagamento auxiliar nas contas auxiliares tambÃ©m nÃ£o Ã© zerada.
vend.reversal.last.with.older=Observe que houve vÃ¡rias reversÃµes neste mÃªs, os detalhes da Ãºltima compra nÃ£o foram redefinidos e Ã© necessÃ¡ria intervenÃ§Ã£o manual para futuras transaÃ§Ãµes no mesmo mÃªs. A data do Ãºltimo pagamento auxiliar nas contas auxiliares tambÃ©m nÃ£o Ã© zerada.
auxaccount.upload.startDate=Data de inÃ­cio
auxaccount.upload.startDate.greater=Data de inÃ­cio nÃ£o pode ser maior que Suspender atÃ©
auxaccount.upload.startDate.format=A data de inÃ­cio deve estar vazia ou formatada corretamente
auxaccount.upload.startDate.invalid.date=Data de inÃ­cio Ã© uma data invÃ¡lida
auxaccount.upload.suspendUntil.invalid.date=Suspender atÃ© que seja uma data invÃ¡lida
trans.bulk.upload.format.error.trans.date=A data da transaÃ§Ã£o deve estar vazia (o padrÃ£o Ã© a data de processamento) ou formatada corretamente (aaaa-MM-dd HH:mm:ss)
trans.bulk.upload.invalid.trans.date=data da transaÃ§Ã£o Ã© invÃ¡lida

# marcod: 2021-07-08 | Planio 12735
error.usagepoint.outdated=ERRO: os dados deste separador estÃ£o desatualizados devido a outra atualizaÃ§Ã£o. Clique em recarregar para atualizar os dados.
button.reload=recarregar

# jacciedt: 2021-04-15 | Planio 12792
sts.unit.generation.limit.error=A quantidade de unidades a serem emitidas nÃ£o pode ser maior que a quantidade configurada de {0} unidades.

# thomasn: 2021-07-22 | Planio 5812
usagepoint.meter.inspection.request.btn=Enviar SolicitaÃ§Ã£o de InspeÃ§Ã£o do Contador
usagepoint.meter.inspection.request.setup.error=O contrato nÃ£o estÃ¡ completo, confirme se os dados do endereÃ§o fÃ­sico estÃ£o lÃ¡.
usagepoint.meter.inspection.request.processing.error=Ocorreu um erro ao processar a solicitaÃ§Ã£o.
usagepoint.meter.inspection.request.meterMng000=Pedido de inspeÃ§Ã£o do contador processado OK. ReferÃªncia={0}
usagepoint.meter.inspection.request.meterMng001=Erro geral na solicitaÃ§Ã£o de inspeÃ§Ã£o do contador. ReferÃªncia={0}
usagepoint.meter.inspection.request.meterMng011=Erro na solicitaÃ§Ã£o de inspeÃ§Ã£o do contador, dados do cliente incompletos ou invÃ¡lidos. ReferÃªncia={0}
usagepoint.meter.inspection.request.txt.comment=Digite comentÃ¡rio
usagepoint.meter.inspection.request.txt.comment.help=Uma descriÃ§Ã£o do motivo.

# jacciedt: 2021-04-15 | Planio 9695
no.aux.charge.schedule.defined=Nenhuma programaÃ§Ã£o de carga auxiliar definida

# jacciedt: 2021-03-05 | Planio 12340
customer.txn.error.tax.more.than.amount=O valor do imposto nÃ£o pode ser maior do que o valor incluindo impostos
customer.auxaccount.error.refund=O novo saldo apÃ³s o ajuste serÃ¡ {0}. VocÃª nÃ£o tem permissÃ£o para transformar esta conta em um reembolso.
customer.auxaccount.error.debt=O novo saldo apÃ³s o ajuste serÃ¡ {0}. VocÃª nÃ£o tem permissÃ£o para transformar esta conta em uma dÃ­vida.

# marcod: 2021-07-15 | Planio 13708
reprint.customer=Cliente

# thomasn: 2021-08-18 | Planio 13381
meter.txn.engineeringtokens.column=Possui tokens de engenharia

# patrickm: 2021-07-02 | Planio 13126
supplygroup.field.code.default=PadrÃ£o

# jacciedt: 2021-06-30 | Planio 12839
up_meter_install.remove.date=Remover Data
up_meter_install.install.ref=Ref. de instalaÃ§Ã£o
up_meter_install.remove.ref=Remover referÃªncia
up_meter_install.header=InstalaÃ§Ãµes do Contador no Ponto de Medida.
up_meter_install.sub.header=InstalaÃ§Ãµes anteriores do contador no ponto de medida feitas neste ponto de medida.

# renciac: 2021-06-28 | Planio 13515 Writeoff charges duplicates
usagepoint.charge.button.close.writeoff.and.unassign.customer=Fechar e cancelar a atribuiÃ§Ã£o do cliente

# marcod: 2021-05-25 | Planio 12620
search.meter.sgc.label1=Supply Group / RevisÃ£o de Chave
search.meter.sgc.label2=(Atual ou Novo)
search.meter.sgc.help=A busca encontrarÃ¡ contadores com um supply group atual ou novo igual ao SGC/KRN selecionado. Um novo SGC/KRN em um contador STS Ã© preenchido somente quando uma mudanÃ§a de chave Ã© acionada. A partir da prÃ³xima venda, torna-se a atual.

# jacciedt: 2021-02-16 | Planio 11622
defaultAccountAdjustmentProcessor.notification.lowBalanceNoUsagePoint.email.subject=Saldo da conta baixo para a conta {12} no contrato {5}
defaultAccountAdjustmentProcessor.notification.lowBalanceNoUsagePoint.email.message=Prezado cliente,\n\nO status da sua conta Ã©: \nSaldo da conta: {7,nÃºmero,moeda}\n Limite de notificaÃ§Ã£o de saldo baixo: {8,nÃºmero,moeda}\n\nAtenciosamente,\nEquipe de suporte
defaultAccountAdjustmentProcessor.notification.lowBalanceNoUsagePoint.sms.message=O saldo da conta {12} no contrato {5} estÃ¡ acabando. Saldo Ã© {7,number,currency} Atenciosamente, Equipe de suporte
bill.payments=Pagamento de conta
bill.payments.provider=Fornecedor
bill.payments.reversal.request.received=SolicitaÃ§Ã£o de reversÃ£o recebida
bill.payments.pay.type=Tipo de Pagamento
bill.payments.pay.type.details=Detalhes do tipo de pagamento
bill.payments.vote.name=Votar nome
bill.payments.description=Uma lista de pagamentos de contas feitos por este cliente
bill.payments.transaction.type=Tipo de TransaÃ§Ã£o de Pagamento de Conta

# renciac: 2021-05-25 | Planio 13153 PS date validation vs installDate
usagepoint.installation.date.before.tariff.start1=A data de instalaÃ§Ã£o Ã© anterior Ã  data de inÃ­cio da tarifa {0}. <br/> A implicaÃ§Ã£o Ã© que os possÃ­veis encargos tarifÃ¡rios devidos antes da data de inÃ­cio da tarifa nÃ£o serÃ£o calculados.
usagepoint.installation.date.before.tariff.start2=<br/> A Ãºltima data em que as cobranÃ§as cÃ­clicas foram calculadas foi {0}.
usagepoint.installation.date.before.tariff.start3=<br/> Nenhuma Ãºltima data de cÃ¡lculo de carga cÃ­clica no arquivo.
usagepoint.installation.date.before.tariff.start4=<br/> Continuar?

# renciac: 2021-04-20 | Planio 7918 Bulk Tariff Generator
error.field.customerdescription.max=A descriÃ§Ã£o do cliente deve ter menos de {max} caracteres.
button.export.ps.title= Exportar TODA a estrutura de preÃ§os Tarifas atuais
export.ps.failed.exception=A exportaÃ§Ã£o falhou. Entre em contato com o Suporte.
export.ps.failed.non=Nenhuma estrutura de preÃ§os com tarifas atuais para exportar ???
import.edit.item.update.bulk.tariff.success=Os dados da tarifa {0} foram atualizados com sucesso.
import.ps.name.label=Estrutura de preÃ§os
import.tariff.name.label=tarifa
import.tariff.edit.resave.error=Erro: calcContents: {0}
import.upload.file.already.uploaded=Erro: Arquivo jÃ¡ carregado. Verifique a tabela.
import.upload.tariff.permission.needed=Este usuÃ¡rio nÃ£o tem permissÃ£o para importar tarifas.

# patrickm: 2021-03-18 | Planio 11152
defaultAccountAdjustmentProcessor.notification.emergencyCredit.email.subject=Limite de crÃ©dito de emergÃªncia para {6}
defaultAccountAdjustmentProcessor.notification.emergencyCredit.email.message=Prezado cliente,\n\nO status da sua conta Ã©: \n Saldo da conta: {7,nÃºmero,moeda}\n Limite de notificaÃ§Ã£o de saldo baixo: {8,nÃºmero}\n Limite de crÃ©dito de emergÃªncia: {9,nÃºmero,moeda}\n \nAtenciosamente,\nEquipe de suporte
defaultAccountAdjustmentProcessor.notification.emergencyCredit.sms.message=O saldo de {6} estÃ¡ baixo e abaixo do limite de crÃ©dito de emergÃªncia. Saldo Ã© {7,number,currency} Atenciosamente, Equipe de suporte

# jacciedt: 2021-03-12 | Planio 12494
error.field.validity.message.content=O conteÃºdo da mensagem nÃ£o pode ficar em branco.

# jacciedt: 2021-03-15 | Planio 11902
customer.account.does.not.exist=A conta do cliente ainda nÃ£o existe

# renciac: 2021-03-04 | Planio 12582
bulk.ignore.dup.meters=Ignorar contadores duplicados
bulk.ignore.dup.meters.help=Se o nÃºmero do contador jÃ¡ existir no banco de dados, ignore um no arquivo de upload. Se duplicar no arquivo de upload, use o primeiro.
bulk.upload.ignore.meter.dups.changed=A configuraÃ§Ã£o de ignorar contadores duplicados foi alterada entre as etapas! Foi {0}; agora {1}
bulk.upload.successful.meter.upload=Total de {0} carregamentos de contadores processados com sucesso, {1} duplicados ignorados.

# marcod: 2021-02-01 | Planio 12168
email.password.reset.message=Recebemos uma solicitaÃ§Ã£o de redefiniÃ§Ã£o de senha para {0}.<br> Para redefinir sua senha, clique no link abaixo.<br> {1}.
password.link.expired=O link de redefiniÃ§Ã£o de senha expirou. VocÃª pode solicitar outro.
password.link.used=O link de redefiniÃ§Ã£o de senha foi desativado.
password.change.now=Alterar senha agora
password.reset.success=Sua senha foi alterada com sucesso.

# jacciedt: 2021-02-12 | Planio 12340
unitsacc.balance.with.symbol=Saldo de Unidades ({0})

# jacciedt: 2021-02-04 | Planio 12330
bulk.upload.heading.metercustup=Gerar template para upload em massa do MeterCustUp
bulk.upload.metercustup.notice=Para uploads em massa de contador/cliente/ponto de medida, use a opÃ§Ã£o de menu -> ConfiguraÃ§Ã£o -> Carregar e importar arquivos

# jacciedt: 2021-02-05 | Planio 11950
demo.addmeterreadings.tariffCalc.failed={0} : {1} adicionado com sucesso, mas o cÃ¡lculo da tarifa falhou.
demo.addmeterreadings.success={0}: {1} adicionado com sucesso.
demo.addmeterreadings.tariffCalc.success={0} : {1} adicionado com sucesso e cÃ¡lculo de tarifa concluÃ­do.

# jacciedt: 2021-01-08 | Planio 12082
bulk.upload.unitsaccountname=Nome da conta de unidades
bulk.upload.unitslowbalancethreshold=Limite de baixo saldo de Unidades
bulk.upload.unitsnotificationemail=E-mail de NotificaÃ§Ã£o de Unidades
bulk.upload.unitsnotificationphone=Telefone de NotificaÃ§Ã£o de Unidades

# jacciedt: 2020-12-22 | Planio 11146
error.date.field.invalid=O valor inserido nÃ£o Ã© uma data vÃ¡lida. Formato = {0}

# renciac: 2020-12-10 | Planio 11365
### Units Account ###
unitsacc.title=Conta de Unidades
unitsacc.name.help=Insira um nome para esta conta
unitsacc.name=Nome da conta de unidades
unitsacc.balance.help=O saldo atual das unidades
unitsacc.balance=Saldo de Unidades
unitsacc.sync.help=Sincronize o saldo das unidades com o saldo das unidades no contador
unitsacc.sync=Sincronizar Saldo
unitsacc.low.balance.threshold.help=Quando o saldo das unidades atingir esse limite, uma mensagem serÃ¡ enviada ao cliente.
unitsacc.low.balance.threshold=Limite de baixo saldo de Unidades
unitsacc.notification.email.help=Uma lista separada por vÃ­rgulas de endereÃ§os de e-mail para os quais as notificaÃ§Ãµes relacionadas Ã s unidades podem ser enviadas (por exemplo, quando o limite de saldo baixo foi atingido)
unitsacc.notification.email=EndereÃ§os de e-mail de notificaÃ§Ã£o
unitsacc.notification.phone.help=Uma lista separada por vÃ­rgulas de nÃºmeros de telefone para os quais as notificaÃ§Ãµes relacionadas Ã s unidades podem ser enviadas (por exemplo, quando o limite de saldo baixo foi atingido)
unitsacc.notification.phone=NÃºmeros de telefone de notificaÃ§Ã£o
unitsacc.note=Uma conta de unidades sÃ³ Ã© necessÃ¡ria se o modelo de contador e a estrutura de preÃ§os exigirem.
unitsacc.required=* \= ObrigatÃ³rio
unitsacc.changes.cleared=As alteraÃ§Ãµes foram apagadas.
units.account.error.save=NÃ£o foi possÃ­vel salvar a conta das unidades.

# jacciedt: 2020-11-27 | Planio 11366
units.account=Conta de Unidades
units.account.transaction.history=HistÃ³rico da Conta de Unidades
units.account.transaction.description=TransaÃ§Ãµes anteriores da conta para esta conta de unidades
amount.cannot.be.zero=O valor nÃ£o pode ser zero
units.transaction.type=Tipo de transaÃ§Ã£o de unidades
units.transaction.type.sale=Venda
units.transaction.type.consumption=Consumo
units.transaction.type.manualadj=Ajuste Manual
units.transaction.type.reversal=ReversÃ£o

# renciac: 2020-11-25 | Planio 11363
# The defaultUnitsAdjustmentProcessor.notification messages has the same arguments as the defaultAccountAdjustmentProcessor, except units instead of currency
defaultUnitsAdjustmentProcessor.notification.disconnect.email.subject=O saldo da conta acabou para {6}
defaultUnitsAdjustmentProcessor.notification.disconnect.email.message=Prezado cliente,\n\nSeu contador serÃ¡ desconectado.\n\nO status da sua conta Ã©: \nSaldo da conta: {7,nÃºmero}\n Limite de notificaÃ§Ã£o de saldo baixo: {8,nÃºmero}\n\nAtenciosamente,\nEquipe de suporte
defaultUnitsAdjustmentProcessor.notification.disconnect.sms.message=O saldo de {6} acabou e serÃ¡ desconectado. O saldo Ã© {7, nÃºmero} Atenciosamente, Equipe de suporte
defaultUnitsAdjustmentProcessor.notification.lowBalance.email.subject=Saldo da conta baixo para {6}
defaultUnitsAdjustmentProcessor.notification.lowBalance.email.message=Prezado cliente,\n\nO status da sua conta Ã©: \n Saldo da conta: {7,nÃºmero}\n Limite de notificaÃ§Ã£o de saldo baixo: {8,nÃºmero}\n\nAtenciosamente,\nEquipe de suporte
defaultUnitsAdjustmentProcessor.notification.lowBalance.sms.message=O saldo de {6} estÃ¡ acabando. O saldo Ã© {7, nÃºmero} Atenciosamente, Equipe de suporte

# thomasn: 2020-10-16 | planio-9668
meter.models.battery.capacity=Capacidade
meter.models.battery.capacity.help=Digite o valor da capacidade total da bateria. Em %, meses, tensÃ£o etc.
meter.models.battery.capacity.error=O valor deve ser positivo.
meter.models.battery.threshold=Limite baixo
meter.models.battery.threshold.help=Digite a percentagem do limite de bateria fraca. Quando cruzado, um evento de bateria fraca serÃ¡ acionado.
meter.models.battery.threshold.error=O valor deve estar entre 0 e 100.
meter.models.battery.event.lbl=Evento de bateria

# renciac: 2020-10-16 | Planio 11204
usagepoint.installdate.change.error.readings=NÃ£o Ã© possÃ­vel alterar a data de instalaÃ§Ã£o, novas leituras no contador/ponto de medida, atualize a pÃ¡gina.
meter.replace.installation.date.error.trans=NÃ£o Ã© possÃ­vel substituir o contador com uma data de instalaÃ§Ã£o futura, novas transaÃ§Ãµes no ponto de medida ou novas leituras no contador; atualize a pÃ¡gina e tente novamente.

# renciac: 2020-06-09 | Planio 9616
meter.change.activation.date=Alterar data de ativaÃ§Ã£o = Nova data de instalaÃ§Ã£o?
meter.change.activation.date.required=Marque uma das caixas para Data de ativaÃ§Ã£o
meter.change.activation.date.error.trans=NÃ£o pode alterar data de ativaÃ§Ã£o, transaÃ§Ãµes no ponto de medida ou novas leituras no contador; por favor atualize a pÃ¡gina.
meter.change.installation.date.error.trans=NÃ£o pode alterar data de instalaÃ§Ã£o, transaÃ§Ãµes no ponto de medida ou novas leituras no contador; por favor atualize a pÃ¡gina.
error.field.installdate.future.trans.or.possible.gap=NÃ£o pode ter uma data de instalaÃ§Ã£o futura. SÃ³ pode ser no futuro se nÃ£o tiver transaÃ§Ãµes no UP e se tiver optado por alterar a data de ativaÃ§Ã£o = data de instalaÃ§Ã£o.

# marcod : 2020-09-09 | Planio 10498
meter.units.help=Digite o nÃºmero de {0} unidades com apenas uma casa decimal.

# marcod : 2020-10-08 | Planio 9723
meter.number.suggestion.help=Comece a digitar o nÃºmero do contador, os contadores que comeÃ§am com esses dÃ­gitos aparecerÃ£o em uma lista suspensa. Clique em um para selecionÃ¡-lo.

# thomasn: 2020-09-01 | Planio 8404
auxaccount.upload.suspendUntil=Suspender atÃ©
auxaccount.upload.suspendUntil.in.past=Suspender AtÃ© nÃ£o pode estar no passado
auxaccount.upload.suspendUntil.format=Suspender atÃ© deve estar vazio ou formatado corretamente
customer.auxaccount.suspend.until.lbl=Suspender atÃ©
customer.auxaccount.suspend.until.error=A data deve estar no futuro.
customer.auxaccount.suspend.until=Suspenso atÃ©
customer.auxaccount.txn.history.suspend.until=Suspenso atÃ©: {0}

# jacciedt: 2020-08-13 | Planio 10007
auxtype.error.update.in.use=NÃ£o Ã© possÃ­vel desativar o tipo auxiliar, jÃ¡ estÃ¡ em uso.

# jacciedt: 2019-01-29 | Planio 8575
bulk.upload.invalid.regex={0} nÃ£o corresponde ao seu padrÃ£o regex

# jacciedt: 2020-06-17 | Planio 9605
demo.addmeterreadings.weekly=Semanalmente

# jacciedt: 2020-04-09 | Planio 6150
customer.unassign.unassign.customer=Cancelar atribuiÃ§Ã£o de cliente
usagepoint.charge.button.writeoff.and.unassign.customer=Encargos de baixa e cancelamento de atribuiÃ§Ã£o de cliente

# joelc: 2020-07-10 | Planio 9609
reprint.remaining.balance=Saldo
reprint.desc=DescriÃ§Ã£o

# renciac: 2019-12-03 | Planio 5311
file.item.panel.reg.read.reminder=LEMBRETE: Quaisquer contadores com tarifas de leitura de registro podem precisar de uma leitura inicial.
channel.readings.header.up= Ponto de medida:
channel.readings.timestamp.label=Lendo Registo HorÃ¡rio
channel.readings.timestamp.help= O o registo horÃ¡rio de leitura deve ser igual Ã  data de instalaÃ§Ã£o OU maior que o registo horÃ¡rio de leitura existente para esta instalaÃ§Ã£o do contador
channel.readings.table.error.heading=Erro
channel.readings.partial.entry=As leituras iniciais do registro para os canais deste contador nÃ£o estÃ£o concluÃ­das ou estÃ£o apenas parcialmente concluÃ­das. VocÃª quer salvar o que vocÃª tem e completar o resto mais tarde?
channel.readings.preExisting.note= NOTA: Existem leituras prÃ©-existentes para esta instalaÃ§Ã£o do contador e do ponto de medida.
channel.readings.preExisting.same.mdc.channels=Estes sÃ£o dos mesmos Canais MDC, conforme abaixo na tabela. \nPressione CANCEL para mantÃª-los como estÃ£o.\nSe desejar alterar as leituras iniciais, insira novos valores. \nObservaÃ§Ã£o: A leitura do registo horÃ¡rio deve ser > anterior.
channel.readings.preExisting.diff.mdc.channels=Estes parecem ser de Canais MDC anteriores.\nA Ãºltima data de leitura encontrada foi: {0}.\nDigite novos valores para os novos canais MDC. Nota: O registo horÃ¡rio de leitura deve ser > Data da Ãºltima leitura anterior.
channel.readings.preExisting.note.end= \nSe nenhuma dessas opÃ§Ãµes for desejÃ¡vel, entre em contato com o Suporte do sistema.
channel.readings.timestamp.install.date=A leitura de registo horÃ¡rio deve ser igual Ã  data de instalaÃ§Ã£o.
channel.readings.timestamp.previous.date=A leitura de registo horÃ¡rio deve ser posterior Ã  data de leitura anterior: {0}
button.ok=OK

warning.change.mdc.on.meter.NO.DATA=ATENÃÃO: Alterar o MDC no meterModel pode afetar {0} pontos de uso ativos e {1} inativos que nÃ£o possuem Tarifas de Leitura de Cadastro. Continuar?
warning.change.mdc.on.meter.PARTIAL.or.TOTAL.active.up=AVISO: Alterar o MDC no meterModel pode afetar {0} pontos de uso ativos e {1} inativos com tarifa de leitura de registro e correspondÃªncia de determinante de cobranÃ§a PARCIAL ou EXATO. TambÃ©m nenhuma correspondÃªncia em pontos de uso inativos pode ser alterada aqui (quando eles sÃ£o ativados, nenhuma correspondÃªncia serÃ¡ rejeitada). Continuar?
error.change.mdc.on.meter.NONE.MATCH.active.up=ERRO: Alterar o MDC neste meterModel tem {0} pontos de medida ATIVOS com Tarifas de Leitura de Registro e SEM CORRESPONDÃNCIA nos determinantes de faturamento de alguns. NÃ£o Ã© possÃ­vel alterar o mdc no modelo de contador agora.

warning.change.mdc.channel.NO.DATA=AVISO: Alterar / (atribuir um novo) canal neste MDC pode afetar {0} pontos de medida ativos e {1} inativos que nÃ£o possuem Tarifas de Leitura de Registro. Continuar?
warning.change.mdc.channel.PARTIAL.or.TOTAL.with.regreadPS=AVISO: Alterar / (atribuir um novo) canal neste MDC pode afetar {0} pontos de medida ativos e {1} inativos com Tarifa de Leitura de Registro e correspondÃªncia PARCIAL ou EXATA do determinante de cobranÃ§a. Continuar?
error.change.mdc.channel.NONE.MATCH.active.up=ERRO: Alterar / (atribuir um novo) canal neste MDC tem {0} pontos de medida ATIVOS com tarifas de leitura de registro e SEM CORRESPONDÃNCIA nos determinantes de faturamento de alguns. NÃ£o Ã© possÃ­vel alterar os canais / determinantes de cobranÃ§a agora.

warning.change.mdc.NO.DATA=ATENÃÃO: A alteraÃ§Ã£o do MDC afetarÃ¡ {0} pontos de medida ativos e {1} inativos que nÃ£o possuem Tarifas de Leitura de Cadastro. Continuar?
warning.change.mdc.inactive.with.regreadPS=ATENÃÃO: A alteraÃ§Ã£o do MDC pode afetar {0} pontos de medida ativos e {1} inativos com Tarifas de Leitura de Registro. Continuar?
error.deactivated.mdc.active.up= ERRO: NÃ£o Ã© possÃ­vel desactivar este MDC - tem {0} pontos de medida activos e {1} inactivos com Tarifas de Leitura de Registo.

warning.change.or.new.tariff.has.diff.billing.dets.to.active.up.with.ps=ATENÃÃO: A alteraÃ§Ã£o/criaÃ§Ã£o de um novo tarifÃ¡rio de Leitura de Registos nesta Estrutura de TarifÃ¡rio tem correspondÃªncia PARCIAL dos determinantes de faturaÃ§Ã£o com alguns dos pontos de medida ativos utilizando a estrutura de tarifÃ¡rio. Continuar?
error.change.or.new.tariff.has.diff.billing.dets.to.active.up.with.ps=ERRO: A alteraÃ§Ã£o/criaÃ§Ã£o de um novo tarifÃ¡rio de Leitura de Registos nesta Estrutura de PreÃ§os NÃO CORRESPONDE aos determinantes de faturaÃ§Ã£o de alguns dos pontos de medida ativos que utilizam a estrutura de preÃ§os. Deve haver pelo menos uma correspondÃªncia parcial em TODOS os casos.
warning.change.status.billing.det.but.in.use=Alterar o status deste determinante de faturamento pode afetar {0} pontos de medida ativos e {1} inativos com contadores atribuÃ­dos a um modelo de contador que usa um MDC com canais atribuÃ­dos a este determinante de faturamento. Continuar?
error.cannot.activate.up.model.not.channels.with.regread.ps=ERRO! NÃ£o Ã© possÃ­vel ativar o Ponto de Medida. O modelo de contador e a estrutura tarifÃ¡ria devem ter pelo menos um canal com o mesmo determinante de faturamento em comum.
error.incompatible.model.with.ps=ERRO! O modelo do contador e a estrutura de preÃ§os nÃ£o sÃ£o compatÃ­veis. \nOu o modelo do contador possui canais, mas nÃ£o possui estrutura de preÃ§os de leitura de registro ou vice-versa.

# renciac: 2019-11-07 | Planio 7951
channel.field.maxsize.help=Leitura mÃ¡xima no registro do contador antes do rollover para zero
channel.field.time.interval.help=Intervalo de tempo das leituras do registrador
channel.field.time.interval=Intervalo de tempo
channel.config.header=SubstituiÃ§Ãµes de canal
channel.config.overrides.button=Substituir Canais MDC
channel.config.mdc.titlename=Mdc
channel.config.field.titlename=SubstituiÃ§Ã£o do Canal do contador
channel.config.title.add=Adicionar SubstituiÃ§Ã£o do Canal do contador
channel.config.title.update=Atualizar SubstituiÃ§Ã£o do Canal do contador
channel.field.titlename.help=Mostra apenas canais MDC ainda nÃ£o substituÃ­dos - deve selecionar um canal MDC para substituir
channel.config.field.billingdetnames=Determinante(s) de Faturamento
channel.config.error.delete=NÃ£o Ã© possÃ­vel excluir a substituiÃ§Ã£o do canal do contador. Entre em contato com o suporte
channel.config.delete.confirm=Tem certeza de que deseja excluir a substituiÃ§Ã£o do canal do contador?
channel.config.deleted=A substituiÃ§Ã£o do canal do contador foi excluÃ­da com sucesso.
mdc.channel.override.metermodels=SubstituiÃ§Ã£o do(s) Modelo(s) de contador(es)
mdc.channel.override.metermodels.none=Sem SubstituiÃ§Ãµes
channel.config.already.override= O canal jÃ¡ tem substituiÃ§Ã£o. Selecionado de cima.
channel.field.meter.reading.type.title=Tipo de leitura
meter.model.change.mdc.confirm.delete.configs=O modelo do contador tinha substituiÃ§Ãµes de canal para o MDC anterior neste modelo. IrÃ¡ excluÃ­-los se prosseguir com a alteraÃ§Ã£o do MDC. Continuar?
meter.model.channel.configs.error.delete=NÃ£o Ã© possÃ­vel excluir as substituiÃ§Ãµes do canal do contador para o MDC anterior. Entre em contato com o suporte
channel.config.no.channels.to.override= Nenhum canal definido para este MDC.
mdc.channel.status.change.warn.overrides=Nota: este canal mdc tem substituiÃ§Ãµes. Continuar com a mudanÃ§a de status?
mdc.channel.field.time.interval=Intervalo de Tempo MdcChannel
channel.override.field.time.interval=Substituir intervalo de tempo
mdc.channel.field.maxsize=Tamanho mÃ¡ximo de leitura do MdcChannel
channel.override.field.maxsize=Substituir tamanho mÃ¡ximo de leitura
mdc.channel.field.reading_multiplier=Multiplicador de Leitura MdcChannel
channel.override.field.reading_multiplier=Substituir multiplicador de leitura
import.edit.reg.Read.item.update.success=Dados para o contador {0}, lendo Timestamp {1} atualizados com sucesso.
import.reg.Read.channel.value.label=Valor do Canal
import.reg.Read.timestamp.label=Lendo Registo HorÃ¡rio
reg.read.init.readings.cancel.confirm=Cancelar significa que nÃ£o serÃ£o salvas mais leituras iniciais para os canais (registros) deste contador e os cÃ¡lculos de tarifa usarÃ£o as primeiras leituras ou leituras existentes como leituras iniciais. Continuar?
channel.readings.import.note=ObservaÃ§Ã£o: As leituras do registro tambÃ©m podem ser importadas manualmente.

# barryc: 2020-05-12 | Planio 8211
tariff.error.monthlycost.name=Especifique um nome para a cobranÃ§a
tariff.error.monthlycost=Um nÃºmero vÃ¡lido deve ser fornecido
tariff.error.monthlycost.positive=O valor deve ser positivo ou zero

# barryc: 2020-04-21 | Planio 7780
error.field.specialactionsdescription.max=A descriÃ§Ã£o deve ter entre {min} e {max} caracteres.

# jacciedt: 2020-04-14 | Planio 8139
meter.model.deactivate.in.use.error=NÃ£o Ã© possÃ­vel desativar - jÃ¡ existem contadores usando este modelo de contador.

# jacciedt: 2020-04-01 | Planio 7873
question.confirm.installation.date.future.date=data futura
usagepoint.field.meter.activation.date=Data de AtivaÃ§Ã£o Inicial

# barryc: 2020-04-22 | Planio 8209
tariff.error.percent_charge=Especifique uma carga.
tariff.error.unit_charge=Especifique um valor.

# patrickm: 2020-04-03 | Planio 8675
register.reading.txn.create_date=Data Criada

#joelc: 2020-02-19 | Planio 8592
meter.models.field.data.decoder=Descodificador de dados do contador
meter.models.field.data.decoder.help= Alguns modelos de contadores requerem descodificadores especÃ­ficos para processar suas leituras.

# patrickm: 2020-01-14 | Planio 7768
blockingtype.form.dailyamount=Montante diÃ¡rio
blockingtype.form.dailyamount.help=Quantidade mÃ¡xima permitida por dia
blockingtype.msg.error.dailyamount=VocÃª deve incluir o modelo de valor mÃ¡ximo por dia, {max_amount_per_day}, em sua mensagem.

# jacciedt: 2019-01-20 | Planio 7356
usagepoint.hist.device.move.ref=ReferÃªncia de movimento do dispositivo
usagepoint.device.move.ref.lbl=NÃºmero de referÃªncia do movimento do dispositivo
error.field.devicemoveref.max=A referÃªncia de movimento do dispositivo deve ter menos de {max} caracteres.

# jacciedt: 2019-12-31 | Planio 7950
configure.user.interface=Configurar interface do usuÃ¡rio
configure.user.interface.field.name=Nome do campo
configure.user.interface.display=Mostrar?
configure.user.interface.validation.regex=Regex de validaÃ§Ã£o
configure.user.interface.regex.failed.message=Mensagem de falha Regex/valores enumerados
changes.saved=As mudanÃ§as foram salvas.
configure.user.interface.invalid.regex=Um ou mais campos tÃªm padrÃµes regex invÃ¡lidos.
error.required.field={0} Ã© um campo obrigatÃ³rio - o registro nÃ£o pode ser salvo sem ele.
error.regex.invalid=O valor de entrada nÃ£o corresponde ao padrÃ£o necessÃ¡rio de: {0}
configure.user.interface.enumerated.values.label=Digite na lista de possÃ­veis valores separados por vÃ­rgulas:
enumerated.field=campo enumerado

# jacciedt: 2019-11-18 | Planio 7264
meter.select.meter.phase=Fase Contador
meter.select.meter.phase.help=Selecione a fase correta do contador para o modelo do contador.

# jacciedt: 2019-12-04 | Planio 7772
meter.txn.reversal.reason=Motivo da reversÃ£o
meter.txn.reversed.by=Revertido por
vend.reversal.exceeds.time.limit=ReversÃ£o sem sucesso. Excede o limite de tempo de reversÃ£o.

# joelc: 2019-11-25 | Planio 7487
sts.tokens.header = Tokens STS
verify.token = Verificar token
verification.error.timeout = Erro ao verificar tokens. Nenhuma resposta recebida do serviÃ§o.
verification.error.general=NÃ£o foi possÃ­vel verificar o token
verify.token.class=Classe de token
verify.token.subclass=Subclasse de Token
verify.token.id=ID do token
verify.token.units=Unidades
verify.token.date=Data do token

# jacciedt: 2019-11-21 | Planio 7362
energybalancing.error.duplicate.selected.meters=O contador selecionado jÃ¡ foi adicionado Ã  lista.

# zachv: 2019-11-21 | Planio 7916
tariff.field.free.units.title=Unidades gratuitas mensais
tariff.field.percent_charge.title=CobranÃ§a Percentual
tariff.field.percent_charge.add=Adicionar cobranÃ§a percentual
tariff.field.cyclic_charge.title=Carga CÃ­clica
tariff.field.cyclic_charge.add=Adicionar carga cÃ­clica
tariff.field.non_accruing_monthly.name=NÃ£o acumulando mensalmente
tariff.field.non_accruing_monthly.name.help=Aplica-se apenas ao ciclo MENSAL. CobranÃ§as mensais nÃ£o acumuladas cobram apenas pelo mÃªs em que uma transaÃ§Ã£o Ã© feita, quaisquer meses anteriores em que nenhuma cobranÃ§a mensal foi cobrada (por exemplo, devido a nenhuma compra ou apenas tokens gratuitos) nÃ£o serÃ£o cobrados retrospectivamente.
tariff.field.unit_charge.title=Carga unitÃ¡ria
tariff.field.unit_charge.add=Adicionar carga unitÃ¡ria
tariff.field.unit_charge.name=Nome da cobranÃ§a
tariff.field.unit_charge.name.help=O nome dessa cobranÃ§a que aparecerÃ¡ no recibo do cliente.
tariff.field.unit_charge.is_percent=Percentagem
tariff.field.unit_charge.is_percent.help=Se isso Ã© cobrado ou nÃ£o como uma percentagem do preÃ§o unitÃ¡rio ou uma taxa de moeda por unidade.
tariff.field.unit_charge.is_taxable=TributÃ¡vel
tariff.field.unit_charge.is_taxable.help=Se o imposto deve ser aplicado a essa cobranÃ§a.
tariff.field.unit_charge=Carga unitÃ¡ria
tariff.field.unit_charge.help=O valor da moeda a ser cobrado por unidade comprada. Este Ã© um encargo adicional sobre o preÃ§o unitÃ¡rio base.
tariff.field.unit_charge_percent=Percentagem do preÃ§o unitÃ¡rio
tariff.field.unit_charge_percent.help=Esta taxa serÃ¡ aplicada como uma percentagem do preÃ§o unitÃ¡rio. Se houver blocos, ele usarÃ¡ o preÃ§o do bloco.
tariff.error.unit_charge.name=Especifique um nome para a cobranÃ§a.
tariff.error.unit_charge.positive_not_zero=Deve ser um valor positivo e nÃ£o zero.

# jacciedt: 2019-11-12 | Planio 7814
customer.auxaccount.increase.debt=Aumentar DÃVIDA em:
customer.auxaccount.decrease.debt=Diminuir DÃVIDA em:
customer.auxaccount.increase.refund=Aumentar REEMBOLSO em:
customer.auxaccount.decrease.refund=Diminuir REEMBOLSO em:
customer.auxaccount.error.amount.negative=O valor nÃ£o pode ser negativo
customer.auxaccount.error.tax.negative=Imposto nÃ£o pode ser negativo
customer.auxaccount.error.adjustment=Ã necessÃ¡rio selecionar Aumentar ou Diminuir
customer.auxaccount.error.balance.refund=Saldo nÃ£o pode ser negativo ou zero

# patrickm: 2019-11-02 | Planio #7801
customer.search.listbox.label=Pesquisa do cliente por
customer.search.listbox.item_agr_ref=Ref do contrato
customer.search.listbox.item_id_num=NÃºmero de identidade
customer.search.listbox.item_surname=Sobrenome

# jacciedt: 2019-10-24 | Planio 7812
customer.auxaccount.functions.as=Funciona como

# thomasn: 2019-10-17 | planio-5133
link.blockingtype=Tipos de bloqueio
blockingtype.title=Tipo de bloqueio
blockingtype.name=Nome
blockingtype.panel.error.missingvalues=Um dos valores ({0}, {1}, {2}, {3})\ndeve ser definido OU deve ser um bloco completo.
blockingtype.title.add=Adicionar tipo de bloqueio
blockingtype.title.update=Actualizar tipo de bloqueio
blockingtype.form.typename=Nome
blockingtype.form.typename.help=Nome para o tipo de bloqueio
blockingtype.form.units=Unidades por dia
blockingtype.form.units.help=MÃ¡ximo de unidades permitidas por dia
blockingtype.form.complete=Completo
blockingtype.form.complete.help=Bloqueio completo significa que nenhuma venda serÃ¡ permitida.
blockingtype.form.vends=NÃºmero de Vendas
blockingtype.form.vends.help=Vendas mÃ¡ximas permitidas antes do bloqueio completo
blockingtype.form.amount=Quantia mÃ¡xima
blockingtype.form.amount.help=Valor mÃ¡ximo permitido antes do bloqueio completo
blockingtype.form.message=Mensagem
blockingtype.form.message.help=Mensagem com detalhes do bloco exibidos ao usuÃ¡rio. Use {remaining_vends} para exibir as vendas restantes antes do bloqueio completo. Use {remaining_amount} para exibir o valor restante antes do bloqueio completo. Use {max_units_per_day} para exibir as unidades mÃ¡ximas permitidas por dia. Use {max_amount_per_day} para exibir o valor mÃ¡ximo permitido por dia. Use {reason_fixed} para exibir o motivo do bloqueio. Observe que os colchetes denotam variÃ¡veis do sistema.
blockingtype.error.save=NÃ£o foi possÃ­vel salvar o tipo de bloqueio.
blockingtype.error.save.duplicate=NÃ£o foi possÃ­vel salvar o tipo de bloqueio, jÃ¡ existe outro tipo de bloqueio com o mesmo nome.
blockingtype.error.update=NÃ£o foi possÃ­vel atualizar o tipo de bloqueio.
blockingtype.error.update.duplicate=NÃ£o foi possÃ­vel atualizar o tipo de bloqueio, jÃ¡ existe outro tipo de bloqueio com o mesmo nome.
blockingtype.msg.error.variables=As chaves sÃ£o permitidas apenas para variÃ¡veis de sistema vÃ¡lidas, {max_units_per_day}, {max_amount_per_day}, {remaining_amount}, {remaining_vends} ou {reason_fixed}.
blockingtype.msg.error.units=VocÃª deve incluir o modelo de unidades, {max_units_per_day}, em sua mensagem.
blockingtype.msg.error.units.undefined=Modelo de unidades {max_units_per_day} nÃ£o permitido, a menos que {0} tenha sido definido.
blockingtype.msg.error.vends=VocÃª deve incluir o modelo de nÃºmero de vendas, {remaining_vends}, em sua mensagem.
blockingtype.msg.error.vends.undefined=Modelo de vendas {remaining_vends} nÃ£o permitido, a menos que {0} tenha sido definido.
blockingtype.msg.error.amount=VocÃª deve incluir o modelo de valor, {remaining_amount}, em sua mensagem.
blockingtype.msg.error.amount.undefined=Modelo de valor {remaining_amount} nÃ£o permitido, a menos que {0} tenha sido definido.
blockingtypes.header=Tipos de bloqueio
blockingtypes.title=Tipos de bloqueio atuais

# jacciedt: 2019-10-10 | Planio 7514
meter.mrid.external.unique.validation=A ID exclusiva deste contador jÃ¡ estÃ¡ em uso

# jacciedt: 2019-09-17 | Planio 5823
demo.addmeterreadings.earliest.reading=Leitura mais antiga
demo.addmeterreadings.latest.reading=Leitura mais recente
demo.addmeterreadings.zero.checkbox.text=Adicionar zero leituras 
demo.addmeterreadings.zero.form.title=Zero leituras
demo.addmeterreadings.consecutive=Consecutivo
demo.addmeterreadings.random=AleatÃ³rio
demo.addmeterreadings.percentage.instances=Percentagem de instÃ¢ncias
demo.addmeterreadings.missing.checkbox.text=Adicionar leituras em falta
demo.addmeterreadings.missing.form.title=Leituras em falta
demo.addmeterreadings.algorithm.logic=LÃ³gica do algoritmo
demo.addmeterreadings.delete=Excluir leituras de intervalo existentes
demo.addmeterreadings.delete.all=Todos
demo.addmeterreadings.delete.selected=Intervalo de datas selecionado
demo.addmeterreadings.append=Acrescentar
demo.addmeterreadings.link=[DEMO] Adicionar leituras do contador
demo.addmeterreadings.header=Adicionar leituras do contador
demo.addmeterreadings.title=Adicionar leituras do contador
demo.addmeterreadings.title.criteria.register=Registrar CritÃ©rios de Leitura
demo.addmeterreadings.reading.variants=Escolha a Variante de Leitura
demo.addmeterreadings.delete.register=Excluir leituras de registro existentes
demo.addmeterreadings.error.misc.start=Esta data de inÃ­cio precisa ser posterior Ã  data de inÃ­cio principal
demo.addmeterreadings.error.misc.end=Esta data final precisa ser anterior Ã  data final principal
demo.addmeterreadings.error.instances.required=As instÃ¢ncias sÃ£o necessÃ¡rias
demo.addmeterreadings.error.instances.format=As instÃ¢ncias precisam ser numÃ©ricas
demo.addmeterreadings.error.instances.range=As instÃ¢ncias precisam ser um nÃºmero inteiro entre 0 e 100
demo.addmeterreadings.error.mdc.channel=Nenhum canal MDC selecionado

# renciac: 2019-09-19 | Planio 7656
import.generic.start.label=InÃ­cio dos dados
error.field.enckey.max=A chave de encriptaÃ§Ã£o do contador nÃ£o pode exceder o mÃ¡ximo de 255 caracteres.
error.field.powerlimitlabel.max=A ETIQUETA de limite de potÃªncia nÃ£o pode exceder o mÃ¡ximo de 255 caracteres.
bulk.upload.invalid.unexpected.commas=VÃ­rgulas inesperadas - nÃ£o Ã© possÃ­vel identificar campos separados com precisÃ£o
button.import.extract.fail=Falha na extraÃ§Ã£o
import.upload.num.failed.upload.label=NÃºmero de upload com falha
import.items.unsuccessful.uploads.reminder=\nLEMBRETE: {0} itens nÃ£o foram carregados com sucesso neste arquivo. Eles sÃ£o ignorados para processamento adicional e devem ser recriados manualmente em um novo arquivo e carregados novamente.
import.upload.completed=Upload do ficheiro concluÃ­do.
bulk.upload.file.error=Erro ao importar o ficheiro
error.field.customvarchar1.max=UP customvarchar1 pode ter no mÃ¡ximo 255 caracteres.
error.field.customvarchar2.max=UP customvarchar2 pode ter no mÃ¡ximo 255 caracteres.
error.field.phonecontact1.max=O contato telefÃ´nico1 deve ter menos de {max} caracteres.
error.field.phonecontact2.max=O contato telefÃ´nico2 deve ter menos de {max} caracteres.
error.field.notificationemail.max=endereÃ§o de e-mail de notificaÃ§Ã£o da conta do cliente deve ter menos de {max} caracteres.
error.field.notificationphone.max=O telefone de notificaÃ§Ã£o da conta do cliente deve ter menos de {max} caracteres.
import.file.item.view.still.busy=A importaÃ§Ã£o deste arquivo estÃ¡ em execuÃ§Ã£o. Nenhuma outra aÃ§Ã£o pode ser tomada atÃ© que seja concluÃ­da ou interrompida.
import.file.item.view.still.busy.stopped=InstruÃ§Ã£o de paragem de importaÃ§Ã£o emitida. A importaÃ§Ã£o cessarÃ¡ apÃ³s o lote atual.
import.file.view.upload.still.busy=O upload deste ficheiro estÃ¡ em curso.
button.stop.import.all=Parar importaÃ§Ã£o
import.file.stopped=A importaÃ§Ã£o deste ficheiro foi interrompida.
import.file.stopped.instruction=A instruÃ§Ã£o Stop Import foi emitida. A importaÃ§Ã£o serÃ¡ interrompida apÃ³s o lote atual.

# renciac: 2019-09-16 | Planio 6715
bulk.upload.idNumber=NÃºmero de identidade

# thomasn: 2019-10-09 | Planio 6286
usagepoint.unblocking.enter.reason=Insira um motivo para desbloquear
usagepoint.unblocking.select.reason=Selecione um motivo para desbloquear

# thomasn: 2019-09-09 | planio-6287 Add reason when re-activating a UP
usagepoint.hist.status.reason=Motivo do estado

# jacciedt: 2019-09-10 | Planio 7490
meter.clearreverseflag=Apagar reverse flag
meter.disabletriplimit=Desativar trip limit
meter.setcurrentlimit=Definir limite atual
meter.issue.token.description.help=Insira uma descriÃ§Ã£o para este {0}.

# robertf: 2019-09-10 | Planio 7571
customer.txn.reason=Motivo da AÃ§Ã£o
customer.auxaccount.history.title=HistÃ³rico da Conta Auxiliar
customer.auxaccount.history.filter.title=HistÃ³rico de conta auxiliar para: {0}
customer.auxaccount.history.filter.discription=AlteraÃ§Ãµes anteriores feitas na conta auxiliar: {0}
customer.auxaccount.history.table.header.datemodified=Data modificada
customer.auxaccount.history.table.header.user=Utilizador
customer.auxaccount.history.table.header.action=AÃ§Ã£o
customer.auxaccount.history.table.header.type=Tipo
customer.auxaccount.history.table.header.accountname=Nome da conta
customer.auxaccount.history.table.header.balance=Saldo
customer.auxaccount.history.table.header.priority=Prioridade
customer.auxaccount.history.table.header.chargeschedule=Agenda de cobranÃ§a
customer.auxaccount.history.table.header.freeissue=EmissÃ£o gratuita
customer.auxaccount.history.table.header.status=Estado
customer.auxaccount.history.table.header.updatereason=Motivo da AtualizaÃ§Ã£o
customer.auxaccount.history.table.header.createreason=Criar Motivo
customer.title.auxaccounts.history.selector.description=Selecione Conta Auxiliar para visualizar o histÃ³rico.

# jacciedt: 2019-08-14 | Planio 7341
import.account.number=NÃºmero de conta
import.arrears.balance=Saldo Atrasado
import.debtor.balance=Saldo devedor
import.edit.account.number.update.success=Os dados da conta nÃºmero {0} foram atualizados com sucesso.
import.edit.generic.update.success=Dados atualizados com sucesso.

# jacciedt: 2019-07-18 | Planio 6240
meter.select.store.add=loja de contadores

# jacciedt: 2019-08-15 | Planio 7310
transaction.history.graph.yaxis.label2=NÃºmero de unidades

# jacciedt: 2019-08-22 | Planio 6738
tou.thin.error.tax.positive=O imposto deve ser um valor positivo.
register.reading.tax.positive=O imposto deve ser um valor positivo.

# jacciedt: 2019-07-29 | Planio 7197
error.field.validity.email=Um dos endereÃ§os de e-mail Ã© invÃ¡lido.
reprint.warning.line.1=AVISO!!! ISTO Ã UMA REIMPRESSÃO
reprint.warning.line.2=de um token emitido em {0}
reprint.warning.line.3=NOTA FISCAL (CÃPIA)
reprint.warning.line.4=Reimpresso em: {0}
reprint.warning.line.5=Reimpresso por: {0}
reprint.credit.vend.tax.invoice=Venda de CrÃ©dito - Nota Fiscal
reprint.util.name=Nome da ConcessionÃ¡ria
reprint.util.dist.id=Concess. Dist. ID
reprint.util.vat.no=NIF Concess. nÂº
reprint.util.address=Morada Concess.
reprint.issued=Publicado
reprint.token.tech=Token Tech.
reprint.alg=Alg.
reprint.sgc=SGC
reprint.krn=KRN
reprint.your.resource.token=Seu token {0}
reprint.standard.token=Token padrÃ£o
reprint.receipt.nr=Recibo #
reprint.free.basic.resource=BÃ¡sico gratuito {0}
reprint.debt.items=Itens de DÃ­vida
reprint.fixed.items=Itens fixos
reprint.total.vat.excl=Total (sem IVA)
reprint.total.vat.incl=Total (IVA Incl.)
reprint.print=Imprimir
reprint.deposit=DepÃ³sito
reprint.save.to.pdf=Salvar em PDF
reprint.electricity=Eletricidade
reprint.water=Ãgua
reprint.gas=GÃ¡s

# jacciedt: 2019-08-20 | Planio 7364
error.supplygroup.server=Suppy group code duplicado e NÃºmero de RevisÃ£o de Chave. Especifique valores exclusivos.

# jacciedt: 2019-08-15 | Planio 7449
location.field.address.line.2=Linha 2
location.field.address.line.3=Linha 3

# jacciedt: 2019-08-01 | Planio 7368
location.field.address.line.1=Linha 1
customer.phone.1=NÃºmero de telefone 1
customer.phone.2=NÃºmero de telefone 2

# jacciedt: 2019-06-19 | Planio 7024
timezone.warning=Nota: Todos os separadores devem ser fechados antes que o fuso horÃ¡rio possa ser alterado.

# jacciedt: 2019-07-15 | Planio 7244
error.field.specialactionsname.range=O nome da aÃ§Ã£o deve ter entre {min} e {max} caracteres.

# jacciedt: 2019-07-16 | Planio 7148
unit.kiloliter.symbol=kl
unit.cubicmeter.symbol=m\u00B3
meter.units=Unidades ({0})

# robertf: 2019-07-08 | Planio 6247
transaction.history.column.header.stdunits=Unidades padrÃ£o
transaction.history.column.header.fbeunits=Unidades bÃ¡sicas gratuitas
transaction.history.column.header.stdtoken=Token PadrÃ£o Total
transaction.history.column.header.fixedamt=Total de Custos Fixos
transaction.history.column.header.auxamt=Total de Pagamento Auxiliar

# thomasn: 2019-07-01 | Planio-6288
usagepoint.blocking.info.message= Este ponto de medida foi bloqueado por {0} no {1}
usagepoint.blocking.info.message.reason= pelo seguinte motivo: <br/> {0}
usagepoint.hist.blocking.name=Tipo de bloqueio
usagepoint.hist.blocking.reason=Motivo do Bloqueio

# renciac: 2019-06-26 | Planio 6291
bulk.upload.powerlimit.key=Limite de potÃªncia
meter.powerlimit.key.error.integer=O valor do limite de potÃªncia deve ser um nÃºmero inteiro
meter.powerlimit.key.error.not.configured=NÃ£o hÃ¡ configuraÃ§Ãµes de limite de potÃªncia definidas nas configuraÃ§Ãµes do aplicativo.
meter.powerlimit.key.error.invalid=Este limite de potÃªncia nÃ£o estÃ¡ configurado nas configuraÃ§Ãµes do aplicativo.

error.field.usagepointgroups.required.group=Falta o grupo de Pontos de Medida NecessÃ¡rio: {0}
error.field.usagepointgroups.missing.group=Hierarquia nÃ£o concluÃ­da: campo de grupo de pontos de medida ausente para Tipo de ponto de medida: {0}
error.field.usagepointgroups.invalid.group=Nome de grupo de ponto de medida invÃ¡lido {0} para tipo de ponto de medida: {1}
error.field.usagepointgroups.incomplete.group=NÃ­vel de hierarquia incompleto no Grupo de pontos de medida: {0}

bulk.upload.invalid.locationgroups.not.configured=Os grupos de locais nÃ£o estÃ£o configurados para este sistema
usage.point.location.group.generate.label=Grupo de localizaÃ§Ã£o do ponto de medida
error.field.uplocationgroups.required=O grupo de localizaÃ§Ã£o do ponto de medida necessÃ¡rio estÃ¡ ausente
error.field.uplocationgroups.invalid.group=Nome de grupo de local invÃ¡lido {0} para Grupo de local de ponto de medida
error.field.uplocationgroups.incomplete.group=NÃ­vel de hierarquia incompleto no grupo de localizaÃ§Ã£o do ponto de medida
error.field.uplocationgroups.first.level.required=O grupo de localizaÃ§Ã£o do ponto de medida Ã© obrigatÃ³rio. Pelo menos o primeiro nÃ­vel de hierarquia deve ser concluÃ­do.

customer.location.group.generate.label=Grupo de localizaÃ§Ã£o do cliente
error.field.custlocationgroups.required=O grupo de localizaÃ§Ã£o do cliente obrigatÃ³rio estÃ¡ ausente.
error.field.custlocationgroups.invalid.group=Nome de grupo de locais invÃ¡lido {0} para Grupo de locais do cliente
error.field.custlocationgroups.incomplete.group=NÃ­vel de hierarquia incompleto no grupo de localizaÃ§Ã£o do cliente
error.field.custlocationgroups.first.level.required=O grupo de localizaÃ§Ã£o do cliente Ã© obrigatÃ³rio. Pelo menos o primeiro nÃ­vel de hierarquia deve ser concluÃ­do.

location.field.address.suburb.name=Nome do subÃºrbio

# jacciedt: 2019-06-21 | Planio 6359
billingdet.error.save.duplicate=NÃ£o foi possÃ­vel salvar o Determinante de Faturamento, jÃ¡ existe outro Determinante de Faturamento com o mesmo nome.

# renciac: 2019-05-29 | Planio 6237
meter.freeissue.currency=Venda de emergÃªncia

# renciac: 2019-04-25 | Planio 6235
customer.id.partial.search=Nenhum ID de cliente que corresponda exatamente. Fazendo pesquisa avanÃ§ada...
error.customer.load=NÃ£o Ã© possÃ­vel exibir o cliente.
search.get.total.label=Contagem Resultados totais dos critÃ©rios selecionados
search.count.label=Contando resultados totais para critÃ©rios selecionados...

# renciac: 2019-04-17 | Planio 6234
customer.id.error.noentry=Insira o nÃºmero de ID do cliente ou % para todos
metersearch.error.nometer=Insira um nÃºmero de contador ou % para todos
usagepoint.error.none=Digite um nome de ponto de medida ou % para todos
customer.error.noentry=Digite o apelido do cliente ou % for all
customer.agreement.error.noentry=Insira um nÃºmero de contrato ou % para todos
customer.account.error.noentry=Introduza um nÃºmero de conta ou % para todos

# renciac: 2019-03-25 | Planio 5961
import.upload.header=Carregar/importar ficheiro
import.upload.file.upload.title=Carregar ficheiro
import.filetype.select.file.help=Selecione o tipo de ficheiro do ficheiro a ser carregado e importado
import.filetype.select.labeltext=Tipo de ficheiro
import.upload.filetype.none= Selecione o tipo de ficheiro do ficheiro a ser carregado
import.upload.filename.txt=Nome do ficheiro selecionado={0}
import.upload.file.select.labeltext=Selecione o ficheiro a carregar
import.upload.select.file.help=Selecione um ficheiro de movimentos contendo as informaÃ§Ãµes para upload no sistema
import.upload.csv.button=Carregar ficheiro
import.upload.workspace.heading=Upload de ficheiro e dados de importaÃ§Ã£o
link.file.import=Carregar e importar ficheiros
import.upload.filetype.error=Tipo de ficheiro invÃ¡lido. Entre em contato com o suporte.
import.upload.file.none=Nenhum ficheiro foi selecionado para ser carregado
import.upload.file.error=Erro ao carregar o ficheiro. Entre em contato com o suporte.
import.selected.items.non=Nenhum item foi selecionado para importaÃ§Ã£o.
import.upload.uploaded.files.title=Ficheiros enviados para importaÃ§Ã£o de dados
import.upload.file.name.label=Nome do ficheiro
import.upload.num.items.label=NÃºmero de itens
import.upload.startdate.label=Inicio de upload
import.upload.enddate.label=Fim de upload
import.upload.last.imported.by.label=Ãltima importaÃ§Ã£o por
import.upload.detail=Detalhe
import.upload.open.label=Abrir

import.file.items.header=Itens de ficheiro
button.import.selected=Importar selecionado
button.import.all=Importar tudo
import.items.title=Itens para Importar
import.select.label=Selecione
import.upload.successful.label=Sucesso upload
import.upload.date.label=Data de ImportaÃ§Ã£o
import.num.attempts.label=NÃºmero de importaÃ§Ãµes
import.last.successful.label=Sucesso importaÃ§Ã£o
import.meter.label=Contador
import.up.label=InstalaÃ§Ã£o
import.agrref.label=Contrato
import.comment.label=ComentÃ¡rio
import.itemdata.label=Dados
import.upload.username.label=Carregar usuÃ¡rio
import.last.start.label=InÃ­cio da Ãºltima importaÃ§Ã£o
import.last.end.label=Fim da Ãltima ImportaÃ§Ã£o
import.items.file.detail.header=Detalhe do ficheiro
import.items.edit.header=Ver/editar item de arquivo
import.cancel.edit.item.confirm=O cancelamento abandonarÃ¡ as alteraÃ§Ãµes feitas acima. Continuar?
import.edit.item.update.success=Dados do cliente {0}, contador {1} atualizados com sucesso.
import.edit.item.update.non=Os dados nÃ£o foram alterados. Nenhuma atualizaÃ§Ã£o necessÃ¡ria.

# thomasn: 2019-02-18 | Planio 6223
customer.idnumber.help=Insira o nÃºmero de identificaÃ§Ã£o do cliente.
customer.idnumber=NÃºmero de identidade
error.field.idnumber.max=O nÃºmero de identificaÃ§Ã£o deve ter menos de {max} caracteres.
customer.idnumber.column=NÃºmero de identidade
search.customer.idnumber=NÃºmero de identidade

#  renciac: 2019-01-28 | Planio 6425
# Removed: usagepoint.deactivate.info.message=Este ponto de medida foi desativado por {0} no {1} pelo seguinte motivo: <br/> {2}
usagepoint.deactivate.info.message= Este ponto de medida foi desativado por {0} no {1}
usagepoint.deactivate.info.message.reason= pelo seguinte motivo: <br/> {0}

# rfowler: 2019-02-08 | Planio 6141
search.customer.phone1.number=Telefone 1
search.customer.phone2.number=Telefone 2
search.customer.phone.number=NÃºmero de telefone
search.customer.custom.textfield1=Campo personalizado 1

search.location.header=Pesquisa de localizaÃ§Ã£o
search.location.erf.number=NÃºmero Erf
search.location.building.name=nome do edifÃ­cio
search.location.suite.number=NÃºmero do apartamento
search.location.address1=EndereÃ§o Linha 1
search.location.address2=endereÃ§o linha 2
search.location.address3=EndereÃ§o Linha 3
search.location.type=Tipo de pesquisa de localizaÃ§Ã£o
search.location.type.label=LocalizaÃ§Ã£o do cliente/ponto de medida
search.location.type.customer=LocalizaÃ§Ã£o do cliente
search.location.type.usagepoint=LocalizaÃ§Ã£o do ponto de medida

# joelc: 2018-01-10 | planio-6324:Samoa - Reason for reactivation of usage point
error.field.reasonname.range=O nome do motivo Ã© obrigatÃ³rio.
error.field.reasontext.range=O texto do motivo Ã© obrigatÃ³rio.
specialaction.auto.deactivate.usagepoint= Desativado porque o ponto de medida nÃ£o estÃ¡ mais completo (falta de cliente ou contador)
specialaction.auto.activate.usagepoint=Ativado apÃ³s completar os dados de ponto de medida necessÃ¡rios.

#  zachv: 2019-01-02 | Planio 5936
# Removed: ndp.schedule.abandon.activation.change=A ativaÃ§Ã£o do agendamento mudou. se quiser manter a configuraÃ§Ã£o, escolha NÃ£o e salve/atualize primeiro. Abandonar configuraÃ§Ã£o?
question.close.tabs.dirty=Todos os separadores serÃ£o fechados, mas alguns tÃªm alteraÃ§Ãµes nÃ£o salvas. Deseja descartar essas alteraÃ§Ãµes?

# joelc: 2018-12-12 | planio-6324:Samoa - Reason for reactivation of usage point
usagepoint.activate.enter.reason=Insira um motivo para esta ativaÃ§Ã£o
usagepoint.activate.select.reason=Selecione um motivo para esta ativaÃ§Ã£o
special.action.reason.error.save.duplicate=Este motivo jÃ¡ foi adicionado.

# renciac: 2018-12-06 | planio-6282
error.field.value.boolean=O valor deve ser verdadeiro ou falso
error.field.value.location.level=O tipo de grupo de locais NÃO Ã© obrigatÃ³rio. Esta configuraÃ§Ã£o nÃ£o pode ser definida como verdadeira.

# thomasn: 2018-11-29 | planio-5296
auxaccount.upload.invalid.duplicate=Duplicate AuxAccount a combinaÃ§Ã£o (accountName & agreementRef) jÃ¡ existe!.

#RobertF 2018-11-21 Planio-6142 : [MMA] CoCT - Transaction History Bar Graph
transaction.history.graph.title=HistÃ³rico de transaÃ§Ãµes
transaction.history.graph.description=TransaÃ§Ãµes por mÃªs nos Ãºltimos 12 meses.
transaction.history.graph.xaxis.label= MÃªs
transaction.history.graph.yaxis.label= NÃºmero de transaÃ§Ãµes
transaction.history.graph.series.label= Contar transaÃ§Ãµes

# joelc 20 November 2018, Planio 4328
question.custom.field.used= Observe que qualquer registro de entidade de grupo usando este campo NÃO serÃ¡ atualizado, \
  only the list of available options will be updated for future use. 
question.custom.field.used.option.yes=Atualizar lista
question.custom.field.used.option.no=Cancelar

# thomasn: 2018-11-12 | planio-6168
customer.title.mr=Sr
customer.title.mrs=Sra
customer.title.ms=Sr
customer.title.miss=Sra
customer.title.doc=Dr.
customer.title.prof=Prof.
customer.title.sir=Senhor

customer.email=EndereÃ§o de email

# joelc: 2018-10-29 | planio-6105: usage point meter download and print for Mayotte
print.customer.contract=Baixar Contrato do Cliente
print.customer.contract.auxtype=Aux type
print.customer.contract.auxname=Nome da conta
print.customer.contract.principleamount=Montante principal
print.customer.contract.balance=Saldo
print.customer.contract.status=Estado
print.customer.contract.signature=Assinatura do cliente
print.customer.contract.signature.date=Data

# thomasn: 2018-10-17 | Planio 5296
auxaccount.upload.balanceType=Tipo de Saldo
auxaccount.upload.invalid.balance.amount=Valor do saldo deve ser positivo
auxaccount.upload.invalid.balancetype=Tipo de saldo invÃ¡lido deve ser dÃ©bito/reembolso.

# robertf: 2018-10-09 | Planio 5955
meter.txn.user.ref=ReferÃªncia do usuÃ¡rio
engineering.token.user.reference.txtbx.label=ReferÃªncia do usuÃ¡rio
engineering.token.user.reference.txtbx.label.help=Insira a referÃªncia do usuÃ¡rio para emissÃ£o de token de engenharia.

# Patrickm: 2018-11-09 | planio-6192: GIS data upload for BVM
link.metadata.upload=Carregar metadados
metadata.upload.heading=Carregamento de metadados
metadata.upload.data.title=Importar Metadados
metadata.upload.description=Selecione o arquivo JSON que contÃ©m os metadados a serem importados para o sistema Meter Management.
metadata.upload.error.object.creation=Erro ao criar objeto {0}. Entre em contato com o suporte!
metadata.upload.select.file.help=Selecione um arquivo contendo os metadados no formato json especificado para importar para o sistema
metadata.upload.button=Carregar metadados
metadata.lat.label=Latitude
metadata.lon.label=Longitude
metadata.lon.help=Metadados de longitude para este grupo UP
metadata.lat.help=Metadados de latitude para este grupo UP
metadata.gis.saved=InformaÃ§Ãµes GIS salvas com sucesso para o grupo: {0}
metadata.gis.error.invalid.lat=Valor de latitude invÃ¡lido. Coordenada fora do intervalo de coordenadas GIS vÃ¡lidas.
metadata.gis.error.invalid.lon=Valor de longitude invÃ¡lido. Coordenada fora do intervalo de coordenadas GIS vÃ¡lidas.

# Patrick: 2017-12-14 | planio-5041 : Add configurable switch to device stores to determine the response of meters in the store to polling requests
devicestore.field.store_vendors_meter=A loja de dispositivos contÃ©m contadores que foram movidos para outro fornecedor
devicestore.field.store_vendors_meter_help=Se o armazenamento de dispositivos contÃ©m contadores que foram movidos para outro fornecedor.
devicestore.field.store_vendors_meter_help2=Os campos '{0}' e '{1}' sÃ£o mutuamente exclusivos. Portanto, vocÃª nÃ£o pode ter uma mensagem personalizada e marcar a caixa
devicestore.field.custom_message=Mensagem de resposta personalizada da loja de dispositivos
devicestore.field.custom_message_help=A mensagem a ser mostrada aos usuÃ¡rios quando os usuÃ¡rios consultam contadores armazenados neste armazenamento de dispositivo.
devicestore.meters.save.dialog=Este contador serÃ¡ salvo na loja '{0}'. Por favor, confirme esta operaÃ§Ã£o.
devicestore.meters.move.dialog=Este contador serÃ¡ movido para a loja '{0}'. Deseja realizar esta operaÃ§Ã£o?
devicestore.meters.fetch.dialog=VocÃª estÃ¡ a procurar um contador de outro fornecedor. Deseja continuar com esta operaÃ§Ã£o?

# thomasn: 2018-10-23 | planio-5956 [MMA] Add an alphanumeric SAP reference number when removing or replacing a meter on a usage point and display it in the history tables
usagepoint.hist.reason=Motivo de RemoÃ§Ã£o/ReatribuiÃ§Ã£o do contador

# robertf: 2018-10-01 | Planio 5954
meter.txn.powerlimits=Limite de potÃªncia
meter.powerlimit.units.w=Limite de PotÃªncia (W)

# Renciac: 2018-09-04 | planio-5466 : Add manual reversal
button.vend.reversal=Venda reversa
vend.reversal.confirm=Confirmar reversÃ£o de Vend / Topup?
vend.reversal.connection.error=Nenhuma resposta recebida do serviÃ§o. Por favor recarregue a pÃ¡gina.
vend.reversal.error=Erro de venda/recarga: {0}
vend.reversal.fail=Apenas o Ãºltimo Vend pode ser revertido.
vend.reversal.success=ReversÃ£o de venda bem-sucedida. Ref Original= {0}, Ref ReversÃ£o={1}
vend.trans.already.reprinted=Esta transaÃ§Ã£o foi reimpressa {0} vez/s. Primeira reimpressÃ£o em {1} por {2}. \n
vend.reprint.null.token= O token Ã© nulo. NÃ£o Ã© possÃ­vel reimprimir.
vend.reversed=A venda foi revertida.
meter.txn.reprint.date=Ãºltima reimpressÃ£o
meter.noshow.token= Reimprimir para token
vend.reprint.user.not.known=Desconhecido

# thomasn: 2018-09-04 | Planio 5476
readings.table.receiptnum=NÃºmero do Recibo

# thomasn: 2018-09-03 | Planio 5296
customer.auxaccount.amount.pos=Um valor positivo indica um REEMBOLSO
customer.auxaccount.amount.neg=Um valor negativo indica uma DÃVIDA
customer.auxaccount.title=Conta Auxiliar
customer.auxaccount.balance.type.debt=DÃ­vida
customer.auxaccount.balance.type.refund=Reembolso
customer.auxaccount.balance.type.error.required=VocÃª deve selecionar um.
customer.auxaccount.error.balance=Saldo nÃ£o pode ser negativo

# Thomas: 2018-08-29 | planio-5475 Time of use calendar allows 00:00 as end time for day profile but marks as incomplete
calendar.assign.period.end.maximum=O valor do final do dia Ã© 23:59
calendar.assign.period.end.help=hora em que o perÃ­odo termina. O valor do final do dia Ã© 23:59

# Renciac: 2018-07-26 | planio-5451 Enabling STS 6 
meter.token.code3=CÃ³digo do token 3
meter.token.code4=CÃ³digo do token 4
base.date.label= Data base:
base.date.label.help=Data base usada para geraÃ§Ã£o de tokens STS6 para este contador.
meter.three.tokens=SÃ£o necessÃ¡rios trÃªs tokens de mudanÃ§a de chave
meter.three.tokens.help=Para o cÃ³digo do algoritmo STS = 07, alguns contadores podem armazenar as informaÃ§Ãµes STS no contador e precisam de um terceiro token keyChange para fornecer as informaÃ§Ãµes.
meter.three.tokens.error= A configuraÃ§Ã£o de trÃªs tokens Ã© aplicÃ¡vel apenas ao cÃ³digo do algoritmo STS = 07
meter.clear.tid=TID reset

# zachv: 2018-08-22
tariff.field.samoa.debt_charge=CobranÃ§a de dÃ­vida
tariff.field.samoa.debt_charge.help=Cobrado por unidade
tariff.field.samoa.energy_charge=CobranÃ§a de Energia
tariff.field.samoa.energy_charge.help=Cobrado por unidade

# robertf: 2018-07-27: #5347 Community Group used feature indicator
usagepointgroups.indicator.thresholds.tooltip = O grupo tem limites de conta de cliente personalizados
usagepointgroups.indicator.ndp.tooltip = O grupo tem programaÃ§Ã£o NDP personalizada

# zachv: 2018-06-26
tariff.field.kenya.monthly = CobranÃ§a Mensal Fixa
tariff.field.kenya.monthly.help = Taxa fixa aplicada por mÃªs.
tariff.field.kenya.fuel = Taxa de custo de combustÃ­vel
tariff.field.kenya.fuel.help = Taxa variÃ¡vel por kWh, publicada mensalmente pela KPLC. O IVA Ã© aplicado a esta taxa.
tariff.field.kenya.forex = Taxa Forex
tariff.field.kenya.forex.help = Ajuste de variaÃ§Ã£o cambial (FERFA). Taxa variÃ¡vel por kWh, publicada mensalmente pela KPLC.
tariff.field.kenya.inflation = Ajuste de inflaÃ§Ã£o
tariff.field.kenya.inflation.help = Taxa variÃ¡vel por kWh, publicada mensalmente pela ELECTRA
tariff.field.kenya.erc =Taxa ERC
tariff.field.kenya.erc.help = Taxa por kWh.
tariff.field.kenya.rep = Taxa REP
tariff.field.kenya.rep.help = Percentagem da taxa bÃ¡sica
tariff.field.kenya.warma = Taxa WARMA
tariff.field.kenya.warma.help = Taxa variÃ¡vel por kWh, publicada mensalmente pela KPLC.

# RobertF 4th July 2018. Planio 5714
bulk.upload.enc.key= Chave de encriptaÃ§Ã£o
meter.enc.key.error=O modelo do contador requer uma chave de encriptaÃ§Ã£o do contador.

#RobertF June 15, 2017 Planio-5787 : Panel with the buying index chart
dashboard.buying.index.graph.title=Ãndice de compra
dashboard.buying.index.graph.month.description=Ãndice de Compra: contadores Transacionados / contadores Ativos (%)
dashboard.buying.index.graph.xaxis.label=MÃªs
dashboard.buying.index.graph.yaxis.label=Ãndice de compra

# zachv: 2018-05-04
meter.models.field.needs.encryption.key = Precisa de chave de encriptaÃ§Ã£o
meter.models.field.needs.encryption.key.help = Se uma chave de encriptaÃ§Ã£o de contador precisa ser inserida para este modelo de contador.
meter.encryptionkey=Chave de encriptaÃ§Ã£o
meter.encryptionkey.help=A chave de encriptaÃ§Ã£o para este contador, por exemplo, para desencriptar os dados recebidos de um coletor de dados do contador
meter.encryptionkey.error=O modelo do contador requer uma chave de encriptaÃ§Ã£o do contador.
meter.metermodelchange.remove_fields.question= Alterar este modelo de contador farÃ¡ com que os seguintes campos sejam apagados: {0} . Continuar?
meter.model.unset.encryption.key.error=NÃ£o Ã© possÃ­vel alterar o requisito de chave de encriptaÃ§Ã£o - jÃ¡ existem contadores com chaves de encriptaÃ§Ã£o usando este modelo de contador.

# Thomas: 2018-05-10 | planio-5502 : MDC messages Override Button needs info message
mdc.txn.override.help=Uma mensagem de substituiÃ§Ã£o serÃ¡ enviada como uma mensagem prioritÃ¡ria que terÃ¡ precedÃªncia sobre outras mensagens que possam estar pendentes no contador e determinadas verificaÃ§Ãµes de validaÃ§Ã£o nÃ£o serÃ£o aplicadas, como validaÃ§Ã£o de perÃ­odos sem desconexÃ£o e ordem de mensagens.

# Thomas: 2018-04-16 | planio-5495 : OpenWayTransaltor, send text message to meter display
meter.models.field.message.display=Suporta mensagens de exibiÃ§Ã£o
meter.models.field.message.display.help= Isso indica se este modelo de contador suporta a exibiÃ§Ã£o de mensagens no contador. Por exemplo, mensagem de saldo baixo

# zachv: 2018-04-09 | planio-5512 : MeterMng to support reading multiplier for mdc channel
channel.field.reading_multiplier=multiplicador de leitura
channel.field.reading_multiplier.help=Multiplicador aplicado Ã  leitura para normalizÃ¡-lo no tipo de dados correto. Por exemplo, um hidrÃ´metro pode fornecer leituras em pulsos que precisam de um multiplicador, como 0,5 pulsos por litro. NÃ£o suportado por todos os componentes mdc.

# Patrickm: 2018-03-22 | planio-5438 : Display meter location on Map
meter.location=LocalizaÃ§Ã£o do contador
usagepoint.location=LocalizaÃ§Ã£o do ponto de medida

# Renciac: 2018-02-28 | Planio 5380 : Non_billable meters
customer.agreement.billable.help= Defina como falso (desmarcado) para Contratos de cliente que sÃ£o para contadores inteligentes de consumo puramente para medir o uso, nÃ£o para compra. As leituras desses contadores sÃ£o carregadas e usadas para grÃ¡ficos de uso, por exemplo. no site EnergyInsight.
customer.agreement.billable=FaturÃ¡vel
customer.agreement.billable.setting.check=VocÃª definiu o valor faturÃ¡vel do contrato do cliente como {0}. Por favor, confirme o valor. Se definido como verdadeiro, esta conta pode ser recarregada, se definido como falso, Ã© apenas para fins de consumo de uso.
bulk.upload.billable=FaturÃ¡vel
error.field.upload.billable.invalid= FaturÃ¡vel sÃ³ pode ser verdadeiro ou falso.

# Renciac: 2018-01-24 | planio-5210 : PayTypeDiscounts & Add vat inclusive / exclusive to help messages
tariff.field.percent_charge.help=Nome opcional da taxa de carga que serÃ¡ retirada da quantia oferecida como primeiro passo no cÃ¡lculo da tarifa. (Inclui Imposto).
tariff.field.discount.help=Desconto a aplicar por tipo de pagamento. (Inclui Imposto). Deixe em branco se nenhum desconto aplicÃ¡vel a um tipo de pagamento.
tariff.field.discount.blockThin.help=Desconto a aplicar por tipo de pagamento. (Exclui Imposto). Deixe em branco se nenhum desconto aplicÃ¡vel a um tipo de pagamento.
tariff.field.unitprice.help=PreÃ§o por kWh (Exclui Imposto).
tariff.field.block.help=Especifique o preÃ§o unitÃ¡rio de atÃ© oito blocos (excluindo impostos) e o limite abaixo. Deixe o preÃ§o unitÃ¡rio e o limite em branco para blocos nÃ£o utilizados.
tariff.field.unitprice.namibia.help=PreÃ§o por kWH (impostos fiscalizados e isentos de impostos)
tou.thin.field.monthlydemand.help=Especifique o montante do serviÃ§o de cobranÃ§as altas. (Exclui Imposto).
tou.thin.field.servicecharge.help=Valor especÃ­fico do serviÃ§o de cobranÃ§a. (Exclui Imposto).
tou.thin.field.charges.help=Capturar uma tarifa para cada uma das estaÃ§Ãµes disponÃ­veis, PerÃ­odos e combinaÃ§Ã£o do tipo de leitura. (Exclui Imposto).
tou.thin.field.charges.specialday.help=Capturar quaisquer tarifas relevantes para cada um dos dias especiais abaixo. (Exclui Imposto).
register.reading.rates.help=Capturar uma taxa de tarifa para cada determinante de cobranÃ§a. (Exclui Imposto).

# rfowler : 2018-01-23 : Planio 4756
bulk.upload.file.no.meterupdata=Nenhum dado de contador/ponto de medida presente no arquivo de upload csv.

# Patrick: 2017-11-27 | planio-5127 : Capture and display power limit of a meter
meter.power_limit.instructions=Para tokens de limite de energia:\n1. Abra o Painel do contador.\n2. No bloco de informaÃ§Ãµes de Limite de potÃªncia, selecione o limite de potÃªncia na caixa de sugestÃµes Limite de potÃªncia.\n3. Isso adicionarÃ¡ uma nova caixa de listagem com opÃ§Ãµes de limite de energia - selecione o que vocÃª precisa.\n4. Ao salvar o contador, uma caixa pop-up serÃ¡ exibida para inserir mais detalhes.
meter.power_limit.container_label=InformaÃ§Ãµes de limite de energia
meter.power_limit.token.generate=Gerar tokens de limite de energia?
meter.power_limit.token.generate.help=Se o contador precisar ser atualizado para corresponder aos novos detalhes de limite de energia, um token de limite de energia precisarÃ¡ ser gerado. Se o registro estiver sendo atualizado para corresponder aos detalhes do contador, nÃ£o hÃ¡ necessidade de gerar tokens.
tokens.power_limit.no_gen=NÃ£o gere tokens de limite de energia
tokens.power_limit.gen=Gerar tokens de limite de energia

# joelc 11 January 2018, Planio 4630
grouptree.search=Procurar

# joelc 3 January 2018, Planio 4627
error.group.contains.usagepoint= O grupo atualmente contÃ©m pontos de medida e nÃ£o pode ser excluÃ­do.

# joelc 13 December 2017, Planio 4631
group.error.name.nonunique=O nome do grupo deve ser exclusivo.

# 2017-11-20 | planio-5134 : Supplier Group Code validation issue
error.field.supplygroupcode.size=O cÃ³digo deve ser igual a {0} dÃ­gitos.

# joelc 13 November 2017, Planio 4636
meter.use.existing.instructions=Copiar grupos do contador existente
meter.not.in.groups=O contador nÃ£o foi atribuÃ­do a nenhum grupo
meter.copy.selected.groups=Copie os grupos selecionados para a tela principal

# RobertF 23 October 2017, Planio 4755
bulk.upload.gencsvtemplate.title=Gerar modelo de upload
bulk.upload.gencsvtemplate.subtitle= Os campos obrigatÃ³rios foram selecionados e nÃ£o podem ser alternados.
bulk.upload.file.button.gentemplate=Gerar modelo
bulk.upload.template.required.first=InformaÃ§Ãµes: ObrigatÃ³rio
bulk.upload.template.required=ObrigatÃ³rio
bulk.upload.template.sts.required=NecessÃ¡rio para contadores STS
bulk.upload.recordstatus=Ativo (em branco/nÃ£o)
bulk.upload.installationdate.format=Data de instalaÃ§Ã£o (aaaa-MM-dd HH:mm:ss)
bulk.upload.file.button.gentemplate.description=Selecione os campos necessÃ¡rios e gere seu modelo de upload:

# Patrick | August 28, 2017 | planio-4680: Duplicate and missing keys in MeterMngAdmin translation files
appsettings.list.duplicate.item=Item duplicado inserido.
usagepoint.charge.view.dialog.invalid.date2=A data selecionada nÃ£o pode estar no futuro

# Rencia 29 August 2017, Planio 4928
meter.attached.to.up=O contador {0} estÃ¡ conectado ao ponto de medida {1}

# Thomas 7th August 2017. Planio 4815
tariff.field.minvendamount.lbl=Valor mÃ­nimo de venda
tariff.field.minvendamount.help=O valor mÃ­nimo de venda DEVE ser fornecido se a tarifa permitir a venda de menos de uma unidade. Isso ignorarÃ¡ a verificaÃ§Ã£o de que uma venda deve ser para pelo menos UMA unidade inteira.

# Patrick | July 19, 2017 | planio-4648: When save new Aux acc, confirm status
auxaccount.checkbox.active.status=Sua conta auxiliar nÃ£o estÃ¡ ativa por padrÃ£o. Gostaria de ativar esta conta?

# Thomas 18th July 2017 Planio 4353 MDCTrans Override UI
mdc.txn.override.lbl=Sobrepor
mdc.txn.override.none=Nenhum
mdc.txn.override.all=Todos
mdc.txn.override=Sobrepor

#Thomas 17th July 2017 Date validation Planio-4644
error.field.datetime.invalid=Formato de data invÃ¡lido. Formato esperado ({0})

#Rencia 24 May 2017 Improve Cell phone validation
customer.trans.upload.invalid.up.no.meter=O ponto de medida nÃ£o tem um contador anexado a ele
customer.trans.upload.invalid.up.not.active=Ponto de medida nÃ£o estÃ¡ ativo

#RobertF July 14, 2017 Planio-4421 : Meter Management: Panel to monitor vending activity
dashboard.vending.activity.graph.title=Atividade de venda
dashboard.vending.activity.graph.description=Total de vendas nos Ãºltimos 15 minutos
dashboard.vending.activity.graph.xaxis.label= Vendas
dashboard.vending.activity.graph.yaxis.label= Intervalos de 15 minutos (clique no grÃ¡fico para redefinir o zoom)

### July 13, 2017 : RobertF : Indicator Dashboard Panel ###
dashboard.key.indicator.indicator.total.sales=Vendas totais
dashboard.key.indicator.indicator.tooltip.total.sales=Vendas totais
dashboard.key.indicator.indicator.transactions=TransaÃ§Ãµes
dashboard.key.indicator.indicator.tooltip.transactions=Quantidade de transaÃ§Ãµes ocorrendo para dar vendas totais
dashboard.key.indicator.indicator.transacting.meters=contadores em transaÃ§Ã£o
dashboard.key.indicator.indicator.tooltip.transacting.meters=NÃºmero de contadores transacionados para dar vendas totais
dashboard.key.indicator.indicator.new.meters.installed=Novos contadores instalados
dashboard.key.indicator.indicator.tooltip.new.meters.installed=Novos contadores associados a pontos de medida
dashboard.key.indicator.indicator.new.active.meters.installed=Novos contadores ACTIVOS instalados
dashboard.key.indicator.indicator.tooltip.new.active.meters.installed=Novos contadores que foram associados a pontos de medida e estÃ£o ativos
dashboard.key.indicator.indicator.total.meters.usage.points=Total de contadores (pontos de medida)
dashboard.key.indicator.indicator.tooltip.total.meters.usage.points=Total de contadores assignados a pontos de medida
dashboard.key.indicator.indicator.total.active.meters.usage.points=Total de contadores ATIVOS (pontos de medida)
dashboard.key.indicator.indicator.tooltip.total.active.meters.usage.points=Total de contadores assignados a pontos de medida e ativos
dashboard.key.indicator.indicator.total.meters.device.store=Total de contadores (armazenamento de dispositivos)
dashboard.key.indicator.indicator.tooltip.total.meters.device.store=Total de contadores no armazenamento de dispositivos

#RobertF June 22, 2017 Planio-4420 : Panel with the sales per resource chart
dashboard.sales.per.resource.graph.title=Vendas por recurso
dashboard.sales.per.resource.graph.day.description=Vendas de recurso por dia
dashboard.sales.per.resource.graph.month.description=Vendas de recurso por mÃªs
dashboard.sales.per.resource.graph.xaxis.day.label=Data (clique no grÃ¡fico para visualizar o mÃªs)
dashboard.sales.per.resource.graph.xaxis.month.label=Data (clique no grÃ¡fico para visualizar o dia)
dashboard.sales.per.resource.graph.yaxis.label=Vendas totais

# Thomas 21 June 2017 UP Blocking
usagepoint.field.blocking.help=Selecione o tipo de bloqueio para o ponto de medida.
usagepoint.field.blocking.label=Tipo de bloqueio
usagepoint.field.blocking.type.default=NÃ£o bloqueado
usagepoint.blocking.enter.reason=Insira um motivo para o bloqueio
usagepoint.blocking.select.reason=Selecione um motivo para bloquear

#RobertF June 2, 2017 Planio-4419 : Panel with the count regarding the Owner and Building usage point groups added over time
dashboard.groups.added.graph.title=Grupos de pontos de medida adicionados
dashboard.groups.added.graph.day.description=Grupos adicionados por dia
dashboard.groups.added.graph.month.description=Grupos adicionados por mÃªs
dashboard.groups.added.graph.xaxis.day.label=Data (clique no grÃ¡fico para visualizar o mÃªs)
dashboard.groups.added.graph.xaxis.month.label=Data (clique no grÃ¡fico para visualizar o dia)
dashboard.groups.added.graph.yaxis.label=Grupos adicionados

# Joel 30 May 2017  Centian Data display planio 4429
meter.centian.header = InformaÃ§Ãµes do contador Centian
meter.centian.kwh.credit.remaining=CrÃ©dito kWh restante:
meter.centian.currency.credit.remaining=Moeda CrÃ©dito restante:
meter.centian.number.disconnections=NÃºmero de DesconexÃµes:
meter.centian.tamper.detected=Estados de tamper detectados:
meter.centian.tamper.none=Nenhum estado de tamper foi detectado
meter.centian.tamper.updated= InformaÃ§Ãµes da data recuperadas:
meter.centian.tamper.overpower=SobrepotÃªncia
meter.centian.tamper.overvoltage=SobretensÃ£o
meter.centian.tamper.lowvoltage=SubtensÃ£o
meter.centian.tamper.overfrequency=SobrefrequÃªncia
meter.centian.tamper.lowfrequency=SubfrequÃªncia
meter.centian.tamper.reverseenergy=Energia reversa
meter.centian.tamper.opencover=tampa aberta
meter.centian.tamper.magnettamper=Tamper magnÃ©tico
meter.centian.tamper.bypassearth=Bypass/Earth tamper
meter.centian.tamper.sequenceerror=Erro de sequÃªncia
meter.centian.tamper.overtemperature=Temperatura alta
meter.centian.tamper.lowtemperature=Temperatura baixa
meter.centian.tamper.phaseunbalance=DesequilÃ­brio de Fase
meter.centian.tamper.phasevoltageloss=Perda de TensÃ£o de Fase
meter.centian.tamper.tariffconfigerror=Erro de ConfiguraÃ§Ã£o de Tarifa
meter.centian.tamper.metrologyfail=Falha de metrologia

meter.centian.current.tamper.status.header=Contador em tamper mode
meter.centian.current.tamper.status.description=Estados Tamper com um tique foram detectados neste contador.
meter.centian.current.tamper.status.description.none=Nenhum estado de Tamper foi detectado neste contador.
meter.centian.current.tamper.status.updated=Estado tamper atualizado:

### May 17, 2017 : RobertF : Admin dashboard workspace ###
admin.dashboard.title=Painel (Administrador)

#Rencia 24 April 2017 Improve Cell phone validation
meter.online.bulk.customer.phone.help=Digite o nÃºmero de telefone do cliente para o qual um token de emissÃ£o gratuita serÃ¡ enviado por sms, se aplicÃ¡vel. Os nÃºmeros de SMS devem estar no formato internacional, comeÃ§ando com um +.
messaging.recipient.help=Para SMS, deve usar nÃºmeros de telefone internacionais.
cellPhone.pattern.description=campo do nÃºmero de telefone para sms deve estar no formato internacional, ou seja, comeÃ§ar com um +. VocÃª pode usar espaÃ§os em branco, parÃªnteses (), hÃ­fens e pontos - eles sÃ£o removidos e o nÃºmero de telefone resultante (excl. o +) deve ter no mÃ­nimo 4 dÃ­gitos e no mÃ¡ximo 25 de comprimento, dependendo da sua localidade. Consulte o espaÃ§o reservado.

#Thomas 8th May 2017 Tariff Date validation
tariff.error.startdate.unique=Data de inÃ­cio duplicada. Especifique uma data de inÃ­cio exclusiva.
#tariff.error.startdate=A Data de inÃ­cio deve ser futura e posterior Ã  data de inÃ­cio da tarifa atualmente ativa.

### May 2, 2017 : RobertF : Indicator Dashboard Panel ###
dashboard.key.indicator.title=Indicadores-chave
dashboard.key.indicator.description=Principais indicadores do sistema em vÃ¡rios perÃ­odos de tempo.
dashboard.key.indicator.indicator=Indicador
dashboard.key.indicator.value.today=Hoje
dashboard.key.indicator.value.monthtodate=Do mÃªs atÃ© a data
dashboard.key.indicator.value.lastmonth=MÃªs passado

# Patrick | May 15, 2017 | planio-4443 - Updates for help message from field behaviour change
meter.algorithmcode.help=Selecione o cÃ³digo de algoritmo correto. Este campo Ã© necessÃ¡rio para ativaÃ§Ã£o - o contador nÃ£o pode ser salvo sem que um cÃ³digo de algoritmo seja selecionado.
meter.tokentechcode.help=Selecione o cÃ³digo de token de engenharia correto. Este campo Ã© necessÃ¡rio para ativaÃ§Ã£o - o contador nÃ£o pode ser salvo sem que um cÃ³digo de talÃ£o de engenharia seja selecionado.
meter.supplygroupcode.help=Insira o cÃ³digo atual do supply group. Este campo Ã© necessÃ¡rio para ativaÃ§Ã£o - o registro nÃ£o pode ser salvo sem o mesmo.
meter.tariffindex.help=Insira o Ã­ndice de tarifa real. Este campo Ã© necessÃ¡rio para ativaÃ§Ã£o - o registro nÃ£o pode ser salvo sem o mesmo.

#Rencia 24 April 2017 Meter Bulk Online Search / Capture : Free Issue Token
meter.online.bulk.free.issue.title=EmissÃ£o gratuita de token
meter.online.bulk.free.issue.generate=Gerar token
meter.online.bulk.free.issue.sms.token=Enviar token por SMS ao utilizador
meter.online.bulk.free.issue.sms.token.help=Selecione aqui se quer que a emissÃ£o gratuita de token seja enviada ao usuÃ¡rio por SMS.
meter.online.bulk.free.issue.check.sms.not.selected=Escolheu gerar um token de emissÃ£o gratuita mas NÃO o seu envio ao usuÃ¡rio por SMS.
meter.online.bulk.free.issue.sms.invalid.phone= Para enviar token por SMS Ã© necessÃ¡rio um nÃºmero de telefone mÃ³vel vÃ¡lido
meter.online.bulk.free.issue.invalid.units= NÃºmero de unidades de emissÃ£o gratuita tem de ser numÃ©rico e maior do que zero
meter.online.bulk.free.issue.sms=EmissÃ£o gratuita de token {2}: NÃºmero de Contador: {0} Token: {1}
meter.online.bulk.free.issue.token.null=Contador foi adicionado ao grupo mas sem capacidade para gerar o token gratuito.
meter.online.bulk.free.issue.token.error=Contador foi adicionado ao grupo mas erro de Token: {0}
credit.token.link=Ver crÃ©dito de tokens
eng.token.link=Ver tokens de engenharia

#Rencia 21 April 2017 Meter Bulk Online Search / Capture : Edit / Remove buttons in table
meter.online.bulk.no.edit=Apenas certos campos em pontos de medida ACTIVOS podem ser editados neste local. Use o link para ir para a pÃ¡gina dos pontos de medida, para outros.
meter.online.bulk.no.remove=Ponto de medida nÃ£o tem contador para ser removido. Use o link para ir para a pÃ¡gina dos pontos de medida.
button.clear.panel=Apagar painel
button.clear.groups=Apagar grupos
online.bulk.panel.tariffindex.help=Insira o Ã­ndice de tarifa real. Este campo Ã© necessÃ¡rio para ativaÃ§Ã£o. Para alterar um contador existente, use a pÃ¡gina dos pontos de uso.
online.bulk.panel.supplygroupcode.help=Insira o cÃ³digo atual do supply group. Este campo Ã© necessÃ¡rio para ativaÃ§Ã£o. Para alterar um contador existente, use a pÃ¡gina dos pontos de uso.
error.field.breakerid.max=ID do Disjuntor nÃ£o deve exceder o nÃºmero mÃ¡ximo de 100 caracteres.
meter.online.bulk.meter.updated= Contador {0} atualizado

### April 11, 2017 : Patrick : Send Reprint ###
button.send_reprint=Enviar reimpressÃ£o
messaging.type.sms=SMS
messaging.type.email=E-mail
messaging.recipient=DestinatÃ¡rio
messaging.message.type=Tipo de mensagem
messaging.message.label=Mensagem
messaging.token.reprint.email.subject=NecessÃ¡ria reimpressÃ£o de token
token.label=SÃ­mbolo
error.field.required.recipient.email=NecessÃ¡rio e-mail do destinatÃ¡rio
error.field.required.recipient.phone=NecessÃ¡rio nÃºmero de telefone do destinatÃ¡rio
error.field.validity.phone=NÃºmero de telefone Ã© invÃ¡lido.

notification.message.send.status.sms=SMS enviado com sucesso
notification.message.send.status.email=Mensagem de e-mail enviada com sucesso

messaging.txn.date=Data da transaÃ§Ã£o
messaging.txn.meter_no=NÃºmero do contador
messaging.txn.token=SÃ­mbolo

#Rencia 31 March 2017 Meter Meter Bulk Online Search / Capture : Popup more information when click on meter in table
more.info=Mais informaÃ§Ã£o - Arraste para aqui
popup.label=campo
popup.value=valor

#Rencia 26 March 2017 Meter Meter Bulk Online Search / Capture : Add pricing Structure Tariff Popup
online.bulk.panel.tariff.title=Tarifa actual
meter.online.bulk.add.group.title=Adicionar / Alterar Grupos

#Robertf 27 March 2017 Usage Point page check for valid installation date
error.field.startdate.invalid=Data de inÃ­cio nÃ£o Ã© uma data vÃ¡lida. Formato = {0}

#Rencia 26 March 2017 Meter Meter Bulk Online Search / Capture : Add New Group function & phone no
customer.phone=NÃºmero de telefone


#Rencia 10 March 2017 Meter Bulk Online Search / Capture add group entry function
grouptype.field.layout.order=Ordem do Layout
grouptype.field.layout.order.help=Esta Ã© a ordem pela qual as caixas de seleÃ§Ã£o dos pontos de medida sÃ£o desenvolvidas na pÃ¡gina. Se nÃ£o forem incorporados serÃ£o colocados, por defeito, DEPOIS de todas as numeradas, por ordem alfabÃ©tica.
grouptype.field.layout.order.error.numeric=A ordem de layout deve ser um nÃºmero inteiro.
grouptype.field.layout.order.error.duplicate= A ordem de layout Ã© uma duplicaÃ§Ã£o. O mesmo que o grupo {0}.
error.field.installdate.invalid=A data de instalaÃ§Ã£o nÃ£o Ã© uma data vÃ¡lida. Formato = {0}

#Rencia 27 February 2017 Meter Bulk Online Search / Capture
meter.online.bulk.header=Adicionar contadores ao grupo/s
meter.online.bulk.title=Selecione ou adicione grupo/s
meter.online.bulk.installdate=Data de instalaÃ§Ã£o
meter.online.bulk.select.meters.button=Selecione contadores para grupo/s
meter.online.bulk.usagepoint.status=estado
meter.online.bulk.search.no.results=NÃ£o foram encontrados resultados de pesquisa correspondentes.
meter.online.bulk.add.meters.to.groups=Adicionar contadores ao grupo/s
meter.online.bulk.button.add=Adicionar contador
meter.online.bulk.add.meter=Adicionar contador
meter.online.bulk.edit.meter=Editar contador

online.bulk.panel.up.group.info.title=InformaÃ§Ã£o do ponto de medida
online.bulk.panel.customer.info.title=InformaÃ§Ã£o do cliente
online.bulk.panel.meter.help=Comece a digitar o nÃºmero do contador; os contadores que iniciam com esses dÃ­gitos no armazÃ©m selecionados aparecerÃ£o numa lista suspensa.Clique num para o selecionar.
online.bulk.panel.select.store.help=O armazÃ©m do qual os contadores serÃ£o selecionados. Se nenhum for selecionado, serÃ£o exibidos como sugestÃµes de nÃºmeros de contadores de todos os armazÃ©ns. Observe que quando o contador Ã© atribuÃ­do a um ponto de medida, ele serÃ¡ automaticamente removido do armazÃ©m.
online.bulk.panel.suite.no.text=Unidade
online.bulk.panel.tenant.text=usuÃ¡rio
online.bulk.panel.surname.help=Insira o nome de FamÃ­lia do usuÃ¡rio. O padrÃ£o serÃ¡ uma sequÃªncia numÃ©rica. Este Ã© um campo obrigatÃ³rio - ponto de medida nÃ£o pode ser ativado sem o mesmo.

online.bulk.panel.error.supply.grpcode.empty=Para contadores STS, o cÃ³digo de supply group deve ser selecionado
online.bulk.panel.error.algorithm.code.empty=Para contadores STS, o cÃ³digo de algoritmo deve ser selecionado
online.bulk.panel.error.token.tech.code.empty=Para contadores STS, Token de engenharia deve ser selecionado
online.bulk.panel.error.tariff.indx.empty=Para contadores STS, deve ser apresentado o Ã­ndice de tarifa com o tamanho mÃ¡ximo 2
online.bulk.panel.error.key.rev.indx.empty=Para contadores STS, o cÃ³digo de chave do nÃºmero de revisÃ£o deve ser inserido e deve ser numÃ©rico
online.bulk.panel.error.ps.meter.model.empty=O modelo do contador Ã© necessÃ¡rio para seleÃ§Ã£o da estrutura de preÃ§os
online.bulk.panel.error.model.new.pricingstructure.required=O modelo do contador nÃ£o suporta a estrutura de preÃ§os
online.bulk.panel.error.meter.num.not.found= O nÃºmero do contador nÃ£o estÃ¡ na base de dados.
question.confirm.continue.new= Capturar como contador novo para este grupo?
online.bulk.panel.error.meter.already.linked= O nÃºmero do contador jÃ¡ se encontra ligado ao ponto de medida.
question.confirm.continue.open.up.page=Abrir janela de ponto de medida para isso?
online.bulk.panel.error.meter.linked.to.diff.store= O nÃºmero do contador estÃ¡ num armazÃ©m diferente, {0}.
question.confirm.continue.save.anyway=Continuar e ligar a este grupo?
online.bulk.meter.error.groups.not.selected=Para gravar um contador, todos os pontos de medida obrigatÃ³rios devem ser selecionados.

meter.key.revision.help=Insira o nÃºmero atual da chave de revisÃ£o. Este campo Ã© necessÃ¡rio para ativaÃ§Ã£o.
meter.key.revision=Chave de revisÃ£o

online.bulk.sts.meter.save.error=Problema para gravar contador STS, cÃ³digos nÃ£o encontrados.
online.bulk.meter.save.error=Problema para gravar contador.

meter.sts.length= Os nÃºmeros dos contadores STS nÃ£o podem ter mais do que 13 e menos do que 11 caracteres.

#Njigi 3 March 2017
usagepoint.charge.button.writeoff= Eliminar cobranÃ§as
usagepoint.charge.button.upchargeview=Ver cobranÃ§as pendentes
usagepoint.charge.view.dialog.heading=Ver cobranÃ§as pendentes
usagepoint.charge.writeoff.dialog.heading=Lista de cobranÃ§as pendentes: {0}
usagepoint.charge.no.data=NÃ£o hÃ¡ dados disponÃ­veis para visualizaÃ§Ã£o.
usagepoint.charge.view.dialog.nodate.filter=Por favor, selecione dados.
usagepoint.charge.view.dialog.invalid.date=Um dado selecionado deve ser posterior ao Ãºltimo ciclo de dados exibido
usagepoint.charge.writeoff.trans.success=EliminaÃ§Ã£o bem sucedida.
usagepoint.charge.writeoff.trans.failure=Processo de eliminado. Entre em contato com o serviÃ§o de suporte.
chargewriteoff.save.error=gravaÃ§Ã£o do registo falhou.

#Njigi 30 January 2017 ####
auxaccount.trans.upload=Carregamento de transaÃ§Ã£o auxiliar
auxaccount.trans.upload.heading=Carregamento de transaÃ§Ã£o auxiliar
auxaccount.trans.upload.auxaccountname=Nome da conta auxiliar
auxaccount.trans.upload.agreementref=ReferÃªncia do contrato
auxaccount.trans.upload.data.title=Importar transaÃ§Ãµes de ajuste de saldo da conta auxiliar
auxaccount.trans.upload.data.description=Selecione o arquivo CSV que contÃ©m as transaÃ§Ãµes auxiliares para que sejam importadas para o sistema de gerenciamento de contadores.
auxaccount.trans.upload.invalid.auxaccountname=Nome da conta auxiliar, mÃ¡ximo de 100 caracteres
auxaccount.trans.upload.invalid.auxaccount=NÃ£o existe conta auxiliar com o nome e referÃªncia de contrato indicado
auxaccount.trans.upload.process.failed=Erro de sistema na transaÃ§Ã£o:Nome da conta auxiliar= {0}, ReferÃªncia do contrato= {1}. Tente enviar o arquivo de novo.
trans.bulk.upload.amt.incl.tax=Montante incluindo taxas
trans.bulk.upload.amt.tax=Montante das taxas
trans.bulk.upload.trans.date=Data da transaÃ§Ã£o
trans.bulk.upload.account.ref=ReferÃªncia da conta
trans.bulk.upload.comment=ComentÃ¡rio
trans.bulk.upload.invalid.amt.incl.tax=Montante incluindo taxas nÃ£o Ã© numÃ©rico
trans.bulk.upload.invalid.amt.tax=Montante das taxas nÃ£o Ã© numÃ©rico
trans.bulk.upload.invalid.account.ref=ReferÃªncia da conta, mÃ¡ximo de 100 caracteres
trans.bulk.upload.invalid.comment=ComentÃ¡rios, mÃ¡ximo de 255 caracteres
trans.bulk.upload.invalid.our.ref=Nossa referÃªncia, mÃ¡ximo de 100 caracteres(renomear arquivo)
trans.bulk.upload.trans.date.in.future=Data da transaÃ§Ã£o nÃ£o pode ser no futuro

#Njigi 16 January 2016 ####
auxaccount.upload=Carregamento da conta auxiliar
auxaccount.upload.heading=Carregamento da conta auxiliar
auxaccount.upload.data.title=Importar dados da conta auxiliar
auxaccount.upload.data.description=Selecione o arquivo CSV que contÃ©m os dados da conta auxiliar para que sejam importados para o sistema de gerenciamento de contadores.
auxaccount.upload.errors=Erros
auxaccount.upload.identifierType=Tipo de identificador
auxaccount.upload.identifier=identificador
auxaccount.upload.auxaccountname=Nome da conta auxiliar
auxaccount.upload.auxtype=Nome do tipo de conta auxiliar
auxaccount.upload.accountpriority=prioridade
auxaccount.upload.chrgschdlname=Nome do agendamento de cobranÃ§as
auxaccount.upload.principleamaount=Montante principal
auxaccount.upload.balance=BalanÃ§o
auxaccount.upload.customerref=ReferÃªncia do contrato do cliente
auxaccount.upload.invalid.identifiertype=Tipo de identificador deve ser a referÃªncia do contrato / Nome do ponto de medida / nÃºmero do contador
auxaccount.upload.invalid.identifier=Identificador invÃ¡lido - nÃ£o se encontra na base de dados
auxaccount.upload.invalid.agreement=Ponto de uso nÃ£o tem contrato de cliente no local
auxaccount.upload.invalid.usagepoint.or.agreement=Contador nÃ£o tem ponto de uso ou o ponto de uso nÃ£o tem contrato
auxaccount.upload.invalid.auxaccountname=Nome da conta auxiliar, mÃ¡ximo de 100 caracteres
auxaccount.upload.invalid.principleamaount=Montante principal nÃ£o Ã© numÃ©rico
auxaccount.upload.invalid.balance=Montante do balanÃ§o nÃ£o Ã© numÃ©rico
auxaccount.upload.successful.count=Total de {0} carregamentos de contas auxiliares processadas com sucesso.

#Antonyo 12 January 2017  ####
#power.limit.edit.label.prompt=Nome
#power.limit.edit.label.help=Nome do valor do limite de potÃªncia. Mostrar texto
power.limit.add.button.prompt=Adicione limite de potÃªncia
power.limit.table.label.header=Nome do limite de potÃªncia
power.limit.table.value.header=Valor do limite de potÃªncia
power.limit.edit.popup.header=Editar limite de potÃªncia

#Patrick 02 December 2016 ####
user.custom.field.datatype.text=Texto
user.custom.field.datatype.numeric=numÃ©rico
user.custom.field.datatype.date=Dados
user.custom.field.datatype.boolean=Verdadeiro/Falso
user.custom.field.datatype.list=lista
button.done=feito

usagepointgroup.custom.field.error.text= Ã necessÃ¡rio um valor para este campo
usagepointgroup.custom.field.error.list=Deve selecionar um item da lista
usagepointgroup.custom.field.error.date.empty=Ã necessÃ¡ria uma data vÃ¡lida. Formato (aaaa-MM-dd HH:mm:ss)
usagepointgroup.custom.field.error.date.invalid=Formato de data invÃ¡lido. Formato (aaaa-MM-dd HH:mm:ss)
usagepointgroup.custom.field.error.numeric= Deve ser fornecido um nÃºmero vÃ¡lido

button.view.usagepointgroup=Ver grupo do ponto de medida

usagepointgroup.custom.field.default.datatype.description=Categorias de dados para este campo personalizado, ou uma lista separada por virgulas para listas suspensas
usagepointgroup.hierarchy.no.additionalinfo=NÃ£o hÃ¡ informaÃ§Ã£o adicional para este grupo de ponto de medida

appsettings.popup.button.add=Acrescentar item
appsettings.popup.table.add=Novo Item - Clique para editar
appsettings.popup.table.title=lista de itens
appsettings.popup.table.note.dups= A duplicaÃ§Ã£o de itens serÃ¡ removida quando concluÃ­da

appsetting.field.datatype=Categorias de dados
appsetting.field.datatype.help=Categorias de dados para a configuraÃ§Ã£o da aplicaÃ§Ã£o. Para lista de categorias de dados, os itens da lista serÃ£o excluÃ­dos quando as categorias de dados forem alteradas para: Texto, NumÃ©rico, Dados, ou Verdadeiro/Falso.
appsetting.field.datatype.listitems=Editar Itens

#Njigi 22 December 2016 ####
bulk.upload.custom.fields.get.error=Erro na base de dados ao buscar de dados para este campo personalizado. Contacte o apoio tÃ©cnico.

#Njigi 28 November 2016 ####
bulk.upload.meterupload.heading=carregamento de contadores
bulk.upload.meterupload.enddevicestorename=Nome do exÃ©rcito de equipamentos
bulk.upload.meterupload.data.title=Importar especificaÃ§Ãµes do contador
bulk.upload.meterupload.description=Selecione o arquivo CSV que contÃ©m as especificaÃ§Ãµes do contador a importar para o sistema de gerenciamento de contadores.

#Special Actions - joelc 01/12/16 
link.special.actions=AÃ§Ãµes com motivos
link.special.action.reasons = Motivo para aÃ§Ã£o excepcional
button.viewreasons = Ver lista de motivos
special.action = AcÃ§Ã£o especial
special.actions.header=Configurar aÃ§Ãµes especiais
special.actions.title=Atualize aÃ§Ãµes especiais
special.action.name=Nome da aÃ§Ã£o
special.action.name.help= O nome desta aÃ§Ã£o especial.
special.action.reason.required= Exigido
special.action.reason.required.help= Ã exigido ou opcional fornecer um motivo
special.action.reason.input.type= Inserir tipo
special.action.reason.input.type.help= O motivo Ã© dado como texto livre, Ã© selecionado de uma lista de motivos ou pode ser texto livre e/ou selecionado de uma lista
special.action.description=DescriÃ§Ã£o da aÃ§Ã£o
special.action.description.help=Uma descriÃ§Ã£o da aÃ§Ã£o especial que dÃª um motivo para que a aÃ§Ã£o seja existente
special.actions.field.name=
special.actions.field.description=DescriÃ§Ã£o
special.actions.field.reason.required=Exigido
special.actions.field.reason.input.type=Inserir tipo
special.actions.reason.inputtype.freetext=Insira o motivo na caixa de texto
special.actions.reason.inputtype.selected= Selecione o motivo da lista
special.actions.reason.inputtype.both=Insira o motivo ou selecione da lista

special.action.reasons.header = Motivos da configuraÃ§Ã£o
special.action.reasons.title = Motivos das aÃ§Ãµes especiais
special.action.reasons.title.add = Adicionar novo motivo
special.action.reasons.title.update = Atualizar motivo
special.action.reasons.field.name = motivo
special.action.reasons.field.description = DescriÃ§Ã£o do motivo
special.action.reasons.field.recordstatus = Ativo
special.action.reasons.name = motivo
special.action.reasons.name.help = Insira um motivo possÃ­vel para fazer esta aÃ§Ã£o. Este serÃ¡ exibido na lista suspensa para essa aÃ§Ã£o.
special.action.reasons.description = DescriÃ§Ã£o
special.action.reasons.description.help = Descreva do que trata este motivo.
special.action.reasons.active = Ativo
special.action.reasons.active.help =Este motivo estÃ¡ ativo?
special.action.reason = motivo
special.action.and.or = e/ou
special.action.enter.reason = Insira um motivo para esta aÃ§Ã£o
special.action.select.reason = Selecione um motivo para esta aÃ§Ã£o
error.special.action.reason.required = Ã necessÃ¡rio um motivo para esta aÃ§Ã£o
usagepoint.deactivate.enter.reason = Insira um motivo para esta desativaÃ§Ã£o
usagepoint.deactivate.select.reason = Selecione um motivo para esta desativaÃ§Ã£o

#Rencia 24 November 2016  ####
bulk.upload.pricingstructure.not.active=Estrutura de preÃ§os nÃ£o estÃ¡ ativa.

#Rencia 16 November 2016  ####
error.field.active.invalid=Ativar deve estar em branco (implica SIM) ou nÃ£o.
error.field.metermodelid.null=Ã exigido o modelo do contador.
error.field.ststariffindex.max=O Ã­ndice de tarifa deve ser menor que {0} caracteres.
error.field.servicelocationid.null= Ã necessÃ¡rio serviÃ§o de localizaÃ§Ã£o
error.field.customerkindid.null=Tipo de cliente
error.field.customerid.null=Ã necessÃ¡rio a identificaÃ§Ã£o do cliente
error.field.accountbalance.null=Ã exigido saldo da conta.

#Bulk Upload - standard
bulk.upload.download.sample=Baixe um exemplo de planilha.
bulk.upload.download.sample.button=Baixe como .CSV
bulk.upload.file.select.labeltext=Selecione arquivo CSV Arquivo a carregar
bulk.upload.select.file.help=Selecione um arquivo contendo uma informaÃ§Ã£o no formato csv especificado, para ser importado para o sistema
bulk.upload.csv.button=Carregar dados CSV
bulk.upload.process.button=Processo de carregamento
bulk.upload.errors=Erros
bulk.upload.object.creation.error=Erro ao criar objeto {0}. Contacte o apoio tÃ©cnico!
bulk.upload.file.action.unknown=AcÃ§Ã£o de carregamento de ficheiro desconhecido, contacte o apoio tÃ©cnico
bulk.upload.file.none=Nenhum arquivo foi selecionado para ser carregado
bulk.upload.invalid.filename=Nome do arquivo indevido - hifen ou espaÃ§o em falta. Nome dos arquivos deve ser xxxxxx-referencia.csv onde xxxxxx Ã© o identificador para o arquivo escolhido, ex. CarregamentoDeContador, e <referencia> Ã© gravado como 'nossa Ref' nas transaÃ§Ãµes
bulk.upload.invalid.filename.changed=Nome do arquivo foi alterado entre etapas! Era {0}; Ã¡gora {1}
bulk.upload.invalid.cannot.create.dir=ERRO! NÃ£o pode criar o diretÃ³rio. Por favor, contacte o apoio tÃ©cnico.
bulk.upload.file.unrecognized.heading.error=TÃ­tulo da coluna nÃ£o reconhecido ao carregar arquivo: {0}
bulk.upload.table.heading.valid=Dados de carregamento vÃ¡lidos: amostra das primeiras 15 linhas no arquivo
bulk.upload.table.heading.errors=Dados de carregamento: Erros
bulk.upload.trans.validation.errors=ValidaÃ§Ã£o de erros encontrados. Por favor, corrija e submeta de novo. Limite de processamento de 15 erros em simultÃ¢neo.
bulk.upload.filename= Nome do arquivo selecionado={0}
bulk.upload.process.failed=Erro de sistema ao carregar apÃ³s {0} registros. Experimente subscrever de novo o arquivo.
bulk.upload.active=Ativo
#Bulk Upload Validation Errors
bulk.upload.invalid.required.field={0} Exigido
bulk.upload.invalid.nonexisting.field={0} NÃ£o se encontra na base de dados
bulk.upload.duplicate.field={0} jÃ¡ existe, na base de dados ou este arquivo
bulk.upload.invalid.field={0} Ã© invÃ¡lido
bulk.upload.invalid.parsedate=NÃ£o consegue analisar {0} - confira formato
#bulk.upload.file.process.error=Erro ao processar o arquivo
bulk.upload.file.process.error=Erro ao carregar o arquivo

#Bulk Upload - meter / Customer / Usage Point
bulk.upload.data.title.meter=Importar contador, ponto de uso e dados do cliente
bulk.upload.data.description.meter=Selecione o arquivo CSV que contÃ©m o contador e as especificaÃ§Ãµes relacionadas para importar no sistema de gerenciamento de contadores.
bulk.upload.metertype=Tipo de contador
bulk.upload.meternum=NÃºmero do contador
bulk.upload.serialnum=NÃºmero de sÃ©rie
bulk.upload.mrid= NÃºmero externo do contador
bulk.upload.metermodelname=Modelo de contador
bulk.upload.enddevicestore=ArmazÃ©m de equipamentos
bulk.upload.breakerid=ID do disjuntor
bulk.upload.ststokentechcode=CÃ³digo de token de engenharia
bulk.upload.stsalgorithmcode=cÃ³digo de algoritmo
bulk.upload.stssupplygroupcode=CÃ³digo do supply group
bulk.upload.stskeyrevisionnum=Chave do NÃºmero de RevisÃ£o
bulk.upload.ststariffindex=Ã­ndice de tarifa
bulk.upload.usagepointname=Nome do ponto de uso
bulk.upload.installationdate=Data da instalaÃ§Ã£o
bulk.upload.pricingstructurename=Estrutura de preÃ§os
bulk.upload.uperfnumber=nÃºmero UP Erf
bulk.upload.upstreetnum=UP nÃºmero de rua
bulk.upload.upbuildingname=EdifÃ­cio UP
bulk.upload.upsuitenum=UP nÃºmero de suÃ­te
bulk.upload.upaddressline1=UP EndereÃ§o 1
bulk.upload.upaddressline2=endereÃ§o UP 2
bulk.upload.upaddressline3=UP EndereÃ§o 3
bulk.upload.uplatitude=Latitude UP
bulk.upload.uplongitude=Longitude CIMA
bulk.upload.upcustomvarchar1=UP Campo de texto personalizado1
bulk.upload.upcustomvarchar2=UP Campo de texto personalizado2
bulk.upload.upcustomnumeric1=UP Campo numÃ©rico personalizado1
bulk.upload.upcustomnumeric2=UP Campo numÃ©rico personalizado2
bulk.upload.upcustomtimestamp1=UP Campo de dados/hora1
bulk.upload.upcustomtimestamp2=UP Campo de dados/hora2
bulk.upload.customerkind=Tipo de cliente
bulk.upload.companyname=Nome da empresa
bulk.upload.taxnum=NÃºmero de taxa
bulk.upload.firstnames=Nomes prÃ³prios
bulk.upload.surname=Apelido
bulk.upload.initials=Iniciais
bulk.upload.title=TÃ­tulo
bulk.upload.email1=E-mail1
bulk.upload.email2=Email2
bulk.upload.phone1=Telefone1
bulk.upload.phone2=Telefone2
bulk.upload.custerfnumber=Personalizar Erf
bulk.upload.custstreetnum=Personalizar nÃºmero de rua
bulk.upload.custbuildingname=Personalizar edifÃ­cio
bulk.upload.custsuitenum=Personalizar suite
bulk.upload.custaddressline1=Personalizar morada 1
bulk.upload.custaddressline2=Personalizar morada 2
bulk.upload.custaddressline3=Personalizar morada 3
bulk.upload.custlatitude=Personalizar Latitude
bulk.upload.custlongitude=Personalizar Longitude
bulk.upload.custcustomvarchar1=Personalizar campo de texto1
bulk.upload.custcustomvarchar2=Personalizar campo de texto2
bulk.upload.custcustomnumeric1=Personalizar campo numÃ©rico1
bulk.upload.custcustomnumeric2=Personalizar campo numÃ©rico2
bulk.upload.custcustomtimestamp1=Personalizar Campo de dados/hora1
bulk.upload.custcustomtimestamp2=Personalizar Campo de dados/hora2
bulk.upload.agreementref=ReferÃªncia do contrato
bulk.upload.startdate=Data de inÃ­cio do contrato
bulk.upload.accountname=Nome da conta
bulk.upload.accountbalance=Saldo da conta
bulk.upload.lowbalancethreshold=Limite de saldo mÃ­nimo
bulk.upload.notificationemail=E-mail de notificaÃ§Ã£o
bulk.upload.notificationphone=Telefone para notificaÃ§Ã£o
bulk.upload.notifylowbalance=Notificar saldo baixo
#Invalid Meter bulk upload errors
bulk.upload.metertype.incompatible.stsinfo=Dados de criptografia STS inseridos sÃ£o para contador nÃ£o-STS
bulk.upload.metertype.incompatible.metermodel=Modelo do contador nÃ£o Ã© compatÃ­vel com o tipo de contador
bulk.upload.invalid.pricingstructure.incompatible.metermodel=Estrutura de preÃ§os incompatÃ­vel com o modelo do contador
bulk.upload.customer.account.notfor.STS=Contadores STS nÃ£o devem ter dados da conta do cliente

#Rencia 11 November 2016  ####
tariff.field.namibia.neflevy=Imposto do Fundo Nacional de Energia
tariff.field.namibia.neflevy.help=Inserir imposto de Fundo Nacional de Energia
tariff.field.namibia.ecblevy=Taxa do BCE
tariff.field.namibia.ecblevy.help=Inserir imposto para o Quadro de Controle de Eletricidade
tariff.error.positive.or.zero=Valor deve ser positivo ou zero

#Rencia 9 November 2016  ####
tariff.field.discount=Descontos segundo forma de pagamento
tariff.field.heading.paytype=Tipo de pagamento
tariff.field.heading.discount=Desconto %
tariff.field.heading.description=DescriÃ§Ã£o
tariff.error.discount=% de desconto deve ser positivo ou zero.
tariff.error.discount.descrip=Se % de desconto nÃ£o for zero, Ã© necessÃ¡ria descriÃ§Ã£o.

# Rencia 20 October 2016  ####
### New
tariff.field.cycle.name=ciclo
tariff.field.cycle.name.help=Selecione o ciclo de faturaÃ§Ã£o.
tariff.field.cost.help=O valor excluÃ­do taxas deve ser devolvido segundo o ciclo selecionado.

### Took out "Monthly"
tariff.field.cost.name=Nome do custo
tariff.field.cost.name.help=nome deste custo serÃ¡ o que aparecerÃ¡ no recibo do cliente.
tariff.field.cost=Custo excluindo taxas

### Tariff Cycle Labels  NEW
tariff.cost.cycle.daily=diÃ¡rio
tariff.cost.cycle.monthly=Mensal
tariff.cost.cycle.error=Deve selecionar um ciclo vÃ¡lido.

### changed names
tariff.error.cyclic.charge.positive=Deve ser um valor positivo.
tariff.error.cyclic.charge=Especifique um custo para o nome.
tariff.error.cyclic.charge.name=Especifique um nome para o custo.

#Rencia 22 August 2016  ####
error.field.numeric.positive_not_zero=Valor deve ser positivo e nÃ£o zero.
tariff.field.percent_charge_name=Nome da porcentagem de carga
tariff.field.percent_charge_name.help=Nome opcional da porcentagem de carga que aparecerÃ¡ no recibo.
tariff.field.percent_charge=Porcentagem de carga
tariff.error.percent_charge.name=Especifique um nome para a carga.
tariff.error.percent_charge.positive_not_zero=Deve ser um nÃºmero positivo e nÃ£o zero

##########################
#### General messages ####
##########################

application.default.title=iPay - GestÃ£o de Contadores
application.title=GestÃ£o de Contadores
workspace.usagepoint.information=InformaÃ§Ã£o e ServiÃ§os
unit.kilowatthour.symbol=kWh
unit.watts.symbol=C
unit.percent=%
changes_unsaved=* AlteraÃ§Ãµes nÃ£o guardadas
menu.about_link=Sobre
application.info=DistribuÃ­do por iPay's BizSwitch, Traduzido por Resul - Equipamentos de Energia, SA

###############
#### Error ####
###############
error.title=Erro
error.general=Ocorreu um erro e foi registrado. Por favor notifique o administrador do sistema.
error.login=A sua tentativa de login nÃ£o foi bem sucedida. Por favor, tente novamente.
error.denied=usuÃ¡rio {0} nÃ£o tem permissÃ£o para acessar este site.
error.accessdenied=NÃ£o tem permissÃ£o para acessar esta funcionalidade.
error.networkConnect=Ocorreu um erro ao tentar se comunicar com o servidor.
error.networkIO=Ocorreu um erro de comunicaÃ§Ã£o na rede.
error.networkTimeout=O servidor estÃ¡ demorando muito para responder.
error.networkUnknown=Ocorreu um erro desconhecido ao tentar contactar o servidor. Notificar administrador.
error.delete=NÃ£o Ã© possÃ­vel desligar
permission.denied=PermissÃ£o recusada
permission.edit.denied=PermissÃ£o de ediÃ§Ã£o recusada
error.no.user=Nenhum usuÃ¡rio recente disponÃ­vel.
error.current.group=Grupo especificado invÃ¡lido para o grupo atual do UsuÃ¡rio.
error.meter.accessdenied=NÃ£o tem permissÃ£o para acessar este contador.
error.pricing.structure.accessdenied=NÃ£o tem permissÃ£o para editar a alocaÃ§Ã£o da estrutura de preÃ§os.
error.pricing.structure.accessdenied.addmeter=NÃ£o tem permissÃ£o para editar a alocaÃ§Ã£o da estrutura de preÃ§os, logo nÃ£o Ã© possÃ­vel ligar este contador ao ponto de medida. O contador foi guardado no armazÃ©m.
error.meter.workspace.error=Erro ao abrir o espaÃ§o de trabalho do contador.
error.customer.workspace.error=Erro ao abrir o espaÃ§o de trabalho do cliente.
error.usagepoint.workspace.error=Erro ao abrir a Ã¡rea de trabalho do ponto de medida.
error.no.user.assignedgroup=O usuÃ¡rio atual nÃ£o tem um grupo atribuÃ­do.
error.login.locked=A sua conta foi bloqueada.
error.loginerror=UsuÃ¡rio ou senha incorreta.
error.login.disabled=A sua conta foi desativada.

###############
#### Login ####
###############
login.title=Entrar
login.form.title=Por favor insira seu nome de usuÃ¡rio e senha.
login.form.username=Nome do usuÃ¡rio
login.form.password=Senha
login.form.login=Entrar
login.form.remember_me=Lembrar-me por duas semanas
login.form.password.forgotten= Esqueceu a sua senha?
password.form.instructions=Introduza o seu endereÃ§o de e-mail registado.
password.email.invalid=EndereÃ§os de e-mail invÃ¡lido.
password.email.unknown.user=UsuÃ¡rio desconhecido para o endereÃ§o de e-mail apresentado.
password.multiple.users=Existem vÃ¡rios usuÃ¡rios para o endereÃ§o de e-mail apresentado.
password.multiple.users.1=Selecione por favor uma opÃ§Ã£o abaixÃ£o e clique em subcontador
password.reset=Senha reiniciada. Por favor, verifique o seu e-mail para detalhes adicionais.
password.error.reset=Erro: NÃ£o foi possÃ­vel reiniciar a senha.
adminuser.password.save.error=Erro: NÃ£o foi salvo o usuÃ¡rio atualizado.
email.password.reset.subject=Senha reiniciada.
password.form.email=EndereÃ§o de e-mail:
password.form.submit=subcontador

#########################
#### Password Change ####
#########################
password_change.title=mudanÃ§a de senha
password_change.form.title=Mudar minha senha
password_change.form.password1=Insira uma nova senha:
password_change.form.password2=Reintroduza a nova senha:
password_change.form.submit=Subscrever alteraÃ§Ãµes
password_change.success=Senha alterada para {0}
password_change.validate.equal=As duas entradas de senha nÃ£o sÃ£o iguais.
password_change.validate.ldap=Ldap autenticado. Os usuÃ¡rios nÃ£o podem alterar suas senhas. Contacte o seu administrador do Ldap do sistema.

########################################################################################################################
# GWT specific properties #
########################################################################################################################

## Errors
error.save=Erro: {0} nÃ£o pode ser salvo.
error.field.required=Campo obrigatÃ³rio.
error.field.is.required={0} Ã© um campo obrigatÃ³rio.
error.field.numeric.required={0} Ã© um campo numÃ©rico obrigatÃ³rio.
error.no.selection=nÃ£o foi feita nenhuma seleÃ§Ã£o vÃ¡lida.
error.data.null=Dados de entrada sÃ£o invÃ¡lidos.
error.datatype.null=O {0} Ã© invÃ¡lido.
error.missing={0} {1} nÃ£o encontrado.
error.numeric.value=Insira um valor numÃ©rico vÃ¡lido.
error.token.retrieve=nÃ£o Ã© possÃ­vel recuperar talÃ£o.
error.meter.load=nÃ£o Ã© possÃ­vel exibir o contador.
error.usagepoint.load=NÃ£o foi possÃ­vel mostrar o ponto de medida.

# Field errors used by the Validation framework's annotations
error.field.id.null=ID Ã© necessÃ¡rio.
error.field.key.null=Ã necessÃ¡ria senha.
error.field.key.range=Senha deve ter entre {min} e {max} caracteres.
error.field.name.null=O nome Ã© obrigatÃ³rio.
error.field.name.range=O nome deve ter entre {min} e {max} caracteres.
error.field.name.max=O nome deve ser inferior a {max} caracteres.
error.field.value.null=Ã necessÃ¡rio um valor.
error.field.value.range= O valor deve ter entre {min} e {max} caracteres.
error.field.description.null=DescriÃ§Ã£o Ã© necessÃ¡ria.
error.field.description.range=A descriÃ§Ã£o deve ter entre {min} e {max} caracteres.
error.field.description.max=Uma descriÃ§Ã£o deve ser inferior a {max} caracteres.
error.field.recordstatus.null=Ã necessÃ¡rio o estado do registo.
error.field.contactname.null=Ã necessÃ¡rio o nome do contato.
error.field.contactname.range=O nome do contato deve ter entre {min} e {max} caracteres.
error.field.contactemail=O contato de e-mail deve ser um endereÃ§o de e-mail vÃ¡lido.
error.field.contactemail.max=O contato de e-mail deve ter menos que {max} caracteres.
error.field.taxref.max=a ReferÃªncia de Imposto deve ter menos de {max} caracteres.
error.field.contactnumber.max=O nÃºmero de contato deve ser inferior a {max} caracteres.
error.supplyserver=A combinaÃ§Ã£o entre o supply group e o nÃºmero de revisÃ£o deve ser Ãºnica.
error.field.supplygroupcode.null=Ã necessÃ¡rio o Fornecimento do CÃ³digo do Grupo.
error.field.keyrevisionnum.null=Ã necessÃ¡ria a chave do nÃºmero de revisÃ£o.
error.field.supplygroupcode.format= Insira um valor numÃ©rico.
error.field.supplygroupcode.range=O cÃ³digo deve ser inferior a 7 dÃ­gitos.
error.field.calccontents.null=Ãndice de cÃ¡lculo Ã© um campo obrigatÃ³rio.
error.field.schedulename.range=marcaÃ§Ã£o de nome deve ter entre {min} e {max} caracteres.
error.minmax.range.auxchargeschedule=Valor mÃ­nimo deve ser inferior ao valor mÃ¡ximo.
error.field.meternum.null=O nÃºmero do contador Ã© um campo obrigatÃ³rio.
error.field.meternum.range=O nÃºmero do contador deve estar entre {min} e {max} caracteres.
error.field.mrid.null=MRID Ã© um campo obrigatÃ³rio.
error.field.mrid.range=MRID deve ter entre {min} e {max} caracteres.
error.field.serialnum.max=NÃºmero de sÃ©rie deve ser inferior a {max} caracteres.
error.field.meternumchecksum.max=A verificaÃ§Ã£o da soma deve ser inferior a {max} caracteres.
error.field.ststokentechcode.max=TalÃ£o de CÃ³digo de tecnologia deve ser inferior a {max} caracteres.
error.field.stsalgorithmcode.max=O algoritmo do cÃ³digo deve ser inferior a {max} caracteres.
error.field.stsprevsupplygroupcode.max=Fornecimento do CÃ³digo do Grupo deve ser inferior a {max} caracteres.
error.field.stscurrtariffindex.max=Ãndice de tarifa deve ser inferior a {max} caracteres.
error.field.addressline1.max=Cada linha de endereÃ§o deve ser inferior a {max} caracteres.
error.field.addressline2.max=Cada linha de endereÃ§o deve ser inferior a {max} caracteres.
error.field.addressline3.max=Cada linha de endereÃ§o deve ser inferior a {max} caracteres.
error.field.city.max=Cidade deve ser inferior a {max} caracteres.
error.field.province.max=ProvÃ­ncia deve ser inferior a {max} caracteres.
error.field.country.max=O paÃ­s deve ser inferior a {max} caracteres.
error.field.postalcode.max=CÃ³digo Postal deve ser inferior a {max} caracteres.
error.field.erfnumber.max=Erf NÃºmero deve ser inferior a {max} caracteres.
error.field.streetnum.max=O nÃºmero da rua deve ser inferior a {max} caracteres.
error.field.buildingname.max=O nome do edifÃ­cio deve ser inferior a {max} caracteres.
error.field.suitenum.max=NÃºmero do apartamento deve ser inferior a {max} caracteres.
error.field.surname.null=O Apelido Ã© um campo obrigatÃ³rio.
error.field.agreementref.null=ReferÃªncia de acordo Ã© um campo obrigatÃ³rio.
error.field.agreementref.duplicate=ReferÃªncia de acordo duplicada {0}. Especifique uma referÃªncia Ãºnica.
error.field.email1.max=O endereÃ§o de e-mail deve ser inferior a {max} caracteres.
error.field.email1=EndereÃ§o de e-mail invÃ¡lido.
error.field.email2.max=O endereÃ§o de e-mail deve ser inferior a {max} caracteres.
error.field.email2=EndereÃ§o de e-mail invÃ¡lido.
error.field.phone1.max=NÃºmero de telefone deve ser inferior a {max} caracteres.
error.field.phone2.max=NÃºmero de telefone deve ser inferior a {max} caracteres.
error.field.phone=NÃºmero de telefone invÃ¡lido, apenas sÃ£o permitidos '0-9', '+', espaÃ§o, hÃ­fen, 'ext' e 'x'. 'Ext' ou 'x' devem ser seguidos por um nÃºmero de extensÃ£o numÃ©rica.
error.field.phone2=Segundo nÃºmero de telefone invÃ¡lido, apenas sÃ£o permitidos '0-9', '+', espaÃ§o, hÃ­fen, 'ext' e 'x'. 'Ext' ou 'x' devem ser seguidos por um nÃºmero de extensÃ£o numÃ©rica.
error.field.startdate.null=Data de inÃ­cio Ã© um campo obrigatÃ³rio.
error.field.startdate.future=Data de inÃ­cio nÃ£o pode ser no futuro.
error.field.installdate.future=Data de instalaÃ§Ã£o nÃ£o pode ser no futuro.
error.powerlimit.invalid=Selecione um valor vÃ¡lido para limite de potÃªncia (obrigatÃ³rio)
error.priority.invalid=A prioridade deve ser um nÃºmero (1 Ã© a mais alta prioridade)
error.search.meter=Insira um nÃºmero de contador vÃ¡lido.
error.search.customer=Digite um termo de pesquisa vÃ¡lido.
error.field.touseasonname.null=Nome da temporada Ã© um campo obrigatÃ³rio.
error.field.touperiodname.null=Nome do perÃ­odo Ã© um campo obrigatÃ³rio.
error.field.touperiodcode.null=CÃ³digo de perÃ­odo Ã© um campo obrigatÃ³rio.
error.field.title.max=TÃ­tulo deve ser inferior a {max} caracteres.
error.field.initials.max=Iniciais devem ser inferiores a {max} caracteres.
error.field.firstnames.max=O primeiro nome deve ser inferior a {max} caracteres.
error.field.surname.max=O Apelido deve ser inferior a {max} caracteres.
error.field.companyname.max=A empresa deve ser inferior a {max} caracteres.
error.field.taxnum.max=O nÃºmero de imposto deve ser inferior a {max} caracteres.
error.field.agreementref.max=A referÃªncia do acordo deve ser inferior a {max} caracteres.
error.field.usagepoint.name.null=Ã necessÃ¡rio o Nome do ponto de uso.
error.field.usagepoint.name.duplicate=O nome {0} para ponto de uso estÃ¡ duplicado. Especifique um nome exclusivo.

error.field.comment.max=O comentÃ¡rio deve ser inferior a {max} caracteres.
error.field.accountref.max=A referÃªncia da conta deve ser inferior a {max} caracteres.
error.field.ourref.null=Ã necessÃ¡ria a Nossa ReferÃªncia.
error.field.ourref.range=A Nossa referÃªncia deve ter entre {min} e {max} caracteres.
error.field.amtincltax.null=Deve ser inserido um Montante.
error.field.amttax.null=O Imposto nÃ£o pode ser nulo. Digite 0 se nÃ£o tem imposto.
error.field.customvarchar.max=O campo de texto personalizado deve ter menos do que {0} caracteres.

error.field.accountname.range= O Nome da Conta deve ter menos de {max} caracteres.
 
# Error headers, etc
error.validation.header=Por favor, corrija os erros de validaÃ§Ã£o:
error.validation.fields.header=Por favor, corrija os erros de entrada:
error.field.code.null=Ã necessÃ¡rio o cÃ³digo.
error.field.code.range=O cÃ³digo deve ter entre {min} e {max} caracteres.
error.server.connection=NÃ£o Ã© possÃ­vel conectar ao servidor.
error.server=Erro na resposta recebida do servidor.
error.field.manufacturerid.null=Ã Fornecedor.
error.field.serviceresourceid.null=Ã necessÃ¡rio ServiÃ§o de recurso.
error.field.metertypeid.null=O Tipo de contador Ã© necessÃ¡rio.
error.field.paymentmodeid.null=O modo de pagamento Ã© obrigatÃ³rio.
error.field.accountname.null=Ã necessÃ¡rio um nome de conta.
error.field.accountname.duplicate=Nome de conta estÃ¡ duplicado {0}. Especifique um nome exclusivo.
error.field.taskschedulename.null=O nome Ã© obrigatÃ³rio.
error.field.taskschedulename.max=O nome deve ser inferior a {max} caracteres.
error.field.cronexpression.null=Ã necessÃ¡ria ProgramaÃ§Ã£o.
error.field.cronexpression.range=Cronograma deve ter entre {min} e {max} caracteres.
error.field.scheduledtaskid.null=Ã necessÃ¡rio o Tipo de Tarefa.
error.field.taskschedulename.range=Ã necessÃ¡rio o Nome da tarefa agendada.
error.field.taskclassid.null=Ã necessÃ¡rio o Tipo de Tarefa.
error.field.scheduledtaskname.range=O nome deve ter entre {min} e {max} caracteres.
error.field.taskscheduleid.null=Ã necessÃ¡rio agendar tarefas.
error.messages.header=Erros:

# Customer Account Threshold errors
error.field.lowbalance.null=Saldo mÃ­nimo deve ser lanÃ§ado.
error.field.emergencycredit.null=CrÃ©dito de emergÃªncia deve ser apresentado
error.field.disconnect.null=Deve ser introduzida DesconexÃ£o 
error.field.reconnect.null=Deve ser introduzida Reconectar.

# Questions
question.discard.changes=VocÃª quer cancelar as alteraÃ§Ãµes solicitadas?
question.discard.potential.changes=Fez alteraÃ§Ãµes e ainda nÃ£o registradas, elas serÃ£o canceladas. Continuar?
option.yes=Cancelar mudanÃ§as
option.no=Cancelar
option.positive=sim
option.negative=NÃ£o
question.delete=VocÃª deseja eliminar o item selecionado?
dayprofile.question.delete=VocÃª deseja eliminar o item selecionado? Isso tambÃ©m irÃ¡ apagar todos os tempos associados a este perfil diÃ¡rio.
question.confirm.installation.date.1=Confirme se o contador {0} foi instalado em {1} a {2}.
question.confirm.installation.date.2=Ã importante que a data e a hora sejam exatas.
question.confirm.link.to.customer=O cliente {0} jÃ¡ se encontra na pÃ¡gina. Escolher este ponto de uso irÃ¡ atribuir automaticamente ao cliente. Continuar?
question.close.tabs.1=nÃ£o Ã© possÃ­vel salvar, enquanto as outras guias, que potencialmente sÃ£o referÃªncias a este item, estÃ£o abertas.
question.close.tabs.2= VocÃª pode fechÃ¡-los e, em seguida, tentar salvar novamente - escolha NÃ£o ou Sim
question.close.tabs.3=Podem-se fechar automaticamente outras guias (vai perder dados nÃ£o salvos) - escolha SIM?
option.confirm= confirmar
option.continue=Continuar?

## Messages
message.saved={0} foi salvo.

## Links
link.logout=Sair
link.loggedin=UsuÃ¡rio:
link.group.change= Mudar grupo
link.group= Grupo:
link.meters=Contadores
link.customers=clientes
link.groups=Grupos
link.menu=Menu
link.pricingstructure=Estrutura de preÃ§os
link.calendars=calendÃ¡rio
link.calendarsettings=DefiniÃ§Ãµes do calendÃ¡rio
link.auxchargeschedule=HorÃ¡rio de Conta Auxiliar
link.auxilliarytype=Tipo de conta auxiliar
link.supplygroup=supply group
link.displaytokens=Mostrar TalÃµes
link.devicestores=ArmazÃ©ns de Equipamentos
link.usergroup=Grupo de Acesso do UsuÃ¡rio
link.accessgroups=grupos de acesso
link.search=pesquisa
link.search.advanced=Pesquisa avanÃ§ada
link.search.meters.viewed=Ãltimos contadores vistos
link.search.meters.modified=Ultimas modificaÃ§Ãµes nos contadores
link.meter.readings=Leituras
link.energybalancing=BalanÃ§o de Energia
link.energybalancing.meters=Contadores de BalanÃ§o de Energia
link.analytics=anÃ¡lise
link.configuration=ConfiguraÃ§Ã£o
link.tools=ferramentas
link.taskschedules=Agendamento de tarefas
link.locationgroups=LocalizaÃ§Ã£o de grupos
link.about=Sobre
link.appsettings=configuraÃ§Ãµes do aplicativo
link.global.ndp=NDP global
link.billingdet=Determinantes de cobranÃ§a

## Buttons ##
button.save=guardar
button.new=novo
button.edit=editar
button.back=Voltar
button.cancel=Cancelar
button.close=fechar
button.select=selecionar
button.delete=Eliminar
button.viewentity=Ver entidade
button.yes=sim
button.no=NÃ£o
button.create=criar
button.update=Atualizar
button.viewtariffs=Ver tarifas
button.replacemeter=Substituir contador
button.removemeter=Remover Contador 
button.displayunits=Exibir Unidades
button.gettoken=Obter TalÃµes
button.saveaccount=Guardar conta
button.addnew=Adicionar novo
button.editchargeschedule=Editar agendamento de cobranÃ§as
button.clear.group=limpar grupo
button.search=pesquisar
button.clear=apagar
button.view=ver
button.add=Adicionar
button.remove=Remover
button.export=Exportar
button.view.scheduledtasks=Ver tarefas agendadas
button.login=Entrar
button.logout=Sair
button.set=Ajustar
button.show.inherited=Mostrar Valores Recebidos
button.send=Enviar
button.viewtrans=Ver TransaÃ§Ãµes

## Menus ##
menu.add=Adicionar
menu.update=Atualizar
menu.delete=apagar
menu.search=Procurar

## Record Statuses ##
status.active=Ativo
status.inactive=Inativo
status.deleted=Apagado

### Supply Group ###
supplygroups.header=Supply group
supplygroups.title=Supply groups atuais
supplygroup.title=Supply group
supplygroup.name=Nome do Supply group
supplygroup.field.name=Nome
supplygroup.field.code=cÃ³digo
supplygroup.field.keyrevisionnumber=NÃºmero da chave de revisÃ£o
supplygroup.field.keyexpirynumber=NÃºmero de validaÃ§Ã£o da chave
supplygroup.field.status=estados
supplygroup.field.active=Ativo
supplygroup.title.add=Adicionar Supply group
supplygroup.title.update=Atualizar Supply group
supplygroup.error.save=NÃ£o foi possÃ­vel gravar o Supply group

### Group Type ###
grouptypeshierarchies.title=Tipos de grupos e autoridades
grouptypes.header=Tipos de grupo
grouptypes.hierarchies.header=Hierarquias de grupos
grouptypes.title=Tipos de grupos atuais
grouptype.title=Tipos de grupo
grouptype.field.id=Tipo de ID de grupo
grouptype.field.name=Nome
grouptype.field.name.help=Digite o nome deste tipo de grupo. Este Ã© um campo obrigatÃ³rio - O registo nÃ£o pode ser salvo sem este campo.
grouptype.field.description=DescriÃ§Ã£o
grouptype.field.description.help=Digite uma descriÃ§Ã£o deste tipo de grupo.
grouptype.field.parent=Diretor
grouptype.field.parent.help=Se este tipo de grupo pertence a uma autoridade sob outro tipo de grupo, selecione aqui o tipo de grupo principal.
grouptype.field.active=Ativo
grouptype.field.active.help=Este grupo estÃ¡ ativo? Selecione a caixa para ativar este tipo de grupo.
grouptype.field.status=estado
grouptype.field.required=Requirido
grouptype.field.required.help=Para grupos de pontos de medida aqui determina-se se Ã© necessÃ¡ria uma seleÃ§Ã£o para este tipo de grupo.
grouptype.field.accessgroup=grupo de acesso
grouptype.field.locationgroup=LocalizaÃ§Ã£o Grupo
grouptype.field.access.location.group=Acesso / LocalizaÃ§Ã£o
grouptype.field.feature=CaracterÃ­sticas
grouptype.field.available.feature=Recursos disponÃ­veis
grouptype.field.assigned.feature=Recursos atribuÃ­dos
grouptype.field.available.feature.help=Se um recurso estÃ¡ marcado como recurso de seleÃ§Ã£o Ãºnica, que sÃ³ pode ser selecionado para um Ãºnico tipo de grupo, uma vez que o tipo de grupo tem sido usado nos dados, esta tarefa nÃ£o pode mudar.
grouptype.field.assigned.feature.help=Recursos atribuÃ­dos a este tipo de grupo.
grouptype.button.viewhierarchies=Veja como superiores.
grouptype.error.noneselected=Nenhum Tipo de grupo atual foi selecionado.
grouptype.error.parentcanthavehierarchy=Os tipos de Grupos Principais nÃ£o podem ter superiores.
grouptype.none=Tipo de Grupo: Nenhum selecionado
grouptype.title.add=Adicionar tipo de grupo
grouptype.title.update=Atualizar tipo de grupo
grouptype.error.save=NÃ£o foi possÃ­vel salvar o tipo de grupo.
grouptype.error.accessgroup.users=nÃ£o Ã© possÃ­vel alterar o grupo de acesso. HÃ¡ UsuÃ¡rios existentes atribuÃ­dos aos grupos de acesso.
grouptype.error.accessgroup.customers=nÃ£o Ã© possÃ­vel alterar o grupo de acesso. HÃ¡ clientes existentes usando os grupos.
grouptype.error.accessgroup.up=nÃ£o Ã© possÃ­vel alterar o grupo de acesso. HÃ¡ pontos de medida existentes, utilizando os grupos.
grouptype.error.accessgroup.pricing=nÃ£o Ã© possÃ­vel alterar o grupo de acesso. Existem estruturas de preÃ§os existentes, utilizando os grupos.
grouptype.error.accessgroup.aux=nÃ£o Ã© possÃ­vel alterar o grupo de acesso. HÃ¡ horÃ¡rios de contas auxiliares existentes usando os grupos.
grouptype.error.accessgroup.stores=nÃ£o Ã© possÃ­vel alterar o grupo de acesso. HÃ¡ lojas de dispositivos existentes, utilizando os grupos.
grouptype.error.name.duplicate=O nome do tipo de grupo jÃ¡ estÃ¡ em uso. Escolha outro nome.
grouptype.error.cannot.change.required=nÃ£o pode alterar a configuraÃ§Ã£o 'requerida', pois o grupo jÃ¡ estÃ¡ em uso.
grouptype.error.select.add.feature=Selecione um recurso para adicionar.
grouptype.error.select.remove.feature=Selecione um recurso para removedor.
grouptype.error.cannot.set.feature.for.group=Este recurso nÃ£o pode ser definido para este tipo de grupo.
grouptype.error.cannot.remove.feature=nÃ£o Ã© possÃ­vel remover a configuraÃ§Ã£o de recurso, o grupo jÃ¡ estÃ¡ em uso.
grouptype.error.save.feature=nÃ£o foi possÃ­vel salvar a configuraÃ§Ã£o de recurso.
grouptype.error.feature.not.multi.instance=Este recurso sÃ³ pode ser atribuÃ­do uma vez, e jÃ¡ foi atribuÃ­do a outro grupo.
grouptype.accessgroup.help=O tipo de grupo de acesso determina o tipo de grupo que controla o acesso do usuÃ¡rio ao site. A mesma Ã© definida uma vez e nÃ£o pode ser alterada.
grouptype.locationgroup.help=tipo de grupo de localizaÃ§Ã£o determina o tipo de grupo que controla os dados de localizaÃ§Ã£o. A mesma Ã© definida uma vez e nÃ£o pode ser alterada.

### Group Hierarchy ###
grouphierarchies.header= Hierarquias de grupos
grouphierarchies.title=Hierarquias de grupo atuais
grouphierarchy.title=Hierarquia de grupo
grouphierarchy.field.id=Hierarquia de grupo ID
grouphierarchy.field.name=Nome
grouphierarchy.field.name.help=Digite o nome deste nÃ­vel na hierarquia do grupo.
grouphierarchy.field.description=DescriÃ§Ã£o
grouphierarchy.field.active=ativar
grouphierarchy.field.parent=Diretor de hierarquia
grouphierarchy.field.parent.none=Nenhum
grouphierarchy.field.level=NÃ­vel
grouphierarchy.delete.confirm=Tem certeza de que deseja excluir a hierarquia do grupo?
grouphierarchy.deleted=A hierarquia do grupo foi excluÃ­da com sucesso.
grouphierarchy.error.delete=nÃ£o Ã© possÃ­vel excluir a hierarquia do grupo.
grouphierarchy.error.delete.linked=nÃ£o Ã© possÃ­vel excluir a hierarquia do grupo, uma vez que o mesmo estÃ¡ sendo usado.
grouphierarchy.error.save=nÃ£o foi possÃ­vel salvar a hierarquia do grupo.
grouphierarchy.error.update=nÃ£o Ã© possÃ­vel atualizar item de hierarquia do grupo, uma vez que estÃ¡ sendo usado.
grouptype.current=Tipo de grupo atual
grouphierarchy.title.add=Adicionar Grupo Hierarquia
grouphierarchy.title.update=Atualizar hierarquia de grupo
grouphierarchy.error.unknown=Hierarquia de grupo desconhecido.
grouphierarchy.error.access.root=primeiro nÃ­vel de hierarquia do tipo de grupo de acesso nÃ£o pode ser modificado.

### Usage Point Groups ###
usagepointgroups.header=Ponto de medida de grupo
usagepointgroups.title=Ponto atual de medida do grupo
usagepointgroups.instructions=selecionar tipo de grupo:
usagepointgroup.title=Ponto de uso de grupo
usagepointgroup.noselection.grouptype=nÃ£o foi selecionado qualquer Tipo de grupo atual.
usagepointgroup.field.id=Ponto de ulitizaÃ§Ã£o de grupo ID
usagepointgroup.field.name=Nome
usagepointgroup.field.hierarchy=Hierarquia
usagepointgroup.help.grouptype=Selecione um tipo de grupo para visualizar seu grupo de ponto de medida. Somente os tipos de Grupo com hierarquias de grupo sÃ£o visÃ­veis aqui.
usagepointgroup.delete.ask=Tem certeza de que deseja excluir o grupo de ponto de medida {0}?
usagepointgroups.help=Clique nos Ã­cones de dados em Ã¡rvore para adicionar, editar ou apagar os dados.
usagepointgroup.title.add=Adicionar ponto de medida de grupo.
usagepointgroup.title.update=Atualizar ponto de medida de grupo.
usagepointgroup.error.entityid=nÃ£o foi possÃ­vel salvar a entidade ID do Grupo.

###Old Usage Point Group Page
usagepointgroup.field.description=DescriÃ§Ã£o
usagepointgroup.field.parent=Nome principal
usagepointgroup.field.active=Ativo
usagepointgroup.field.status=estado
usagepointgroup.field.name.help=Digite um nome para o grupo - que serÃ¡ usado para usÃ¡-lo quando forem adicionados Pontos de Medida ao grupo.
usagepointgroup.field.description.help=Digite uma descriÃ§Ã£o do grupo (opcional).
usagepointgroup.field.parent.help=Se o grupo faz parte de um grupo maior, identifique o grupo principal aqui
usagepointgroup.field.status.help=Ativar ou desativar este grupo

## Groups
group.error.save=nÃ£o foi possÃ­vel salvar o grupo.
group.error.delete=nÃ£o foi possÃ­vel desligar o grupo.
group.error.delete.ap=nÃ£o foi possÃ­vel desligar o grupo pois ainda existem pontos de medida ativos na mesma.
group.error.entity.save=nÃ£o Ã© possÃ­vel ligar as informaÃ§Ãµes de contato para o grupo.
group.error.threshold.save=nÃ£o Ã© possÃ­vel ligar as informaÃ§Ãµes de limites para o grupo.
group.error.ndp.schedule.save=nÃ£o Ã© possÃ­vel ligar a informaÃ§Ã£o NDP ao grupo.
group.new.instructions=Insira um novo
group.new.for=por
group.current.none=nÃ£o existe grupo atual definido para o seu UsuÃ¡rio.
group.delete.ask=Tem certeza de que deseja excluir o grupo {0}?
groups.error.select.at.minimum= No mÃ­nimo {0} deve ser selecionado.

## Usage Point Workspace
usagepointworkspace.meter.saved.usagepoint.deactivation.failed=Contador {0} salvo! (Faltou a desativaÃ§Ã£o do ponto de medida!)
usagepointworkspace.meter.saved.usagepoint.deactivated=Contador {0} salvo! (ponto de medida desativado!)
usagepointworkspace.meter.saved=Contador {0} salvo!
usagepointworkspace.meter.saved.attach.usagepoint=Contador {0} salvo! Para ligar este contador ao ponto de medida abaixo, selecione a data de instalaÃ§Ã£o e a estrutura de preÃ§os, bem como, a ativaÃ§Ã£o.
usagepointworkspace.customer.saved.usagepoint.deactivation.failed=Cliente {0} salvo! (Faltou a desativaÃ§Ã£o do ponto de medida!)
usagepointworkspace.customer.saved.usagepoint.deactivated=Cliente {0} salvo! (ponto de medida desativado!)
usagepointworkspace.customer.saved.usagepoint.failed=Cliente {0} salvo, mas nÃ£o foi possÃ­vel atualizar o ponto de medida. Entre em contato com o suporte.
usagepointworkspace.customer.saved.usagepoint.updated=Cliente {0}salvo e ponto de medida atualizado.
usagepointworkspace.customer.saved=Cliente {0} salvo!
usagepointworkspace.customer.saved.no.usage.point=Cliente {0} garantido, sem ponto de medida para atualizar.
usagepointworkspace.customer.unassigned.usagepoint.deactivation.failed=Cliente {0} nÃ£o estÃ¡ atribuÃ­do ao ponto de medida {1}. (Faltou a desativaÃ§Ã£o do ponto de medida!)
usagepointworkspace.customer.unassigned.usagepoint.deactivated=Cliente {0} nÃ£o estÃ¡ atribuÃ­do ao ponto de medida {1}. (O ponto de medida foi desativado!)
usagepointworkspace.customer.assign.error.already.assigned=O cliente jÃ¡ estÃ¡ atribuÃ­do ao ponto de medida {0}.
usagepointworkspace.customer.assign.error=Cliente {0} nÃ£o estÃ¡ atribuÃ­do ao ponto de medida {1}. (Faltou a desativaÃ§Ã£o do ponto de medida!)
usagepointworkspace.customer.assigned.usagepoint.deactivated=O cliente {0} estÃ¡ agora atribuÃ­do ao ponto de medida {1}. (ponto de medida estÃ¡ desativado)
usagepointworkspace.customer.assigned=O Cliente {0} estÃ¡ agora atribuÃ­do ao ponto de medida {1}.
usagepointworkspace.error.meter.not.found=Contador nÃ£o foi encontrado.
usagepointworkspace.error.meter.already.assigned=Contador {0} jÃ¡ estÃ¡ atribuÃ­do ao ponto de medida {1}.
usagepointworkspace.error.meter.unable.to.unassign=nÃ£o Ã© possÃ­vel atribuir o contador atual.
usagepointworkspace.error.meter.installdate.before.previous=A nova data de instalaÃ§Ã£o {0} nÃ£o pode ser anterior data de instalaÃ§Ã£o do contador {1}.
usagepointworkspace.error.meter.installdate.before.last.remove=A nova data de instalaÃ§Ã£o{0} nÃ£o pode ser ANTERIOR Ã  data de remoÃ§Ã£o do contador{1}.
usagepointworkspace.error.meter.installdate.before.last.reading=Incapaz de voltar a atribuir ponto de medida - nova data de instalaÃ§Ã£o Ã© anterior Ã  data da Ãºltima leitura do contador atual.
usagepointworkspace.error.meter.installdate.before.last.register.reading=NÃ£o foi possÃ­vel reatribuir o ponto de medida - a nova data de instalaÃ§Ã£o Ã© anterior ao Ãºltimo registro de leitura do contador atual.
usagepointworkspace.error.meter.removedate.before.last.reading=NÃ£o Ã© possÃ­vel remover agora o contador do ponto de medida - O Ãºltimo dado de leitura Ã© superior.
usagepointworkspace.error.meter.removedate.before.last.register.reading= NÃ£o Ã© possÃ­vel remover agora o Ãºltimo contador do ponto de medida - O registro de leitura Ã© superior.
usagepointworkspace.meter.assigned=Contador {0} agora Ã© atribuÃ­do para ponto de medida {1}
usagepointworkspace.meter.assigned.usagepoint.deactivated=Contador {0} estÃ¡ agora atribuÃ­do ao ponto de medida{1} (ponto de medida nÃ£o esta ativo)
usagepointworkspace.meter.removed=Contador {0} foi removido do ponto de medida{1}.
usagepointworkspace.meter.add.usagepoint.to.join.customer=Adiciona o ponto de medida (abaixo) para ligar contador {0} e cliente{1} entre si.
usagepointworkspace.meter.add.usagepoint.to.join=Adiciona o ponto de medida (abaixo) para ligar contador.
usagepointworkspace.assign.activate.usage.point.question=ponto de medida nÃ£o estÃ¡ ativo de momento. Quer ativÃ¡-lo agora?
usagepointworkspace.assign.usage.point.activated=ponto de medida ativado.
usagepointworkspace.save.usage.point.inactive=O ponto de medida nÃ£o estÃ¡ ativo.
usagepointworkspace.meter.customer.assigned=Contador {0}, Cliente {2} estÃ£o agora atribuÃ­dos ao ponto de medida {1}

### Usage Point ###
usagepoint.groups.title= Grupos dos Pontos de Medida
usagepoint.info.title=InformaÃ§Ã£o do ponto de medida
usagepoint.title=ponto de medida
usagepoint.add.new=Adicionar ponto de medida
usagepoint.show.info=Mostrar informaÃ§Ãµes do ponto de medida
usagepoint.showing.info=Mostrando informaÃ§Ã£o do ponto de medida
usagepoint.field.active.help=Este ponto de medida estÃ¡ ativo? Esta opÃ§Ã£o estÃ¡ desativada atÃ© que um contador ativo e cliente estejam ligados a este ponto de medida.
usagepoint.field.activated_date.help=Defina um dado de ativaÃ§Ã£o do ponto de medida. Isto sÃ³ pode ser ativado uma vez. Este campo Ã© necessÃ¡rio para ativaÃ§Ã£o. Embora o registro possa ser salvo sem ele, o ponto de medida nÃ£o pode ser ativado a menos que seja preenchido corretamente.
usagepoint.field.active=Ativo
usagepoint.field.activated_date=dados de ativaÃ§Ã£o
usagepoint.field.meter.installation_date=Data/hora da instalaÃ§Ã£o do contador
usagepoint.field.meter.installation_date.meter.required=Um contador deve ser instalado antes de definir a data de instalaÃ§Ã£o.
usagepoint.field.meter.installation_date.help=A data e hora de instalaÃ§Ã£o do contador neste ponto de medida. Isso sÃ³ pode ser definido uma vez pelo contador. Este campo Ã© obrigatÃ³rio e um contador deve ser atribuÃ­do antes de definir a data de instalaÃ§Ã£o.
usagepoint.field.name.help=Introduza o nome deste ponto de medida. Este campo Ã© obrigatÃ³rio e o registro nÃ£o pode ser salvo sem ele.
usagepoint.field.name=Nome do ponto de medida
usagepoint.field.pricingstructure.help=Selecione a estrutura de preÃ§os. Este Ã© um campo obrigatÃ³rio - o registo nÃ£o pode ser salvo sem ele.
usagepoint.field.lastmdcconnectcontrol=Ãltimo Controlo de ligaÃ§Ã£o para recolha de leituras MDC.
usagepoint.field.group.help=Adicionar ponto de medida a um grupo.
usagepoint.field.group=Grupos de pontos de medida
usagepoint.required.text=* \= Requer - tambÃ©m Ã© necessÃ¡rio ter um contador associado.
usagepoint.required.activation.text=** \= NecessÃ¡ria para a ativaÃ§Ã£o - Um contador ativo e cliente tambÃ©m sÃ£o necessÃ¡rios para a ativaÃ§Ã£o.
usagepoint.name.required=O momento do ponto de medida Ã© obrigatÃ³rio
usagepoint.pricingstructure.required=Estrutura de preÃ§os Ã© obrigatÃ³ria
usagepoint.pricingstructure.change.illegal=A estrutura de preÃ§os nÃ£o pode ser alterada agora. NÃ£o existe contador anexado.
usagepoint.save.error=GravaÃ§Ã£o do ponto de medida falhou
usagepoint.save.errors=O Ponto de medida nÃ£o foi salvo. Devem-se corrigir os erros exibidos.
usagepoint.save.errors.meter.required=GravaÃ§Ã£o do ponto de medida falhou. Ã necessÃ¡rio associar um contador.
usagepoint.saved=Ponto de medida {0} salvo.
usagepoint.changes.cleared=As alteraÃ§Ãµes foram apagadas.
usagepoint.no.meter=Um contador deve ser salvo antes de adicionar um ponto de medida.
usagepoint.txn.history=HistÃ³rico de transaÃ§Ãµes
usagepoint.txn.history.description=TransacÃ§Ãµes de clientes anteriores para este ponto de medida.
usagepoint.txn.meterreadings=Leitura de contadores
usagepoint.history=HistÃ³rico do ponto de medida
usagepoint.history.description=MudanÃ§as efectuadas neste ponto de medida (As mudanÃ§as estÃ£o realizadas)
usagepoint.reports=RelatÃ³rios do ponto de medida
usagepoint.recharge.history=HistÃ³rico de recargas dos pontos de medida
usagepoint.retailers=Retalhistas prÃ³ximos
usagepoint.reports.general=RelatÃ³rios gerais para os pontos de medida
usagepoint.meter.reports=RelatÃ³rios para ponto de medida
usagepoint.coords=Adicione coordenadas de latitude e longitude para o ponto de medida a fim de ver os retalhistas nas proximidades.
usagepoint.txn.reference=referÃªncia
usagepoint.txn.receipt=NÃºmero recebido
usagepoint.txn.date=Dados
usagepoint.txn.meter=contador
usagepoint.txn.customer=cliente
usagepoint.txn.type=Tipo
usagepoint.txn.client=cliente
usagepoint.txn.term=terminal
usagepoint.txn.ref=referÃªncia
usagepoint.txn.revref=ReversÃ£o da ref
usagepoint.txn.isreversed=Revertido
usagepoint.txn.amt=Montante
usagepoint.hist.serial=SÃ©rie
usagepoint.hist.date=Dados
usagepoint.hist.customer=cliente
usagepoint.hist.datemod=Dados de alteraÃ§Ã£o
usagepoint.hist.byuser=Pelo UsuÃ¡rio
usagepoint.hist.user=UsuÃ¡rio
usagepoint.hist.action=AcÃ§Ã£o
usagepoint.hist.status=estado
usagepoint.hist.name=Nome
usagepoint.hist.meter=contador
usagepoint.hist.custagree=Contrato de cliente
usagepoint.hist.service=serviÃ§o de localizaÃ§Ã£o
usagepoint.recharge.title=Recargas dos pontos de medida
usagepoint.recharge.date=Dados
usagepoint.recharge.currency=AOA
usagepoint.recharge.kwh=kWh
usagepoint.history.filter=Filtro
usagepoint.txn.filter=Filtro
usagepoint.group.required=Grupo obrigatÃ³rio
usagepoint.onegroup.required=Pelo menos um grupo deve ser selecionado.
usagepoint.group.error.delete=nÃ£o foi possÃ­vel apagar o ponto de medida do grupo.
usagepoint.group.error.save=nÃ£o foi possÃ­vel guardar o ponto de medida do grupo.
usagepoint.group.no.value=nÃ£o hÃ¡ valores definidos
usagepoint.calculate.tariff=Calcular Tarifa
usagepoint.calculate.tariff.error=Ocorreu um erro no calculo da tarifa.
usagepoint.calculate.tariff.connection.error=Nenhuma resposta recebida do servidor.
usagepoint.calculate.tariff.ok=Tarifa calculada com sucesso
usagepoint.recharge.chart.title=Recargas do ponto de medida
usagepoint.recharge.chart.subtitle=Os valores de recarga
usagepoint.recharge.chart.xtitle=Data Hora
usagepoint.recharge.chart.ytitle=custo
usagepoint.recharge.chart.price=PreÃ§o
usagepoint.recharge.chart.purchaseprice=PreÃ§o de compra
usagepoint.error.meterandcustomer.required= Um contador ativo e um cliente sÃ£o necessÃ¡rios para a ativaÃ§Ã£o
usagepoint.meter.installed.at=Contador {0} foi instalado no {1} em {2}
usagepoint.installation.date.required=InstalaÃ§Ã£o do contador data / hora Ã© necessÃ¡ria.
usagepoint.name.instructions=Procurar por nome do ponto de medida
usagepoint.partial.search=Sem resultado semelhante para o ponto de medida {0}. A fazer procura avanÃ§ada...
usagepoint.fetch=Associar ponto de medida.
usagepoint.fetch.help=Associar um ponto de medida existente.
usagepoint.assigned=Este ponto de medida jÃ¡ tem um contador associado. NÃ£o Ã© elegÃ­vel para associaÃ§Ã£o.
usagepoint.name.instr=Introduza ponto de medida.
usagepoint.find=Procurar ponto de medida
usagepoint.fetch.duplicate=O ponto de medida {0} jÃ¡ estÃ¡ na pÃ¡gina.
usagepoint.install.date.required=A data e hora a que o contador foi instalado neste ponto de medida {0}.
usagepoint.new.pricingstructure.required=Estrutura de ponto de medida{1} incompatÃ­vel com o contador {0}.
usagepoint.error.new.installdate.before.removal=A data de instalaÃ§Ã£o nÃ£o pode ser ANTERIOR Ã  Ãºltima data de remoÃ§Ã£o no ponto de medida : {0}
usagepoint.error.new.installdate.before.removaldate.meter=A data de instalaÃ§Ã£o nÃ£o pode ser ANTERIOR Ã  Ãºltima data de remoÃ§Ã£o do contador: {0}
usagepoint.saved.linked.meter=Ligado ao contador {0}.
usagepoint.saved.linked.customer=Ligado ao cliente {0}.
moxiechart.abbrev.month.categories="Jan.", "Fev.", "MarÃ§o", "Abril", "Maio", "Junho", "Julho", "Agosto", "Set.", "Fora.", "Nov.", " dez."

### Group Entity ###
groupentity.header=InformaÃ§Ãµes do grupo
groupentity.title=InformaÃ§Ãµes de contato
groupentity.contact.title=Contato
groupentity.field.contact.name=Nome de contato
groupentity.field.contact.number=N.Âº de Telefone
groupentity.field.contact.email=e-mail de contato
groupentity.field.contact.address=EndereÃ§o
groupentity.field.contact.taxref=NIF
groupentity.error.save=nÃ£o foi possÃ­vel salvar as informaÃ§Ãµes do grupo.

### Group Thresholds ###
groupthreshold.title=InformaÃ§Ãµes dos limites da conta do cliente
groupthreshold.meter.disconnect.text=Desligar os limites
groupthreshold.meter.disconnect.help=Limite abaixo do qual o cliente serÃ¡ desconectado.
groupthreshold.emergency.credit.text=Limite de crÃ©dito de emergÃªncia.
groupthreshold.emergency.credit.help=Limite abaixo do qual o saldo do cliente irÃ¡ originar a desconexÃ£o e permitir a solicitaÃ§Ã£o para a coleta de dados mdc se suportado.
groupthreshold.meter.reconnect.text=Reconectar ou limitar
groupthreshold.meter.reconnect.help=Limite acima do qual o cliente serÃ¡ reconectado.
groupthreshold.low.balance.text=Alerta de limite de crÃ©dito baixo.
groupthreshold.low.balance.help=Limite para alertar o cliente para um saldo baixo na sua conta.
groupthreshold.global.source.label=configuraÃ§Ãµes globais
groupthreshold.parent.source.label=configuraÃ§Ãµes principais
groupthreshold.children.change.alert=Hierarquias inferiores com nenhum ou mesmos limites tambÃ©m serÃ£o atualizadas com estas mudanÃ§as. Continuar?
groupthreshold.null.thresholds.alert=Limites deixados em branco serÃ£o desativados. Continuar?
groupthreshold.error.save.thresholds=nÃ£o foi possÃ­vel guardar os limites de informaÃ§Ã£o.
groupthreshold.revert.parent.global=Tem certeza que deseja desligar? Este nÃ³ irÃ¡ agora receber {0} configuraÃ§Ãµes de limite.
groupthreshold.error.disconnect.greater.emergency.credit=A desconexÃ£o deve ser menor ou igual ao crÃ©dito de emergÃªncia.
groupthreshold.error.disconnect.greater.reconnect=A desconexÃ£o deve ser menor ou igual Ã  reconexÃ£o.
groupthreshold.error.emergency.credit.greater.low.balance=O crÃ©dito de emergÃªncia deve ser igual ou inferior ao baixo balanÃ§o.

### Global Non-Disconnect Periods ###
global.ndp.tab.heading=PerÃ­odos Globais de NÃ£o DesconexÃ£o NDP
global.ndp.heading=PerÃ­odos Globais de NÃ£o DesconexÃ£o NDP
global.ndp.none=NÃ£o existe agenda NDP, clique no botÃ£o criar.
global.ndp.schedule.new.added=A agenda NDP global foi criada.
global.ndp.schedule.activation.saved=Uma agenda NDP global foi salva.

### Group Non-Disconnect Periods ###
ndp.active.instruction=Pode ser criada e trabalhada uma agenda NDP, mas apenas serÃ¡ aplicada quando ativada. A agenda pode ser ativada apenas quando pelo menos um dia de NDP com tempos para criado. Seja uma estaÃ§Ã£o com um dia ou um dia especial
ndp.schedule.title= Agenda NDP
ndp.schedule.active=Ativar
ndp.schedule.active.help=Esta agenda estÃ¡ ativa? Selecione a caixa para ativar esta agenda - apenas pode ser feita quando pelo menos um dia NDP for lanÃ§ado.
ndp.schedule.delete.button=Agenda apagada
ndp.disclosurePanel.title=PerÃ­odos de NÃ£o-DesconexÃ£o
ndp.seasons.title=estaÃ§Ãµes
ndp.season.day.title=Dias de EstaÃ§Ã£o
ndp.season.day.description=Introduza um dado de inÃ­cio e um dado final para a estaÃ§Ã£o, seguido do nÃºmero de vezes de NDP por dia da semana.
ndp.assign.season.start=Data de inÃ­cio
ndp.assign.season.start.help=Inserir dados de inÃ­cio da estaÃ§Ã£o
ndp.assign.season.end=Dados finais
ndp.assign.season.end.help=Inserir dados finais da estaÃ§Ã£o
ndp.per.day.title=NDP por dia
ndp.days.of.week=Dia da Semana
ndp.assign.dayperiod.start=Data de inÃ­cio
ndp.assign.dayperiod.start.help=O tempo de inÃ­cio do NDP
ndp.assign.dayperiod.start.hour= hora
ndp.assign.dayperiod.start.minute= minuto
ndp.assign.dayperiod.end= Tempo final
ndp.assign.dayperiod.end.help= O tempo de fim da NDP
ndp.assign.dayperiod.end.hour=hora
ndp.assign.dayperiod.end.minute= minuto
ndp.assign.dayperiod.saved=O tempo de NDP foi salvo.
ndp.assign.dayperiod.deleted=O tempo de NDP foi desligado.
ndp.assign.dayperiod.error.end.before.start=Um dado final nÃ£o pode ser anterior ao dado de inÃ­cio.
ndp.assign.dayperiod.error.time.already.assigned=O tempo selecionado jÃ¡ foi atribuÃ­do.
ndp.assign.dayperiod.nulls=Todos os valores de inÃ­cio e fim devem ser apresentados. NÃ£o pode estar em branco.
ndp.days.title=Tempos diÃ¡rios de NDP
ndp.add.season.button=Adicionar estaÃ§Ã£o
ndp.assign.season.error.end.before.start=O fim nÃ£o pode ser antes do inÃ­cio.
ndp.assign.season.error.date.already.assigned=Estas datas sobrepoÃªm-se com outra estaÃ§Ã£o.
ndp.assign.season.error.time.already.assigned=Um tempo selecionado jÃ¡ foi atribuÃ­do.
ndp.weekdays=Segunda-feira, TerÃ§a-feira, Quarta-feira, Quinta-feira, Sexta-feira, SÃ¡bado e Domingo.
ndp.children.change.alert=As hierarquias inferiores tambÃ©m serÃ£o adaptadas com estas alteraÃ§Ãµes. Continuar?
ndp.error.save.schedule=NÃ£o foi possÃ­vel salvar a informaÃ§Ã£o da agenda NDP.
ndp.abort.save=Decidiu nÃ£o salvar a agenda NDP.
ndp.error.save.season=NÃ£o foi possÃ­vel salvar a estaÃ§Ã£o NDP.
ndp.error.save.day=NÃ£o foi possÃ­vel salvar os tempos diÃ¡rios NDP.
ndp.revert.parent.global=Tem certeza que quer desligar? Este nÃ³ irÃ¡ herdar {0} configuraÃ§Ã£o de NDP .
ndp.children.delete.alert=As hierarquias inferiores com a mesma agenda NDP irÃ£o ser revertidas para as {0} configuraÃ§Ãµes de NDP . Continuar?
ndp.error.delete.day.profiles=NÃ£o foi possÃ­vel apagar os perfis diÃ¡rios NDP.
ndp.error.delete.season=NÃ£o foi possÃ­vel desligar as estaÃ§Ãµes NDP.
ndp.error.delete.special.days=NÃ£o foi possÃ­vel desligar os dias especiais NDP.
ndp.error.delete.schedule=NÃ£o foi possÃ­vel apagar a agenda NDP.
ndp.new.season.button=Adicionar nova estaÃ§Ã£o
ndp.new.special.day.button=Adicionar dia especial
ndp.special.day.column.heading=dia
ndp.no.change.continue=Nada foi alterado. Continuar?
ndp.day.panel.changed=Foram feitas alteraÃ§Ãµes Ã  estaÃ§Ã£o. Baixar estas mudanÃ§as e cancelar?
ndp.schedule.activation.no.change=NÃ£o houve alteraÃ§Ã£o no estado ativo.
ndp.schedule.new.added=Foi adicionada uma nova agenda NDP a este grupo e sugrupos relevantes.
ndp.schedule.activation.saved=Agenda NDP salva.
ndp.schedule.deleted=A agenda NDP foi apagada deste grupo e subgrupos relevantes.
ndp.season.saved=EstaÃ§Ã£o NDP salva.
ndp.day.profile.confirm.delete=Confirmar a exclusÃ£o do perfil diÃ¡rio para {0}?
ndp.day.profile.confirm.delete.and.deactivate=Apagar o perfil diÃ¡rio para {0} irÃ¡ fazer com que a agenda NDP seja desativada apÃ³s atualizar a estaÃ§Ã£o, visto que Ã© o Ãºnico tempo NDP para esta agenda. Continuar?
ndp.season.confirm.delete=Confirmar exclusÃ£o da estaÃ§Ã£o{0}?
ndp.season.deleted=EstaÃ§Ã£o NDP apagada.
ndp.season.confirm.delete.and.deactivate=Apagar a estaÃ§Ã£o para {0} irÃ¡ fazer com que a agenda NDP desative apÃ³s atualizar o tempo, visto que Ã© o Ãºnico tempo NDP para esta agenda. Continuar?
ndp.special.day.confirm.delete=Confirmar exclusÃ£o de dia especial{0}?
ndp.special.day.confirm.delete.and.deactivate=Apagar o dia especial para {0} irÃ¡ fazer com que a agenda NDP seja desativada apÃ³s atualizar a estaÃ§Ã£o, visto que Ã© o Ãºnico tempo NDP para esta agenda. Continuar?
ndp.special.day.deleted=Dia especial desligado.
ndp.special.day.title=dias especiais
ndp.special.day.time.title= Tempos NDP para dias especiais
ndp.special.day.description=Introduza o dia & mÃs para o dia especial, seguido dos tempos NDP para esse dia.
ndp.assign.special.day=Data de dia especial
ndp.assign.special.day.help=Introduza dia e mÃªs do dia especial.
ndp.assign.special.day.duplicate=Dados duplicados do dia especial.
ndp.special.day.panel.changed=Foram feitas alteraÃ§Ãµes a este dia especial. Quer baixar essas alteraÃ§Ãµes e cancelar?
ndp.special.day.times.confirm.delete.and.deactivate=Apagar os tempos para {0} irÃ¡ fazer com que a agenda NDP desative apÃ³s atualizar o dia especial, visto que Ã© o Ãºnico tempo NDP para esta agenda. Continuar?
ndp.special.day.saved=Dia especial NDP salvo.
ndp.error.save.special.day=NÃ£o foi possÃ­vel salvar dia especial NDP.
ndp.global.none.found=NÃ£o existe agenda NDP. Por favor crie uma na secÃ§Ã£o de configuraÃ§Ã£o.
ndp.inherited.global.none=NÃ£o existe agenda global NDP ou, ainda nÃ£o estÃ¡ ativado. Os perÃ­odos globais NDP nÃ£o podem ser herdados.

### Customer Agreement ###
customeragreement.title=Contrato de cliente

### Pricing Structures ###
pricingstructures.header=Estruturas de preÃ§os
pricingstructures.title=Estrutura atual de preÃ§os
pricingstructure.title=Estrutura de preÃ§os
pricingstructure.title.new=Nova estrutura de preÃ§os
pricingstructure.title.edit=Editar estrutura de preÃ§os
pricingstructure.field.name=Nome
pricingstructure.field.description=DescriÃ§Ã£o
pricingstructure.field.status=estado
pricingstructure.field.active=Ativo
pricingstructure.field.name.help=Nome desta estrutura de preÃ§os, deve ser Ãºnico
pricingstructure.field.description.help=A descriÃ§Ã£o desta estrutura de preÃ§os
pricingstructure.field.active.help=Se a estrutura de preÃ§os estÃ¡ ativa ou nÃ£o.
pricingstructure.field.type=Tipo
pricingstructure.field.startdate=Data de inÃ­cio
pricingstructure.field.tariffs=# Tarifas
pricingstructure.error.save=nÃ£o foi possÃ­vel guardar a estrutura de preÃ§os.
pricingstructure.field.serviceresource.help=Se nÃ£o houver tarifas, pode selecionar o recurso de serviÃ§o para a estrutura de preÃ§os.
pricingstructure.field.metertype.help=Se nÃ£o houver tarifas, pode selecionar o tipo de contador para a estrutura de preÃ§os.
pricingstructure.field.paymentmode.help=Se nÃ£o houver tarifas, pode alterar o modo de pagamento para a estrutura de preÃ§os.
pricingstructure.tariffs.prt.none=nÃ£o existem tipos de tarifas disponÃ­veis para a combinaÃ§Ã£o de recursos de serviÃ§o, estruturas de preÃ§os, tipos de contadores e modos de pagamento atuais.
pricingstructure.tariffs.ui.none=nÃ£o existe formulÃ¡rio de preÃ§os disponÃ­vel para ser salvo.
pricingstructure.error.tariff.load=Erro ao carregar os dados das tarifas. NÃ£o Ã© possÃ­vel exibir os campos das tarifas e os valores correspondentes.
pricingstructure.name.duplicate=Nome duplicado {0} para uma estrutura de preÃ§os. Especifique um nome exclusivo.
pricingstructure.error.deactivate=A estrutura de preÃ§os estÃ¡ em uso. NÃ£o Ã© possÃ­vel desativÃ¡-la.

### Tariffs ###
tariffs.header=Tarifas
tariffs.title=Tarifas atuais
tariff.title=Tarifa
tariff.title.add=Adicionar nova tarifa
tariff.title.edit=Editar tarifa
tariff.title.view=Ver tarifa
tariff.field.name=Nome
tariff.field.name.help=O nome desta tarifa deve ser Ãºnico
tariff.field.description=DescriÃ§Ã£o
tariff.field.description.help=A descriÃ§Ã£o deste tarifÃ¡rio
tariff.field.startdate=Data de InÃ­cio
tariff.field.startdate.help=A tarifa de dados desta serÃ¡ ativada. As outras tarifas nÃ£o podem iniciar nesta data somente numa data posterior.
tariff.title.type=Detalhes do tipo de tarifa
tariff.field.type=Tipo
tariff.field.unitprice=PreÃ§o unitÃ¡rio
tariff.field.tax=percentagem de imposto
tariff.field.tax.help=Percentagem como por exemplo 14%
tariff.field.free.units.descrip=DescriÃ§Ã£o das unidades gratuitas mensais
tariff.field.free.units=Unidades gratuitas mensais
tariff.field.free.units.help=Unidades que sÃ£o emitidas gratuitamente.
tariff.field.groupthreshold=Grupo de Limiar
tariff.field.groupthreshold.help=O limite em kWh grupo de uso para o mÃªs apÃ³s o qual o limite de preÃ§o tem efeito
tariff.field.thresholdprice=limite de preÃ§o
tariff.field.thresholdprice.help=preÃ§o por kWh (assim que o limite seja ultrapassado)
tariff.field.price=PreÃ§o
tariff.field.baseprice=PreÃ§o Base
tariff.field.threshold=limite
tariff.field.threshold.help=PreÃ§o por kWh
tariff.field.step1=Passo 1
tariff.field.step2=Passo 2
tariff.field.step3=Passo 3
tariff.error.numeric.value=O valor deve ser numÃ©rico
tariff.error.save=nÃ£o foi possÃ­vel guardar a tarifa.
tariff.error.save.duplicate=nÃ£o foi possÃ­vel a tarifa, jÃ¡ existe outra tarifa com o mesmo nome.
tariff.error.load=nÃ£o foi possÃ­vel carregar os dados tarifÃ¡rios.
tariff.field.block=blocos
tariff.field.block.single=bloco
tariff.error.freeunits.positive=Deve ser um valor positivo.
tariff.error.freeunits.decimal.limit=Unidades sÃ³ podem ter 1 decimal
tariff.blocks.error=Os blocos precisam de valores de preÃ§os unitÃ¡rios e limites vÃ¡lidos.
tariff.blocks.error.incomplete=Os Blocos precisam de um preÃ§o unitÃ¡rio para um limite de valor.
tariff.blocks.error.last=Apenas o bloco final nÃ£o deve ter um limite.
tariff.blocks.error.last.none=O bloco final nÃ£o deve ter um limite.
tariff.blocks.error.increasing.thresholds=Os limiares para um bloco devem ser maiores que zero e que os limiares do bloco anterior.
tariff.blocks.error.none=Os Blocos sÃ£o obrigatÃ³rios.
tariff.tou.thinsmart.season=EstaÃ§Ã£o
tariff.tou.thinsmart.period=PerÃ­odo
tariff.tou.thinsmart.readingtype=Tipo de leitura do contador
tariff.tou.thinsmart.rate=Taxa
tariff.tou.thinsmart.chargeunits=unidades
tariff.tou.thinsmart.specialday=Dia Especial
tarif.adv.settings.header=ConfiguraÃ§Ãµes AvanÃ§adas
tariff.field.pricesymbol=sÃ­mbolo da moeda
tariff.field.pricesymbol.help=O sÃ­mbolo de moeda a ser utilizado para o preÃ§o.
tariff.field.unitsymbol=SÃ­mbolo Unidade
tariff.field.unitsymbol.help=O sÃ­mbolo das unidades monetÃ¡rias para ser usado no preÃ§o.
tariff.field.amountrounding=Modo de arredondamento para o montante
tariff.field.amountrounding.help=O modo de arredondamento para os montantes.
tariff.field.amountprecision=PrecisÃ£o do montante
tariff.field.amountprecision.help=A precisÃ£o dos montantes.
tariff.field.unitsrounding=Unidade de arredondamento
tariff.field.unitsrounding.help=O modo de arredondamento a ser usado para unidades.
tariff.field.unitsprecision=PrecisÃ£o das unidades
tariff.field.unitsprecision.help=A precisÃ£o para as unidades.
tariff.field.taxrounding=Imposto de arredondamento
tariff.field.taxrounding.help=O modo de arredondamento para o imposto.
tariff.field.taxprecision=PrecisÃ£o do Imposto.
tariff.field.taxprecision.help=Uma precisÃ£o a ser usada para o imposto.
tariff.error.field.pricesymbol=Ã necessÃ¡rio o sÃ­mbolo da moeda.
tariff.error.field.unitsymbol=Ã necessÃ¡rio o sÃ­mbolo da unidade.
tariff.error.field.amountrounding=Ã necessÃ¡rio o modo de arredondamento.
tariff.error.field.unitsrounding=Ã necessÃ¡rio o modo de arredondamento.
tariff.error.field.taxrounding=Ã necessÃ¡rio o modo de arredondamento.
tariff.error.field.amountprecision=A precisÃ£o deve ser um valor positivo.
tariff.error.field.unitsprecision=A precisÃ£o deve ser um valor positivo.
tariff.error.field.taxprecision=A precisÃ£o deve ser um valor positivo.
tariff.readonly=A tarifa foi iniciada e Ã© apenas de leitura.
tariff.error.startdate=A data de inicio deve ser futura e maior que a data de inicio da tarifa anterior.

### Tou Calendar ###
calendar.settings.header=DefiniÃ§Ãµes do calendÃ¡rio
calendar.settings.title=DefiniÃ§Ãµes do calendÃ¡rio
calendar.season.title=ConfiguraÃ§Ã£o das estaÃ§Ãµes
calendar.season.current.title=EstaÃ§Ã£o actual
calendar.season.description=Adicionar ou atualizar estaÃ§Ãµes.
calendar.season.field.name=Nome
calendar.season.field.active=Ativo
calendar.season.add=Adicionar EstaÃ§Ã£o
calendar.season.update=Atualizar EstaÃ§Ã£o
calendar.season.field.name.help=Digite um nome para esta estaÃ§Ã£o. Este Ã© um campo obrigatÃ³rio.
calendar.season.field.active.help=O estado de atividade atual deste item
season.error.update=A estaÃ§Ã£o nÃ£o pode ser atualizada.
season.error.save=A estaÃ§Ã£o nÃ£o pode ser salva.
season.error.delete=A estaÃ§Ã£o estÃ¡ em uso e nÃ£o pode ser excluÃ­da.
calendar.season.deleted={0} estaÃ§Ã£o foi excluÃ­da com sucesso.
calendar.period.deleted={0} perÃ­odo foi excluÃ­do com sucesso.

calendar.period.title=PerÃ­odos de configuraÃ§Ã£o
calendar.period.current.title=PerÃ­odos atuais
calendar.period.description=Adicionar e atualizar perÃ­odos (por exemplo, Ponta, Vazio, PadrÃ£o).
calendar.period.field.name=Nome
calendar.period.field.code=cÃ³digo
calendar.period.field.active=Ativo
calendar.period.add=Adicionar perÃ­odo
calendar.period.update=PerÃ­odo de atualizaÃ§Ã£o
calendar.period.field.name.help=Digite um nome para este perÃ­odo. Este Ã© um campo obrigatÃ³rio.
calendar.period.field.code.help=Entre um cÃ³digo para este perÃ­odo. Este Ã© um cÃ³digo exclusivo para o perÃ­odo e Ã© um campo obrigatÃ³rio.
calendar.period.field.active.help=O estado de atividade atual deste item
period.error.save=O perÃ­odo nÃ£o pode ser salvo.
period.error.delete=O perÃ­odo estÃ¡ em uso e nÃ£o pode ser excluÃ­do.

calendars.title=calendÃ¡rios
calendars.heading=ConfiguraÃ§Ã£o de CalendÃ¡rios
calendars.description=Abaixo estÃ£o listados os calendÃ¡rios atuais. Podem-se adicionar ou editar os formulÃ¡rios abaixo.
calendar.field.name=Nome do CalendÃ¡rio
calendar.field.name.help=Digite um nome para este calendÃ¡rio.
calendar.field.description=DescriÃ§Ã£o
calendar.field.description.help=Digite uma descriÃ§Ã£o deste calendÃ¡rio.
calendar.field.active=Ativo
calendar.field.active.help=O estado de atividade atual deste calendÃ¡rio
calendar.title=calendÃ¡rio
calendar.add=Adicionar Novo CalendÃ¡rio
calendar.update=atualizaÃ§Ã£o de calendÃ¡rio
calendar.changes.cleared=AlteraÃ§Ãµes do CalendÃ¡rio Limpo
calendar.save.errors=nÃ£o foi possÃ­vel salvar o calendÃ¡rio
calendar.complete=completa
calendar.incomplete=incompleto
calendar.optional=Opcional
calendar.duplicate=Nome do calendÃ¡rio estÃ¡ duplicado. Escolha um nome Ãºnico.

calendar.assign.season.heading=Atribuir Datas e EstaÃ§Ã£o para calendÃ¡rio
calendar.assign.season.title=dados da estaÃ§Ã£o
calendar.assign.season.description=Digite um dado de inÃ­cio e fim para a estaÃ§Ã£o selecionada.
calendar.assign.season.form.heading=Atribuir dados.
calendar.assign.season=EstaÃ§Ã£o
calendar.assign.season.help=selecionar estaÃ§Ã£o
calendar.assign.season.start=Data de inÃ­cio
calendar.assign.season.start.help=Digite a data do inÃ­cio da EstaÃ§Ã£o
calendar.assign.season.end=Data de Fim
calendar.assign.season.end.help=Digite um dado em que a estaÃ§Ã£o termina
calendar.assign.season.deleted=dados de estaÃ§Ã£o excluÃ­dos com sucesso.
calendar.assign.season.error.end.before.start=Fim nÃ£o pode ser antes de Iniciar
calendar.assign.season.error.date.already.assigned=A data jÃ¡ estÃ¡ atribuÃ­da a uma estaÃ§Ã£o
calendar.assign.season.error.select.season=Selecione uma estaÃ§Ã£o a partir da lista

calendar.assign.period.title=Perfil diÃ¡rio
calendar.assign.period.description=Atribuir tempos do dia a perÃ­odos
calendar.assign.period=PerÃ­odo
calendar.assign.period.help=Escolha um perÃ­odo
calendar.assign.period.start=Hora de inÃ­cio
calendar.assign.period.start.help=A hora que o perÃ­odo comeÃ§a
calendar.assign.period.start.hour=hora
calendar.assign.period.start.minute=minuto
calendar.assign.period.end=Fim do perÃ­odo
calendar.assign.period.end.hour=hora
calendar.assign.period.end.minute=minuto
calendar.assign.period.saved=Tempo foi salvo.
calendar.assign.period.cleared=As alteraÃ§Ãµes dos perÃ­odos foram apagadas.
calendar.assign.period.deleted=As alteraÃ§Ãµes dos perÃ­odos foram excluÃ­das.
calendar.assign.period.error.end.before.start=O perÃ­odo do fim nÃ£o pode ser antes da hora de inÃ­cio
calendar.assign.period.error.time.already.assigned=O tempo selecionado jÃ¡ foi atribuÃ­do.
calendar.assign.period.nulls=Todos os valores finais e iniciais devem ser inseridos. NÃ£o pode estar em branco.

calendar.dayprofiles.heading=Configure os perfis diÃ¡rios para o calendÃ¡rio.
calendar.dayprofiles.title=Perfis diÃ¡rios
calendar.dayprofiles.description=Crie dias que sÃ£o divididos em perÃ­odos de tempo especÃ­ficos.

calendar.dayprofile.field.name=Nome do perfil
calendar.dayprofile.field.code=cÃ³digo
calendar.dayprofile.field.active=Ativo
calendar.dayprofile.field.name.help=Digite um nome para este perfil diÃ¡rio
calendar.dayprofile.field.code.help=Introduza um cÃ³digo rÃ¡pido para este perfil diÃ¡rio.
calendar.dayprofile.field.active.help=O estado de atividade atual desse perfil diÃ¡rio
calendar.dayprofile.error.save=nÃ£o foi possÃ­vel salvar o perfil diÃ¡rio
calendar.dayprofile.error.first.unassign=nÃ£o Ã© possÃ­vel excluir um perfil diÃ¡rio, que estÃ¡ atribuÃ­do a uma estaÃ§Ã£o de calendÃ¡rio. Deve-se cancelar primeiro a atribuiÃ§Ã£o do perfil dia e, em seguida, excluir.
calendar.dayprofile.error.special.day.first.unassign=nÃ£o Ã© possÃ­vel excluir um perfil de dia que estÃ¡ atualmente atribuÃ­do a um dia especial. Apague primeiro o dia especial, e de seguida, exclua o perfil diÃ¡rio.
calendar.dayprofile.deleted=Perfil diÃ¡rio excluÃ­do
calendar.dayprofile.saved=AlteraÃ§Ãµes no perfil diÃ¡rio guardadas.
calendar.dayprofile.cleared=AlteraÃ§Ãµes no perfil diÃ¡rio apagadas.

calendar.assign.dayprofile.heading=Atribuir Perfis diÃ¡rios ao calendÃ¡rio
calendar.assign.dayprofile.description=Para cada estaÃ§Ã£o, que foi atribuÃ­da ao calendÃ¡rio, deve-se especificar o perfil diÃ¡rio para cada dia da semana.
calendar.assign.dayprofile.cleared=Atribuir alteraÃ§Ãµes no perfil diÃ¡rio apagadas.
calendar.assign.dayprofile.saved=Atribuir alteraÃ§Ãµes no perfil diÃ¡rio guardados.
calendar.assign.dayprofile.error.save=Erro ao atribuir perfis diÃ¡rios.

calendar.specialday.heading=Configurar dias especiais para calendÃ¡rio
calendar.specialday.title=Configurar dias especiais
calendar.specialday.description=Atribuir perfis diÃ¡rios para dias especÃ­ficos
calendar.specialday.field.name=Nomes para Dia Especial
calendar.specialday.field.name.help=Um nome Ãºnico para o dia.
calendar.specialday.field.active=Ativo
calendar.specialday.field.active.help=O estado de atividade atual desse dia especial
calendar.specialday.field.day=dia
calendar.specialday.field.month=MÃªs
calendar.specialday.field.dayprofile=perfil do dia
calendar.specialday.field.dayprofile.help=Selecione o perfil de dia
calendar.specialday.field.dayprofile.error=Dia deve ser apresentado para o dia especial.
calendar.specialday.add=Adicionar dia especial
calendar.specialday.update=AtualizaÃ§Ã£o Dia Especial
calendar.special.day.deleted=Dia especial desligado.
calendar.specialday.error.date.already.assigned.to=Esta data jÃ¡ foi atribuÃ­da ao dia especial.

calendar.readOnly=Nota: Este calendÃ¡rio nÃ£o pode ser atualizado porque jÃ¡ estÃ¡ em uso pelas seguintes estruturas de preÃ§os: {0}

### Aux Charge Schedule ###
auxchargeschedules.header=HorÃ¡rios de Contas Auxiliares
auxchargeschedules.title=HorÃ¡rios atuais de Contas Auxiliares
auxchargeschedule.title=ProgramaÃ§Ã£o de Contas Auxiliares
auxchargeschedule.title.add=Adicionar horÃ¡rio de Contas Auxiliares
auxchargeschedule.title.update=Atualizar Agenda de Contas Auxiliares
auxchargeschedule.field.name=Nome
auxchargeschedule.field.name.help= O nome desta conta auxiliar Ã© necessÃ¡rio e deve ser Ãºnico.
auxchargeschedule.field.status=estado
auxchargeschedule.field.minamount= Montante Min
auxchargeschedule.field.minamount.help=Insira o valor mÃ­nimo necessÃ¡rio para cobrar nesta conta auxiliar.
auxchargeschedule.field.maxamount=Montante Max
auxchargeschedule.field.maxamount.help=Introduza o valor mÃ¡ximo para cobrar nesta conta auxiliar.
auxchargeschedule.field.vendportion=Parcela de venda
auxchargeschedule.field.vendportion.help=Insira a porcentagem de venda que serÃ¡ utilizada para calcular a taxa.
auxchargeschedule.field.currportion=Parcela actual
auxchargeschedule.field.currportion.help=Digite a percentagem do saldo remanescente que serÃ¡ utilizada para calcular a taxa.
auxchargeschedule.field.active=Ativo
auxchargeschedule.field.active.help=O estado de atividade atual deste item
auxchargeschedule.error.save=nÃ£o foi possÃ­vel guardar o horÃ¡rio da conta auxiliar.
auxchargeschedule.error.duplicate=Nome duplicado {0} o HorÃ¡rio da Conta Aux. Especifique um nome exclusivo.
auxchargeschedule.nocharge.error=Deve ser definido para esta venda uma parcela de venda, uma parcela de venda ou uma quantidade por venda.

### Auxilliary Type ###
auxilliarytypes.header=Tipos de contas auxiliares
auxilliarytypes.title=Tipos de contas auxiliares atuais
auxillarytype.title.add=Adicionar tipo de conta auxiliar
auxillarytype.title.update=Atualizar tipo de conta auxiliar
auxillarytype.title=Tipo de conta auxiliar
auxtype.field.name=Nome
auxtype.field.description=DescriÃ§Ã£o
auxtype.field.status=estado
auxtype.field.active=Ativo
auxtype.field.name.help=Nome deste tipo de conta auxiliar, deve ser exclusivo
auxtype.field.description.help=DescriÃ§Ã£o deste tipo de conta auxiliar
auxtype.field.active.help=O estado de atividade atual deste item
auxtype.error.save=nÃ£o foi possÃ­vel salvar o tipo de conta auxiliar.
auxtype.error.save.duplicate=nÃ£o foi possÃ­vel salvar o tipo de conta auxiliar, um outro tipo de conta auxiliar com o mesmo nome jÃ¡ existe.
auxtype.error.update=nÃ£o foi possÃ­vel atualizar o tipo de conta auxiliar.
auxtype.error.update.duplicate=nÃ£o foi possÃ­vel atualizar o tipo de conta auxiliar, um outro tipo de conta auxiliar com o mesmo nome jÃ¡ existe.

### Device Store ###
devicestores.header=ArmazÃ©ns
devicestores.title=ArmazÃ©ns actuais
devicestore.title.add=Adicionar armazÃ©m
devicestore.title.update=Atualizar armazÃ©m
devicestore.title=ArmazÃ©ns
devicestore.field.name=Nome
devicestore.field.description=DescriÃ§Ã£o
devicestore.field.name.help=Nome deste armazÃ©m (deve ser exclusivo)
deviceStore.name.duplicate=Nome duplicado {0} para um armazÃ©m. Especifique um nome exclusivo.
devicestore.field.description.help=DescriÃ§Ã£o de armazenamento
devicestore.field.active=Ativo
devicestore.field.active.help=O estado de actividade actual do armazÃ©m 
devicestore.location.title=LocalizaÃ§Ã£o do armazÃ©m
devicestore.error.save=nÃ£o Ã© possÃ­vel guardar armazÃ©m.
devicestore.error.update=nÃ£o foi possÃ­vel actualizar o local do armazÃ©m.
devicestore.button.addmeters=Adicionar contadores para o armazÃ©m selecionado
devicestore.meters=Contadores atuais
devicestore.meters.in=Contadores atuais em
devicestore.meters.description=Contadores actualmente neste armazem.
devicestore.meters.header=ArmazÃ©m de contadores
devicestore.meters.title=Actual armazÃ©m de contadores
devicestore.history=HistÃ³rico de armazÃ©ns
devicestore.history.description=AlteraÃ§Ãµes anteriores feitas a este armazÃ©m (As alteraÃ§Ãµes estÃ£o destacadas)
devicestore.user=UsuÃ¡rio
devicestore.date=Dados
devicestore.date.mod.column=Dados de alteraÃ§Ã£o
devicestore.user.by.column=Pelo UsuÃ¡rio
devicestore.action.column=AcÃ§Ã£o
devicestore.status.column=estado
devicestore.name.column=Nome
devicestore.description.column=DescriÃ§Ã£o
devicestore.button.importmeters=Importar contadores no armazÃ©m selecionado
devicestore.import.meters.header=Importar contadores no armazÃ©m {0}

### Meter ###
meter.title=contador
meter.number.instructions=Procurar por NÃºmero do contador
meter.add=Adicionar novo contador
meter.add.new=Adicionar novo contador
meter.or=vocÃª
meter.then=entÃ£o
meter.enter.number=Digite o nÃºmero do contador
#meter.enter.number=Insira o NÃºmero de Contador
meter.fetch.number=Associar contador
meter.specify.install.date=Especificar dados de instalaÃ§Ã£o
meter.open=Abrir contador
meter.open.newtab=Abrir contador numa nova janela.
meter.attach=Anexar contador ao ponto de medida
meter.assign=Localizar contador
meter.info.title=InformaÃ§Ãµes do contador
meter.required.text=* \=ObrigatÃ³rio
meter.required.activation.text=** \=NecessÃ¡rio para ativaÃ§Ã£o
meter.show.info=Mostrar informaÃ§Ãµes do contador
meter.showing.info=Mostrando informaÃ§Ãµes do contador
meter.active.help=Ã este o contador ativo? Esta opÃ§Ã£o estÃ¡ desativada atÃ© que toda a informaÃ§Ã£o necessÃ¡ria dos contadores seja guardada.
meter.active=Ativo
meter.replace.help=Substituir contador atual neste ponto de medida por outro contador.
meter.replace=Substituir contador no ponto de medida
meter.remove.help=Remover contador atual deste ponto de ativaÃ§Ã£o irÃ¡ desativar o ponto de medida. NÃ£o Ã© possÃ­vel acessar atravÃ©s da Procura avanÃ§ada.
meter.remove=Removedor contador do ponto de medida
meter.assign.from.store.help=Procurar um contador a partir da loja
meter.assign.from.store=procurador contador na loja
meter.date.install.missing=Introduzir novos dados de instalaÃ§Ã£o.
meter.select.meter.model=selecione o modelo do contador
meter.select.meter.model.help=Selecione o modelo de contador. Isto irÃ¡ mostrar os detalhes que sÃ£o necessÃ¡rios para o contador.
#meter.select.metertype.help=Selecione o tipo do contador. Isto irÃ¡ determinar quais detalhes sÃ£o necessÃ¡rios para o contador.
meter.select.metertype=Tipo de contador
meter.select.metertype.help=Selecione o tipo de contador
meter.number.help=Digite o nÃºmero do contador. Este Ã© um campo obrigatÃ³rio - o registro nÃ£o pode ser salvo sem ele.
meter.number=NÃºmero do contador
meter.number.optional.help=Caso tenha, introduza um nÃºmero de contador.
meter.number.optional=NÃºmero de contador (opcional)
meter.iso.help=Digite o nÃºmero do contador ISO.
meter.iso=ISO
meter.checksum.help=Introduza um somatÃ³rio de verificaÃ§Ã£o do contador.
meter.checksum=VerificaÃ§Ã£o da soma
meter.serialnumber.help=Digite o nÃºmero de sÃ©rie do contador.
meter.serialnumber=NÃºmero de sÃ©rie
meter.breakerid=ID do interruptor
meter.breakerid.help=O modelo de contador necessita de um ID de interruptor
meter.breakerid.error=O modelo de contador necessita de um ID de interruptor
meter.stsinfo=InformaÃ§Ã£o STS
meter.algorithmcode=CÃ³digo AlgorÃ­timo
meter.tokentechcode=talÃ£o de engenharia
meter.supplygroupcode=supply group atual
meter.tariffindex=Ãndice de tarifa actual
meter.save.errors=Contador nÃ£o foi salvo. Corrigir os erros.
meter.saved=Contador salvo.
meter.changes.cleared=As alteraÃ§Ãµes foram apagadas.
meter.powerlimit=Limite de PotÃªncia
meter.powerlimit.help=Selecione o limite de potÃªncia necessÃ¡ria
meter.description=DescriÃ§Ã£o
meter.description.help=Digite uma descriÃ§Ã£o
meter.token.active=O ponto de medida deve estar ativo para obter esse talÃ£o
meter.token.code=cÃ³digo de talÃ£o
meter.token.code1=TalÃ£o 1
meter.token.code2=TalÃ£o 2
meter.title.enginerringtokens=talÃµes de engenharia
meter.issue.engineeringtoken=Emitir novos talÃµes de engenharia
meter.engineeringtoken.history=HistÃ³rico de talÃµes de engenharia
meter.engineeringtoken.description=TalÃµes de engenharia anteriores gerados para este contador
meter.select.tokentype=Selecione tipo de talÃ£o
meter.select.tokentype.help=Selecione um talÃ£o de engenharia da lista abaixo.
meter.txn.history=HistÃ³rico de transaÃ§Ãµes
meter.txn.history.description=TransaÃ§Ãµes de clientes anteriores para este contador
meter.history=HistÃ³rico do contador
meter.history.description=AlteraÃ§Ãµes feitas acima para este contador (mudanÃ§as sÃ£o destacadas)
meter.reports=RelatÃ³rios para Contador
meter.recharge.history=HistÃ³rico das recargas do contador
meter.retailers=Retalhistas nas proximidades
meter.reports.general=RelatÃ³rios Gerais para Contadores
meter.reports.meter=RelatÃ³rios para Contador
meter.freeissue.units=EdiÃ§Ã£o GrÃ¡tis - Unidades
meter.cleartamper=Limpar tamper
meter.clearcredit=limpar crÃ©dito
meter.clearcredit.all=Limpar Todo o CrÃ©dito
meter.clearcredit.elec=Limpar todo o crÃ©dito de eletricidade
meter.clearcredit.type.description=Tipo de crÃ©dito
meter.clearcredit.type.help=Selecione o tipo de crÃ©dito que precisa ser limpo
meter.coords=Adicionar coordenadas de latitude e longitude para o ponto de medida para este contador, a fim de ver os retalhistas nas proximidades.
meter.user=UsuÃ¡rio
meter.serial=SÃ©rie
meter.date=Dados
meter.date.mod.column=Dados de alteraÃ§Ã£o
meter.user.by.column=Pelo UsuÃ¡rio
meter.action.column=AcÃ§Ã£o
meter.status.column=estado
meter.number.column= NÃºmero do Contador
meter.serial.column=SÃ©rie
meter.uniqueid.column= ID Ãºnico
meter.unique.external.column= ID Ãºnico Externo
meter.techtoken.column=TT
meter.alg.column=Alg
meter.supplygroup.column=SG
meter.keyrevision.column=KR
meter.tariffindex.column=TI
meter.enddevicestore.column=Loja
meter.units.kwh=Unidades (kWh)
meter.units.kwh.help=Digite o nÃºmero de unidades de kWh necessÃ¡rias.
meter.units.watts=Unidades (Watts)
meter.units.watts.help=Digite o limite mÃ¡ximo por fase em Watts.
meter.currency=moeda
meter.currency.help=Insira o valor de moeda necessÃ¡ria.
meter.free.description=DescriÃ§Ã£o
meter.free.description.help=Digite uma descriÃ§Ã£o para esta emissÃ£o grÃ¡tis.
meter.setphase=selecione Fase MÃ¡xima
meter.setphase.description=DescriÃ§Ã£o
meter.setphase.description.help=Digite uma descriÃ§Ã£o para este talÃ£o de fase.
meter.token.error=nÃ£o foi possÃ­vel obter talÃ£o. Verifique erros.
meter.error.units=Digite um valor vÃ¡lido para o nÃºmero de unidades necessÃ¡rias.
meter.error.amount=Digite um valor vÃ¡lido para o montante necessÃ¡rio.
meter.txn.type=Tipo
meter.txn.token.type=tipo de talÃ£o
meter.txn.receipt=Recibo
meter.txn.token=talÃ£o
meter.txn.amount=Quantidade de IVA incl
meter.txn.tax=imposto
meter.txn.units=unidades
meter.txn.tariff=Tarifa
meter.txn.date=Dados
meter.txn.ref=referÃªncia
meter.txn.receiptnum=NÃºmero do recibo
meter.txn.customer=cliente
meter.txn.client=cliente
meter.txn.term=terminal
meter.txn.revref=ReferÃªncia da reversÃ£o
meter.txn.isreversed=revertida
meter.txn.amount.column=quantidade
meter.txn.usagepoint=Ponto de medida
meter.txn.user=UsuÃ¡rio
meter.txn.description=DescriÃ§Ã£o
meter.clear.description=DescriÃ§Ã£o
meter.clear.description.help=Digite uma descriÃ§Ã£o para a limpeza desta adulteraÃ§Ã£o.
meter.changekey=MudanÃ§a de chave
meter.changekey.instructions=Para talÃµes de MudanÃ§a de chave:\n1. Abra o Painel de Contadores.\n2. No bloco de informaÃ§Ã£o STS, altere o cÃ³digo do supply group atual.\n3. Isto irÃ¡ adicionar uma nova caixa de listagem com as opÃ§Ãµes de alteraÃ§Ã£o de Chave - Selecione o cÃ³digo obrigatÃ³rio.\n4. Uma vez salvo o contador, uma caixa de texto aparecerÃ¡ com mais detalhes.
meter.new.supplygroupcode.help=Digite o novo cÃ³digo do supply group.
meter.new.supplygroupcode=Novo CÃ³digo do supply group
meter.old.supplygroupcode=Anterior CÃ³digo do supply group
meter.new.tariffindex=Ãndice de tarifa
meter.old.tariffindex=Ãndice de tarifa antiga
meter.new.keyrevisionnum=Nova chave do NÃºmero de RevisÃ£o
meter.old.keyrevisionnum=Chave antiga do NÃºmero de RevisÃ£o
meter.new.tariffindex.help=Digite o novo Ã­ndice tarifÃ¡rio.
meter.txn.filter=Filtro
mdc.txn.show.connect.disconnect.only=Mostrar apenas Ligar / Desligar
mdc.txn.show.balance.messages.only=Ver mensagens de saldo
meter.engtoken.filter=Filtro
meter.history.filter=Filtro
meter.generate.keychange=Gerar talÃµes de mudanÃ§a de chave?
meter.generate.keychange.help=Se o contador tem de ser atualizado para corresponder aos novos detalhes STS, entÃ£o os talÃµes de mudanÃ§a de chave devem ser gerados. Se o registro estÃ¡ sendo atualizado para combinar com os detalhes do contador, entÃ£o nÃ£o hÃ¡ necessidade de gerar talÃµes.
meter.luhncheck.failed=NÃºmero do contador incorreto (verificaÃ§Ã£o falhou)
meter.error.alreadyexists=JÃ¡ existe um contador com este nÃºmero.
meter.keychange.none=NÃ£o gerar talÃµes de mudanÃ§as de chave
meter.keychange.now=Gerar talÃµes de mudanÃ§as agora
meter.keychange.flag=Definir para gerar talÃµes de mudanÃ§a de chave com a prÃ³xima venda
meter.pending.keychange=* A aguardar a mudanÃ§a de chave
meter.warning.sg.transactions=NOTA: JÃ¡ hÃ¡ mais de 3 transaÃ§Ãµes neste contador.
meter.error.save=nÃ£o foi possÃ­vel salvar o contador.
stsmeter.error.save=nÃ£o foi possÃ­vel salvar o contador STS.
meter.select.store.move=Mover o contador atual para a seguinte loja:
meter.select.store.help=O contador atual deve ser adicionado a uma loja. Quando Ã© atribuÃ­do a um ponto de medida, ele serÃ¡ removido automaticamente do depÃ³sito. Este Ã© um campo obrigatÃ³rio - o registro nÃ£o pode ser salvo sem ele.
meter.message.type=Tipo de mensagem
meter.message.type.help=Enviar uma mensagem para o contador
meter.assigned.to.usagepoint=Contador atualmente atribuÃ­do a um ponto de medida:
meter.error.invalid=InformaÃ§Ã£o invÃ¡lida do contador.
meter.error.invalid.datemanu=Dados de fabrico sÃ£o invÃ¡lidos.
meter.error.cannot.activate=nÃ£o Ã© possÃ­vel ativar
meter.error.required.for.activation=NecessÃ¡rio para a ativaÃ§Ã£o
meter.partial.search=Contador nÃ£o corresponde. Fazendo pesquisa avanÃ§ada...
meter.install.date.required=A data e hora em que o novo contador foi instalado no ponto de medida {0}.
meter.installed.at=Contador foi instalado no {0} em {1}
meter.connect.disconnect.error=Ocorreu um erro de envio de instruÃ§Ãµes do contador: {0}
meter.connect.disconnect.connection.error=Sem resposta recebida. ServiÃ§o nÃ£o disponÃ­vel. Por favor notifique o administrador do sistema.
meter.connect.disconnect.ok.mdc000=Comando no Contador {0} concluÃ­do com sucesso. ReferÃªncia={1}.
meter.connect.disconnect.ok.mdc010=Comando no Contador {0} aceito. ReferÃªncia={1}.
meter.connect.disconnect.ok.mdc011=Mensagem de Contador {0} foi colocada na fila. ReferÃªncia={1}.
meter.manufacturer.code.length=CÃ³digo do fabricante
meter.manufacturer.code.length.help=comprimento do cÃ³digo do fabricante, 2 dÃ­gitos ou 4 dÃ­gitos.
meter.2digit.manufacturer.code=CÃ³digo de 2 dÃ­gitos
meter.4digit.manufacturer.code=CÃ³digo de 4 dÃ­gitos
meter.found.incorrect.group=Contador encontrado mas num grupo diferente do usuÃ¡rio.
usagepoint.found.incorrect.group=Ponto de medida encontrado mas num grupo diferente do usuÃ¡rio.
meter.attach.cancelled=Processo de anexaÃ§Ã£o cancelado. O contador {0} foi criado no armazÃ©m, mas nÃ£o foi atribuÃ­do a este ponto de medida.
meter.attach.instructions=Anexar contador ao ponto de medida
meter.attach.cancel.button=Cancelar contador ao ponto de medida

### Customer ###
customer.title=cliente
customer.search.instructions=Pesquisar cliente por:
customer.add=Adicionar novo cliente
customer.add.new=Adicionar novo cliente
customer.info.title=InformaÃ§Ãµes do Cliente
customer.show.info=Mostrar InformaÃ§Ãµes do Cliente
customer.showing.info=Mostrando InformaÃ§Ãµes do Cliente
customer.active.help=Ã um cliente ativo? Esta opÃ§Ã£o Ã© desativada atÃ© que todas as informaÃ§Ãµes solicitadas para o cliente sejam guardadas.
customer.active=Ativo
customer.unassign=Retirar Cliente do ponto de medida
customer.assign=Atribuir Cliente ao ponto de medida
customer.assign.short=Atribuir ao Cliente
customer.assign.help=Atribuir um cliente existente a este ponto de medida
customer.unassign.help=RemoÃ§Ã£o do cliente actual deste ponto de medida.
customer.fetch=Associar cliente
customer.fetch.help=Associar cliente existente
customer.fetch.duplicate= O cliente{0} jÃ¡ estÃ¡ na pÃ¡gina.
customer.open=Abrir Cliente
customer.field.title.help=Insira o tÃ­tulo do cliente (por exemplo, Sr, Sr.Âª, Dr).
customer.field.title=tÃ­tulo
customer.initials.help=Introduza como inicial do cliente.
customer.initials=Iniciais
customer.firstnames.help=Introduza os primeiros nomes do cliente.
customer.firstnames=Nomes
customer.surname.help=Introduza o apelido do cliente. Este Ã© um campo obrigatÃ³rio e o cliente nÃ£o poderÃ¡ ser salvo sem ele.
customer.surname=Apelido
customer.surname.instr=Digite o apelido do cliente
customer.company.help=Introduza o nome da empresa.
customer.company=Nome da empresa
customer.tax.help=Insira o nÃºmero fiscal do cliente.
customer.tax=NÃºmero de IdentificaÃ§Ã£o Fiscal
customer.emails.help=Introduza os endereÃ§os de e-mail do cliente.
customer.phones.help=Insira os nÃºmeros de telefone.
customer.address.physical=EndereÃ§o fÃ­sico
customer.agreement=Contrato com o Cliente
customer.agreementref.help=Introduza a referÃªncia do contrato com o cliente. Este Ã© um campo obrigatÃ³rio - o cliente nÃ£o pode ser salvo sem ele.
customer.agreementref=Ref. de Contrato
customer.startdate.help=Introduza a data de inÃ­cio do contrato. Embora o registro possa ser salvo sem esses dados, o cliente nÃ£o pode ser ativado, a menos que seja preenchido corretamente.
customer.startdate=Data de inÃ­cio
customer.freeissue.help=Selecione a conta auxiliar para ser usado com talÃµes grÃ¡tis, onde os valores dos talÃµes serÃ£o recuperados atravÃ©s da adiÃ§Ã£o do montante para a conta selecionada.
customer.freeissue=Emitir conta auxiliar
customer.required=* \=ObrigatÃ³rio
customer.required.activation=** \=NecessÃ¡rio para ativaÃ§Ã£o
customer.save.errors=O cliente nÃ£o foi guardado. Corrigir os erros exibidos.
customer.sync.accbal.error=Ocorreu um erro ao sincronizar o saldo da conta ao contador. ReferÃªncia={0}
customer.sync.accbal.connection.error=Sem resposta do serviÃ§o.
customer.sync.accbal.ok.mdc000=SincronizaÃ§Ã£o do Saldo da conta concluÃ­da com Ãªxito. ReferÃªncia={0}
customer.sync.accbal.ok.mdc010=Comando de SincronizaÃ§Ã£o do saldo da conta aceite. ReferÃªncia={0}
customer.sync.accbal.ok.mdc011=Mensagem de sincronizaÃ§Ã£o do saldo da Conta foi colocada na fila. ReferÃªncia={0}
customer.changes.cleared=As alteraÃ§Ãµes foram apagadas.
customer.auxaccount.adjust=Ajudar conta auxiliar
customer.title.find=Procurar Cliente
customer.assigned=NÃ£o Ã© possÃ­vel atribuir ao cliente, jÃ¡ foi atribuÃ­do.
customer.auxaccount=Conta Auxiliar: {0}
customer.auxaccount.addedit=Contas auxiliares
customer.auxaccount.active=Ativo
customer.auxaccount.active.help=Esta conta estÃ¡ ativa? Esta opÃ§Ã£o Ã© desativada atÃ© que todas as informaÃ§Ãµes solicitadas para a conta sejam guardadas.
customer.auxaccount.type.help=Selecione o tipo de conta auxiliar que estÃ¡ adicionando.
customer.auxaccount.balance=BalanÃ§o
customer.auxaccount.balance.help=Digite o saldo atual da conta.
customer.auxaccount.balance.pos=Um balanÃ§o positivo indica um REEMBOLSO.
customer.auxaccount.balance.neg=Um balanÃ§o negativo indica uma DIVIDA.
customer.auxaccount.priority.help=Insira uma prioridade para esta conta (1 Ã© prioridade mais alta).
customer.auxaccount.chargeschedule=Agenda de encargos
#customer.auxaccount.chargeschedule=Agenda de Encargos
customer.auxaccount.chargeschedule.help=Selecione a agenda de encargos.
customer.auxaccount.txn.history=HistÃ³rico de transaÃ§Ãµes de conta auxiliar para: {0}.
customer.auxaccount.txn.description=TransaÃ§Ãµes anteriores para esta conta auxiliar
customer.title.auxaccounts=Contas auxiliares
customer.title.auxaccounts.current=Contas auxiliares atuais
customer.title.auxaccounts.description=Exibir e editar contas auxiliares atuais e criar novas contas auxiliares.
customer.title.txnhistory=HistÃ³rico de transaÃ§Ãµes
customer.title.history=histÃ³rico do cliente
customer.title.reports=RelatÃ³rios para Clientes
customer.title.generalreports=RelatÃ³rios Gerais de Clientes
customer.title.chargescheduledetails=Detalhes da agenda dos encargos auxiliares
customer.title.chargeschedule=Agenda de encargos
customer.chargeschedule.startdate=Data de inÃ­cio
customer.chargeschedule.vendpor=Parcela de Venda
customer.chargeschedule.currpor=Parcela actual
customer.chargeschedule.minamt=Montante Min
customer.chargeschedule.maxamt= Montante Max
customer.chargeschedule.status=estado
customer.auxaccount.filter=Filtro
customer.auxaccount.date=Dados
customer.auxaccount.edit=editar
customer.auxaccount.name=Nome da conta
customer.auxaccount.name.help=Digite um nome para esta conta.
customer.auxaccount.type=Tipo de conta
customer.auxaccount.type.column=Tipo
customer.auxaccount.priority=prioridade
customer.auxaccount.status=estado
customer.auxaccount.freeissue=EmissÃ£o gratuita
customer.auxaccount.add=Adicionar conta auxiliar
customer.auxaccount.update=Atualizar conta auxiliar
customer.auxaccount.error.save=nÃ£o foi possÃ­vel salvar a conta auxiliar.
customer.auxaccount.error.id=NÃ£o foi possÃ­vel aplicar o ajuste Ã  conta auxiliar para a conta do cliente. Entre em contato com o suporte!
customer.auxaccount.error.unique.priority=Ã necessÃ¡ria prioridade e deve ser Ãºnica. Prioridade nÃ£o pode ser menor do que ou igual a zero.
customer.freeissue.error.save=NÃ£o foi possÃ­vel atualizar o contrato do cliente.
customer.partial.search=Cliente correspondente nÃ£o encontrado. Fazendo pesquisa avanÃ§ada...
customer.agreement.partial.search=Contrato correspondente nÃ£o encontrado. Fazendo pesquisa avanÃ§ada...
customer.account.partial.search=Conta correspondente nÃ£o encontrada. Fazendo pesquisa avanÃ§ada...

customer.date.mod.column=Dados de alteraÃ§Ã£o
customer.user.by.column=Pelo UsuÃ¡rio
customer.action.column=AcÃ§Ã£o
customer.status.column=estado
customer.title.column=tÃ­tulo
customer.initials.column=Iniciais
customer.firstnames.column=Nomes
customer.surname.column=Apelido
customer.company.column=empresa
customer.email1.column=E-mail
customer.email2.column=E-mail 2
customer.phone1.column=telefone
customer.phone2.column=Telefone 2

customer.user=UsuÃ¡rio
customer.date=Dados
customer.history=histÃ³rico do cliente
customer.history.description=AlteraÃ§Ãµes feitas acima para este cliente (as alteraÃ§Ãµes estÃ£o destacadas)
customer.history.filter=Filtro

customer.agreement.user=UsuÃ¡rio
customer.agreement.date=Dados
customer.agreement.history=HistÃ³rico do contrato com o Cliente
customer.agreement.history.description=As alteraÃ§Ãµes anteriores feitas a este contrato com o cliente (as alteraÃ§Ãµes sÃ£o destacadas)
customer.agreement.history.filter=Filtro
customer.agreement.date.mod.column=Dados de alteraÃ§Ã£o
customer.agreement.user.by.column=Pelo UsuÃ¡rio
customer.agreement.action.column=AcÃ§Ã£o
customer.agreement.status.column=estado
customer.agreement.customer.column=cliente
customer.agreement.ref.column= ReferÃªncia do Contrato
customer.agreement.start.column=Data de InÃ­cio
customer.agreement.freeaux.column=Conta aux. gratuito.
customer.error.save=NÃ£o foi possÃ­vel salvar o cliente.
customeraggreement.error.save=NÃ£o foi possÃ­vel guardar o contrato do cliente.

customer.account=conta de cliente
customer.account.name=Nome da conta
customer.account.name.help=Digite um nome para esta conta
customer.account.balance=saldo da conta
customer.account.balance.help=O saldo da conta corrente
customer.account.sync=Sincronizar crÃ©dito
customer.account.sync.help=Sincronizar o saldo da conta com o saldo do contador
customer.account.low.balance.threshold=Limite de crÃ©dito baixo
customer.account.low.balance.threshold.help=Quando o saldo da conta atingir esse limite, uma mensagem serÃ¡ enviada para o cliente.
customer.account.credit.limit=Limite de CrÃ©dito
customer.account.credit.limit.help=O limite de crÃ©dito permitido nesta conta
customer.account.note=A conta de cliente sÃ³ Ã© necessÃ¡ria se a estrutura de preÃ§os selecionada para o ponto de medida (abaixo) o require.
customer.account.notification.email= E-mail de notificaÃ§Ã£o
customer.account.notification.email.help=Um endereÃ§o de e-mail para o qual podem ser enviadas notificaÃ§Ãµes relacionadas com a conta(por exemplo, quando o limite de crÃ©dito foi atingido)
customer.account.notification.phone=Telefone de notificaÃ§Ã£o
customer.account.notification.phone.help=Um nÃºmero de telefone para o qual podem ser enviadas notificaÃ§Ãµes relacionadas com a conta (por exemplo, quando o limite de crÃ©dito foi atingido)
customeraccount.error.save=nÃ£o foi possÃ­vel salvar a conta do cliente.

customer.txn.filter=Filtro
customer.txn.history=HistÃ³rico de transaÃ§Ãµes
customer.txn.description=TransaÃ§Ãµes da conta anterior para este contrato de cliente.
customer.txn.ent.date=Dados introduzidos
customer.txn.user=Pelo UsuÃ¡rio
customer.txn.name=cliente
customer.txn.agreement.ref=ReferÃªncia de contrato
customer.txn.trans.type=Tipo de transaÃ§Ã£o
customer.txn.trans.date=Data da TransacÃ§Ã£o
customer.txn.comment=ComentÃ¡rio
customer.txn.amt=Valor do IVA
customer.txn.tax=imposto
customer.txn.bal=Saldo
customer.txn.input=Ajuste da conta
customer.txn.acc.ref=ReferÃªncia da conta
customer.txn.our.ref=Nossa ReferÃªncia
customer.txn.amt.incl.tax=Montante incl. imposto
customer.txn.successful.adjustment=Conta ajustada com sucesso e novo saldo da conta
customer.txn.no.agreement=nÃ£o existe nenhum contrato de cliente para ajustar
customer.txn.error.amt.and.tax.zero=Montante e imposto nÃ£o podem ser ambos zero
customer.txn.error.update=Erro ao atualizar o saldo da conta do cliente
customer.txn.error.insert=Erro ao inserir uma nova transaÃ§Ã£o de ajuste
customer.txn.error.no.usagepoint=Ponto de medida nÃ£o encontrado
customer.txn.notification.failure=nÃ£o foi possÃ­vel enviar uma notificaÃ§Ã£o de ajuste de contas. Por favor notifique o administrador do sistema.
customer.txn.send.email.failure=nÃ£o foi possÃ­vel enviar e-mail Acerto de Contas para o cliente. Por favor notifique o administrador do sistema.

# Customer transaction upload
customer.trans.upload=Carregamento de transaÃ§Ã£o
customer.trans.upload.heading=Carregamento de transaÃ§Ã£o do cliente
customer.trans.upload.data.title=Importar conta de balanÃ§o com configuraÃ§Ãµes de transaÃ§Ã£o da conta do cliente
customer.trans.upload.data.description=Selecione o arquivo .CSV que contÃ©m as transaÃ§Ãµes do cliente a importar para o sistema de GestÃ£o de Contadores.
customer.trans.upload.file.help=Selecione o arquivo .CSV que contÃ©m as transaÃ§Ãµes do cliente a importar para o sistema
customer.trans.upload.file=Selecionar ficheiro de transaÃ§Ã£o
customer.trans.upload.csv.button=Carregar dados CSV
customer.process.trans.button=Processar transaÃ§Ãµes
customer.trans.upload.identifierType=Tipo de identificador
customer.trans.upload.identifier=identificador
customer.trans.upload.amt.incl.tax=Montante incl Imposto
customer.trans.upload.amt.tax=Montante de imposto
customer.trans.upload.trans.date=Data de transaÃ§Ã£o
customer.trans.upload.account.ref=ReferÃªncia de conta
customer.trans.upload.comment=ComentÃ¡rios
customer.trans.upload.errors=Erros
customer.trans.upload.table.heading.errors=TransaÃ§Ãµes: Erros
customer.trans.upload.Bizswitch.down=NÃ£o foi recebida resposta. ServiÃ§o BizSwitch nÃ£o estÃ¡ disponÃ­vel. Por favor notifique o administrador do sistema.
customer.trans.upload.table.heading.valid=TransaÃ§Ãµes vÃ¡lidas: Demonstrar para as primeiras 15 linhas do ficheiro
customer.trans.upload.invalid.cannot.create.dir=ERRO! NÃ£o foi possÃ­vel criar um diretÃ³rio. Por favor, entre em contato com o suporte.
customer.trans.upload.filename= Ficheiro selecionado={0}
customer.trans.upload.invalid.filename=Ficheiro imprÃ³rio - hifen ou ponto em falta. Os nomes dos arquivos sÃ£o esperados como AccountTrans-reference.csv onde <reference> Ã© guardado como 'our Ref' nas transaÃ§Ãµes
customer.trans.upload.invalid.filename.changed=O nome do ficheiro mudou entre etapas! Era {0}; Agora Ã© {1}
customer.trans.upload.invalid.unexpected.commas=ParÃªnteses dentro dos campos - nÃ£o foi possÃ­vel identificar campos separados com precisÃ£o
customer.trans.upload.invalid.identifiertype=O tipo de identificador deve ser Nome Ponto Util / Nome Conta / NÃºm Contador
customer.trans.upload.invalid.identifier=Indetificador invÃ¡lido - NÃ£o estÃ¡ na base-de-dados
customer.trans.upload.invalid.agreement=O ponto de medida nÃ£o tem um contrato de cliente
customer.trans.upload.invalid.usagepoint.or.agreement=O contador nÃ£o tem ponto de medida ou o ponto de medida nÃ£o tem contrato
customer.trans.upload.invalid.usagepoint=A conta do cliente nÃ£o pertence a um ponto de medida
customer.trans.upload.invalid.amt.incl.tax=Montante incluindo Imposto nÃ£o Ã© numÃ©rico
customer.trans.upload.invalid.amt.tax=Montante de imposto nÃ£o Ã© numÃ©rico
customer.trans.upload.invalid.trans.date=A data da transaÃ§Ã£o deve estar vazia (dados de processamento) ou formatada como aaaa-mm-dd hh:mm:ss, exemplo: 2015-09-23 22:14:55
customer.trans.upload.invalid.account.ref=ReferÃªncia de conta, no mÃ¡ximo 100 quilates.
customer.trans.upload.invalid.comment=ComentÃ¡rio, mÃ¡ximo 255 quilates.
customer.trans.upload.invalid.duplicate=Tipo de identificador de transmissÃ£o duplicado: {0}, identificador: {1}. Ambos apontam para a mesma conta de cliente.
customer.trans.upload.file.action.unknown=AcÃ§Ã£o desconhecida no carregamento do ficheiro, contacte o suporte
customer.trans.upload.file.none=Nenhum ficheiro foi selecionado ou carregado
customer.trans.upload.file.error=Erro ao carregar o ficheiro
customer.trans.upload.file.process.error=Erro ao processar o ficheiro
customer.trans.upload.successful.counts=Um total de {0} transaÃ§Ãµes no lote - {1} foram processadas com sucesso, {2} foram duplicadas e ignoradas desta vez.
customer.trans.upload.process.failed= Erro de sistema na transaÃ§Ã£o: Tipo de identificador= {0}, identificador={1}, Ref Conta={2}. A tentar re-submeter ficheiro.
customer.trans.upload.trans.validation.errors=Foram encontrados erros de validaÃ§Ã£o. Por favor, repare-os e envie-os. SÃ³ podem ser processados um limite mÃ¡ximo de 15 erros de cada vez.

### Location ###
location.field.erfnumber=NÃºmero Erf
location.field.erfnumber.help=Digite o nÃºmero ERF deste local
location.field.address=EndereÃ§o
location.field.address.help=Digite o endereÃ§o deste local
location.field.city=cidade
location.field.city.help=Digite o nome da cidade
location.field.province=ProvÃ­ncia
location.field.province.help=ProvÃ­ncia
location.field.country=paÃ­s
location.field.country.help=Selecione o paÃ­s
location.field.postalcode=cÃ³digo postal
location.field.postalcode.help=Digite o cÃ³digo postal
location.field.lat=Latitude
location.field.lat.help= introduza uma latitude
location.field.long=Longitude
location.field.long.help= Digite uma longitude
location.latitude.invalid=Latitude Ã© invÃ¡lida.
location.longitude.invalid=Longitude Ã© invÃ¡lida.
location.field.streetnumber=NÃºmero da Rua
location.field.streetnumber.help=Digite o nÃºmero da rua
location.field.buildingname=Nome do edifÃ­cio
location.field.buildingname.help=Digite o nome do edifÃ­cio
location.field.suitenumber=NÃºmero do apartamento
location.field.suitenumber.help=Digite o nÃºmero do apartamento
location.error.save=nÃ£o foi possÃ­vel salvar o local.
location.user=UsuÃ¡rio
location.date=Dados
location.history=HistÃ³rico de localizaÃ§Ã£o
location.history.description=Foram feitas alteraÃ§Ãµes anteriores para este endereÃ§o.
location.history.date.mod.column=Dados de alteraÃ§Ã£o
location.history.user.by.column=Pelo UsuÃ¡rio
location.history.action.column=AcÃ§Ã£o
location.history.status.column=estado
location.history.address1.column=EndereÃ§o
location.history.address2.column=
location.history.address3.column=
location.history.erfnumber.column=NÃºmero Erf
location.history.latitude.column=Latitude
location.history.longitude.column=Longitude
location.history.group.column=grupo
location.history.streetnum.column=NÃºmero da rua
location.history.buildingname.column=Edificio
location.history.suitenum.column=NÃºmero do apartamento
location.history.physical.address=HistÃ³rico de endereÃ§o fÃ­sico

## Display Tokens
displaytokens.title=Mostrar talÃµes
display.initiate=Iniciar teste ao contador
display.testload=1. Teste a carga
display.testdisplay=2. Teste os dispositivos de visualizaÃ§Ã£o de informaÃ§Ãµes do contador
display.totals=3. Mostrar os kWh acumulados e registros de energia
display.krn=4. Apresentar o KRN
display.ti=5. Apresente um TI
display.testreader=6. Teste o dispositivo leitor de talÃ£o
display.powerlimit=7. Exibir limite mÃ¡ximo de potÃªncia
display.tamper=8. Mostrar o modo de tamper
display.consumption=9. Mostrar o consumo de energia
display.version=10. Apresente a versÃ£o do software
display.phase=11. ExibiÃ§Ã£o limite de potÃªncia e desequilÃ­brio de fase

## Change Group
changegroup.change=Mudar grupo
changegroup.username=Nome de usuÃ¡rio
changegroup.current=Grupo real
usergroup.field.grouphierarchy=NÃ­vel do grupo
changegroup.set=selecionar grupo
changegroup.select=Analise os grupos de controle de acesso disponÃ­veis abaixo e selecione um.
changegroup.available=Grupos disponÃ­veis
changegroup.error.group.none=Selecione um grupo vÃ¡lido.
changegroup.error.same=O grupo selecionado Ã© o mesmo grupo que o seu grupo atual.
changegroup.assigned=Grupo de Acesso AtribuÃ­do ao UsuÃ¡rio
changegroup.group.changed=O seu grupo atual foi alterado.
changegroup.group.cleared=O seu grupo atual foi cancelado, permanecendo agora a utilizar o grupo atribuÃ­do.
changegroup.error.clear=Para passar do grupo atribuÃ­do ao grupo cancelado Ã© necessÃ¡rio selecionar o grupo atual a utilizar.
changegroup.current.details=Detalhes atuais
changegroup.username.help=Comece a digitar o nome de usuÃ¡rio e selecione o usuÃ¡rio correto na lista.
changegroup.warning=Nota: A alteraÃ§Ã£o do seu grupo exigirÃ¡ a atualizaÃ§Ã£o e / ou encerramento de separadores de espaÃ§o de trabalho que estÃ£o relacionados com o grupo.

## User Group
usergroup.title=Grupo de Acesso do UsuÃ¡rio
usergroup.header=Grupo de Acesso do UsuÃ¡rio
usergroup.title.current=Grupo atual de acesso do usuÃ¡rio
usergroup.label.help=Selecione o grupo de acesso a que o UsuÃ¡rio irÃ¡ pertencer para a visualizaÃ§Ã£o e alteraÃ§Ã£o dos dados.
usergroup.error.user=UsuÃ¡rio especificado invÃ¡lido.
usergroup.error.group=Grupo especificado invÃ¡lido.
usergroup.error.save=nÃ£o foi possÃ­vel salvar o grupo do UsuÃ¡rio.
usergroup.error.update=nÃ£o foi possÃ­vel atualizar o grupo do UsuÃ¡rio.
usergroup.field.user=UsuÃ¡rio
usergroup.field.usergroup=Grupo de Acesso AtribuÃ­do
usergroup.title.add=Adicionar usuÃ¡rio ao grupo de acesso
usergroup.title.update=Atualizar UsuÃ¡rio ao Grupo de Acesso
usergroup.instructions=Cada usuÃ¡rio pode ser atribuÃ­do a um grupo de acesso. O grupo de acesso do UsuÃ¡rio, determina o que estÃ¡ disponÃ­vel para o UsuÃ¡rio visualizar e editar dados.
usergroup.confirm.clear=Tem certeza que quer cancelar o grupo de acesso do utlizador? Esse utlizador jÃ¡ nÃ£o poderÃ¡ acessar ao sistema.
usergroup.clear.yourself=NÃ£o Ã© possÃ­vel cancelar o seu grupo de acesso enquanto estiver conectado nesta sessÃ£o.
usergroup.cleared=O grupo de acesso do UsuÃ¡rio foi cancelado.
usergroup.error.usergroup.none=nÃ£o existe nenhum grupo de acesso atual para o UsuÃ¡rio.
usergroup.error.delete=nÃ£o foi possÃ­vel limpar o grupo de acesso do UsuÃ¡rio.
usergroup.no.accessgroup=Nenhum tipo de grupo de acesso foi definido para acesso a dados. Use a pÃ¡gina tipos de grupo para definir um tipo de grupo de acesso.
usergroup.accessgroup.none=nÃ£o Ã© possÃ­vel atribuir grupo de acesso aos UsuÃ¡rios, visto que nÃ£o existem grupos disponÃ­veis para controle de acesso do UsuÃ¡rio.

## Access Groups
accessgroups.title=Grupos de Acesso
accessgroup.access.label=Tipo de grupo:
accessgroups.title.current=Grupo de acesso atual
accessgroup.access.instructions=O seu grupo de acesso atualmente atribuÃ­do pode ser atualizado, bem como qualquer um dos seus subgrupos. Novos subgrupos de seu grupo de acesso tambÃ©m podem ser adicionados. Se nÃ£o houver dados vinculados, tambÃ©m pode excluir subgrupos de nÃ­vel inferior.

locationgroups.title=LocalizaÃ§Ã£o de Grupos
locationgroup.instructions=LocalizaÃ§Ã£o dos grupos sÃ£o usados para dados de localizaÃ§Ã£o do grupo numa autoridade.
locationgroups.title.current=LocalizaÃ§Ã£o de grupos atuais

## Advanced Search
search.advanced.title=Pesquisa AvanÃ§ada
search.advanced.header=Pesquisa AvanÃ§ada
search.advanced.instructions=Insira alguns dados de pesquisa completos nos vÃ¡rios campos abaixo e clique no botÃ£o Procurar para visualizar os resultados.
search.advanced.results.header=Resultados de Pesquisa AvanÃ§ada
search.meter.number=NÃºmero do contador
search.meter.model=Modelo do contador
search.meter.no.usage.point=Contador sem ponto de medida
search.type=Tipo de pesquisa
search.type.agreement=Acordo Tipo de Pesquisa
search.startswith=comeÃ§a com
search.contains=contÃ©m
search.customer.name=Nome
search.customer.surname=Apelido
search.customer.title=tÃ­tulo
search.customer.agreement=acordo
search.customer.agreement.ref=referÃªncia
search.customer.no.usage.point=Clientes sem ponto de medida
search.account=conta
search.account.name=Nome
search.usagepoint.name=Nome
search.meter=Pesquisa do contador
search.customer=Pesquisa Cliente
search.usagepoint=Pesquisa de ponto de medida
search.usage.point.no.customer=Clientes sem ponto de medida
search.usage.point.no.meter=Sem contador nos pontos de medida
search.error.no.criteria=Nenhum teste vÃ¡lido de pesquisa foi lanÃ§ado.
search.no.results=Nenhum resultado de pesquisa correspondente foi encontrado.
search.meter.result=contador
search.meter.model.result=Modelo do contador
search.customer.result=cliente
search.usagepoint.result=Ponto de medida
search.pricingStructure.result=Estrutura de preÃ§os
search.paymentMode.result=Modo de pagamento
search.name=Nome
search.no.meter=Nenhum contador correspondente foi encontrado. Realizando a pesquisa usando o nÃºmero parcial do contador: {0}.

meterreadings.title=Leituras do contador
meterreadings.chart.title.single=Leituras do contador
meterreadings.chart.title.balancing=BalanÃ§o energÃ©tico
meterreadings.header.graph=grÃ¡fico
meterreadings.title.graph=GrÃ¡fico de leitura do contador
meterreadings.error.none=Comece a digitar um nÃºmero de contador e selecione o contador correspondente da lista.
meterreadings.error.none.selected=Selecione um contador vÃ¡lido da lista de contadores disponÃ­veis.
meterreadings.start=Data de inÃ­cio
meterreadings.end=Data de Fim
meterreadings.error.dates=InÃ­cio deve ser anterior Ã­ data de tÃ©rmino.
meterreadings.error.type=Selecione um tipo de leitura.
meterreadings.type=Tipo de leitura
meterreadings.error.start=Ã necessÃ¡ria Data de inÃ­cio.
meterreadings.error.start.format=A data de inÃ­cio deve coincidir com {0}.
meterreadings.error.end=Ã necessÃ¡rio Data de TÃ©rmino .
meterreadings.error.end.format=Data de tÃ©rmino deve coincidir com {0}.
meterreadings.noreadings=Nenhuma das leituras dos contadores correspondentes foram encontradas.
meterreadings.chart.title=Leituras
meterreadings.chart.subtitle=Uso de kWh
meterreadings.chart.ytitle=kWh
meterreadings.chart.xtitle=Data e Hora
meterreadings.series.name=kWh
meterreadings.graph.type=Tipo de grÃ¡fico
meterreadings.type.graph.single=Contador Simples
meterreadings.type.graph.balancing=BalanÃ§o energÃ©tico
meterreadings.error.type.reading.unknown=O tipo de leitura Ã© desconhecido.
meterreadings.balancing=Contador totalizador
meterreadings.error.balancing=Ã necessÃ¡rio Contador totalizador.
#meterreadings.error.type.graph=Ã necessÃ¡rio tipo de grÃ¡fico.
meterreadings.error.type.graph=Tipo de grÃ¡fico Desconhecido.
meterreadings.meter.super=Leituras do contador totalizador
meterreadings.meter.totals=Total de sub-contador
meterreadings.meter.field.super=Contador totalizador
meterreadings.meter.field.subs=Sub-contador
meterreadings.header.table=relatÃ³rio
meterreadings.title.table=RelatÃ³rio de leitura do contador
meterreadings.table.meter=contador
meterreadings.table.reading=leitura
meterreadings.table.start=Data de inÃ­cio
meterreadings.table.end=dados de fim
meterreadings.report.type=Tipo de relatÃ³rio
meterreadings.label.supermeter.total=Contador totalizador
meterreadings.label.singlemeters.total=Subcontador
meterreadings.report.results=Leituras de contador

energybalancing.header=BalanÃ§o energÃ©tico
energybalancing.title=BalanÃ§o energÃ©tico
energybalancing.start=Data de InÃ­cio
energybalancing.end=Data de Fim
energybalancing.variation=variaÃ§Ã£o
energybalancing.error.start=Data de inÃ­cio Ã© necessÃ¡ria.
energybalancing.error.end=Data de fim Ã© necessÃ¡ria.
energybalancing.error.dates=A data de inÃ­cio deve ser anterior Ã­ data de tÃ©rmino.
energybalancing.error.percent=A diferenÃ§a deve ser um valor percentual positivo.
energybalancing.error.readingtype=Ã necessÃ¡rio o tipo de Leitura de contador.
energybalancing.supermeter=Contador totalizador
energybalancing.supermeter.reading=Total de contadores totalizadores
energybalancing.submeters.total=Total de subcontadores
energybalancing.none=nÃ£o foi encontrada qualquer leitura de balanÃ§o energÃ©tico;
energybalancing.view.graph=Ver grÃ¡fico

energybalancing.meter.header=Contadores Totalizadores de Energia
energybalancing.meter.title=Contadores Totalizadores de Energia
energybalancing.meter.super=NÃºmero do contador totalizador
energybalancing.meter.sub=NÃºmero do sub-contador
energybalancing.meter.subs=Subcontador selecionado
energybalancing.meter.super.help=Digite um nÃºmero de contador e selecione o contador correspondente.
energybalancing.meter.sub.help=Digite um nÃºmero de contador e selecione o contador correspondente. Em seguida, clique no botÃ£o> para selecionÃ¡-lo como um sub-contador.
energybalancing.meter.subs.help=Todos os contadores selecionados serÃ£o salvos como subcontadores do contador totalizador.
energybalancing.meter.instructions=Selecione um contador totalizador. Pode depois selecionar e adicionar ou remover outros sub-contadores.
energybalancing.error.super=Ã necessÃ¡rio selecionar um contador totalizador.
energybalancing.error.sub.selected=Ã necessÃ¡rio selecionar sub-contadores vÃ¡lidos.
energybalancing.error.super.id=ID do contador totalizador invÃ¡lido.
energybalancing.error.sub.ids=ID de sub-contador invÃ¡lido.
energybalancing.error.save=nÃ£o foi possÃ­vel salvar o sub-contador / contador totalizador.
energybalancing.meter.error.no.sub=Selecione primeiro um sub-contador.
energybalancing.meter.error.same.meter=subcontador nÃ£o pode ser o contador totalizador.
energybalancing.meter.save=Contadores Totalizadores e Subcontadores salvos.
energybalancing.confirm.delete=Tem certeza de que deseja excluir este contador totalizador e todos os seus subcontadores?
energybalancing.meter.deleted=Contadores totalizadores foram desligados.
energybalancing.error.delete.none=nÃ£o existe nenhum contador totalizador para o contador especificado.

meterrecharge.chart.title=Recargas de contadores
meterrecharge.chart.subtitle=valores de recarga
meterrecharge.chart.xtitle=Data Hora
meterrecharge.chart.ytitle=custo
meterrecharge.chart.price=PreÃ§o
meterrecharge.chart.purchaseprice=PreÃ§o de compra

# Manufacturer 
meter.manufacturers=fabricantes de contadores
meter.manufacturers.title=Fabricantes atuais de contadores
meter.manufacturers.title.add=Adicionar fabricante de contadores
meter.manufacturers.title.update=AtualizaÃ§Ã£o do fabricante de contadores
meter.manufacturer.name=Fabricante
meter.manufacturers.field.name=Nome
meter.manufacturers.field.description=DescriÃ§Ã£o
meter.manufacturers.field.active=Ativo
meter.manufacturers.field.status=estado
meter.manufacturers.field.name.help=Nome exclusivo do fabricante.
meter.manufacturers.field.description.help=Uma descriÃ§Ã£o do fabricante.
meter.manufacturers.field.active.help=Apenas os fabricantes ativos podem ser usados.
meter.manufacturer.name.duplicate=Nome do fabricante duplicado: {0}.

# Mdc (Meter Data Collector) 
meter.mdc=Reconhecimentos de dados do contador (MDC)
meter.mdc.title= Recolhas de dados de contadores atuais
meter.mdc.title.add=Adicionar Recolhas de Dados de contadores
meter.mdc.title.update=AtualizaÃ§Ã£o de Recolhas de Dados de Contadores
meter.mdc.name=Recolha de dados do contador (MDC)
meter.mdc.field.name=Nome
meter.mdc.field.description=DescriÃ§Ã£o
meter.mdc.field.active=Ativo
meter.mdc.field.status=estado
meter.mdc.field.value=Valor
meter.mdc.field.name.help=Nome exclusivo do MDC.
meter.mdc.field.description.help=A descriÃ§Ã£o do MDC.
meter.mdc.field.value.help=Usados em mensagens de comunicaÃ§Ã£o com o MDC.
meter.mdc.field.active.help=Apenas o MDC ativo pode ser usado.
meter.mdc.name.duplicate=Nome duplicado para um MDC: {0}.
meter.mdc.value.duplicate=Valor duplicado para um MDC: {0}.
mdc.txn.messages=Mensagens MDC
mdc.txn.message=Detalhes da mensagem MDC - Arraste Aqui
mdc.txn.messages.description=Mensagens MDC para este contador
mdc.txn.ref=ReferÃªncia
mdc.txn.meter=contador
mdc.txn.usagepoint=Ponto de medida
mdc.txn.customer=cliente
mdc.txn.reqreceived=Pedido recebido
mdc.txn.reqsent=pedido enviado
mdc.txn.reqtype=Tipo de SolicitaÃ§Ã£o
mdc.txn.controltype=Tipo de controle
mdc.txn.cmdaccrec=Comando aceite
mdc.txn.params=ParÃ¢metros
mdc.txn.recdate=Resposta recebida
mdc.txn.repcount=repetir
mdc.txn.status=estado
mdc.txn.timecompld=Tempo ConcluÃ­do
mdc.txn.client=cliente
mdc.txn.term=terminal
mdc.txn.refreceived=Ref recebido
mdc.txn.identifier=identificador
mdc.txn.identifiertype=Tipo de identificador
mdc.txn.rescodereceived=CÃ³digo de Resposta
mdc.txn.scheduledate=data agendada
mdc.txn.success=sucesso
mdc.txn.pending=pendente
mdc.txn.discarded=Rejeitado
mdc.txn.successful=sucesso
mdc.txn.failed=Falha
mdc.txn.popup.label=dados
mdc.txn.popup.value=Valor
mdc.txn.send.message.title=Enviar Mensagem MDC
mdc.button.viewchannels=Ver canais
mdc.error.noneselected=NÃ£o existe nenhum MDC selecionado
mdc.txn.connect=Conectar
mdc.txn.disconnect=Desconectar
mdc.txn.disconnect.enable=Permitir_DesconexÃ£o

# Mdc Channel
channel.header=Canais
channel.title=Canais MDC atuais
channel.value.duplicate=Valor duplicado para um canal MDC: {0}
channel.field.titlename=Canal MDC
channel.field.value=Valor
channel.field.name=Nome
channel.field.billingdet=Determinante de cobranÃ§a
channel.field.status=estado
channel.title.add=Adicionar canal MDC
channel.title.update=Atualizar canal MDC
channel.field.value.help=Insira um identificador de canal.
channel.field.descrip=DescriÃ§Ã£o
channel.field.billingdetname=Determinante de cobranÃ§a
channel.field.billingdetname.channel.help=Selecione o determinante de cobranÃ§a. Pode estar em branco se as leituras do canal nÃ£o forem usadas para cobranÃ§a.
channel.field.meter.reading.type=Tipo de leitura do contador
channel.field.maxsize=Tamanho mÃ¡x. leitura
channel.field.active.help=Estado actual da actividade neste canal
channel.field.active=Ativo
channel.billingdet.confirm=NÃ£o selecionou um determinante de cobranÃ§a o que implica que este canal nÃ£o Ã© utilizado para faturaÃ§Ã£o.Continuar?

#Mdc Channel Initial Readings
channel.readings.header= Atribuir canais adicionais de leitura para o contador:
channel.readings.meter.model=Modelo de contador:
channel.readings.mdc=CDM:
channel.readings.pricing.structure=Estrutura de preÃ§os:
channel.readings.installdate=Dados de instalaÃ§Ã£o:
channel.readings.table.heading= Leitura Inicial
channel.readings.reading.error=Canal de Erro {0} : A leitura deve ser um valor numÃ©rico positivo igual ou inferior ao tamanho mÃ¡ximo {1}

# Billing Determinante
billingdet.tab.label=Det CobranÃ§a
billingdet.header=Determinantes de cobranÃ§a
billingdet.title=Determinantes de cobranÃ§a atuais
billingdet.name=Determinantes de cobranÃ§a
billingdet.title.add=Adicionar determinantes de cobranÃ§a
billingdet.title.update=Atualizar determinantes de cobranÃ§a
billingdet.field.name=Nome
billingdet.field.name.help=Nome de Determinante de CobranÃ§a
billingdet.field.description=DescriÃ§Ã£o
billingdet.field.description.help=DescriÃ§Ã£o dos determinantes de cobranÃ§a
billingdet.active=Ativo
billingdet.active.help=Selecione para ativar, deve estar ativo para ser usado.
billingdet.error.save=NÃ£o foi possÃ­vel salvar o determinante de cobranÃ§a.
billingdet.error.update=NÃ£o foi possÃ­vel atualizar o determinante de cobranÃ§a.

# Meter Model
meter.models=Modelos de contadores
meter.models.title=Modelos Actuais de Contadores
meter.models.name=Modelo de contador
meter.models.field.manufacturer=Fabricante
meter.models.field.manufacturer.help=selecione o fabricante atual para o modelo de contador.
meter.models.field.name=Nome
meter.models.field.description=DescriÃ§Ã£o
meter.models.field.active=Ativo
meter.models.field.status=estado
meter.models.field.name.help=Nome exclusivo do modelo de contador.
meter.models.field.description.help=A descriÃ§Ã£o do modelo do contador.
meter.models.field.active.help=Apenas modelos de contadores ativos podem ser usados.
meter.models.field.resource=Recurso de serviÃ§o
meter.models.field.resource.help=Selecione o recurso de serviÃ§o correto para o modelo de contador.
meter.models.field.metertype=Tipo de Contador
meter.models.field.metertype.help=Selecione o tipo de contador correto para o modelo de contador.
meter.models.field.toa=Suporta o envio de talÃµes pela rede.
meter.models.field.toa.help=Isto indica se este modelo suporta o envio de talÃµes, como os STS, diretamente atravÃ©s de uma rede.
meter.models.field.mdc=Recolha de dados de contadores (MDC)
meter.models.field.mdc.help=Associar este modelo de contador a um MDC.
meter.models.field.paymentmodes=modos de pagamento
meter.models.field.paymentmodes.help=Use a tecla Ctrl para selecionar vÃ¡rios modos de pagamento para o modelo de contador.
meter.models.title.add=Adicionar modelo de contador
meter.models.title.update=AtualizaÃ§Ã£o do modelo de contador
meter.models.paymentmodes.required=Pelo menos um modo de pagamento Ã© exigido.
meter.models.name.duplicate=Nome duplicado para o modelo do contador: {0}.
meter.models.field.balance.sync=Suporta sincronizaÃ§Ã£o de balanÃ§o.
meter.models.field.balance.sync.help=Isto indica que este modelo de contador pode suportar uma sincronizaÃ§Ã£o de balanÃ§o.
meter.models.field.needs.breaker.id=Precisa de ID de disjuntor
meter.models.field.needs.breaker.id.help=Indica se o contador precisa de um ID de disjuntor.
meter.model.unset.breaker.id.error=NÃ£o foi possÃ­vel alterar o requerimento de ID de disjuntor - JÃ¡ existem contadores com ID's de disjuntores a utilizar este modelo de contador.

ptr.serviceresource=recurso de serviÃ§o
ptr.metertype=Tipo de contador
ptr.paymentmode=Modo de pagamento

tou.thin.field.calendar=calendÃ¡rio
tou.thin.field.calendar.help=Selecione o calendÃ¡rio relevante para a tarifa.
tou.thin.field.monthlydemand=ServiÃ§o de cobranÃ§as suspensas
tou.thin.field.monthlydemandtype=Tipo de Leitura do serviÃ§o de cobranÃ§as laterais.
tou.thin.field.monthlydemandtype.help=Especifique o tipo de leitura do serviÃ§o de cobranÃ§as suspensas.
tou.thin.field.servicecharge=serviÃ§o de cobranÃ§a diÃ¡ria
tou.thin.field.servicecharge.descrip=DescriÃ§Ã£o
tou.thin.field.servicecharge.descrip.help=Introduza uma descriÃ§Ã£o para a cobranÃ§a.
tou.thin.field.servicechargecycle= Ciclo do serviÃ§o de cobranÃ§a
tou.thin.field.servicechargecycle.help=Quando o serviÃ§o de cobranÃ§a deve ser aplicado.
tou.thin.field.enablereadingtypes=Ativar Tipos de ServiÃ§os de Leitura no Contador
tou.thin.field.enablereadingtypes.help=selecione os tipos de leitura que devem ser aplicados para esta tarifa.
tou.thin.charges.button=CobranÃ§as recolhidas
tou.thin.field.charges=CobranÃ§as
tou.thin.field.charges.specialday=CobranÃ§as de Dia Especial
tou.thin.change.calendar=Tem certeza de que deseja descartar todas as suas despesas correntes e definir um novo calendÃ¡rio?
tou.thin.error.no.calendar=Selecione um calendÃ¡rio.
tou.thin.error.no.types=Selecione pelo menos um tipo.
tou.thin.error.no.tax=Ã necessÃ¡rio Imposto.
tou.thin.monthlydemandtype.required=Ã necessÃ¡rio definir o Tipo de leitura.
tou.thin.monthlydemand.required=Ã necessÃ¡ria cobranÃ§a.
tou.thin.monthlydemand.positive=A cobranÃ§a deve ter um valor positivo.
tou.thin.monthlydemand.invalid=A cobranÃ§a deve ter um valor numÃ©rico vÃ¡lido.
tou.thin.cycle.required=Ã necessÃ¡rio um ciclo.
tou.thin.servicecharge.invalid=A taxa de serviÃ§o deve ser um valor numÃ©rico vÃ¡lido.
tou.thin.tax.invalid=O Imposto deve ser um valor numÃ©rico vÃ¡lido.
tou.thin.servicecharge.required=Ã necessÃ¡ria a taxa de serviÃ§o.
tou.thin.servicecharge.positive=A taxa de serviÃ§o deve ser um valor positivo.
tou.thin.no.tariff.data.available=nÃ£o hÃ¡ dados tarifÃ¡rios disponÃ­veis para serem atualizados e salvos.
tou.thin.charges.none=As cobranÃ§as precisam ser recolhidas para a tarifa.
tou.thin.charges.invalid=As cobranÃ§as devem ter valores numÃ©ricos positivos e vÃ¡lidos.
tou.thin.specialdayscharges.none=As cobranÃ§as para dias especiais devem ser capturadas para a tarifa.
tou.thin.specialdayscharges.invalid=As cobranÃ§as para Dias Especiais devem ser valores numÃ©ricos positivos e vÃ¡lidos.
tou.thin.rates.none=NÃ£o foram atribuÃ­das taxas
tou.thin.rates.invalid=As taxas devem ser positivas e nÃ£o zero

# Register Reading Tariffs
register.reading.billing.determinant.title=Determinantes de cobranÃ§a
register.reading.billing.determinant.help=Selecione os determinantes de cobranÃ§a para os quais serÃ£o capturadas taxas para a estrutura de preÃ§os
register.reading.rates.button=Capturar Taxas
register.reading.rates.title=impostos
register.reading.billing.determinant.column.title=Determinante de cobranÃ§a
register.reading.error.no.tax=Ã necessÃ¡rio imposto.
register.reading.rates.none= NÃ£o foram atribuÃ­das taxas
register.reading.rates.invalid=As taxas devem ser positivas e nÃ£o zero
register.reading.error.no.billingdet=Selecione pelo menso um determinante de cobranÃ§a
register.reading.no.tariff.data.available=Sem dados de tarifa disponÃ­veis para atualizar e salvar
register.reading.billing.det.change=Os determinantes de cobranÃ§a na lista selecionada nÃ£o sÃ£o os mesmos que os encargos para esta tarifa. Os encargos anteriores vÃ£o ser desativados. Todos os encargos devem ser reintroduzidos. Continuar?
register.reading.servicecharge.descrip.required=Ã necessÃ¡ria descriÃ§Ã£o de encargo
register.reading.cannot.change.charge=Tarifa activa, nÃ£o Ã© possÃ­vel alterar taxas.

# Register Reading Information tab
register.reading.txn.label=registros de leitura
register.reading.txn.description=Registros de leitura para este contador para o perÃ­odo selecionado
register.reading.txn.timestamp=Selo de tempo
register.reading.txn.channel=Canal
register.reading.txn.determinant=determinante
register.reading.txn.readingtype=Tipo de leitura do contador
register.reading.txn.readingvalue=Valor
register.reading.txn.error.enddate=A data final de ser igual ou superior Ã  data de inicio.
register.reading.txn.filter=Filtro
register.reading.txn.noreadings=NÃ£o foram encontrados registos de leitura correspondentes.

# AppSetting 
appsetting.header=DefiniÃ§Ãµes da aplicaÃ§Ã£o
appsetting.title=DefiniÃ§Ãµes atuais da aplicaÃ§Ã£o
appsetting.title.update=Atualizar definiÃ§Ãµes de aplicaÃ§Ã£o
appsetting.name=DefiniÃ§Ã£o da AplicaÃ§Ã£o
appsetting.field.name=Nome
appsetting.field.value=valor
appsetting.field.description=DescriÃ§Ã£o
appsetting.field.name.help=DefiniÃ§Ã£o do nome da aplicaÃ§Ã£o. Pode ser editado para fins de traduÃ§Ã£o personalizada. Para campos personalizados, recomendamos que vocÃª mantenha as palavras 'RÃ³tulo' ou 'Status' como parte do nome para facilitar a leitura...
appsetting.field.value.help=O valor a ser aplicado para esta definiÃ§Ã£o. Para rÃ³tulos de campos personalizados, insira o rÃ³tulo que deseja ver no componente de entrada desse campo.
appsetting.field.description.help=DescriÃ§Ã£o da configuraÃ§Ã£o da aplicaÃ§Ã£o.
appsetting.name.duplicate=Nome duplicado para a configuraÃ§Ã£o da aplicaÃ§Ã£o: {0}.
appsetting.error.new=ConfiguraÃ§Ã£o de aplicaÃ§Ã£o {0} nÃ£o foi encontrada. Por favor, contate o seu administrador de Sistema.
appsetting.error.disconnect.greater.emergency.credit=CrÃ©dito de EmergÃªncia deve ser maior ou igual para reconectar.
appsetting.error.disconnect.greater.reconnect=A conexÃ£o deve ser maior ou igual para a desconexÃ£o.
appsetting.error.disconnect.greater.both=DesconexÃ£o deve ser igual ou inferior para reconectar E igual ou inferior para o crÃ©dito de emergÃªncia.
appsetting.error.maxvend.smaller.minvend=O montante mÃ¡ximo de venda deve ser maior ou igual ao montante mÃ­nimo de venda.
appsetting.error.emergency.credit.greater.low.balance=O crÃ©dito de emergÃªncia deve ser igual ou inferior ao BalanÃ§o baixo.
appsetting.error.emergency.credit.errors=O crÃ©dito de emergÃªncia deve ser maior ou igual para desconectar E menor ou igual para o balanÃ§o baixo.
appsetting.error.invalid.custom.status=Estado de campo personalizÃ¡vel invÃ¡lido! Deve ser OPCIONAL, REQUERIDO ou INDISPONIVEL.

demo.addmeterreadings.title.criteria=CritÃ©rios de leitura do contador
demo.addmeterreadings.interval=Intervalo de leitura do contador
demo.addmeterreadings.readingtypes=Tipos de serviÃ§o ao contador
demo.addmeterreadings.tariffcalc=Enviar cÃ¡lculo da tarifa pedido apÃ³s leituras
demo.addmeterreadings.error.paymentmode=Ã necessÃ¡rio definir Modo de pagamento.
demo.addmeterreadings.error.meter=Digite um nÃºmero de contador e selecione-o da lista disponÃ­vel.
demo.addmeterreadings.error.meter.select=Por favor, selecione um contador a partir da seleÃ§Ã£o disponÃ­vel.
demo.addmeterreadings.no.usagepoint1=O contador nÃ£o tem nenhum ponto de medida.
demo.addmeterreadings.no.usagepoint2=Pedido de cÃ¡lculo de tarifa recusado.
demo.addmeterreadings.no.usagepoint3=NÃ£o foi possÃ­vel ler ponto de medida para o contador. Por favor informe o suporte.
demo.addmeterreadings.error.start=Ã necessÃ¡rio iniciar.
demo.addmeterreadings.error.end=Ã necessÃ¡rio Finalizar.
demo.addmeterreadings.error.dates=Iniciar deve ser antes de Finalizar.
demo.addmeterreadings.error.end.future=Finalizar deve ser antes de hoje.
demo.addmeterreadings.error.interval=Ã necessÃ¡ria leitura de intervalos.
demo.addmeterreadings.error.types=Ã necessÃ¡rio pelo menos um tipo de leitura.
demo.addmeterreadings.invalid.input=Entrada invÃ¡lida para adicionar leituras do contador.
demo.addmeterreadings.error.insert=Erro ao inserir a leitura do contador.
demo.addmeterreadings.error.insert.fact=Erro ao inserir o valor da leitura.
demo.addmeterreadings.error.insert.timeDim=Erro ao inserir um TimeDim para um novo valor de leitura .
demo.addmeterreadings.error.duplicates=Contador tem leituras existentes para o perÃ­odo atual. Altere o intervalo de dados ou selecione para desligar as leituras do contador existente.
demo.addmeterreadings.error.duplicate.Facts=O contador tem valores de leitura existentes para o perÃ­odo atual. Alterar o intervalo de dados ou selecionar para desligar as leituras do contador existentes.
demo.addmeterreadings.minutes.15=15 minutos
demo.addmeterreadings.minutes.30=30 minutos
demo.addmeterreadings.minutes.60=60 minutos

demo.addsupermeterreadings.link=[DEMO] Adicionando leituras do contador totalizador
demo.addsupermeterreadings.header=Adicionar leituras do contador totalizador
demo.addsupermeterreadings.title=Leituras do contador totalizador
demo.addsupermeterreadings.title.criteria=CritÃ©rio de leituras do contador totalizador
demo.addsupermeterreadings.readingtype=Tipo de leitura do contador
demo.addsupermeterreadings.variation=VariaÃ§Ã£o de leitura do contador totalizador
demo.addsupermeterreadings.variations=VariaÃ§Ãµes do contador totalizador
demo.addsupermeterreadings.variations.help=As leituras do contador totalizador devem corresponder ao total dos seus subcontadores. Podem ser horas temporÃ¡rias especificadas e uma taxa que pode ser usada para reduzir as leituras do contador totalizador, para que defira seus sub-contadores.
demo.addsupermeterreadings.hour=hora
demo.addsupermeterreadings.supermeter=Contador Totalizador
demo.addsupermeterreadings.super.delete.readings=Apagar leituras do contador totalizador
demo.addsupermeterreadings.subs.regen.readings=Regenerar leituras de sub-contadores
demo.addsupermeterreadings.error.supermeter=Ã necessÃ¡rio contador totalizador.
demo.addsupermeterreadings.error.type=Ã necessÃ¡rio Tipo de leitura do contador.
demo.addsupermeterreadings.hour.required=Ã hora necessÃ¡ria do dia.
demo.addsupermeterreadings.percentage.required=Ã necessÃ¡ria Percentagem.
demo.addsupermeterreadings.variation.duplicate=Esta hora jÃ¡ estÃ¡ em uso noutra variaÃ§Ã£o existente.
demo.addsupermeterreadings.invalid.input=Entrada invÃ¡lida para gerar leituras do contador totalizador.
demo.addsupermeterreadings.invalid.input.submeters=nÃ£o estÃ£o disponÃ­veis sub-contadores para o contador totalizador.
demo.addsupermeterreadings.success=leitura do contador totalizador foi adicionada com sucesso.
demo.addsupermeterreadings.error.super.duplicates=O Contador Totalizador tem leituras para o perÃ­odo atual. Deve-se alterar o perÃ­odo ou apagar as leituras atuais.
demo.addsupermeterreadings.error.sub.duplicates=JÃ¡ existem leituras para o periodo atual. Deve-se alterar o intervalo de dados ou regenerar as leituras para o contador.

export.error=Erro: nÃ£o foi possÃ­vel exportar os dados.
export.error.nodata=Erro: nÃ£o hÃ¡ dados disponÃ­veis para serem exportados.
export.error.nofile=Erro: O nome do arquivo nÃ£o pode ser determinado.
export.field.meter=contador
export.field.metertype=Tipo de Contador
export.field.date=Dados de leituras
export.field.start=Hora de inÃ­cio
export.field.end=hora do fim
export.field.reading=Leituras
export.denied=Acesso negado. Precisa de estar conectado para acessar esta funcionalidade.
export.denied.group=Acesso negado. nÃ£o Pertence ao grupo do contador.
export.singlemeter.filename.prefix=Leituras do contador-contador
export.energybalancing.filename.prefix=BalanÃ§o de energia - Contador Totalizador

taskschedule.title.add=Adicionar um agendador de tarefas
taskschedule.title.update=Atualizar um agendador de Tarefa
taskschedule.header=Agendador de tarefas atuais
taskschedule.title=Agendador de tarefas
taskschedule.title.single=Agendador de tarefas
taskschedule.type=Agendar tarefas
taskschedule.name=Nome da tarefa
taskschedule.name.help=Especifique um nome para identificar o seu agendador de tarefas.
taskschedule.active=Ativo
taskschedule.active.help=Se o agendamento de tarefas estÃ¡ ativo e serÃ¡ executado como previsto.
taskschedule.schedule=Agenda Hora / Tempo
taskschedule.schedule.help=Especificar quando a programaÃ§Ã£o da tarefa serÃ¡: executada, selecionando uma opÃ§Ã£o apropriada e completando os campos.
taskschedule.status=estado
taskschedule.scheduledtask=Tipo de tarefa
taskschedule.schedule.daily=Uma vez por dia
taskschedule.schedule.weekly=Uma vez por semana
taskschedule.schedule.monthly=Uma vez por mÃªs
taskschedule.schedule.repeatedly=Repetindo por minutos / horas
taskschedule.hours=Horas
taskschedule.minutes=minutos
taskschedule.time=Tempo
taskschedule.day=Dia da Semana
taskschedule.daymonth=Dia do MÃªs
taskschedule.days=domingo, segunda-feira, terÃ§a-feira, quarta-feira, quinta-feira, sexta-feira, sÃ©bado
taskschedule.error.time=O tempo Ã© um campo obrigatÃ³rio.
taskschedule.error.day=Dia Ã© um campo obrigatÃ³rio.
taskschedule.error.daymonth=Dia Ã© um campo obrigatÃ³rio.
taskschedule.users=UsuÃ¡rios da tarefa
taskschedule.error.no.user=Procurar primeiro por um usuÃ¡rio e depois adicionÃ£Â¡-lo como usuÃ¡rio da tarefa.
taskschedule.error.taskusers=Pelo menos um usuÃ¡rio da tarefa ou um cliente Ã© necessÃ¡rio.
taskschedule.error.customer.notselected=Nome do cliente nÃ£o coincide com o nome do cliente selecionado. Digite um apelido para selecionar um cliente vÃ¡lido.
taskschedule.user.noemail=O usuÃ¡rio nÃ£o tem um endereÃ§o de e-mail vÃ¡lido. Os usuÃ¡rios de tarefas precisam ter um endereÃ§o de e-mail vÃ¡lido para receber tarefas agendadas por e-mails.
taskschedule.meter.single=Contador Simples
taskschedule.meter.super=Contador totalizador
taskschedule.meter.readingtype=Tipo de leitura do contador
taskschedule.timeperiods=Periodo de tempo
taskschedule.class.error.singlemeter.none=Ã necessÃ¡rio um contador simples.
taskschedule.class.error.singlemeter.select=Comece a digitar um nÃºmero de contador e selecione da lista.
taskschedule.class.error.type=Ã necessÃ¡rio um Tipo de leitura de contador.
taskschedule.class.error.time=Ã necessÃ¡rio o Periodo de tempo.
taskschedule.class.error.supermeter=Ã necessÃ¡rio Contador totalizador.
taskschedule.class.error.hours=As horas devem ser um nÃºmero positivo.
taskschedule.taskclass.previous.day=Dia Anterior
taskschedule.taskclass.previous.week=Semana anterior
taskschedule.taskclass.previous.month=MÃªs anterior
taskschedule.error.taskschedule.save=Erro ao salvar o agendamento da tarefa.
taskschedule.error.taskschedule.none=Sem agendamento de tarefas especificadas.
taskschedule.error.scheduledtask.save=Erro ao guardar a tarefa agendada.
taskschedule.error.scheduledtaskuser.delete=Erro ao eliminar o UsuÃ¡rio para tarefas agendadas.
taskschedule.error.scheduledtaskuser.save=Erro ao salvar o UsuÃ¡rio para tarefas agendadas.
taskschedule.error.scheduling=Erro ao agendar a tarefa.
taskschedule.error.descheduling=Erro na desindexaÃ§Ã£o da tarefa existente.
taskschedule.every=Cada
taskschedule.of=NÃºmero de
taskschedule.repeatedly.error.number=Ã necessÃ¡rio um nÃºmero positivo.
taskschedule.repeatedly.error.units=Selecione um tipo de intervalo.
taskschedule.all.supermeters=Todos os contadores Totalizadores

scheduledtask.title=Tarefas agendadas
scheduledtask.header=Tarefas agendadas atualmente
scheduledtask.type=Tarefa agendada
scheduledtask.field.name=Nome da Tarefa
scheduledtask.field.name.help=Especifique um nome para identificar a sua agenda de tarefas.
scheduledtask.field.class=Tipo de tarefa
scheduledtask.field.class.help=Selecione o tipo de tarefa a ser executada na data e hora programada.
scheduledtask.title.add=Adicionar uma tarefa agendada
scheduledtask.title.update=Atualizar uma tarefa agendada
scheduledtask.previous.hours=Horas anteriores
scheduledtask.customer.name=cliente
scheduledtask.customer.name.help=Digite o apelido de um cliente e selecione o cliente correspondente, que deve estar ligado a esta tarefa agendada.
scheduledtask.error.delete.none.selected=nÃ£o foi selecionada qualquer tarefa agendada.
scheduledtask.delete.confirm=Tem a certeza de que deseja excluir a tarefa agendada?
scheduledtask.error.delete.none=nÃ£o foi especificada qualquer tarefa agendada.
scheduledtask.error.delete=nÃ£o foi possÃ­vel apagar a tarefa agendada.
scheduledtask.deleted=A tarefa agendada foi excluÃ­da com sucesso.

scheduledtask.email.subject.taskschedule=GestÃ£o de Contadores - Agendar tarefas:
scheduledtask.email.subject.scheduledtask=\ u0020 Tarefa: \ u0020
scheduledtask.email.message.taskschedule=Agendar tarefas: \ u0020
scheduledtask.email.message.scheduledtask=Tarefa: \u0020
scheduledtask.email.message.meter=Contador: \u0020
scheduledtask.email.message.start=Data de inÃ­cio: \ u0020
scheduledtask.email.message.end=Dados do Termo: \ u0020
scheduledtask.email.message=Segue em anexo a saÃ£da de sua tarefa.
scheduledtask.email.message.supermeter=Contador totalizador: \ u0020

password.change.header=Mudar Senha
password.old=Senha atual
password.new=nova senha
password.confirm=confirmar nova senha
password.old.required=Ã necessÃ¡ria Senha real.
password.new.required=Ã necessÃ¡ria Nova senha.
password.confirm.required=Ã necessÃ¡rio confirmar nova senha.
password.new.nonmatching=A nova senha e a confirmaÃ§Ã£o da nova senha nÃ£o coincidem.
password.changed=A senha foi alterada com sucesso.
password.expiry=A sua senha irÃ¡ expirar em {0} dia(s).
password.ldap=O seu usuÃ¡rio Ã© autenticado atravÃ©s do logo LDAP nÃ£o Ã© possÃ­vel alterar a senha.
password.login.expired=Deve alterar a sua senha, pois a mesma expirou.
password.login.reset=Deve alterar a sua senha, porque foi reiniciada.
password.user.change=Deve alterar sua senha antes de poder usar a aplicaÃ§Ã£o.
password.locked=A sua conta foi bloqueada.
password.validate.current=A senha atual estÃ¡ incorreta.
password.validation.minUppercase=senha deve ter pelo menos {0} caractere(s) maiÃºsculo(s).
password.validation.minDigits=A senha deve ter pelo menos {0} dÃ­gitos.
password.validation.minLength=A senha deve ter um comprimento mÃ­nimo de {0} dÃ­gitos.
password.validation.maxLength=A senha deve ter um comprimento mÃ¡ximo de {0} dÃ­gitos.
password.validation.numHistory=nÃ£o pode usar uma senha que corresponde a uma das Ãºltimas {0} senhas.
password.required=Ã necessÃ¡ria a senha.
username.required=Ã necessÃ¡rio o Nome de UsuÃ¡rio.
logged.in=Inicio de SessÃ£o.
login.session.timedout=A sua sessÃ£o realmente expira. Por favor incie novamente para continuar usando o site.

# Dashboard
dashboard.title=Painel
dashboard.meter.count.title=NÃºmero de contadores
dashboard.meter.count.description=NÃºmero atual de contadores no sistema, por modelo de contador.
dashboard.meter.model.name=Modelo do Contador
dashboard.meter.count=#

## Custom fields
user.custom.fields.title=Campos personalizÃ¡veis
user.custom.fields.error.get=Erro na base de dados ao obter as configuraÃ§Ãµes da alicaÃ§Ã£o de campos personalizÃ¡veis para {0}! Entre em contato com o Suporte.
user.custom.fields.error.unknown.setting=A configuraÃ§Ã£o da aplicaÃ§Ã£o dos campos personalizÃ¡veis {0} nÃ£o foi permitida. Informe o suporte tÃ©cnico.
user.custom.field.status.optional=OPCIONAL
user.custom.field.status.required=REQUERIDO
user.custom.field.status.unavailable=INDISPONÃVEL

#Import Data
import.data.header=importar dados
import.meters.title=Importar contadores
import.data.metermodel.help=Se um modelo de contador nÃ£o estiver especificado no arquivo a importar, serÃ¡ usado um modelo da lista
import.data.metermodel=Modelo de contador padrÃ£o
import.data.file.help=Selecione um arquivo contendo informaÃ§Ãµes do contador no formato especÃ­fico CSV para importar para o sistema.
import.data.file=selecionar Arquivo de contadores
import.data.button=Importar contadores contadores import.meters.heading=Importar contadores para o armazm
import.meters.heading=Importar contadores para o armazÃ©m
import.meters.description=Selecione o arquivo CSV que contÃ©m informaÃ§Ãµes do contador para importar para o sistema de gerenciamento dos contadores. <br/> Selecione um modelo de contador padrÃ£o a partir da lista. Este modelo serÃ¡ usado quando existir um modelo de contador nÃ£o especificado no arquivo a importar.
   
timezone.change=MudanÃ§a de fuso horÃ¡rio
timezone.header=Fuso horÃ¡rio atual
timezone.label=Fuso horÃ¡rio

##########################################################
#### IpayXml Messages for meter balance notifications ####
##########################################################
# The touThinSmart.notification messages have the following arguments:
# arg0=cliente.tÃ­tulo
# arg1=cliente.iniciais
# arg2=cliente.nomes
# arg3=cliente.sobrenome
# arg4=nÃºmero do contador
# arg5=contrato do cliente ref
# arg6=nome do ponto de medida
# arg7=saldo da conta
# arg8=limite de crÃ©dito
# arg9=limite de saldo baixo
# arg10=limite de desconexÃ£o
# arg11=limite de reconexÃ£o
# currency format example for account balance: 
defaultAccountAdjustmentProcessor.notification.disconnect.email.subject=Saldo da conta esgotou-se para {6}
defaultAccountAdjustmentProcessor.notification.disconnect.email.message=Caro cliente,\n\n O seu contador serÃ¡ desconectado.\n\n O estado da sua conta Ã©: \n Saldo de conta: {7,number,currency}\n NotificaÃ§Ã£o de Limiar de balanÃ§o baixo: {8,number ,currency}\n\n Atenciosamente,\n Equipa de Suporte
defaultAccountAdjustmentProcessor.notification.disconnect.sms.message=Saldo para{6} esgotou-se e serÃ¡ desconectado. O saldo Ã© {7,nÃºmero,moeda} Atenciosamente,\n Equipa de Suporte
defaultAccountAdjustmentProcessor.notification.lowBalance.email.subject=O saldo da conta Ã© baixo para{6}
defaultAccountAdjustmentProcessor.notification.lowBalance.email.message=Caro cliente,\n\n O estado da sua conta Ã©: \n Saldo da conta: {7,number,currency}\n NotificaÃ§Ã£o de Limiar de saldo baixo: {8,number,currency}\n\nn Atenciosamente,\ n Equipa de Suporte
defaultAccountAdjustmentProcessor.notification.lowBalance.sms.message=Saldo para {6} Ã© baixo. O saldo Ã© {7,nÃºmero,moeda} n Atenciosamente,\n Equipa de Suporte

#database actions
db.action.update=atualizado
db.action.insert=inserido
db.action.delete=apagar

#MDC controltypes
controltype.connect=CONECTAR
controltype.disconnect=DESCONECTAR
controltype.disconnect.enable=PERMITIR_DESCONEXÃ£O
controltype.pan.display=PAN_DISPLAY
controltype.sync.balance=SINC_BALANÃO
controltype.adjust.balance=AJUST_BALANÃO
controltype.power.limit=LIMITE_POTÃN
