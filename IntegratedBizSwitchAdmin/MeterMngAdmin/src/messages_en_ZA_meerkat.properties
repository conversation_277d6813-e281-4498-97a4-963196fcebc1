### !!! CAUTION !!!
### Before adding keys, ensure you have no duplicates. Duplicates could lead to unexpected behaviour.

# zachv: 2025-05-16 | Planio #30009
calendar.specialday.field.date=Date
calendar.specialday.field.date.help=Enter a date for the special day
calendar.specialday.field.year=Year

# jacciedt: 2025-04-25 | Planio #33761
multiUsagePointAccountAdjustmentProcessor.notification.disconnect.email.subject=Account balance has run out for your account
multiUsagePointAccountAdjustmentProcessor.notification.disconnect.email.message=Dear Customer,\n\nYour meters will be disconnected.\n\nYour account status is: \n  Account balance: {7,number,currency}\n  Low balance notification threshold: {8,number,currency}\n\nRegards,\nSupport Team
multiUsagePointAccountAdjustmentProcessor.notification.disconnect.sms.message=Balance for your account has run out and will be disconnected. Balance is {7,number,currency} Regards, Support Team
multiUsagePointAccountAdjustmentProcessor.notification.emergencyCredit.email.subject=Emergency credit threshold for your account
multiUsagePointAccountAdjustmentProcessor.notification.emergencyCredit.email.message=Dear Customer,\n\nYour account status is: \n Account balance: {7,number,currency}\n  Low balance notification threshold: {8,number}\n  Emergency credit threshold: {9,number,currency}\n\nRegards,\nSupport Team
multiUsagePointAccountAdjustmentProcessor.notification.lowBalance.email.subject=Account balance low for your account
multiUsagePointAccountAdjustmentProcessor.notification.lowBalance.email.message=Dear Customer,\n\nYour account status is: \n  Account balance: {7,number,currency}\n  Low balance notification threshold: {8,number,currency}\n\nRegards,\nSupport Team
multiUsagePointAccountAdjustmentProcessor.notification.emergencyCredit.sms.message=Balance for your account is running low and below emergency credit threshold. Balance is {7,number,currency} Regards, Support Team
multiUsagePointAccountAdjustmentProcessor.notification.lowBalance.sms.message=Balance for your account is running low. Balance is {7,number,currency} Regards, Support Team


# michalv: 2025-05-09 | Planio #32735
export.error.too.many.records=Error: Too many records to export (over {0}). Please filter on a smaller date range.

# joelc: 2025-02-06 | Planio #28349
#power.limit.edit.label.prompt=Name
#power.limit.edit.label.help=Name of the power limit value. This is text display
power.limit.edit.value.prompt=Watt Value
power.limit.edit.value.help=Watt value of the power limit. This value should be numeric
error.field.powerlimit.value.already=This power limit already exists.

# joelc: 2025-02-13 | Planio #31090
pricingstructure.suggestbox.placeholder = Start typing to filter list


#rodgersn: 2025-02-06 | Planio 29656
engineering.token.display.sgc=Supply Group Code:
engineering.token.display.krn=Key Revision Number:
engineering.token.display.ti=Tariff Index:

#renciac: 2024-08-31 | Planio 17062: MMA: get email from name & address from appsetting not from messages.properties
error.email.invalid=Invalid email address.

# christoe: 2024-07-29 | Planio #28161
import.edit.bulk.mdc.item.update.success=MDC message successfully updated.

# michalv: 2024-10-09 | Planio 25922
meter.models.mdc.required.for.thin.payment=Thin payment mode requires MDC

# christoe: 2024-10-21 | Planio #30157
online.bulk.panel.error.regex.validation1=Regex validation has been configured for UP Name, Agreement Reference or Account Name.
online.bulk.panel.error.regex.validation2=Auto-generation of these fields is disabled and requires manual entry on the Meter tab.

# Michalv: 2024-09-12 | Planio #25404
error.tab.duplicate.auxaccount=AuxAccount open in another tab. Tab: ({0})
error.tab.duplicate.custaccounttrans=Customer Transaction open in another tab. Tab: ({0})

#thomasn: 2023-12-06 | Planio 12164
access_group.lbl=Access Group
access_group.update.header=Update Access Group
access_group.update.group.error=New group has not been selected.
access_group.update.pricing.error=Customer UsagePoint(s) must have GLOBAL Pricing Structure.
access_group.update.future.pricing.clear=This will also clear any future pricing structures.
access_group.update.location.groups.clear=This will clear the usage point and customer location groups.
access_group.update.confirm.lbl=Proceed?
access_group.update.button=Update Access Group
insert.unique.error.meter=The meter details must be unique.
# Used by MMC MapperStaticProxy. e.g. CustomerMapperStaticProxy
insert.unique.error.customer=The customer details must be unique.
insert.unique.error.usagepoint=The usage point details must be unique.
insert.unique.error.devicestore=The device store details must be unique.
insert.unique.error.pricingstructure=The pricing structure details must be unique.
insert.unique.error.auxchargeschedule=The Aux charge schedule details must be unique.

#thomasn: 2023-10-11 | Planio 12164
session_auth.form.submit=Submit
session_auth.form.logout=Logout
session_auth.form.instructions=Choose session authorization details
session_auth.form.role=Role:
session_auth.form.group=Group:
session_auth.form.group_and_role=Group and Role:
session_auth.form.invalid=Login Error. Invalid Form Mode
access_group.error.group_already_cleared=The group has already been cleared on these entities.
access_group.success.updated_group=Successfully updated group
workspace.usagepoint.overview=Overview
workspace.usagepoint.actions=Actions
grouphierarchy.field.is_access_group=Access Group
grouphierarchy.field.is_access_group.help=Whether to connect this group to an access control organisation access group. This will show a list of access control groups on creating groups for this hierarchy level
grouphierarchy.field.is_access_group.error.already_assigned=An organisation access group can only be linked to one hierarchy level.
grouptype.field.location.group=Location Group
groupnode.field.access_group=Access Group
groupnode.field.access_group.help=Connects an access control organisation access group to this group which will restrict access to group users. A group user in a different group would not see this group in selections.
groupnode.field.access_group.error.no_groups=This hierarchy level has enabled organisation access groups but none have been defined.
changegroup.org_group.confirm=Changing your group and/or role selection will reload the application. Would you like to continue?
login.session.reload=You will be redirected to the login screen and the application will be reloaded.
mrid.ui=Unique ID
mrid.ui.help=Enter the unique ID
mrid.ui.external=External Unique ID?
mrid.ui.external.help=Whether unique id is from an external system

# christoe: 2024-06-21 | Planio #28160, #28158
mdc.txn.relay.title=Relay
mdc.txn.relay.help=Select which relay to connect or disconnect.
mdc.txn.relay.main=Main
mdc.txn.relay.aux.one=Auxiliary 1
mdc.txn.relay.aux.two=Auxiliary 2
mdc.txn.power.limit=Power_Limit

# michalv: 2024-06-15 | Planio #29332
error.field.email3=One or multiple email addresses are invalid

# michalv: 2024-07-18 | Planio 28459
online.bulk.panel.encryptionKey.help = Enter the encryption key for this meter. This field is required for activation. To edit an already existing meter, use the UsagePoint Page.

# christoe: 2024-02-06 | Planio #23257 [i-Switch] Send an SMS when an aux charge is loaded
notify.selection.inherit=Inherit ({0})
error.notify.selection.null=It is required to set both preferences.
group.notify.children.change.alert=Children with matching or undefined notification IDs will be updated with these changes. Continue?
group.error.notification.save=Unable to link the notification information to the group.
customer.adjust.aux.accounts=Auxiliary Account Adjustments
customer.new.aux.accounts=New Auxiliary Accounts
customer.notification.types=Notification Types
customer.manage.notification.types=Manage Notification Types

#renciac: 2024-05-20 | Planio 27858: TariffStartDate not on the first of a month at midnight
warning.tariff.start.date.not.on.month.boundary=WARNING: tariff start date is not on a month boundary (the 1st at zero hours).</br>\
Bizswitch must be configured properly for this, as mid-month tariff changes MAY cause billing problems if not handled correctly!</br>\
Please be aware that MONTHLY BILLING Cyclic charges may only start or end on a month boundary.</br>\
Continue?
error.billing.cyclic.change.midmonth.monthly=MONTHLY BILLING Cyclic charges may only be initiated / changed for tariff start ON a month boundary (the 1st of a month at zero hours).

# michalv: 2024-05-22 | Planio 28023
token.reversal.reprinted.error=You do not have permission to reverse a reprinted token.

# rodgersn: 2024-02-20 | Planio #26293
meter.uri.remove.question=Changing the meter model will clear the Meter URI Fields (if captured). Continue?
meter.models.field.uri.present=URI Present
meter.models.field.uri.present.help=This indicates if the meter has URI.
meter.uri.address=Address
meter.uri.address.help=Enter the URI address of the meter.
meter.uri.port=Port
meter.uri.port.help=Enter the URI port number of the meter.
meter.uri.protocol=Protocol
meter.uri.protocol.help=Enter the URI protocol used to communicate with the meter.
meter.uri.params=Parameters
meter.uri.params.help=Enter the URI parameters used when communicating with the meter.
meter.uri.fields=Meter URI Fields
meter.uri.fields.list=Meter URI Fields (if captured): Address, Port, Protocol, Parameters.
meter.uri.port.error=Port must be a number from 0 to 65535.
bulk.upload.meter.uri.address=Meter URI Address
bulk.upload.meter.uri.port=Meter URI Port
bulk.upload.meter.uri.protocol=Meter URI Protocol
bulk.upload.meter.uri.params=Meter URI Parameters
bulk.upload.meter.uri.not.present.address.error=The meter Model can not have a meter URI address.
bulk.upload.meter.uri.not.present.port.error=The meter Model can not have a meter URI port.
bulk.upload.meter.uri.not.present.protocol.error=The meter Model can not have a meter URI protocol.
bulk.upload.meter.uri.not.present.params.error=The meter Model can not have a meter URI parameters.
error.field.meteruriprotocol.max=Meter URI Protocol must not exceed 255 characters.
error.field.meteruriaddress.max=Meter URI Address must not exceed 100 characters.
error.field.meteruriparams.max=Meter URI Parameters value is too long.

# zachv: 2024-05-07 | Planio 28174
tariff.field.bsst.charge_name.title=Charge Name
tariff.field.bsst.charge_name.title.help=Name of the charge that will be displayed on the receipt. 

# joelc: 2024-04-26 | planio-27792
group.type.for.cape.verde.contract = -

# michalv: 2024-04-05 | planio-26839
meter.txn.tokencode1=Token Code/s

# christoe: 2023-12-28 | Planio #25194 Reason Entry for Writing Off Charges in MMA
usagepoint.charge.writeoff.enter.reason=Enter a reason for writing off charges
usagepoint.charge.writeoff.select.reason=Select a reason for writing off charges

# thomasn: 2024-02-21 | planio-25347 [Cape Verde] Analyze and create the Cape Verde tariffs
tariff.blocks.thresholdCharge.error.empty=Where Block Price is set all Threshold Charges must be set or all must be empty.
tariff.blocks.thresholdCharge.error.incomplete=Threshold Charges can be set only where block price exists.

# zachv: 2023-11-25 | Planio #25498
grouptree.show_more = Show more
grouptree.empty = Empty
grouptree.search.help = Type at least the first two letters of an item. The item could be at any level of the hierarchy of data.
suggestbox.placeholder = Type to search...

# renciac: 2023-11-20 | Planio 25211: [TANESCO UAT] Staff tariff as "FBE" with monthly cyclic charge
tariff.field.subsidised.units.title=Monthly Subsidised Units
tariff.field.subsidised.units.descrip=Description
tariff.field.subsidised.units=Units
tariff.field.subsidised.units.help=Units that are issued at a subsidised charge.
tariff.field.bsst.charge.title=Charge
tariff.field.bsst.charge.title.help=Enter the amount to charge for subsidised units 
tariff.error.bsst.charge.positive=Must be a positive value.

# renciac: 2023-11-15 | Planio 25151 [TANESCO] [MMA] Automatically loading debt for a newly installed meter
tariff.field.meter_debt.title=Meter Debt
tariff.field.meter_debt.singlephase.label=Single Phase
tariff.field.meter_debt.singlephase.label.help=Enter Amount of Debt to consumer for pre-loaded single phase units on a new meter.
tariff.field.meter_debt.threephase.label=Three Phase
tariff.field.meter_debt.threephase.label.help=Enter Amount of Debt to consumer for pre-loaded three phase units on a new meter.

# marcod: 2023-08-29| planio 13587
import.upload.file.already.uploaded.group=Duplicate Filename. This file was already uploaded by another group

# marcod: 2023-06-21 | Planio 12175 Bulk device store movements
bulk.device.store.movement.help=Select the end device store the meters should be transferred to
bulk.device.store.movement=Select TO Device Store
bulk.device.store.movement.header=Bulk Device Store Movement
bulk.device.store.movement.without.input.file.not.implemented.yet=Bulk Device Store Movement needs input file of meters. Database setting has_input_file only implemented as 'y' for this filetype yet.
bulk.device.store.movement.param=TO Device Store

# renciac: 2023-09-20 | Planio 21358 View & writeoff also billing cyclic charges
usagepoint.last.cyclic.date.info=Last Cyclic Charge Date
usagepoint.last.cyclic.dates.info=Last Cyclic Charge Dates
usagepoint.last.cyclic.vend.date=At Vend/Topup
usagepoint.last.cyclic.billing.date=At Billing
usagepoint.charge.view.writeoff.date.help=Last cyclic date is the date when periodic charges like daily and monthly charges were last paid by a consumer. Charges after this date have yet to be recovered and may be written off here by selecting a new 'End Date' which becomes the new 'Last Cyclic Date'. Depending on the tariff and payment mode, there are potentially two sets of cyclic charges, one at Vend/Topup time, one at billing time.
usagepoint.charge.view.dialog.invalid.date.both=Date selected cannot be before BOTH last cyclic dates displayed
usagepoint.charge.view.dialog.warning.last.vend.cyclic.date=Last Vend Cyclic Charge already calculated on {0}. Nothing to calculate for VEND cyclic charges. Continue?
usagepoint.charge.view.dialog.warning.last.vend.billing.date=Last Billing Cyclic Charge already calculated on {0}. Nothing to calculate for Billing cyclic charges. Continue?
usagepoint.charge.view.filter.date.help=Outstanding charges will be calculated from the above Last Cyclic dates to this new selected date. It must be AFTER the previous last Cyclic Date/s. 
usagepoint.charge.view.dialog.date=Select End Date for charges
usagepoint.charge.writeoff.vend.heading=VEND/TOPUP CHARGES, Last Cyclic Charge Date
usagepoint.charge.writeoff.billing.heading=BILLING CHARGES, Last Billing Cyclic Charge Date
usagepoint.charge.writeoff.vend.total=The total vend charges amount including tax is
usagepoint.charge.writeoff.billing.total=The total billing charges amount including tax is
usagepoint.charge.writeoff.both.total=TOTAL AMOUNT INCLUDING TAX

# christoe: 2023-10-04 | planio 22545
special.action.reason.no.reason=No reason given.

# rodgersn: 2023-06-07 | Planio 21001
register.reading.txn.meter=Meter
meterreadings.table.date=Date Created
usage.point.register.reading.txn.description=Register Readings for all meters on this usage point for the time period selected

# christoe: 2023-06-27 | planio 22796
reprint.default.email.message=Dear Customer\n\nPlease find your receipt details below:\n{0}\nKind regards\n{1}

# rodgersn: 2023-02-28 | Planio 15160
meter.assign.from.units.warn=Changing the pricing structure from Thin Units will not migrate the Units balance. A manual adjustment will have to be done. Continue?
ps.paymentmode.change.warn=This change in pricing structure changes the payment mode. Please ensure all the charges and/or billings are up to date. Continue?

# joelc: 2022-12-09 | planio 7589
usagepoint.save.license.error=  Maximum usage points allowed has been reached. Please notify your System Administrator.
bulk.import.license.waring = Importing {0} active Usage Points will exceed the maximum active usage points allowed. <br/>Note that once the limit is reached, only usage points marked as Inactive will be imported.

# marcod: 2023-04-06 | Planio 21668 [ZESCO] Debt collection methods
auxchargeschedule.specific.list.item=ACCOUNT SPECIFIC
customer.auxaccount.principle.amt=Principle Amount

# renciac: 2023-04-01 | Planio 18524 New requirements for RegisterReadingThinTariff
billingdet.appliesto.group.label=Only for Billing dets that apply to others
billingdet.discount.label=Discount
billingdet.discount.help=Check to indicate that this billing determinant signifies a discount, eg. solar rebate.
billingdet.charge.type.help=This billing determinant is either a percentage charge of another billing determinant's charge or a flat rate.
billingdet.applies.to.label=Applies To
billingdet.applies.to.help=Select the master billing determinant to which the percentage value of THIS billing determinant is applied.
billingdet.lnk.error.save=Unable to save the Billing Determinant appliesTo link.
billingdet.lnk.error.both=AppliesTo and Charge_type must both be captured or neither.
billingdet.applies.to.already.in.use=Billing det/s to which this billing det applies are already in use on a tariff or template.
billingdet.applies.to.already.in.use.on.mdc=This billing Det is in use on an MDC, cannot be used as a sub billing det that applies to others. 
billingDet.in.use.cannot.change.settings=This billing det is already in use on a tariff or template, cannot change settings or record status.
billingDet.in.use.cannot.change.applies.to=This billing det or those it applies to is already in use on a tariff or template, cannot change billing dets it applies to.
billingDet.change.regread.panel.open=You currently have a RegisterReadingThinTariff under construction. Saving this billing_det change will cause that to clear. if you need to save changes there first, do not confirm this message, save that tariff first then return here to save this billing_det. Continue with this save now?
billingdet.taxable=Is AppliesTo Taxable? 
billingdet.taxable.help=Unselect if tax is not applicable to this Billing Det - ONLY for billingDets that apply to others
billingdet.lnk.error.taxable=Taxable can only be unselected when this is an appliesTo BillingDet
cyclic.charge.apply.at.lbl=Apply At:    
cyclic.charge.apply.at.vend.lbl=Vend
cyclic.charge.apply.at.billing.lbl=Billing
cyclic.charge.apply.at.error.required=You must select one.
tariff.blocks.unitcharge.negative=Billing Det Unit Charge percentage or discount must not be negative
tariff.save.failed.billingDets.changed.on.database=Save tariff Failed. Percentage / Discount BillingDets have changed on database. Incompatible with input. Refresh the page.
unitcharge.choose.charge.type=Choose type of Charge
unitcharge.type.none=None
unitcharge.type.percentage=Percentage
unitcharge.type.flatrate=Flat Rate
unitcharge.discount.type.charge.error=Discount must be either a percentage or a flat rate 

# thomasn: 2023-03-21 | Planio 17726
supply.group.in.use.error.service=Supply Group in use by one or more meters. Press cancel to load latest.
supply.group.in.use.error.lbl=Supply Group in use by one or more meters. Some fields are read-only while in this state.

# rodgersn: 2023-02-09 | Planio 20655
register.readings.total.consumption=Total consumption:

# thomasn: 2023-01-10 | Planio 18459
error.positive.value=Value must be positive.

# jacciedt: 2022-12-14 | Planio 19775
export.field.receiptnum=Receipt Number

# patrickm: 2022-11-08 | Planio #19650
reprint.total.tax=Total Tax
reprint.total.tax-inclusive=Total (Tax Incl.)

# thomasn: 2022-11-08 | Planio 17785
aux.account.mrid.external.unique.validation=The Unique ID of this aux account is already in use

# marcod: 2022-10-07 | Planio 19234
error.field.powerlimit.name.range=Name must be between 1 and 255 characters.
error.field.powerlimit.name.required=Name is required.
error.field.powerlimit.value.required=Value is required.
error.field.powerlimit.value.type=Value must be an integer and greater than zero.

# rodgersn: 2022-09-29 | Planio 16034
default.template.bulk.uploads=Download Template

# jacciedt: 2022-05-05 | Planio 15256
bulk.blocking.header=Bulk Blocking : Filename: {0}
bulk.blocking.without.input.file.not.implemented.yet=Bulk Blocking generation needs input file of meters. Database setting has_input_file only implemented as 'y' for this filetype yet.
import.upload.blocking.permission.needed=This user does not have permission to bulk upload Blocking instructions.
bulk.blocking.blocking.type=Blocking Type
bulk.blocking.blocking.type.required.field.error=Blocking Type is a required field
bulk.blocking.not.blocked=Not blocked

# thomasn: 2022-08-31 | Planio 17172
usagepoint.ps.unitstocurrency=Changing Pricing structure from thin-units will not migrate the units balance. A manual adjustment will have to be done. Continue?

# thomasn: 2022-06-07 | Planio 16665
mdc.txn.pandisplay=Pan Display(Clear Balance)
remove.meter.pandisplay=*Sends MDC Message to clear meter balance.

# jacciedt: 2022-02-28 | Planio 12375
bulk.pricing.structure.change.header=Bulk Pricing Structure Change : Filename: {0}
bulk.pricing.structure.change.without.input.file.not.implemented.yet=Bulk Pricing Structure Change generation needs input file of meters. Database setting has_input_file only implemented as 'y' for this filetype yet.
import.upload.pricing.structure.change.permission.needed=This user does not have permission to bulk upload pricing structure change instructions.
bulk.pricing.structure.change.ps.start.date.error=Start Date must be in the future and greater than the start date of currently active pricing structure and first tariff start date.
bulk.pricing.structure.change.start.date=Start Date
bulk.pricing.structure.change.start.date.help=This is the date that the selected Pricing Structure will start on. Must be future dated based on when the import is done.
bulk.pricing.structure.change.pricing.structure=Pricing Structure
bulk.pricing.structure.change.pricing.structure.help=Pricing Structures that have active tariffs based on selected Start Date. Changing from thin-units will not migrate the units balance. A manual adjustment will have to be done.
import.items.abort=Import Failed:  

# renciac: 2022-05-05 | Planio 15937 curb validation between PS and billing dets esp for TOU
error.pricingStructure.future.ps.no.tariff.at.date=The future pricing structure has no running tariff at the start date selected.

# rodgersn: 2022-05-17 | Planio #16807
customer.txn.error.tax.less.than.amount=The Tax amount cannot be less than the Amount incl Tax for negative values
customer.txn.error.tax.and.amount.different.sign=If both Amount incl Tax and Tax amount are entered, they should be both positive or both negative
customer.txn.error.amount.incl.tax.is.zero=Amount incl tax cannot be zero

# patrickm: 2022-03-24 | Planio #15899
error.tab.duplicate.customer=Customer open in another tab. Tab: ({0})
error.tab.duplicate.meter=Meter open in another tab. Tab: ({0})
error.tab.duplicate.usagepoint=Usage Point open in another tab. Tab: ({0})

# jacciedt: 2022-02-10 | Planio 14296
confirm.bill.payment.reversal=Confirm Bill Payment reversal?
bill.payment.reversal.success=Successful Bill Payment Reversal. Original Ref = {0}, Reversal Ref = {1}
reverse.payment=Reverse Payment

# renciac: 2022-01-25 | Planio 12812 New pricing_structure history
usagepoint.hist.pricing.structure=Pricing Structure
usagepoint.hist.pricing.start=Start Date
usagepoint.hist.pricing.end=End Date
usagepoint.hist.ps.change.reason=Change Reason
up_pricing_structure.header=Pricing Structure History on the Usage Point
up_pricing_structure.sub.header=Previous Pricing Structure changes made to this Usage Point
usagepoint.current.pricing.change.enter.reason=Enter a reason for changing current pricing structure
usagepoint.current.pricing.change.select.reason=Select a reason for changing current pricing structure
usagepoint.future.pricing.change.enter.reason=Enter a reason for changing future pricing structure
usagepoint.future.pricing.change.select.reason=Select a reason for changing future pricing structure
usagepoint.field.pricingstructure=Current Pricing Structure
usagepoint.field.future.pricingstructure=Future Pricing Structure
usagepoint.ps.date.modified.hd=Last Date Modified
usagepoint.ps.user.modified.hd=User 
usagepoint.ps.change.reason.hd=Change Reason

# jacciedt: 2022-01-19 | Planio 14121
reprint.key.change.notice.line.1=Your resource token is below, but your meter requires a key change before you enter it.
reprint.key.change.notice.line.2=To change your meter's key, enter the tokens listed below:

# thomasn: 2022-01-31 | planio-15259 [BizSwitch & MeterMngCommon] Add support for codes in BlockTariff
tariff.blocks.unitcharge.error.empty=Where Block Price is set all Unit Charges must be set or all must be empty.
tariff.blocks.unitcharge.error.incomplete=Unit Charges can be set only where block price exists.

# marcod: 2021-11-18 | Planio 14768 Indra integration UI changes
bulk.upload.cust.ref=Cust Reference
bulk.upload.external.unique.id=Meter External UniqueId
bulk.upload.external.cust.unique.id=Cust External UniqueId
bulk.upload.external.up.unique.id=UP External UniqueId
mrid.component.error=Unique ID is a required field
group.edit=Edit
customer.ref.label=Customer Reference
customer.ref.help=Enter a unique reference number for the customer. This reference will refer to this particular customer with in the Meter Management System
error.field.customerreference.null=Customer Reference is a required field
error.field.customerreference.range=Customer Reference must be between {min} and {max} characters.
cust.mrid.external.unique.validation=The Unique ID of this customer is already in use
cust.ref.external.unique.validation=The customer reference of this customer is already in use
up.mrid.external.unique.validation=The Unique ID of this usage point is already in use
gen.group.mrid.external.unique.validation=The Unique ID of this group is already in use
gen.group.mrid.external.length.validation=The Unique ID must be between 1 and 100 characters
meter.model.mrid.external.unique.validation=The Unique ID of this meter model is already in use
aux.type.mrid.external.unique.validation=The Unique ID of this aux type is already in use
special.action.reason.mrid.external.unique.validation=The Unique ID of this special action reason is already in use
pricing.structure.mrid.external.unique.validation=The Unique ID of this pricing structure is already in use

# renciac: 2021-12-30 | Planio 15152 Cater for changing payment mode in Pricing Structure
usagepoint.error.new.installdate.before.last.sts.vend.date=Installation date cannot be BEFORE last Vend / Topup Date: {0}
usagepoint.error.new.installdate.before.current.ps.start.date=Installation date cannot be BEFORE the start date of the current Pricing Structure on this usage point: {0}.

# renciac: 2021-12-06 | Planio 14852 [EPC] Zero value in block tariff
tariff.blocks.zero.error=Only the first Block may have a unit price = 0

# renciac: 2021-10-18 | Planio 14521 Aux Account Specific charge schedule
auxspecchargeschedule.title=Aux Account Specific Charge Schedule
auxspecchargeschedule.title.add=Add Aux Specific Charge Schedule
auxspecchargeschedule.title.update=Update Aux Specific Charge Schedule
customer.debt.status.lbl=Debt Status
customer.debt.status.help=DEBT STATUS:<br/><b>Active:</b> balance is not zero and positive<br/><b>Settled:</b> balance is zero <br/><b>Overcollected:</b> balance is negative (i.e Refund)<br/><b>Suspended:</b> suspend-until date is in the future<br/><b>Written Off:</b> LAST transaction for Aux account is type WRITE_OFF and balance = 0
customer.chargeschedule.cycle=Charge Cycle
customer.chargeschedule.chamt=Charge Cycle Amount

# renciac: 2021-09-05 | Planio 11634, 11636, 13969 ViewOutstandingCharges bugs
usagepoint.charge.view.activation.in.future=Usage point activation date is in the future, has no cyclic charges yet 
usagepoint.charge.writeoff.dialog.heading=List of Outstanding Charges as at {0}

# patrickm: 2021-08-20 | Planio 9834
meter.model.in.use=Meter model has one or more meters attached. Some fields are read-only while in this state.
meter.models.paymentmodes.preselected=Active payment modes cannot be removed while Meter model has meters attached.

# renciac: 2021-06-01 | Planio 11646 Bulk Keychange
supply.group.target.label=Target Supply Group / Key Revision
supply.group.target.label.help=The next Supply Group / Key Revision that this supply group will change to.  
supply.group.target.error.same=Target Supply Group cannot be the same as current
supply.group.target.validation.error=Target baseDate is less than current SGC
supply.group.target.validation.error.expired=Target SGC expiry- or issueUntil date is expired
supply.group.target.validation.nulls= NOTE: Validation between SGC and target SGC was not completed because one or more of base date, expiry date or issue until date is still null, pending update from HSM. Needs manual verification.
supplygroup.field.issued.until.date.label=Issued Until
supplygroup.field.expiry.date.label=Expiry Date
supplygroup.field.target=Target SGC/KRN 
supplygroup.base.date.label=Base Date
supplygroup.base.date.label.help=Base date used for generation of STS6 tokens
supplygroup.target.deactivate.question=This SGC/KRN is being de-activated now, but is still currently in use as a target SGC/KRN for others. Continue? 
supplygroup.base.dates.no.check=A target SGC is in play, but either or both base dates have not yet been updated by the HSM, so cannot validate against each other. Continue?
supplygroup.target.base.date.smaller.question=SGC base date > target SGC base date. Target is older SGC. Continue? 
bulk.Keychange.extract.label.meterNum=Meter Num 
bulk.Keychange.extract.label.userRef=User Ref 
bulk.Keychange.extract.label.token1=Token1 
bulk.Keychange.extract.label.token2=Token2 
bulk.Keychange.extract.label.token3=Token3 
bulk.Keychange.extract.label.token4=Token4 
bulk.Keychange.extract.label.fromSupGroup=From SupGroup 
bulk.Keychange.extract.label.fromKeyRev=From KeyRev 
bulk.Keychange.extract.label.fromTariffIdx=From TariffIdx 
bulk.Keychange.extract.label.fromBaseDate=From BaseDate 
bulk.Keychange.extract.label.toSupGroup=To SupGroup 
bulk.Keychange.extract.label.toKeyRev=To KeyRev 
bulk.Keychange.extract.label.toTariffIdx=To TariffIdx 
bulk.Keychange.extract.label.toBaseDate=To BaseDate 
bulk.Keychange.extract.label.transDate=TransDate 
bulk.Keychange.extract.label.userRecEntered=User generated 
bulk.Keychange.extract.label.importFileName=Import Filename
bulk.Keychange.extract.label.bulkRef=Bulk Ref 
bulk.Keychange.extract.none=No Keychange found for this import file
button.submit=Submit
action.params.header.label=Parameters
import.file.explanation=Upload file containing data and / or parameters for action
bulk.keychange.header=Bulk Key Change : Filename: {0}
bulk.keychange.to.header=KeyChange TO:
bulk.keychange.use.target=Use pre-captured Target SGC/KRN 
bulk.keychange.use.targetHelp=Target SGC/KRN is captured on the Supply Group page under Meters Menu. If selected, then each meter's SGC/KRN will be mapped to it's target. If no target for a particular SGC/KRN, will get an error unless also enter a specific SGC/KRN in the TO, then THAT will be used when no target. 
bulk.keychange.use.target.selected.message=Can select 'Use Target' as well as select supply Group values. If no values and an SGC/KRN has no target, will get an error. If enter values as well, these will be used for those with no target. 
bulk.keychange.supplygroupcode=New Supply Group Code
bulk.keychange.supplygroupcode.help=Select the new supply group code. 
bulk.keychange.supplygroupcode.error.required.no.target=At least, the Supply Group Code must be selected.
bulk.keychange.tariffindex.required=Tariff Index is required
bulk.keychange.tariffindex=New Tariff Index
bulk.keychange.tariffindex.help=Enter the tariff index to be changed. Required. If a meter currently has a different tariff index, a keychange will be generated.
bulk.keychange.instruction.label=Generate Key Change Tokens Instruction 
bulk.keychange.instruction.help=Issue instruction as to when the keychange tokens must be generated.
bulk.keychange.instruction.generate.keychanges.now=Generate and update Key Tokens now
bulk.keychange.instruction.generate.keychanges.next.vend=Set to generate Key Tokens with next vend
bulk.keychange.instruction.generate.keychanges.next.vend.after.date=Set to generate Key Tokens with next vend AFTER a date....
bulk.keychange.generate.keychanges.next.vend.after.date=Keychange with vend AFTER Date
bulk.keychange.generate.keychanges.next.vend.after.date.help=Keychanges will be generated at vend_time but only AFTER this date
bulk.keychange.after.date.error=After Date cannot be in the past. Code checked against now() being {0}
bulk.keychange.after.date.required=After Date is required for selected bulk instruction
bulk.keychange.overwrite.existing=Overwrite existing New SGC/KRN details?
bulk.keychange.overwrite.existing.help=Must select radiobutton to handle the case when new Supply Group details already exist on a meter: Overwrite and continue, or leave existing details and abort the keychange instruction for such meters.
bulk.keychange.overwrite.existing.error=Must choose one, overwrite or not.
import.upload.keychange.permission.needed=This user does not have permission to bulk upload KeyChange instructions. 
import.upload.cannot.change.action.params.now=An import was logged, action params can no longer be edited, only viewed. To redo items with different action parameters, will need to re-import in a new file.
import.upload.view.params.label=Params
button.process.selected=Process Selected
button.process.all=Process All
button.import.extract.keychanges=Extract Generated KeyChanges
bulk.keychange.without.input.file.not.implemented.yet=Bulk Keychange generation needs input file of meters. Database setting has_input_file only implemented as 'y' for this filetype yet.
import.items.selected.success=Selected Items successfully processed.
import.items.errors= Not all items were successfully processed.
import.items.all.success=All ImportFile items successfully processed.
import.extract.items.non=No items to extract
import.extract.items.exception=Extract Failed. Please contact Support.
import.upload.twirly.waiting.text.keychange=Keychange requests may take a while
import.upload.keychange.bulkref.label=KeyChange BulkRef 
meter.txn.bulk.import.file.name=Import FileName

# renciac: 2021-06-01 | Planio 12963 Extension to Generic upload framework for action params
import.file.parameters.needed= Please confirm and submit parameters for the import of this data. 
import.file.parameters.needed.no.data=Please confirm and submit parameters for bulk changes.\nA dummy filename will be generated.\nIf data IS needed as well as parameters import will be rejected.
import.file.param.no.data.dummy.filename= Generated filename is {0}
import.file.no.parameters=This file type {0} has no UI defined for action parameters. Contact support. Dummy file created.
import.file.no.parameters.or.setting=This file type {0} has no UI defined for action parameters or database setting for has_input_file should be {1}. Contact support.
import.file.params.save.error=Unable to save the import file with action params.
import.file.no.params.converter.error=There is no JSON parameter converter for the action params of this import file
import.file.get.params.fail=Setting up parameter panel failed. Check your filename and file type.
import.file.parameters.updated=Parameters were updated for filename: {0}
import.upload.file.settings.conflict=No file was selected to be uploaded, and conflict in bulk file type settings: input data is n or b, but action params is false
import.upload.file.no.input.data=Bulk File Type setting has_input_file = 'n', no filename needed.
import.upload.file.needs.action.params=This file type requires action parameters. Please capture on upload page.

# thomasn: 2021-08-16 | planio-10426
usagepoint.ps.start.date.lbl=Start Date
usagepoint.ps.name.lbl=Pricing Structure
usagepoint.ps.start.date.help=The date this pricing structure will be activated on. This start date is unique.
usagepoint.ps.start.date.error=Start Date must be in the future and greater than the start date of currently active pricing structure and first tariff start date.
usagepoint.ps.start.date.error.unique=Start Date must be unique. A pricing structure already exists with this start date.
usagepoint.ps.view.all.btn=View All
usagepoint.ps.delete.btn=Delete
usagepoint.ps.delete.btn.confirm=Are you sure you want to delete the future pricing structure?
usagepoint.ps.save.error=Saving of usage point pricing structure failed.
usagepoint.ps.delete.error=Deleting of usage point pricing structure failed.
usagepoint.ps.delete.success=Deleted usage point pricing structure successfully.
usagepoint.ps.required=Pricing Structure required.
usagepoint.ps.future.required=Future pricing structure should not be the same as the current one above.
usagepoint.ps.future.list.help=Select the pricing structure then set a start date for it.
usagepoint.ps.future.date.help=Start date for the selected future pricing structure.
usagepoint.ps.future.lbl=Future Pricing Structure
usagepoint.ps.future.start.date.lbl=Future Pricing Structure Start Date
usagepoint.ps.historic.error=Usage point has historic pricing structure which are not compatible with the meter model. Use new usage point.
meter.new.current.pricingstructure.required=Meter ({0}) does not support the current pricing structure - {1}.
meter.new.current.pricingstructure.select=Current Pricing Structure\n(If changed all future ones are deleted)
tariff.error.save.up.ps.start.date.conflict=Unable to update the tariff start date, pricing structure already added to usage point with start date {0}.
usagepointworkspace.error.meter.unsupported.model.current=The model of meter {0} does not support the usage point's current pricing structure.
warning.pricingStructure.billingDets.notsame.asmetermodel.mdc=WARNING: The Billing Determinant/s covered by the pricing structure(s) current tariff(s) are a PARTIAL match to those used by the MDC Channels connected to the Meter Model. Continue?
warning.pricingStructure.billingDets.notsame.asmetermodel.mdc.save.UP=WARNING ON SAVE: The Billing Determinant/s covered by the pricing structure(s) current tariff(s) are a PARTIAL match to those used by the MDC Channels connected to the Meter Model. Continue?
warning.pricingStructure.billingDets.notsame.asmetermodel.mdc.activate.UP=WARNING ON ACTIVATE: The Billing Determinant/s covered by the pricing structure(s) current tariff(s) are a PARTIAL match to those used by the MDC Channels connected to the Meter Model. Continue?
error.pricingStructure.billingDets.notsame.asmetermodel.mdc=ERROR: The Billing Determinant/s covered by the pricing structure(s) current tariff(s) have NO MATCHES to those used by the MDC Channels connected to the Meter Model. If not a exact match, there should be at least one matching.
error.pricingStructure.billingDets.notsame.asmetermodel.mdc.save.UP=ERROR ON SAVE: The Billing Determinant/s covered by the pricing structure(s) current tariff(s) have NO MATCHES to those used by the MDC Channels connected to the Meter Model. If not a exact match, there should be at least one matching.
error.pricingStructure.billingDets.notsame.asmetermodel.mdc.activate.UP=ERROR ON ACTIVATE: The Billing Determinant/s covered by the pricing structure(s) current tariff(s) have NO MATCHES to those used by the MDC Channels connected to the Meter Model. If not a exact match, there should be at least one matching.
question.confirm.installation.date.3=Activation date change conflicts with Pricing structures. Current PS will be deleted and Future PS will be made current.

# marcod: 2021-07-27 | Planio 12179
supplygroup.field.kmc.expirydate=KMC Expiry Date
supplygroup.panel.kmc.expirydate.help=The system will automatically send an email warning every day for vending keys that are about to expire.

# renciac: 2021-07-21 | Planio 13124 Debt Instalments
aux.charge.sched.cycle.label=Charge Cycle
aux.charge.sched.cycle.label.help=For Ad-hoc, charge with every vend. For Daily / Monthly determines the cycle of when charges are levied
aux.charge.sched.cycle.amount.label=Cycle Amount
aux.charge.sched.cycle.amount.label.help=An ad hoc amount is charged on each vend. Daily or Monthly cycle amounts will be charged once a day or once a month depending on the cycle; until the Debt is paid off.
aux.charge.sched.cycle.select.error=Charge cycle selection must accompany entry of Charge Cycle amount 
aux.charge.sched.cycle.instalment.label=Daily or Monthly Cycle Instalments are standalone amount entries
customer.auxaccount.start.date.lbl=Start Date
customer.auxaccount.start.date.help=Select the auxiliary account start date
customer.auxaccount.suspend.until.help=Temporarily suspend an auxiliary account until the set date in this field. For Debt Instalment charge schedules, take note of the appSetting for 'Accumulate Aux instalments during Suspension'.
customer.auxaccount.last.charge.date.lbl=Date Last Charged
customer.auxaccount.last.charge.date.help=Last date when a vend transaction was made that paid towards this account. Does not reflect manual payments or account adjustments, only actual payments via vends/topups. Note that for charge schedules which are NOT instalments, this might have been a part payment, not a full charge.
error.field.value.monthly.charge=Value must be one of PRO_RATA, FREE or FULL 
customer.auxaccount.suspend.until.smlr.start=Suspend Until Date should not be before Start Date 
customer.auxaccount.install.suspend.info=Charge schedule using Instalments follows specific behaviour after suspension. \r\nSee appSetting 'accumulate_aux_during_suspension'
vend.older.trans.info=Note that when reversing a transaction which is NOT the last transaction for the customer agreement, last purchase details are not reset and manual intervention is needed for future transactions in the same month. The last aux payment date on auxiliary accounts is also not reset.
vend.reversal.last.with.older=Note that there were multiple reversals this month, last purchase details are not reset and manual intervention is needed for future transactions in the same month. The last aux payment date on auxiliary accounts is also not reset.
auxaccount.upload.startDate=Start Date
auxaccount.upload.startDate.greater=Start Date may not be greater than Suspend Until
auxaccount.upload.startDate.format=Start Date must be either empty or properly formatted
auxaccount.upload.startDate.invalid.date=Start Date is an invalid date
auxaccount.upload.suspendUntil.invalid.date=Suspend Until is an invalid date
trans.bulk.upload.format.error.trans.date=Transaction date must be either empty (defaults to date of processing) or properly formatted (yyyy-MM-dd HH:mm:ss)
trans.bulk.upload.invalid.trans.date=Transaction date is invalid

# marcod: 2021-07-08 | Planio 12735
error.usagepoint.outdated=ERROR: Data on this tab is outdated due to another update. Please click reload to refresh the data.
button.reload=Reload

# jacciedt: 2021-04-15 | Planio 12792
sts.unit.generation.limit.error=The amount of units to be issued cannot be more than the configured amount of {0} units.

# thomasn: 2021-07-22 | Planio 5812
usagepoint.meter.inspection.request.btn=Send Meter Inspection Request
usagepoint.meter.inspection.request.setup.error=The Contract is not complete, confirm Physical Address data is there.
usagepoint.meter.inspection.request.processing.error=An error occurred processing the request.
usagepoint.meter.inspection.request.meterMng000=Meter inspection request processed OK. Reference={0}
usagepoint.meter.inspection.request.meterMng001=Meter inspection request general error. Reference={0}
usagepoint.meter.inspection.request.meterMng011=Meter inspection request error customer data incomplete or invalid. Reference={0}
usagepoint.meter.inspection.request.txt.comment=Enter Comment
usagepoint.meter.inspection.request.txt.comment.help=A description of the reason.

# jacciedt: 2021-04-15 | Planio 9695
no.aux.charge.schedule.defined=No Aux Charge Schedule defined

# jacciedt: 2021-03-05 | Planio 12340
customer.txn.error.tax.more.than.amount=The Tax amount cannot be more than the Amount Including Tax
customer.auxaccount.error.refund=The new balance after the adjustment will be {0}. You are not allowed to change this account into a refund.
customer.auxaccount.error.debt=The new balance after the adjustment will be {0}. You are not allowed to change this account into a debt.

# marcod: 2021-07-15 | Planio 13708
reprint.customer=Customer

# thomasn: 2021-08-18 | Planio 13381
meter.txn.engineeringtokens.column=Has Engineering Tokens

# patrickm: 2021-07-02 | Planio 13126
supplygroup.field.code.default=Default

# jacciedt: 2021-06-30 | Planio 12839
up_meter_install.remove.date=Remove Date
up_meter_install.install.ref=Install Ref
up_meter_install.remove.ref=Remove Ref
up_meter_install.header=Usage Point Meter Installations
up_meter_install.sub.header=Previous Usage Point Meter Installations made to this Usage Point

# renciac: 2021-06-28 | Planio 13515 Writeoff charges duplicates
usagepoint.charge.button.close.writeoff.and.unassign.customer=Close and Unassign Customer

# marcod: 2021-05-25 | Planio 12620
search.meter.sgc.label1=Supply Group / Key Revision
search.meter.sgc.label2=(Current or New)
search.meter.sgc.help=The search will find meters with a current or new supply group code equal to the selected SGC/KRN. A new SGC/KRN on a STS meter is populated only when a keychange is actioned. As from next vend it then becomes the current one.

# jacciedt: 2021-02-16 | Planio 11622
defaultAccountAdjustmentProcessor.notification.lowBalanceNoUsagePoint.email.subject=Account balance low for account {12} on agreement {5}
defaultAccountAdjustmentProcessor.notification.lowBalanceNoUsagePoint.email.message=Dear Customer,\n\nYour account status is: \n  Account balance: {7,number,currency}\n Low balance notification threshold: {8,number,currency}\n\nRegards,\nSupport Team
defaultAccountAdjustmentProcessor.notification.lowBalanceNoUsagePoint.sms.message=Balance for account {12} on agreement {5} is running low. Balance is {7,number,currency} Regards, Support Team
bill.payments=Bill Payments
bill.payments.provider=Provider
bill.payments.reversal.request.received=Reversal Request Received
bill.payments.pay.type=Pay Type
bill.payments.pay.type.details=Pay Type Details
bill.payments.vote.name=Vote Name
bill.payments.description=A list of bill payments made by this customer
bill.payments.transaction.type=Bill Pay Transaction Type

# renciac: 2021-05-25 | Planio 13153 PS date validation vs installDate
usagepoint.installation.date.before.tariff.start1=Installation date is before tariff start date {0}. <br/> The implication is that potential Tariff charges that are due before tariff start date will not be calculated.
usagepoint.installation.date.before.tariff.start2=<br/> Last date that cyclic charges were calculated was {0}.
usagepoint.installation.date.before.tariff.start3=<br/> No Last cyclic charge calculation date on file.
usagepoint.installation.date.before.tariff.start4=<br/> Continue?

# renciac: 2021-04-20 | Planio 7918 Bulk Tariff Generator
error.field.customerdescription.max=Customer description must be less than {max} characters.
button.export.ps.title=Export ALL pricing structure Current tariffs 
export.ps.failed.exception=Export failed. Please contact Support.
export.ps.failed.non=No Pricing Structures with current tariffs to export ???
import.edit.item.update.bulk.tariff.success=Data for tariff {0} was successfully updated.
import.ps.name.label=Pricing Structure
import.tariff.name.label=Tariff
import.tariff.edit.resave.error=Error: calcContents: {0}
import.upload.file.already.uploaded=Error: File already uploaded. Check table.
import.upload.tariff.permission.needed=This user does not have permission to import tariffs.

# patrickm: 2021-03-18 | Planio 11152
defaultAccountAdjustmentProcessor.notification.emergencyCredit.email.subject=Emergency credit threshold for {6}
defaultAccountAdjustmentProcessor.notification.emergencyCredit.email.message=Dear Customer,\n\nYour account status is: \n Account balance: {7,number,currency}\n  Low balance notification threshold: {8,number}\n  Emergency credit threshold: {9,number,currency}\n\nRegards,\nSupport Team
defaultAccountAdjustmentProcessor.notification.emergencyCredit.sms.message=Balance for {6} is running low and below emergency credit threshold. Balance is {7,number,currency} Regards, Support Team

# jacciedt: 2021-03-12 | Planio 12494
error.field.validity.message.content=The message content cannot be blank.

# jacciedt: 2021-03-15 | Planio 11902
customer.account.does.not.exist=Customer Account does not exist yet

# marcod: 2021-02-01 | Planio 12168
email.password.reset.message=We have received a password reset request for {0}.<br> To reset your password please click the link below.<br> {1}.
password.link.expired=The password reset link has expired. You can request another one.
password.link.used=The password reset link has been deactivated.
password.change.now= Change Password Now
password.reset.success=Your password has been changed successfully.

# renciac: 2021-03-04 | Planio 12582
bulk.ignore.dup.meters=Ignore Duplicate Meters
bulk.ignore.dup.meters.help=If meter number already exists on Database, ignore one in upload file. If duplicates in upload file, use first one.
bulk.upload.ignore.meter.dups.changed=Ignore duplicate meters setting has changed between steps! Was {0}; now {1}
bulk.upload.successful.meter.upload=Total of {0} meter uploads successfully processed, {1} duplicates ignored.

# jacciedt: 2021-02-12 | Planio 12340
unitsacc.balance.with.symbol=Units Balance ({0})

# jacciedt: 2021-02-04 | Planio 12330
bulk.upload.heading.metercustup=Generate Template for MeterCustUp Bulk Upload
bulk.upload.metercustup.notice=For Meter/Customer/UsagePoint Bulk Uploads, use the menu option -> Configuration -> Upload and Import Files

# jacciedt: 2021-02-05 | Planio 11950
demo.addmeterreadings.tariffCalc.failed={0} : {1} successfully added, but tariff calculation failed.
demo.addmeterreadings.success={0} : {1} successfully added.
demo.addmeterreadings.tariffCalc.success={0} : {1} successfully added and tariff calculation completed.

# jacciedt: 2021-01-08 | Planio 12082
bulk.upload.unitsaccountname=Units Account Name
bulk.upload.unitslowbalancethreshold=Units Low Bal Threshold
bulk.upload.unitsnotificationemail=Units Notif Email
bulk.upload.unitsnotificationphone=Units Notif Phone

# jacciedt: 2020-12-22 | Planio 11146
error.date.field.invalid=Value entered is not a valid date. Format = {0}

# renciac: 2020-12-10 | Planio 11365
### Units Account ###
unitsacc.title=Units Account
unitsacc.name.help=Enter a name for this account
unitsacc.name=Units Account Name
unitsacc.balance.help=The current units balance
unitsacc.balance=Units Balance
unitsacc.sync.help=Synchronize the units balance with the units balance on the meter
unitsacc.sync=Synchronize Balance
unitsacc.low.balance.threshold.help=When the units balance reaches this threshold a message will be sent to the customer.
unitsacc.low.balance.threshold=Units Low Balance Threshold
unitsacc.notification.email.help=A comma separated list of email addresses to which units related notifications can be sent (e.g. when the low balance threshold has been reached)
unitsacc.notification.email=Notification Email Addresses
unitsacc.notification.phone.help=A comma separated list of phone numbers to which units related notifications can be sent (e.g. when the low balance threshold has been reached)
unitsacc.notification.phone=Notification Phone Numbers
unitsacc.note=A units account is only necessary if the meter model and the pricing structure require it.
unitsacc.required=* \= Required
unitsacc.changes.cleared=Changes have been cleared.
units.account.error.save=Unable to save the units account.

# jacciedt: 2020-11-27 | Planio 11366
units.account=Units Account
units.account.transaction.history=Units Account History
units.account.transaction.description=Previous Account transactions for this Units Account
amount.cannot.be.zero=Amount cannot be zero
units.transaction.type=Units Transaction Type
units.transaction.type.sale=Sale
units.transaction.type.consumption=Consumption
units.transaction.type.manualadj=Manual Adjustment
units.transaction.type.reversal=Reversal

# renciac: 2020-11-25 | Planio 11363
# The defaultUnitsAdjustmentProcessor.notification messages has the same arguments as the defaultAccountAdjustmentProcessor, except units instead of currency
defaultUnitsAdjustmentProcessor.notification.disconnect.email.subject=Account balance has run out for {6}
defaultUnitsAdjustmentProcessor.notification.disconnect.email.message=Dear Customer,\n\nYour meter will be disconnected.\n\nYour account status is: \n  Account balance: {7,number}\n  Low balance notification threshold: {8,number}\n\nRegards,\nSupport Team
defaultUnitsAdjustmentProcessor.notification.disconnect.sms.message=Balance for {6} has run out and will be disconnected. Balance is {7,number} Regards, Support Team
defaultUnitsAdjustmentProcessor.notification.lowBalance.email.subject=Account balance low for {6}
defaultUnitsAdjustmentProcessor.notification.lowBalance.email.message=Dear Customer,\n\nYour account status is: \n  Account balance: {7,number}\n  Low balance notification threshold: {8,number}\n\nRegards,\nSupport Team
defaultUnitsAdjustmentProcessor.notification.lowBalance.sms.message=Balance for {6} is running low. Balance is {7,number} Regards, Support Team

# thomasn: 2020-10-16 | planio-9668
meter.models.battery.capacity=Capacity
meter.models.battery.capacity.help=Enter the full battery capacity value. Either in %, Months,Voltage etc.
meter.models.battery.capacity.error=Value must be positive.
meter.models.battery.threshold=Low Threshold
meter.models.battery.threshold.help=Enter the low battery threshold percentage. When crossed a low battery event will be triggered.
meter.models.battery.threshold.error=Value must be between 0 and 100.
meter.models.battery.event.lbl=Battery Event

# renciac: 2020-10-16 | Planio 11204
usagepoint.installdate.change.error.readings=Cannot change installation date, new readings on the meter / usage point, please refresh the page.
meter.replace.installation.date.error.trans=Cannot replace meter with a future installation date, new transactions on the usage point, or new readings on the meter; please refresh the page and try again.

# renciac: 2020-06-09 | Planio 9616
meter.change.activation.date=Change Activation date = New Installation date?
meter.change.activation.date.required=Please check one of the boxes for Activation Date
meter.change.activation.date.error.trans=Cannot change activation date, transactions on the usage point, or new readings on the meter; please refresh the page.
meter.change.installation.date.error.trans=Cannot change installation date, transactions on the usage point, or new readings on the meter; please refresh the page.
error.field.installdate.future.trans.or.possible.gap=Cannot have a future installation date. It can only be in the future if has no transactions on the UP and if have chosen to change the activation date = installation date.

# marcod : 2020-09-09 | Planio 10498
meter.units.help=Enter the number of {0} units to one decimal place only.

# marcod : 2020-10-08 | Planio 9723
meter.number.suggestion.help=Start typing the meter number, meters that start with those digits will appear in a dropdown. Click on one to select it.

# thomasn: 2020-09-01 | Planio 8404
auxaccount.upload.suspendUntil=Suspend Until
auxaccount.upload.suspendUntil.in.past=Suspend Until may not be in the past
auxaccount.upload.suspendUntil.format=Suspend Until must be either empty or properly formatted
customer.auxaccount.suspend.until.lbl=Suspend Until
customer.auxaccount.suspend.until.error=Date must be in the future.
customer.auxaccount.suspend.until=Suspended Until
customer.auxaccount.txn.history.suspend.until=Suspended Until : {0}

# jacciedt: 2020-08-13 | Planio 10007
auxtype.error.update.in.use=Unable to deactivate the auxiliary type, it is already in use.

# jacciedt: 2019-01-29 | Planio 8575
bulk.upload.invalid.regex={0} does not match its regex pattern

# jacciedt: 2020-06-17 | Planio 9605
demo.addmeterreadings.weekly=Weekly

# jacciedt: 2020-04-09 | Planio 6150
customer.unassign.unassign.customer=Unassign Customer
usagepoint.charge.button.writeoff.and.unassign.customer=Writeoff Charges and Unassign Customer

# joelc: 2020-07-10 | Planio 9609
reprint.remaining.balance=Balance
reprint.desc=Description

# renciac: 2019-12-03 | Planio 5311
file.item.panel.reg.read.reminder=REMINDER: Any meters with register reading tariffs might need an initial reading.
channel.readings.header.up=Usage Point: 
channel.readings.timestamp.label=Reading TimeStamp
channel.readings.timestamp.help=Reading timeStamp must be equal to installation date OR greater than existing reading timestamp for this meter installation 
channel.readings.table.error.heading=Error
channel.readings.partial.entry=The initial register readings for this meter's channels are not or only partially completed. Do you want to save what you have and complete the rest later?
channel.readings.preExisting.note=NOTE: There are pre-existing readings for this meter and usagepoint installation. 
channel.readings.preExisting.same.mdc.channels=These are from the same MDC Channels, as below in the table. \nPress CANCEL to keep these as is.\nIf you wish to change the init readings, enter new values. \nNote: Reading TimeStamp must be > previous.
channel.readings.preExisting.diff.mdc.channels=These seem to be from previous MDC Channels.\nLast reading date found was: {0}.\nEnter new values for the new MDC channels. Note: Reading TimeStamp must be > Prevous Last Reading Date.
channel.readings.preExisting.note.end=\nIf none of these options are desirable, please contact System Support. 
channel.readings.timestamp.install.date=Reading time stamp must be equal to installation date.
channel.readings.timestamp.previous.date=Reading time stamp must be greater than previous reading date: {0} 
button.ok=OK

warning.change.mdc.on.meter.NO.DATA=WARNING: Changing the MDC on the meterModel might affect {0} active and {1} inactive usage points which do not have Register Reading Tariffs. Continue?
warning.change.mdc.on.meter.PARTIAL.or.TOTAL.active.up=WARNING: Changing the MDC on the meterModel might affect {0} active and {1} inactive usage points with Register Reading Tariff and PARTIAL or EXACT billing determinant match. Also NO match on inactive usage points can be changed here (when they are activated NO match will be rejected). Continue?
error.change.mdc.on.meter.NONE.MATCH.active.up=ERROR: Changing the MDC on this meterModel has {0} ACTIVE usage-points with Register Reading Tariffs and NO MATCHES on the billing determinants of some. Cannot change the mdc on the meter-model now.

warning.change.mdc.channel.NO.DATA=WARNING: Changing / (assigning a new) channel on this MDC might affect {0} active and {1} inactive usage points which do not have Register Reading Tariffs. Continue?
warning.change.mdc.channel.PARTIAL.or.TOTAL.with.regreadPS=WARNING: Changing / (assigning a new) channel on this MDC might affect {0} active and {1} inactive usage points with Register Reading Tariff and PARTIAL or EXACT billing determinant match. Continue?
error.change.mdc.channel.NONE.MATCH.active.up=ERROR: Changing / (assigning a new) channel on this MDC has {0} ACTIVE usage-points with Register Reading Tariffs and NO MATCHES on the billing determinants of some. Cannot change the channels / billing determinants now.

warning.change.mdc.NO.DATA=WARNING: Changing the MDC will affect {0} active and {1} inactive usage points which do not have Register Reading Tariffs. Continue?
warning.change.mdc.inactive.with.regreadPS=WARNING: Changing the MDC might affect {0} active and {1} inactive usage points with Register Reading Tariffs. Continue?
error.deactivated.mdc.active.up=ERROR: Cannot deactivate this MDC - has {0} active and {1} inactive usage points with Register Reading Tariffs. 

warning.change.or.new.tariff.has.diff.billing.dets.to.active.up.with.ps=WARNING: Changing / creating a new Register Reading tariff on this Pricing Structure has PARTIAL match of billing determinants to some of the active usage points using the pricing structure. Continue?
error.change.or.new.tariff.has.diff.billing.dets.to.active.up.with.ps=ERROR: Changing / creating a new Register Reading tariff on this Pricing Structure has NO MATCH to billing determinants of some of the active usage points using the pricing structure. There should be at least a partial match in ALL cases.
warning.change.status.billing.det.but.in.use=Changing the status of this billing determinant might affect {0} active and {1} inactive usage points with meters assigned to a meter model that use an MDC with channels assigned to this billing determinant. Continue?
error.cannot.activate.up.model.not.channels.with.regread.ps=ERROR! Cannot activate the Usage Point. The meter model and pricing structure must have at least one channel with the same billing determinant in common.
error.incompatible.model.with.ps=ERROR! The meter model and pricing structure are not compatible. \nEither the meter model has channels but not Register reading Pricing Structure or vice versa.

# renciac: 2019-11-07 | Planio 7951
channel.field.maxsize.help=Maximum reading on the meter's register before rollover to zero
channel.field.time.interval.help=Time interval of register readings
channel.field.time.interval=Time Interval
channel.config.header=Channel Overrides
channel.config.overrides.button=Override MDC Channels
channel.config.mdc.titlename=Mdc
channel.config.field.titlename=Meter Channel Override
channel.config.title.add=Add Meter Channel Override
channel.config.title.update=Update Meter Channel Override
channel.field.titlename.help=Only shows Mdc Channels not yet overridden - must select an MDC Channel to override
channel.config.field.billingdetnames=Billing Determinant/s
channel.config.error.delete=Unable to delete the Meter Channel Override. Contact support
channel.config.delete.confirm=Are you sure you want to delete the Meter Channel Override?
channel.config.deleted=The Meter Channel Override was successfully deleted.
mdc.channel.override.metermodels=Override from Meter Model/s
mdc.channel.override.metermodels.none=No Overrides
channel.config.already.override=Channel already has override. Selected from above. 
channel.field.meter.reading.type.title=Reading Type
meter.model.change.mdc.confirm.delete.configs=Meter model had channel overrides for the previous MDC on this model. Will delete those if go ahead with change of MDC. Continue?
meter.model.channel.configs.error.delete=Unable to delete the Meter Channel Overrides for previous MDC. Contact support
channel.config.no.channels.to.override=No channels defined for this MDC. 
mdc.channel.status.change.warn.overrides=Note: this mdc channel has overrides. Continue with status change?
mdc.channel.field.time.interval=MdcChannel Time Interval
channel.override.field.time.interval=Override Time Interval
mdc.channel.field.maxsize=MdcChannel Reading Max Size
channel.override.field.maxsize=Override Reading Max Size
mdc.channel.field.reading_multiplier=MdcChannel Reading Multiplier
channel.override.field.reading_multiplier=Override Reading Multiplier
import.edit.reg.Read.item.update.success=Data for meter {0}, reading Timestamp {1} successfully updated.
import.reg.Read.channel.value.label=Channel Value
import.reg.Read.timestamp.label=Reading Timestamp
reg.read.init.readings.cancel.confirm=Cancelling means no further initial readings for this meter's channels (registers) are saved and tariff calculations will use the first or existing readings as the initial readings. Continue?
channel.readings.import.note=Note: Register readings can also be imported manually.

# barryc: 2020-05-12 | Planio 8211
tariff.error.monthlycost.name=Specify a name for the charge
tariff.error.monthlycost=A valid number must be provided
tariff.error.monthlycost.positive=Value must be positive or zero

# barryc: 2020-04-21 | Planio 7780
error.field.specialactionsdescription.max=Description must be between {min} and {max} characters.

# jacciedt: 2020-04-14 | Planio 8139
meter.model.deactivate.in.use.error=Cannot deactivate - there are already meters using this meter model.

# jacciedt: 2020-04-01 | Planio 7873
question.confirm.installation.date.future.date=future date
usagepoint.field.meter.activation.date=Initial Activation Date

# barryc: 2020-04-22 | Planio 8209
tariff.error.percent_charge=Specify a value.
tariff.error.unit_charge=Specify a value.

# patrickm: 2020-04-03 | Planio 8675
register.reading.txn.create_date=Date Created

#joelc: 2020-02-19 | Planio 8592
meter.models.field.data.decoder=Meter Data Decoder
meter.models.field.data.decoder.help=Some meter models require specific decoders to process their readings.  

# patrickm: 2020-01-14 | Planio 7768
blockingtype.form.dailyamount=Amount per day
blockingtype.form.dailyamount.help=Maximum amount allowed per day
blockingtype.msg.error.dailyamount=You must include the maximum amount per day template, {max_amount_per_day}, in your message.

# jacciedt: 2020-01-20 | Planio 7356
usagepoint.hist.device.move.ref=Device Movement Reference
usagepoint.device.move.ref.lbl=Device Movement Reference Number
error.field.devicemoveref.max=Device Movement Reference must be less than {max} characters.

# jacciedt: 2019-12-31 | Planio 7950
configure.user.interface=Configure User Interface
configure.user.interface.field.name=Field Name
configure.user.interface.display=Display?
configure.user.interface.validation.regex=Validation Regex
configure.user.interface.regex.failed.message=Regex Failed Message / Enumerated Values
changes.saved=Changes have been saved.
configure.user.interface.invalid.regex=One or more fields have invalid regex patterns in them.
error.required.field={0} is a required field - the record cannot be saved without it.
error.regex.invalid=The input value does not match the required pattern of: {0}
configure.user.interface.enumerated.values.label=Type in the list of possible values separated by commas:
enumerated.field=enumerated field

# jacciedt: 2019-11-18 | Planio 7264
meter.select.meter.phase=Meter Phase
meter.select.meter.phase.help=Select the correct meter phase for the meter model.

# jacciedt: 2019-12-04 | Planio 7772
meter.txn.reversal.reason=Reversal Reason
meter.txn.reversed.by=Reversed By
vend.reversal.exceeds.time.limit=Reversal unsuccessful. It exceeds the reversal time limit.

# joelc: 2019-11-25 | Planio 7487
sts.tokens.header = STS Tokens
verify.token = Verify Token
verification.error.timeout=Error verifying tokens. No response received from service.
verification.error.general=Unable to verify token
verify.token.class=Token Class
verify.token.subclass=Token Sub-Class
verify.token.id=Token ID
verify.token.units=Units
verify.token.date=Token Date

# jacciedt: 2019-11-21 | Planio 7362
energybalancing.error.duplicate.selected.meters=The selected meter is already added to the list.

# zachv: 2019-11-21 | Planio 7916
tariff.field.free.units.title=Monthly Free Units
tariff.field.percent_charge.title=Percentage Charge
tariff.field.percent_charge.add=Add Percent Charge
tariff.field.cyclic_charge.title=Cyclic Charge
tariff.field.cyclic_charge.add=Add Cyclic Charge
tariff.field.non_accruing_monthly.name=Non accruing monthly
tariff.field.non_accruing_monthly.name.help=Only applies to MONTHLY cycle. Non accruing monthly charges only charge for the month a transaction is done, any previous months where no monthly charges were levied (eg. due to no purchases or only free tokens) will not be charged for retrospectively.
tariff.field.unit_charge.title=Unit Charge
tariff.field.unit_charge.add=Add Unit Charge
tariff.field.unit_charge.name=Charge Name
tariff.field.unit_charge.name.help=The name for this charge that will end up on the customer's receipt.
tariff.field.unit_charge.is_percent=Percentage
tariff.field.unit_charge.is_percent.help=Whether or not this is charged as a percentage of the unit price or a currency charge per unit.
tariff.field.unit_charge.is_taxable=Taxable
tariff.field.unit_charge.is_taxable.help=Whether tax should be applied to this charge.
tariff.field.unit_charge=Unit Charge
tariff.field.unit_charge.help=The currency value to charge per unit purchased. This is an additional charge over and above the base unit price.
tariff.field.unit_charge_percent=Percentage of Unit Price
tariff.field.unit_charge_percent.help=This charge will be applied as a percentage of the unit price. If there are blocks it will use the block price.
tariff.error.unit_charge.name=Specify a name for the charge.
tariff.error.unit_charge.positive_not_zero=Must be a positive value and not zero.

# jacciedt: 2019-11-12 | Planio 7814
customer.auxaccount.increase.debt=Increase DEBT by:
customer.auxaccount.decrease.debt=Decrease DEBT by:
customer.auxaccount.increase.refund=Increase REFUND by:
customer.auxaccount.decrease.refund=Decrease REFUND by:
customer.auxaccount.error.amount.negative=Amount can not be negative
customer.auxaccount.error.tax.negative=Tax can not be negative
customer.auxaccount.error.adjustment=Either Increase or Decrease needs to be selected
customer.auxaccount.error.balance.refund=Balance cannot be negative or zero

# patrickm: 2019-11-02 | Planio #7801
customer.search.listbox.label=Customer Search by
customer.search.listbox.item_agr_ref=Agreement Ref
customer.search.listbox.item_id_num=ID Number
customer.search.listbox.item_surname=Surname

# jacciedt: 2019-10-24 | Planio 7812
customer.auxaccount.functions.as=Functions As

# thomasn: 2019-10-17 | planio-5133
link.blockingtype=Blocking Types
blockingtype.title=Blocking Type
blockingtype.name=Name
blockingtype.panel.error.missingvalues=One of the values ({0}, {1}, {2}, {3})\nmust be defined OR it must be a complete block.
blockingtype.title.add=Add Blocking Type
blockingtype.title.update=Update Blocking Type
blockingtype.form.typename=Name
blockingtype.form.typename.help=Name for the Blocking Type
blockingtype.form.units=Units per day
blockingtype.form.units.help=Maximum units allowed per day
blockingtype.form.complete=Complete
blockingtype.form.complete.help=Complete block means no vending will be allowed.
blockingtype.form.vends=Number of Vends
blockingtype.form.vends.help=Maximum vends allowed before Complete block
blockingtype.form.amount=Maximum Amount
blockingtype.form.amount.help=Maximum amount allowed before complete block
blockingtype.form.message=Message
blockingtype.form.message.help=Message with block details displayed to user. Use {remaining_vends} to display remaining vends before complete block. Use {remaining_amount} to display remaining amount before complete block. Use {max_units_per_day} to display max allowed units per day. Use {max_amount_per_day} to display max amount allowed per day. Use {reason_fixed} to display reason for block. Note, curly brackets denote system variables.
blockingtype.error.save=Unable to save the blocking type.
blockingtype.error.save.duplicate=Unable to save the blocking type, another blocking type with the same name already exists.
blockingtype.error.update=Unable to update the blocking type.
blockingtype.error.update.duplicate=Unable to update the blocking type, another blocking type with the same name already exists.
blockingtype.msg.error.variables=Curly brackets only allowed for valid system variables, {max_units_per_day}, {max_amount_per_day}, {remaining_amount}, {remaining_vends} or {reason_fixed}.
blockingtype.msg.error.units=You must include the units template, {max_units_per_day}, in your message.
blockingtype.msg.error.units.undefined=Units template {max_units_per_day} not allowed, unless {0} has been defined.
blockingtype.msg.error.vends=You must include the number of vends template, {remaining_vends}, in your message.
blockingtype.msg.error.vends.undefined=Vends template {remaining_vends} not allowed, unless {0} has been defined.
blockingtype.msg.error.amount=You must include the amount template, {remaining_amount}, in your message.
blockingtype.msg.error.amount.undefined=Amount template {remaining_amount} not allowed, unless {0} has been defined.
blockingtypes.header=Blocking Types
blockingtypes.title=Current Blocking Types

# jacciedt: 2019-10-10 | Planio 7514
meter.mrid.external.unique.validation=The Unique ID of this meter is already in use

# jacciedt: 2019-09-17 | Planio 5823
demo.addmeterreadings.earliest.reading=Earliest Reading
demo.addmeterreadings.latest.reading=Latest Reading
demo.addmeterreadings.zero.checkbox.text=Add zero readings
demo.addmeterreadings.zero.form.title=Zero Readings
demo.addmeterreadings.consecutive=Consecutive
demo.addmeterreadings.random=Random
demo.addmeterreadings.percentage.instances=Percentage of instances
demo.addmeterreadings.missing.checkbox.text=Add missing readings
demo.addmeterreadings.missing.form.title=Missing Readings
demo.addmeterreadings.algorithm.logic=Algorithm logic
demo.addmeterreadings.delete=Delete existing Interval Readings
demo.addmeterreadings.delete.all=All
demo.addmeterreadings.delete.selected=Selected date range
demo.addmeterreadings.append=Append
demo.addmeterreadings.link=[DEMO] Add Meter Readings
demo.addmeterreadings.header=Add Meter Readings
demo.addmeterreadings.title=Add Meter Readings
demo.addmeterreadings.title.criteria.register=Register Readings Criteria
demo.addmeterreadings.reading.variants=Choose Reading Variant
demo.addmeterreadings.delete.register=Delete existing Register Readings
demo.addmeterreadings.error.misc.start=This start date needs to be after the main start date
demo.addmeterreadings.error.misc.end=This end date needs to be before the main end date
demo.addmeterreadings.error.instances.required=Instances are required
demo.addmeterreadings.error.instances.format=Instances need to be numeric
demo.addmeterreadings.error.instances.range=Instances need to be a whole number between 0 and 100
demo.addmeterreadings.error.mdc.channel=No MDC Channel selected

# renciac: 2019-09-19 | Planio 7656
import.generic.start.label=Start of Data
error.field.enckey.max=Meter encryption key may not exceed maximum 255 characters.
error.field.powerlimitlabel.max=Power Limit LABEL may not exceed maximum 255 characters.
bulk.upload.invalid.unexpected.commas=Unexpected Commas - cannot identify separate fields accurately
button.import.extract.fail=Extract Failed
import.upload.num.failed.upload.label=Num Failed upload
import.items.unsuccessful.uploads.reminder=\nREMINDER: There were {0} items not successfully uploaded in this file. They are ignored for further processing and must be manually recreated in a new file and uploaded afresh.
import.upload.completed=File Upload completed. 
bulk.upload.file.error=Error while importing the file
error.field.customvarchar1.max=UP customvarchar1 can be max 255 characters.
error.field.customvarchar2.max=UP customvarchar2 can be max 255 characters.
error.field.phonecontact1.max=Phone Contact1 must be less than {max} characters.
error.field.phonecontact2.max=Phone Contact2 must be less than {max} characters.
error.field.notificationemail.max=Customer Account Notification Email Address must be less than {max} characters.
error.field.notificationphone.max=Customer Account Notification Phone must be less than {max} characters.
import.file.item.view.still.busy=The import on this file is running. No further action can be taken until that is completed or stopped.
import.file.item.view.still.busy.stopped=Stop import instruction issued. Import will cease after current batch.
import.file.view.upload.still.busy=The upload on this file is in progress. 
button.stop.import.all=Stop Import
import.file.stopped=The import on this file was stopped.
import.file.stopped.instruction=Stop Import instruction has been issued. The import will stop after the current batch.

# renciac: 2019-09-16 | Planio 6715
bulk.upload.idNumber=Id Number

# thomasn: 2019-10-09 | Planio 6286
usagepoint.unblocking.enter.reason=Enter a reason for unblocking
usagepoint.unblocking.select.reason=Select a reason for unblocking

# thomasn: 2019-09-09 | planio-6287 Add reason when re-activating a UP
usagepoint.hist.status.reason=Status Reason

# jacciedt: 2019-09-10 | Planio 7490
meter.clearreverseflag=Clear Reverse Flag
meter.disabletriplimit=Disable Trip Limit
meter.setcurrentlimit=Set Current Limit
meter.issue.token.description.help=Enter a description for this {0}.

# robertf: 2019-09-10 | Planio 7571
customer.txn.reason=Action Reason
customer.auxaccount.history.title=Auxiliary Account History
customer.auxaccount.history.filter.title=Auxiliary Account History for : {0}
customer.auxaccount.history.filter.discription=Previous changes made to auxiliary account : {0}
customer.auxaccount.history.table.header.datemodified=Date Modified
customer.auxaccount.history.table.header.user=User
customer.auxaccount.history.table.header.action=Action
customer.auxaccount.history.table.header.type=Type
customer.auxaccount.history.table.header.accountname=Account Name
customer.auxaccount.history.table.header.balance=Balance
customer.auxaccount.history.table.header.priority=Priority
customer.auxaccount.history.table.header.chargeschedule=Charge Schedule
customer.auxaccount.history.table.header.freeissue=Free Issue
customer.auxaccount.history.table.header.status=Status
customer.auxaccount.history.table.header.updatereason=Update Reason
customer.auxaccount.history.table.header.createreason=Create Reason
customer.title.auxaccounts.history.selector.description=Select Auxiliary Account to view history.

# jacciedt: 2019-08-14 | Planio 7341
import.account.number=Account Number
import.arrears.balance=Arrears Balance
import.debtor.balance=Debtor Balance
import.edit.account.number.update.success=Data for Account Number {0} successfully updated.
import.edit.generic.update.success=Data successfully updated.

# jacciedt: 2019-07-18 | Planio 6240
meter.select.store.add=Meter Store

# jacciedt: 2019-08-15 | Planio 7310
transaction.history.graph.yaxis.label2=Number of Units

# jacciedt: 2019-08-22 | Planio 6738
tou.thin.error.tax.positive=Tax must be a positive value.
register.reading.tax.positive=Tax must be a positive value.

# jacciedt: 2019-07-29 | Planio 7197
error.field.validity.email=One of the email addresses are invalid.
reprint.warning.line.1=WARNING!!! THIS IS A REPRINT
reprint.warning.line.2=of a token issued on {0}
reprint.warning.line.3=TAX INVOICE (COPY)
reprint.warning.line.4=Reprinted on: {0}
reprint.warning.line.5=Reprinted by: {0}
reprint.credit.vend.tax.invoice=Credit Vend - Tax Invoice
reprint.util.name=Util. Name
reprint.util.dist.id=Util. Dist. ID
reprint.util.vat.no=Util. VAT No.
reprint.util.address=Util. Address
reprint.issued=Issued
reprint.token.tech=Token Tech.
reprint.alg=Alg.
reprint.sgc=SGC
reprint.krn=KRN
reprint.your.resource.token=Your {0} Token
reprint.standard.token=Standard Token
reprint.receipt.nr=Receipt #
reprint.free.basic.resource=Free Basic {0}
reprint.debt.items=Debt Items
reprint.fixed.items=Fixed Items
reprint.total.vat.excl=Total (VAT Excl.)
reprint.total.vat.incl=Total (VAT Incl.)
reprint.print=Print
reprint.deposit=Deposit
reprint.save.to.pdf=Save to PDF
reprint.electricity=Electricity
reprint.water=Water
reprint.gas=Gas

# jacciedt: 2019-08-20 | Planio 7364
error.supplygroup.server=Duplicate Supply Group Code and Key Revision Number. Specify unique values.

# jacciedt: 2019-08-15 | Planio 7449
location.field.address.line.2=Line 2
location.field.address.line.3=Line 3

# jacciedt: 2019-08-01 | Planio 7368
location.field.address.line.1=Line 1
customer.phone.1=Phone Number 1
customer.phone.2=Phone Number 2

# jacciedt: 2019-07-15 | Planio 7244
error.field.specialactionsname.range=Action Name must be between {min} and {max} characters.

# jacciedt: 2019-07-16 | Planio 7148
unit.kiloliter.symbol=kl
unit.cubicmeter.symbol=m\u00B3
meter.units=Units ({0})

# robertf: 2019-07-08 | Planio 6247
transaction.history.column.header.stdunits=Standard Units
transaction.history.column.header.fbeunits=Free Basic Units
transaction.history.column.header.stdtoken=Standard Token Total
transaction.history.column.header.fixedamt=Fixed Costs Total
transaction.history.column.header.auxamt=Auxiliary Payment Total

# thomasn: 2019-07-01 | Planio-6288
usagepoint.blocking.info.message=This usage point was blocked by {0} on the {1} 
usagepoint.blocking.info.message.reason= for the following reason: <br/> {0}
usagepoint.hist.blocking.name=Blocking Type
usagepoint.hist.blocking.reason=Blocking Reason

# renciac: 2019-06-26 | Planio 6291
bulk.upload.powerlimit.key=Power Limit
meter.powerlimit.key.error.integer=Power Limit Value must be an integer
meter.powerlimit.key.error.not.configured=There are no power limit settings configured in the app settings.
meter.powerlimit.key.error.invalid=This power limit is not configured in the app settings.

error.field.usagepointgroups.required.group=Required Usage Point group is missing: {0} 
error.field.usagepointgroups.missing.group=Hierarchy not complete : missing usage point group field for Usage Point Type: {0}
error.field.usagepointgroups.invalid.group=Invalid usage point group name {0} for Usage Point Type: {1}
error.field.usagepointgroups.incomplete.group=Incomplete hierarchy level in Usage Point Group: {0} 

bulk.upload.invalid.locationgroups.not.configured=Location groups are not configured for this system
usage.point.location.group.generate.label=Usage Point Location Group
error.field.uplocationgroups.required=Required Usage Point Location group is missing
error.field.uplocationgroups.invalid.group=Invalid location group name {0} for Usage Point Location Group
error.field.uplocationgroups.incomplete.group=Incomplete hierarchy level in Usage Point Location Group
error.field.uplocationgroups.first.level.required=Usage Point Location Group is required. At least the first level of hierarchy should be completed.

customer.location.group.generate.label=Customer Location Group
error.field.custlocationgroups.required=Required Customer Location group is missing. 
error.field.custlocationgroups.invalid.group=Invalid location group name {0} for Customer Location Group
error.field.custlocationgroups.incomplete.group=Incomplete hierarchy level in Customer Location Group
error.field.custlocationgroups.first.level.required=Customer Location Group is required. At least the first level of hierarchy should be completed.

location.field.address.suburb.name=Suburb Name

# jacciedt: 2019-06-21 | Planio 6359
billingdet.error.save.duplicate=Unable to save the Billing Determinant, another Billing Determinant with the same name already exists.

# renciac: 2019-05-29 | Planio 6237
meter.freeissue.currency=Emergency Vend

# renciac: 2019-04-25 | Planio 6235
customer.id.partial.search=No exactly matching customer Id. Doing advanced search...
error.customer.load=Unable to display the Customer.
search.get.total.label=Count Total results of the selected criteria
search.count.label=Counting total results for selected criteria ...

# renciac: 2019-04-17 | Planio 6234
customer.id.error.noentry=Enter Customer Id number or % for all
metersearch.error.nometer=Enter a meter number or % for all
usagepoint.error.none=Enter a usage point name or % for all
customer.error.noentry=Enter a customer surname or % for all
customer.agreement.error.noentry=Enter an agreement number or % for all
customer.account.error.noentry=Enter an account number or % for all

# renciac: 2019-03-25 | Planio 5961
import.upload.header=File Upload / Import
import.upload.file.upload.title=File Upload
import.filetype.select.file.help=Select the file type of the file to be uploaded & imported 
import.filetype.select.labeltext=File Type
import.upload.filetype.none=Please select the file type of the file to be uploaded 
import.upload.filename.txt=Selected filename={0}
import.upload.file.select.labeltext=Select File to upload
import.upload.select.file.help=Select a movements file containing the information for uploading into the system
import.upload.csv.button=Upload File
import.upload.workspace.heading=File Upload and Import Data
link.file.import=Upload and Import Files
import.upload.filetype.error=Invalid File Type. Contact Support.
import.upload.file.none=No file was selected to be uploaded
import.upload.file.error=Error while uploading the file. Contact Support.
import.selected.items.non=No items have been selected for import.
import.upload.uploaded.files.title=Uploaded Files for Data Import
import.upload.file.name.label=File Name
import.upload.num.items.label=Num Items
import.upload.startdate.label=Upload Start
import.upload.enddate.label=Upload End
import.upload.last.imported.by.label=Last Import By
import.upload.detail=Detail
import.upload.open.label=Open

import.file.items.header=File Items
button.import.selected=Import Selected
button.import.all=Import All
import.items.title=Items for Import
import.select.label=Select
import.upload.successful.label=Upload Success
import.upload.date.label=Import Date
import.num.attempts.label=Num imports
import.last.successful.label=Import Success
import.meter.label=Meter
import.up.label=Installation
import.agrref.label=Contract
import.comment.label=Comment
import.itemdata.label=Data
import.upload.username.label=Upload User
import.last.start.label=Last Import Start
import.last.end.label=Last Import End
import.items.file.detail.header=File Detail
import.items.edit.header=View / Edit File Item
import.cancel.edit.item.confirm=Cancelling will abandon changes made above. Continue?
import.edit.item.update.success=Data for Customer {0}, meter {1} successfully updated.
import.edit.item.update.non=Data has not changed. No update necessary.

# thomasn: 2019-02-18 | Planio 6223
customer.idnumber.help=Enter the customer's ID number.
customer.idnumber=ID Number
error.field.idnumber.max=ID Number must be less than {max} characters.
customer.idnumber.column=ID Number
search.customer.idnumber=ID Number

#  renciac: 2019-01-28 | Planio 6425
# Removed: usagepoint.deactivate.info.message=This usage point was deactivated by {0} on the {1} for the following reason: <br/> {2}
usagepoint.deactivate.info.message=This usage point was deactivated by {0} on the {1} 
usagepoint.deactivate.info.message.reason= for the following reason: <br/> {0}

# rfowler: 2019-02-08 | Planio 6141
search.customer.phone1.number=Phone 1
search.customer.phone2.number=Phone 2
search.customer.phone.number=Phone Number
search.customer.custom.textfield1=Custom Field 1

search.location.header=Location Search
search.location.erf.number=Erf Number
search.location.building.name=Building Name
search.location.suite.number=Suite Number
search.location.address1=Address Line 1
search.location.address2=Address Line 2
search.location.address3=Address Line 3
search.location.type=Location Search Type
search.location.type.label=Customer/Usage Point Location
search.location.type.customer=Customer Location
search.location.type.usagepoint=Usage Point Location

# joelc: 2018-01-10 | planio-6324:Samoa - Reason for reactivation of usage point
error.field.reasonname.range=Reason name is required.
error.field.reasontext.range=Reason text is required.
specialaction.auto.deactivate.usagepoint=Deactivated as usage point is no longer complete (missing customer or meter) 
specialaction.auto.activate.usagepoint=Activated after completing required usage point data.

#  zachv: 2019-01-02 | Planio 5936
# Removed: ndp.schedule.abandon.activation.change=Schedule activation has changed. if you want to keep the setting, choose No and save / update first. Abandon setting?
question.close.tabs.dirty=All tabs will be closed, but some have unsaved changes. Do you want to discard those changes? 

# joelc: 2018-12-12 | planio-6324:Samoa - Reason for reactivation of usage point
usagepoint.activate.enter.reason=Enter a reason for this activation
usagepoint.activate.select.reason=Select a reason for this activation
special.action.reason.error.save.duplicate=This reason has already been added.

# renciac: 2018-12-06 | planio-6282
error.field.value.boolean=Value must be true or false
error.field.value.location.level=Location Group Type is NOT required. This setting cannot be set to true.

# thomasn: 2018-11-29 | planio-5296
auxaccount.upload.invalid.duplicate=Duplicate AuxAccount the (accountName & agreementRef) combination already exist!.

#RobertF 2018-11-21 Planio-6142 : [MMA] CoCT - Transaction History Bar Graph
transaction.history.graph.title=Transaction History
transaction.history.graph.description=Transactions per month for last 12 months.
transaction.history.graph.xaxis.label= Month
transaction.history.graph.yaxis.label= Number of Transactions
transaction.history.graph.series.label= Transactions count

# joelc 20 November 2018, Planio 4328
question.custom.field.used= Note that any group entity records using this field will NOT be updated, \
  only the list of available options will be updated for future use. 
question.custom.field.used.option.yes=Update List
question.custom.field.used.option.no=Cancel

# thomasn: 2018-11-12 | planio-6168
customer.title.mr=Mr
customer.title.mrs=Mrs
customer.title.ms=Ms
customer.title.miss=Miss
customer.title.doc=Dr
customer.title.prof=Prof
customer.title.sir=Sir

customer.email=Email Address

# joelc: 2018-10-29 | planio-6105: usage point meter download and print for Mayotte
print.customer.contract=Download Customer Contract
print.customer.contract.auxtype=Aux Type
print.customer.contract.auxname=Account Name
print.customer.contract.principleamount=Principle Amount
print.customer.contract.balance=Balance
print.customer.contract.status=Status
print.customer.contract.signature=Customer Signature
print.customer.contract.signature.date=Date

# thomasn: 2018-10-17 | Planio 5296
auxaccount.upload.balanceType=Balance Type
auxaccount.upload.invalid.balance.amount=Balance Amount must be positive
auxaccount.upload.invalid.balancetype=Invalid Balance Type must be debt/refund.

# robertf: 2018-10-09 | Planio 5955
meter.txn.user.ref=User Ref
engineering.token.user.reference.txtbx.label=User Reference
engineering.token.user.reference.txtbx.label.help=Enter user reference for engineering token issue.

# Patrickm: 2018-11-09 | planio-6192: GIS data upload for BVM
link.metadata.upload=Upload Metadata
metadata.upload.heading=Metadata Upload
metadata.upload.data.title=Gis Metadata
metadata.upload.description=Select the JSON file containing the metadata to import into the Meter Management system.
metadata.upload.error.object.creation=Error creating {0} object. Contact Support!
metadata.upload.select.file.help=Select a file containing the metadata in the specified json format for importing into the system
metadata.upload.button=Upload metadata
metadata.lat.label=Latitude
metadata.lon.label=Longitude
metadata.lon.help=Longitude metadata for this UP Group
metadata.lat.help=Latitude metadata for this UP Group
metadata.gis.saved=Successfully saved GIS information for group: {0}
metadata.gis.error.invalid.lat=Invalid latitude value. Coordinate out of range of valid GIS coordinates.
metadata.gis.error.invalid.lon=Invalid longitude value. Coordinate out of range of valid GIS coordinates.

# Patrickm: 2017-12-14 | planio-5041 : Add configurable switch to device stores to determine the response of meters in the store to polling requests
devicestore.field.store_vendors_meter=Device store holds meters that have moved to another vendor
devicestore.field.store_vendors_meter_help=Whether the device store holds meters that have moved to another vendor.
devicestore.field.store_vendors_meter_help2=The fields '{0}' and '{1}'  are mutually exclusive. Therefore, you cannot have a custom message and check the box
devicestore.field.custom_message=Device Store Custom Response Message
devicestore.field.custom_message_help=The message to be shown to users when users query for meters stored in this device store.
devicestore.meters.save.dialog=This meter will be saved in '{0}' store. Please confirm this operation.
devicestore.meters.move.dialog=This meter will be moved to '{0}' store. Would you like to perform this operation?
devicestore.meters.fetch.dialog=You are fetching a meter from another vendor. Would you like to continue with this operation?

# thomasn: 2018-10-23 | planio-5956 [MMA] Add an alphanumeric SAP reference number when removing or replacing a meter on a usage point and display it in the history tables
usagepoint.hist.reason=Meter Remove/Reassign Reason

# robertf: 2018-10-01 | Planio 5954
meter.txn.powerlimits=Power Limit
meter.powerlimit.units.w=Power Limit (W)

# Renciac: 2018-09-04 | planio-5466 : Add manual reversal
button.vend.reversal=Reverse Vend
vend.reversal.confirm=Confirm Vend / Topup reversal?
vend.reversal.connection.error=No response received from service. Please refresh the page.
vend.reversal.error=Vend / Topup error: {0}
vend.reversal.fail=Only the last Vend can be reversed.
vend.reversal.success=Successful Vend Reversal. Original Ref= {0}, Reversal Ref={1}
vend.trans.already.reprinted=This transaction was been reprinted {0} time/s. First reprinted on {1} by {2}. \n
vend.reprint.null.token=Token is null. Reprint not possible. 
vend.reversed=Vend has been reversed.
meter.txn.reprint.date=Last Reprint
meter.noshow.token=Reprint for token 
vend.reprint.user.not.known=Not Known

# thomasn: 2018-09-04 | Planio 5476
readings.table.receiptnum=Receipt Number 

# thomasn: 2018-09-03 | Planio 5296
customer.auxaccount.amount.pos=A positive amount indicates a REFUND
customer.auxaccount.amount.neg=A negative amount indicates a DEBT
customer.auxaccount.title=Auxiliary Account
customer.auxaccount.balance.type.debt=Debt
customer.auxaccount.balance.type.refund=Refund
customer.auxaccount.balance.type.error.required=You must select one.
customer.auxaccount.error.balance=Balance cannot be negative

# Thomas: 2018-08-29 | planio-5475 Time of use calendar allows 00:00 as end time for day profile but marks as incomplete
calendar.assign.period.end.maximum=End of day value is 23:59
calendar.assign.period.end.help=The time that the period ends. End of day value is 23:59

# Renciac: 2018-07-26 | planio-5451 Enabling STS 6
meter.token.code3=Token code 3
meter.token.code4=Token code 4
base.date.label=Base Date:
base.date.label.help=Base date used for generation of STS6 tokens for this meter.
meter.three.tokens=Three Key Change tokens required
meter.three.tokens.help=For STS algorithm code = 07, some meters can store the STS info ON the meter and need a third keyChange token to supply the information.
meter.three.tokens.error=Three Tokens setting is only applicable to STS Algorithm Code = 07
meter.clear.tid=TID reset

# zachv: 2018-08-22
tariff.field.samoa.debt_charge=Debt Charge
tariff.field.samoa.debt_charge.help=Charged per unit
tariff.field.samoa.energy_charge=Energy Charge
tariff.field.samoa.energy_charge.help=Charged per unit

# robertf: 2018-07-27: #5347 Community Group used feature indicator
usagepointgroups.indicator.thresholds.tooltip = Group has custom customer account thresholds
usagepointgroups.indicator.ndp.tooltip = Group has custom NDP schedule

# zachv: 2018-06-26
tariff.field.kenya.monthly = Fixed Monthly Charge
tariff.field.kenya.monthly.help = Fixed charge applied per month.
tariff.field.kenya.fuel = Fuel Cost Charge
tariff.field.kenya.fuel.help = Variable rate per kWh, published monthly by KPLC. VAT is applied to this charge.
tariff.field.kenya.forex = Forex Charge
tariff.field.kenya.forex.help = Foreign exchange rate fluctuation adjustment (FERFA). Variable rate per kWh, published monthly by KPLC.
tariff.field.kenya.inflation = Inflation Adjustment
tariff.field.kenya.inflation.help = Variable rate per kWh, published monthly by KPLC.
tariff.field.kenya.erc = ERC Levy
tariff.field.kenya.erc.help = Rate per kWh.
tariff.field.kenya.rep = REP Levy
tariff.field.kenya.rep.help = Percentage of base rate
tariff.field.kenya.warma = WARMA Levy
tariff.field.kenya.warma.help = Variable rate per kWh, published monthly by KPLC.

# RobertF 4th July 2018. Planio 5714
bulk.upload.enc.key=Encryption Key
meter.enc.key.error=The Meter Model requires a meter encryption key.

#RobertF June 15, 2017 Planio-5787 : Panel with the buying index chart
dashboard.buying.index.graph.title=Buying Index
dashboard.buying.index.graph.month.description=Buying Index: Transacting Meters / Active Meters (%)
dashboard.buying.index.graph.xaxis.label=Month
dashboard.buying.index.graph.yaxis.label=Buying Index

# zachv: 2018-05-04
meter.models.field.needs.encryption.key = Needs Encryption Key
meter.models.field.needs.encryption.key.help = Whether a meter encryption key needs to be entered for this meter model.
meter.encryptionkey=Encryption Key
meter.encryptionkey.help=The encryption key for this meter, for example to decrypt data received from a meter data collector
meter.encryptionkey.error=The Meter Model requires a meter encryption key.
meter.metermodelchange.remove_fields.question=Changing this meter model will cause the following fields to be cleared: {0} . Continue?
meter.model.unset.encryption.key.error=Cannot change the Encryption Key requirement - there are already meters with encryption keys using this meter model.

# Thomas: 2018-05-10 | planio-5502 : MDC messages Override Button needs info message
mdc.txn.override.help=An override message will be sent as a priority message that will take precedence over other messages that may be pending against the meter and certain validation checks will not be applied such as validation of non-disconnect periods and message ordering.

# Thomas: 2018-04-16 | planio-5495 : OpenWayTransaltor, send text message to meter display
meter.models.field.message.display=Supports display messages
meter.models.field.message.display.help=This indicates whether this meter model supports displaying messages on the meter. E.g. Low balance message

# zachv: 2018-04-09 | planio-5512 : MeterMng to support reading multiplier for mdc channel
channel.field.reading_multiplier=Reading Multiplier
channel.field.reading_multiplier.help=Multiplier applied to reading to normalize it into the correct data type. For example a water meter may give readings in pulses needing a multiplier such as 0.5 pulses per liter. Not supported by all mdc components.

# Patrickm: 2018-03-22 | planio-5438 : Display meter location on Map
meter.location=Meter Location
usagepoint.location=Usage Point Location

# Renciac: 2018-02-28 | Planio 5380 : Non_billable meters
customer.agreement.billable.help=Set to false  (unchecked) for Customer Agreements that are for consumption smart meters purely for measuring usage, not for purchasing. Readings for these meters are uploaded and used for Usage Graphs on, for eg. the EnergyInsight website.
customer.agreement.billable=Billable
customer.agreement.billable.setting.check=You have set the customer agreement billable value to {0}. Please confirm the value. If set to true, this account can be topped up, if set to false it is purely for usage consumption purposes.
bulk.upload.billable=Billable
error.field.upload.billable.invalid=Billable can only be true or false.

# Renciac: 2018-01-24 | planio-5210 : PayTypeDiscounts & Add vat inclusive / exclusive to help messages
tariff.field.percent_charge.help=Optional percentage charge that would be taken off from the tendered amount as the first step in the tariff calculation. (Includes Tax).
tariff.field.discount.help=Discount to be applied per payment type. (Includes Tax). Leave blank if no discount applicable for a payment type.
tariff.field.discount.blockThin.help=Discount to be applied per payment type. (Excludes Tax). Leave blank if no discount applicable for a payment type.
tariff.field.unitprice.help=Price per kWH (Excludes Tax).
tariff.field.block.help=Specify up to eight block's unit price (excluding Tax) and threshold below. Leave the unit price and threshold blank for unused blocks.
tariff.field.unitprice.namibia.help=Price per kWH (excludes Levies & excludes Tax)
tou.thin.field.monthlydemand.help=Specify the Monthly Demand Charge amount. (Excludes Tax).
tou.thin.field.servicecharge.help=Specify the Service Charge's amount. (Excludes Tax).
tou.thin.field.charges.help=Capture a tariff rate for each of the available Season, Period and Reading Type combinations. (Excludes Tax).
tou.thin.field.charges.specialday.help=Capture any relevant tariff rates for each of the special days below. (Excludes Tax).
register.reading.rates.help=Capture a tariff rate for each of the selected billing determinants. (Excludes Tax).

# rfowler : 2018-01-23 : Planio 4756
bulk.upload.file.no.meterupdata=No meter/usage point data present in csv upload file.

# Patrick: 2017-11-27 | planio-5127 : Capture and display power limit of a meter
meter.power_limit.instructions=For Power Limit Tokens:\n1. Open the Meter Panel.\n2. In the Power Limit information block, Select the power limit from the Power Limit suggestion box.\n3. This will add a new listbox with Power Limit options - select what you require.\n4. Upon saving the meter, a popup box will be shown to input further details.
meter.power_limit.container_label=Power Limit Information
meter.power_limit.token.generate=Generate Power limit Tokens?
meter.power_limit.token.generate.help=If the meter needs to be updated to match the new Power limit details then a power limit token needs to be generated. If the record is being updated to match the meter's details then there is no need to generate tokens.
tokens.power_limit.no_gen=Do not generate power limit tokens
tokens.power_limit.gen=Generate power limit tokens

# joelc 11 January 2018, Planio 4630
grouptree.search=Search

# joelc 3 January 2018, Planio 4627
error.group.contains.usagepoint=Group currently contains usage points and cannot be deleted.

# joelc 13 December 2017, Planio 4631
group.error.name.nonunique=Group name must be unique.

# 2017-11-20 | planio-5134 : Supplier Group Code validation issue
error.field.supplygroupcode.size=Code must be equal to {0} digits.

# joelc 13 November 2017, Planio 4636
meter.use.existing.instructions=Copy groups from existing meter
meter.not.in.groups=Meter has not been assigned to any groups
meter.copy.selected.groups=Copy selected groups to main screen

# RobertF 23 October 2017, Planio 4755
bulk.upload.gencsvtemplate.title=Generate Upload Template
bulk.upload.gencsvtemplate.subtitle= Required fields have been selected and cannot be toggled.
bulk.upload.file.button.gentemplate=Generate Template
bulk.upload.template.required.first=Info: Required
bulk.upload.template.required=Required
bulk.upload.template.sts.required=Required for STS meters
bulk.upload.recordstatus=Active(blank/no)
bulk.upload.installationdate.format=Install Date (yyyy-MM-dd HH:mm:ss)
bulk.upload.file.button.gentemplate.description=Select the fields you require and generate your upload template:

# Rencia 29 August 2017, Planio 4928
meter.attached.to.up=Meter {0} is attached to Usage Point {1}

# Thomas 7th August 2017. Planio 4815
tariff.field.minvendamount.lbl=Min Vend Amount
tariff.field.minvendamount.help=Minimum vend amount MUST be provided if tariff allows for vend of less than one unit. This will bypass the check that a vend must be for at least ONE whole unit.

# Patrick | July 19, 2017 | planio-4648: When save new Aux acc, confirm status
auxaccount.checkbox.active.status=Your Auxiliary account is not active by default. Would you like to activate this account?

# Thomas 18th July 2017 Planio 4353 MDCTrans Override UI
mdc.txn.override.lbl=Override
mdc.txn.override.none=None
mdc.txn.override.all=All
mdc.txn.override=Override

#Thomas 17th July 2017 Date validation Planio-4644
error.field.datetime.invalid=Invalid date format. Expected format ({0})

#Rencia 24 May 2017 Improve Cell phone validation
customer.trans.upload.invalid.up.no.meter=Usage Point does not have a meter attached to it
customer.trans.upload.invalid.up.not.active=Usage Point is not active

#RobertF July 14, 2017 Planio-4421 : Meter Management: Panel to monitor vending activity
dashboard.vending.activity.graph.title=Vending activity
dashboard.vending.activity.graph.description=Total vends over last 15 minute interval
dashboard.vending.activity.graph.xaxis.label= Vends
dashboard.vending.activity.graph.yaxis.label= 15 minute intervals (click chart to reset zoom)

### July 13, 2017 : RobertF : Indicator Dashboard Panel ###
dashboard.key.indicator.indicator.total.sales=Total sales
dashboard.key.indicator.indicator.tooltip.total.sales=Total sales
dashboard.key.indicator.indicator.transactions=Transactions
dashboard.key.indicator.indicator.tooltip.transactions=Amount of transactions occuring to give total sales
dashboard.key.indicator.indicator.transacting.meters=Transacting meters
dashboard.key.indicator.indicator.tooltip.transacting.meters=Number of meters transacting to give total sales
dashboard.key.indicator.indicator.new.meters.installed=New meters installed
dashboard.key.indicator.indicator.tooltip.new.meters.installed=New meters that have been associated to usage points
dashboard.key.indicator.indicator.new.active.meters.installed=New ACTIVE meters installed
dashboard.key.indicator.indicator.tooltip.new.active.meters.installed=New meters that have been associated to usage points and are active
dashboard.key.indicator.indicator.total.meters.usage.points=Total meters (Usage points)
dashboard.key.indicator.indicator.tooltip.total.meters.usage.points=Total meters assigned to usage points
dashboard.key.indicator.indicator.total.active.meters.usage.points=Total ACTIVE meters (Usage points)
dashboard.key.indicator.indicator.tooltip.total.active.meters.usage.points=Total meters assigned to usage points and are active
dashboard.key.indicator.indicator.total.meters.device.store=Total meters (Device store)
dashboard.key.indicator.indicator.tooltip.total.meters.device.store=Total meters in device store

#RobertF June 22, 2017 Planio-4420 : Panel with the sales per resource chart
dashboard.sales.per.resource.graph.title=Sales per resource
dashboard.sales.per.resource.graph.day.description=Resource sales per day
dashboard.sales.per.resource.graph.month.description=Resource sales per month
dashboard.sales.per.resource.graph.xaxis.day.label=Date (click on chart for month view)
dashboard.sales.per.resource.graph.xaxis.month.label=Date (click on chart for day view)
dashboard.sales.per.resource.graph.yaxis.label=Total sales

# Thomas 21 June 2017 UP Blocking
usagepoint.field.blocking.help=Select the blocking type for the usage point.
usagepoint.field.blocking.label=Blocking Type
usagepoint.field.blocking.type.default=Not blocked
usagepoint.blocking.enter.reason=Enter a reason for blocking
usagepoint.blocking.select.reason=Select a reason for blocking

#RobertF June 2, 2017 Planio-4419 : Panel with the count regarding the Owner and Building usage point groups added over time
dashboard.groups.added.graph.title=Usage point groups added
dashboard.groups.added.graph.day.description=Groups added per day
dashboard.groups.added.graph.month.description=Groups added per month
dashboard.groups.added.graph.xaxis.day.label=Date (click on chart for month view)
dashboard.groups.added.graph.xaxis.month.label=Date (click on chart for day view)
dashboard.groups.added.graph.yaxis.label=Groups added

# Joel 30 May 2017  Centian Data display planio 4429
meter.centian.header=Centian Meter Information
meter.centian.kwh.credit.remaining=kWh Credit Remaining:
meter.centian.currency.credit.remaining=Currency Credit Remaining:
meter.centian.number.disconnections=Number of Disconnections:
meter.centian.tamper.detected=Tamper states detected:
meter.centian.tamper.none=No Tamper states have been detected
meter.centian.tamper.updated=Date Info retrieved:
meter.centian.tamper.overpower=Over Power
meter.centian.tamper.overvoltage=Over Voltage
meter.centian.tamper.lowvoltage=Low Voltage
meter.centian.tamper.overfrequency=Over Frequency
meter.centian.tamper.lowfrequency=Low Frequency
meter.centian.tamper.reverseenergy=Reverse Energy
meter.centian.tamper.opencover=Open Cover
meter.centian.tamper.magnettamper=Magnet Tamper
meter.centian.tamper.bypassearth=Bypass/Earth Tamper
meter.centian.tamper.sequenceerror=Sequence Error
meter.centian.tamper.overtemperature=Over Temperature
meter.centian.tamper.lowtemperature=Low Temperature
meter.centian.tamper.phaseunbalance=Phase Unbalance
meter.centian.tamper.phasevoltageloss=Phase Voltage Loss
meter.centian.tamper.tariffconfigerror=Tariff Configuration Error
meter.centian.tamper.metrologyfail=Metrology Fail

meter.centian.current.tamper.status.header=Tamper Status for Meter
meter.centian.current.tamper.status.description=Tamper states with a tick have been detected on this meter.
meter.centian.current.tamper.status.description.none=No Tamper states have been detected on this meter.
meter.centian.current.tamper.status.updated=Tamper status updated:

#Rencia 24 May 2017 Improve Cell phone validation
meter.online.bulk.customer.phone.help=Enter the customer's phone number to which a Free Issue Token will be sms-ed, if applicable. SMS numbers must be in International Format, starting with a +.
messaging.recipient.help=For SMS, must use international phone numbers.
cellPhone.pattern.description=The phone number field for smses must be in international format, i.e. start with a +. You may use whitespaces, parentheses (), hyphens and periods - these are stripped out and the resulting phone number (excl. the +) must be minimum 4 digits, maximum 25 in length, depending on your locale. Refer the placeholder.

### May 17, 2017 : RobertF : Admin dashboard workspace ###
admin.dashboard.title=Dashboard (Admin)

# Patrick | May 15, 2017 | planio-4443 - Updates for help message from field behaviour change
meter.algorithmcode.help=Select the correct algorithm code. This field is required for activation - the meter cannot be saved unless an algorithm code is selected.
meter.tokentechcode.help=Select the correct token tech code. This field is required for activation - the meter cannot be saved unless a token tech code is selected.
meter.supplygroupcode.help=Enter the current supply group code. This field is required for activation - the record cannot be saved without it.
meter.tariffindex.help=Enter the current tariff index. This field is required for activation - the record cannot be saved without it.

#Thomas 8th May 2017 Tariff Date validation
tariff.error.startdate.unique=Duplicate start date. Specify a unique start date.
tariff.error.startdate=Start Date must be in the future and greater than the start date of currently active tariff.

### May 2, 2017 : RobertF : Indicator Dashboard Panel ###
dashboard.key.indicator.title=Key Indicators
dashboard.key.indicator.description=Key system indicators over various time frames.
dashboard.key.indicator.indicator=Indicator
dashboard.key.indicator.value.today=Today
dashboard.key.indicator.value.monthtodate=Month to date
dashboard.key.indicator.value.lastmonth=Last month

#Rencia 24 April 2017 Meter Bulk Online Search / Capture : Free Issue Token
meter.online.bulk.free.issue.title=Free Issue Token
meter.online.bulk.free.issue.generate=Generate Token
meter.online.bulk.free.issue.sms.token=Sms token to tenant
meter.online.bulk.free.issue.sms.token.help=Select here if you want the Free Issue token to be sms-ed to the tenant?
meter.online.bulk.free.issue.check.sms.not.selected=You have elected to generate a Free Issue Token but NOT to send it via SMS.
meter.online.bulk.free.issue.sms.invalid.phone=To SMS a token, must have a valid cellphone number
meter.online.bulk.free.issue.invalid.units=Number of Free Issue units must be numeric and greater than zero
meter.online.bulk.free.issue.sms=Free issue token {2}: Meter Number: {0}  Token: {1}
meter.online.bulk.free.issue.token.null=Meter has been added to the group but Unable to generate the Free Issue Token.
meter.online.bulk.free.issue.token.error=Meter has been added to the group but Token Error: {0}
credit.token.link=View Credit Tokens
eng.token.link=View Engineering Tokens

#Rencia 21 April 2017 Meter Bulk Online Search / Capture : Edit / Remove buttons in table
meter.online.bulk.no.edit=Only certain fields on ACTIVE usage points can be edited here. Use link to go to Usage Point page for others.
meter.online.bulk.no.remove=Usage Point has no meter to remove. Use link to go to Usage Point page.
button.clear.panel=Clear Panel
button.clear.groups=Clear Groups
online.bulk.panel.tariffindex.help=Enter the current tariff index. This field is required for activation. To edit an already existing meter, use the UsagePoint Page.
online.bulk.panel.supplygroupcode.help=Enter the current supply group code. This field is required for activation. To edit an already existing meter, use the UsagePoint Page.
error.field.breakerid.max=BreakerId may not exceed maximum 100 characters.
meter.online.bulk.meter.updated=Meter {0} updated

### April 11, 2017 : Patrick : Send Reprint ###
button.send_reprint=Send reprint
messaging.type.sms=Sms
messaging.type.email=Email
messaging.recipient=Recipient
messaging.message.type=Message Type
messaging.message.label=Message
messaging.token.reprint.email.subject=Token Reprint Request
token.label=Token
error.field.required.recipient.email=Recipient's email address is required
error.field.required.recipient.phone=Recipient's phone number is required
error.field.validity.phone=Phone number is invalid.

notification.message.send.status.sms=SMS Message Sent successfully
notification.message.send.status.email=Email Message Sent successfully

messaging.txn.date=Transaction Date
messaging.txn.meter_no=Meter Number
messaging.txn.token=Token

#Rencia 31 March 2017 Meter Meter Bulk Online Search / Capture : Popup more information when click on meter in table
more.info=More Information - Drag Here
popup.label=Field
popup.value=Value

#Rencia 26 March 2017 Meter Meter Bulk Online Search / Capture : Add pricing Structure Tariff Popup
online.bulk.panel.tariff.title=Current Tariff
meter.online.bulk.add.group.title=Add / Edit Groups

#Robertf 27 March 2017 Usage Point page check for valid installation date
error.field.startdate.invalid=Start Date is not a valid date. Format = {0}

#Rencia 26 March 2017 Meter Meter Bulk Online Search / Capture : Add New Group function & phone no
customer.phone=Phone Number


#Rencia 10 March 2017 Meter Bulk Online Search / Capture add group entry function
grouptype.field.layout.order=Layout Order
grouptype.field.layout.order.help=This is the order in which Usage Point Group Selection boxes are displayed on the page. If not entered will default to AFTER all the numbered ones, in alphabetical name order.
grouptype.field.layout.order.error.numeric=Layout order must be a numeric integer.
grouptype.field.layout.order.error.duplicate=Layout order is a duplicate. Same as group {0}.
error.field.installdate.invalid=Installation Date is not a valid date. Format = {0}

#Rencia 27 February 2017 Meter Bulk Online Search / Capture v1
meter.online.bulk.header=Add Meters to Group/s
meter.online.bulk.title=Select or Add Group/s
meter.online.bulk.installdate=Installation Date
meter.online.bulk.select.meters.button=Select Meters for Group/s
meter.online.bulk.usagepoint.status=Status
meter.online.bulk.search.no.results=No matching search results were found.
meter.online.bulk.add.meters.to.groups=Add Meters to Group/s
meter.online.bulk.button.add=Add Meter
meter.online.bulk.add.meter=Add Meter
meter.online.bulk.edit.meter=Edit Meter

online.bulk.panel.up.group.info.title=Usage Point Information
online.bulk.panel.customer.info.title=Customer Information
online.bulk.panel.meter.help=Start typing the meter number, meters that start with those digits for the selected device store will appear in a dropdown. Click on one to select it.
online.bulk.panel.select.store.help=The store from which meters will be selected. If none selected, meter numbers from all stores will be shown in the meter suggestions. Note that when the meter is assigned to a Usage Point it will be automatically removed from the store.
online.bulk.panel.suite.no.text=Unit
online.bulk.panel.tenant.text=Tenant
online.bulk.panel.surname.help=Can enter the tenant's surname. Will default to numeric sequence. This is a required field - usage point cannot be activated without it.

online.bulk.panel.error.supply.grpcode.empty=For STS meters, Supply Group Code must be selected
online.bulk.panel.error.algorithm.code.empty=For STS meters, Algorithm Code must be selected
online.bulk.panel.error.token.tech.code.empty=For STS meters, Token Tech Code must be selected
online.bulk.panel.error.tariff.indx.empty=For STS meters, Tariff Index must be entered and max length 2
online.bulk.panel.error.key.rev.indx.empty=For STS meters, Key Revision Code must be entered and must be numeric
online.bulk.panel.error.ps.meter.model.empty=Meter Model is required for Pricing Structure selection
online.bulk.panel.error.model.new.pricingstructure.required=Meter Model does not support the pricing structure.
online.bulk.panel.error.meter.num.not.found=Meter Number is not on Database.
online.bulk.panel.error.meter.already.linked=Meter Number is already linked to Usage Point.
online.bulk.panel.error.meter.linked.to.diff.store=Meter Number is in a different store, {0}.
online.bulk.meter.error.groups.not.selected=For saving a meter all required Usage Point Groups must be selected.

question.confirm.continue.new=Capture as new meter for this grouping?
question.confirm.continue.open.up.page=Open Usage Point tab for it?
question.confirm.continue.save.anyway=Continue and link to this grouping?

meter.key.revision.help=Enter the current Key Revision no. This field is required for activation.
meter.key.revision=Key Revision

online.bulk.sts.meter.save.error=Problem saving STS Meter, codes not found.
online.bulk.meter.save.error=Problem saving Meter.

meter.sts.length=STS meter numbers must be between 11 and 13 characters long.

#Njigi 3 March 2017
usagepoint.charge.button.writeoff=Writeoff Charges
usagepoint.charge.button.upchargeview=View Outstanding Charges
usagepoint.charge.view.dialog.heading=View Outstanding Charges
usagepoint.charge.no.data=No data is available to be viewed.
usagepoint.charge.view.dialog.nodate.filter=Please select date.
usagepoint.charge.view.dialog.invalid.date=Date selected should be after last cyclic date displayed
usagepoint.charge.view.dialog.invalid.date2=Date selected cannot be in future
usagepoint.charge.writeoff.trans.success=Writeoff processed successfully.
usagepoint.charge.writeoff.trans.failure=Writeoff processed failed. Contact support.
chargewriteoff.save.error=Saving of charge writeoff record failed.

#Njigi 30 January 2017 ####
auxaccount.trans.upload=Aux Transaction Upload
auxaccount.trans.upload.heading=Aux Transaction Upload
auxaccount.trans.upload.auxaccountname=Aux Account Name
auxaccount.trans.upload.agreementref=Agreement Ref
auxaccount.trans.upload.data.title=Import Aux Account Balance Adjustment Transactions
auxaccount.trans.upload.data.description=Select the CSV file containing the Aux transactions for importing into the Meter Management system.
auxaccount.trans.upload.invalid.auxaccountname=Aux Account Name maximum 100 chars
auxaccount.trans.upload.invalid.auxaccount=No Aux Account with the provided Name and Agreement Ref exist
auxaccount.trans.upload.process.failed=System Error on transaction:AuxAccount Name= {0}, Agreement Ref= {1}. Try resubmitting the file.
trans.bulk.upload.amt.incl.tax=Amt incl Tax
trans.bulk.upload.amt.tax=Tax Amt
trans.bulk.upload.trans.date=Transaction date
trans.bulk.upload.account.ref=Account Reference
trans.bulk.upload.comment=Comment
trans.bulk.upload.invalid.amt.incl.tax=Amount incl. Tax is not numeric
trans.bulk.upload.invalid.amt.tax=Tax Amount is not numeric
trans.bulk.upload.invalid.account.ref=Account Reference maximum 100 chars
trans.bulk.upload.invalid.comment=Comment maximum 255 chars
trans.bulk.upload.invalid.our.ref=Our Reference maximum 100 chars (Rename file)
trans.bulk.upload.trans.date.in.future=Transaction date may not be in the future

#Njigi 16 January 2016 ####
auxaccount.upload=Auxiliary Account Upload
auxaccount.upload.heading=Auxiliary Account Upload
auxaccount.upload.data.title=Import Auxiliary Account Data
auxaccount.upload.data.description=Select the CSV file containing the Auxiliary Account data for importing into the Meter Management system.
auxaccount.upload.errors=Errors
auxaccount.upload.identifierType=Identifier Type
auxaccount.upload.identifier=Identifier
auxaccount.upload.auxaccountname=Aux Account Name
auxaccount.upload.auxtype=Aux Type Name
auxaccount.upload.accountpriority=Account Priority
auxaccount.upload.chrgschdlname=Charge Schedule Name
auxaccount.upload.principleamaount=Principle Amount
auxaccount.upload.balance=Balance
auxaccount.upload.customerref=Customer Agreement Ref
auxaccount.upload.invalid.identifiertype=IdentifierType must be agreementRef / usagePointName / meterNumber
auxaccount.upload.invalid.identifier=Invalid Identifier - not in database
auxaccount.upload.invalid.agreement=Usage Point does not have a customer agreement in place
auxaccount.upload.invalid.usagepoint.or.agreement=Meter has no usage point or the usage point has no agreement
auxaccount.upload.invalid.auxaccountname=Aux Account Name maximum 100 chars
auxaccount.upload.invalid.principleamaount=Principle Amount is not numeric
auxaccount.upload.invalid.balance=Balance Amount is not numeric
auxaccount.upload.successful.count=Total of {0} Aux Account uploads successfully processed.

#Antonyo 12 January 2017  ####

power.limit.add.button.prompt=Add Power Limit
power.limit.table.label.header=Power Limit Name
power.limit.table.value.header=Power Limit Value
power.limit.edit.popup.header=Edit Power Limit

#Patrick 02 December 2016 ####
user.custom.field.datatype.text=Text
user.custom.field.datatype.numeric=Numeric
user.custom.field.datatype.date=Date
user.custom.field.datatype.boolean=True/False
user.custom.field.datatype.list=List
button.done=Done

usagepointgroup.custom.field.error.text=A value is required for this field
usagepointgroup.custom.field.error.list=You must select an item from the list
usagepointgroup.custom.field.error.date.empty=A valid date is required. Format (yyyy-MM-dd HH:mm:ss)
usagepointgroup.custom.field.error.date.invalid=Invalid date format. Format (yyyy-MM-dd HH:mm:ss)
usagepointgroup.custom.field.error.numeric=A valid number must be provided

button.view.usagepointgroup=Show Details

usagepointgroup.custom.field.default.datatype.description=The dataType for this custom field, or a comma-seperated list for dropdowns
usagepointgroup.hierarchy.no.additionalinfo=No additional information for this UsagePoint Group

appsettings.popup.button.add=Add Item
appsettings.popup.table.add=New Item - Click to Edit
appsettings.popup.table.title=List Items
appsettings.popup.table.note.dups=Duplicate items will be removed when Done

appsetting.field.datatype=Datatype
appsetting.field.datatype.help=Datatype for the Application Setting. For Datatype List, List items will be discarded when data type is changed to: Text, Numeric, Date, or True/False.
appsetting.field.datatype.listitems=Edit Items

appsettings.list.duplicate.item=Duplicate item entered.

#Njigi 22 December 2016 ####
bulk.upload.custom.fields.get.error=Database error getting custom field application settings! Contact Support.

#Njigi 28 November 2016 ####
bulk.upload.meterupload.heading=Meter Bulk Upload
bulk.upload.meterupload.enddevicestorename=Device Store Name
bulk.upload.meterupload.data.title=Import Meter Details
bulk.upload.meterupload.description=Select the CSV file containing the meter details for importing into the Meter Management system.

#Special Actions - joelc 01/12/16
link.special.actions=Actions with reasons
link.special.action.reasons=Special Action Reasons
button.viewreasons=View reasons list
special.action=Special action
special.actions.header=Configure Special Actions
special.actions.title=Update Special Actions
special.action.name=Action Name
special.action.name.help=The name of this special action.
special.action.reason.required=Required
special.action.reason.required.help=Is it required or optional to supply a reason
special.action.reason.input.type=Input Type
special.action.reason.input.type.help=Is the reason given as free text, is it selected from a list of reasons or is it either free text and/or selected from a list
special.action.description=Action description
special.action.description.help=A description of the special action that allows a reason for the action to be supplied
special.actions.field.name=	Action name
special.actions.field.description=Description
special.actions.field.reason.required=Required
special.actions.field.reason.input.type=Input type
special.actions.reason.inputtype.freetext=Enter reason into textbox
special.actions.reason.inputtype.selected=Select reason from list
special.actions.reason.inputtype.both=Enter reason or select from list

special.action.reasons.header=Setup Reasons
special.action.reasons.title=Special action reasons
special.action.reasons.title.add=Add new reason
special.action.reasons.title.update=Update reason
special.action.reasons.field.name=Reason
special.action.reasons.field.description=Reason description
special.action.reasons.field.recordstatus=Active
special.action.reasons.name=Reason
special.action.reasons.name.help=Enter a possible reason for doing this action. This will be displayed in the drop down list for that action.
special.action.reasons.description=Description
special.action.reasons.description.help=Describe what this reason is about.
special.action.reasons.active=Active
special.action.reasons.active.help=Is this reason active?
special.action.reason=Reason
special.action.and.or=and/or
special.action.enter.reason=Enter a reason for this action
special.action.select.reason=Select a reason for this action
error.special.action.reason.required=A reason for this action is required
usagepoint.deactivate.enter.reason=Enter a reason for this deactivation
usagepoint.deactivate.select.reason=Select a reason for this deactivation

#Rencia 24 November 2016  ####
bulk.upload.pricingstructure.not.active=Pricing Structure is not active.

#Rencia 16 November 2016  ####
error.field.active.invalid=Active must be blank (implies YES) or no.
error.field.metermodelid.null=Meter Model is required.
error.field.ststariffindex.max=Tariff Index must be less than {0} characters.
error.field.servicelocationid.null=Service location is required
error.field.customerkindid.null=Customer Kind
error.field.customerid.null=Customer id is required
error.field.accountbalance.null=Account Balance is required.

#Bulk Upload - standard
bulk.upload.download.sample=Download a sample spreadsheet.
bulk.upload.download.sample.button=Download as .CSV
bulk.upload.file.select.labeltext=Select CSV File to upload
bulk.upload.select.file.help=Select a file containing the information in the specified csv format for importing into the system
bulk.upload.csv.button=Upload CSV Data
bulk.upload.process.button=Process Upload
bulk.upload.errors=Errors
bulk.upload.object.creation.error=Error creating {0} object. Contact Support!
bulk.upload.file.action.unknown=Unknown file upload action, contact Support
bulk.upload.file.none=No file was selected to be uploaded
bulk.upload.invalid.filename=Improper filename - hyphen or period missing. Filenames expected as xxxxxx-reference.csv where xxxxxx is your chosen file identifier, eg. MeterUpload, and <reference> is saved as 'our Ref' on transactions
bulk.upload.invalid.filename.changed=Filename has changed between steps! Was {0}; now {1}
bulk.upload.invalid.cannot.create.dir=ERROR! Cannot create the directory. Please contact Support.
bulk.upload.file.unrecognized.heading.error=Unrecognized Column Heading in upload file: {0}
bulk.upload.table.heading.valid=Valid Upload Data : sample of first 15 lines in file
bulk.upload.table.heading.errors=Upload Data : Errors
bulk.upload.trans.validation.errors=Validation errors found. Please repair and resubmit. Maximum 15 errors are processed at any one time
bulk.upload.filename=Selected filename={0}
bulk.upload.process.failed=System Error on upload after {0} records. Try resubmitting the file.
bulk.upload.active=Active
#Bulk Upload Validation Errors
bulk.upload.invalid.required.field={0} Required
bulk.upload.invalid.nonexisting.field={0} Not on Database
bulk.upload.duplicate.field={0} already exists, either on Database or in this upload file
bulk.upload.invalid.field={0} is invalid
bulk.upload.invalid.parsedate=Cannot parse {0} - check format
bulk.upload.file.process.error=Error while processing the upload file

#Bulk Upload - meter / Customer / Usage Point
bulk.upload.data.title.meter=Import Meter, Usage point and Customer Details
bulk.upload.data.description.meter=Select the CSV file containing the meter & related details for importing into the Meter Management system.
bulk.upload.metertype=Meter Type
bulk.upload.meternum=Meter Num
bulk.upload.serialnum=Serial Num
bulk.upload.mrid=External MeterNum
bulk.upload.metermodelname=Meter Model
bulk.upload.enddevicestore=Device Store
bulk.upload.breakerid=Breaker Id
bulk.upload.ststokentechcode=Token Tech Code
bulk.upload.stsalgorithmcode=Algorithm Code
bulk.upload.stssupplygroupcode=Supply Group Code
bulk.upload.stskeyrevisionnum=Key Rev Num
bulk.upload.ststariffindex=Tariff Indx
bulk.upload.usagepointname=Usage Point Name
bulk.upload.installationdate=Install Date
bulk.upload.pricingstructurename=Pricing Structure
bulk.upload.uperfnumber=UP Erf Num
bulk.upload.upstreetnum=UP Street Num
bulk.upload.upbuildingname=UP Building
bulk.upload.upsuitenum=UP Suite Num
bulk.upload.upaddressline1=UP Addr 1
bulk.upload.upaddressline2=UP Addr 2
bulk.upload.upaddressline3=UP Addr 3
bulk.upload.uplatitude=UP Latitude
bulk.upload.uplongitude=UP Longitude
bulk.upload.upcustomvarchar1=UP Custom Char1
bulk.upload.upcustomvarchar2=UP Custom Char2
bulk.upload.upcustomnumeric1=UP Custom Num1
bulk.upload.upcustomnumeric2=UP Custom Num2
bulk.upload.upcustomtimestamp1=UP TimeStamp1
bulk.upload.upcustomtimestamp2=UP TimeStamp2
bulk.upload.customerkind=Customer Kind
bulk.upload.companyname=Company Name
bulk.upload.taxnum=Tax Num
bulk.upload.firstnames=First Names
bulk.upload.surname=Surname
bulk.upload.initials=Initials
bulk.upload.title=Title
bulk.upload.email1=Email1
bulk.upload.email2=Email2
bulk.upload.phone1=Phone1
bulk.upload.phone2=Phone2
bulk.upload.custerfnumber=Cust Erf
bulk.upload.custstreetnum=Cust Street num
bulk.upload.custbuildingname=Cust Building
bulk.upload.custsuitenum=Cust Suite
bulk.upload.custaddressline1=Cust Addr 1
bulk.upload.custaddressline2=Cust Addr 2
bulk.upload.custaddressline3=Cust Addr 3
bulk.upload.custlatitude=Cust Latitude
bulk.upload.custlongitude=Cust Longitude
bulk.upload.custcustomvarchar1=Cust Custom Char1
bulk.upload.custcustomvarchar2=Cust Custom Char2
bulk.upload.custcustomnumeric1=Cust Custom Num1
bulk.upload.custcustomnumeric2=Cust Custom Num2
bulk.upload.custcustomtimestamp1=Cust Custom Timestamp1
bulk.upload.custcustomtimestamp2=Cust Custom Timestamp2
bulk.upload.agreementref=Agreement Ref
bulk.upload.startdate=Agreement Start Date
bulk.upload.accountname=Account Name
bulk.upload.accountbalance=Account Balance
bulk.upload.lowbalancethreshold=Low Bal Threshold
bulk.upload.notificationemail=Notif Email
bulk.upload.notificationphone=Notif Phone
bulk.upload.notifylowbalance=Notify Low Bal
#Invalid Meter bulk upload errors
bulk.upload.metertype.incompatible.stsinfo=STS encryption details entered for non-STS meter type
bulk.upload.metertype.incompatible.metermodel=Meter Model not compatible with Meter Type
bulk.upload.invalid.pricingstructure.incompatible.metermodel=Pricing structure incompatible with Meter Model
bulk.upload.customer.account.notfor.STS=STS meters should not have Customer Account Info

#Rencia 11 November 2016  ####
tariff.field.namibia.neflevy=NEF Levy
tariff.field.namibia.neflevy.help=Enter National Energy Fund Levy
tariff.field.namibia.ecblevy=ECB levy
tariff.field.namibia.ecblevy.help=Enter Electricity Control Board Levy
tariff.error.positive.or.zero=Value must be positive or zero

#Rencia 9 November 2016  ####
tariff.field.discount=Payment Type Discounts
tariff.field.heading.paytype=Payment Type
tariff.field.heading.discount=Discount %
tariff.field.heading.description=Description
tariff.error.discount=Discount % must be positive or zero.
tariff.error.discount.descrip=If discount % entered, and not zero, description is required.

# Rencia 20 October 2016  ####
### New
tariff.field.cycle.name=Cycle
tariff.field.cycle.name.help=Select the cycle of billing.
tariff.field.cost.help=The amount excluding tax to be paid back as per cycle selection.

### Took out "Monthly"
tariff.field.cost.name=Cost Name
tariff.field.cost.name.help=The name for this cost that will end up on the customer's receipt.
tariff.field.cost=Cost Excl. Tax

### Tariff Cycle Labels  NEW
tariff.cost.cycle.daily=Daily
tariff.cost.cycle.monthly=Monthly
tariff.cost.cycle.error=Must select a valid cycle.

### changed names
tariff.error.cyclic.charge.positive=Must be a positive value.
tariff.error.cyclic.charge=Specify a cost for the name.
tariff.error.cyclic.charge.name=Specify a name for the cost.

#Rencia 22 August 2016  ####
error.field.numeric.positive_not_zero=Value must be positive and not zero.
tariff.field.percent_charge_name=Percentage Charge Name
tariff.field.percent_charge_name.help=Optional name of the percentage charge that will be displayed on the receipt.
tariff.field.percent_charge=Percentage Charge
tariff.error.percent_charge.name=Specify a name for the charge.
tariff.error.percent_charge.positive_not_zero=Must be a positive value and not zero.



##########################
#### General messages ####
##########################

application.default.title=iPay - Meter Management
application.title=Meter Management
workspace.usagepoint.information=Information and Services
unit.kilowatthour.symbol=kWh
unit.watts.symbol=W
unit.percent=%
changes_unsaved=* Unsaved changes
menu.about_link=About
application.info=Powered by iPay's BizSwitch

###############
#### Error ####
###############
error.title=Error
error.general=An error has occurred and has been logged. Please notify your System Administrator.
error.login=Your login attempt was not successful. Please try again.
error.denied=User {0} does not have permission to access this site.
error.accessdenied=You do not have permission to access this functionality.
error.networkConnect=An error occurred while trying to contact server.
error.networkIO=A network communication error occurred.
error.networkTimeout=The server is taking too long to respond.
error.networkUnknown=An unknown error occurred while trying to contact server. Notify administrator.
error.delete=Unable to delete
permission.denied=Permission Denied
permission.edit.denied=Editing Permission Denied
error.no.user=No current user available.
error.current.group=Invalid group specified for the user's current group.
error.meter.accessdenied=You do not have permission to access this meter.
error.pricing.structure.accessdenied=You do not have permission to edit pricing structure allocation.
error.pricing.structure.accessdenied.addmeter=You do not have permission to edit pricing structure allocation, so cannot attach the new meter above to this usage point. The meter has been saved in the selected device store.
error.meter.workspace.error=Error opening meter workspace.
error.customer.workspace.error=Error opening customer workspace.
error.usagepoint.workspace.error=Error opening usage point workspace.
error.no.user.assignedgroup=The current user does not have an assigned group set.
error.login.locked=Your account has been locked.
error.loginerror=Incorrect username or password.
error.login.disabled=Your account has been disabled.

###############
#### Login ####
###############
login.title=Login
login.form.title=Please enter your username and password.
login.form.username=Username
login.form.password=Password
login.form.login=Login
login.form.remember_me=Remember me for two weeks
login.form.password.forgotten=Forgotten your password?
password.form.instructions=Enter your user's registered email address.
password.email.invalid=Invalid email address.
password.email.unknown.user=Unknown user for the entered email address.
password.multiple.users=Multiple users match the entered email address.
password.multiple.users.1=Please select one below and click Submit.
password.reset=Password reset. Please check your email for additional details.
password.error.reset=Error: Unable to reset your password.
adminuser.password.save.error=Error: Unable to save updated user.
email.password.reset.subject=Password Reset
password.form.email=Email Address:
password.form.submit=Submit

#########################
#### Password Change ####
#########################
password_change.title=Password Change
password_change.form.title=Change my password
password_change.form.password1=Enter New Password:
password_change.form.password2=Re-enter New Password:
password_change.form.submit=Submit Change
password_change.success=Password changed for {0}
password_change.validate.equal=Two password entries not equal
password_change.validate.ldap=Ldap authenticated users can't change their passwords. Contact your Ldap System Administrator.

########################################################################################################################
# GWT specific properties #
########################################################################################################################

## Errors
error.save=Error: {0} could not be saved.
error.field.required=Required field.
error.field.is.required={0} is a required field.
error.field.numeric.required={0} is a required, numeric field.
error.no.selection=No valid selection has been made.
error.data.null=Incoming data is invalid.
error.datatype.null=The {0} is invalid.
error.missing={0} {1} not found.
error.numeric.value=Enter a valid numeric value.
error.token.retrieve=Unable to retrieve token.
error.meter.load=Unable to display the meter.
error.usagepoint.load=Unable to display the usage point.

# Field errors used by the Validation framework's annotations
error.field.id.null=ID is required.
error.field.key.null=Key is required.
error.field.key.range=Key must be between {min} and {max} characters.
error.field.name.null=Name is required.
error.field.name.range=Name must be between {min} and {max} characters.
error.field.name.max=Name must be less than {max} characters.
error.field.value.null=Value is required.
error.field.value.range=Value must be between {min} and {max} characters.
error.field.description.null=Description is required.
error.field.description.range=Description must be between {min} and {max} characters.
error.field.description.max=Description must be less than {max} characters.
error.field.recordstatus.null=Record Status is required.
error.field.contactname.null=Contact Name is required.
error.field.contactname.range=Contact Name must be between {min} and {max} characters.
error.field.contactemail=Contact Email must be a valid email address.
error.field.contactemail.max=Contact Email must be less than {max} characters.
error.field.taxref.max=Tax Reference must be less than {max} characters.
error.field.contactnumber.max=Contact Number must be less than {max} characters.
error.supplyserver=The Supply Group's code and revision number combination must be unique.
error.field.supplygroupcode.null=Supply Group Code is required.
error.field.keyrevisionnum.null=Key Revision Number is required.
error.field.supplygroupcode.format=Enter a numeric value.
error.field.supplygroupcode.range=Code must be less than 7 digits.
error.field.calccontents.null=Calc Contents is a required field.
error.field.schedulename.range=Schedule Name must be between {min} and {max} characters.
error.minmax.range.auxchargeschedule=Min Amount must be less than Max Amount.
error.field.meternum.null=Meter Number is a required field.
error.field.meternum.range=Meter Number must be between {min} and {max} characters.
error.field.mrid.null=MRID is a required field.
error.field.mrid.range=MRID must be between {min} and {max} characters.
error.field.serialnum.max=Serial Number must be less than {max} characters.
error.field.meternumchecksum.max=Checksum must be less than {max} characters.
error.field.ststokentechcode.max=Token Tech Code must be less than {max} characters.
error.field.stsalgorithmcode.max=Algorithm Code must be less than {max} characters.
error.field.stsprevsupplygroupcode.max=Supply Group Code must be less than {max} characters.
error.field.stscurrtariffindex.max=Tariff Index must be less than {max} characters.
error.field.addressline1.max=Each Address Line must be less than {max} characters.
error.field.addressline2.max=Each Address Line must be less than {max} characters.
error.field.addressline3.max=Each Address Line must be less than {max} characters.
error.field.city.max=City must be less than {max} characters.
error.field.province.max=Province must be less than {max} characters.
error.field.country.max=Country must be less than {max} characters.
error.field.postalcode.max=Postal Code must be less than {max} characters.
error.field.erfnumber.max=Erf Number must be less than {max} characters.
error.field.streetnum.max=Street Number must be less than {max} characters.
error.field.buildingname.max=Building Name must be less than {max} characters.
error.field.suitenum.max=Suite Number must be less than {max} characters.
error.field.surname.null=Surname is a required field.
error.field.agreementref.null=Agreement Ref is a required field.
error.field.agreementref.duplicate=Duplicate agreement reference {0}. Specify a unique reference.
error.field.email1.max=Email Address must be less than {max} characters.
error.field.email1=Email Address is invalid.
error.field.email2.max=Email Address must be less than {max} characters.
error.field.email2=Email Address is invalid.
error.field.phone1.max=Phone number must be less than {max} characters.
error.field.phone2.max=Phone number must be less than {max} characters.
error.field.phone=Phone number invalid, only 0-9, '+', space, hyphen, 'ext' and 'x' allowed. 'ext' or 'x' must be followed by a numeric extension number.
error.field.phone2=Second Phone number invalid, only 0-9, '+', space, hyphen, 'ext' and 'x' allowed. 'ext' or 'x' must be followed by a numeric extension number.
error.field.startdate.null=Start Date is a required field.
error.field.startdate.future=Start Date can not be in the future.
error.field.installdate.future=Installation Date can not be in the future.
error.powerlimit.invalid=Select a valid value for power limit (required)
error.priority.invalid=Priority must be a number (1 is highest priority)
error.search.meter=Enter a valid meter number.
error.search.customer=Enter a valid search term.
error.field.touseasonname.null=Season Name is a required field.
error.field.touperiodname.null=Period Name is a required field.
error.field.touperiodcode.null=Period Code is a required field.
error.field.title.max=Title must be less than {max} characters.
error.field.initials.max=Initials must be less than {max} characters.
error.field.firstnames.max=First names must be less than {max} characters.
error.field.surname.max=Surname must be less than {max} characters.
error.field.companyname.max=Company must be less than {max} characters.
error.field.taxnum.max=Tax Number must be less than {max} characters.
error.field.agreementref.max=Agreement Ref must be less than {max} characters.
error.field.usagepoint.name.null=Usage point name is required
error.field.usagepoint.name.duplicate=Duplicate name {0} for a UsagePoint. Specify a unique name.

error.field.comment.max=Comment must be less than {max} characters.
error.field.accountref.max=Account Reference must be less than {max} characters.
error.field.ourref.null=Our Reference is required.
error.field.ourref.range=Our Reference must be between {min} and {max} characters.
error.field.amtincltax.null=Amount must be entered.
error.field.amttax.null=Tax may not be null. Enter 0 if no tax.
error.field.customvarchar.max=Custom Text Field must be less than {0} characters.

error.field.accountname.range=Account Name must be less than {max} characters.

# Error headers, etc
error.validation.header=Please fix these validation errors:
error.validation.fields.header=Please fix these input errors:
error.field.code.null=Code is required
error.field.code.range=Code must be between {min} and {max} characters.
error.server.connection=Unable to connect to the web server.
error.server=Error response received from the web server.
error.field.manufacturerid.null=Manufacturer is required.
error.field.serviceresourceid.null=Service Resource is required.
error.field.metertypeid.null=Meter Type is required.
error.field.paymentmodeid.null=Payment Mode is required.
error.field.accountname.null=An account name is required.
error.field.accountname.duplicate=Duplicate account name {0}. Specify a unique name.
error.field.taskschedulename.null=Name is required.
error.field.taskschedulename.max=Name must be less than {max} characters.
error.field.cronexpression.null=Schedule is required.
error.field.cronexpression.range=Schedule must be between {min} and {max} characters.
error.field.scheduledtaskid.null=Task Type is required.
error.field.taskschedulename.range=Task Schedule Name is required.
error.field.taskclassid.null=Task Type is required.
error.field.scheduledtaskname.range=Name must be between {min} and {max} characters.
error.field.taskscheduleid.null=Task Schedule is required.
error.messages.header=Errors:

# Customer Account Threshold errors
error.field.lowbalance.null=Low Balance must be entered
error.field.emergencycredit.null=Emergency Credit must be entered
error.field.disconnect.null=Disconnect must be entered
error.field.reconnect.null=Reconnect must be entered

# Questions
question.discard.changes=Do you want to discard the current changes?
question.discard.potential.changes=If you made changes & not yet saved, they will be discarded. Continue?
option.yes=Discard Changes
option.no=Cancel
option.positive=Yes
option.negative=No
question.delete=Do you want to delete the selected item?
dayprofile.question.delete=Do you want to delete the selected item? This will also delete all times associated with this day profile.
question.confirm.installation.date.1=Confirm that meter {0} was installed on {1} at {2}.
question.confirm.installation.date.2=It is important that this date and time is accurate.
question.confirm.link.to.customer=Customer {0} is already on the page. Fetching this usagePoint here will automatically assign that customer to it. Continue?
question.close.tabs.1=Cannot save while other tabs, that potentially might reference this item, are open.
question.close.tabs.2=You can close them yourself and then attempt the save again - choose NO or
question.close.tabs.3=shall we automatically close other tabs for you (will lose unsaved data) - choose YES?
option.confirm=Confirm
option.continue=Continue?

## Messages
message.saved={0} was saved.

## Links
link.logout=Logout
link.loggedin=User:
link.group.change=Change Group
link.group=Group:
link.meters=Meters
link.customers=Customers
link.groups=Groups
link.menu=Menu
link.pricingstructure=Pricing Structure
link.calendars=Calendars
link.calendarsettings=Calendar Settings
link.auxchargeschedule=Aux Charge Schedule
link.auxilliarytype=Aux Type
link.supplygroup=Supply Group
link.displaytokens=Display Tokens
link.devicestores=Device Stores
link.usergroup=User's Access Group
link.accessgroups=Access Groups
link.search=Search
link.search.advanced=Advanced Search
link.search.meters.viewed=Last Viewed Meters
link.search.meters.modified=Last Modified Meters
link.meter.readings=Interval Readings
link.energybalancing=Energy Balancing
link.energybalancing.meters=Energy Balancing Meters
link.analytics=Analytics
link.configuration=Configuration
link.tools=Tools
link.taskschedules=Task Schedules
link.locationgroups=Location Groups
link.about=About
link.appsettings=Application Settings
link.global.ndp=Global NDP
link.billingdet=Billing Determinants

## Buttons ##
button.save=Save
button.new=New
button.edit=Edit
button.back=Back
button.cancel=Cancel
button.close=Close
button.select=Select
button.delete=Delete
button.viewentity=View Entity
button.yes=Yes
button.no=No
button.create=Create
button.update=Update
button.viewtariffs=View Tariffs
button.replacemeter=Replace Meter
button.removemeter=Remove Meter
button.displayunits=Display Units
button.gettoken=Get Token
button.saveaccount=Save Account
button.addnew=Add New
button.editchargeschedule=Edit Charge Schedule
button.clear.group=Clear Group
button.search=Search
button.clear=Clear
button.view=View
button.add=Add
button.remove=Remove
button.export=Export
button.view.scheduledtasks=View Scheduled Tasks
button.login=Login
button.logout=Leave Site
button.set=Set
button.show.inherited=Show Inherited Values
button.send=Send
button.viewtrans=View Transactions

## Menus ##
menu.add=Add
menu.update=Update
menu.delete=Delete
menu.search=Search

## Record Statuses ##
status.active=Active
status.inactive=Inactive
status.deleted=Deleted

### Supply Group ###
supplygroups.header=Supply Groups
supplygroups.title=Current Supply Groups
supplygroup.title=Supply Group
supplygroup.name=Supply Group
supplygroup.field.name=Name
supplygroup.field.code=Code
supplygroup.field.keyrevisionnumber=Key Revision Number
supplygroup.field.keyexpirynumber=Key Expiry Number
supplygroup.field.status=Status
supplygroup.field.active=Active
supplygroup.title.add=Add Supply Group
supplygroup.title.update=Update Supply Group
supplygroup.error.save=Unable to save the supply group.

### Group Type ###
grouptypeshierarchies.title=Group Types and Hierarchies
grouptypes.header=Group Types
grouptypes.hierarchies.header=Group Hierarchies
grouptypes.title=Current Group Types
grouptype.title=Group Type
grouptype.field.id=Group Type ID
grouptype.field.name=Name
grouptype.field.name.help=Enter the name of this group type. This is a required field - the record cannot be saved without it.
grouptype.field.description=Description
grouptype.field.description.help=Enter a description of this group type.
grouptype.field.parent=Parent
grouptype.field.parent.help=If this group type belongs in a hierarchy under another group type, select the parent group type here.
grouptype.field.active=Active
grouptype.field.active.help=Is this group active? Check the box to activate this group type.
grouptype.field.status=Status
grouptype.field.required=Required
grouptype.field.required.help=For usage point groups this determines whether a selection for this group type is required
grouptype.field.accessgroup=Access Group
grouptype.field.locationgroup=Location Group
grouptype.field.access.location.group=Access / Location
grouptype.field.feature=Features
grouptype.field.available.feature=Available Features
grouptype.field.assigned.feature=Assigned Features
grouptype.field.available.feature.help=If a feature is marked as single-select feature, it can only be selected for a single group type, once the group type has been used in the data, this assignment cannot change.
grouptype.field.assigned.feature.help=Features assigned to this Group Type
grouptype.button.viewhierarchies=View Hierarchies
grouptype.error.noneselected=No current Group Type selected.
grouptype.error.parentcanthavehierarchy=Parent group types can't have hierarchies
grouptype.none=Group Type: None Selected
grouptype.title.add=Add Group Type
grouptype.title.update=Update Group Type
grouptype.error.save=Unable to save the group type.
grouptype.error.accessgroup.users=Unable to change the access group. There are existing users assigned to the access groups.
grouptype.error.accessgroup.customers=Unable to change the access group. There are existing customers using the groups.
grouptype.error.accessgroup.up=Unable to change the access group. There are existing usage points using the groups.
grouptype.error.accessgroup.pricing=Unable to change the access group. There are existing pricing structures using the groups.
grouptype.error.accessgroup.aux=Unable to change the access group. There are existing aux charge schedules using the groups.
grouptype.error.accessgroup.stores=Unable to change the access group. There are existing device stores using the groups.
grouptype.error.name.duplicate=The group type's name is already in use. Choose another name.
grouptype.error.cannot.change.required=Cannot change the 'required' setting, group already in use.
grouptype.error.select.add.feature=Select a feature to add.
grouptype.error.select.remove.feature=Select a feature to remove.
grouptype.error.cannot.set.feature.for.group=This feature may not be set for this type of group.
grouptype.error.cannot.remove.feature=Cannot remove the feature setting, group already in use.
grouptype.error.save.feature=Unable to save the feature setting.
grouptype.error.feature.not.multi.instance=This feature can only be assigned once, already assigned to another Group.
grouptype.accessgroup.help=The access group type determines what group type controls the user's access to the web site. It is set once and can not be changed.
grouptype.locationgroup.help=The location group type determines what group type controls the location data. It is set once and can not be changed.

### Group Hierarchy ###
grouphierarchies.header=Group Hierarchies
grouphierarchies.title=Current Group Hierarchies
grouphierarchy.title=Group Hierarchy
grouphierarchy.field.id=Group Hierarchy ID
grouphierarchy.field.name=Name
grouphierarchy.field.name.help=Enter the name of this level in the group hierarchy.
grouphierarchy.field.description=Description
grouphierarchy.field.active=Active
grouphierarchy.field.parent=Parent Hierarchy
grouphierarchy.field.parent.none=None
grouphierarchy.field.level=Level
grouphierarchy.delete.confirm=Are you sure you want to delete the group hierarchy?
grouphierarchy.deleted=The group hierarchy was successfully deleted.
grouphierarchy.error.delete=Unable to delete the group hierarchy.
grouphierarchy.error.delete.linked=Unable to delete the group hierarchy as it is being used.
grouphierarchy.error.save=Unable to save the group hierarchy.
grouphierarchy.error.update=Cannot update group hierarchy item as it is being used.
grouptype.current=Current Group Type
grouphierarchy.title.add=Add Group Hierarchy
grouphierarchy.title.update=Update Group Hierarchy
grouphierarchy.error.unknown=Unknown group hierarchy.
grouphierarchy.error.access.root=The access group type's first level hierarchy data can not be modified.

### Usage Point Groups ###
usagepointgroups.header=Usage Point Groups
usagepointgroups.title=Current Usage Point Groups
usagepointgroups.instructions=Select a Group Type:
usagepointgroup.title=Usage Point Group
usagepointgroup.noselection.grouptype=No current Group Type selected.
usagepointgroup.field.id=Usage Point Group ID
usagepointgroup.field.name=Name
usagepointgroup.field.hierarchy=Hierarchy
usagepointgroup.help.grouptype=Select a Group Type to view its corresponding Usage Point Groups. Only Group Types with Group Hierarchies are visible here.
usagepointgroup.delete.ask=Are you sure you want to delete the {0} usage point group?
usagepointgroups.help=Click the tree data's icons to add, edit or delete the data.
usagepointgroup.title.add=Add Usage Point Group
usagepointgroup.title.update=Update Usage Point Group
usagepointgroup.error.entityid=Unable to save the GenGroup's Entity id.

###Old Usage Point Group Page
usagepointgroup.field.description=Description
usagepointgroup.field.parent=Parent Name
usagepointgroup.field.active=Active
usagepointgroup.field.status=Status
usagepointgroup.field.name.help=Enter a name for the group - this will be used to identify it when adding usage points to the group.
usagepointgroup.field.description.help=Enter a description of the group (optional).
usagepointgroup.field.parent.help=If the group is part of a bigger group identify the parent group here
usagepointgroup.field.status.help=Activate or deactivate this group

## Groups
group.error.save=Unable to save the group.
group.error.delete=Unable to delete the group.
group.error.delete.ap=Unable to delete the group as there are still active usage points in it.
group.error.entity.save=Unable to link the contact information to the group.
group.error.threshold.save=Unable to link the threshold information to the group.
group.error.ndp.schedule.save=Unable to link the NDP information to the group.
group.new.instructions=Enter a new
group.new.for=for
group.current.none=No current group has been set for your user.
group.delete.ask=Are you sure you want to delete the {0} group?
groups.error.select.at.minimum=At minimum the {0} must be selected.

## Usage Point Workspace
usagepointworkspace.meter.saved.usagepoint.deactivation.failed=Meter {0} Saved! (Usage point deactivation failed!)
usagepointworkspace.meter.saved.usagepoint.deactivated=Meter {0} saved! (Usage point deactivated!)
usagepointworkspace.meter.saved=Meter {0} saved!
usagepointworkspace.meter.saved.attach.usagepoint=Meter {0} saved! To attach this meter to the usage point below, select installation date and pricing structure, as well as activation.
usagepointworkspace.customer.saved.usagepoint.deactivation.failed=Customer {0} Saved! (Usage point deactivation failed!)
usagepointworkspace.customer.saved.usagepoint.deactivated=Customer {0} saved! (Usage point deactivated!)
usagepointworkspace.customer.saved.usagepoint.failed=Customer {0} saved but unable to update Usage point! Contact Support.
usagepointworkspace.customer.saved.usagepoint.updated=Customer {0} saved and Usage point updated.
usagepointworkspace.customer.saved=Customer {0} saved.
usagepointworkspace.customer.saved.no.usage.point=Customer {0} saved, no Usage Point to update.
usagepointworkspace.customer.unassigned.usagepoint.deactivation.failed=Customer {0}  is no longer assigned to usage point {1} (Usage point deactivation failed!)
usagepointworkspace.customer.unassigned.usagepoint.deactivated=Customer {0} is no longer assigned to usage point {1} (Usage point deactivated!)
usagepointworkspace.customer.assign.error.already.assigned=Customer is already assigned to usage point {0}
usagepointworkspace.customer.assign.error=Customer {0} is NOT assigned to usage point {1} (Usage Point deactivation failed)
usagepointworkspace.customer.assigned.usagepoint.deactivated=Customer {0} is now assigned to usage point {1} (Usage Point not active)
usagepointworkspace.customer.assigned=Customer {0} is now assigned to usage point {1}
usagepointworkspace.error.meter.not.found=Meter not found
usagepointworkspace.error.meter.already.assigned=Meter {0} is already assigned to usage point {1}
usagepointworkspace.error.meter.unable.to.unassign=Unable to unassign current meter
usagepointworkspace.error.meter.installdate.before.previous=New install date {0} cannot be BEFORE previous meter's installation date {1}.
usagepointworkspace.error.meter.installdate.before.last.remove=New install date {0} cannot be BEFORE previous meter's removal date {1}.
usagepointworkspace.error.meter.installdate.before.last.reading=Unable to re-assign usage point - new installation date is equal or before last reading date of current meter.
usagepointworkspace.error.meter.installdate.before.last.register.reading=Unable to re-assign usage point - new installation date is equal or before last register reading date of current meter.
usagepointworkspace.error.meter.removedate.before.last.reading=Unable to remove meter from usage point now - last reading date of current meter is greater.
usagepointworkspace.error.meter.removedate.before.last.register.reading=Unable to remove meter from usage point now - last register reading date of current meter is greater.
usagepointworkspace.meter.assigned=Meter {0} is now assigned to usage point {1}
usagepointworkspace.meter.assigned.usagepoint.deactivated=Meter {0} is now assigned to usage point {1} (Usage Point is not active)
usagepointworkspace.meter.removed=Meter {0} has been removed from usage point {1}
usagepointworkspace.meter.add.usagepoint.to.join.customer=Add usage point (below) to link meter {0} and customer {1} to each other
usagepointworkspace.meter.add.usagepoint.to.join=Add usage point (below) to link this meter to a usage point
usagepointworkspace.assign.activate.usage.point.question=Usage Point is not active at this time. Do you want to activate it now?
usagepointworkspace.assign.usage.point.activated=Usage Point activated.
usagepointworkspace.save.usage.point.inactive=Usage Point is not active.
usagepointworkspace.meter.customer.assigned=Meter {0}, Customer {2} are now assigned to usage point {1}

### Usage Point ###
usagepoint.groups.title=Usage Point Groups
usagepoint.info.title=Usage Point Information
usagepoint.title=Usage Point
usagepoint.add.new=Add Usage Point
usagepoint.show.info=Show Usage Point Information
usagepoint.showing.info=Showing Usage Point Information
usagepoint.field.active.help=Is this Usage Point active? This option is disabled until an active meter and customer are linked to this usage point.
usagepoint.field.activated_date.help=Set the date this usage point was activated. This can only be set once. This field is required for activation. Although the record can be saved without it, the usage point cannot be activated unless it is filled in correctly.
usagepoint.field.active=Inactive
usagepoint.field.activated_date=Date Activated
usagepoint.field.meter.installation_date=Meter Installation date/time
usagepoint.field.meter.installation_date.meter.required=A meter must be installed before setting the installation date.
usagepoint.field.meter.installation_date.help=The date and time when the meter was installed at this usage point. This can only be set once per meter. This field is required and a meter must be assigned before setting the installation date.
usagepoint.field.name.help=Enter the name of this usage point. This is a required field - the record cannot be saved without it.
usagepoint.field.name=Usage Point Name
usagepoint.field.pricingstructure.help=Select the pricing structure. This is a required field - the record cannot be saved without it.
usagepoint.field.lastmdcconnectcontrol=Last MDC Connect Control
usagepoint.field.group.help=Add this usage point to a group
usagepoint.field.group=Usage Point groups
usagepoint.required.text=* \= Required
usagepoint.required.activation.text=** \= Required for Activation - An active meter and customer are also required for activation
usagepoint.name.required=Usage Point Name is required
usagepoint.pricingstructure.required=Pricing Structure is required
usagepoint.pricingstructure.change.illegal=Pricing Structure cannot be changed now. No meter is attached. Have reset the selection.
usagepoint.save.error=Saving of usage point failed.
usagepoint.save.errors=Usage Point not saved. Correct the errors shown.
usagepoint.save.errors.meter.required=Saving of usage point failed. An associated meter is required
usagepoint.saved=UsagePoint {0} saved.
usagepoint.changes.cleared=Changes have been cleared.
usagepoint.no.meter=A meter must be saved before adding a Usage Point.
usagepoint.txn.history=Transaction History
usagepoint.txn.history.description=Previous customer transactions for this usage point
usagepoint.txn.meterreadings=Interval Readings
usagepoint.history=Usage Point History
usagepoint.history.description=Previous changes made to this usage point (changes are highlighted)
usagepoint.reports=Reports for Usage Point
usagepoint.recharge.history=Usage Point Meters Recharge History
usagepoint.retailers=Nearby Retailers
usagepoint.reports.general=General Reports for Usage Points
usagepoint.meter.reports=Reports for Usage Point
usagepoint.coords=Add Latitude and Longitude coordinates to the Usage Point, in order to see nearby retailers.
usagepoint.txn.reference=Reference
usagepoint.txn.receipt=Receipt Number
usagepoint.txn.date=Date
usagepoint.txn.meter=Meter
usagepoint.txn.customer=Customer
usagepoint.txn.type=Type
usagepoint.txn.client=Client
usagepoint.txn.term=Term
usagepoint.txn.ref=Reference
usagepoint.txn.revref=Reversal Ref
usagepoint.txn.isreversed=Reversed
usagepoint.txn.amt=Amount
usagepoint.hist.user=User
usagepoint.hist.serial=Serial
usagepoint.hist.date=Date
usagepoint.hist.meter=Meter
usagepoint.hist.customer=Customer
usagepoint.hist.datemod=Date Modified
usagepoint.hist.byuser=By User
usagepoint.hist.action=Action
usagepoint.hist.status=Status
usagepoint.hist.name=Name
usagepoint.hist.custagree=Customer Agreement
usagepoint.hist.service=Service Location
usagepoint.recharge.title=Usage Point Recharge
usagepoint.recharge.date=Date
usagepoint.recharge.currency=R
usagepoint.recharge.kwh=kWh
usagepoint.history.filter=Filter
usagepoint.txn.filter=Filter
usagepoint.group.required=Group required
usagepoint.onegroup.required=At least one group must be selected
usagepoint.group.error.delete=Unable to delete the usage point\'s group.
usagepoint.group.error.save=Unable to save the usage point\'s group.
usagepoint.group.no.value=No values defined
usagepoint.calculate.tariff=Calculate Tariff
usagepoint.calculate.tariff.error=An error occurred calculating the tariff.
usagepoint.calculate.tariff.connection.error=No response received from service.
usagepoint.calculate.tariff.ok=Tariff calculated successfully
usagepoint.recharge.chart.title=Usage Point Recharges
usagepoint.recharge.chart.subtitle=Recharge Amounts
usagepoint.recharge.chart.xtitle=Date Time
usagepoint.recharge.chart.ytitle=Cost
usagepoint.recharge.chart.price=Price
usagepoint.recharge.chart.purchaseprice=Purchase Price
usagepoint.error.meterandcustomer.required=An active meter and customer are required for activation
usagepoint.meter.installed.at=Meter {0} was installed on {1} at {2}
usagepoint.installation.date.required=Meter Installation date/time is required.
usagepoint.name.instructions=Search by Usage Point Name
usagepoint.partial.search=No exactly matching usage point for {0}. Doing advanced search...
usagepoint.fetch=Fetch Usage Point
usagepoint.fetch.help=Fetch an existing usage point.
usagepoint.assigned=This usage point already has a meter or customer assigned to it. Not eligible for a Fetch.
usagepoint.name.instr=Enter Usage Point name
usagepoint.find=Find Usage Point
usagepoint.fetch.duplicate=Usage Point {0} already on page.
usagepoint.install.date.required=The date and time when the above meter was installed at this usage point {0}.
usagepoint.new.pricingstructure.required=Selected Usage point pricing structure {1} incompatible with Meter {0}.
usagepoint.error.new.installdate.before.removal=Installation date cannot be BEFORE last removal date on usage point: {0}
usagepoint.error.new.installdate.before.removaldate.meter=Installation date cannot be BEFORE last removal date of the meter: {0}
usagepoint.saved.linked.meter=Linked to meter {0}.
usagepoint.saved.linked.customer=Linked to customer {0}.
moxiechart.abbrev.month.categories="Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"

### Group Entity ###
groupentity.header=Group Information
groupentity.title=Contact Information
groupentity.contact.title=Contact
groupentity.field.contact.name=Contact Name
groupentity.field.contact.number=Contact Number
groupentity.field.contact.email=Contact Email
groupentity.field.contact.address=Contact Address
groupentity.field.contact.taxref=Contact Tax Reference
groupentity.error.save=Unable to save the group information.

### Group Thresholds ###
groupthreshold.title=Customer Account Thresholds information
groupthreshold.meter.disconnect.text=Disconnect Threshold
groupthreshold.meter.disconnect.help=Threshold below which the customer will be disconnected.
groupthreshold.emergency.credit.text=Emergency Credit Threshold
groupthreshold.emergency.credit.help=Threshold below which the customer balance will result in a disconnect and enable request to the mdc if supported.
groupthreshold.meter.reconnect.text=Reconnect Threshold
groupthreshold.meter.reconnect.help=Threshold above which the customer will be reconnected.
groupthreshold.low.balance.text=Low Balance Warning Threshold
groupthreshold.low.balance.help=Threshold to alert customer to a low balance in their account.
groupthreshold.global.source.label=Global Settings
groupthreshold.parent.source.label=Parent Settings : {0}
groupthreshold.children.change.alert=Lower hierarchies with no or same thresholds will also be updated with these changes. Continue?
groupthreshold.null.thresholds.alert=Thresholds that are left blank will be disabled. Continue?
groupthreshold.error.save.thresholds=Unable to save the thresholds information.
groupthreshold.revert.parent.global=Are you sure to delete? This node will now inherit {0} threshold Settings.
groupthreshold.error.disconnect.greater.emergency.credit=Disconnect must be smaller than or equal to Emergency Credit
groupthreshold.error.disconnect.greater.reconnect=Disconnect must be smaller than or equal to Reconnect
groupthreshold.error.emergency.credit.greater.low.balance=Emergency Credit must be smaller than or equal to Low Balance

### Global Non-Disconnect Periods ###
global.ndp.tab.heading=Global NDP
global.ndp.heading=Global Non Disconnect Periods
global.ndp.none=There is no Global NDP Schedule, please press Create Button.
global.ndp.schedule.new.added=The Global NDP schedule has been created.
global.ndp.schedule.activation.saved=Global NDP schedule saved.

### Group Non-Disconnect Periods ###
ndp.active.instruction=An NDP schedule can be created and worked on, but will only be applied when it is active. The schedule can only be activated when at least one NDP day with times was entered. Either a season with a day or a special day.
ndp.schedule.title=NDP Schedule
ndp.schedule.active=Active
ndp.schedule.active.help=Is this schedule active? Check the box to activate this schedule - can only be done when at least one NDP day with times was entered.
ndp.schedule.delete.button=Delete Schedule
ndp.disclosurePanel.title=Non-Disconnect Periods
ndp.seasons.title=Seasons
ndp.season.day.title=Season Days
ndp.season.day.description=Enter a start and end date for the season, followed by the NDP times per day of week.
ndp.assign.season.start=Start date
ndp.assign.season.start.help=Enter the date the season starts
ndp.assign.season.end=End date
ndp.assign.season.end.help=Enter the date the season ends
ndp.per.day.title=NDP per Day
ndp.days.of.week=Day of Week
ndp.assign.dayperiod.start=Start time
ndp.assign.dayperiod.start.help=The time that the NDP starts
ndp.assign.dayperiod.start.hour=Hour
ndp.assign.dayperiod.start.minute=Minute
ndp.assign.dayperiod.end=End time
ndp.assign.dayperiod.end.help=The time that the NDP ends
ndp.assign.dayperiod.end.hour=Hour
ndp.assign.dayperiod.end.minute=Minute
ndp.assign.dayperiod.saved=NDP Time has been saved.
ndp.assign.dayperiod.deleted=NDP Time has been deleted.
ndp.assign.dayperiod.error.end.before.start=End time cannot be before start time
ndp.assign.dayperiod.error.time.already.assigned=The time selected has already been assigned.
ndp.assign.dayperiod.nulls=All start & end time values must be entered. Cannot be blank.
ndp.days.title=NDP Day Times
ndp.add.season.button=Add Season
ndp.assign.season.error.end.before.start=End cannot be before Start
ndp.assign.season.error.date.already.assigned=These dates overlap with another season.
ndp.assign.season.error.time.already.assigned=The time selected has already been assigned.
ndp.weekdays=Monday,Tuesday,Wednesday,Thursday,Friday,Saturday,Sunday
ndp.children.change.alert=Lower hierarchies with no NDP will also be updated with these changes. Continue?
ndp.error.save.schedule=Unable to save the NDP Schedule information.
ndp.abort.save=You elected NOT to save this NDP schedule.
ndp.error.save.season=Unable to save the NDP Season.
ndp.error.save.day=Unable to save the NDP Day times.
ndp.revert.parent.global=Are you sure to delete? This node will now inherit {0} NDP Settings.
ndp.children.delete.alert=Lower hierarchies with the same NDP Schedule will also be reverted to {0} NDP Setting. Continue?
ndp.error.delete.day.profiles=Unable to delete the NDP Day Profiles.
ndp.error.delete.season=Unable to delete an NDP Season.
ndp.error.delete.special.days=Unable to delete the NDP Special Days.
ndp.error.delete.schedule=Unable to delete the NDP Schedule.
ndp.new.season.button=Add New Season
ndp.new.special.day.button=Add Special Day
ndp.special.day.column.heading=Day
ndp.no.change.continue=Nothing has changed. Continue?
ndp.day.panel.changed=Changes were made to this Season. Abandon those changes and continue with cancel?
# zachv: 2019-01-02 | Planio 5936
#Removed ndp.schedule.abandon.activation.change=Schedule activation has changed. if you want to keep the setting, choose No and save / update first. Abandon setting?
ndp.schedule.activation.no.change=No change in active status.
ndp.schedule.new.added=A new NDP Schedule has been added to this group & relevant subgroups.
ndp.schedule.activation.saved=NDP schedule saved.
ndp.schedule.deleted=The NDP schedule was deleted from this group & relevant subgroups.
ndp.season.saved=NDP Season saved.
ndp.day.profile.confirm.delete=Confirm delete of day profile for {0}?
ndp.day.profile.confirm.delete.and.deactivate=Deleting day Profile for {0} will cause the NDP schedule to de-activate upon updating the season, as it is the only NDP time for this schedule. Continue?
ndp.season.confirm.delete=Confirm delete of Season {0}
ndp.season.deleted=NDP Season deleted.
ndp.season.confirm.delete.and.deactivate=Deleting Season {0} will cause the NDP schedule to de-activate, as it contains the only NDP time/s for this schedule. Continue?
ndp.special.day.confirm.delete=Confirm delete of Special Day {0}
ndp.special.day.confirm.delete.and.deactivate=Deleting Special Day {0} will cause the NDP schedule to de-activate, as it contains the only NDP time/s for this schedule. Continue?
ndp.special.day.deleted=Special Day deleted.
ndp.special.day.title=Special Days
ndp.special.day.time.title=Special Day NDP Times
ndp.special.day.description=Enter the day & month of the Special Day, followed by the NDP times for that day.
ndp.assign.special.day=Special Day Date
ndp.assign.special.day.help=Enter the day and Month of the Special Day.
ndp.assign.special.day.duplicate=Duplicate Special Day date.
ndp.special.day.panel.changed=Changes were made to this Special Day. Abandon those changes and continue with cancel?
ndp.special.day.times.confirm.delete.and.deactivate=Deleting NDP times for {0} will cause the NDP schedule to de-activate upon updating the special day, as it is the only NDP time for this schedule. Continue?
ndp.special.day.saved=Special Day NDP saved.
ndp.error.save.special.day=Unable to save the Special Day NDP.
ndp.global.none.found=There is no Global NDP schedule. Please go and create one in the Configuration Section.
ndp.inherited.global.none=There is no Global NDP schedule or it is not yet active. No Global NDP periods can be inherited!

### Customer Agreement ###
customeragreement.title=Customer Agreement

### Pricing Structures ###
pricingstructures.header=Pricing Structures
pricingstructures.title=Current Pricing Structures
pricingstructure.title=Pricing Structure
pricingstructure.title.new=New Pricing Structure
pricingstructure.title.edit=Edit Pricing Structure
pricingstructure.field.name=Name
pricingstructure.field.description=Description
pricingstructure.field.status=Status
pricingstructure.field.active=Active
pricingstructure.field.name.help=Name of this pricing structure, must be unique
pricingstructure.field.description.help=A description of this pricing structure
pricingstructure.field.active.help=Whether the pricing structure is active or not. A pricing structure can only be activated if it has a current tariff.
pricingstructure.field.type=Type
pricingstructure.field.startdate=Start Date
pricingstructure.field.tariffs=# Tariffs
pricingstructure.error.save=Unable to save the pricing structure.
pricingstructure.field.serviceresource.help=If there are no tariffs, you can select the service resource for the pricing structure.
pricingstructure.field.metertype.help=If there are no tariffs, you can select the meter type for the pricing structure.
pricingstructure.field.paymentmode.help=If there are no tariffs, you can change the payment mode for the pricing structure.
pricingstructure.tariffs.prt.none=There are no available tariff types for the current pricing structure's service resource, meter type and payment mode combination.
pricingstructure.tariffs.ui.none=No available tariff form input to be saved.
pricingstructure.error.tariff.load=Error loading the tariff's data. Unable to display the tariff's fields and corresponding values.
pricingstructure.name.duplicate=Duplicate name {0} for a Pricing Structure. Specify a unique name.
pricingstructure.error.active=Pricing Structure has no Tariff. Cannot be active.
pricingstructure.error.deactivate=Pricing Structure is in use. Cannot deactivate.

### Tariffs ###
tariffs.header=Tariffs
tariffs.title=Current Tariffs
tariff.title=Tariff
tariff.title.add=Add New Tariff
tariff.title.edit=Edit Tariff
tariff.title.view=View Tariff
tariff.field.name=Name
tariff.field.name.help=Name of this tariff, must be unique
tariff.field.description=Description
tariff.field.description.help=A description of this tariff
tariff.field.startdate=Start Date
tariff.field.startdate.help=The date this tariff will be activated on. No other tariffs may start on this date and it must be a future date.
tariff.title.type=Tariff Type Details
tariff.field.type=Type
tariff.field.unitprice=Unit Price
tariff.field.tax=Tax Percent
tariff.field.tax.help=Percentage such as 14%
tariff.field.free.units.descrip=Monthly Free Units Description
tariff.field.free.units=Monthly Free Units
tariff.field.free.units.help=Units that are issued free of charge.
tariff.field.groupthreshold=Group Threshold
tariff.field.groupthreshold.help=The threshold in kWH of group usage for the month after which the Threshold Price takes affect
tariff.field.thresholdprice=Threshold Price
tariff.field.thresholdprice.help=Price per kWH (after threshold has been crossed)
tariff.field.price=Price
tariff.field.baseprice=Base Price
tariff.field.threshold=Threshold
tariff.field.threshold.help=Price per kWh
tariff.field.step1=Step 1
tariff.field.step2=Step 2
tariff.field.step3=Step 3
tariff.error.numeric.value=Value must be numeric
tariff.error.save=Unable to save the tariff.
tariff.error.save.duplicate=Unable to save the tariff, another tariff with the same name already exists.
tariff.error.load=Unable to load tariff data.
tariff.field.block=Blocks
tariff.field.block.single=Block
tariff.error.freeunits.positive=Must be a positive value.
tariff.error.freeunits.decimal.limit=Units may only have 1 decimal
tariff.blocks.error=Blocks need valid numeric unit price and threshold values.
tariff.blocks.error.incomplete=Blocks need a unit price for a threshold value.
tariff.blocks.error.last=Only the final block should not have a threshold.
tariff.blocks.error.last.none=The final block should not have a threshold.
tariff.blocks.error.increasing.thresholds=Thresholds for a block should be greater than zero and greater than the previous block's threshold.
tariff.blocks.error.none=Blocks are required.
tariff.tou.thinsmart.season=Season
tariff.tou.thinsmart.period=Period
tariff.tou.thinsmart.readingtype=Meter Reading Type
tariff.tou.thinsmart.rate=Rate
tariff.tou.thinsmart.chargeunits=Units
tariff.tou.thinsmart.specialday=Special Day
tarif.adv.settings.header=Advanced Settings
tariff.field.pricesymbol=Currency Symbol
tariff.field.pricesymbol.help=The currency symbol to be used for the price.
tariff.field.unitsymbol=Unit Symbol
tariff.field.unitsymbol.help=The currency units symbol to be used for the price.
tariff.field.amountrounding=Amount Rounding Mode
tariff.field.amountrounding.help=The rounding mode for the amounts.
tariff.field.amountprecision=Amount Precision
tariff.field.amountprecision.help=The precision for the amounts.
tariff.field.unitsrounding=Unit Rounding
tariff.field.unitsrounding.help=The rounding mode to use for the units.
tariff.field.unitsprecision=Unit Precision
tariff.field.unitsprecision.help=The precision for the units.
tariff.field.taxrounding=Tax Rounding
tariff.field.taxrounding.help=The rounding mode for the tax.
tariff.field.taxprecision=Tax Precision
tariff.field.taxprecision.help=The precision to use for the tax.
tariff.error.field.pricesymbol=Currency Symbol is required.
tariff.error.field.unitsymbol=Unit Symbol is required.
tariff.error.field.amountrounding=Rounding Mode is required.
tariff.error.field.unitsrounding=Rounding Mode is required.
tariff.error.field.taxrounding=Rounding Mode is required.
tariff.error.field.amountprecision=Precision must be a positive value.
tariff.error.field.unitsprecision=Precision must be a positive value.
tariff.error.field.taxprecision=Precision must be a positive value.
tariff.readonly=Tariff has started and is read-only.

### Tou Calendar ###
calendar.settings.header=Calendar Settings
calendar.settings.title=Calendar Settings
calendar.season.title=Setup Seasons
calendar.season.current.title=Current Seasons
calendar.season.description=Add or update seasons.
calendar.season.field.name=Name
calendar.season.field.active=Active
calendar.season.add=Add Season
calendar.season.update=Update Season
calendar.season.field.name.help=Enter a name for this season. This is a required field.
calendar.season.field.active.help=The current activity status of this item
season.error.update=The season could not be updated.
season.error.save=The season could not be saved.
season.error.delete=The season is in use and cannot be deleted.
calendar.season.deleted={0} season was deleted successfully.
calendar.period.deleted={0} period was deleted successfully.

calendar.period.title=Setup Periods
calendar.period.current.title=Current Periods
calendar.period.description=Add and update periods (e.g. Peak, Off-Peak, Standard).
calendar.period.field.name=Name
calendar.period.field.code=Code
calendar.period.field.active=Active
calendar.period.add=Add Period
calendar.period.update=Update Period
calendar.period.field.name.help=Enter a name for this period. This is a required field.
calendar.period.field.code.help=Enter a Code for this period. This is a unique shorthand code for the period. This is a required field.
calendar.period.field.active.help=The current activity status of this item
period.error.save=The period could not be saved.
period.error.delete=The period is in use and cannot be deleted.

calendars.title=Calendars
calendars.heading=Setup Calendars
calendars.description=Current calendars are listed, add or edit them using the forms below
calendar.field.name=Calendar Name
calendar.field.name.help=Enter a name for this calendar.
calendar.field.description=Description
calendar.field.description.help=Enter a description of this calendar.
calendar.field.active=Active
calendar.field.active.help=The current activity status of this calendar
calendar.title=Calendar
calendar.add=Add New Calendar
calendar.update=Update Calendar
calendar.changes.cleared=Calendar changes cleared
calendar.save.errors=Unable to save calendar
calendar.complete=Complete
calendar.incomplete=Incomplete
calendar.optional=Optional
calendar.duplicate=Duplicate Calendar Name. Choose a unique name.

calendar.assign.season.heading=Assign Dates to Seasons for calendar
calendar.assign.season.title=Season dates
calendar.assign.season.description=Enter a start and end date for selected season.
calendar.assign.season.form.heading=Assign Dates
calendar.assign.season=Season
calendar.assign.season.help=Select Season
calendar.assign.season.start=Start date
calendar.assign.season.start.help=Enter the date the season starts
calendar.assign.season.end=End date
calendar.assign.season.end.help=Enter the date the season ends
calendar.assign.season.deleted=Season dates have been deleted.
calendar.assign.season.error.end.before.start=End cannot be before Start
calendar.assign.season.error.date.already.assigned=The date is already assigned to a season
calendar.assign.season.error.select.season=Select a season from the list

calendar.assign.period.title=Day Profile
calendar.assign.period.description=Assign times of the day to Periods
calendar.assign.period=Period
calendar.assign.period.help=Select a period
calendar.assign.period.start=Start time
calendar.assign.period.start.help=The time that the period starts
calendar.assign.period.start.hour=Hour
calendar.assign.period.start.minute=Minute
calendar.assign.period.end=End time
calendar.assign.period.end.hour=Hour
calendar.assign.period.end.minute=Minute
calendar.assign.period.saved=Time period has been saved.
calendar.assign.period.cleared=Time period changes have been cleared.
calendar.assign.period.deleted=Time period has been deleted.
calendar.assign.period.error.end.before.start=End time cannot be before start time
calendar.assign.period.error.time.already.assigned=The time selected has already been assigned.
calendar.assign.period.nulls=All start & end values must be entered. Cannot be blank.

calendar.dayprofiles.heading=Setup Day Profiles for calendar
calendar.dayprofiles.title=Day Profiles
calendar.dayprofiles.description=Create days which are broken up into specific time periods.

calendar.dayprofile.field.name=Profile Name
calendar.dayprofile.field.code=Code
calendar.dayprofile.field.active=Active
calendar.dayprofile.field.name.help=Enter a name for this day profile
calendar.dayprofile.field.code.help=Enter a shorthand code for this day profile
calendar.dayprofile.field.active.help=The current activity status of this day profile
calendar.dayprofile.error.save=Unable to save day profile
calendar.dayprofile.error.first.unassign=Cannot delete a day profile which is currently assigned to a calendar season. First unassign the day profile and then delete.
calendar.dayprofile.error.special.day.first.unassign=Cannot delete a day profile which is currently assigned to a Special Day. First delete the Special Day, then delete the day profile.
calendar.dayprofile.deleted=Day profile deleted
calendar.dayprofile.saved=Day profile changes saved
calendar.dayprofile.cleared=Day profile changes cleared

calendar.assign.dayprofile.heading=Assign Day Profiles to calendar
calendar.assign.dayprofile.description=For each season which has been assigned to the calendar, specify the day profile for each day of the week.
calendar.assign.dayprofile.cleared=Assign day profile changes cleared.
calendar.assign.dayprofile.saved=Assign day profile changes saved.
calendar.assign.dayprofile.error.save=Error assigning day profiles

calendar.specialday.heading=Setup Special Days for calendar
calendar.specialday.title=Setup special days
calendar.specialday.description=Assign day profiles to specific days
calendar.specialday.field.name=Special Day Name
calendar.specialday.field.name.help=A unique name for the day.
calendar.specialday.field.active=Active
calendar.specialday.field.active.help=The current activity status of this special day
calendar.specialday.field.day=Day
calendar.specialday.field.month=Month
calendar.specialday.field.dayprofile=Day Profile
calendar.specialday.field.dayprofile.help=Select the day profile
calendar.specialday.field.dayprofile.error=Day Profile must be entered for Special Day.
calendar.specialday.add=Add Special Day
calendar.specialday.update=Update Special Day
calendar.special.day.deleted=Special day deleted.
calendar.specialday.error.date.already.assigned.to=This date has already been assigned to special day

calendar.readOnly=Note: This calendar cannot be updated because it is already in use by the following pricing structures: {0}

### Aux Charge Schedule ###
auxchargeschedules.header=Aux Charge Schedules
auxchargeschedules.title=Current Aux Charge Schedules
auxchargeschedule.title=Aux Charge Schedule
auxchargeschedule.title.add=Add Aux Charge Schedule
auxchargeschedule.title.update=Update Aux Charge Schedule
auxchargeschedule.field.name=Name
auxchargeschedule.field.name.help=Name of this schedule, must be unique and is required
auxchargeschedule.field.status=Status
auxchargeschedule.field.minamount=Min Amount
auxchargeschedule.field.minamount.help=Enter the minimum amount required to be charged by this schedule
auxchargeschedule.field.maxamount=Max Amount
auxchargeschedule.field.maxamount.help=Enter the maximum amount allowed to be charged by this schedule
auxchargeschedule.field.vendportion=Vend Portion
auxchargeschedule.field.vendportion.help=Enter the percentage of a vend that will be used to calculate the charge.
auxchargeschedule.field.currportion=Current Portion
auxchargeschedule.field.currportion.help=Enter the percentage of the remaining balance that will be used to calculate the charge.
auxchargeschedule.field.active=Active
auxchargeschedule.field.active.help=The current activity status of this item
auxchargeschedule.error.save=Unable to save the aux charge schedule.
auxchargeschedule.error.duplicate=Duplicate name {0} for an Aux Charge Schedule. Specify a unique name.
auxchargeschedule.nocharge.error=A Vend Portion, a Current Portion or an Ad Hoc Amount must be defined

### Auxilliary Type ###
auxilliarytypes.header=Auxiliary Types
auxilliarytypes.title=Current Auxiliary Types
auxillarytype.title.add=Add Auxiliary Type
auxillarytype.title.update=Update Auxiliary Type
auxillarytype.title=Auxiliary Type
auxtype.field.name=Name
auxtype.field.description=Description
auxtype.field.status=Status
auxtype.field.active=Active
auxtype.field.name.help=Name of this auxiliary type, must be unique
auxtype.field.description.help=Description of this auxiliary type
auxtype.field.active.help=The current activity status of this item
auxtype.error.save=Unable to save the auxiliary type.
auxtype.error.save.duplicate=Unable to save the auxiliary type, another auxiliary type with the same name already exists.
auxtype.error.update=Unable to update the auxiliary type.
auxtype.error.update.duplicate=Unable to update the auxiliary type, another auxiliary type with the same name already exists.

### Device Store ###
devicestores.header=Device Stores
devicestores.title=Current Device Stores
devicestore.title.add=Add Device Store
devicestore.title.update=Update Device Store
devicestore.title=Device Stores
devicestore.field.name=Name
devicestore.field.description=Description
devicestore.field.name.help=Name of this device store (must be unique)
deviceStore.name.duplicate=Duplicate name {0} for a Device Store. Specify a unique name.
devicestore.field.description.help=Description of this device store
devicestore.field.active=Active
devicestore.field.active.help=The current activity status of this device store
devicestore.location.title=Device Store Location
devicestore.error.save=Unable to save the device store.
devicestore.error.update=Unable to update the device store.
devicestore.button.addmeters=Add Meters to selected device store
devicestore.meters=Current meters
devicestore.meters.in=Current meters in
devicestore.meters.description=Meters currently in this device store.
devicestore.meters.header=Device Store Meters
devicestore.meters.title=Current Device Store Meters
devicestore.history=Device Store History
devicestore.history.description=Previous changes made to this device store (changes are highlighted)
devicestore.user=User
devicestore.date=Date
devicestore.date.mod.column=Date Modified
devicestore.user.by.column=By User
devicestore.action.column=Action
devicestore.status.column=Status
devicestore.name.column=Name
devicestore.description.column=Description
devicestore.button.importmeters=Import meters into selected device store
devicestore.import.meters.header=Import meters into device store {0}

### Meter ###
meter.title=Meter
meter.number.instructions=Search by Meter Number
meter.add=Add New Meter
meter.add.new=Add New Meter
meter.or=or
meter.then=then
meter.enter.number=Enter meter number
meter.fetch.number=Fetch Meter
meter.specify.install.date=Specify install date
meter.open=Open Meter
meter.open.newtab=Open replaced meter in new tab.
meter.assign=Fetch Meter
meter.attach=Attach Meter to Usage Point
meter.info.title=Meter Information
meter.required.text=* \= Required
meter.required.activation.text=** \= Required for Activation
meter.show.info=Show Meter Information
meter.showing.info=Showing Meter Information
meter.active.help=Is this meter active? This option is disabled until all the required meter information is saved.
meter.active=Active
meter.replace.help=Replace current meter on this usage point with another meter.
meter.replace=Replace Meter on Usage Point
meter.remove.help=Remove current meter from this usage point. Will de-activate the usage point. Can access it again through Advanced Search.
meter.remove=Remove Meter from Usage Point
meter.assign.from.store.help=Fetch a meter from the device store.
meter.assign.from.store=Fetch meter from device store
meter.date.install.missing=Enter the new installation date.
meter.select.meter.model=Select Meter Model
meter.select.meter.model.help=Select the model of the meter. This will determine what details are required for the meter.
meter.select.metertype=Meter Type
meter.select.metertype.help=Select the type of meter
meter.number.help=Enter the meter number. This is a required field - the record cannot be saved without it.
meter.number=Meter Number
meter.number.optional.help=Enter a specific meter number, if you have one.
meter.number.optional=Meter Number (optional)
meter.iso.help=Enter the meter ISO.
meter.iso=ISO
meter.checksum.help=Enter the meter checksum.
meter.checksum=Checksum
meter.serialnumber.help=Enter the meter's serial number.
meter.serialnumber=Serial Number
meter.breakerid=Breaker Id
meter.breakerid.help=The Meter Model requires a breaker id, enter here.
meter.breakerid.error=The Meter Model requires a meter breaker id.
meter.stsinfo=STS Information
meter.algorithmcode=Algorithm Code
meter.tokentechcode=Token Tech Code
meter.supplygroupcode=Current Supply Group Code
meter.tariffindex=Current Tariff Index
meter.save.errors=Meter not saved. Correct the errors.
meter.saved=Meter saved.
meter.changes.cleared=Changes have been cleared.
meter.powerlimit=Power Limit
meter.powerlimit.help=Select the power limit required
meter.description=Description
meter.description.help=Enter a description
meter.token.active=The usage point must be active to get this token
meter.token.code=Token code
meter.token.code1=Token code 1
meter.token.code2=Token code 2
meter.title.enginerringtokens=Engineering Tokens
meter.issue.engineeringtoken=Issue New Engineering Token
meter.engineeringtoken.history=Engineering Token History
meter.engineeringtoken.description=Previous engineering tokens generated for this meter
meter.select.tokentype=Select token type
meter.select.tokentype.help=Select a token type from the list below.
meter.txn.history=Transaction History
meter.txn.history.description=Previous customer transactions for this meter
meter.history=Meter History
meter.history.description=Previous changes made to this meter (changes are highlighted)
meter.reports=Reports for Meter
meter.recharge.history=Meter Recharge History
meter.retailers=Nearby Retailers
meter.reports.general=General Reports for Meters
meter.reports.meter=Reports for Meter
meter.freeissue.units=Free Issue - Units
meter.cleartamper=Clear Tamper
meter.clearcredit=Clear Credit
meter.clearcredit.all=Clear All Credit
meter.clearcredit.elec=Clear Electricity Credit
meter.clearcredit.type.description=Credit Type
meter.clearcredit.type.help=Select the kind of credit which needs to be cleared
meter.coords=Add Latitude and Longitude coordinates to the Usage Point for this meter, in order to see nearby retailers.
meter.user=User
meter.serial=Serial
meter.date=Date
meter.date.mod.column=Date Modified
meter.user.by.column=By user
meter.action.column=Action
meter.status.column=Status
meter.number.column=Meter Num
meter.serial.column=Serial
meter.uniqueid.column=Unique Id
meter.unique.external.column=External Unique Id
meter.techtoken.column=TT
meter.alg.column=Alg
meter.supplygroup.column=SG
meter.keyrevision.column=KR
meter.tariffindex.column=TI
meter.enddevicestore.column=Store
meter.units.kwh=Units (kWh)
meter.units.kwh.help=Enter the number of kWh units required.
meter.units.watts=Units (Watts)
meter.units.watts.help=Enter the maximum phase limit in Watts.
meter.currency=Currency
meter.currency.help=Enter the currency value required.
meter.free.description=Description
meter.free.description.help=Enter a description for this free issue.
meter.setphase=Set Maximum Phase
meter.setphase.description=Description
meter.setphase.description.help=Enter a description for this set phase token.
meter.token.error=Could not retrieve token. Check errors.
meter.error.units=Enter a valid value for number of units required
meter.error.amount=Enter a valid value for amount required
meter.txn.type=Type
meter.txn.token.type=Token Type
meter.txn.receipt=Receipt
meter.txn.token=Token
meter.txn.amount=Amount Incl Tax
meter.txn.tax=Tax
meter.txn.units=Units
meter.txn.tariff=Tariff
meter.txn.date=Date
meter.txn.ref=Reference
meter.txn.receiptnum=Receipt Number
meter.txn.customer=Customer
meter.txn.client=Client
meter.txn.term=Term
meter.txn.revref=Reversal Ref
meter.txn.isreversed=Reversed
meter.txn.amount.column=Amount
meter.txn.usagepoint=Usage Point
meter.txn.user=User
meter.txn.description=Description
meter.clear.description=Description
meter.clear.description.help=Enter a description for this clear tamper.
meter.changekey=Key Change
meter.changekey.instructions=For Key Change Tokens:\n1. Open the Meter Panel.\n2. In the STS information block, change the Current Supply Group Code.\n3. This will add a new listbox with key change options - select what you require.\n4. Upon saving the meter, a popup box will be shown to input further details.
meter.new.supplygroupcode.help=Enter the new supply group code.
meter.new.supplygroupcode=New Supply Group Code
meter.old.supplygroupcode=Old Supply Group Code
meter.new.tariffindex=New Tariff Index
meter.old.tariffindex=Old Tariff Index
meter.new.keyrevisionnum=New Key Revision Number
meter.old.keyrevisionnum=Old Key Revision Number
meter.new.tariffindex.help=Enter the new tariff index.
meter.txn.filter=Filter
mdc.txn.show.connect.disconnect.only=Show Connect Disconnect Only
mdc.txn.show.balance.messages.only=Show Balance Messages Only
meter.engtoken.filter=Filter
meter.history.filter=Filter
meter.generate.keychange=Generate Key Change Tokens?
meter.generate.keychange.help=If the meter needs to be updated to match the new STS details then key change tokens need to be generated. If the record is being updated to match the meter's details then there is no need to generate tokens.
meter.luhncheck.failed=Incorrect Meter Number (failed Luhn check)
meter.error.alreadyexists=A meter with this number already exists.
meter.keychange.none=Do not generate key change tokens
meter.keychange.now=Generate key change tokens now
meter.keychange.flag=Set to generate key change tokens with next vend
meter.pending.keychange=* Pending key change:
meter.warning.sg.transactions=NOTE: There are already more than 3 transactions on this meter.
meter.error.save=Unable to save the meter.
stsmeter.error.save=Unable to save the STS meter.
meter.select.store.move=Move current meter to the following store:
meter.select.store.help=The current meter must be added to a store. When it is assigned to a Usage Point it will be automatically removed from the store. This is a required field - the record cannot be saved without it.
meter.message.type=Message Type
meter.message.type.help=Send a message for the meter
meter.assigned.to.usagepoint=Meter currently assigned to usage point:
meter.error.invalid=Invalid meter specified.
meter.error.invalid.datemanu=Manufactured date is invalid.
meter.error.cannot.activate=Cannot Activate
meter.error.required.for.activation=Required for activation
meter.partial.search=No exactly matching meter. Doing advanced search...
meter.install.date.required=The date and time when the new meter was installed at usage point {0}.
meter.installed.at=Meter was installed on {0} at {1}
meter.connect.disconnect.error=An error occurred sending meter instruction : {0}
meter.connect.disconnect.connection.error=No response received. Service not available. Please notify your System Administrator.
meter.connect.disconnect.ok.mdc000=Meter {0} command successfully completed. Reference={1}.
meter.connect.disconnect.ok.mdc010=Meter {0} command accepted. Reference={1}.
meter.connect.disconnect.ok.mdc011=Meter {0} message has been queued. Reference={1}.
meter.manufacturer.code.length=Manufacturer code
meter.manufacturer.code.length.help=The length of the manufacturer code, 2 digit or 4 digit
meter.2digit.manufacturer.code=2 digit code
meter.4digit.manufacturer.code=4 digit code
meter.found.incorrect.group=Meter found but in different group to user
usagepoint.found.incorrect.group=Usage Point found but in different group to user
meter.attach.cancelled=Attachment Process Cancelled. Meter {0} has been created in device store, but not assigned to this usage point.
meter.attach.instructions=Attach Meter to Usage Point
meter.attach.cancel.button=Cancel Attachment Process

### Customer ###
customer.title=Customer
customer.search.instructions=Customer Search by:
customer.add=Add New Customer
customer.add.new=Add New Customer
customer.info.title=Customer Information
customer.show.info=Show Customer Information
customer.showing.info=Showing Customer Information
customer.active.help=Is this Customer active? This option is disabled until all the required customer information is saved.
customer.active=Active
customer.unassign=Unassign Customer from Usage Point
customer.assign=Assign Customer to Usage Point
customer.assign.short=Assign Customer
customer.assign.help=Assign an existing customer to this usage point.
customer.unassign.help=Remove the current customer from this usage point.
customer.fetch=Fetch Customer
customer.fetch.help=Fetch existing Customer
customer.fetch.duplicate=Customer {0} already on page.
customer.open=Open Customer
customer.field.title.help=Enter the customer's title (eg Mr, Mrs, Dr).
customer.field.title=Title
customer.initials.help=Enter the customer's initials.
customer.initials=Initials
customer.firstnames.help=Enter the customer's first names.
customer.firstnames=First Names
customer.surname.help=Enter the customer's surname. This is a required field - the customer cannot be saved without it.
customer.surname=Surname
customer.surname.instr=Enter customer surname
customer.company.help=Enter the company name.
customer.company=Company Name
customer.tax.help=Enter the customer's tax number.
customer.tax=Tax Number
customer.emails.help=Enter the customer's email addresses.
customer.phones.help=Enter the customer's phone numbers.
customer.address.physical=Physical Address
customer.agreement=Customer Agreement
customer.agreementref.help=Enter the agreement reference with the customer. This is a required field - the customer cannot be saved without it.
customer.agreementref=Agreement Ref
customer.startdate.help=Enter the date the agreement with the customer started.  Although the record can be saved without it, the customer cannot be activated unless it is filled in correctly.
customer.startdate=Start Date
customer.freeissue.help=Select the auxiliary account to be used with free issue tokens, where the value of the token will be recovered by adding the amount to the selected account.
customer.freeissue=Free issue auxiliary account
customer.required=* \= Required
customer.required.activation=** \= Required for Activation
customer.save.errors=Customer not saved. Correct the errors shown.
customer.sync.accbal.error=An error occurred synchronizing the account balance to meter. Reference={0}
customer.sync.accbal.connection.error=No response received from service.
customer.sync.accbal.ok.mdc000=Synchronize Account Balance successfully completed. Reference={0}
customer.sync.accbal.ok.mdc010=Synchronize Account Balance Command accepted. Reference={0}
customer.sync.accbal.ok.mdc011=Synchronize Account Balance message has been queued. Reference={0}
customer.changes.cleared=Changes have been cleared.
customer.auxaccount.adjust=Adjust Auxiliary Account
customer.title.find=Find Customer
customer.assigned=Cannot assign customer, already assigned.
customer.auxaccount=Auxiliary Account: {0}
customer.auxaccount.addedit=Auxiliary Accounts
customer.auxaccount.active=Active
customer.auxaccount.active.help=Is this account active? This option is disabled until all the required account information is saved.
customer.auxaccount.type.help=Select the type of auxiliary account you are adding.
customer.auxaccount.balance=Balance
customer.auxaccount.balance.help=Enter the current balance of this account.
customer.auxaccount.balance.pos=A positive balance indicates a REFUND
customer.auxaccount.balance.neg=A negative balance indicates a DEBT
customer.auxaccount.priority=Priority
customer.auxaccount.priority.help=Enter the priority for this account (1 is highest priority).
customer.auxaccount.chargeschedule=Charge Schedule
customer.auxaccount.chargeschedule.help=Select the charge schedule.
customer.auxaccount.txn.history=Auxiliary Account Transaction History for  : {0}.
customer.auxaccount.txn.description=Previous Transactions for this Auxiliary Account
customer.title.auxaccounts=Auxiliary Accounts
customer.title.auxaccounts.current=Current Auxiliary Accounts
customer.title.auxaccounts.description=View and edit current auxiliary accounts and create new auxiliary accounts.
customer.title.txnhistory=Account Transaction History
customer.title.history=Customer History
customer.title.reports=Reports for Customer
customer.title.generalreports=General Reports for Customers
customer.title.chargescheduledetails=Details for Auxiliary Charge Schedule
customer.title.chargeschedule=Charge Schedule
customer.chargeschedule.startdate=Start Date
customer.chargeschedule.vendpor=Vend Portion
customer.chargeschedule.currpor=Current Portion
customer.chargeschedule.minamt=Min Amount
customer.chargeschedule.maxamt=Max Amount
customer.chargeschedule.status=Status
customer.auxaccount.filter=Filter
customer.auxaccount.date=Date
customer.auxaccount.freeissue=Free Issue
customer.auxaccount.edit=Edit
customer.auxaccount.name=Account Name
customer.auxaccount.name.help=Enter a name for this account.
customer.auxaccount.type=Account Type
customer.auxaccount.type.column=Type
customer.auxaccount.status=Status
customer.auxaccount.add=Add Auxiliary Account
customer.auxaccount.update=Update Auxiliary Account
customer.auxaccount.error.save=Unable to save the auxiliary account.
customer.auxaccount.error.id=Unable to apply auxiliary account adjustment against Customer Account. Contact support!
customer.auxaccount.error.unique.priority=Priority is required and must be unique. May not be smaller than or equal to zero.
customer.freeissue.error.save=Unable to update the customer\'s agreement.
customer.partial.search=No exactly matching customer. Doing advanced search...
customer.agreement.partial.search=No exactly matching agreement. Doing advanced search...
customer.account.partial.search=No exactly matching account name. Doing advanced search...

customer.date.mod.column=Date Modified
customer.user.by.column=By User
customer.action.column=Action
customer.status.column=Status
customer.title.column=Title
customer.initials.column=Initials
customer.firstnames.column=First Names
customer.surname.column=Surname
customer.company.column=Company
customer.email1.column=Email
customer.email2.column=Email 2
customer.phone1.column=Phone
customer.phone2.column=Phone 2

customer.user=User
customer.date=Date
customer.history=Customer History
customer.history.description=Previous changes made to this customer (changes are highlighted)
customer.history.filter=Filter

customer.agreement.user=User
customer.agreement.date=Date
customer.agreement.history=Customer Agreement History
customer.agreement.history.description=Previous changes made to this customer agreement (changes are highlighted)
customer.agreement.history.filter=Filter
customer.agreement.date.mod.column=Date Modified
customer.agreement.user.by.column=By User
customer.agreement.action.column=Action
customer.agreement.status.column=Status
customer.agreement.customer.column=Customer
customer.agreement.ref.column=Agreement Ref
customer.agreement.start.column=Start Date
customer.agreement.freeaux.column=Free Aux Account
customer.error.save=Unable to save the customer.
customeraggreement.error.save=Unable to save the customer agreement.

customer.account=Customer Account
customer.account.name=Account Name
customer.account.name.help=Enter a name for this account
customer.account.balance=Account Balance
customer.account.balance.help=The current account balance
customer.account.sync=Synchronize Balance
customer.account.sync.help=Synchronize the account balance with the meter balance
customer.account.low.balance.threshold=Low Balance Threshold
customer.account.low.balance.threshold.help=When the account balance reaches this threshold a message will be sent to the customer.
customer.account.credit.limit=Credit Limit
customer.account.credit.limit.help=The credit limit allowed on this account
customer.account.note=A customer account is only necessary if the pricing structure selected for the usage point (below) requires it.
customer.account.notification.email=Notification Email Addresses
customer.account.notification.email.help=A comma separated list of email addresses to which account related notifications can be sent (e.g. when the low balance threshold has been reached)
customer.account.notification.phone=Notification Phone Numbers
customer.account.notification.phone.help=A comma separated list of phone numbers to which account related notifications can be sent (e.g. when the low balance threshold has been reached)
customeraccount.error.save=Unable to save the customer account.

customer.txn.filter=Filter
customer.txn.history=Account Transaction History
customer.txn.description=Previous Account transactions for this Customer Agreement
customer.txn.ent.date=Date Entered
customer.txn.user=User entered
customer.txn.name=Customer
customer.txn.agreement.ref=Agreement Ref
customer.txn.trans.type=Transaction Type
customer.txn.trans.date=Transaction Date
customer.txn.comment=Comment
customer.txn.our.ref=Our Reference
customer.txn.amt=Amount incl tax
customer.txn.tax=Tax
customer.txn.bal=Balance
customer.txn.input=Adjust Account
customer.txn.acc.ref=Account Reference
customer.txn.amt.incl.tax=Amount incl. Tax
customer.txn.successful.adjustment=Account successfully adjusted and new Account Balance
customer.txn.no.agreement=There is NO Customer Agreement to adjust
customer.txn.error.amt.and.tax.zero=Amount and Tax cannot BOTH be zero
customer.txn.error.update=Error updating Customer Account Balance
customer.txn.error.insert=Error inserting the new adjustment transaction
customer.txn.error.no.usagepoint=Usage Point not found
customer.txn.notification.failure=Unable to send Account Adjustment Notification. Please notify your System Administrator.
customer.txn.send.email.failure=Unable to send Account Adjustment email to client. Please notify your System Administrator.

# Customer transaction upload
customer.trans.upload=Transaction Upload
customer.trans.upload.heading=Customer Transaction Upload
customer.trans.upload.data.title=Import Customer Account Balance Adjustment Transactions
customer.trans.upload.data.description=Select the CSV file containing the Customer transactions for importing into the Meter Management system.
customer.trans.upload.file.help=Select a file containing customer transaction information in the specified csv format for importing into the system
customer.trans.upload.file=Select Transaction File
customer.trans.upload.csv.button=Upload CSV Data
customer.process.trans.button=Process Transactions
customer.trans.upload.identifierType=Identifier Type
customer.trans.upload.identifier=Identifier
customer.trans.upload.amt.incl.tax=Amt incl Tax
customer.trans.upload.amt.tax=Tax Amt
customer.trans.upload.trans.date=Transaction date
customer.trans.upload.account.ref=Account Reference
customer.trans.upload.comment=Comment
customer.trans.upload.errors=Errors
customer.trans.upload.table.heading.errors=Transactions : Errors
customer.trans.upload.Bizswitch.down=No response received. BizSwitch service not available. Please notify your System Administrator.
customer.trans.upload.table.heading.valid=Valid Transactions : sample of first 15 lines in file
customer.trans.upload.invalid.cannot.create.dir=ERROR! Cannot create the directory. Please contact Support.
customer.trans.upload.filename= Selected filename={0}
customer.trans.upload.invalid.filename=Improper filename - hyphen or period missing. Filenames expected as AccountTrans-reference.csv where <reference> is saved as 'our Ref' on transactions
customer.trans.upload.invalid.filename.changed=Filename has changed between steps! Was {0}; now {1}
customer.trans.upload.invalid.unexpected.commas=Commas inside fields - cannot identify separate fields accurately
customer.trans.upload.invalid.identifiertype=IdentifierType must be usagePointName / accountName / meterNumber
customer.trans.upload.invalid.identifier=Invalid Identifier - not in database
customer.trans.upload.invalid.agreement=Usage Point does not have a customer agreement in place
customer.trans.upload.invalid.usagepoint.or.agreement=Meter has no usage point or the usage point has no agreement
customer.trans.upload.invalid.usagepoint=Customer Account does not belong to a usage point
customer.trans.upload.invalid.amt.incl.tax=Amount incl. Tax is not numeric
customer.trans.upload.invalid.amt.tax=Tax Amount is not numeric
customer.trans.upload.invalid.trans.date=Transaction date must be either empty (defaults to date of processing) or formatted as yyyy-mm-dd hh:mm:ss, example: 2015-09-23 22:14:55
customer.trans.upload.invalid.account.ref=Account Reference maximum 100 chars
customer.trans.upload.invalid.comment=Comment maximum 255 chars
customer.trans.upload.invalid.duplicate=Duplicate transaction of identifierType: {0}, identifier: {1}. Both target the same Customer Account.
customer.trans.upload.file.action.unknown=Unknown file upload action, contact Support
customer.trans.upload.file.none=No file was selected to be uploaded
customer.trans.upload.file.error=Error while uploading the file
customer.trans.upload.file.process.error=Error while processing the file
customer.trans.upload.successful.counts=Total of {0} transactions in batch - {1} were successfully processed, {2} were duplicates and ignored in this run.
customer.trans.upload.process.failed=System Error on transaction:identifierType= {0}, identifier= {1}, accountRef= {2}. Try resubmitting the file.
customer.trans.upload.trans.validation.errors=Validation errors found. Please repair and resubmit. Maximum 15 errors are processed at any one time

### Location ###
location.field.erfnumber=Erf Number
location.field.erfnumber.help=Enter the ERF number of this location
location.field.address=Address
location.field.address.help=Enter the address of this location
location.field.city=City
location.field.city.help=Enter the city of this location
location.field.province=Province
location.field.province.help=Enter the province of this location
location.field.country=Country
location.field.country.help=Select the country of this location
location.field.postalcode=Postal Code
location.field.postalcode.help=Enter the postal code of this location
location.field.lat=Latitude
location.field.lat.help=Enter the latitude of this location
location.field.long=Longitude
location.field.long.help=Enter the longitude of this location
location.latitude.invalid=Latitude is invalid.
location.longitude.invalid=Longitude is invalid.
location.field.streetnumber=Street Number
location.field.streetnumber.help=Enter the street number
location.field.buildingname=Building Name
location.field.buildingname.help=Enter the building's name
location.field.suitenumber=Suite Number
location.field.suitenumber.help=Enter the apartment number
location.error.save=Unable to save the location.
location.user=User
location.date=Date
location.history=Location History
location.history.description=Previous changes made to this address.
location.history.date.mod.column=Date Modified
location.history.user.by.column=By User
location.history.action.column=Action
location.history.status.column=Status
location.history.address1.column=Address
location.history.address2.column=
location.history.address3.column=
location.history.erfnumber.column=Erf Num
location.history.latitude.column=Latitude
location.history.longitude.column=Longitude
location.history.group.column=Group
location.history.streetnum.column=Street Num
location.history.buildingname.column=Building
location.history.suitenum.column=Suite Num
location.history.physical.address=Physical Address History

## Display Tokens
displaytokens.title=Display Tokens
display.initiate=Initiate Meter Test
display.testload=1. Test the load switch
display.testdisplay=2. Test the payment meter information display devices
display.totals=3. Display cumulative kWh energy register totals
display.krn=4. Display the KRN
display.ti=5. Display the TI
display.testreader=6. Test the token reader device
display.powerlimit=7. Display maximum power limit
display.tamper=8. Display tamper status
display.consumption=9. Display power consumption
display.version=10. Display software version
display.phase=11. Display phase power unbalance limit

## Change Group
changegroup.change=Change Group
changegroup.username=Username
changegroup.current=Current Group
usergroup.field.grouphierarchy=Group Level
changegroup.set=Set Group
changegroup.select=Browse the available access control groups available to you below and select a group.
changegroup.available=Available Groups
changegroup.error.group.none=Select a valid group.
changegroup.error.same=The selected group is the same group as your current group.
changegroup.assigned=Assigned User Access Group
changegroup.group.changed=Your current group was changed.
changegroup.group.cleared=Your current group was cleared, now using assigned group.
changegroup.error.clear=Cleared Group back to assigned group, but you still need to select the current group to use.
changegroup.current.details=Current Details
changegroup.username.help=Start typing the user's username and select the correct user from the user list.
changegroup.warning=Note: Changing your group will require refreshing and/or closing of any workspace tabs that are group-related.

## User Group
usergroup.title=User's Access Group
usergroup.header=User's Access Group
usergroup.title.current=Current User's Access Group
usergroup.label.help=Select the access group that the user will belong to for viewing and changing of data.
usergroup.error.user=Invalid user specified.
usergroup.error.group=Invalid group specified.
usergroup.error.save=Unable to save the user's group.
usergroup.error.update=Unable to update the user's group.
usergroup.field.user=User
usergroup.field.usergroup=Assigned Access Group
usergroup.title.add=Add User's Access Group
usergroup.title.update=Update User\'s Access Group
usergroup.instructions=Each user can be assigned to an access group. The user's access group determines what data is available to the user to view and edit.
usergroup.confirm.clear=Are you sure you want to clear the user's access group? That user will no longer be able to access the system.
usergroup.clear.yourself=Cannot clear your own user's access group while you are logged on to this session.
usergroup.cleared=The user's access group was cleared.
usergroup.error.usergroup.none=There is no current access group for the user.
usergroup.error.delete=Unable to clear the user's access group.
usergroup.no.accessgroup=No access group type has been set yet for data access. Use the group types page to set an access group type.
usergroup.accessgroup.none=Unable to assign any user's access group as there are no available groups for user's access control.

## Access Groups
accessgroups.title=Access Groups
accessgroup.access.label=Group Type:
accessgroups.title.current=Current Access Groups
accessgroup.access.instructions=Your currently assigned access group can be updated as well as any of its sub-groups. New sub-groups of your access group can also be added. If there is no linked data, you can also delete bottom level sub-groups.

locationgroups.title=Location Groups
locationgroup.instructions=Location groups are used to group location data in a hierarchy.
locationgroups.title.current=Current Location Groups

## Advanced Search
search.advanced.title=Advanced Search
search.advanced.header=Advanced Search
search.advanced.instructions=Enter some or complete search data in the various fields below and click the Search button to view your results.
search.advanced.results.header=Advanced Search Results
search.meter.number=Meter Number
search.meter.model=Meter Model
search.meter.no.usage.point=Meters with no Usage Point
search.type=Search Type
search.type.agreement=Agreement Search Type
search.startswith=Starts with
search.contains=Contains
search.customer.name=Name
search.customer.surname=Surname
search.customer.title=Title
search.customer.agreement=Agreement
search.customer.agreement.ref=Reference
search.customer.no.usage.point=Customers with no Usage Point
search.account=Account
search.account.name=Name
search.usagepoint.name=Name
search.meter=Meter Search
search.customer=Customer Search
search.usagepoint=Usage Point Search
search.usage.point.no.customer=Usage Points no customer
search.usage.point.no.meter=Usage Points no meter
search.error.no.criteria=No valid search criteria has been specified.
search.no.results=No matching search results were found.
search.meter.result=Meter
search.meter.model.result=Meter Model
search.customer.result=Customer
search.usagepoint.result=Usage Point
search.pricingStructure.result=Pricing Structure
search.paymentMode.result=Payment Mode
search.name=Name
search.no.meter=No exactly matching meter was found. Performing search using partial meter number: {0}.

meterreadings.title=Interval Readings
meterreadings.chart.title.single=Interval Readings
meterreadings.chart.title.balancing=Energy Balancing
meterreadings.header.graph=Graph
meterreadings.title.graph=Interval Readings Graph
meterreadings.error.none=Start typing a meter number and select the corresponding meter from the meters.
meterreadings.error.none.selected=Select a valid meter from the available meters.
meterreadings.start=Start Date
meterreadings.end=End Date
meterreadings.error.dates=Start date must be before the end date.
meterreadings.error.type=Select a reading type.
meterreadings.type=Reading Type
meterreadings.error.start=Start Date is required.
meterreadings.error.start.format=Start Date must match {0}.
meterreadings.error.end=End Date is required.
meterreadings.error.end.format=End Date must match {0}.
meterreadings.noreadings=No corresponding interval readings were found.
meterreadings.chart.title=Interval Readings
meterreadings.chart.subtitle=kWh usage
meterreadings.chart.ytitle=kWh
meterreadings.chart.xtitle=Date and Time
meterreadings.series.name=kWh
meterreadings.graph.type=Graph Type
meterreadings.type.graph.single=Single Meter
meterreadings.type.graph.balancing=Energy Balancing
meterreadings.error.type.reading.unknown=Reading Type is unknown.
meterreadings.balancing=Super Meter
meterreadings.error.balancing=Super meter is required.
meterreadings.error.type.graph=Unknown graph type.
meterreadings.meter.super=Super Interval Meter Readings
meterreadings.meter.totals=Sub Meters Total
meterreadings.meter.field.super=Super Meter
meterreadings.meter.field.subs=Sub Meters
meterreadings.header.table=Report
meterreadings.title.table=Interval Readings Report
meterreadings.table.meter=Meter
meterreadings.table.reading=Reading
meterreadings.table.start=Start Time
meterreadings.table.end=End Time
meterreadings.report.type=Report Type
meterreadings.label.supermeter.total=Super Meter
meterreadings.label.singlemeters.total=Sub Meters
meterreadings.report.results=Interval Readings

energybalancing.header=Energy Balancing
energybalancing.title=Energy Balancing
energybalancing.start=Start Date
energybalancing.end=End Date
energybalancing.variation=Variation
energybalancing.error.start=Start date is required.
energybalancing.error.end=End date is required.
energybalancing.error.dates=Start date must be before the end date.
energybalancing.error.percent=Difference must be a positive percent value.
energybalancing.error.readingtype=Meter Reading Type is required.
energybalancing.supermeter=Super Meter
energybalancing.supermeter.reading=Super Meter Total
energybalancing.submeters.total=Sub Meters Total
energybalancing.none=No matching energy balancing readings were found.
energybalancing.view.graph=View Graph

energybalancing.meter.header=Energy Balancing Meters
energybalancing.meter.title=Energy Balancing Meter
energybalancing.meter.super=Balancing Meter Number
energybalancing.meter.sub=Sub Meter Number
energybalancing.meter.subs=Selected Sub Meters
energybalancing.meter.super.help=Type a meter number and select the corresponding meter.
energybalancing.meter.sub.help=Type a meter number and select the corresponding meter. Then click the > button to select it as a sub meter.
energybalancing.meter.subs.help=All the selected meters will be saved as sub meters of the current balancing meter.
energybalancing.meter.instructions=Select a meter for the balancing meter. Select and add other sub meters or remove existing sub meters.
energybalancing.error.super=A valid selected balancing meter is required.
energybalancing.error.sub.selected=Valid selected sub meters are required.
energybalancing.error.super.id=Invalid balancing meter id.
energybalancing.error.sub.ids=Invalid sub meter id(s).
energybalancing.error.save=Unable to save balancing / sub meter.
energybalancing.meter.error.no.sub=Select a sub meter first.
energybalancing.meter.error.same.meter=The sub meter can not be the balancing meter.
energybalancing.meter.save=Balancing and sub meters saved.
energybalancing.confirm.delete=Are you sure you want to delete this balancing meter and all its sub meters?
energybalancing.meter.deleted=Balancing and sub meters were deleted.
energybalancing.error.delete.none=No balancing meters exist for the specified meter.

meterrecharge.chart.title=Meter Recharges
meterrecharge.chart.subtitle=Recharge Amounts
meterrecharge.chart.xtitle=Date Time
meterrecharge.chart.ytitle=Cost
meterrecharge.chart.price=Price
meterrecharge.chart.purchaseprice=Purchase Price

# Manufacturer
meter.manufacturers=Meter Manufacturers
meter.manufacturers.title=Current Meter Manufacturers
meter.manufacturers.title.add=Add Meter Manufacturer
meter.manufacturers.title.update=Update Meter Manufacturer
meter.manufacturer.name=Manufacturer
meter.manufacturers.field.name=Name
meter.manufacturers.field.description=Description
meter.manufacturers.field.active=Active
meter.manufacturers.field.status=Status
meter.manufacturers.field.name.help=The manufacturer's unique name.
meter.manufacturers.field.description.help=The manufacturer's description.
meter.manufacturers.field.active.help=Only active manufacturers can be used.
meter.manufacturer.name.duplicate=Duplicate name for a manufacturer: {0}.

# Mdc (Meter Data Collector)
meter.mdc=Meter Data Collectors
meter.mdc.title=Current Meter Data Collectors
meter.mdc.title.add=Add Meter Data Collector
meter.mdc.title.update=Update Meter Data Collector
meter.mdc.name=Meter Data Collector
meter.mdc.field.name=Name
meter.mdc.field.description=Description
meter.mdc.field.active=Active
meter.mdc.field.status=Status
meter.mdc.field.value=Value
meter.mdc.field.name.help=The mdc's unique name.
meter.mdc.field.description.help=The mdc's description.
meter.mdc.field.value.help=Used in messages communicating with the mdc.
meter.mdc.field.active.help=Only active mdc can be used.
meter.mdc.name.duplicate=Duplicate name for an mdc: {0}.
meter.mdc.value.duplicate=Duplicate value for an mdc: {0}.
mdc.txn.messages=MDC Messages
mdc.txn.message=MDC Message Details - Drag Here
mdc.txn.messages.description=Mdc messages for this meter
mdc.txn.ref=Reference
mdc.txn.meter=Meter
mdc.txn.usagepoint=Usage Point
mdc.txn.customer=Customer
mdc.txn.reqreceived=Request Received
mdc.txn.reqsent=Request Sent
mdc.txn.reqtype=Request Type
mdc.txn.controltype=Control Type
mdc.txn.cmdaccrec=Command Accepted
mdc.txn.params=Params
mdc.txn.recdate=Response Received
mdc.txn.repcount=Repeats
mdc.txn.status=Status
mdc.txn.timecompld=Time Completed
mdc.txn.client=Client
mdc.txn.term=Term
mdc.txn.refreceived=Ref Received
mdc.txn.identifier=Identifier
mdc.txn.identifiertype=Identifier Type
mdc.txn.rescodereceived=Response Code
mdc.txn.scheduledate=Schedule Date
mdc.txn.success=Success
mdc.txn.pending=Pending
mdc.txn.discarded=Discarded
mdc.txn.successful=Successful
mdc.txn.failed=Failed
mdc.txn.popup.label=Data
mdc.txn.popup.value=Value
mdc.txn.send.message.title=Send MDC Message
mdc.button.viewchannels=View Channels
mdc.error.noneselected=No current Mdc selected
mdc.txn.connect=Connect
mdc.txn.disconnect=Disconnect
mdc.txn.disconnect.enable=Disconnect_Enable

# Mdc Channel
channel.header=Channels
channel.title=Current MDC Channels
channel.value.duplicate=Duplicate value for an mdc channel: {0}
channel.field.titlename=Mdc Channel
channel.field.value=Value
channel.field.name=Name
channel.field.descrip=Description
channel.field.billingdet=Billing Determinant
channel.field.status=Status
channel.title.add=Add Mdc Channel
channel.title.update=Update Mdc Channel
channel.field.value.help=Enter Channel identifier.
channel.field.billingdetname=Billing Determinant
channel.field.billingdetname.channel.help=Select Billing determinant. Can be blank IF channel readings are not to be used for billing.
channel.field.meter.reading.type=Meter Reading Type
channel.field.maxsize=Reading Max Size
channel.field.active.help=The current activity status of this channel
channel.field.active=Active
channel.billingdet.confirm=You have not selected a billing determinant which implies that this channel is not used for billing. Continue?

#Mdc Channel Initial Readings
channel.readings.header=Assign Initial Channel Readings for Meter:
channel.readings.meter.model=Meter Model:
channel.readings.mdc=MDC:
channel.readings.pricing.structure=Pricing Structure:
channel.readings.installdate=Installation Date:
channel.readings.table.heading=Initial Reading
channel.readings.reading.error=Error Channel {0} : Reading value must be a positive numeric value smaller than or equal to the maximum size {1}

# Billing Determinant
billingdet.tab.label=Billing Det
billingdet.header=Billing Determinants
billingdet.title=Current Billing Determinants
billingdet.name=Billing Determinant
billingdet.title.add=Add Billing Determinant
billingdet.title.update=Update Billing Determinant
billingdet.field.name=Name
billingdet.field.name.help=Name of Billing Determinant
billingdet.field.description=Description
billingdet.field.description.help=Description of Billing Determinant
billingdet.active=Active
billingdet.active.help=Check to activate, must be Active to be in use.
billingdet.error.save=Unable to save the Billing Determinant.
billingdet.error.update=Unable to update the Billing Determinant

# Meter Model
meter.models=Meter Models
meter.models.title=Current Meter Models
meter.models.name=Meter Model
meter.models.field.manufacturer=Manufacturer
meter.models.field.manufacturer.help=Select the correct manufacturer for the meter model.
meter.models.field.name=Name
meter.models.field.description=Description
meter.models.field.active=Active
meter.models.field.status=Status
meter.models.field.name.help=The meter model's unique name.
meter.models.field.description.help=The meter model's description.
meter.models.field.active.help=Only active meter models can be used.
meter.models.field.resource=Service Resource
meter.models.field.resource.help=Select the correct service resource for the meter model.
meter.models.field.metertype=Meter Type
meter.models.field.metertype.help=Select the correct meter type for the meter model.
meter.models.field.toa=Supports Token Over the Air
meter.models.field.toa.help=This indicates whether this model supports sending tokens such as sts tokens directly to the meter via a network.
meter.models.field.mdc=Meter Data Collector
meter.models.field.mdc.help=Associate this meter model with a meter data collector.
meter.models.field.paymentmodes=Payment Modes
meter.models.field.paymentmodes.help=Use the Ctrl key to select multiple payment modes for the meter model.
meter.models.title.add=Add Meter Model
meter.models.title.update=Update Meter Model
meter.models.paymentmodes.required=At least one payment mode is required.
meter.models.name.duplicate=Duplicate name for a meter model: {0}.
meter.models.field.balance.sync=Supports synchronising Balance
meter.models.field.balance.sync.help=This indicates whether this meter model supports balance synchronization.
meter.models.field.needs.breaker.id=Needs Breaker Id
meter.models.field.needs.breaker.id.help=Indicates whether the meter requires a breaker Id.
meter.model.unset.breaker.id.error=Cannot change the Breaker Id requirement - there are already meters with breakerId's using this meter model.

ptr.serviceresource=Service Resource
ptr.metertype=Meter Type
ptr.paymentmode=Payment Mode

tou.thin.field.calendar=Calendar
tou.thin.field.calendar.help=Select the relevant Calendar for the tariff.
tou.thin.field.monthlydemand=Monthly Demand Charge
tou.thin.field.monthlydemandtype=Monthly Demand Reading Type
tou.thin.field.monthlydemandtype.help=Specify the Monthly Demand Charge's Reading Type to be used for the charge.
tou.thin.field.servicecharge=Daily Service Charge
tou.thin.field.servicecharge.descrip=Description
tou.thin.field.servicecharge.descrip.help=Enter a line item description for the service charge.
tou.thin.field.servicechargecycle=Service Charge Cycle
tou.thin.field.servicechargecycle.help=When the Service Charge should be applied.
tou.thin.field.enablereadingtypes=Enable Meter Reading Types
tou.thin.field.enablereadingtypes.help=Select the Reading Types to be applied for this tariff.
tou.thin.charges.button=Capture Charges
tou.thin.field.charges=Charges
tou.thin.field.charges.specialday=Special Day Charges
tou.thin.change.calendar=Are you sure you want to discard all your current charges and set a new calendar?
tou.thin.error.no.calendar=Select a calendar.
tou.thin.error.no.types=Select at least one type.
tou.thin.error.no.tax=Tax is required.
tou.thin.monthlydemandtype.required=Reading Type is required.
tou.thin.monthlydemand.required=Charge is required.
tou.thin.monthlydemand.positive=Charge must be positive.
tou.thin.monthlydemand.invalid=Charge must be a valid numeric value.
tou.thin.cycle.required=Cycle is required.
tou.thin.servicecharge.invalid=Service Charge must be a valid numeric value.
tou.thin.tax.invalid=Tax must be a valid numeric value.
tou.thin.servicecharge.required=Service Charge is required.
tou.thin.servicecharge.positive=Service Charge must be positive.
tou.thin.no.tariff.data.available=No tariff data available to be updated and saved.
tou.thin.charges.none=Charges need to be captured for the tariff.
tou.thin.charges.invalid=Charge rates must be positive, valid numeric values.
tou.thin.specialdayscharges.none=Special Day Charges need to be captured for the tariff.
tou.thin.specialdayscharges.invalid=Special Day rates must be positive, valid numeric values.
tou.thin.rates.none=No rates entered
tou.thin.rates.invalid=Rates must be positive and not zero

# Register Reading Tariffs
register.reading.billing.determinant.title=Billing Determinants
register.reading.billing.determinant.help=Select the Billing Determinants for which to capture rates for this pricing structure
register.reading.rates.button=Capture Rates
register.reading.rates.title=Rates
register.reading.billing.determinant.column.title=Billing Determinant
register.reading.error.no.tax=Tax is required.
register.reading.rates.none=No rates entered
register.reading.rates.invalid=Rates must be positive and not zero
register.reading.error.no.billingdet=Select at least one billing determinant.
register.reading.no.tariff.data.available=No tariff data available to be updated and saved.
register.reading.billing.det.change=Billing Determinants in selected list not the same as current charges for this Tariff. Previous charges will be deleted, all charges must be re-entered. Continue?
register.reading.servicecharge.descrip.required=Service Charge Description is required.
register.reading.cannot.change.charge=Tariff already active, cannot change rates.

# Register Reading Information tab
register.reading.txn.label=Register Readings
register.reading.txn.description=Register Readings for this meter for the time period selected
register.reading.txn.timestamp=Timestamp
register.reading.txn.channel=Channel
register.reading.txn.determinant=Determinant
register.reading.txn.readingtype=Meter Reading Type
register.reading.txn.readingvalue=Value
register.reading.txn.error.enddate=End date must be equal or greater than startdate.
register.reading.txn.filter=Filter
register.reading.txn.noreadings=No corresponding register readings were found.

# AppSetting
appsetting.header=Application Settings
appsetting.title=Current Application Settings
appsetting.title.update=Update Application Setting
appsetting.name=Application Setting
appsetting.field.name=Name
appsetting.field.value=Value
appsetting.field.description=Description
appsetting.field.name.help=Application Setting Name. Can be edited for customised translation purposes. For Custom Fields, we recommmend you keep the words 'Label' or 'Status' as part of the name for readability sake...
appsetting.field.value.help=The value to be applied for this setting. For custom field Labels, enter the Label you wish to see on the input component for this field.
appsetting.field.description.help=Description of the Application Setting.
appsetting.name.duplicate=Duplicate name for an Application Setting: {0}.
appsetting.error.new=Application Setting {0} not found. Please contact your System Administrator.
appsetting.error.disconnect.greater.emergency.credit=Emergency Credit must be greater than or equal to Disconnect.
appsetting.error.disconnect.greater.reconnect=Reconnect must be greater than or equal to Disconnect.
appsetting.error.disconnect.greater.both=Disconnect must be smaller than or equal to Reconnect AND smaller than or equal to Emergency Credit
appsetting.error.maxvend.smaller.minvend=Maximum Vend Amount must be greater than or equal to Minimum Vend Amount
appsetting.error.emergency.credit.greater.low.balance=Emergency Credit must be smaller than or equal to Low Balance.
appsetting.error.emergency.credit.errors=Emergency Credit must be greater than or equal to Disconnect AND smaller than or equal to Low Balance.
appsetting.error.invalid.custom.status=Custom Field status invalid! Must be one of OPTIONAL, REQUIRED or UNAVAILABLE.

demo.addmeterreadings.title.criteria=Interval Readings Criteria
demo.addmeterreadings.interval=Reading Interval
demo.addmeterreadings.readingtypes=Meter Reading Types
demo.addmeterreadings.tariffcalc=Send tariffCalcRequest after readings
demo.addmeterreadings.error.paymentmode=Payment Mode is required.
demo.addmeterreadings.error.meter=Type a meter number and select one from the available selection.
demo.addmeterreadings.error.meter.select=Please select a meter from the available selection.
demo.addmeterreadings.no.usagepoint1=Meter has no Usage Point / Customer.
demo.addmeterreadings.no.usagepoint2=Tariff Calc Request not possible.
demo.addmeterreadings.no.usagepoint3=Unable to read usage point for Meter. Please inform Support.
demo.addmeterreadings.error.start=Start is required.
demo.addmeterreadings.error.end=End is required.
demo.addmeterreadings.error.dates=Start must be before End.
demo.addmeterreadings.error.end.future=End must be before today.
demo.addmeterreadings.error.interval=Reading Interval is required.
demo.addmeterreadings.error.types=At least one Reading Type is required.
demo.addmeterreadings.invalid.input=Invalid input to add interval readings.
demo.addmeterreadings.error.insert=Error inserting the interval reading.
demo.addmeterreadings.error.insert.fact=Error inserting the interval reading fact.
demo.addmeterreadings.error.insert.timeDim=Error inserting a TimeDim for new interval reading fact.
demo.addmeterreadings.error.duplicates=Meter has existing interval readings for the current date range. Change the date range or select to delete the existing interval readings.
demo.addmeterreadings.error.duplicate.Facts=Meter has existing interval reading Facts for the current date range. Change the date range or select to delete the existing interval readings.
demo.addmeterreadings.minutes.15=15 minutes
demo.addmeterreadings.minutes.30=30 minutes
demo.addmeterreadings.minutes.60=60 minutes

demo.addsupermeterreadings.link=[DEMO] Add Balancing Meter's Interval Readings
demo.addsupermeterreadings.header=Add Balancing Meter's Interval Readings
demo.addsupermeterreadings.title=Balancing Meter Interval Readings
demo.addsupermeterreadings.title.criteria=Balancing Meter's Interval Readings Criteria
demo.addsupermeterreadings.readingtype=Meter Reading Type
demo.addsupermeterreadings.variation=Interval Reading Variation
demo.addsupermeterreadings.variations=Balancing Meter Variations
demo.addsupermeterreadings.variations.help=Balancing Meter's interval readings will match the total of its sub meters. You can specify hours of the day and a percent that will be used to reduce the balancing meter's interval reading so it differs from its sub meters.
demo.addsupermeterreadings.hour=Hour
demo.addsupermeterreadings.supermeter=Balancing Meter
demo.addsupermeterreadings.super.delete.readings=Delete existing Balancing Meter's Interval Readings
demo.addsupermeterreadings.subs.regen.readings=Re-generate Sub Meters Interval Readings
demo.addsupermeterreadings.error.supermeter=Balancing Meter is required.
demo.addsupermeterreadings.error.type=Meter Reading Type is required.
demo.addsupermeterreadings.hour.required=Hour of Day is required.
demo.addsupermeterreadings.percentage.required=Percentage is required.
demo.addsupermeterreadings.variation.duplicate=Hour is already in use in an existing variation.
demo.addsupermeterreadings.invalid.input=Invalid input to generate the balancing meter's interval readings.
demo.addsupermeterreadings.invalid.input.submeters=No sub meters are available for the balancing meter.
demo.addsupermeterreadings.success=Balancing Meter's interval readings were successfully added.
demo.addsupermeterreadings.error.super.duplicates=Balancing meter has existing interval readings for the current date range. Change the date range or select to delete the existing balancing interval readings.
demo.addsupermeterreadings.error.sub.duplicates=There are existing interval readings for the current date range. Change the date range or select to regenerate the interval readings.

export.error=Error: Unable to export data successfully.
export.error.nodata=Error: No data was available to be exported.
export.error.nofile=Error: The file name could not be determined.
export.field.meter=Meter
export.field.metertype=Meter Type
export.field.date=Reading Date
export.field.start=Start Time
export.field.end=End Time
export.field.reading=Reading
export.denied=Access Denied. You need to be logged in to access this functionality.
export.denied.group=Access Denied. You do not belong to the same group as the meter.
export.singlemeter.filename.prefix=MeterReadings-Meter
export.energybalancing.filename.prefix=EnergyBalancing-SuperMeter

taskschedule.title.add=Add a Task Schedule
taskschedule.title.update=Update a Task Schedule
taskschedule.header=Current Task Schedules
taskschedule.title=Task Schedules
taskschedule.title.single=Task Schedule
taskschedule.type=Task Schedule
taskschedule.name=Task Schedule Name
taskschedule.name.help=Specify a name to identify your task schedule.
taskschedule.active=Active
taskschedule.active.help=Whether the task schedule is active and will run as scheduled.
taskschedule.schedule=Date/Time Schedule
taskschedule.schedule.help=Specify when the task schedule will run by selecting an appropriate option and completing the fields.
taskschedule.status=Status
taskschedule.scheduledtask=Task Type
taskschedule.schedule.daily=Once a Day
taskschedule.schedule.weekly=Once a Week
taskschedule.schedule.monthly=Once a Month
taskschedule.schedule.repeatedly=Repeating Per Minutes/Hours
taskschedule.hours=Hours
taskschedule.minutes=Minutes
taskschedule.time=Time
taskschedule.day=Day of the Week
taskschedule.daymonth=Day of the Month
taskschedule.days=Sunday,Monday,Tuesday,Wednesday,Thursday,Friday,Saturday
taskschedule.error.time=Time is a required field.
taskschedule.error.day=Day is required.
taskschedule.error.daymonth=Day is required.
taskschedule.users=Task Users
taskschedule.error.no.user=Search for a user first and then add them as a task user.
taskschedule.error.taskusers=At least one task user or a customer is required.
taskschedule.error.customer.notselected=Customer's name does not match the selected customer's name. Type a surname to selected a valid customer.
taskschedule.user.noemail=User does not have a valid email address. Task users need to have a valid email address to receive scheduled tasks emails.
taskschedule.meter.single=Single Meter
taskschedule.meter.super=Super Meter
taskschedule.meter.readingtype=Meter Reading Type
taskschedule.timeperiods=Time Period
taskschedule.class.error.singlemeter.none=Single Meter is required.
taskschedule.class.error.singlemeter.select=Start typing a meter number and select a meter from the list.
taskschedule.class.error.type=Meter Reading Type is required.
taskschedule.class.error.time=Time Period is required.
taskschedule.class.error.supermeter=Super Meter is required.
taskschedule.class.error.hours=Hours must be a positive number.
taskschedule.taskclass.previous.day=Previous Day
taskschedule.taskclass.previous.week=Previous Week
taskschedule.taskclass.previous.month=Previous Month
taskschedule.error.taskschedule.save=Error saving Task Schedule.
taskschedule.error.taskschedule.none=No Task Schedule specified.
taskschedule.error.scheduledtask.save=Error saving Scheduled Task.
taskschedule.error.scheduledtaskuser.delete=Error deleting User for Scheduled Task.
taskschedule.error.scheduledtaskuser.save=Error saving User for Scheduled Task.
taskschedule.error.scheduling=Error scheduling the Task Schedule.
taskschedule.error.descheduling=Error descheduling the existing Task Schedule.
taskschedule.every=Every
taskschedule.of=Number of
taskschedule.repeatedly.error.number=A positive number is required.
taskschedule.repeatedly.error.units=Select an interval type.
taskschedule.all.supermeters=All Super Meters

scheduledtask.title=Scheduled Tasks
scheduledtask.header=Current Scheduled Tasks
scheduledtask.type=Scheduled Task
scheduledtask.field.name=Scheduled Task Name
scheduledtask.field.name.help=Specify a name to identify your task schedule.
scheduledtask.field.class=Task Type
scheduledtask.field.class.help=Select the type of task to be run on the scheduled date and time.
scheduledtask.title.add=Add a Scheduled Task
scheduledtask.title.update=Update a Scheduled Task
scheduledtask.previous.hours=Previous Hours
scheduledtask.customer.name=Customer
scheduledtask.customer.name.help=Type a customer's surname and select the corresponding customer which should be linked to this scheduled task.
scheduledtask.error.delete.none.selected=No Scheduled Task is selected.
scheduledtask.delete.confirm=Are you sure you want to delete the Scheduled Task?
scheduledtask.error.delete.none=No Scheduled Task is specified.
scheduledtask.error.delete=Unable to delete the Scheduled Task.
scheduledtask.deleted=Scheduled Task was successfully deleted.

scheduledtask.email.subject.taskschedule=Meter Management - Task Schedule:
scheduledtask.email.subject.scheduledtask=Task:
scheduledtask.email.message.taskschedule=Task Schedule:
scheduledtask.email.message.scheduledtask=Task:
scheduledtask.email.message.meter=Meter:
scheduledtask.email.message.start=Start Date:
scheduledtask.email.message.end=End Date:
scheduledtask.email.message=Please find attached the output of your task.
scheduledtask.email.message.supermeter=Super Meter:

password.change.header=Change Password
password.old=Current Password
password.new=New Password
password.confirm=Confirm New Password
password.old.required=Current Password is required.
password.new.required=New Password is required.
password.confirm.required=Confirm New Password is required.
password.new.nonmatching=New and Confirm Passwords do not match.
password.changed=Password was successfully changed.
password.expiry=Your password will expire in {0} day(s).
password.ldap=Your user is authenticated through LDAP so unable to change password here.
password.login.expired=You must change your password, because it has expired.
password.login.reset=You must change your password, because it has been reset.
password.user.change=You must change your password before you can use the application.
password.locked=Your account has been locked.
password.validate.current=Incorrect current password.
password.validation.minUppercase=Password must have at least {0} upper case character(s).
password.validation.minDigits=Password must have at least {0} digits.
password.validation.minLength=Password must have a minimum length of {0} digits.
password.validation.maxLength=Password must have a maximum length of {0} digits.
password.validation.numHistory=You cannot use a password that matches one of the last {0} ones.
password.required=Password is required.
username.required=Username is required.
logged.in=You have been logged in successfully.
login.session.timedout=Your current session has timed out. Please login again to continue using the site.

# Dashboard
dashboard.title=Dashboard
dashboard.meter.count.title=Number of Meters
dashboard.meter.count.description=The current number of meters in the system, by meter model.
dashboard.meter.model.name=Meter Model
dashboard.meter.count=#

## Custom fields
user.custom.fields.title=Custom Fields
user.custom.fields.error.get=Database error getting custom field application settings for {0}! Contact Support.
user.custom.fields.error.unknown.setting=Custom field application setting {0} not catered for. Inform Support.
user.custom.field.status.optional=OPTIONAL
user.custom.field.status.required=REQUIRED
user.custom.field.status.unavailable=UNAVAILABLE

#Import Data ex devise stores - not implemented
import.data.header=Import Data
import.meters.title=Import Meters
import.data.metermodel.help=If a meter model is not specified in the file being imported, the model selected in the drop down list will be used instead
import.data.metermodel=Default Meter Model
import.data.file.help=Select a file containing meter information in the specified csv format for importing into the system
import.data.file=Select Meters File
import.data.button=Import Meters
import.meters.heading=Import Meters onto device store
import.meters.description=Select the CSV file containing meter information for importing into the Meter Management system. <br/>Select a default meter model from the drop down list. This will be used when a meter model is not specified in the file being imported

timezone.change=Change Time Zone
timezone.header=Set Current Time Zone
timezone.warning=Note: All tabs must be closed before the time zone can be changed.
timezone.label=Time Zone

############################################################
#### IpayXml Messages for account balance notifications ####
############################################################
# The defaultAccountAdjustmentProcessor.notification messages have the following arguments:
# arg0=customer.title
# arg1=customer.initials
# arg2=customer.firstnames
# arg3=customer.surname
# arg4=meter number
# arg5=customer agreement ref
# arg6=usage point name
# arg7=account balance
# arg8=low balance threshold
# arg9=emergency credit threshold
# arg10=disconnect threshold
# arg11=reconnect threshold
# arg12=customer account name
# currency format example for account balance: {7,number,currency}
defaultAccountAdjustmentProcessor.notification.disconnect.email.subject=Account balance has run out for {6}
defaultAccountAdjustmentProcessor.notification.disconnect.email.message=Dear Customer,\n\nYour meter will be disconnected.\n\nYour account status is: \n  Account balance: {7,number,currency}\n  Low balance notification threshold: {8,number,currency}\n\nRegards,\nSupport Team
defaultAccountAdjustmentProcessor.notification.disconnect.sms.message=Balance for {6} has run out and will be disconnected. Balance is {7,number,currency} Regards, Support Team
defaultAccountAdjustmentProcessor.notification.lowBalance.email.subject=Account balance low for {6}
defaultAccountAdjustmentProcessor.notification.lowBalance.email.message=Dear Customer,\n\nYour account status is: \n  Account balance: {7,number,currency}\n  Low balance notification threshold: {8,number,currency}\n\nRegards,\nSupport Team
defaultAccountAdjustmentProcessor.notification.lowBalance.sms.message=Balance for {6} is running low. Balance is {7,number,currency} Regards, Support Team

#database actions
db.action.update=update
db.action.insert=insert
db.action.delete=delete

#MDC controltypes
controltype.connect=CONNECT
controltype.disconnect=DISCONNECT
controltype.disconnect.enable=DISCONNECT_ENABLE
controltype.pan.display=PAN_DISPLAY
controltype.sync.balance=SYNC_BALANCE
controltype.adjust.balance=ADJUST_BALANCE
controltype.power.limit=POWER_LIMIT
