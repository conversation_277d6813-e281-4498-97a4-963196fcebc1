### !!! CAUTION !!!
### Before adding keys, ensure you have no duplicates. Duplicates could lead to unexpected behaviour.

# zachv: 2025-05-16 | Planio #30009
calendar.specialday.field.date=Date
calendar.specialday.field.date.help=Enter a date for the special day
calendar.specialday.field.year=Year

# jacciedt: 2025-04-25 | Planio #33761
multiUsagePointAccountAdjustmentProcessor.notification.disconnect.email.subject=Account balance has run out for your account
multiUsagePointAccountAdjustmentProcessor.notification.disconnect.email.message=Dear Customer,\n\nYour meters will be disconnected.\n\nYour account status is: \n  Account balance: {7,number,currency}\n  Low balance notification threshold: {8,number,currency}\n\nRegards,\nSupport Team
multiUsagePointAccountAdjustmentProcessor.notification.disconnect.sms.message=Balance for your account has run out and will be disconnected. Balance is {7,number,currency} Regards, Support Team
multiUsagePointAccountAdjustmentProcessor.notification.emergencyCredit.email.subject=Emergency credit threshold for your account
multiUsagePointAccountAdjustmentProcessor.notification.emergencyCredit.email.message=Dear Customer,\n\nYour account status is: \n Account balance: {7,number,currency}\n  Low balance notification threshold: {8,number}\n  Emergency credit threshold: {9,number,currency}\n\nRegards,\nSupport Team
multiUsagePointAccountAdjustmentProcessor.notification.lowBalance.email.subject=Account balance low for your account
multiUsagePointAccountAdjustmentProcessor.notification.lowBalance.email.message=Dear Customer,\n\nYour account status is: \n  Account balance: {7,number,currency}\n  Low balance notification threshold: {8,number,currency}\n\nRegards,\nSupport Team
multiUsagePointAccountAdjustmentProcessor.notification.emergencyCredit.sms.message=Balance for your account is running low and below emergency credit threshold. Balance is {7,number,currency} Regards, Support Team
multiUsagePointAccountAdjustmentProcessor.notification.lowBalance.sms.message=Balance for your account is running low. Balance is {7,number,currency} Regards, Support Team


# michalv: 2025-05-09 | Planio #32735
export.error.too.many.records=Error: Too many records to export (over {0}). Please filter on a smaller date range.

# joelc: 2025-02-06 | Planio #28349
#power.limit.edit.label.prompt=Name
#power.limit.edit.label.help=Name of the power limit value. This is text display
power.limit.edit.value.prompt=Watt Value
power.limit.edit.value.help=Watt value of the power limit. This value should be numeric
error.field.powerlimit.value.already=This power limit already exists. 

# joelc: 2025-02-13 | Planio #31090
pricingstructure.suggestbox.placeholder = Start typing to filter list


#rodgersn: 2025-02-06 | Planio 29656
engineering.token.display.sgc=Supply Group Code:
engineering.token.display.krn=Key Revision Number:
engineering.token.display.ti=Tariff Index:

#renciac: 2024-08-31 | Planio 17062: MMA: get email from name & address from appsetting not from messages.properties
error.email.invalid=Invalid email address.

# christoe: 2024-07-29 | Planio #28161
import.edit.bulk.mdc.item.update.success=MDC message successfully updated.

# michalv: 2024-10-09 | Planio 25922
meter.models.mdc.required.for.thin.payment=Thin payment mode requires MDC

# christoe: 2024-10-21 | Planio #30157
online.bulk.panel.error.regex.validation1=Regex validation has been configured for UP Name, Agreement Reference or Account Name.
online.bulk.panel.error.regex.validation2=Auto-generation of these fields is disabled and requires manual entry on the Meter tab.

# Michalv: 2024-09-12 | Planio #25404
error.tab.duplicate.auxaccount=AuxAccount open in another tab. Tab: ({0})
error.tab.duplicate.custaccounttrans=Customer Transaction open in another tab. Tab: ({0})

#thomasn: 2023-12-06 | Planio 12164
access_group.lbl=Access Group
access_group.update.header=Update Access Group
access_group.update.group.error=New group has not been selected.
access_group.update.pricing.error=Customer UsagePoint(s) must have GLOBAL Pricing Structure.
access_group.update.future.pricing.clear=This will also clear any future pricing structures.
access_group.update.location.groups.clear=This will clear the usage point and customer location groups.
access_group.update.confirm.lbl=Proceed?
access_group.update.button=Update Access Group
insert.unique.error.meter=The meter details must be unique.
# Used by MMC MapperStaticProxy. e.g. CustomerMapperStaticProxy
insert.unique.error.customer=The customer details must be unique.
insert.unique.error.usagepoint=The usage point details must be unique.
insert.unique.error.devicestore=The device store details must be unique.
insert.unique.error.pricingstructure=The pricing structure details must be unique.
insert.unique.error.auxchargeschedule=The Aux charge schedule details must be unique.

#thomasn: 2023-10-11 | Planio 12164
session_auth.form.submit=Submit
session_auth.form.logout=Logout
session_auth.form.instructions=Choose session authorization details
session_auth.form.role=Role:
session_auth.form.group=Group:
session_auth.form.group_and_role=Group and Role:
session_auth.form.invalid=Login Error. Invalid Form Mode
access_group.error.group_already_cleared=The group has already been cleared on these entities.
access_group.success.updated_group=Successfully updated group
workspace.usagepoint.overview=Overview
workspace.usagepoint.actions=Actions
grouphierarchy.field.is_access_group=Access Group
grouphierarchy.field.is_access_group.help=Whether to connect this group to an access control organisation access group. This will show a list of access control groups on creating groups for this hierarchy level
grouphierarchy.field.is_access_group.error.already_assigned=An organisation access group can only be linked to one hierarchy level.
grouptype.field.location.group=Location Group
groupnode.field.access_group=Access Group
groupnode.field.access_group.help=Connects an access control organisation access group to this group which will restrict access to group users. A group user in a different group would not see this group in selections.
groupnode.field.access_group.error.no_groups=This hierarchy level has enabled organisation access groups but none have been defined.
changegroup.org_group.confirm=Changing your group and/or role selection will reload the application. Would you like to continue?
login.session.reload=You will be redirected to the login screen and the application will be reloaded.
mrid.ui=Unique ID
mrid.ui.help=Enter the unique ID
mrid.ui.external=External Unique ID?
mrid.ui.external.help=Whether unique id is from an external system

# christoe: 2024-06-21 | Planio #28160, #28158
mdc.txn.relay.title=Relay
mdc.txn.relay.help=Select which relay to connect or disconnect.
mdc.txn.relay.main=Main
mdc.txn.relay.aux.one=Auxiliary 1
mdc.txn.relay.aux.two=Auxiliary 2
mdc.txn.power.limit=Power_Limit

# michalv: 2024-06-15 | Planio #29332
error.field.email3=One or multiple email addresses are invalid

# michalv: 2024-07-18 | Planio 28459
online.bulk.panel.encryptionKey.help = Enter the encryption key for this meter. This field is required for activation. To edit an already existing meter, use the UsagePoint Page.

# christoe: 2024-02-06 | Planio #23257 [i-Switch] Send an SMS when an aux charge is loaded
notify.selection.inherit=Inherit ({0})
error.notify.selection.null=It is required to set both preferences.
group.notify.children.change.alert=Children with matching or undefined notification IDs will be updated with these changes. Continue?
group.error.notification.save=Unable to link the notification information to the group.
customer.adjust.aux.accounts=Auxiliary Account Adjustments
customer.new.aux.accounts=New Auxiliary Accounts
customer.notification.types=Notification Types
customer.manage.notification.types=Manage Notification Types

#renciac: 2024-05-20 | Planio 27858: TariffStartDate not on the first of a month at midnight
warning.tariff.start.date.not.on.month.boundary=WARNING: tariff start date is not on a month boundary (the 1st at zero hours).</br>\
Bizswitch must be configured properly for this, as mid-month tariff changes MAY cause billing problems if not handled correctly!</br>\
Please be aware that MONTHLY BILLING Cyclic charges may only start or end on a month boundary.</br>\
Continue?
error.billing.cyclic.change.midmonth.monthly=MONTHLY BILLING Cyclic charges may only be initiated / changed for tariff start ON a month boundary (the 1st of a month at zero hours).

# michalv: 2024-05-22 | Planio 28023
token.reversal.reprinted.error=You do not have permission to reverse a reprinted token.

# rodgersn: 2024-02-20 | Planio #26293
meter.uri.remove.question=Changing the meter model will clear the Meter URI Fields (if captured). Continue?
meter.models.field.uri.present=URI Present
meter.models.field.uri.present.help=This indicates if the meter has URI.
meter.uri.address=Address
meter.uri.address.help=Enter the URI address of the meter.
meter.uri.port=Port
meter.uri.port.help=Enter the URI port number of the meter.
meter.uri.protocol=Protocol
meter.uri.protocol.help=Enter the URI protocol used to communicate with the meter.
meter.uri.params=Parameters
meter.uri.params.help=Enter the URI parameters used when communicating with the meter.
meter.uri.fields=Meter URI Fields
meter.uri.fields.list=Meter URI Fields (if captured): Address, Port, Protocol, Parameters.
meter.uri.port.error=Port must be a number from 0 to 65535.
bulk.upload.meter.uri.address=Meter URI Address
bulk.upload.meter.uri.port=Meter URI Port
bulk.upload.meter.uri.protocol=Meter URI Protocol
bulk.upload.meter.uri.params=Meter URI Parameters
bulk.upload.meter.uri.not.present.address.error=The meter Model can not have a meter URI address.
bulk.upload.meter.uri.not.present.port.error=The meter Model can not have a meter URI port.
bulk.upload.meter.uri.not.present.protocol.error=The meter Model can not have a meter URI protocol.
bulk.upload.meter.uri.not.present.params.error=The meter Model can not have a meter URI parameters.
error.field.meteruriprotocol.max=Meter URI Protocol must not exceed 255 characters.
error.field.meteruriaddress.max=Meter URI Address must not exceed 100 characters.
error.field.meteruriparams.max=Meter URI Parameters value is too long.

# zachv: 2024-05-07 | Planio 28174
tariff.field.bsst.charge_name.title=Charge Name
tariff.field.bsst.charge_name.title.help=Name of the charge that will be displayed on the receipt. 

# joelc: 2024-04-26 | planio-27792
group.type.for.cape.verde.contract = -

# michalv: 2024-04-05 | planio-26839
meter.txn.tokencode1=Token Code/s

# christoe: 2023-12-28 | Planio #25194 Reason Entry for Writing Off Charges in MMA
usagepoint.charge.writeoff.enter.reason=Enter a reason for writing off charges
usagepoint.charge.writeoff.select.reason=Select a reason for writing off charges

# thomasn: 2024-02-21 | planio-25347 [Cape Verde] Analyze and create the Cape Verde tariffs
tariff.blocks.thresholdCharge.error.empty=Where Block Price is set all Threshold Charges must be set or all must be empty.
tariff.blocks.thresholdCharge.error.incomplete=Threshold Charges can be set only where block price exists.

# zachv: 2023-11-25 | Planio #25498
grouptree.show_more = Show more
grouptree.empty = Empty
grouptree.search.help = Type at least the first two letters of an item. The item could be at any level of the hierarchy of data.
suggestbox.placeholder = Type to search...

# renciac: 2023-11-20 | Planio 25211: [TANESCO UAT] Staff tariff as "FBE" with monthly cyclic charge
tariff.field.subsidised.units.title=Monthly Subsidised Units
tariff.field.subsidised.units.descrip=Description
tariff.field.subsidised.units=Units
tariff.field.subsidised.units.help=Units that are issued at a subsidised charge.
tariff.field.bsst.charge.title=Charge
tariff.field.bsst.charge.title.help=Enter the amount to charge for subsidised units 
tariff.error.bsst.charge.positive=Must be a positive value.

# renciac: 2023-11-15 | Planio 25151 [TANESCO] [MMA] Automatically loading debt for a newly installed meter
tariff.field.meter_debt.title=Meter Debt
tariff.field.meter_debt.singlephase.label=Single Phase
tariff.field.meter_debt.singlephase.label.help=Enter Amount of Debt to consumer for pre-loaded single phase units on a new meter.
tariff.field.meter_debt.threephase.label=Three Phase
tariff.field.meter_debt.threephase.label.help=Enter Amount of Debt to consumer for pre-loaded three phase units on a new meter.

# marcod: 2023-08-29| planio 13587
import.upload.file.already.uploaded.group=Duplicate Filename. This file was already uploaded by another group

# marcod: 2023-06-21 | Planio 12175 Bulk device store movements
bulk.device.store.movement.help=Select the end device store the meters should be transferred to
bulk.device.store.movement=Select TO Device Store
bulk.device.store.movement.header=Bulk Device Store Movement
bulk.device.store.movement.without.input.file.not.implemented.yet=Bulk Device Store Movement needs input file of meters. Database setting has_input_file only implemented as 'y' for this filetype yet.
bulk.device.store.movement.param=TO Device Store

# renciac: 2023-09-20 | Planio 21358 View & writeoff also billing cyclic charges
usagepoint.last.cyclic.date.info=Last Cyclic Charge Date
usagepoint.last.cyclic.dates.info=Last Cyclic Charge Dates
usagepoint.last.cyclic.vend.date=At Vend/Topup
usagepoint.last.cyclic.billing.date=At Billing
usagepoint.charge.view.writeoff.date.help=Last cyclic date is the date when periodic charges like daily and monthly charges were last paid by a consumer. Charges after this date have yet to be recovered and may be written off here by selecting a new 'End Date' which becomes the new 'Last Cyclic Date'. Depending on the tariff and payment mode, there are potentially two sets of cyclic charges, one at Vend/Topup time, one at billing time.
usagepoint.charge.view.dialog.invalid.date.both=Date selected cannot be before BOTH last cyclic dates displayed
usagepoint.charge.view.dialog.warning.last.vend.cyclic.date=Last Vend Cyclic Charge already calculated on {0}. Nothing to calculate for VEND cyclic charges. Continue?
usagepoint.charge.view.dialog.warning.last.vend.billing.date=Last Billing Cyclic Charge already calculated on {0}. Nothing to calculate for Billing cyclic charges. Continue?
usagepoint.charge.view.filter.date.help=Outstanding charges will be calculated from the above Last Cyclic dates to this new selected date. It must be AFTER the previous last Cyclic Date/s. 
usagepoint.charge.view.dialog.date=Select End Date for charges
usagepoint.charge.writeoff.vend.heading=VEND/TOPUP CYCLIC CHARGES OUTSTANDING
usagepoint.charge.writeoff.billing.heading=BILLING CYCLIC CHARGES OUTSTANDING
usagepoint.charge.writeoff.vend.total=The total vend charges amount including tax is
usagepoint.charge.writeoff.billing.total=The total billing charges amount including tax is
usagepoint.charge.writeoff.both.total=The total charges amount including tax is

# christoe: 2023-10-04 | planio 22545
special.action.reason.no.reason=No reason given.

# rodgersn: 2023-06-07 | Planio 21001
register.reading.txn.meter=Meter
meterreadings.table.date=Date Created
usage.point.register.reading.txn.description=Register Readings for all meters on this usage point for the time period selected

# christoe: 2023-06-27 | planio 22796
reprint.default.email.message=Dear Customer\n\nPlease find your receipt details below:\n{0}\nKind regards\n{1}

# rodgersn: 2023-02-28 | Planio 15160
meter.assign.from.units.warn=Changing the pricing structure from Thin Units will not migrate the Units balance. A manual adjustment will have to be done. Continue?
ps.paymentmode.change.warn=This change in pricing structure changes the payment mode. Please ensure all the charges and/or billings are up to date. Continue?

# joelc: 2022-12-09 | planio 7589
usagepoint.save.license.error=  Maximum usage points allowed has been reached. Please notify your System Administrator.
bulk.import.license.waring = Importing {0} active Usage Points will exceed the maximum active usage points allowed. <br/>Note that once the limit is reached, only usage points marked as Inactive will be imported.

# marcod: 2023-04-06 | Planio 21668 [ZESCO] Debt collection methods
auxchargeschedule.specific.list.item=ACCOUNT SPECIFIC
customer.auxaccount.principle.amt=Principle Amount

# renciac: 2023-04-01 | Planio 18524 New requirements for RegisterReadingThinTariff
billingdet.appliesto.group.label=Only for Billing dets that apply to others
billingdet.discount.label=Discount
billingdet.discount.help=Check to indicate that this billing determinant signifies a discount, eg. solar rebate.
billingdet.charge.type.help=This billing determinant is either a percentage charge of another billing determinant's charge or a flat rate.
billingdet.applies.to.label=Applies To
billingdet.applies.to.help=Select the master billing determinant to which the percentage value of THIS billing determinant is applied.
billingdet.lnk.error.save=Unable to save the Billing Determinant appliesTo link.
billingdet.lnk.error.both=AppliesTo and Charge_type must both be captured or neither.
billingdet.applies.to.already.in.use=Billing det/s to which this billing det applies are already in use on a tariff or template.
billingdet.applies.to.already.in.use.on.mdc=This billing Det is in use on an MDC, cannot be used as a sub billing det that applies to others. 
billingDet.in.use.cannot.change.settings=This billing det is already in use on a tariff or template, cannot change settings or record status.
billingDet.in.use.cannot.change.applies.to=This billing det or those it applies to is already in use on a tariff or template, cannot change billing dets it applies to.
billingDet.change.regread.panel.open=You currently have a RegisterReadingThinTariff under construction. Saving this billing_det change will cause that to clear. if you need to save changes there first, do not confirm this message, save that tariff first then return here to save this billing_det. Continue with this save now?
billingdet.taxable=Is AppliesTo Taxable? 
billingdet.taxable.help=Unselect if tax is not applicable to this Billing Det - ONLY for billingDets that apply to others
billingdet.lnk.error.taxable=Taxable can only be unselected when this is an appliesTo BillingDet
cyclic.charge.apply.at.lbl=Apply At:    
cyclic.charge.apply.at.vend.lbl=Vend
cyclic.charge.apply.at.billing.lbl=Billing
cyclic.charge.apply.at.error.required=You must select one.
tariff.blocks.unitcharge.negative=Billing Det Unit Charge percentage or discount must not be negative
tariff.save.failed.billingDets.changed.on.database=Save tariff Failed. Percentage / Discount BillingDets have changed on database. Incompatible with input. Refresh the page.
unitcharge.choose.charge.type=Choose type of Charge
unitcharge.type.none=None
unitcharge.type.percentage=Percentage
unitcharge.type.flatrate=Flat Rate
unitcharge.discount.type.charge.error=Discount must be either a percentage or a flat rate 

# thomasn: 2023-03-21 | Planio 17726
supply.group.in.use.error.service=Supply Group in use by one or more meters. Press cancel to load latest.
supply.group.in.use.error.lbl=Supply Group in use by one or more meters. Some fields are read-only while in this state.

# rodgersn: 2023-02-09 | Planio 20655
register.readings.total.consumption=Total consumption:

# thomasn: 2023-01-10 | Planio 18459
error.positive.value=Value must be positive.

# jacciedt: 2022-12-14 | Planio 19775
export.field.receiptnum=Receipt Number

# patrickm: 2022-11-08 | Planio #19650
reprint.total.tax=Total Tax
reprint.total.tax-inclusive=Total (Tax Incl.)

# thomasn: 2022-11-08 | Planio 17785
aux.account.mrid.external.unique.validation=The Unique ID of this aux account is already in use

# marcod: 2022-10-07 | Planio 19234
error.field.powerlimit.name.range=Name must be between 1 and 255 characters.
error.field.powerlimit.name.required=Name is required.
error.field.powerlimit.value.required=Value is required.
error.field.powerlimit.value.type=Value must be an integer and greater than zero.

# rodgersn: 2022-09-29 | Planio 16034
default.template.bulk.uploads=Download Template

# jacciedt: 2022-05-05 | Planio 15256
bulk.blocking.header=Bulk Blocking : Filename: {0}
bulk.blocking.without.input.file.not.implemented.yet=Bulk Blocking generation needs input file of meters. Database setting has_input_file only implemented as 'y' for this filetype yet.
import.upload.blocking.permission.needed=This user does not have permission to bulk upload Blocking instructions.
bulk.blocking.blocking.type=Blocking Type
bulk.blocking.blocking.type.required.field.error=Blocking Type is a required field
bulk.blocking.not.blocked=Not blocked

# thomasn: 2022-08-31 | Planio 17172
usagepoint.ps.unitstocurrency=Changing Pricing structure from thin-units will not migrate the units balance. A manual adjustment will have to be done. Continue?

# rodgersn:2022-07-28 | Planio 18092 Missing message on some translation files
customer.txn.error.amount.incl.tax.is.zero=Amount incl tax cannot be zero

# thomasn: 2022-06-07 | Planio 16665
mdc.txn.pandisplay=Pan Display(Clear Balance)
remove.meter.pandisplay=*Sends MDC Message to clear meter balance.

# jacciedt: 2022-02-28 | Planio 12375
bulk.pricing.structure.change.header=Bulk Pricing Structure Change : Filename: {0}
bulk.pricing.structure.change.without.input.file.not.implemented.yet=Bulk Pricing Structure Change generation needs input file of meters. Database setting has_input_file only implemented as 'y' for this filetype yet.
import.upload.pricing.structure.change.permission.needed=This user does not have permission to bulk upload pricing structure change instructions.
bulk.pricing.structure.change.ps.start.date.error=Start Date must be in the future and greater than the start date of currently active pricing structure and first tariff start date.
bulk.pricing.structure.change.start.date=Start Date
bulk.pricing.structure.change.start.date.help=This is the date that the selected Pricing Structure will start on. Must be future dated based on when the import is done.
bulk.pricing.structure.change.pricing.structure=Pricing Structure
bulk.pricing.structure.change.pricing.structure.help=Pricing Structures that have active tariffs based on selected Start Date. Changing from thin-units will not migrate the units balance. A manual adjustment will have to be done.
import.items.abort=Import Failed:  

# renciac: 2022-05-05 | Planio 15937 curb validation between PS and billing dets esp for TOU
error.pricingStructure.future.ps.no.tariff.at.date=The future pricing structure has no running tariff at the start date selected.

# rodgersn: 2022-05-17 | Planio #16807
customer.txn.error.tax.less.than.amount=The Tax amount cannot be less than the Amount Including Tax for negative values
customer.txn.error.tax.and.amount.different.sign=If both Amount incl Tax and Tax amount are entered, they should be both positive or both negative

# patrickm: 2022-03-24 | Planio #15899
error.tab.duplicate.customer=Customer open in another tab. Tab: ({0})
error.tab.duplicate.meter=Meter open in another tab. Tab: ({0})
error.tab.duplicate.usagepoint=Usage Point open in another tab. Tab: ({0})

# jacciedt: 2022-02-10 | Planio 14296
confirm.bill.payment.reversal=Confirm Bill Payment reversal?
bill.payment.reversal.success=Successful Bill Payment Reversal. Original Ref = {0}, Reversal Ref = {1}
reverse.payment=Reverse Payment

# renciac: 2022-01-25 | Planio 12812 New pricing_structure history
usagepoint.hist.pricing.structure=Pricing Structure
usagepoint.hist.pricing.start=Start Date
usagepoint.hist.pricing.end=End Date
usagepoint.hist.ps.change.reason=Change Reason
up_pricing_structure.header=Pricing Structure History on the Usage Point
up_pricing_structure.sub.header=Previous Pricing Structure changes made to this Usage Point
usagepoint.current.pricing.change.enter.reason=Enter a reason for changing current pricing structure
usagepoint.current.pricing.change.select.reason=Select a reason for changing current pricing structure
usagepoint.future.pricing.change.enter.reason=Enter a reason for changing future pricing structure
usagepoint.future.pricing.change.select.reason=Select a reason for changing future pricing structure
usagepoint.field.pricingstructure=Aktuelle Preisstruktur
usagepoint.field.future.pricingstructure=ZukÃ¼nftige Preisstruktur
usagepoint.ps.date.modified.hd=Last Date Modified
usagepoint.ps.user.modified.hd=User 
usagepoint.ps.change.reason.hd=Change Reason

# jacciedt: 2022-01-19 | Planio 14121
reprint.key.change.notice.line.1=Your resource token is below, but your meter requires a key change before you enter it.
reprint.key.change.notice.line.2=To change your meter's key, enter the tokens listed below:

# thomasn: 2022-01-31 | planio-15259 [BizSwitch & MeterMngCommon] Add support for codes in BlockTariff
tariff.blocks.unitcharge.error.empty=Where Block Price is set all Unit Charges must be set or all must be empty.
tariff.blocks.unitcharge.error.incomplete=Unit Charges can be set only where block price exists.

# marcod: 2021-11-18 | Planio 14768 Indra integration UI changes
bulk.upload.cust.ref=Cust Reference
bulk.upload.external.unique.id=Meter External UniqueId
bulk.upload.external.cust.unique.id=Cust External UniqueId
bulk.upload.external.up.unique.id=UP External UniqueId
mrid.component.error=Unique ID is a required field
group.edit=Edit
customer.ref.label=Customer Reference
customer.ref.help=Enter a unique reference number for the customer. This reference will refer to this particular customer with in the Meter Management System
error.field.customerreference.null=Customer Reference is a required field
error.field.customerreference.range=Customer Reference must be between {min} and {max} characters.
cust.mrid.external.unique.validation=The Unique ID of this customer is already in use
cust.ref.external.unique.validation=The customer reference of this customer is already in use
up.mrid.external.unique.validation=The Unique ID of this usage point is already in use
gen.group.mrid.external.unique.validation=The Unique ID of this group is already in use
meter.model.mrid.external.unique.validation=The Unique ID of this meter model is already in use
gen.group.mrid.external.length.validation=The Unique ID must be between 1 and 100 characters
aux.type.mrid.external.unique.validation=The Unique ID of this aux type is already in use
special.action.reason.mrid.external.unique.validation=The Unique ID of this special action reason is already in use
pricing.structure.mrid.external.unique.validation=The Unique ID of this pricing structure is already in use

# renciac: 2021-12-30 | Planio 15152 Cater for changing payment mode in Pricing Structure
usagepoint.error.new.installdate.before.last.sts.vend.date=Installation date cannot be BEFORE last Vend / Topup Date: {0}
usagepoint.error.new.installdate.before.current.ps.start.date=Installation date cannot be BEFORE the start date of the current Pricing Structure on this usage point: {0}.

# renciac: 2021-12-06 | Planio 14852 [EPC] Zero value in block tariff
tariff.blocks.zero.error=Only the first Block may have a unit price = 0

# renciac: 2021-10-18 | Planio 14521 Aux Account Specific charge schedule
auxspecchargeschedule.title=Aux Account Specific Charge Schedule
auxspecchargeschedule.title.add=Add Aux Specific Charge Schedule
auxspecchargeschedule.title.update=Update Aux Specific Charge Schedule
customer.debt.status.lbl=Debt Status
customer.debt.status.help=DEBT STATUS:<br/><b>Active:</b> balance is not zero and positive<br/><b>Settled:</b> balance is zero <br/><b>Overcollected:</b> balance is negative (i.e Refund)<br/><b>Suspended:</b> suspend-until date is in the future<br/><b>Written Off:</b> LAST transaction for Aux account is type WRITE_OFF and balance = 0
customer.chargeschedule.cycle=Charge Cycle
customer.chargeschedule.chamt=Ladezyklusbetrag

# renciac: 2021-09-05 | Planio 11634, 11636, 13969 ViewOutstandingCharges bugs
usagepoint.charge.view.activation.in.future=Usage point activation date is in the future, has no cyclic charges yet 
usagepoint.charge.writeoff.dialog.heading=List of Outstanding Charges as at {0}

# patrickm: 2021-08-20 | Planio 9834
meter.model.in.use=Meter model has one or more meters attached. Some fields are read-only while in this state.
meter.models.paymentmodes.preselected=Active payment modes cannot be removed while Meter model has meters attached.

# renciac: 2021-06-01 | Planio 11646 Bulk Keychange
supply.group.target.label=Target Supply Group / Key Revision
supply.group.target.label.help=The next Supply Group / Key Revision that this supply group will change to.  
supply.group.target.error.same=Target Supply Group cannot be the same as current
supply.group.target.validation.error=Target baseDate is less than current SGC
supply.group.target.validation.error.expired=Target SGC expiry- or issueUntil date is expired
supply.group.target.validation.nulls= NOTE: Validation between SGC and target SGC was not completed because one or more of base date, expiry date or issue until date is still null, pending update from HSM. Needs manual verification.
supplygroup.field.issued.until.date.label=Issued Until
supplygroup.field.expiry.date.label=Expiry Date
supplygroup.field.target=Target SGC/KRN 
supplygroup.base.date.label=Base Date
supplygroup.base.date.label.help=Base date used for generation of STS6 tokens
supplygroup.target.deactivate.question=This SGC/KRN is being de-activated now, but is still currently in use as a target SGC/KRN for others. Continue? 
supplygroup.base.dates.no.check=A target SGC is in play, but either or both base dates have not yet been updated by the HSM, so cannot validate against each other. Continue?
supplygroup.target.base.date.smaller.question=SGC base date > target SGC base date. Target is older SGC. Continue? 
bulk.Keychange.extract.label.meterNum=Meter Num 
bulk.Keychange.extract.label.userRef=User Ref 
bulk.Keychange.extract.label.token1=Token1 
bulk.Keychange.extract.label.token2=Token2 
bulk.Keychange.extract.label.token3=Token3 
bulk.Keychange.extract.label.token4=Token4 
bulk.Keychange.extract.label.fromSupGroup=From SupGroup 
bulk.Keychange.extract.label.fromKeyRev=From KeyRev 
bulk.Keychange.extract.label.fromTariffIdx=From TariffIdx 
bulk.Keychange.extract.label.fromBaseDate=From BaseDate 
bulk.Keychange.extract.label.toSupGroup=To SupGroup 
bulk.Keychange.extract.label.toKeyRev=To KeyRev 
bulk.Keychange.extract.label.toTariffIdx=To TariffIdx 
bulk.Keychange.extract.label.toBaseDate=To BaseDate 
bulk.Keychange.extract.label.transDate=TransDate 
bulk.Keychange.extract.label.userRecEntered=User generated 
bulk.Keychange.extract.label.importFileName=Import Filename
bulk.Keychange.extract.label.bulkRef=Bulk Ref 
bulk.Keychange.extract.none=No Keychange found for this import file
button.submit=Submit
action.params.header.label=Parameters
import.file.explanation=Upload file containing data and / or parameters for action
bulk.keychange.header=Bulk Key Change : Filename: {0}
bulk.keychange.to.header=KeyChange TO:
bulk.keychange.use.target=Use pre-captured Target SGC/KRN 
bulk.keychange.use.targetHelp=Target SGC/KRN is captured on the Supply Group page under Meters Menu. If selected, then each meter's SGC/KRN will be mapped to it's target. If no target for a particular SGC/KRN, will get an error unless also enter a specific SGC/KRN in the TO, then THAT will be used when no target. 
bulk.keychange.use.target.selected.message=Can select 'Use Target' as well as select supply Group values. If no values and an SGC/KRN has no target, will get an error. If enter values as well, these will be used for those with no target. 
bulk.keychange.supplygroupcode=New Supply Group Code
bulk.keychange.supplygroupcode.help=Select the new supply group code. 
bulk.keychange.supplygroupcode.error.required.no.target=At least, the Supply Group Code must be selected.
bulk.keychange.tariffindex.required=Tariff Index is required
bulk.keychange.tariffindex=New Tariff Index
bulk.keychange.tariffindex.help=Enter the tariff index to be changed. Required. If a meter currently has a different tariff index, a keychange will be generated.
bulk.keychange.instruction.label=Generate Key Change Tokens Instruction 
bulk.keychange.instruction.help=Issue instruction as to when the keychange tokens must be generated.
bulk.keychange.instruction.generate.keychanges.now=Generate and update Key Tokens now
bulk.keychange.instruction.generate.keychanges.next.vend=Set to generate Key Tokens with next vend
bulk.keychange.instruction.generate.keychanges.next.vend.after.date=Set to generate Key Tokens with next vend AFTER a date....
bulk.keychange.generate.keychanges.next.vend.after.date=Keychange with vend AFTER Date
bulk.keychange.generate.keychanges.next.vend.after.date.help=Keychanges will be generated at vend_time but only AFTER this date
bulk.keychange.after.date.error=After Date cannot be in the past. Code checked against now() being {0}
bulk.keychange.after.date.required=After Date is required for selected bulk instruction
bulk.keychange.overwrite.existing=Overwrite existing New SGC/KRN details?
bulk.keychange.overwrite.existing.help=Must select radiobutton to handle the case when new Supply Group details already exist on a meter: Overwrite and continue, or leave existing details and abort the keychange instruction for such meters.
bulk.keychange.overwrite.existing.error=Must choose one, overwrite or not.
import.upload.keychange.permission.needed=This user does not have permission to bulk upload KeyChange instructions. 
import.upload.cannot.change.action.params.now=An import was logged, action params can no longer be edited, only viewed. To redo items with different action parameters, will need to re-import in a new file.
import.upload.view.params.label=Params
button.process.selected=Process Selected
button.process.all=Process All
button.import.extract.keychanges=Extract Generated KeyChanges
bulk.keychange.without.input.file.not.implemented.yet=Bulk Keychange generation needs input file of meters. Database setting has_input_file only implemented as 'y' for this filetype yet.
import.items.selected.success=Selected Items successfully processed.
import.items.errors= Not all items were successfully processed.
import.items.all.success=All ImportFile items successfully processed.
import.extract.items.non=No items to extract
import.extract.items.exception=Extract Failed. Please contact Support.
import.upload.twirly.waiting.text.keychange=Keychange requests may take a while
import.upload.keychange.bulkref.label=KeyChange BulkRef 
meter.txn.bulk.import.file.name=Import FileName

# renciac: 2021-06-01 | Planio 12963 Extension to Generic upload framework for action params
import.file.parameters.needed= Please confirm and submit parameters for the import of this data. 
import.file.parameters.needed.no.data=Please confirm and submit parameters for bulk changes.\nA dummy filename will be generated.\nIf data IS needed as well as parameters import will be rejected.
import.file.param.no.data.dummy.filename= Generated filename is {0}
import.file.no.parameters=This file type {0} has no UI defined for action parameters. Contact support. Dummy file created.
import.file.no.parameters.or.setting=This file type {0} has no UI defined for action parameters or database setting for has_input_file should be {1}. Contact support.
import.file.params.save.error=Unable to save the import file with action params.
import.file.no.params.converter.error=There is no JSON parameter converter for the action params of this import file
import.file.get.params.fail=Setting up parameter panel failed. Check your filename and file type.
import.file.parameters.updated=Parameters were updated for filename: {0}
import.upload.file.settings.conflict=No file was selected to be uploaded, and conflict in bulk file type settings: input data is n or b, but action params is false
import.upload.file.no.input.data=Bulk File Type setting has_input_file = 'n', no filename needed.
import.upload.file.needs.action.params=This file type requires action parameters. Please capture on upload page.

# thomasn: 2021-08-16 | planio-10426
usagepoint.ps.start.date.lbl=Start Date
usagepoint.ps.name.lbl=Pricing Structure
usagepoint.ps.start.date.help=The date this pricing structure will be activated on. This start date is unique.
usagepoint.ps.start.date.error=Start Date must be in the future and greater than the start date of currently active pricing structure and first tariff start date.
usagepoint.ps.start.date.error.unique=Start Date must be unique. A pricing structure already exists with this start date.
usagepoint.ps.view.all.btn=View All
usagepoint.ps.delete.btn=Delete
usagepoint.ps.delete.btn.confirm=Are you sure you want to delete the future pricing structure?
usagepoint.ps.save.error=Saving of usage point pricing structure failed.
usagepoint.ps.delete.error=Deleting of usage point pricing structure failed.
usagepoint.ps.delete.success=Deleted usage point pricing structure successfully.
usagepoint.ps.required=Pricing Structure required.
usagepoint.ps.future.required=Future pricing structure should not be the same as the current one above.
usagepoint.ps.future.list.help=Select the pricing structure then set a start date for it.
usagepoint.ps.future.date.help=Start date for the selected future pricing structure.
usagepoint.ps.future.lbl=Future Pricing Structure
usagepoint.ps.future.start.date.lbl=Future Pricing Structure Start Date
usagepoint.ps.historic.error=Usage point has historic pricing structure which are not compatible with the meter model. Use new usage point.
meter.new.current.pricingstructure.required=Meter ({0}) does not support the current pricing structure - {1}.
meter.new.current.pricingstructure.select=Current Pricing Structure\n(If changed all future ones are deleted)
tariff.error.save.up.ps.start.date.conflict=Unable to update the tariff start date, pricing structure already added to usage point with start date {0}.
usagepointworkspace.error.meter.unsupported.model.current=The model of meter {0} does not support the usage point's current pricing structure.
warning.pricingStructure.billingDets.notsame.asmetermodel.mdc=WARNING: The Billing Determinant/s covered by the pricing structure(s) current tariff(s) are a PARTIAL match to those used by the MDC Channels connected to the Meter Model. Continue?
warning.pricingStructure.billingDets.notsame.asmetermodel.mdc.save.UP=WARNING ON SAVE: The Billing Determinant/s covered by the pricing structure(s) current tariff(s) are a PARTIAL match to those used by the MDC Channels connected to the Meter Model. Continue?
warning.pricingStructure.billingDets.notsame.asmetermodel.mdc.activate.UP=WARNING ON ACTIVATE: The Billing Determinant/s covered by the pricing structure(s) current tariff(s) are a PARTIAL match to those used by the MDC Channels connected to the Meter Model. Continue?
error.pricingStructure.billingDets.notsame.asmetermodel.mdc=ERROR: The Billing Determinant/s covered by the pricing structure(s) current tariff(s) have NO MATCHES to those used by the MDC Channels connected to the Meter Model. If not a exact match, there should be at least one matching.
error.pricingStructure.billingDets.notsame.asmetermodel.mdc.save.UP=ERROR ON SAVE: The Billing Determinant/s covered by the pricing structure(s) current tariff(s) have NO MATCHES to those used by the MDC Channels connected to the Meter Model. If not a exact match, there should be at least one matching.
error.pricingStructure.billingDets.notsame.asmetermodel.mdc.activate.UP=ERROR ON ACTIVATE: The Billing Determinant/s covered by the pricing structure(s) current tariff(s) have NO MATCHES to those used by the MDC Channels connected to the Meter Model. If not a exact match, there should be at least one matching.
question.confirm.installation.date.3=Activation date change conflicts with Pricing structures. Current PS will be deleted and Future PS will be made current.

# marcod: 2021-07-27 | Planio 12179
supplygroup.field.kmc.expirydate=KMC Expiry Date
supplygroup.panel.kmc.expirydate.help=The system will automatically send an email warning every day for vending keys that are about to expire.

# renciac: 2021-07-21 | Planio 13124 Debt Instalments
aux.charge.sched.cycle.label=Charge Cycle
aux.charge.sched.cycle.label.help=For Ad-hoc, charge with every vend. For Daily / Monthly determines the cycle of when charges are levied
aux.charge.sched.cycle.amount.label=Cycle Amount
aux.charge.sched.cycle.amount.label.help=An ad hoc amount is charged on each vend. Daily or Monthly cycle amounts will be charged once a day or once a month depending on the cycle; until the Debt is paid off.
aux.charge.sched.cycle.select.error=Charge cycle selection must accompany entry of Charge Cycle amount 
aux.charge.sched.cycle.instalment.label=Daily or Monthly Cycle Instalments are standalone amount entries
customer.auxaccount.start.date.lbl=Start Date
customer.auxaccount.start.date.help=Select the auxiliary account start date
customer.auxaccount.suspend.until.help=Temporarily suspend an auxiliary account until the set date in this field. For Debt Instalment charge schedules, take note of the appSetting for 'Accumulate Aux instalments during Suspension'.
customer.auxaccount.last.charge.date.lbl=Date Last Charged
customer.auxaccount.last.charge.date.help=Last date when a vend transaction was made that paid towards this account. Does not reflect manual payments or account adjustments, only actual payments via vends/topups. Note that for charge schedules which are NOT instalments, this might have been a part payment, not a full charge.
error.field.value.monthly.charge=Value must be one of PRO_RATA, FREE or FULL 
customer.auxaccount.suspend.until.smlr.start=Suspend Until Date should not be before Start Date 
customer.auxaccount.install.suspend.info=Charge schedule using Instalments follows specific behaviour after suspension. \r\nSee appSetting 'accumulate_aux_during_suspension' 
vend.older.trans.info=Note that when reversing a transaction which is NOT the last transaction for the customer agreement, last purchase details are not reset and manual intervention is needed for future transactions in the same month. The last aux payment date on auxiliary accounts is also not reset.
vend.reversal.last.with.older=Note that there were multiple reversals this month, last purchase details are not reset and manual intervention is needed for future transactions in the same month. The last aux payment date on auxiliary accounts is also not reset.
auxaccount.upload.startDate=Start Date
auxaccount.upload.startDate.greater=Start Date may not be greater than Suspend Until
auxaccount.upload.startDate.format=Start Date must be either empty or properly formatted
auxaccount.upload.startDate.invalid.date=Start Date is an invalid date
auxaccount.upload.suspendUntil.invalid.date=Suspend Until is an invalid date
trans.bulk.upload.format.error.trans.date=Transaction date must be either empty (defaults to date of processing) or properly formatted (yyyy-MM-dd HH:mm:ss)
trans.bulk.upload.invalid.trans.date=Transaction date is invalid

# marcod: 2021-07-08 | Planio 12735
error.usagepoint.outdated=ERROR: Data on this tab is outdated due to another update. Please click reload to refresh the data.
button.reload=Reload

# jacciedt: 2021-04-15 | Planio 12792
sts.unit.generation.limit.error=The amount of units to be issued cannot be more than the configured amount of {0} units.

# thomasn: 2021-07-22 | Planio 5812
usagepoint.meter.inspection.request.btn=Send Meter Inspection Request
usagepoint.meter.inspection.request.setup.error=The Contract is not complete, confirm Physical Address data is there.
usagepoint.meter.inspection.request.processing.error=An error occurred processing the request.
usagepoint.meter.inspection.request.meterMng000=Meter inspection request processed OK. Reference={0}
usagepoint.meter.inspection.request.meterMng001=Meter inspection request general error. Reference={0}
usagepoint.meter.inspection.request.meterMng011=Meter inspection request error customer data incomplete or invalid. Reference={0}
usagepoint.meter.inspection.request.txt.comment=Enter Comment
usagepoint.meter.inspection.request.txt.comment.help=A description of the reason.

# jacciedt: 2021-04-15 | Planio 9695
no.aux.charge.schedule.defined=No Aux Charge Schedule defined

# jacciedt: 2021-03-05 | Planio 12340
customer.txn.error.tax.more.than.amount=The Tax amount cannot be more than the Amount Including Tax
customer.auxaccount.error.refund=The new balance after the adjustment will be {0}. You are not allowed to change this account into a refund.
customer.auxaccount.error.debt=The new balance after the adjustment will be {0}. You are not allowed to change this account into a debt.

# marcod: 2021-07-15 | Planio 13708
reprint.customer=Customer

# thomasn: 2021-08-18 | Planio 13381
meter.txn.engineeringtokens.column=Has Engineering Tokens

# patrickm: 2021-07-02 | Planio 13126
supplygroup.field.code.default=Default

# jacciedt: 2021-06-30 | Planio 12839
up_meter_install.remove.date=Remove Date
up_meter_install.install.ref=Install Ref
up_meter_install.remove.ref=Remove Ref
up_meter_install.header=Usage Point Meter Installations
up_meter_install.sub.header=Previous Usage Point Meter Installations made to this Usage Point

# renciac: 2021-06-28 | Planio 13515 Writeoff charges duplicates
usagepoint.charge.button.close.writeoff.and.unassign.customer=Close and Unassign Customer

# marcod: 2021-05-25 | Planio 12620
search.meter.sgc.label1=Supply Group / Key Revision
search.meter.sgc.label2=(Current or New)
search.meter.sgc.help=The search will find meters with a current or new supply group code equal to the selected SGC/KRN. A new SGC/KRN on a STS meter is populated only when a keychange is actioned. As from next vend it then becomes the current one.

# jacciedt: 2021-02-16 | Planio 11622
defaultAccountAdjustmentProcessor.notification.lowBalanceNoUsagePoint.email.subject=Account balance low for account {12} on agreement {5}
defaultAccountAdjustmentProcessor.notification.lowBalanceNoUsagePoint.email.message=Dear Customer,\n\nYour account status is: \n  Account balance: {7,number,currency}\n Low balance notification threshold: {8,number,currency}\n\nRegards,\nSupport Team
defaultAccountAdjustmentProcessor.notification.lowBalanceNoUsagePoint.sms.message=Balance for account {12} on agreement {5} is running low. Balance is {7,number,currency} Regards, Support Team
bill.payments=Bill Payments
bill.payments.provider=Provider
bill.payments.reversal.request.received=Reversal Request Received
bill.payments.pay.type=Pay Type
bill.payments.pay.type.details=Pay Type Details
bill.payments.vote.name=Vote Name
bill.payments.description=A list of bill payments made by this customer
bill.payments.transaction.type=Bill Pay Transaction Type

# renciac: 2021-05-25 | Planio 13153 PS date validation vs installDate
usagepoint.installation.date.before.tariff.start1=Installation date is before tariff start date {0}. <br/> The implication is that potential Tariff charges that are due before tariff start date will not be calculated.
usagepoint.installation.date.before.tariff.start2=<br/> Last date that cyclic charges were calculated was {0}.
usagepoint.installation.date.before.tariff.start3=<br/> No Last cyclic charge calculation date on file.
usagepoint.installation.date.before.tariff.start4=<br/> Continue?

# renciac: 2021-04-20 | Planio 7918 Bulk Tariff Generator
error.field.customerdescription.max=Customer description must be less than {max} characters.
button.export.ps.title=Export ALL pricing structure Current tariffs 
export.ps.failed.exception=Export failed. Please contact Support.
export.ps.failed.non=No Pricing Structures with current tariffs to export ???
import.edit.item.update.bulk.tariff.success=Data for tariff {0} was successfully updated.
import.ps.name.label=Pricing Structure
import.tariff.name.label=Tariff
import.tariff.edit.resave.error=Error: calcContents: {0}
import.upload.file.already.uploaded=Error: File already uploaded. Check table.
import.upload.tariff.permission.needed=This user does not have permission to import tariffs.

# patrickm: 2021-03-18 | Planio 11152
defaultAccountAdjustmentProcessor.notification.emergencyCredit.email.subject=Emergency credit threshold for {6}
defaultAccountAdjustmentProcessor.notification.emergencyCredit.email.message=Dear Customer,\n\nYour account status is: \n Account balance: {7,number,currency}\n  Low balance notification threshold: {8,number}\n  Emergency credit threshold: {9,number,currency}\n\nRegards,\nSupport Team
defaultAccountAdjustmentProcessor.notification.emergencyCredit.sms.message=Balance for {6} is running low and below emergency credit threshold. Balance is {7,number,currency} Regards, Support Team

# jacciedt: 2021-03-12 | Planio 12494
error.field.validity.message.content=The message content cannot be blank.

# jacciedt: 2021-03-15 | Planio 11902
customer.account.does.not.exist=Customer Account does not exist yet

# renciac: 2021-03-04 | Planio 12582
bulk.ignore.dup.meters=Ignore Duplicate Meters
bulk.ignore.dup.meters.help=If meter number already exists on Database, ignore one in upload file. If duplicates in upload file, use first one.
bulk.upload.ignore.meter.dups.changed=Ignore duplicate meters setting has changed between steps! Was {0}; now {1}
bulk.upload.successful.meter.upload=Total of {0} meter uploads successfully processed, {1} duplicates ignored.

# marcod: 2021-02-01 | Planio 12168
email.password.reset.message=We have received a password reset request for {0}.<br> To reset your password please click the link below.<br> {1}.
password.link.expired=The password reset link has expired. You can request another one.
password.link.used=The password reset link has been deactivated.
password.change.now= Change Password Now
password.reset.success=Your password has been changed successfully.

# jacciedt: 2021-02-12 | Planio 12340
unitsacc.balance.with.symbol=Units Balance ({0})

# jacciedt: 2021-02-04 | Planio 12330
bulk.upload.heading.metercustup=Generate Template for MeterCustUp Bulk Upload
bulk.upload.metercustup.notice=For Meter/Customer/UsagePoint Bulk Uploads, use the menu option -> Configuration -> Upload and Import Files

# jacciedt: 2021-02-05 | Planio 11950
demo.addmeterreadings.tariffCalc.failed={0} : {1} successfully added, but tariff calculation failed.
demo.addmeterreadings.success={0} : {1} successfully added.
demo.addmeterreadings.tariffCalc.success={0} : {1} successfully added and tariff calculation completed.

# jacciedt: 2021-01-08 | Planio 12082
bulk.upload.unitsaccountname=Units Account Name
bulk.upload.unitslowbalancethreshold=Units Low Bal Threshold
bulk.upload.unitsnotificationemail=Units Notif Email
bulk.upload.unitsnotificationphone=Units Notif Phone

# jacciedt: 2020-12-22 | Planio 11146
error.date.field.invalid=Value entered is not a valid date. Format = {0}

# renciac: 2020-12-10 | Planio 11365
### Units Account ###
unitsacc.title=Units Account
unitsacc.name.help=Enter a name for this account
unitsacc.name=Units Account Name
unitsacc.balance.help=The current units balance
unitsacc.balance=Units Balance
unitsacc.sync.help=Synchronize the units balance with the units balance on the meter
unitsacc.sync=Synchronize Balance
unitsacc.low.balance.threshold.help=When the units balance reaches this threshold a message will be sent to the customer.
unitsacc.low.balance.threshold=Units Low Balance Threshold
unitsacc.notification.email.help=A comma separated list of email addresses to which units related notifications can be sent (e.g. when the low balance threshold has been reached)
unitsacc.notification.email=Notification Email Addresses
unitsacc.notification.phone.help=A comma separated list of phone numbers to which units related notifications can be sent (e.g. when the low balance threshold has been reached)
unitsacc.notification.phone=Notification Phone Numbers
unitsacc.note=A units account is only necessary if the meter model and the pricing structure require it.
unitsacc.required=* \= Required
unitsacc.changes.cleared=Changes have been cleared.
units.account.error.save=Unable to save the units account.

# jacciedt: 2020-11-27 | Planio 11366
units.account=Units Account
units.account.transaction.history=Units Account History
units.account.transaction.description=Previous Account transactions for this Units Account
amount.cannot.be.zero=Amount cannot be zero
units.transaction.type=Units Transaction Type
units.transaction.type.sale=Sale
units.transaction.type.consumption=Consumption
units.transaction.type.manualadj=Manual Adjustment
units.transaction.type.reversal=Reversal

# renciac: 2020-11-25 | Planio 11363
# The defaultUnitsAdjustmentProcessor.notification messages has the same arguments as the defaultAccountAdjustmentProcessor, except units instead of currency
defaultUnitsAdjustmentProcessor.notification.disconnect.email.subject=Account balance has run out for {6}
defaultUnitsAdjustmentProcessor.notification.disconnect.email.message=Dear Customer,\n\nYour meter will be disconnected.\n\nYour account status is: \n  Account balance: {7,number}\n  Low balance notification threshold: {8,number}\n\nRegards,\nSupport Team
defaultUnitsAdjustmentProcessor.notification.disconnect.sms.message=Balance for {6} has run out and will be disconnected. Balance is {7,number} Regards, Support Team
defaultUnitsAdjustmentProcessor.notification.lowBalance.email.subject=Account balance low for {6}
defaultUnitsAdjustmentProcessor.notification.lowBalance.email.message=Dear Customer,\n\nYour account status is: \n  Account balance: {7,number}\n  Low balance notification threshold: {8,number}\n\nRegards,\nSupport Team
defaultUnitsAdjustmentProcessor.notification.lowBalance.sms.message=Balance for {6} is running low. Balance is {7,number} Regards, Support Team

# thomasn: 2020-10-16 | planio-9668
meter.models.battery.capacity=Capacity
meter.models.battery.capacity.help=Enter the full battery capacity value. Either in %, Months,Voltage etc.
meter.models.battery.capacity.error=Value must be positive.
meter.models.battery.threshold=Low Threshold
meter.models.battery.threshold.help=Enter the low battery threshold percentage. When crossed a low battery event will be triggered.
meter.models.battery.threshold.error=Value must be between 0 and 100.
meter.models.battery.event.lbl=Battery Event

# renciac: 2020-10-16 | Planio 11204
usagepoint.installdate.change.error.readings=Cannot change installation date, new readings on the meter / usage point, please refresh the page.
meter.replace.installation.date.error.trans=Cannot replace meter with a future installation date, new transactions on the usage point, or new readings on the meter; please refresh the page and try again.

# renciac: 2020-06-09 | Planio 9616
meter.change.activation.date=Change Activation date = New Installation date?
meter.change.activation.date.required=Please check one of the boxes for Activation Date
meter.change.activation.date.error.trans=Cannot change activation date, transactions on the usage point, or new readings on the meter; please refresh the page.
meter.change.installation.date.error.trans=Cannot change installation date, transactions on the usage point, or new readings on the meter; please refresh the page.
error.field.installdate.future.trans.or.possible.gap=Cannot have a future installation date. It can only be in the future if has no transactions on the UP and if have chosen to change the activation date = installation date.

# marcod : 2020-09-09 | Planio 10498
meter.units.help=Enter the number of {0} units to one decimal place only.

# marcod : 2020-10-08 | Planio 9723
meter.number.suggestion.help=Start typing the meter number, meters that start with those digits will appear in a dropdown. Click on one to select it.

# thomasn: 2020-09-01 | Planio 8404
auxaccount.upload.suspendUntil=Suspend Until
auxaccount.upload.suspendUntil.in.past=Suspend Until may not be in the past
auxaccount.upload.suspendUntil.format=Suspend Until must be either empty or properly formatted
customer.auxaccount.suspend.until.lbl=Suspend Until
customer.auxaccount.suspend.until.error=Date must be in the future.
customer.auxaccount.suspend.until=Suspended Until
customer.auxaccount.txn.history.suspend.until=Suspended Until : {0}

# jacciedt: 2020-08-13 | Planio 10007
auxtype.error.update.in.use=Unable to deactivate the auxiliary type, it is already in use.

# jacciedt: 2019-01-29 | Planio 8575
bulk.upload.invalid.regex={0} does not match its regex pattern

# jacciedt: 2020-06-17 | Planio 9605
demo.addmeterreadings.weekly=Weekly

# jacciedt: 2020-04-09 | Planio 6150
customer.unassign.unassign.customer=Unassign Customer
usagepoint.charge.button.writeoff.and.unassign.customer=Writeoff Charges and Unassign Customer

# joelc: 2020-07-10 | Planio 9609
reprint.remaining.balance=Balance 
reprint.desc=Description

# renciac: 2019-12-03 | Planio 5311
file.item.panel.reg.read.reminder=REMINDER: Any meters with register reading tariffs might need an initial reading.
channel.readings.header.up=Usage Point: 
channel.readings.timestamp.label=Reading TimeStamp
channel.readings.timestamp.help=Reading timeStamp must be equal to installation date OR greater than existing reading timestamp for this meter installation 
channel.readings.table.error.heading=Error
channel.readings.partial.entry=The initial register readings for this meter's channels are not or only partially completed. Do you want to save what you have and complete the rest later?
channel.readings.preExisting.note=NOTE: There are pre-existing readings for this meter and usagepoint installation. 
channel.readings.preExisting.same.mdc.channels=These are from the same MDC Channels, as below in the table. \nPress CANCEL to keep these as is.\nIf you wish to change the init readings, enter new values. \nNote: Reading TimeStamp must be > previous.
channel.readings.preExisting.diff.mdc.channels=These seem to be from previous MDC Channels.\nLast reading date found was: {0}.\nEnter new values for the new MDC channels. Note: Reading TimeStamp must be > Prevous Last Reading Date.
channel.readings.preExisting.note.end=\nIf none of these options are desirable, please contact System Support. 
channel.readings.timestamp.install.date=Reading time stamp must be equal to installation date.
channel.readings.timestamp.previous.date=Reading time stamp must be greater than previous reading date: {0} 
button.ok=OK

warning.change.mdc.on.meter.NO.DATA=WARNING: Changing the MDC on the meterModel might affect {0} active and {1} inactive usage points which do not have Register Reading Tariffs. Continue?
warning.change.mdc.on.meter.PARTIAL.or.TOTAL.active.up=WARNING: Changing the MDC on the meterModel might affect {0} active and {1} inactive usage points with Register Reading Tariff and PARTIAL or EXACT billing determinant match. Also NO match on inactive usage points can be changed here (when they are activated NO match will be rejected). Continue?
error.change.mdc.on.meter.NONE.MATCH.active.up=ERROR: Changing the MDC on this meterModel has {0} ACTIVE usage-points with Register Reading Tariffs and NO MATCHES on the billing determinants of some. Cannot change the mdc on the meter-model now.

warning.change.mdc.channel.NO.DATA=WARNING: Changing / (assigning a new) channel on this MDC might affect {0} active and {1} inactive usage points which do not have Register Reading Tariffs. Continue?
warning.change.mdc.channel.PARTIAL.or.TOTAL.with.regreadPS=WARNING: Changing / (assigning a new) channel on this MDC might affect {0} active and {1} inactive usage points with Register Reading Tariff and PARTIAL or EXACT billing determinant match. Continue?
error.change.mdc.channel.NONE.MATCH.active.up=ERROR: Changing / (assigning a new) channel on this MDC has {0} ACTIVE usage-points with Register Reading Tariffs and NO MATCHES on the billing determinants of some. Cannot change the channels / billing determinants now.

warning.change.mdc.NO.DATA=WARNING: Changing the MDC will affect {0} active and {1} inactive usage points which do not have Register Reading Tariffs. Continue?
warning.change.mdc.inactive.with.regreadPS=WARNING: Changing the MDC might affect {0} active and {1} inactive usage points with Register Reading Tariffs. Continue?
error.deactivated.mdc.active.up=ERROR: Cannot deactivate this MDC - has {0} active and {1} inactive usage points with Register Reading Tariffs. 

warning.change.or.new.tariff.has.diff.billing.dets.to.active.up.with.ps=WARNING: Changing / creating a new Register Reading tariff on this Pricing Structure has PARTIAL match of billing determinants to some of the active usage points using the pricing structure. Continue?
error.change.or.new.tariff.has.diff.billing.dets.to.active.up.with.ps=ERROR: Changing / creating a new Register Reading tariff on this Pricing Structure has NO MATCH to billing determinants of some of the active usage points using the pricing structure. There should be at least a partial match in ALL cases.
warning.change.status.billing.det.but.in.use=Changing the status of this billing determinant might affect {0} active and {1} inactive usage points with meters assigned to a meter model that use an MDC with channels assigned to this billing determinant. Continue?
error.cannot.activate.up.model.not.channels.with.regread.ps=ERROR! Cannot activate the Usage Point. The meter model and pricing structure must have at least one channel with the same billing determinant in common.
error.incompatible.model.with.ps=ERROR! The meter model and pricing structure are not compatible. \nEither the meter model has channels but not Register reading Pricing Structure or vice versa.

# renciac: 2019-11-07 | Planio 7951
channel.field.maxsize.help=Maximum reading on the meter's register before rollover to zero
channel.field.time.interval.help=Time interval of register readings
channel.field.time.interval=Time Interval
channel.config.header=Channel Overrides
channel.config.overrides.button=Override MDC Channels
channel.config.mdc.titlename=Mdc
channel.config.field.titlename=Meter Channel Override
channel.config.title.add=Add Meter Channel Override
channel.config.title.update=Update Meter Channel Override
channel.field.titlename.help=Only shows Mdc Channels not yet overridden - must select an MDC Channel to override
channel.config.field.billingdetnames=Billing Determinant/s
channel.config.error.delete=Unable to delete the Meter Channel Override. Contact support
channel.config.delete.confirm=Are you sure you want to delete the Meter Channel Override?
channel.config.deleted=The Meter Channel Override was successfully deleted.
mdc.channel.override.metermodels=Override from Meter Model/s
mdc.channel.override.metermodels.none=No Overrides
channel.config.already.override=Channel already has override. Selected from above. 
channel.field.meter.reading.type.title=Reading Type
meter.model.change.mdc.confirm.delete.configs=Meter model had channel overrides for the previous MDC on this model. Will delete those if go ahead with change of MDC. Continue?
meter.model.channel.configs.error.delete=Unable to delete the Meter Channel Overrides for previous MDC. Contact support
channel.config.no.channels.to.override=No channels defined for this MDC. 
mdc.channel.status.change.warn.overrides=Note: this mdc channel has overrides. Continue with status change?
mdc.channel.field.time.interval=MdcChannel Time Interval
channel.override.field.time.interval=Override Time Interval
mdc.channel.field.maxsize=MdcChannel Reading Max Size
channel.override.field.maxsize=Override Reading Max Size
mdc.channel.field.reading_multiplier=MdcChannel Reading Multiplier
channel.override.field.reading_multiplier=Override Reading Multiplier
import.edit.reg.Read.item.update.success=Data for meter {0}, reading Timestamp {1} successfully updated.
import.reg.Read.channel.value.label=Channel Value
import.reg.Read.timestamp.label=Reading Timestamp
reg.read.init.readings.cancel.confirm=Cancelling means no further initial readings for this meter's channels (registers) are saved and tariff calculations will use the first or existing readings as the initial readings. Continue?
channel.readings.import.note=Note: Register readings can also be imported manually.

# barryc: 2020-05-12 | Planio 8211
tariff.error.monthlycost.name=Specify a name for the charge
tariff.error.monthlycost=A valid number must be provided
tariff.error.monthlycost.positive=Value must be positive or zero

# barryc: 2020-04-21 | Planio 7780
error.field.specialactionsdescription.max=Beschreibung muss zwischen {min} und {max} Zeichen lang sein.

# jacciedt: 2020-04-14 | Planio 8139
meter.model.deactivate.in.use.error=Cannot deactivate - there are already meters using this meter model.

# jacciedt: 2020-04-01 | Planio 7873
question.confirm.installation.date.future.date=future date
usagepoint.field.meter.activation.date=Initial Activation Date

# barryc: 2020-04-22 | Planio 8209
tariff.error.percent_charge=Specify a value.
tariff.error.unit_charge=Specify a value.

# patrickm: 2020-04-03 | Planio 8675
register.reading.txn.create_date=Date Created

#joelc: 2020-02-19 | Planio 8592
meter.models.field.data.decoder=Meter Data Decoder
meter.models.field.data.decoder.help=Some meter models require specific decoders to process their readings.  

# patrickm: 2020-01-14 | Planio 7768
blockingtype.form.dailyamount=Amount per day
blockingtype.form.dailyamount.help=Maximum amount allowed per day
blockingtype.msg.error.dailyamount=You must include the maximum amount per day template, {max_amount_per_day}, in your message.

# jacciedt: 2019-01-20 | Planio 7356
usagepoint.hist.device.move.ref=Device Movement Reference
usagepoint.device.move.ref.lbl=Device Movement Reference Number
error.field.devicemoveref.max=Device Movement Reference must be less than {max} characters.

# jacciedt: 2019-12-31 | Planio 7950
configure.user.interface=Configure User Interface
configure.user.interface.field.name=Field Name
configure.user.interface.display=Display?
configure.user.interface.validation.regex=Validation Regex
configure.user.interface.regex.failed.message=Regex Failed Message / Enumerated Values
changes.saved=Changes have been saved.
configure.user.interface.invalid.regex=One or more fields have invalid regex patterns in them.
error.required.field={0} is a required field - the record cannot be saved without it.
error.regex.invalid=The input value does not match the required pattern of: {0}
configure.user.interface.enumerated.values.label=Type in the list of possible values separated by commas:
enumerated.field=enumerated field

# jacciedt: 2019-11-18 | Planio 7264
meter.select.meter.phase=Meter Phase
meter.select.meter.phase.help=Select the correct meter phase for the meter model.

# jacciedt: 2019-12-04 | Planio 7772
meter.txn.reversal.reason=Reversal Reason
meter.txn.reversed.by=Reversed By
vend.reversal.exceeds.time.limit=Reversal unsuccessful. It exceeds the reversal time limit.

# joelc: 2019-11-25 | Planio 7487
sts.tokens.header = STS Tokens
verify.token = Verify Token
verification.error.timeout = Error verifying tokens. No response received from service.
verification.error.general=Unable to verify token
verify.token.class=Token Class
verify.token.subclass=Token Sub-Class
verify.token.id=Token ID
verify.token.units=Units
verify.token.date=Token Date

# jacciedt: 2019-11-21 | Planio 7362
energybalancing.error.duplicate.selected.meters=The selected meter is already added to the list.

# zachv: 2019-11-21 | Planio 7916
tariff.field.free.units.title=Monthly Free Units
tariff.field.percent_charge.title=Percentage Charge
tariff.field.percent_charge.add=Add Percent Charge
tariff.field.cyclic_charge.title=Cyclic Charge
tariff.field.cyclic_charge.add=Add Cyclic Charge
tariff.field.non_accruing_monthly.name=Non accruing monthly
tariff.field.non_accruing_monthly.name.help=Only applies to MONTHLY cycle. Non accruing monthly charges only charge for the month a transaction is done, any previous months where no monthly charges were levied (eg. due to no purchases or only free tokens) will not be charged for retrospectively.
tariff.field.unit_charge.title=Unit Charge
tariff.field.unit_charge.add=Add Unit Charge
tariff.field.unit_charge.name=Charge Name
tariff.field.unit_charge.name.help=The name for this charge that will end up on the customer's receipt.
tariff.field.unit_charge.is_percent=Percentage
tariff.field.unit_charge.is_percent.help=Whether or not this is charged as a percentage of the unit price or a currency charge per unit.
tariff.field.unit_charge.is_taxable=Taxable
tariff.field.unit_charge.is_taxable.help=Whether tax should be applied to this charge.
tariff.field.unit_charge=Unit Charge
tariff.field.unit_charge.help=The currency value to charge per unit purchased. This is an additional charge over and above the base unit price.
tariff.field.unit_charge_percent=Percentage of Unit Price
tariff.field.unit_charge_percent.help=This charge will be applied as a percentage of the unit price. If there are blocks it will use the block price.
tariff.error.unit_charge.name=Specify a name for the charge.
tariff.error.unit_charge.positive_not_zero=Must be a positive value and not zero.

# jacciedt: 2019-11-12 | Planio 7814
customer.auxaccount.increase.debt=Increase DEBT by:
customer.auxaccount.decrease.debt=Decrease DEBT by:
customer.auxaccount.increase.refund=Increase REFUND by:
customer.auxaccount.decrease.refund=Decrease REFUND by:
customer.auxaccount.error.amount.negative=Amount can not be negative
customer.auxaccount.error.tax.negative=Tax can not be negative
customer.auxaccount.error.adjustment=Either Increase or Decrease needs to be selected
customer.auxaccount.error.balance.refund=Balance cannot be negative or zero

# patrickm: 2019-11-02 | Planio #7801
customer.search.listbox.label=Customer Search by
customer.search.listbox.item_agr_ref=Agreement Ref
customer.search.listbox.item_id_num=ID Number
customer.search.listbox.item_surname=Surname

# jacciedt: 2019-10-24 | Planio 7812
customer.auxaccount.functions.as=Functions As

# thomasn: 2019-10-17 | planio-5133
link.blockingtype=Blocking Types
blockingtype.title=Blocking Type
blockingtype.name=Name
blockingtype.panel.error.missingvalues=One of the values ({0}, {1}, {2}, {3})\nmust be defined OR it must be a complete block.
blockingtype.title.add=Add Blocking Type
blockingtype.title.update=Update Blocking Type
blockingtype.form.typename=Name
blockingtype.form.typename.help=Name for the Blocking Type
blockingtype.form.units=Units per day
blockingtype.form.units.help=Maximum units allowed per day
blockingtype.form.complete=Complete
blockingtype.form.complete.help=Complete block means no vending will be allowed.
blockingtype.form.vends=Number of Vends
blockingtype.form.vends.help=Maximum vends allowed before Complete block
blockingtype.form.amount=Maximum Amount
blockingtype.form.amount.help=Maximum amount allowed before complete block
blockingtype.form.message=Message
blockingtype.form.message.help=Message with block details displayed to user. Use {remaining_vends} to display remaining vends before complete block. Use {remaining_amount} to display remaining amount before complete block. Use {max_units_per_day} to display max allowed units per day. Use {max_amount_per_day} to display max amount allowed per day. Use {reason_fixed} to display reason for block. Note, curly brackets denote system variables.
blockingtype.error.save=Unable to save the blocking type.
blockingtype.error.save.duplicate=Unable to save the blocking type, another blocking type with the same name already exists.
blockingtype.error.update=Unable to update the blocking type.
blockingtype.error.update.duplicate=Unable to update the blocking type, another blocking type with the same name already exists.
blockingtype.msg.error.variables=Curly brackets only allowed for valid system variables, {max_units_per_day}, {max_amount_per_day}, {remaining_amount}, {remaining_vends} or {reason_fixed}.
blockingtype.msg.error.units=You must include the units template, {max_units_per_day}, in your message.
blockingtype.msg.error.units.undefined=Units template {max_units_per_day} not allowed, unless {0} has been defined.
blockingtype.msg.error.vends=You must include the number of vends template, {remaining_vends}, in your message.
blockingtype.msg.error.vends.undefined=Vends template {remaining_vends} not allowed, unless {0} has been defined.
blockingtype.msg.error.amount=You must include the amount template, {remaining_amount}, in your message.
blockingtype.msg.error.amount.undefined=Amount template {remaining_amount} not allowed, unless {0} has been defined.
blockingtypes.header=Blocking Types
blockingtypes.title=Current Blocking Types

# jacciedt: 2019-10-10 | Planio 7514
meter.mrid.external.unique.validation=The Unique ID of this meter is already in use

# jacciedt: 2019-09-17 | Planio 5823
demo.addmeterreadings.earliest.reading=Earliest Reading
demo.addmeterreadings.latest.reading=Latest Reading
demo.addmeterreadings.zero.checkbox.text=Add zero readings
demo.addmeterreadings.zero.form.title=Zero Readings
demo.addmeterreadings.consecutive=Consecutive
demo.addmeterreadings.random=Random
demo.addmeterreadings.percentage.instances=Percentage of instances
demo.addmeterreadings.missing.checkbox.text=Add missing readings
demo.addmeterreadings.missing.form.title=Missing Readings
demo.addmeterreadings.algorithm.logic=Algorithm logic
demo.addmeterreadings.delete=Delete existing Interval Readings
demo.addmeterreadings.delete.all=All
demo.addmeterreadings.delete.selected=Selected date range
demo.addmeterreadings.append=Append
demo.addmeterreadings.link=[DEMO] Add Meter Readings
demo.addmeterreadings.header=Add Meter Readings
demo.addmeterreadings.title=Add Meter Readings
demo.addmeterreadings.title.criteria.register=Register Readings Criteria
demo.addmeterreadings.reading.variants=Choose Reading Variant
demo.addmeterreadings.delete.register=Delete existing Register Readings
demo.addmeterreadings.error.misc.start=This start date needs to be after the main start date
demo.addmeterreadings.error.misc.end=This end date needs to be before the main end date
demo.addmeterreadings.error.instances.required=Instances are required
demo.addmeterreadings.error.instances.format=Instances need to be numeric
demo.addmeterreadings.error.instances.range=Instances need to be a whole number between 0 and 100
demo.addmeterreadings.error.mdc.channel=No MDC Channel selected

# renciac: 2019-09-19 | Planio 7656
import.generic.start.label=Start of Data
error.field.enckey.max=Meter encryption key may not exceed maximum 255 characters.
error.field.powerlimitlabel.max=Power Limit LABEL may not exceed maximum 255 characters.
button.import.extract.fail=Extract Failed
import.upload.num.failed.upload.label=Num Failed upload
import.items.unsuccessful.uploads.reminder=\nREMINDER: There were {0} items not successfully uploaded in this file. They are ignored for further processing and must be manually recreated in a new file and uploaded afresh.
import.upload.completed=File Upload completed. 
bulk.upload.file.error=Error while importing the file
error.field.customvarchar1.max=UP customvarchar1 can be max 255 characters.
error.field.customvarchar2.max=UP customvarchar2 can be max 255 characters.
error.field.phonecontact1.max=Phone Contact1 must be less than {max} characters.
error.field.phonecontact2.max=Phone Contact2 must be less than {max} characters.
error.field.notificationemail.max=Customer Account Notification Email Address must be less than {max} characters.
error.field.notificationphone.max=Customer Account Notification Phone must be less than {max} characters.
import.file.item.view.still.busy=The import on this file is running. No further action can be taken until that is completed or stopped.
import.file.item.view.still.busy.stopped=Stop import instruction issued. Import will cease after current batch.
import.file.view.upload.still.busy=The upload on this file is in progress. 
button.stop.import.all=Stop Import
import.file.stopped=The import on this file was stopped.
import.file.stopped.instruction=Stop Import instruction has been issued. The import will stop after the current batch.

# renciac: 2019-09-16 | Planio 6715
bulk.upload.idNumber=Id Number

# thomasn: 2019-10-09 | Planio 6286
usagepoint.unblocking.enter.reason=Enter a reason for unblocking
usagepoint.unblocking.select.reason=Select a reason for unblocking

# thomasn: 2019-09-09 | planio-6287 Add reason when re-activating a UP
usagepoint.hist.status.reason=Status Reason

# jacciedt: 2019-09-10 | Planio 7490
meter.clearreverseflag=Clear Reverse Flag
meter.disabletriplimit=Disable Trip Limit
meter.setcurrentlimit=Set Current Limit
meter.issue.token.description.help=Enter a description for this {0}.

# robertf: 2019-09-10 | Planio 7571
customer.txn.reason=Action Reason
customer.auxaccount.history.title=Auxiliary Account History
customer.auxaccount.history.filter.title=Auxiliary Account History for : {0}
customer.auxaccount.history.filter.discription=Previous changes made to auxiliary account : {0}
customer.auxaccount.history.table.header.datemodified=Date Modified
customer.auxaccount.history.table.header.user=User
customer.auxaccount.history.table.header.action=Action
customer.auxaccount.history.table.header.type=Type
customer.auxaccount.history.table.header.accountname=Account Name
customer.auxaccount.history.table.header.balance=Balance
customer.auxaccount.history.table.header.priority=Priority
customer.auxaccount.history.table.header.chargeschedule=Charge Schedule
customer.auxaccount.history.table.header.freeissue=Free Issue
customer.auxaccount.history.table.header.status=Status
customer.auxaccount.history.table.header.updatereason=Update Reason
customer.auxaccount.history.table.header.createreason=Create Reason
customer.title.auxaccounts.history.selector.description=Select Auxiliary Account to view history.

# jacciedt: 2019-08-14 | Planio 7341
import.account.number=Account Number
import.arrears.balance=Arrears Balance
import.debtor.balance=Debtor Balance
import.edit.account.number.update.success=Data for Account Number {0} successfully updated.
import.edit.generic.update.success=Data successfully updated.

# jacciedt: 2019-07-18 | Planio 6240
meter.select.store.add=Meter Store

# jacciedt: 2019-08-15 | Planio 7310
transaction.history.graph.yaxis.label2=Number of Units

# jacciedt: 2019-08-22 | Planio 6738
tou.thin.error.tax.positive=Tax must be a positive value.
register.reading.tax.positive=Tax must be a positive value.

# jacciedt: 2019-07-29 | Planio 7197
error.field.validity.email=One of the email addresses are invalid.
reprint.warning.line.1=WARNING!!! THIS IS A REPRINT
reprint.warning.line.2=of a token issued on {0}
reprint.warning.line.3=TAX INVOICE (COPY)
reprint.warning.line.4=Reprinted on: {0}
reprint.warning.line.5=Reprinted by: {0}
reprint.credit.vend.tax.invoice=Credit Vend - Tax Invoice
reprint.util.name=Util. Name
reprint.util.dist.id=Util. Dist. ID
reprint.util.vat.no=Util. VAT No.
reprint.util.address=Util. Address
reprint.issued=Issued
reprint.token.tech=Token Tech.
reprint.alg=Alg.
reprint.sgc=SGC
reprint.krn=KRN
reprint.your.resource.token=Your {0} Token
reprint.standard.token=Standard Token
reprint.receipt.nr=Receipt #
reprint.free.basic.resource=Free Basic {0}
reprint.debt.items=Debt Items
reprint.fixed.items=Fixed Items
reprint.total.vat.excl=Total (VAT Excl.)
reprint.total.vat.incl=Total (VAT Incl.)
reprint.print=Print
reprint.deposit=Deposit
reprint.save.to.pdf=Save to PDF
reprint.electricity=Electricity
reprint.water=Water
reprint.gas=Gas

# jacciedt: 2019-08-20 | Planio 7364
error.supplygroup.server=Duplicate Supply Group Code and Key Revision Number. Specify unique values.

# jacciedt: 2019-08-15 | Planio 7449
location.field.address.line.2=Line 2
location.field.address.line.3=Line 3

# jacciedt: 2019-08-01 | Planio 7368
location.field.address.line.1=Line 1
customer.phone.1=Phone Number 1
customer.phone.2=Phone Number 2

# jacciedt: 2019-06-19 | Planio 7024
timezone.warning=Note: All tabs must be closed before the time zone can be changed.

# jacciedt: 2019-07-15 | Planio 7244
error.field.specialactionsname.range=Action Name must be between {min} and {max} characters.

# jacciedt: 2019-07-16 | Planio 7148
unit.kiloliter.symbol=kl
unit.cubicmeter.symbol=m\u00B3
meter.units=Units ({0})

# robertf: 2019-07-08 | Planio 6247
transaction.history.column.header.stdunits=Standard Units
transaction.history.column.header.fbeunits=Free Basic Units
transaction.history.column.header.stdtoken=Standard Token Total
transaction.history.column.header.fixedamt=Fixed Costs Total
transaction.history.column.header.auxamt=Auxiliary Payment Total

# thomasn: 2019-07-01 | Planio-6288
usagepoint.blocking.info.message=This usage point was blocked by {0} on the {1} 
usagepoint.blocking.info.message.reason= for the following reason: <br/> {0}
usagepoint.hist.blocking.name=Blocking Type
usagepoint.hist.blocking.reason=Blocking Reason

# renciac: 2019-06-26 | Planio 6291
bulk.upload.powerlimit.key=Power Limit
meter.powerlimit.key.error.integer=Power Limit Value must be an integer
meter.powerlimit.key.error.not.configured=There are no power limit settings configured in the app settings.
meter.powerlimit.key.error.invalid=This power limit is not configured in the app settings.

error.field.usagepointgroups.required.group=Required Usage Point group is missing: {0} 
error.field.usagepointgroups.missing.group=Hierarchy not complete : missing usage point group field for Usage Point Type: {0}
error.field.usagepointgroups.invalid.group=Invalid usage point group name {0} for Usage Point Type: {1}
error.field.usagepointgroups.incomplete.group=Incomplete hierarchy level in Usage Point Group: {0} 

bulk.upload.invalid.locationgroups.not.configured=Location groups are not configured for this system
usage.point.location.group.generate.label=Usage Point Location Group
error.field.uplocationgroups.required=Required Usage Point Location group is missing
error.field.uplocationgroups.invalid.group=Invalid location group name {0} for Usage Point Location Group
error.field.uplocationgroups.incomplete.group=Incomplete hierarchy level in Usage Point Location Group
error.field.uplocationgroups.first.level.required=Usage Point Location Group is required. At least the first level of hierarchy should be completed.

customer.location.group.generate.label=Customer Location Group
error.field.custlocationgroups.required=Required Customer Location group is missing. 
error.field.custlocationgroups.invalid.group=Invalid location group name {0} for Customer Location Group
error.field.custlocationgroups.incomplete.group=Incomplete hierarchy level in Customer Location Group
error.field.custlocationgroups.first.level.required=Customer Location Group is required. At least the first level of hierarchy should be completed.

location.field.address.suburb.name=Suburb Name
# jacciedt: 2019-06-21 | Planio 6359
billingdet.error.save.duplicate=Unable to save the Billing Determinant, another Billing Determinant with the same name already exists.

# renciac: 2019-05-29 | Planio 6237
meter.freeissue.currency=Emergency Vend

# renciac: 2019-04-25 | Planio 6235
customer.id.partial.search=No exactly matching customer Id. Doing advanced search...
error.customer.load=Unable to display the Customer.
search.get.total.label=Count Total results of the selected criteria
search.count.label=Counting total results for selected criteria ...

# renciac: 2019-04-17 | Planio 6234
customer.id.error.noentry=Enter Customer Id number or % for all
metersearch.error.nometer=Geben Sie eine ZÃ¤hlernummer an  or % for all
usagepoint.error.none=Enter a usage point name or % for all
customer.error.noentry=Geben Sie den Nachnamen des Kunden an or % for all
customer.agreement.error.noentry=Geben Sie eine Vertragsnummer an or % for all
customer.account.error.noentry=Geben Sie eine Kontonummer an or % for all

# renciac: 2019-03-25 | Planio 5961
import.upload.header=File Upload / Import
import.upload.file.upload.title=File Upload
import.filetype.select.file.help=Select the file type of the file to be uploaded & imported
import.filetype.select.labeltext=File Type
import.upload.filetype.none=Please select the file type of the file to be uploaded 
import.upload.file.none=No file was selected to be uploaded
import.upload.filename.txt=Selected filename={0}
import.upload.file.select.labeltext=Select File to upload
import.upload.select.file.help=Select a movements file containing the information for uploading into the system
import.upload.csv.button=Upload File
import.upload.workspace.heading=File Upload and Import Data
link.file.import=Upload and Import Files
import.upload.filetype.error=Invalid File Type. Contact Support.
import.upload.file.error=Error while uploading the file. Contact Support.
import.selected.items.non=No items have been selected for import.
import.upload.uploaded.files.title=Uploaded Files for Data Import
import.upload.file.name.label=File Name
import.upload.num.items.label=Num Items
import.upload.startdate.label=Upload Start
import.upload.enddate.label=Upload End
import.upload.last.imported.by.label=Last Import By
import.upload.detail=Detail
import.upload.open.label=Open

import.file.items.header=File Items
button.import.selected=Import Selected
button.import.all=Import All
import.items.title=Items for Import
import.select.label=Select
import.upload.successful.label=Upload Success
import.upload.date.label=Import Date
import.num.attempts.label=Num imports
import.last.successful.label=Import Success
import.meter.label=Meter
import.up.label=Installation
import.agrref.label=Contract
import.comment.label=Comment
import.itemdata.label=Data
import.upload.username.label=Upload User
import.last.start.label=Last Import Start
import.last.end.label=Last Import End
import.items.file.detail.header=File Detail
import.items.edit.header=View / Edit File Item
import.cancel.edit.item.confirm=Cancelling will abandon changes made above. Continue?
import.edit.item.update.success=Data for Customer {0}, meter {1} successfully updated.
import.edit.item.update.non=Data has not changed. No update necessary.

# thomasn: 2019-02-18 | Planio 6223
customer.idnumber.help=Enter the customer's ID number.
customer.idnumber=ID Number
error.field.idnumber.max=ID Number must be less than {max} characters.
customer.idnumber.column=ID Number
search.customer.idnumber=ID Number

#  renciac: 2019-01-28 | Planio 6425
usagepoint.deactivate.info.message=This usage point was deactivated by {0} on the {1} 
usagepoint.deactivate.info.message.reason= for the following reason: <br/> {0}

# rfowler: 2019-02-08 | Planio 6141
search.customer.phone1.number=Phone 1
search.customer.phone2.number=Phone 2
search.customer.phone.number=Phone Number
search.customer.custom.textfield1=Custom Field 1

search.location.header=Location Search
search.location.erf.number=Erf Number
search.location.building.name=Building Name
search.location.suite.number=Suite Number
search.location.address1=Address Line 1
search.location.address2=Address Line 2
search.location.address3=Address Line 3
search.location.type=Location Search Type
search.location.type.label=Customer/Usage Point Location
search.location.type.customer=Customer Location
search.location.type.usagepoint=Usage Point Location

# joelc: 2018-01-10 | planio-6324:Samoa - Reason for reactivation of usage point
error.field.reasonname.range=Reason name is required.
error.field.reasontext.range=Reason text is required.
specialaction.auto.deactivate.usagepoint=Deactivated as usage point is no longer complete (missing customer or meter) 
specialaction.auto.activate.usagepoint=Activated after completing required usage point data.

#  zachv: 2019-01-02 | Planio 5936
# Removed: ndp.schedule.abandon.activation.change=Schedule activation has changed. if you want to keep the setting, choose No and save / update first. Abandon setting?
question.close.tabs.dirty=All tabs will be closed, but some have unsaved changes. Do you want to discard those changes? 

# joelc: 2018-12-12 | planio-6324:Samoa - Reason for reactivation of usage point
usagepoint.activate.enter.reason=Enter a reason for this activation
usagepoint.activate.select.reason=Select a reason for this activation
special.action.reason.error.save.duplicate=This reason has already been added.

# renciac: 2018-12-06 | planio-6282
error.field.value.boolean=Value must be true or false
error.field.value.location.level=Location Group Type is NOT required. This setting cannot be set to true.

# thomasn: 2018-11-29 | planio-5296
auxaccount.upload.invalid.duplicate=Duplicate AuxAccount the (accountName & agreementRef) combination already exist!.

#RobertF 2018-11-21 Planio-6142 : [MMA] CoCT - Transaction History Bar Graph
transaction.history.graph.title=Transaction History
transaction.history.graph.description=Transactions per month for last 12 months.
transaction.history.graph.xaxis.label= Month
transaction.history.graph.yaxis.label= Number of Transactions
transaction.history.graph.series.label= Transactions count

# joelc 20 November 2018, Planio 4328
question.custom.field.used= Note that any group entity records using this field will NOT be updated, \
  only the list of available options will be updated for future use. 
question.custom.field.used.option.yes=Update List
question.custom.field.used.option.no=Cancel

# thomasn: 2018-11-12 | planio-6168
customer.title.mr=Mr
customer.title.mrs=Mrs
customer.title.ms=Ms
customer.title.miss=Miss
customer.title.doc=Dr
customer.title.prof=Prof
customer.title.sir=Sir

customer.email=Email Address

# joelc: 2018-10-29 | planio-6105: usage point meter download and print for Mayotte
print.customer.contract=Download Customer Contract
print.customer.contract.auxtype=Aux Type
print.customer.contract.auxname=Account Name
print.customer.contract.principleamount=Principle Amount
print.customer.contract.balance=Balance
print.customer.contract.status=Status
print.customer.contract.signature=Customer Signature
print.customer.contract.signature.date=Date

# thomasn: 2018-10-17 | Planio 5296
auxaccount.upload.balanceType=Balance Type
auxaccount.upload.invalid.balance.amount=Balance Amount must be positive
auxaccount.upload.invalid.balancetype=Invalid Balance Type must be debt/refund.

# Renciac: 2018-09-04 | planio-5466 : Add manual reversal
button.vend.reversal=Reverse Vend
vend.reversal.confirm=Confirm Vend / Topup reversal?
vend.reversal.connection.error=No response received from service. Please refresh the page.
vend.reversal.error=Vend / Topup error: {0}
vend.reversal.fail=Only the last Vend can be reversed.
vend.reversal.success=Successful Vend Reversal. Original Ref= {0}, Reversal Ref={1}
vend.trans.already.reprinted=This transaction was been reprinted {0} time/s. First reprinted on {1} by {2}. \n
vend.reprint.null.token=Token is null. Reprint not possible. 
vend.reversed=Vend has been reversed.
meter.txn.reprint.date=Last Reprint
meter.noshow.token=Reprint for token 
vend.reprint.user.not.known=Not Known

# robertf: 2018-10-09 | Planio 5955
meter.txn.user.ref=User Ref
engineering.token.user.reference.txtbx.label=User Reference
engineering.token.user.reference.txtbx.label.help=Enter user reference for engineering token issue.

# Patrickm: 2018-11-09 | planio-6192: GIS data upload for BVM
link.metadata.upload=Upload Metadata
metadata.upload.heading=Metadata Upload
metadata.upload.data.title=Import Metadata
metadata.upload.description=Select the JSON file containing the metadata to import into the Meter Management system.
metadata.upload.error.object.creation=Error creating {0} object. Contact Support!
metadata.upload.select.file.help=Select a file containing the metadata in the specified json format for importing into the system
metadata.upload.button=Upload metadata
metadata.lat.label=Latitude
metadata.lon.label=Longitude
metadata.lon.help=Longitude metadata for this UP Group
metadata.lat.help=Latitude metadata for this UP Group
metadata.gis.saved=Successfully saved GIS information for group: {0}
metadata.gis.error.invalid.lat=Invalid latitude value. Coordinate out of range of valid GIS coordinates.
metadata.gis.error.invalid.lon=Invalid longitude value. Coordinate out of range of valid GIS coordinates.

# Patrick: 2017-12-14 | planio-5041 : Add configurable switch to device stores to determine the response of meters in the store to polling requests
devicestore.field.store_vendors_meter=Device store holds meters that have moved to another vendor
devicestore.field.store_vendors_meter_help=Whether the device store holds meters that have moved to another vendor.
devicestore.field.store_vendors_meter_help2=The fields '{0}' and '{1}'  are mutually exclusive. Therefore, you cannot have a custom message and check the box
devicestore.field.custom_message=Device Store Custom Response Message
devicestore.field.custom_message_help=The message to be shown to users when users query for meters stored in this device store.
devicestore.meters.save.dialog=This meter will be saved in '{0}' store. Please confirm this operation.
devicestore.meters.move.dialog=This meter will be moved to '{0}' store. Would you like to perform this operation?
devicestore.meters.fetch.dialog=You are fetching a meter from another vendor. Would you like to continue with this operation?

# thomasn: 2018-10-23 | planio-5956 [MMA] Add an alphanumeric SAP reference number when removing or replacing a meter on a usage point and display it in the history tables
usagepoint.hist.reason=Meter Remove/Reassign Reason

# robertf: 2018-10-01 | Planio 5954
meter.txn.powerlimits=Power Limit
meter.powerlimit.units.w=Power Limit (W)

# thomasn: 2018-09-04 | Planio 5476
readings.table.receiptnum=Receipt Number

# thomasn: 2018-09-03 | Planio 5296
customer.auxaccount.amount.pos=A positive amount indicates a REFUND
customer.auxaccount.amount.neg=A negative amount indicates a DEBT
customer.auxaccount.title=Auxiliary Account
customer.auxaccount.balance.type.debt=Debt
customer.auxaccount.balance.type.refund=Refund
customer.auxaccount.balance.type.error.required=You must select one.
customer.auxaccount.error.balance=Balance cannot be negative

# Thomas: 2018-08-29 | planio-5475 Time of use calendar allows 00:00 as end time for day profile but marks as incomplete
calendar.assign.period.end.maximum=End of day value is 23:59
calendar.assign.period.end.help=The time that the period ends. End of day value is 23:59

# Renciac: 2018-07-26 | planio-5451 Enabling STS 6 
meter.token.code3=Token code 3
meter.token.code4=Token code 4
base.date.label=Base Date: 
base.date.label.help=Base date used for generation of STS6 tokens for this meter.
meter.three.tokens=Three Key Change tokens required
meter.three.tokens.help=For STS algorithm code = 07, some meters can store the STS info ON the meter and need a third keyChange token to supply the information.
meter.three.tokens.error=Three Tokens setting is only applicable to STS Algorithm Code = 07 
meter.clear.tid=TID reset 

# zachv: 2018-08-22
tariff.field.samoa.debt_charge=Debt Charge
tariff.field.samoa.debt_charge.help=Charged per unit
tariff.field.samoa.energy_charge=Energy Charge
tariff.field.samoa.energy_charge.help=Charged per unit

# robertf: 2018-07-27: #5347 Community Group used feature indicator
usagepointgroups.indicator.thresholds.tooltip = Group has custom customer account thresholds
usagepointgroups.indicator.ndp.tooltip = Group has custom NDP schedule

# zachv: 2018-06-26
tariff.field.kenya.monthly = Fixed Monthly Charge
tariff.field.kenya.monthly.help = Fixed charge applied per month.
tariff.field.kenya.fuel = Fuel Cost Charge
tariff.field.kenya.fuel.help = Variable rate per kWh, published monthly by KPLC. VAT is applied to this charge.
tariff.field.kenya.forex = Forex Charge
tariff.field.kenya.forex.help = Foreign exchange rate fluctuation adjustment (FERFA). Variable rate per kWh, published monthly by KPLC. 
tariff.field.kenya.inflation = Inflation Adjustment
tariff.field.kenya.inflation.help = Variable rate per kWh, published monthly by KPLC.
tariff.field.kenya.erc = ERC Levy
tariff.field.kenya.erc.help = Rate per kWh.
tariff.field.kenya.rep = REP Levy
tariff.field.kenya.rep.help = Percentage of base rate
tariff.field.kenya.warma = WARMA Levy
tariff.field.kenya.warma.help = Variable rate per kWh, published monthly by KPLC.

# RobertF 4th July 2018. Planio 5714
bulk.upload.enc.key= Encryption Key
meter.enc.key.error=The Meter Model requires a meter encryption key.

#RobertF June 15, 2017 Planio-5787 : Panel with the buying index chart
dashboard.buying.index.graph.title=Buying Index
dashboard.buying.index.graph.month.description=Buying Index: Transacting Meters / Active Meters (%)
dashboard.buying.index.graph.xaxis.label=Month
dashboard.buying.index.graph.yaxis.label=Buying Index

# zachv: 2018-05-04
meter.models.field.needs.encryption.key = Needs Encryption Key
meter.models.field.needs.encryption.key.help = Whether a meter encryption key needs to be entered for this meter model.
meter.encryptionkey=Encryption Key
meter.encryptionkey.help=The encryption key for this meter, for example to decrypt data received from a meter data collector
meter.encryptionkey.error=The Meter Model requires a meter encryption key.
meter.metermodelchange.remove_fields.question=Changing this meter model will cause the following fields to be cleared: {0} . Continue? 
meter.model.unset.encryption.key.error=Cannot change the Encryption Key requirement - there are already meters with encryption keys using this meter model.

# Thomas: 2018-05-10 | planio-5502 : MDC messages Override Button needs info message
mdc.txn.override.help=An override message will be sent as a priority message that will take precedence over other messages that may be pending against the meter and certain validation checks will not be applied such as validation of non-disconnect periods and message ordering.

# Thomas: 2018-04-16 | planio-5495 : OpenWayTransaltor, send text message to meter display
meter.models.field.message.display=Supports display messages
meter.models.field.message.display.help=This indicates whether this meter model supports displaying messages on the meter. E.g. Low balance message 

# zachv: 2018-04-09 | planio-5512 : MeterMng to support reading multiplier for mdc channel
channel.field.reading_multiplier=Reading Multiplier
channel.field.reading_multiplier.help=Multiplier applied to reading to normalize it into the correct data type. For example a water meter may give readings in pulses needing a multiplier such as 0.5 pulses per liter. Not supported by all mdc components.

# Patrickm: 2018-03-22 | planio-5438 : Display meter location on Map
meter.location=Meter Location
usagepoint.location=Usage Point Location

# Renciac: 2018-02-28 | Planio 5380 : Non_billable meters
customer.agreement.billable.help=Set to false  (unchecked) for Customer Agreements that are for consumption smart meters purely for measuring usage, not for purchasing. Readings for these meters are uploaded and used for Usage Graphs on, for eg. the EnergyInsight website.  
customer.agreement.billable=Billable
customer.agreement.billable.setting.check=You have set the customer agreement billable value to {0}. Please confirm the value. If set to true, this account can be topped up, if set to false it is purely for usage consumption purposes.
bulk.upload.billable=Billable
error.field.upload.billable.invalid=Billable can only be true or false.  

# Renciac: 2018-01-24 | planio-5210 : PayTypeDiscounts & Add vat inclusive / exclusive to help messages
tariff.field.percent_charge.help=Optional percentage charge that would be taken off from the tendered amount as the first step in the tariff calculation. (Includes Tax).
tariff.field.discount.help=Discount to be applied per payment type. (Includes Tax). Leave blank if no discount applicable for a payment type.
tariff.field.discount.blockThin.help=Discount to be applied per payment type. (Excludes Tax). Leave blank if no discount applicable for a payment type.
tariff.field.unitprice.help=Preis pro kWh (Excludes Tax).
tariff.field.block.help=Specify up to eight block's unit price (excluding Tax) and threshold below. Leave the unit price and threshold blank for unused blocks.
tariff.field.unitprice.namibia.help=Price per kWH (excludes Levies & excludes Tax)
tou.thin.field.monthlydemand.help=Geben Sie den monatliche Leistungspreis an. (Excludes Tax).
tou.thin.field.servicecharge.help=Geben Sie den Wartungskostenbetrag an. (Excludes Tax).
tou.thin.field.charges.help=Erfassen Sie einen GebÃ¼hrensatz fÃ¼r jede verfÃ¼gbare Saison, Zeitraum und Ableseart-Kombinationen. (Excludes Tax).
tou.thin.field.charges.specialday.help=Erfassen Sie alle relevanten GebÃ¼hrensÃ¤tze fÃ¼r jede der unteren Feiertage. (Excludes Tax).
register.reading.rates.help=Capture a tariff rate for each of the selected billing determinants. (Excludes Tax).

# rfowler : 2018-01-23 : Planio 4756
bulk.upload.file.no.meterupdata=No meter/usage point data present in csv upload file.

# Patrick: 2017-11-27 | planio-5127 : Capture and display power limit of a meter
meter.power_limit.instructions=For Power Limit Tokens:\n1. Open the Meter Panel.\n2. In the Power Limit information block, Select the power limit from the Power Limit suggestion box.\n3. This will add a new listbox with Power Limit options - select what you require.\n4. Upon saving the meter, a popup box will be shown to input further details.
meter.power_limit.container_label=Power Limit Information
meter.power_limit.token.generate=Generate Power limit Tokens?
meter.power_limit.token.generate.help=If the meter needs to be updated to match the new Power limit details then a power limit token needs to be generated. If the record is being updated to match the meter's details then there is no need to generate tokens.
tokens.power_limit.no_gen=Do not generate power limit tokens
tokens.power_limit.gen=Generate power limit tokens

# joelc 11 January 2018, Planio 4630
grouptree.search=Search

# joelc 3 January 2018, Planio 4627
error.group.contains.usagepoint=Group currently contains usage points and cannot be deleted. 

# joelc 13 December 2017, Planio 4631
group.error.name.nonunique=Group name must be unique.

# 2017-11-20 | planio-5134 : Supplier Group Code validation issue
error.field.supplygroupcode.size=Code must be equal to {0} digits.
# RobertF 23 October 2017, Planio 4755
bulk.upload.gencsvtemplate.title=Generate Upload Template
bulk.upload.gencsvtemplate.subtitle= Required fields have been selected and cannot be toggled.
bulk.upload.file.button.gentemplate=Generate Template
bulk.upload.template.required.first=Info: Required
bulk.upload.template.required=Required
bulk.upload.template.sts.required=Required for STS meters
bulk.upload.recordstatus=Active(blank/no)
bulk.upload.installationdate.format=Install Date (yyyy-MM-dd HH:mm:ss)
bulk.upload.file.button.gentemplate.description=Select the fields you require and generate your upload template:

# joelc 13 November 2017, Planio 4636
meter.use.existing.instructions=Copy groups from existing meter
meter.not.in.groups=Meter has not been assigned to any groups
meter.copy.selected.groups=Copy selected groups to main screen

# Patrick | August 28, 2017 | planio-4680: Duplicate and missing keys in MeterMngAdmin translation files
appsettings.list.duplicate.item=Duplicate item entered.
usagepoint.charge.view.dialog.invalid.date2=Date selected cannot be in future

# Rencia 29 August 2017, Planio 4928
meter.attached.to.up=Meter {0} is attached to Usage Point {1}

# Thomas 7th August 2017. Planio 4815
tariff.field.minvendamount.lbl=Min Vend Amount
tariff.field.minvendamount.help=Minimum vend amount MUST be provided if tariff allows for vend of less than one unit. This will bypass the check that a vend must be for at least ONE whole unit.

# Patrick | July 19, 2017 | planio-4648: When save new Aux acc, confirm status
auxaccount.checkbox.active.status=Your Auxiliary account is not active by default. Would you like to activate this account?

# Thomas 18th July 2017 Planio 4353 MDCTrans Override UI
mdc.txn.override.lbl=Override
mdc.txn.override.none=None
mdc.txn.override.all=All
mdc.txn.override=Override

#Thomas 17th July 2017 Date validation Planio-4644
error.field.datetime.invalid=Invalid date format. Expected format ({0})

#Rencia 24 May 2017 Improve Cell phone validation
customer.trans.upload.invalid.up.no.meter=Usage Point does not have a meter attached to it
customer.trans.upload.invalid.up.not.active=Usage Point is not active

#RobertF July 14, 2017 Planio-4421 : Meter Management: Panel to monitor vending activity
dashboard.vending.activity.graph.title=Vending activity
dashboard.vending.activity.graph.description=Total vends over last 15 minute interval
dashboard.vending.activity.graph.xaxis.label= Vends
dashboard.vending.activity.graph.yaxis.label= 15 minute intervals (click chart to reset zoom)

### July 13, 2017 : RobertF : Indicator Dashboard Panel ###
dashboard.key.indicator.indicator.total.sales=Total sales
dashboard.key.indicator.indicator.tooltip.total.sales=Total sales
dashboard.key.indicator.indicator.transactions=Transactions
dashboard.key.indicator.indicator.tooltip.transactions=Amount of transactions occuring to give total sales
dashboard.key.indicator.indicator.transacting.meters=Transacting meters
dashboard.key.indicator.indicator.tooltip.transacting.meters=Number of meters transacting to give total sales
dashboard.key.indicator.indicator.new.meters.installed=New meters installed
dashboard.key.indicator.indicator.tooltip.new.meters.installed=New meters that have been associated to usage points
dashboard.key.indicator.indicator.new.active.meters.installed=New ACTIVE meters installed
dashboard.key.indicator.indicator.tooltip.new.active.meters.installed=New meters that have been associated to usage points and are active
dashboard.key.indicator.indicator.total.meters.usage.points=Total meters (Usage points)
dashboard.key.indicator.indicator.tooltip.total.meters.usage.points=Total meters assigned to usage points
dashboard.key.indicator.indicator.total.active.meters.usage.points=Total ACTIVE meters (Usage points)
dashboard.key.indicator.indicator.tooltip.total.active.meters.usage.points=Total meters assigned to usage points and are active
dashboard.key.indicator.indicator.total.meters.device.store=Total meters (Device store)
dashboard.key.indicator.indicator.tooltip.total.meters.device.store=Total meters in device store

#RobertF June 22, 2017 Planio-4420 : Panel with the sales per resource chart
dashboard.sales.per.resource.graph.title=Sales per resource
dashboard.sales.per.resource.graph.day.description=Resource sales per day
dashboard.sales.per.resource.graph.month.description=Resource sales per month
dashboard.sales.per.resource.graph.xaxis.day.label=Date (click on chart for month view)
dashboard.sales.per.resource.graph.xaxis.month.label=Date (click on chart for day view)
dashboard.sales.per.resource.graph.yaxis.label=Total sales

# Thomas 21 June 2017 UP Blocking
usagepoint.field.blocking.help=Select the blocking type for the usage point.
usagepoint.field.blocking.label=Blocking Type
usagepoint.field.blocking.type.default=Not blocked
usagepoint.blocking.enter.reason=Enter a reason for blocking
usagepoint.blocking.select.reason=Select a reason for blocking

#RobertF June 2, 2017 Planio-4419 : Panel with the count regarding the Owner and Building usage point groups added over time
dashboard.groups.added.graph.title=Usage point groups added
dashboard.groups.added.graph.day.description=Groups added per day
dashboard.groups.added.graph.month.description=Groups added per month
dashboard.groups.added.graph.xaxis.day.label=Date (click on chart for month view)
dashboard.groups.added.graph.xaxis.month.label=Date (click on chart for day view)
dashboard.groups.added.graph.yaxis.label=Groups added

# Joel 30 May 2017  Centian Data display planio 4429
meter.centian.header = Centian Meter Information
meter.centian.kwh.credit.remaining=kWh Credit Remaining:
meter.centian.currency.credit.remaining=Currency Credit Remaining:
meter.centian.number.disconnections=Number of Disconnections:
meter.centian.tamper.detected=Tamper states detected:
meter.centian.tamper.none=No Tamper states have been detected
meter.centian.tamper.updated= Date Info retrieved: 
meter.centian.tamper.overpower=Over Power
meter.centian.tamper.overvoltage=Over Voltage
meter.centian.tamper.lowvoltage=Low Voltage
meter.centian.tamper.overfrequency=Over Frequency
meter.centian.tamper.lowfrequency=Low Frequency
meter.centian.tamper.reverseenergy=Reverse Energy
meter.centian.tamper.opencover=Open Cover
meter.centian.tamper.magnettamper=Magnet Tamper
meter.centian.tamper.bypassearth=Bypass/Earth Tamper 
meter.centian.tamper.sequenceerror=Sequence Error
meter.centian.tamper.overtemperature=Over Temperature
meter.centian.tamper.lowtemperature=Low Temperature
meter.centian.tamper.phaseunbalance=Phase Unbalance
meter.centian.tamper.phasevoltageloss=Phase Voltage Loss
meter.centian.tamper.tariffconfigerror=Tariff Configuration Error
meter.centian.tamper.metrologyfail=Metrology Fail

meter.centian.current.tamper.status.header=Tamper Status for Meter
meter.centian.current.tamper.status.description=Tamper states with a tick have been detected on this meter.
meter.centian.current.tamper.status.description.none=No Tamper states have been detected on this meter.
meter.centian.current.tamper.status.updated=Tamper status updated:

#Rencia 24 May 2017 Improve Cell phone validation
meter.online.bulk.customer.phone.help=Enter the customer's phone number to which a Free Issue Token will be sms-ed, if applicable. SMS numbers must be in International Format, starting with a +.
messaging.recipient.help=For SMS, must use international phone numbers.
cellPhone.pattern.description=The phone number field for smses must be in international format, i.e. start with a +. You may use whitespaces, parentheses (), hypens and periods - these are stripped out and the resulting phone number (excl. the +) must be minimum 4 digits, maximum 25 in length, depending on your locale. Refer the placeholder.

### May 17, 2017 : RobertF : Admin dashboard workspace ###
admin.dashboard.title=Dashboard (Admin)

# Patrick | May 15, 2017 | planio-4443 - Updates for help message from field behaviour change
meter.algorithmcode.help=Select the correct algorithm code. This field is required for activation - the meter cannot be saved unless an algorithm code is selected.
meter.tokentechcode.help=Select the correct token tech code. This field is required for activation - the meter cannot be saved unless a token tech code is selected.
meter.supplygroupcode.help=Enter the current supply group code. This field is required for activation - the record cannot be saved without it.
meter.tariffindex.help=Enter the current tariff index. This field is required for activation - the record cannot be saved without it.

#Thomas 8th May 2017 Tariff Date validation
tariff.error.startdate.unique=Duplicate start date. Specify a unique start date.
tariff.error.startdate=Start Date must be in the future and greater than the start date of currently active tariff.

### May 2, 2017 : RobertF : Indicator Dashboard Panel ###
dashboard.key.indicator.title=Key Indicators
dashboard.key.indicator.description=Key system indicators over various time frames.
dashboard.key.indicator.indicator=Indicator
dashboard.key.indicator.value.today=Today
dashboard.key.indicator.value.monthtodate=Month to date
dashboard.key.indicator.value.lastmonth=Last month

#Rencia 24 April 2017 Meter Bulk Online Search / Capture : Free Issue Token
meter.online.bulk.free.issue.title=Free Issue Token
meter.online.bulk.free.issue.generate=Generate Token
meter.online.bulk.free.issue.sms.token=Sms token to tenant
meter.online.bulk.free.issue.sms.token.help=Select here if you want the Free Issue token to be sms-ed to the tenant?
meter.online.bulk.free.issue.check.sms.not.selected=You have elected to generate a Free Issue Token but NOT to send it via SMS.
meter.online.bulk.free.issue.sms.invalid.phone=To SMS a token, must have a valid cellphone number 
meter.online.bulk.free.issue.invalid.units=Number of Free Issue units must be numeric and greater than zero 
meter.online.bulk.free.issue.sms=Free issue token {2}: Meter Number: {0}  Token: {1}
meter.online.bulk.free.issue.token.null=Meter has been added to the group but Unable to generate the Free Issue Token.
meter.online.bulk.free.issue.token.error=Meter has been added to the group but Token Error: {0}
credit.token.link=View Credit Tokens
eng.token.link=View Engineering Tokens

#Rencia 21 April 2017 Meter Bulk Online Search / Capture : Edit / Remove buttons in table
meter.online.bulk.no.edit=Only certain fields on ACTIVE usage points can be edited here. Use link to go to Usage Point page for others.
meter.online.bulk.no.remove=Usage Point has no meter to remove. Use link to go to Usage Point page.
button.clear.panel=Clear Panel
button.clear.groups=Clear Groups
online.bulk.panel.tariffindex.help=Enter the current tariff index. This field is required for activation. To edit an already existing meter, use the UsagePoint Page.
online.bulk.panel.supplygroupcode.help=Enter the current supply group code. This field is required for activation. To edit an already existing meter, use the UsagePoint Page.
error.field.breakerid.max=BreakerId may not exceed maximum 100 characters.
meter.online.bulk.meter.updated=Meter {0} updated 

### April 11, 2017 : Patrick : Send Reprint ###
button.send_reprint=Send reprint
messaging.type.sms=Sms
messaging.type.email=Email
messaging.recipient=Recipient
messaging.message.type=Message Type
messaging.message.label=Message
messaging.token.reprint.email.subject=Token Reprint Request
token.label=Token
error.field.required.recipient.email=Recipient's email address is required
error.field.required.recipient.phone=Recipient's phone number is required
error.field.validity.phone=Phone number is invalid.

notification.message.send.status.sms=SMS Message Sent successfully
notification.message.send.status.email=Email Message Sent successfully

messaging.txn.date=Transaction Date
messaging.txn.meter_no=Meter Number
messaging.txn.token=Token

#Rencia 31 March 2017 Meter Meter Bulk Online Search / Capture : Popup more information when click on meter in table
more.info=More Information - Drag Here
popup.label=Field
popup.value=Value

#Rencia 26 March 2017 Meter Meter Bulk Online Search / Capture : Add pricing Structure Tariff Popup
online.bulk.panel.tariff.title=Current Tariff
meter.online.bulk.add.group.title=Add / Edit Groups

#Robertf 27 March 2017 Usage Point page check for valid installation date
error.field.startdate.invalid=Start Date is not a valid date. Format = {0}

#Rencia 26 March 2017 Meter Meter Bulk Online Search / Capture : Add New Group function & phone no
customer.phone=Phone Number

#Rencia 10 March 2017 Meter Bulk Online Search / Capture add group entry function
grouptype.field.layout.order=Layout Order
grouptype.field.layout.order.help=This is the order in which Usage Point Group Selection boxes are displayed on the page. If not entered will default to AFTER all the numbered ones, in alphabetical name order.
grouptype.field.layout.order.error.numeric=Layout order must be a numeric integer.
grouptype.field.layout.order.error.duplicate=Layout order is a duplicate. Same as group {0}. 
error.field.installdate.invalid=Installation Date is not a valid date. Format = {0}

#Rencia 27 February 2017 Meter Bulk Online Search / Capture
meter.online.bulk.header=Add Meters to Group/s
meter.online.bulk.title=Select or Add Group/s
meter.online.bulk.installdate=Installation Date
meter.online.bulk.select.meters.button=Select Meters for Group/s
meter.online.bulk.usagepoint.status=Status
meter.online.bulk.search.no.results=No matching search results were found.
meter.online.bulk.add.meters.to.groups=Add Meters to Group/s
meter.online.bulk.button.add=Add Meter
meter.online.bulk.add.meter=Add Meter
meter.online.bulk.edit.meter=Edit Meter

online.bulk.panel.up.group.info.title=Usage Point Information
online.bulk.panel.customer.info.title=Customer Information
online.bulk.panel.meter.help=Start typing the meter number, meters that start with those digits for the selected device store will appear in a dropdown. Click on one to select it.
online.bulk.panel.select.store.help=The store from which meters will be selected. If none selected, meter numbers from all stores will be shown in the meter suggestions. Note that when the meter is assigned to a Usage Point it will be automatically removed from the store.
online.bulk.panel.suite.no.text=Unit
online.bulk.panel.tenant.text=Tenant
online.bulk.panel.surname.help=Can enter the tenant's surname. Will default to numeric sequence. This is a required field - usage point cannot be activated without it.

online.bulk.panel.error.supply.grpcode.empty=For STS meters, Supply Group Code must be selected
online.bulk.panel.error.algorithm.code.empty=For STS meters, Algorithm Code must be selected
online.bulk.panel.error.token.tech.code.empty=For STS meters, Token Tech Code must be selected
online.bulk.panel.error.tariff.indx.empty=For STS meters, Tariff Index must be entered and max length 2
online.bulk.panel.error.key.rev.indx.empty=For STS meters, Key Revision Code must be entered and must be numeric
online.bulk.panel.error.ps.meter.model.empty=Meter Model is required for Pricing Structure selection
online.bulk.panel.error.model.new.pricingstructure.required=Meter Model does not support the pricing structure.
online.bulk.panel.error.meter.num.not.found=Meter Number is not on Database. 
question.confirm.continue.new=Capture as new meter for this grouping? 
online.bulk.panel.error.meter.already.linked=Meter Number is already linked to Usage Point. 
question.confirm.continue.open.up.page=Open Usage Point tab for it?
online.bulk.panel.error.meter.linked.to.diff.store=Meter Number is in a different store, {0}. 
question.confirm.continue.save.anyway=Continue and link to this grouping?
online.bulk.meter.error.groups.not.selected=For saving a meter all required Usage Point Groups must be selected.

meter.key.revision.help=Enter the current Key Revision no. This field is required for activation.
meter.key.revision=Key Revision

online.bulk.sts.meter.save.error=Problem saving STS Meter, codes not found.
online.bulk.meter.save.error=Problem saving Meter.

meter.sts.length=STS meter numbers can be maximum 13 characters in length and minimum 11 characters.

#Njigi 3 March 2017
usagepoint.charge.button.writeoff=Writeoff Charges
usagepoint.charge.button.upchargeview=View Outstanding Charges
usagepoint.charge.view.dialog.heading=View Outstanding Charges
usagepoint.charge.no.data=No data is available to be viewed.
usagepoint.charge.view.dialog.nodate.filter=Please select date.
usagepoint.charge.view.dialog.invalid.date=Date selected should be after last cyclic date displayed
usagepoint.charge.writeoff.trans.success=Writeoff processed successfully.
usagepoint.charge.writeoff.trans.failure=Writeoff processed failed. Contact support.
chargewriteoff.save.error=Saving of charge writeoff record failed.

#Njigi 30 January 2017 ####
auxaccount.trans.upload=Aux Transaction Upload
auxaccount.trans.upload.heading=Aux Transaction Upload
auxaccount.trans.upload.auxaccountname=Aux Account Name
auxaccount.trans.upload.agreementref=Agreement Ref
auxaccount.trans.upload.data.title=Import Aux Account Balance Adjustment Transactions
auxaccount.trans.upload.data.description=Select the CSV file containing the Aux transactions for importing into the Meter Management system.
auxaccount.trans.upload.invalid.auxaccountname=Aux Account Name maximum 100 chars
auxaccount.trans.upload.invalid.auxaccount=No Aux Account with the provided Name and Agreement Ref exist
auxaccount.trans.upload.process.failed=System Error on transaction:AuxAccount Name= {0}, Agreement Ref= {1}. Try resubmitting the file.
trans.bulk.upload.amt.incl.tax=Amt incl Tax
trans.bulk.upload.amt.tax=Tax Amt
trans.bulk.upload.trans.date=Transaction date
trans.bulk.upload.account.ref=Account Reference
trans.bulk.upload.comment=Comment
trans.bulk.upload.invalid.amt.incl.tax=Amount incl. Tax is not numeric
trans.bulk.upload.invalid.amt.tax=Tax Amount is not numeric
trans.bulk.upload.invalid.account.ref=Account Reference maximum 100 chars
trans.bulk.upload.invalid.comment=Comment maximum 255 chars
trans.bulk.upload.invalid.our.ref=Our Reference maximum 100 chars (Rename file)
trans.bulk.upload.trans.date.in.future=Transaction Date can not be in the future 

#Njigi 16 January 2016 ####
auxaccount.upload=Auxiliary Account Upload
auxaccount.upload.heading=Auxiliary Account Upload
auxaccount.upload.data.title=Import Auxiliary Account Data
auxaccount.upload.data.description=Select the CSV file containing the Auxiliary Account data for importing into the Meter Management system.
auxaccount.upload.errors=Errors
auxaccount.upload.identifierType=Identifier Type
auxaccount.upload.identifier=Identifier
auxaccount.upload.auxaccountname=Aux Account Name
auxaccount.upload.auxtype=Aux Type Name
auxaccount.upload.accountpriority=Account Priority
auxaccount.upload.chrgschdlname=Charge Schedule Name
auxaccount.upload.principleamaount=Principle Amount
auxaccount.upload.balance=Balance
auxaccount.upload.customerref=Customer Agreement Ref
auxaccount.upload.invalid.identifiertype=IdentifierType must be agreementRef / usagePointName / meterNumber
auxaccount.upload.invalid.identifier=Invalid Identifier - not in database
auxaccount.upload.invalid.agreement=Usage Point does not have a customer agreement in place
auxaccount.upload.invalid.usagepoint.or.agreement=Meter has no usage point or the usage point has no agreement
auxaccount.upload.invalid.auxaccountname=Aux Account Name maximum 100 chars
auxaccount.upload.invalid.principleamaount=Principle Amount is not numeric
auxaccount.upload.invalid.balance=Balance Amount is not numeric
auxaccount.upload.successful.count=Total of {0} Aux Account uploads successfully processed.

#Antonyo 12 January 2017  ####

power.limit.add.button.prompt=Add Power Limit
power.limit.table.label.header=Power Limit Name
power.limit.table.value.header=Power Limit Value
power.limit.edit.popup.header=Edit Power Limit

#Patrick 16 December 2016 ####
user.custom.field.datatype.text=Text
user.custom.field.datatype.numeric=Numeric
user.custom.field.datatype.date=Date
user.custom.field.datatype.boolean=True/False
user.custom.field.datatype.list=List
button.done=Done

usagepointgroup.custom.field.error.text=A value is required for this field
usagepointgroup.custom.field.error.list=You must select an item from the list
usagepointgroup.custom.field.error.date.empty=A valid date is required. Format (yyyy-MM-dd HH:mm:ss)
usagepointgroup.custom.field.error.date.invalid=Invalid date format. Format (yyyy-MM-dd HH:mm:ss)
usagepointgroup.custom.field.error.numeric=A valid number must be provided

button.view.usagepointgroup=View UsagePoint group

usagepointgroup.custom.field.default.datatype.description=The dataType for this custom field, or a comma-seperated list for dropdowns
usagepointgroup.hierarchy.no.additionalinfo=No additional information for this UsagePoint Group

appsettings.popup.button.add=Add Item
appsettings.popup.table.add=New Item - Click to Edit
appsettings.popup.table.title=List Items
appsettings.popup.table.note.dups=Duplicate items will be removed when Done

appsetting.field.datatype=Datatype
appsetting.field.datatype.help=Datatype for the Application Setting. For Datatype List, List items will be discarded when data type is changed to: Text, Numeric, Date, or True/False.
appsetting.field.datatype.listitems=Edit Items

#Njigi 22 December 2016 ####
bulk.upload.custom.fields.get.error=Database error getting custom field application settings! Contact Support.

#Njigi 28 November 2016 ####
bulk.upload.meterupload.heading=Meter Bulk Upload
bulk.upload.meterupload.enddevicestorename=Device Store Name
bulk.upload.meterupload.data.title=Import Meter Details
bulk.upload.meterupload.description=Select the CSV file containing the meter details for importing into the Meter Management system.

#Special Actions - joelc 01/12/16 
link.special.actions=Actions with reasons
link.special.action.reasons = Special Action Reasons
button.viewreasons = View reasons list
special.action = Special action
special.actions.header=Configure Special Actions
special.actions.title=Update Special Actions
special.action.name=Action Name
special.action.name.help= The name of this special action.
special.action.reason.required= Required
special.action.reason.required.help= Is it required or optional to supply a reason
special.action.reason.input.type= Input Type
special.action.reason.input.type.help= Is the reason given as free text, is it selected from a list of reasons or is it either free text and/or selected from a list 
special.action.description=Action description
special.action.description.help=A description of the special action that allows a reason for the action to be supplied
special.actions.field.name=	Action name
special.actions.field.description=Description	
special.actions.field.reason.required=Required	
special.actions.field.reason.input.type=Input type
special.actions.reason.inputtype.freetext=Enter reason into textbox
special.actions.reason.inputtype.selected=Select reason from list 
special.actions.reason.inputtype.both=Enter reason or select from list

special.action.reasons.header = Setup Reasons
special.action.reasons.title = Special action reasons
special.action.reasons.title.add = Add new reason
special.action.reasons.title.update = Update reason
special.action.reasons.field.name = Reason
special.action.reasons.field.description = Reason description
special.action.reasons.field.recordstatus = Active
special.action.reasons.name = Reason 
special.action.reasons.name.help = Enter a possible reason for doing this action. This will be displayed in the drop down list for that action. 
special.action.reasons.description = Description
special.action.reasons.description.help = Describe what this reason is about.
special.action.reasons.active = Active
special.action.reasons.active.help = Is this reason active?
special.action.reason = Reason 
special.action.and.or = and/or
special.action.enter.reason = Enter a reason for this action
special.action.select.reason = Select a reason for this action
error.special.action.reason.required = A reason for this action is required     
usagepoint.deactivate.enter.reason = Enter a reason for this deactivation
usagepoint.deactivate.select.reason = Select a reason for this deactivation

#Rencia 24 November 2016  ####
bulk.upload.pricingstructure.not.active=Pricing Structure is not active.

#Rencia 16 November 2016  ####
#database actions
db.action.update=update
db.action.insert=insert
db.action.delete=delete

#MDC controltypes
controltype.connect=CONNECT
controltype.disconnect=DISCONNECT
controltype.disconnect.enable=DISCONNECT_ENABLE
controltype.pan.display=PAN_DISPLAY
controltype.sync.balance=SYNC_BALANCE
controltype.adjust.balance=ADJUST_BALANCE
controltype.power.limit=POWER_LIMIT

error.field.active.invalid=Active must be blank (implies YES) or no.
error.field.metermodelid.null=Meter Model is required.
error.field.ststariffindex.max=Tariff Index must be less than {0} characters.
error.field.servicelocationid.null= Service location is required
error.field.customerkindid.null=Customer Kind 
error.field.customerid.null=Customer id is required
error.field.accountbalance.null=Account Balance is required.
meter.select.tokentype.help=Select a token type from the list below.

#Bulk Upload - standard
bulk.upload.download.sample=Download a sample spreadsheet.
bulk.upload.download.sample.button=Download as .CSV
bulk.upload.file.select.labeltext=Select CSV File to upload
bulk.upload.select.file.help=Select a file containing the information in the specified csv format for importing into the system
bulk.upload.csv.button=Upload CSV Data
bulk.upload.process.button=Process Upload
bulk.upload.errors=Errors
bulk.upload.object.creation.error=Error creating {0} object. Contact Support!
bulk.upload.file.action.unknown=Unknown file upload action, contact Support
bulk.upload.file.none=No file was selected to be uploaded
bulk.upload.invalid.filename=Improper filename - hyphen or period missing. Filenames expected as xxxxxx-reference.csv where xxxxxx is your chosen file identifier, eg. MeterUpload, and <reference> is saved as 'our Ref' on transactions
bulk.upload.invalid.filename.changed=Filename has changed between steps! Was {0}; now {1}
bulk.upload.invalid.cannot.create.dir=ERROR! Cannot create the directory. Please contact Support.
bulk.upload.file.unrecognized.heading.error=Unrecognized Column Heading in upload file: {0}
bulk.upload.table.heading.valid=Valid Upload Data : sample of first 15 lines in file
bulk.upload.table.heading.errors=Upload Data : Errors
bulk.upload.trans.validation.errors=Validation errors found. Please repair and resubmit. Maximum 15 errors are processed at any one time
bulk.upload.invalid.unexpected.commas=Commas inside fields - cannot identify separate fields accurately 
bulk.upload.filename= Selected filename={0}
bulk.upload.process.failed=System Error on upload after {0} records. Try resubmitting the file.
bulk.upload.active=Active
#Bulk Upload Validation Errors
bulk.upload.invalid.required.field={0} Required 
bulk.upload.invalid.nonexisting.field={0} Not on Database
bulk.upload.duplicate.field={0} already exists, either on Database or in this upload file
bulk.upload.invalid.field={0} is invalid
bulk.upload.invalid.parsedate=Cannot parse {0} - check format
bulk.upload.file.process.error=Error while processing the upload file

#Bulk Upload - meter / Customer / Usage Point
bulk.upload.data.title.meter=Import Meter, Usage point and Customer Details
bulk.upload.data.description.meter=Select the CSV file containing the meter & related details for importing into the Meter Management system.
bulk.upload.metertype=Meter Type
bulk.upload.meternum=Meter Num
bulk.upload.serialnum=Serial Num
bulk.upload.mrid=External MeterNum
bulk.upload.metermodelname=Meter Model
bulk.upload.enddevicestore=Device Store
bulk.upload.breakerid=Breaker Id
bulk.upload.ststokentechcode=Token Tech Code
bulk.upload.stsalgorithmcode=Algorithm Code
bulk.upload.stssupplygroupcode=Supply Group Code
bulk.upload.stskeyrevisionnum=Key Rev Num
bulk.upload.ststariffindex=Tariff Indx
bulk.upload.usagepointname=Usage Point Name
bulk.upload.installationdate=Install Date
bulk.upload.pricingstructurename=Pricing Structure
bulk.upload.uperfnumber=UP Erf Num
bulk.upload.upstreetnum=UP Street Num
bulk.upload.upbuildingname=UP Building
bulk.upload.upsuitenum=UP Suite Num
bulk.upload.upaddressline1=UP Addr 1
bulk.upload.upaddressline2=UP Addr 2
bulk.upload.upaddressline3=UP Addr 3
bulk.upload.uplatitude=UP Latitude
bulk.upload.uplongitude=UP Longitude
bulk.upload.upcustomvarchar1=UP Custom Char1
bulk.upload.upcustomvarchar2=UP Custom Char2
bulk.upload.upcustomnumeric1=UP Custom Num1
bulk.upload.upcustomnumeric2=UP Custom Num2
bulk.upload.upcustomtimestamp1=UP TimeStamp1
bulk.upload.upcustomtimestamp2=UP TimeStamp2
bulk.upload.customerkind=Customer Kind
bulk.upload.companyname=Company Name
bulk.upload.taxnum=Tax Num
bulk.upload.firstnames=First Names
bulk.upload.surname=Surname
bulk.upload.initials=Initials
bulk.upload.title=Title
bulk.upload.email1=Email1
bulk.upload.email2=Email2
bulk.upload.phone1=Phone1
bulk.upload.phone2=Phone2
bulk.upload.custerfnumber=Cust Erf
bulk.upload.custstreetnum=Cust Street num
bulk.upload.custbuildingname=Cust Building
bulk.upload.custsuitenum=Cust Suite
bulk.upload.custaddressline1=Cust Addr 1
bulk.upload.custaddressline2=Cust Addr 2
bulk.upload.custaddressline3=Cust Addr 3
bulk.upload.custlatitude=Cust Latitude
bulk.upload.custlongitude=Cust Longitude
bulk.upload.custcustomvarchar1=Cust Custom Char1
bulk.upload.custcustomvarchar2=Cust Custom Char2
bulk.upload.custcustomnumeric1=Cust Custom Num1
bulk.upload.custcustomnumeric2=Cust Custom Num2
bulk.upload.custcustomtimestamp1=Cust Custom Timestamp1
bulk.upload.custcustomtimestamp2=Cust Custom Timestamp2
bulk.upload.agreementref=Agreement Ref
bulk.upload.startdate=Agreement Start Date
bulk.upload.accountname=Account Name
bulk.upload.accountbalance=Account Balance
bulk.upload.lowbalancethreshold=Low Bal Threshold
bulk.upload.notificationemail=Notif Email
bulk.upload.notificationphone=Notif Phone
bulk.upload.notifylowbalance=Notify Low Bal
#Invalid Meter bulk upload errors
bulk.upload.metertype.incompatible.stsinfo=STS encryption details entered for non-STS meter type
bulk.upload.metertype.incompatible.metermodel=Meter Model not compatible with Meter Type
bulk.upload.invalid.pricingstructure.incompatible.metermodel=Pricing structure incompatible with Meter Model
bulk.upload.customer.account.notfor.STS=STS meters should not have Customer Account Info

#Rencia 11 November 2016  ####
tariff.field.namibia.neflevy=NEF Levy
tariff.field.namibia.neflevy.help=Enter National Energy Fund Levy
tariff.field.namibia.ecblevy=ECB levy
tariff.field.namibia.ecblevy.help=Enter Electricity Control Board Levy
tariff.error.positive.or.zero=Value must be positive or zero

#Rencia 9 November 2016  ####
tariff.field.discount=Payment Type Discounts
tariff.field.heading.paytype=Payment Type
tariff.field.heading.discount=Discount %
tariff.field.heading.description=Description
tariff.error.discount=Discount % must be positive or zero.
tariff.error.discount.descrip=If discount % entered, and not zero, description is required.

# Rencia 20 October 2016  ####
### New
tariff.field.cycle.name=Cycle
tariff.field.cycle.name.help=Select the cycle of billing.
tariff.field.cost.help=The amount excluding tax to be paid back as per cycle selection.

### Took out "Monthly"
tariff.field.cost.name=Cost Name
tariff.field.cost.name.help=The name for this cost that will end up on the customer's receipt.
tariff.field.cost=Cost Excl. Tax

### Tariff Cycle Labels  NEW
tariff.cost.cycle.daily=Daily
tariff.cost.cycle.monthly=Monthly
tariff.cost.cycle.error=Must select a valid cycle.

### changed names
tariff.error.cyclic.charge.positive=Must be a positive value.
tariff.error.cyclic.charge=Specify a cost for the name.
tariff.error.cyclic.charge.name=Specify a name for the cost.

#Rencia 22 August 2016  ####
error.field.numeric.positive_not_zero=Value must be positive and not zero.
tariff.field.percent_charge_name=Percentage Charge Name
tariff.field.percent_charge_name.help=Optional name of the percentage charge that will be displayed on the receipt.
tariff.field.percent_charge=Percentage Charge
tariff.error.percent_charge.name=Specify a name for the charge.
tariff.error.percent_charge.positive_not_zero=Must be a positive value and not zer

##########################
#### General messages ####
##########################

application.default.title=iPay - ZÃ¤hlerverwaltung
application.title=ZÃ¤hlerverwaltung
workspace.usagepoint.information=Informationen und Service
unit.kilowatthour.symbol=kWh
unit.watts.symbol=W
unit.percent=%
changes_unsaved=* ungespeicherte Ãnderungen
menu.about_link=Ãber
application.info=Powered by iPay's BizSwitch

###############
#### Error ####
###############
error.title=Fehler
error.general=Ein Fehler ist aufgetreten und wurde protokolliert. Bitte informieren Sie Ihren Systemadministrator.
error.login=Ihr Login-Versuch war nicht erfolgreich. Bitte versuchen sie es erneut.
error.denied=Benutzer {0} ist nicht berechtigt auf diese Seite zuzugreifen.
error.accessdenied=Sie haben keine Berechtigung, diese FunktionalitÃ¤t zu nutzen.
error.networkConnect=Bei dem Versuch den Server zu kontaktieren ist ein Fehler aufgetreten.
error.networkIO=Ein Netzwerkkommunikationsfehler ist aufgetreten.
error.networkTimeout=Der Server benÃ¶tigt zu lange um zu antworten.
error.networkUnknown=Bei dem Versuch den Server zu kontaktieren ist ein unbekannter Fehler aufgetreten. Benachrichtigen Sie den Administrator.
error.delete=Kann nicht gelÃ¶scht werden
permission.denied=Zugriff verweigert
permission.edit.denied=Bearbeitung Zugriff verweigert
error.no.user=Kein aktueller Benutzer verfÃ¼gbar.
error.current.group=UngÃ¼ltige Gruppe fÃ¼r aktuelle Benutzergruppe festgelegt.
error.meter.accessdenied=Sie haben keine Berechtigung, auf diesen ZÃ¤hler zuzugreifen.
error.pricing.structure.accessdenied=You do not have permission to edit pricing structure allocation.
error.pricing.structure.accessdenied.addmeter=You do not have permission to edit pricing structure allocation, so cannot attach the new meter above to this usage point. The meter has been saved in the selected device store.
error.meter.workspace.error=Fehler beim Ãffnen des ZÃ¤hlerarbeitsbereich. 
error.customer.workspace.error=Fehler beim Ãffnen des Kundenarbeitsbereich.
error.usagepoint.workspace.error=Error opening usage point workspace.
error.no.user.assignedgroup=Der aktuelle Benutzer hat keinen zugewiesenen Gruppensatz
error.login.locked=Ihr Konto wurde gesperrt.
error.loginerror=Benutzername oder Passwort sind falsch.
error.login.disabled=Ihr Konto wurde deaktiviert.

###############
#### Login ####
###############
login.title=Anmeldung
login.form.title=Bitte geben Sie Ihren Benutzernamen und Passwort ein.
login.form.username=Benutzername:
login.form.password=Passwort:
login.form.login=anmelden
login.form.remember_me=FÃ¼r 2 Wochen merken
login.form.password.forgotten=Forgotten your password?
password.form.instructions=Enter your user's registered email address.
password.email.invalid=Invalid email address.
password.email.unknown.user=Unknown user for the entered email address.
password.multiple.users=Multiple users match the entered email address.
password.multiple.users.1=Please select one below and click Submit.
password.reset=Password reset. Please check your email for additional details.
password.error.reset=Error: Unable to reset your password.  
adminuser.password.save.error=Error: Unable to save updated user.
email.password.reset.subject=Password Reset
password.form.email=Email Address:
password.form.submit=Submit

#########################
#### Password Change ####
#########################
password_change.title=Passwort Ã¤ndern
password_change.form.title=Mein Passwort Ã¤ndern
password_change.form.password1=Neues Passwort eingeben:
password_change.form.password2=Neues Passwort erneut eingeben:
password_change.form.submit=Ãnderung abschicken
password_change.success=Passwort geÃ¤ndert fÃ¼r {0}
password_change.validate.equal=Die zwei Passworteingaben sind nicht gleich
password_change.validate.ldap=LDAP authentifizierte Benutzer kÃ¶nnen ihre PasswÃ¶rter nicht Ã¤ndern. Kontaktieren Sie Ihren LDAP-Systemadministrator.

########################################################################################################################
# GWT specific properties #
########################################################################################################################

## Errors
error.save=Fehler: {0} konnte nicht gespeichert werden.
error.field.required=Pflichtfeld.
error.field.is.required={0} ist ein Pflichtfeld.
error.field.numeric.required={0} ist numerisches Pflichtfeld.
error.no.selection=Keine gÃ¼ltige Auswahl wurde vorgenommen.
error.data.null=Eingehende Daten sind ungÃ¼ltig.
error.datatype.null={0} ist ungÃ¼ltig.
error.missing={0} {1} nicht gefunden.
error.numeric.value=Geben Sie einen gÃ¼ltigen numerischen Wert ein.
error.token.retrieve=Nicht in der Lage, das Guthaben abzurufen.
error.meter.load=Nicht in der Lage, den ZÃ¤hler anzuzeigen.
error.usagepoint.load=Unable to display the usage point.

# Field errors used by the Validation framework's annotations
error.field.id.null=ID erforderlich.
error.field.key.null=SchlÃ¼ssel erforderlich.
error.field.key.range=SchlÃ¼ssel muss zwischen {min} und {max} Zeichen lang sein.
error.field.name.null=Name erforderlich.
error.field.name.range=Name muss zwischen {min} und {max} Zeichen lang sein.
error.field.name.max=Name muss weniger als {max} Zeichen lang sein.
error.field.value.null=Ein Wert ist erforderlich.
error.field.value.range=Wert muss zwischen {min} und {max} Zeichen lang sein.
error.field.description.null=Beschreibung erforderlich.
error.field.description.range=Beschreibung muss zwischen {min} und {max} Zeichen lang sein.
error.field.description.max=Beschreibung muss weniger als {max} Zeichen lang sein.
error.field.recordstatus.null=Registrierungsstatus ist erforderlich.
error.field.contactname.null=Kontaktname ist erforderlich.
error.field.contactname.range=Kontaktname muss zwischen {min} und {max} Zeichen lang sein.
error.field.contactemail=Kontakt E-Mail muss eine gÃ¼ltige E-Mail-Adresse sein.
error.field.contactemail.max=Kontakt E-Mail muss kleiner als {max} Zeichen lang sein.
error.field.taxref.max=Tax Reference must be less than {max} characters.
error.field.contactnumber.max=Kontaktnummer muss kleiner als {max} Zeichen lang sein.
error.supplyserver=Code und Revisionsnummer Kombination der Versorgungsgruppe muss eindeutig sein.
error.field.supplygroupcode.null=Versorgungsgruppecode ist erforderlich.
error.field.keyrevisionnum.null=SchlÃ¼ssel Revisionsnummer ist erforderlich.
error.field.supplygroupcode.format=Geben Sie einen numerischen Wert ein.
error.field.supplygroupcode.range=Code muss weniger als 7 Ziffern lang sein.
error.field.calccontents.null=Kalkulation ist ein Pflichtfeld.
error.field.schedulename.range=Planname muss zwischen {min} und {max} Zeichen lang sein.
error.minmax.range.auxchargeschedule=Mindestbetrag muss kleiner als Maximalbetrag sein.
error.field.meternum.null=ZÃ¤hlernummer ist ein Pflichtfeld.
error.field.meternum.range=ZÃ¤hlernummer muss zwischen {min} und {max} Zeichen lang sein.
error.field.mrid.null=MRID ist ein Pflichtfeld.
error.field.mrid.range=MRID muss zwischen {min} und {max} Zeichen lang sein.
error.field.serialnum.max=Seriennummer muss kleiner als {max} Zeichen lang sein.
error.field.meternumchecksum.max=PrÃ¼fsumme muss kleiner als {max} Zeichen lang sein.
error.field.ststokentechcode.max=Guthaben Tech-Code muss kleiner als {max} Zeichen lang sein.   
error.field.stsalgorithmcode.max=Algorithmus-Code muss kleiner als {max} Zeichen lang sein.
error.field.stsprevsupplygroupcode.max=Versorgungsgruppecode muss kleiner als {max} Zeichen lang sein.
error.field.stscurrtariffindex.max=Der Tarif Index muss kleiner als {max} Zeichen lang sein.
error.field.addressline1.max=Jeder Adresszeile muss kleiner als {max} Zeichen lang sein.
error.field.addressline2.max=Jeder Adresszeile muss kleiner als {max} Zeichen lang sein.
error.field.addressline3.max=Jeder Adresszeile muss kleiner als {max} Zeichen lang sein.
error.field.city.max=Stadt muss weniger als {max} Zeichen lang sein.
error.field.province.max=Bundesland muss kleiner als {max} Zeichen lang sein.
error.field.country.max=Land muss kleiner als {max} Zeichen lang sein.
error.field.postalcode.max=Postleitzahl muss kleiner als {max} Zeichen lang sein.
error.field.erfnumber.max=Erf Nummer muss kleiner als {max} Zeichen lang sein.
error.field.streetnum.max=Hausnummer muss kleiner als {max} Zeichen lang sein.
error.field.buildingname.max=GebÃ¤udename muss weniger als {max} Zeichen lang sein.
error.field.suitenum.max=Zimmernummer muss kleiner als {max} Zeichen lang sein.
error.field.surname.null=Nachname ist ein Pflichtfeld.
error.field.agreementref.null=Die Vertragsreferenz ist ein Pflichtfeld.
error.field.agreementref.duplicate=Doppelte Vertragsreferenz {0}. Geben Sie eine eindeutige Referenz ein.
error.field.email1.max=Die E-Mail Adresse muss kleiner als {max} Zeichen lang sein.
error.field.email1=E-Mail-Adresse ist ungÃ¼ltig.
error.field.email2.max=E-Mail Adresse muss kleiner als {max} Zeichen lang sein.
error.field.email2=E-Mail-Adresse ist ungÃ¼ltig.
error.field.phone1.max=Telefonnummer muss kleiner als {max} Zeichen lang sein.
error.field.phone2.max=Telefonnummer muss kleiner als {max} Zeichen lang sein.
error.field.phone=Telefonnummer ist ungÃ¼ltig, nur 0-9, '+', Leerzeichen, Bindestrich, "DW" ist erlaubt. "DW" muss von einer numerischen Nebenstellennummer gefolgt sein.
error.field.phone2=2.Telefonnummer ist ungÃ¼ltig, nur 0-9, '+', Leerzeichen, Bindestrich, "DW" ist erlaubt. "DW" muss von einer numerischen Nebenstellennummer gefolgt sein.
error.field.startdate.null=Startdatum ist ein Pflichtfeld.
error.field.startdate.future=Startdatum kann nicht in die Zukunft sein.
error.field.installdate.future=Installation Date can not be in the future.
error.powerlimit.invalid=Einen gÃ¼ltigen Wert fÃ¼r die Leistungsgrenze wÃ¤hlen (erforderlich)
error.priority.invalid=PrioritÃ¤t muss eine Zahl sein (1 ist die hÃ¶chste PrioritÃ¤t)
error.search.meter=Geben Sie eine gÃ¼ltige ZÃ¤hlernummer ein.
error.search.customer=Geben Sie einen gÃ¼ltigen Suchbegriff ein.
error.field.touseasonname.null=Saison Name ist ein Pflichtfeld.
error.field.touperiodname.null=Zeitraum Name ist ein Pflichtfeld.
error.field.touperiodcode.null=Zeitraum Code ist ein Pflichtfeld.
error.field.title.max=Anrede muss kleiner als {max} Zeichen lang sein.
error.field.initials.max=Initialen mÃ¼ssen kleiner als {max} Zeichen lang sein.
error.field.firstnames.max=Vorname muss weniger als {max} Zeichen lang sein.
error.field.surname.max=Nachname muss kleiner als {max} Zeichen lang sein.
error.field.companyname.max=Unternehmen muss weniger als {max} Zeichen lang sein.
error.field.taxnum.max=Steuernummer muss kleiner als {max} Zeichen lang sein.
error.field.agreementref.max=Vertragsreferenz muss kleiner als {max} Zeichen lang sein.
error.field.usagepoint.name.null=Name des Einsatzorts ist erforderlich
error.field.usagepoint.name.duplicate=Doppelter Name {0} fÃ¼r einen Einsatzort. Geben Sie einen eindeutigen Namen ein.

error.field.comment.max=Bemerkung muss weniger als {max} Zeichen lang sein.
error.field.accountref.max=Kontoreferenz muss kleiner als {max} Zeichen lang sein.
error.field.ourref.null=Unsere Referenz erforderlich.
error.field.ourref.range=Unsere Referenz muss zwischen {min} und {max} Zeichen lang sein.
error.field.amtincltax.null=Betrag muss eingegeben werden.
error.field.amttax.null=Steuer kann nicht null sein. Geben Sie 0 ein, wenn keine Steuer.
error.field.customvarchar.max=Custom Text Field must be less than {max} characters.

error.field.accountname.range=Account Name must be less than {max} characters. 
 
# Error headers, etc
error.validation.header=Bitte korrigieren Sie diese Validierungsfehler:
error.validation.fields.header=Bitte korrigieren Sie diese Eingabefehler:
error.field.code.null=Code ist erforderlich
error.field.code.range=Code muss zwischen {min} und {max} Zeichen lang sein.
error.server.connection=Keine Verbindung zum Web-Server.
error.server=Fehlermeldung vom Web-Server empfangen.
error.field.manufacturerid.null=Hersteller ist erforderlich.
error.field.serviceresourceid.null=Serviceeinsatz ist erforderlich.
error.field.metertypeid.null=ZÃ¤hlerart ist erforderlich.
error.field.paymentmodeid.null=Zahlungsweise erforderlich.
error.field.accountname.null=Kontoname ist erforderlich
error.field.accountname.duplicate=Doppelter Kontoname {0}. Geben Sie einen eindeutigen Namen ein.
error.field.taskschedulename.null=Name ist erforderlich.
error.field.taskschedulename.max=Name muss weniger als {max} Zeichen lang sein.
error.field.cronexpression.null=Zeitplan ist erforderlich.
error.field.cronexpression.range=Zeitplan muss zwischen {min} und {max} Zeichen lang sein.
error.field.scheduledtaskid.null=Aufgabenart ist erforderlich.
error.field.taskschedulename.range=Aufgaben Zeitplan Name ist erforderlich.
error.field.taskclassid.null=Aufgabentyp ist erforderlich.
error.field.scheduledtaskname.range=Name muss zwischen {min} und {max} Zeichen lang sein.
error.field.taskscheduleid.null=Aufgaben Zeitplan ist erforderlich.
error.messages.header=Fehler:

# Customer Account Threshold errors
error.field.lowbalance.null=geringer Depotstand muss eingegeben werden
error.field.emergencycredit.null=Notfallkredit mÃ¼ssen eingegeben werden
error.field.disconnect.null=Unterbrechen muss eingegeben werden
error.field.reconnect.null=Wiedereinschaltung muss eingegeben werden

# Questions
question.discard.changes=Wollen Sie die aktuellen Ãnderungen verwerfen?
question.discard.potential.changes=Wenn Sie Ãnderungen noch nicht gespeichert haben werden sie verworfen. Weiter?
option.yes=Ãnderungen verwerfen
option.no=Abbrechen
option.positive=Ja
option.negative=Nein
question.delete=MÃ¶chten Sie den ausgewÃ¤hlten Inhalt lÃ¶schen?
dayprofile.question.delete=MÃ¶chten Sie den ausgewÃ¤hlten Inhalt lÃ¶schen? Dadurch werden auch alle Zeiten gelÃ¶scht, die diesem Tagesprofil zugeordnet sind.
question.confirm.installation.date.1=BestÃ¤tigen Sie, dass ZÃ¤hler {0} am {1} um {2} installiert wurde.
question.confirm.installation.date.2=Es ist wichtig, dass dieses Datum und die Uhrzeit korrekt sind.
question.confirm.link.to.customer=Customer {0} is already on the page. Fetching this usagePoint here will automatically assign that customer to it. Continue?
question.close.tabs.1=Kann nicht gespeichert werden, wÃ¤hrend anderen Registerkarten, die mÃ¶glicherweise auf dieses Element verweisen, geÃ¶ffnet sind.
question.close.tabs.2=Sie kÃ¶nnen diese selbst schlieÃen und dann erneut versuchen zu speichern - wÃ¤hlen Sie NEIN oder
question.close.tabs.3=sollen anderen Registerkarten automatisch geschlossen werden (nicht gespeicherten Daten gehen verloren) - wÃ¤hlen Sie JA? 
option.confirm=BestÃ¤tigen
option.continue=Weiter?

## Messages
message.saved={0} wurde gespeichert.

## Links
link.logout=Logout
link.loggedin=Benutzer:
link.group.change=Ãndere Gruppe 
link.group=Gruppe:  
link.meters=ZÃ¤hler
link.customers=Kunden
link.groups=Gruppen
link.menu=MenÃ¼
link.pricingstructure=Preisstruktur
link.calendars=Kalender
link.calendarsettings=Kalendereinstellungen
link.auxchargeschedule=Hilfs GebÃ¼hrenschema
link.auxilliarytype=Zusatzart aux
link.supplygroup=Versorgungsgruppe
link.displaytokens=Anzeige Guthaben                                     
link.devicestores=GerÃ¤telager
link.usergroup=Benutzerzugriffsgruppe
link.accessgroups=Zugriffsgruppen
link.search=Suche
link.search.advanced=Erweiterte Suche
link.search.meters.viewed=Zuletzt angezeigte ZÃ¤hler
link.search.meters.modified=Zuletzt geÃ¤nderte ZÃ¤hler
link.meter.readings=ZÃ¤hlerstÃ¤nde
link.energybalancing=Energiebilanzierung
link.energybalancing.meters=HauptzÃ¤hler
link.analytics=Analytik 
link.configuration=Konfiguration 
link.tools=Werkzeuge
link.taskschedules=Aufgaben Zeitplan
link.locationgroups=Standortgruppen
link.about=Ãber
link.appsettings=Anwendungseinstellungen
link.global.ndp=Global NDP
link.billingdet=Billing Determinants

## Buttons ##
button.save=Speichern
button.new=Neu
button.edit=Bearbeiten
button.back=ZurÃ¼ck
button.cancel=Abbrechen
button.close=SchlieÃen
button.select=WÃ¤hlen
button.delete=LÃ¶schen
button.viewentity=VAnsicht Gesamtheit
button.yes=Ja
button.no=Nein
button.create=Erstellen
button.update=Aktualisieren
button.viewtariffs=Ansicht Tarife
button.replacemeter=ZÃ¤hler tauschen
button.removemeter=Remove Meter
button.displayunits=Anzeigeeinheiten
button.gettoken=Guthaben erhalten
button.saveaccount=Konto speichern
button.addnew=Neu hinzufÃ¼gen
button.editchargeschedule=Bearbeite GebÃ¼hrenschema 
button.clear.group=LÃ¶sche Gruppe
button.search=Suche
button.clear=LÃ¶schen
button.view=Anzeigen
button.add=HinzufÃ¼gen
button.remove=Entfernen
button.export=Export
button.view.scheduledtasks=Ansicht geplante Aufgaben
button.login=Anmeldung
button.logout=Stelle verlassen
button.set=Einstellen
button.show.inherited=Vererbte Werte anzeigen
button.send=Senden
button.viewtrans=View Transactions

## Menus ##
menu.add=HinzufÃ¼gen
menu.update=Aktualisieren
menu.delete=LÃ¶schen
menu.search=Suchen

## Record Statuses ##
status.active=Aktive
status.inactive=Inaktive
status.deleted=GelÃ¶scht

### Supply Group ###
supplygroups.header=Versorgungsgruppen
supplygroups.title=Aktuelle Versorgungsgruppen
supplygroup.title=Versorgungsgruppe
supplygroup.name=Versorgungsgruppe
supplygroup.field.name=Name
supplygroup.field.code=Code
supplygroup.field.keyrevisionnumber=SchlÃ¼ssel Revisionsnummer
supplygroup.field.keyexpirynumber=SchlÃ¼ssel Verfallsnummer 
supplygroup.field.status=Status
supplygroup.field.active=Aktive
supplygroup.title.add=HinzufÃ¼gen Versorgungsgruppe
supplygroup.title.update=Aktualisieren Versorgungsgruppe
supplygroup.error.save=Versorgungsgruppe kann nicht gespeichert werden.

### Group Type ###
grouptypeshierarchies.title=Gruppenarten und Hierarchien
grouptypes.header=Gruppenarten
grouptypes.hierarchies.header=Gruppenhierarchien
grouptypes.title=Aktuelle Gruppenarten
grouptype.title=Gruppenart
grouptype.field.id=Gruppenart ID
grouptype.field.name=Name
grouptype.field.name.help=Geben Sie den Namen dieser Gruppenart an. Dies ist ein Pflichtfeld - der Datensatz kann nicht ohne sie gespeichert werden.
grouptype.field.description=Beschreibung
grouptype.field.description.help=Geben Sie eine Beschreibung dieser Gruppenart an.
grouptype.field.parent=Ãbergeordnet
grouptype.field.parent.help=Wenn diese Gruppenart in einer Hierarchie unter eine andere Gruppenart gehÃ¶rt, wÃ¤hlen Sie die Ã¼bergeordnete Gruppenart hier.
grouptype.field.active=Aktive
grouptype.field.active.help=Ist diese Gruppe aktiv? Markieren Sie das KÃ¤stchen, um diese Gruppenart zu aktivieren.
grouptype.field.status=Status
grouptype.field.required=Pflichtfeld
grouptype.field.required.help=FÃ¼r Einsatzortgruppen wird festgelegt, ob eine Auswahl fÃ¼r diese Gruppenart erforderlich ist  
grouptype.field.accessgroup=Zugriffsgruppe
grouptype.field.locationgroup=Standortgruppe 
grouptype.field.access.location.group=Zugriff / Standort
grouptype.field.feature=Merkmale
grouptype.field.available.feature=VerfÃ¼gbare Merkmale
grouptype.field.assigned.feature=Zugewiesene Merkmale
grouptype.field.available.feature.help=Wenn ein Merkmal als Einzelauswahl-Funktion markiert ist, kann es nur fÃ¼r einen einzelnen Gruppenarten ausgewÃ¤hlt werden, sobald die Gruppenart in den Daten verwendet wurde, diese Zuordnung kann nicht geÃ¤ndert werden. 
grouptype.field.assigned.feature.help=Dieser Gruppe zugeordnete Merkmale
grouptype.button.viewhierarchies=Ansicht Hierarchien
grouptype.error.noneselected=Keine aktueller Gruppenart ausgewÃ¤hlt.
grouptype.error.parentcanthavehierarchy=Ãbergeordnete Gruppenarten kÃ¶nnen keine Hierarchien haben
grouptype.none=Gruppenart: Keine Auswahl
grouptype.title.add=Gruppenart hinzufÃ¼gen
grouptype.title.update=Gruppenart aktualisieren
grouptype.error.save=Gruppenart kann nicht gespeichert werden.
grouptype.error.accessgroup.users=Die Zugriffsgruppe kann nicht geÃ¤ndert werden. Aktuelle Benutzer sind der Gruppe zugeordnet.
grouptype.error.accessgroup.customers=Die Zugriffsgruppe kann nicht geÃ¤ndert werden. Bestehenden Kunden sind der Gruppe zugeordnet.
grouptype.error.accessgroup.up=Die Zugriffsgruppe kann nicht geÃ¤ndert werden. Bestehende Verwendungszweck Punkte sind der Gruppe zugeordnet.
grouptype.error.accessgroup.pricing=Die Zugriffsgruppe kann nicht geÃ¤ndert werden. Es gibt vorhandene Preisstrukturen in der Gruppe.
grouptype.error.accessgroup.aux=Die Zugriffsgruppe kann nicht geÃ¤ndert werden. Bestehende aux LadeplÃ¤ne sind der Gruppe zugeordnet.
grouptype.error.accessgroup.stores=Die Zugriffsgruppe kann nicht geÃ¤ndert werden. Bestehende GerÃ¤tlager sind der Gruppe zugeordnet.
grouptype.error.name.duplicate=Der Name der Gruppenarten ist bereits im Verwendung. WÃ¤hlen Sie einen anderen Namen.
grouptype.error.cannot.change.required=Die "Pflichtfeld" Einstellung kann nicht geÃ¤ndert werden, die Gruppe wird bereits verwendet.
grouptype.error.select.add.feature=WÃ¤hlen Sie ein Merkmal um es hinzuzufÃ¼gen.
grouptype.error.select.remove.feature=WÃ¤hlen Sie ein Merkmal um es zulÃ¶schen.
grouptype.error.cannot.set.feature.for.group=Diese Funktion kann fÃ¼r diese Gruppenarten nicht festgelegt werden.
grouptype.error.cannot.remove.feature=Das Merkmal kann nicht entfernt werden, die Gruppe wird bereits verwendet.
grouptype.error.save.feature=Die Merkmale kÃ¶nnen nicht gespeichert werden.
grouptype.error.feature.not.multi.instance=Dieses Merkmal kann nur einmal vergeben werden, es ist bereits einer anderen Gruppe zugeordnet.
grouptype.accessgroup.help=Die Zugriffsgruppenart bestimmt, welche Gruppenart den Zugriff der Benutzer auf die Webseite kontrolliert. Wird einmal eingestellt und kann nicht verÃ¤ndert werden.
grouptype.locationgroup.help=Die Standortgruppe bestimmt, welche Gruppenart die Standortdaten kontrolliert. Wird einmal eingestellt und kann nicht verÃ¤ndert werden.

### Group Hierarchy ###
grouphierarchies.header=Gruppenhierarchien
grouphierarchies.title=Aktuelle Gruppenhierarchien
grouphierarchy.title=Gruppenhierarchie
grouphierarchy.field.id=Gruppenhierarchie-ID
grouphierarchy.field.name=Name
grouphierarchy.field.name.help= Geben Sie den Namen dieser Ebene in der Gruppenhierarchie an.
grouphierarchy.field.description=Beschreibung
grouphierarchy.field.active=Aktive
grouphierarchy.field.parent=Ãbergeordnete Hierarchie
grouphierarchy.field.parent.none=Keine
grouphierarchy.field.level=Stufe
grouphierarchy.delete.confirm=Sind Sie sicher, dass Sie diese Gruppenhierarchie lÃ¶schen mÃ¶chten?
grouphierarchy.deleted=Die Gruppenhierarchie wurde erfolgreich gelÃ¶scht.
grouphierarchy.error.delete=Die Gruppenhierarchie kann nicht gelÃ¶scht werden.
grouphierarchy.error.delete.linked=Die Gruppenhierarchie kann nicht gelÃ¶scht werden, da sie verwendet wird.
grouphierarchy.error.save=Die Gruppenhierarchie kann nicht gespeichert werden.
grouphierarchy.error.update=Die Gruppenhierarchie kann nicht aktualisiert werden, da sie verwendet wird.
grouptype.current=Aktuelle Gruppenarten
grouphierarchy.title.add=Gruppenart hinzufÃ¼gen
grouphierarchy.title.update=Gruppenhierarchie aktualisieren
grouphierarchy.error.unknown=Unbekannte Gruppenhierarchie.
grouphierarchy.error.access.root=Die erste Stufe der Hierarchiedaten des Zugangsgruppentypes kann nicht geÃ¤ndert werden.

### Usage Point Groups ###
usagepointgroups.header=Einsatzortsgruppen
usagepointgroups.title=Aktuelle Einsatzortsgruppen
usagepointgroups.instructions=WÃ¤hlen Sie eine Gruppenart:
usagepointgroup.title=Einsatzortsgruppe
usagepointgroup.noselection.grouptype=Keine aktuellen Gruppentyp ausgewÃ¤hlt.
usagepointgroup.field.id=Einsatzortsgruppen-ID
usagepointgroup.field.name=Name
usagepointgroup.field.hierarchy=Hierarchie
usagepointgroup.help.grouptype=WÃ¤hlen Sie eine Gruppenart zur Ansicht der entsprechenden Einsatzortsgruppen. Nur Gruppenarten mit Gruppenhierarchien sind hier sichtbar.
usagepointgroup.delete.ask=Sind Sie sicher, dass Sie die {0} Einsatzortsgruppe lÃ¶schen mÃ¶chten?
usagepointgroups.help=Klicken Sie auf eines der Symbole zum HinzufÃ¼gen, Bearbeiten oder LÃ¶schen der Daten. 
usagepointgroup.title.add=Einsatzortsgruppen hinzufÃ¼gen
usagepointgroup.title.update=Einsatzortsgruppen aktualisieren
usagepointgroup.error.entityid=Es ist nicht mÃ¶glich die Gruppen Instanz zu lÃ¶schen 

###Old Usage Point Group Page
usagepointgroup.field.description=Beschreibung
usagepointgroup.field.parent=Ãbergeordneter Name
usagepointgroup.field.active=Aktive
usagepointgroup.field.status=Status
usagepointgroup.field.name.help=Geben Sie einen Namen fÃ¼r die Gruppe ein - Dieser wird benutzt um sie zu identifizieren, wenn ein Einsatzort der Gruppe hinzugefÃ¼gt wird.
usagepointgroup.field.description.help=Geben Sie eine Beschreibung der Gruppe ein (optional).
usagepointgroup.field.parent.help=Wenn die Gruppe Teil einer grÃ¶Ãeren Gruppe ist, die Ã¼bergeordnete Gruppe hier bestimmen
usagepointgroup.field.status.help=Aktivieren oder deaktivieren dieser Gruppe

## Groups
group.error.save=Gruppe kann nicht gespeichert werden.
group.error.delete=Gruppe kann nicht gelÃ¶scht werden.
group.error.delete.ap=Gruppe kann nicht gelÃ¶scht werden, es gibt darin noch aktive Einsatzorte.
group.error.entity.save=Die Kontaktinformationen kÃ¶nnen der Gruppe nicht zugeordnet werden.
group.error.threshold.save=Die Schwellwertinformationen kÃ¶nnen der Gruppe nicht zugeordnet werden.
group.error.ndp.schedule.save=Unable to link the NDP information to the group.
group.new.instructions=Geben Sie einen neuen
group.new.for=fÃ¼r
group.current.none=Keine aktuellen Gruppe wurde fÃ¼r Ihren Benutzer festgelegt.
group.delete.ask=Sind Sie sicher, dass Sie die {0} Gruppe lÃ¶schen mÃ¶chten?
groups.error.select.at.minimum= Zumindest die {0} muss ausgewÃ¤hlt werden.

## Usage Point Workspace
usagepointworkspace.meter.saved.usagepoint.deactivation.failed=ZÃ¤hler {0} gespeichert! (Deaktivierung des Einsatzorts fehlgeschlagen!)
usagepointworkspace.meter.saved.usagepoint.deactivated=ZÃ¤hler {0} gespeichert! (Einsatzort deaktiviert!)                                  
usagepointworkspace.meter.saved=Meter {0} saved!
usagepointworkspace.meter.saved.attach.usagepoint=Meter {0} saved! To attach this meter to the usage point below, select installation date and pricing structure, as well as activation.
usagepointworkspace.customer.saved.usagepoint.deactivation.failed=Kunde {0} gespeichert! (Deaktivierung des Einsatzorts fehlgeschlagen!)      
usagepointworkspace.customer.saved.usagepoint.deactivated=Kunde {0} gespeichert! (Einsatzort deaktiviert!)       
usagepointworkspace.customer.saved.usagepoint.failed=Customer {0} saved but unable to update Usage point! Contact Support.
usagepointworkspace.customer.saved.usagepoint.updated=Customer {0} saved and Usage point updated.                                           
usagepointworkspace.customer.saved=Kunden {0} gespeichert!
usagepointworkspace.customer.saved.no.usage.point=Customer {0} saved, no Usage Point to update.
usagepointworkspace.customer.unassigned.usagepoint.deactivation.failed=Kunden {0} ist nicht mehr dem Einsatzort {1} zugeordnet (Deaktivierung des Einsatzorts fehlgeschlagen!)
usagepointworkspace.customer.unassigned.usagepoint.deactivated=Kunden {0} ist nicht mehr dem Einsatzort {1} zugeordnet (Einsatzort deaktiviert!)
usagepointworkspace.customer.assign.error.already.assigned=Kunde ist bereits dem Einsatzort {0} zugeordnet
usagepointworkspace.customer.assign.error=Kunden {0} ist NICHT dem Einsatzort zugeordnet {1} (Deaktivierung des Einsatzorts fehlgeschlagen!)
usagepointworkspace.customer.assigned.usagepoint.deactivated=Customer {0} is now assigned to usage point {1} (Usage Point not active)
usagepointworkspace.customer.assigned=Kunde {0} ist nun dem Einsatzort {1} zugeordnet
usagepointworkspace.error.meter.not.found=ZÃ¤hler nicht gefunden
usagepointworkspace.error.meter.already.assigned= ZÃ¤hler {0} ist bereits dem Einsatzort {1} zugeordnet
usagepointworkspace.error.meter.unable.to.unassign=Zuordnung des aktuellen ZÃ¤hlers kann nicht aufgehoben werden
usagepointworkspace.error.meter.installdate.before.previous= Neues Installationsdatum {0} kann nicht vor dem Installationsdatum des vorherigen ZÃ¤hlers {1} sein.
usagepointworkspace.error.meter.installdate.before.last.remove=New install date {0} cannot be BEFORE previous meter's removal date {1}.
usagepointworkspace.error.meter.installdate.before.last.reading= Neuzuweisung des Einsatzortes ist nicht mÃ¶glich - neues Installationsdatum ist vor dem letzten Auslesetag des aktuellen ZÃ¤hlers.
usagepointworkspace.error.meter.installdate.before.last.register.reading=Unable to re-assign usage point - new installation date is equal or before last register reading date of current meter.
usagepointworkspace.error.meter.removedate.before.last.reading=Unable to remove meter from usage point now - last reading date of current meter is greater.
usagepointworkspace.error.meter.removedate.before.last.register.reading=Unable to remove meter from usage point now - last register reading date of current meter is greater. 
usagepointworkspace.meter.assigned=ZÃ¤hler {0} ist nun dem Einsatzort {1} zugeordnet
usagepointworkspace.meter.assigned.usagepoint.deactivated=Meter {0} is now assigned to usage point {1} (Usage Point is not active)
usagepointworkspace.meter.removed=Meter {0} has been removed from usage point {1}
usagepointworkspace.meter.add.usagepoint.to.join.customer=HinzufÃ¼gen Einsatzort (unten) zum miteinander Verbinden von ZÃ¤hler {0} und Kunde {1}
usagepointworkspace.meter.add.usagepoint.to.join=HinzufÃ¼gen Einsatzort (unten) zum Verbinden dieses ZÃ¤hlers zu einem Einsatzort
usagepointworkspace.assign.activate.usage.point.question= Einsatzort ist nicht aktiv. MÃ¶chten Sie es jetzt aktivieren?
usagepointworkspace.assign.usage.point.activated= Einsatzort aktiviert.
usagepointworkspace.save.usage.point.inactive=Usage Point is not active.
usagepointworkspace.meter.customer.assigned=Meter {0}, Customer {2} are now assigned to usage point {1}

### Usage Point ###
usagepoint.groups.title=Einsatzortgruppen
usagepoint.info.title=Einsatzort Informationen
usagepoint.title=Einsatzort
usagepoint.add.new=HinzufÃ¼gen Einsatzort
usagepoint.show.info=Einsatzort Information anzeigen
usagepoint.showing.info=Zeige Einsatzort Information
usagepoint.field.active.help=Ist dieser Einsatzort aktiv? Diese Option ist deaktiviert, bis ein aktiver ZÃ¤hler und Kunden dem Einsatzort verbunden werden.
usagepoint.field.activated_date.help=Setzen Sie das Datum, dieser Einsatzort wurde aktiviert. Dies kann nur einmal festgelegt werden. Dieses Feld ist fÃ¼r die Aktivierung erforderlich. Obwohl der Datensatz auch ohne gespeichert werden kann, kann der Einsatzort nicht aktiviert werden, wenn sie nicht korrekt gefÃ¼llt werden.
usagepoint.field.active=Aktive
usagepoint.field.activated_date=Datum aktiviert
usagepoint.field.meter.installation_date=ZÃ¤hler Installations Datum / Uhrzeit
usagepoint.field.meter.installation_date.meter.required=Ein ZÃ¤hler muss vor dem Einstellen des Installationsdatums installiert werden.
usagepoint.field.meter.installation_date.help=Datum und Zeit wenn der ZÃ¤hler am Einsatzort installiert wurde. Dies kann nur einmal pro ZÃ¤hler eingestellt werden. Dieses Feld ist erforderlich und ein ZÃ¤hler muss vor der Einstellung des Installationsdatums zugeordnet werden.
usagepoint.field.name.help=Geben Sie den Einsatzort an. Dies ist ein Pflichtfeld - der Datensatz kann nicht ohne gespeichert werden.
usagepoint.field.name=Einsatzort Name
usagepoint.field.pricingstructure.help=WÃ¤hlen Sie die Preisstruktur. Dies ist ein Pflichtfeld - der Datensatz kann nicht ohne gespeichert werden.
usagepoint.field.lastmdcconnectcontrol=Letzte MDC Verbindungskontrolle
usagepoint.field.group.help=HinzufÃ¼gen des Einsatzortes zu einer Gruppe
usagepoint.field.group=Einsatzortgruppen
usagepoint.required.text=* \=Pflichtfeld
usagepoint.required.activation.text=** \=Pflichtfeld zur Aktivierung - Ein aktiver ZÃ¤hler und Kunde sind fÃ¼r die Aktivierung erforderlich
usagepoint.name.required=Einsatzort Name ist erforderlich
usagepoint.pricingstructure.required=Preisstruktur ist erforderlich
usagepoint.pricingstructure.change.illegal=Pricing Structure cannot be changed now. No meter is attached. Have reset the selection.
usagepoint.save.error=Speichern vom Einsatzort fehlgeschlagen.
usagepoint.save.errors=Einsatzort nicht gespeichert. Korrigieren Sie die angezeigten Fehler.
usagepoint.save.errors.meter.required=Speichern vom Einsatzort fehlgeschlagen. Eine zugeordneter ZÃ¤hler ist erforderlich
usagepoint.saved=Einsatzort {0} gespeichert.
usagepoint.changes.cleared=Ãnderungen wurden gelÃ¶scht.
usagepoint.no.meter=ZÃ¤hler muss gespeichert werden, bevor ein Einsatzort hinzugefÃ¼gt wird.
usagepoint.txn.history=Transaktion Verlauf
usagepoint.txn.history.description=FrÃ¼here Kunden Transaktionen fÃ¼r diesen Einsatzort
usagepoint.txn.meterreadings=ZÃ¤hler Ablesungen
usagepoint.history=Einsatzort Verlauf
usagepoint.history.description=FrÃ¼here Ãnderungen dieses Einsatzortes (Ãnderungen markiert)
usagepoint.reports=Reporte fÃ¼r Einsatzort
usagepoint.recharge.history=ZÃ¤hler Aufladeverlauf des Einsatzortes
usagepoint.retailers=Vertriebsstellen in der NÃ¤he
usagepoint.reports.general=Allgemeine Reporte fÃ¼r Einsatzorte
usagepoint.meter.reports=Reporte fÃ¼r Einsatzort
usagepoint.coords=HinzufÃ¼gen von Breite und LÃ¤nge.-Koordinaten des Einsatzortes, um Vertriebsstelle in der NÃ¤he zu sehen.
usagepoint.txn.reference=Referenz
usagepoint.txn.receipt=Belegnummer
usagepoint.txn.date=Datum
usagepoint.txn.meter=ZÃ¤hler
usagepoint.txn.customer=Kunden
usagepoint.txn.type=Art
usagepoint.txn.client=Auftraggeber
usagepoint.txn.term=Laufzeit
usagepoint.txn.ref=Referenz
usagepoint.txn.revref=Reversal Ref
usagepoint.txn.isreversed=Reversed
usagepoint.txn.amt=Betrag
usagepoint.hist.user=Benutzer
usagepoint.hist.serial=Fortsetzung
usagepoint.hist.date=Datum
usagepoint.hist.meter=ZÃ¤hler
usagepoint.hist.customer=Kunden
usagepoint.hist.datemod=Datum geÃ¤ndert
usagepoint.hist.byuser=von Benutzer
usagepoint.hist.action=Aktion
usagepoint.hist.status=Status
usagepoint.hist.name=Name
usagepoint.hist.custagree=Kundenvereinbarung
usagepoint.hist.service=Servicestandort
usagepoint.recharge.title=Einsatzort nachladen
usagepoint.recharge.date=Datum
usagepoint.recharge.currency=R
usagepoint.recharge.kwh=kWh
usagepoint.history.filter=Filter
usagepoint.txn.filter=Filter
usagepoint.group.required= Gruppe erforderlich
usagepoint.onegroup.required=Mindestens eine Gruppe muss ausgewÃ¤hlt werden
usagepoint.group.error.delete=Die Gruppe des Einsatzortes kann nicht gelÃ¶scht werden.
usagepoint.group.error.save=Die Gruppe des Einsatzortes kann nicht gespeichert werden.
usagepoint.group.no.value=Keine Werte definiert
usagepoint.calculate.tariff=Tarif berechnen
usagepoint.calculate.tariff.error=Bei der Berechnung des Tarifes ist ein Fehler aufgetreten. 
usagepoint.calculate.tariff.connection.error=Keine Antwort vom Service.
usagepoint.calculate.tariff.ok=Tarif erfolgreich berechnet
usagepoint.recharge.chart.title=Einsatzort Aufladungen
usagepoint.recharge.chart.subtitle=Auflade BetrÃ¤ge
usagepoint.recharge.chart.xtitle=Datum Zeit
usagepoint.recharge.chart.ytitle=Kosten
usagepoint.recharge.chart.price=Preis
usagepoint.recharge.chart.purchaseprice=Kaufpreis
usagepoint.error.meterandcustomer.required= Ein aktiver ZÃ¤hler und Kunden sind fÃ¼r die Aktivierung erforderlich
usagepoint.meter.installed.at=ZÃ¤hler {0} wurde am {1} um {2} installiert
usagepoint.installation.date.required=ZÃ¤hler Installationsdatum/Zeit ist erforderlich.
usagepoint.name.instructions=Search by Usage Point Name
usagepoint.partial.search=No exactly matching usage point for {0}. Doing advanced search...
usagepoint.fetch=Fetch Usage Point
usagepoint.fetch.help=Fetch an existing usage point.
usagepoint.assigned=Cannot fetch usage point {0} for the meter on this page, already has a different meter assigned to it.
usagepoint.name.instr=Enter Usage Point name
usagepoint.find=Find Usage Point
usagepoint.fetch.duplicate=Usage Point {0} already on page.
usagepoint.install.date.required=The date and time when the above meter was installed at this usage point {0}.
usagepoint.new.pricingstructure.required=Selected Usage point pricing structure {1} incompatible with Meter {0}.
usagepoint.error.new.installdate.before.removal=Installation date cannot be BEFORE last removal date on usage point: {0}
usagepoint.error.new.installdate.before.removaldate.meter=Installation date cannot be BEFORE last removal date of the meter: {0}
usagepoint.saved.linked.meter=Linked to meter {0}.
usagepoint.saved.linked.customer=Linked to customer {0}.
moxiechart.abbrev.month.categories="Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"

### Group Entity ###
groupentity.header=Gruppeninformationen
groupentity.title=Kontaktinformationen
groupentity.contact.title=Kontakt
groupentity.field.contact.name=Kontaktname
groupentity.field.contact.number=Kontaktnummer
groupentity.field.contact.email=Kontakt Email
groupentity.field.contact.address=Contact Address
groupentity.field.contact.taxref=Contact Tax Reference
groupentity.error.save=Gruppeninformationen kÃ¶nnen nicht gespeichert werden.

### Group Thresholds ###
groupthreshold.title=Kundenkonto Grenzwert Informationen
groupthreshold.meter.disconnect.text=Grenzwert Unterbrechen
groupthreshold.meter.disconnect.help=Grenzwert, unter dem der Kunde wird unterbrochen wird.
groupthreshold.emergency.credit.text=Grenzwert Notfallkredit
groupthreshold.emergency.credit.help=Grenzwert, unter dem das Guthaben zu einer Unterbrechung fÃ¼hrt, ermÃ¶glicht eine Anforderung von der MDC, wenn unterstÃ¼tzt.
groupthreshold.meter.reconnect.text=Grenzwert Wiedereinschalten
groupthreshold.meter.reconnect.help=Grenzwert, ab dem der Kunde wieder verbunden wird.
groupthreshold.low.balance.text=Grenzwert Niedriger-Kontostand Warnung
groupthreshold.low.balance.help=Grenzwert, um Kunden auf ein niedrigen Kontostand auf ihrem Konto zu alarmieren.
groupthreshold.global.source.label=Globale Einstellungen
groupthreshold.parent.source.label=Ãbergeordnete Einstellungen
groupthreshold.children.change.alert=Niedrigere Hierarchien ohne oder mit gleichen Grenzwerten werden auch mit diesen Ãnderungen aktualisiert werden. Weiter?
groupthreshold.null.thresholds.alert=Grenzwerte die leer gelassen wurden werden deaktiviert. Weiter?
groupthreshold.error.save.thresholds=Die Grenzwertinformationen kÃ¶nnen nicht gespeichert werden.
groupthreshold.revert.parent.global=Sind Sie sicher, dass Sie lÃ¶schen? Dieser Knoten wird nun {0} Grenzwerteinstellungen erben.
groupthreshold.error.disconnect.greater.emergency.credit=Unterbrechen muss kleiner oder gleich Notfallkredit sein
groupthreshold.error.disconnect.greater.reconnect=Unterbrechen muss kleiner oder gleich Wiedereinschalten sein
groupthreshold.error.emergency.credit.greater.low.balance=Emergency Credit must be smaller than or equal to Low Balance

### Global Non-Disconnect Periods ###
global.ndp.tab.heading=Global NDP
global.ndp.heading=Global Non Disconnect Periods 
global.ndp.none=There is no Global NDP Schedule, please press Create Button.
global.ndp.schedule.new.added=The Global NDP schedule has been created.
global.ndp.schedule.activation.saved=Global NDP schedule saved.

### Group Non-Disconnect Periods ###
ndp.active.instruction=An NDP schedule can be created and worked on, but will only be applied when it is active. The schedule can only be activated when at least one NDP day with times was entered. Either a season with a day or a special day.
ndp.schedule.title=NDP Schedule
ndp.schedule.active=Active
ndp.schedule.active.help=Is this schedule active? Check the box to activate this schedule - can only be done when at least one NDP day with times was entered.
ndp.schedule.delete.button=Delete Schedule
ndp.disclosurePanel.title=Non-Disconnect Periods
ndp.seasons.title=Seasons 
ndp.season.day.title=Season Days
ndp.season.day.description=Enter a start and end date for the season, followed by the NDP times per day of week. 
ndp.assign.season.start=Start date
ndp.assign.season.start.help=Enter the date the season starts
ndp.assign.season.end=End date
ndp.assign.season.end.help=Enter the date the season ends
ndp.per.day.title=NDP per Day
ndp.days.of.week=Day of Week
ndp.assign.dayperiod.start=Start time  
ndp.assign.dayperiod.start.help=The time that the NDP starts     
ndp.assign.dayperiod.start.hour=Hour    
ndp.assign.dayperiod.start.minute=Minute    
ndp.assign.dayperiod.end=End time    
ndp.assign.dayperiod.end.help=The time that the NDP ends    
ndp.assign.dayperiod.end.hour=Hour    
ndp.assign.dayperiod.end.minute=Minute    
ndp.assign.dayperiod.saved=NDP Time has been saved.
ndp.assign.dayperiod.deleted=NDP Time has been deleted.
ndp.assign.dayperiod.error.end.before.start=End time cannot be before start time
ndp.assign.dayperiod.error.time.already.assigned=The time selected has already been assigned.  
ndp.assign.dayperiod.nulls=All start & end time values must be entered. Cannot be blank.
ndp.days.title=NDP Day Times
ndp.add.season.button=Add Season
ndp.assign.season.error.end.before.start=End cannot be before Start
ndp.assign.season.error.date.already.assigned=These dates overlap with another season. 
ndp.assign.season.error.time.already.assigned=The time selected has already been assigned.
ndp.weekdays=Monday,Tuesday,Wednesday,Thursday,Friday,Saturday,Sunday  
ndp.children.change.alert=Lower hierarchies with no NDP will also be updated with these changes. Continue?
ndp.error.save.schedule=Unable to save the NDP Schedule information.
ndp.abort.save=You elected NOT to save this NDP schedule.
ndp.error.save.season=Unable to save the NDP Season.
ndp.error.save.day=Unable to save the NDP Day times.
ndp.revert.parent.global=Are you sure to delete? This node will now inherit {0} NDP Settings.
ndp.children.delete.alert=Lower hierarchies with the same NDP Schedule will also be reverted to {0} NDP Setting. Continue?
ndp.error.delete.day.profiles=Unable to delete the NDP Day Profiles.
ndp.error.delete.season=Unable to delete an NDP Season.
ndp.error.delete.special.days=Unable to delete the NDP Special Days.
ndp.error.delete.schedule=Unable to delete the NDP Schedule.
ndp.new.season.button=Add New Season
ndp.new.special.day.button=Add Special Day
ndp.special.day.column.heading=Day
ndp.no.change.continue=Nothing has changed. Continue?
ndp.day.panel.changed=Changes were made to this Season. Abandon those changes and continue with cancel? 
ndp.schedule.activation.no.change=No change in active status.
ndp.schedule.new.added=A new NDP Schedule has been added to this group & relevant subgroups.
ndp.schedule.activation.saved=NDP schedule saved.
ndp.schedule.deleted=The NDP schedule was deleted from this group & relevant subgroups.
ndp.season.saved=NDP Season saved.
ndp.day.profile.confirm.delete=Confirm delete of day profile for {0}?
ndp.day.profile.confirm.delete.and.deactivate=Deleting day Profile for {0} will cause the NDP schedule to de-activate upon updating the season, as it is the only NDP time for this schedule. Continue?
ndp.season.confirm.delete=Confirm delete of Season {0}
ndp.season.deleted=NDP Season deleted.
ndp.season.confirm.delete.and.deactivate=Deleting Season {0} will cause the NDP schedule to de-activate, as it contains the only NDP time/s for this schedule. Continue?
ndp.special.day.confirm.delete=Confirm delete of Special Day {0}
ndp.special.day.confirm.delete.and.deactivate=Deleting Special Day {0} will cause the NDP schedule to de-activate, as it contains the only NDP time/s for this schedule. Continue?
ndp.special.day.deleted=Special Day deleted.
ndp.special.day.title=Special Days
ndp.special.day.time.title= Special Day NDP Times
ndp.special.day.description=Enter the day & month of the Special Day, followed by the NDP times for that day.
ndp.assign.special.day=Special Day Date
ndp.assign.special.day.help=Enter the day and Month of the Special Day.
ndp.assign.special.day.duplicate=Duplicate Special Day date - abandon here and select that day from table.
ndp.special.day.panel.changed=Changes were made to this Special Day. Abandon those changes and continue with cancel? 
ndp.special.day.times.confirm.delete.and.deactivate=Deleting NDP times for {0} will cause the NDP schedule to de-activate upon updating the special day, as it is the only NDP time for this schedule. Continue?
ndp.special.day.saved=Special Day NDP saved.
ndp.error.save.special.day=Unable to save the Special Day NDP.
ndp.global.none.found=There is no Global NDP schedule. Please go and create one in the Configuration Section. 
ndp.inherited.global.none=There is no Global NDP schedule or it is not yet active. No Global NDP periods can be inherited!

### Customer Agreement ###
customeragreement.title=Kundenvereinbarung

### Pricing Structures ###
pricingstructures.header=Preisstrukturen
pricingstructures.title=Aktuelle Preisstrukturen
pricingstructure.title=Preisstruktur
pricingstructure.title.new=Neue Preisstruktur
pricingstructure.title.edit=Bearbeiten Preisstruktur
pricingstructure.field.name=Name
pricingstructure.field.description=Beschreibung
pricingstructure.field.status=Status
pricingstructure.field.active=Aktive
pricingstructure.field.name.help=Name dieser Preisstruktur muss eindeutig sein
pricingstructure.field.description.help=Eine Beschreibung dieser Preisstruktur
pricingstructure.field.active.help=Whether the pricing structure is active or not. A pricing structure can only be activated if it has a current tariff.
pricingstructure.field.type=Art
pricingstructure.field.startdate=Start Datum
pricingstructure.field.tariffs=# Tarife
pricingstructure.error.save=Preisstruktur kann nicht gespeichert werden.
pricingstructure.field.serviceresource.help=Wenn es keine Tarife gibt, kÃ¶nnen sie die Service Ressourcen fÃ¼r die Preisstruktur wÃ¤hlen.
pricingstructure.field.metertype.help=Wenn es keine Tarife gibt, kÃ¶nnen sie die ZÃ¤hlerart fÃ¼r die Preisstruktur wÃ¤hlen.
pricingstructure.field.paymentmode.help=If there are no tariffs, kÃ¶nnen sie Zahlungsweise fÃ¼r die Preisstruktur Ã¤ndern.
pricingstructure.tariffs.prt.none=Es gibt keine verfÃ¼gbaren Tarifarten fÃ¼r die aktuellen Preisstrukturen mit dieser Service Ressourcen, ZÃ¤hlerart und Zahlungsweise Kombination.
pricingstructure.tariffs.ui.none=Keine verfÃ¼gbare Tarif Eingabe zum Speichern.
pricingstructure.error.tariff.load=Fehler beim Laden der Daten des Tarifes. Tariffelder und entsprechende Werte kÃ¶nnen nicht angezeigt werden.
pricingstructure.name.duplicate=Doppelter Name {0} fÃ¼r eine Preisstruktur. Geben Sie einen eindeutigen Namen ein.
pricingstructure.error.active=Pricing Structure has no Tariff. Cannot be active.
pricingstructure.error.deactivate=Pricing Structure is in use. Cannot deactivate.

### Tariffs ###
tariffs.header=Tarife
tariffs.title=Aktuelle Tarife
tariff.title=Tarif
tariff.title.add=HinzufÃ¼gen neuer Tarif
tariff.title.edit=Ãndern Tarif
tariff.title.view=Anzeigen Tarif
tariff.field.name=Name
tariff.field.name.help=Name dieses Tarifes muss eindeutig sein
tariff.field.description=Beschreibung
tariff.field.description.help=Eine Beschreibung dieses Tarifes
tariff.field.startdate=Startdatum
tariff.field.startdate.help=Das Datum dieses Tarifes an dem er aktiviert wird. Keine anderen Tarife kÃ¶nnen an diesem Tag beginnen und es muss ein Datum in der Zukunft sein.
tariff.title.type=Tarifart Informationen
tariff.field.type=Art
tariff.field.unitprice=Preis je Einheit
tariff.field.tax=Steuer Prozent
tariff.field.tax.help=Prozentsatz wie 14%
tariff.field.free.units.descrip=Monthly Free Units Description
tariff.field.free.units=Monthly Free Units
tariff.field.free.units.help=Units that are issued free of charge.
tariff.field.groupthreshold=Gruppe Grenzwert
tariff.field.groupthreshold.help=Der Grenzwert eines Monats in kWh, nachdem der Grenzwertpreis fÃ¼r die Gruppe wirsam wird.
tariff.field.thresholdprice=Grenzwertpreis
tariff.field.thresholdprice.help=Preis pro kWh (wenn Grenzwert Ã¼berschritten wurde)
tariff.field.price=Preis
tariff.field.baseprice=Grundpreis
tariff.field.threshold=Grenzwert
tariff.field.threshold.help=Preis pro kWh
tariff.field.step1=Schritt 1
tariff.field.step2=Schritt 2
tariff.field.step3=Schritt 3
tariff.error.numeric.value=Value must be numeric
tariff.error.save=Tarif kann nicht gespeichert werden.
tariff.error.save.duplicate=Tarif kann nicht gespeichert werden, ein anderer tarif mit dem selben Namen existiert bereits.
tariff.error.load=Tarifdaten kÃ¶nnen nicht geladen werden.
tariff.field.block=BlÃ¶cke
tariff.field.block.single=Block
tariff.error.freeunits.positive=Must be a positive value.
tariff.error.freeunits.decimal.limit=Units may only have 1 decimal
tariff.blocks.error=BlÃ¶cke benÃ¶tigen einen gÃ¼ltigen numerischen Einheiten Preis und Grenzwerte.
tariff.blocks.error.incomplete=BlÃ¶cke benÃ¶tigen einen Einheiten Preis fÃ¼r einen Grenzwert.
tariff.blocks.error.last=Nur der letzte Block sollte keinen Grenzwert haben.
tariff.blocks.error.last.none=Der letzte Block sollte keinen Grenzwert haben.
tariff.blocks.error.none=BlÃ¶cke sind erforderlich.
tariff.blocks.error.increasing.thresholds=Thresholds for a block should be greater than zero and greater than the previous block's threshold. 
tariff.tou.thinsmart.season=Saison
tariff.tou.thinsmart.period=Zeitraum
tariff.tou.thinsmart.readingtype=Ableseart
tariff.tou.thinsmart.rate=Tarif
tariff.tou.thinsmart.chargeunits=Einheiten
tariff.tou.thinsmart.specialday=Feiertage
tarif.adv.settings.header=Erweiterte Einstellungen
tariff.field.pricesymbol=WÃ¤hrungssymbol
tariff.field.pricesymbol.help=Das WÃ¤hrungssymbol, das fÃ¼r den Preis verwendet wird.
tariff.field.unitsymbol=Einheit Symbol
tariff.field.unitsymbol.help=Das WÃ¤hrungseinheiten Symbol, das fÃ¼r den Preis verwendet wird.
tariff.field.amountrounding=BetrÃ¤ge Rundungsmodus
tariff.field.amountrounding.help=Der Rundungsmodus fÃ¼r BetrÃ¤ge.
tariff.field.amountprecision=BetrÃ¤ge Genauigkeit
tariff.field.amountprecision.help=Die Genauigkeit fÃ¼r BetrÃ¤ge.
tariff.field.unitsrounding=Einheit Runden
tariff.field.unitsrounding.help=Der Rundungsmodus fÃ¼r die Einheiten.
tariff.field.unitsprecision=Einheit Genauigkeit
tariff.field.unitsprecision.help=Die Genauigkeit fÃ¼r die Einheit.
tariff.field.taxrounding=Steuern Runden
tariff.field.taxrounding.help=Der Rundungsmodus fÃ¼r die Steuern.
tariff.field.taxprecision=Steuern Genauigkeit
tariff.field.taxprecision.help=Die Genauigkeit fÃ¼r die Steuern.
tariff.error.field.pricesymbol=WÃ¤hrungssymbol ist erforderlich.
tariff.error.field.unitsymbol=Einheit Symbol ist erforderlich.
tariff.error.field.amountrounding=Rundungsmodus ist erforderlich.
tariff.error.field.unitsrounding=Rundungsmodus ist erforderlich.
tariff.error.field.taxrounding=Rundungsmodus ist erforderlich.
tariff.error.field.amountprecision=Genauigkeit muss ein positiver Wert sein.
tariff.error.field.unitsprecision=Genauigkeit muss ein positiver Wert sein.
tariff.error.field.taxprecision=Genauigkeit muss ein positiver Wert sein.
tariff.readonly=Tarif hat begonnen und ist schreibgeschÃ¼tzt.

### Tou Calendar ###
calendar.settings.header=Kalendereinstellungen
calendar.settings.title=Kalendereinstellungen
calendar.season.title=Saison erstellen
calendar.season.current.title=Aktuelle Saisonen
calendar.season.description=HinzufÃ¼gen oder aktualisieren von Saisonen.
calendar.season.field.name=Name
calendar.season.field.active=Aktive
calendar.season.add=Saison hinzufÃ¼gen
calendar.season.update=Saison Ã¤ndern
calendar.season.field.name.help=Geben Sie einen Namen fÃ¼r diese Saison an. Dies ist ein Pflichtfeld.
calendar.season.field.active.help=Der aktuelle AktivitÃ¤tsstatus fÃ¼r dieses Objekt
season.error.update=Die Saison konnte nicht aktualisiert werden.
season.error.save=Die Saison konnte nicht gespeichert werden.
season.error.delete=Die Saison wird verwendet und kann nicht gelÃ¶scht werden. 
calendar.season.deleted={0} Saison wurde erfolgreich gelÃ¶scht.
calendar.period.deleted={0} Zeitraum wurde erfolgreich gelÃ¶scht.

calendar.period.title=ZeitrÃ¤ume erstellen
calendar.period.current.title=Aktuelle ZeitrÃ¤ume
calendar.period.description=HinzufÃ¼gen und Aktualisieren von ZeitrÃ¤umen (zB Spitzenzeiten, Nachtstrom, Standard).
calendar.period.field.name=Name
calendar.period.field.code=Code
calendar.period.field.active=Aktive
calendar.period.add=Zeitraum hinzufÃ¼gen
calendar.period.update=Zeitraum Ã¤ndern
calendar.period.field.name.help=Geben Sie einen Namen fÃ¼r diesen Zeitraum an. Dies ist ein Pflichtfeld.
calendar.period.field.code.help=Geben Sie einen Code fÃ¼r diesen Zeitraum an. Dies ist eine einzigartige KÃ¼rzel Code fÃ¼r den Zeitraum. Dies ist ein Pflichtfeld.
calendar.period.field.active.help=Der aktuelle AktivitÃ¤tsstatus fÃ¼r dieses Objekt
period.error.save=Der Zeitraum konnte nicht gespeichert werden.
period.error.delete=Der Zeitraum wird verwendet und kann nicht gelÃ¶scht werden. 

calendars.title=Kalender
calendars.heading=Kalender erstellen
calendars.description= Aktuelle Kalender sind angefÃ¼hrt, hinzuzufÃ¼gen oder bearbeiten unter Verwendung der Formulare unterhalb 
calendar.field.name=Kalender Name
calendar.field.name.help=Geben Sie einen Namen fÃ¼r diesen Kalender an.
calendar.field.description=Beschreibung
calendar.field.description.help=Geben Sie eine Beschreibung dieses Kalenders an.
calendar.field.active=Aktive
calendar.field.active.help=Der aktuelle AktivitÃ¤tszustand des Kalenders
calendar.title=Kalender
calendar.add=Kalender neu hinzufÃ¼gen
calendar.update=Kalender Ã¤ndern 
calendar.changes.cleared=Kalender Ãnderungen gelÃ¶scht
calendar.save.errors=kalender kann nicht gespeichert werden
calendar.complete=VollstÃ¤ndig
calendar.incomplete=UnvollstÃ¤ndig
calendar.optional=Optional
calendar.duplicate=Doppelter Kalendername. WÃ¤hlen Sie einen eindeutigen Namen.

calendar.assign.season.heading=Saisonen Zeiten zuweisen 
calendar.assign.season.title=Saisonzeiten
calendar.assign.season.description=Geben Sie eine Start- und Enddatum fÃ¼r die gewÃ¤hlten Saison an. 
calendar.assign.season.form.heading=Tage zuweisen 
calendar.assign.season=Saison
calendar.assign.season.help=Saison wÃ¤hlen
calendar.assign.season.start=Startdatum
calendar.assign.season.start.help=Geben Sie das Datum des Saisonbeginns an
calendar.assign.season.end=Enddatum
calendar.assign.season.end.help=Geben Sie das Datum des Saisonendes an
calendar.assign.season.deleted=Saisonzeiten wurden gelÃ¶scht.
calendar.assign.season.error.end.before.start=Ende kann nicht vor Anfang sein
calendar.assign.season.error.date.already.assigned=Das Datum ist bereits einer Saison zugeordnet
calendar.assign.season.error.select.season=WÃ¤hlen Sie eine Saison aus der Liste

calendar.assign.period.title=Tagesprofil 
calendar.assign.period.description=Tageszeiten einem Zeitraum zuweisen
calendar.assign.period=Zeitraum 
calendar.assign.period.help= WÃ¤hlen Sie eine Periode 
calendar.assign.period.start=Startzeit 
calendar.assign.period.start.help=Die Zeit, mit der der Zeitraum beginnt    
calendar.assign.period.start.hour=Stunde    
calendar.assign.period.start.minute=Minute    
calendar.assign.period.end=Endzeit  
calendar.assign.period.end.hour=Stunde    
calendar.assign.period.end.minute=Minute    
calendar.assign.period.saved=Der Zeitraum wurde gespeichert.
calendar.assign.period.cleared=Zeitraum Ãnderungen wurden gelÃ¶scht.
calendar.assign.period.deleted=Zeitraum wurde gelÃ¶scht.
calendar.assign.period.error.end.before.start=Endzeit kann nicht vor dem Startzeitpunkt sein
calendar.assign.period.error.time.already.assigned=Die gewÃ¤hlte Zeit wurde bereits zugewiesen.  
calendar.assign.period.nulls=Alle Start.- und Endwerte mÃ¼ssen eingegeben werden. Darf nicht leer sein. 
    
calendar.dayprofiles.heading=Tagesprofil einrichten 
calendar.dayprofiles.title=Tagesprofile
calendar.dayprofiles.description=Erstellen Sie Tage, die in bestimmte ZeitrÃ¤ume aufgeteilt werden.

calendar.dayprofile.field.name=Profil Name
calendar.dayprofile.field.code=Code
calendar.dayprofile.field.active=Aktive
calendar.dayprofile.field.name.help=Geben Sie einen Namen fÃ¼r dieses Tagesprofil an
calendar.dayprofile.field.code.help=Geben Sie eine KÃ¼rzel Code fÃ¼r dieses Tagesprofil an
calendar.dayprofile.field.active.help=Der aktuelle AktivitÃ¤tszustand dieses Tagesprofils
calendar.dayprofile.error.save=Tagesprofil kann nicht gespeichert werden
calendar.dayprofile.error.first.unassign=Ein Tagesprofil, welches derzeit einer Saison zugeordnet ist, kann nicht gelÃ¶scht werden. Heben Sie zunÃ¤chst die Zuordnung auf und lÃ¶schen sie danach.
calendar.dayprofile.error.special.day.first.unassign=Ein Tagesprofil, welches derzeit einem Feiertag zugeordnet ist, kann nicht gelÃ¶scht werden. LÃ¶schen Sie zunÃ¤chst den Feiertag und danach das Profil.
calendar.dayprofile.deleted=Tagesprofil gelÃ¶scht
calendar.dayprofile.saved=Tagesprofil Ãnderungen gespeichert
calendar.dayprofile.cleared=TagesprofilÃ¤nderungen gelÃ¶scht

calendar.assign.dayprofile.heading= Tagesprofil Kalenderzuweisung
calendar.assign.dayprofile.description=Tagesprofile fÃ¼r jeden Tag der Woche, fÃ¼r jede Saison eines Kalenders, zuweisen.
calendar.assign.dayprofile.cleared=Tagesprofil Kalenderzuweisungs Ãnderungen wurden gelÃ¶scht.
calendar.assign.dayprofile.saved=Tagesprofil Kalenderzuweisungs Ãnderungen wurden gespeichert.
calendar.assign.dayprofile.error.save=Fehler beim Zuweisen von Tagesprofilen

calendar.specialday.heading=Feiertage erstellen
calendar.specialday.title=Feiertage erstellen
calendar.specialday.description=Tagesprofil bestimmten Tagen zuweisen
calendar.specialday.field.name=Feiertagsname
calendar.specialday.field.name.help=Ein eindeutiger Name fÃ¼r den Tag.
calendar.specialday.field.active=Aktive
calendar.specialday.field.active.help=Der aktuelle AktivitÃ¤tszustand dieses Feiertages
calendar.specialday.field.day=Tag
calendar.specialday.field.month=Monat
calendar.specialday.field.dayprofile=Tagesprofil
calendar.specialday.field.dayprofile.help=WÃ¤hlen Sie das Tagesprofil
calendar.specialday.field.dayprofile.error=Das Tagesprofil muss fÃ¼r Feiertage eingegeben werden.
calendar.specialday.add=Feiertag hinzufÃ¼gen
calendar.specialday.update=Feiertag Ã¤ndern
calendar.special.day.deleted= Feiertag gelÃ¶scht.   
calendar.specialday.error.date.already.assigned.to=Diesem Datum wurde bereits ein Feiertag zugewiesen.

calendar.readOnly=Hinweis: Dieser Kalender kann nicht aktualisiert werden, da er bereits von den folgenden Preisstrukturen: {0}

### Aux Charge Schedule ###
auxchargeschedules.header=ZusatzgebÃ¼hrenplan
auxchargeschedules.title=aktuelle ZusatzgebÃ¼hrenplÃ¤ne
auxchargeschedule.title=ZusatzgebÃ¼hrenplan
auxchargeschedule.title.add=ZusatzgebÃ¼hrenplan hinzufÃ¼gen
auxchargeschedule.title.update=ZusatzgebÃ¼hrenplan Ã¤ndern
auxchargeschedule.field.name=Name
auxchargeschedule.field.name.help=Name des GebÃ¼hrenplanes muss eindeutig sein und wird benÃ¶tigt
auxchargeschedule.field.status=Status
auxchargeschedule.field.minamount=Min Betrag
auxchargeschedule.field.minamount.help=Geben Sie den erforderlichen Mindestbetrag fÃ¼r diesen Zeitplan an
auxchargeschedule.field.maxamount=Max Betrag
auxchargeschedule.field.maxamount.help= Geben Sie den erlaubten Maximalbetrag fÃ¼r diesen Zeitplan an 
auxchargeschedule.field.vendportion=Verkaufsanteil
auxchargeschedule.field.vendportion.help=Geben Sie den Prozentsatz der Verkaufs an, der verwendet wird um die GebÃ¼hren zu berechnen wird.
auxchargeschedule.field.currportion=Laufender Anteil
auxchargeschedule.field.currportion.help=Geben Sie den Prozentsatz des Restbetrags, die verwendet werden, um die GebÃ¼hren zu berechnen wird.
auxchargeschedule.field.active=Aktive
auxchargeschedule.field.active.help=Der aktuelle AktivitÃ¤tsstatus fÃ¼r dieses Objekt
auxchargeschedule.error.save=Kann den ZusatzgebÃ¼hrenplan nicht speichern.
auxchargeschedule.error.duplicate=Doppelter Name {0} fÃ¼r einen ZusatzgebÃ¼hernplan. Geben Sie einen eindeutigen Namen an.
auxchargeschedule.nocharge.error= Ein Verkaufsanteil, ein laufender Anteil oder ein Grundbetrag muss definiert werden

### Auxilliary Type ###
auxilliarytypes.header=Zusatzarten
auxilliarytypes.title=Aktuelle Zusatzarten
auxillarytype.title.add=Zusatzarten hinzufÃ¼gen
auxillarytype.title.update=Zusatzarten Ã¤ndern
auxillarytype.title=Zusatzart
auxtype.field.name=Name
auxtype.field.description=Beschreibung
auxtype.field.status=Status
auxtype.field.active=Active
auxtype.field.name.help=Name dieser Zusatzart muss eindeutig sein
auxtype.field.description.help=Beschreibung dieser Zusatzarten
auxtype.field.active.help=Der aktuelle AktivitÃ¤tsstatus fÃ¼r dieses Objekt
auxtype.error.save=Hilfsart kann nicht gespeichert werden
auxtype.error.save.duplicate=Zusatzart kann nicht gespeichert werden, ein weitere Zusatzart mit dem gleichen Namen ist bereits vorhanden.
auxtype.error.update=Zusatzart kann nicht aktualisiert werden
auxtype.error.update.duplicate=Zusatzart kann nicht aktualisiert werden, ein weitere Zusatzart mit dem gleichen Namen ist bereits vorhanden.

### Device Store ###
devicestores.header=GerÃ¤telager
devicestores.title=Aktuelle GerÃ¤telager
devicestore.title.add=GerÃ¤telager hinzufÃ¼gen
devicestore.title.update=GerÃ¤telager aktualisieren
devicestore.title=GerÃ¤telager
devicestore.field.name=Name
devicestore.field.description=Beschreibung
devicestore.field.name.help=Name dieses GerÃ¤telagers (muss eindeutig sein)
deviceStore.name.duplicate=Doppelter Namee {0} fÃ¼r ein GerÃ¤telager. Geben Sie einen eindeutigen Namen an.
devicestore.field.description.help=Beschreibung des GerÃ¤tes Shop
devicestore.field.active=Aktive
devicestore.field.active.help=Der aktuelle AktivitÃ¤tszustand des GerÃ¤telagers
devicestore.location.title=GerÃ¤telager Standort
devicestore.error.save=GerÃ¤telager kann nicht gespeichert werden.
devicestore.error.update=GerÃ¤telager kann nicht aktualisiert werden.
devicestore.button.addmeters=ZÃ¤hler zum gewÃ¤hlten GerÃ¤telager hinzufÃ¼gen
devicestore.meters=Aktuelle ZÃ¤hler
devicestore.meters.in=Aktuelle ZÃ¤hler in 
devicestore.meters.description=ZÃ¤hler zur Zeit in diesm GerÃ¤telager.
devicestore.meters.header=GerÃ¤telager ZÃ¤hler
devicestore.meters.title=Aktuelle GerÃ¤telager ZÃ¤hler
devicestore.history=GerÃ¤telager Verlauf
devicestore.history.description=Vorherige Ãnderungen im GerÃ¤telager (Ãnderungen sind markiert)
devicestore.user=Benutzer
devicestore.date=Datum
devicestore.date.mod.column=Ãnderungsdatum
devicestore.user.by.column=Durch Benutzer
devicestore.action.column=Aktion
devicestore.status.column=Status
devicestore.name.column=Name
devicestore.description.column=Beschreibung
devicestore.button.importmeters=ZÃ¤hler in gewÃ¤hltes GerÃ¤telager importieren
devicestore.import.meters.header=ZÃ¤hler in GerÃ¤telager {0} importieren

### Meter ###
meter.title=ZÃ¤hler
meter.number.instructions=Suche mit ZÃ¤hlernummer
meter.add=ZÃ¤hler neu hinzufÃ¼gen
meter.add.new=ZÃ¤hler neu hinzufÃ¼gen
meter.or=oder
meter.then=dann
meter.fetch.number=Fetch Meter
meter.specify.install.date=Installationsdatum angeben
meter.open=ZÃ¤hler Ã¶ffnen
meter.open.newtab=Ãffne ersetzten ZÃ¤hler in neuem Tab.
meter.assign=ZÃ¤hler abrufen 
meter.attach=Attach Meter to Usage Point 
meter.info.title=ZÃ¤hlerinformationen
meter.required.text=* \= Pfichtfeld
meter.required.activation.text=** \= FÃ¼r die Aktivierung erforderlich
meter.show.info=ZÃ¤hlerinformationen anzeigen
meter.showing.info=zeige ZÃ¤hlerinformationen
meter.active.help=Ist dieser ZÃ¤hler aktiv? Diese Option ist deaktiviert, bis alle erforderlichen ZÃ¤hlerinformationen gespeichert wurden.
meter.active=Aktive
meter.replace.help=Ersetzte aktuellen ZÃ¤hler an diesem Einsatzort mit anderem ZÃ¤hler.
meter.replace=ZÃ¤hler am Einsatzort ersetzen
meter.remove.help=Remove current meter from this usage point. Will de-activate the usage point. Can access it again through Advanced Search. 
meter.remove=Remove Meter from Usage Point
meter.assign.from.store.help=Fetch a meter from the device store.
meter.date.install.missing=Enter the new installation date.
meter.assign.from.store=ZÃ¤hler vom GerÃ¤telager abrufen 
meter.select.meter.model=WÃ¤hle ZÃ¤hlerversion
meter.select.meter.model.help=WÃ¤hlen Sie die Version des ZÃ¤hlers. Damit wird festgelegt welche Informationen fÃ¼r den ZÃ¤hler benÃ¶tigt werden.
meter.select.metertype=ZÃ¤hlerart
meter.select.metertype.help=WÃ¤hlen Sie den ZÃ¤hlerart
meter.number.help=Geben Sie die ZÃ¤hlernummer an. Dies ist ein Pflichtfeld - der Datensatz kann nicht ohne sie gespeichert werden.
meter.number=ZÃ¤hlernummer
meter.number.optional.help=Enter a specific meter number, if you have one. 
meter.number.optional=Meter Number (optional)
meter.iso.help=Geben Sie die ZÃ¤hler ISO an.
meter.iso=ISO
meter.checksum.help=Geben Sie die PrÃ¼fsumme des ZÃ¤hlers an.
meter.checksum=PrÃ¼fsumme
meter.serialnumber.help=Geben Sie die Seriennummer des ZÃ¤hlers an.
meter.serialnumber=Seriennummer
meter.breakerid=Breaker Id
meter.breakerid.help=The Meter Model requires a breaker id, enter here.
meter.breakerid.error=The Meter Model requires a meter breaker id.
meter.stsinfo=STS Information
meter.algorithmcode=Algorithmus Code
meter.tokentechcode=Guthaben Tech Code
meter.supplygroupcode=Aktueller Versorgungsgruppen Code
meter.tariffindex=Aktueller Tarifindex
meter.save.errors=ZÃ¤hler nicht gespeichert. Korrigieren Sie die Fehler.
meter.saved=ZÃ¤hler gespeichert.
meter.changes.cleared=Ãnderungen wurden gelÃ¶scht.
meter.enter.number=ZÃ¤hlernummer eingeben
#meter.enter.number=Geben Sie eine ZÃ¤hlernummer an
meter.powerlimit=Leistungsbegrenzung
meter.powerlimit.help=WÃ¤hlen Sie die gewÃ¼nschte Leistungsbegrenzung
meter.description=Beschreibung
meter.description.help=Geben Sie eine Beschreibung an
meter.token.active=Die Einsatzort muss aktiv sein, um dieses Guthaben zu bekommen
meter.token.code=Guthaben Code
meter.token.code1=Guthaben Code 1
meter.token.code2=Guthaben Code 2
meter.title.enginerringtokens=Technisches Guthaben
meter.issue.engineeringtoken=Technisches Guthaben neu Ausstellung
meter.engineeringtoken.history=Technisches Guthaben Verlauf
meter.engineeringtoken.description=Bisherige Technisches Guthaben fÃ¼r diesen ZÃ¤hler
meter.select.tokentype=WÃ¤hle Guthabentype
meter.txn.history=Transaktion Verlauf
meter.txn.history.description= Bisherige Kundentransaktionen fÃ¼r diesen ZÃ¤hler
meter.history=ZÃ¤hler Verlauf
meter.history.description=Bisherige Ãnderungen an diesem ZÃ¤hler (Ãnderungen markiert)
meter.reports=Reporte fÃ¼r ZÃ¤hler
meter.recharge.history=ZÃ¤hler Aufladeverlauf
meter.retailers=Vertriebsstellen in der NÃ¤he
meter.reports.general=Allgemeine Reporte fÃ¼r ZÃ¤hler
meter.reports.meter=Reporte fÃ¼r ZÃ¤hler
meter.freeissue.units=Kostenlos ausgegebene - Einheiten
meter.cleartamper=LÃ¶sche Manipulation
meter.clearcredit=LÃ¶sche Guthaben
meter.clearcredit.all=LÃ¶sche gesamtes Guthaben
meter.clearcredit.elec=LÃ¶sche Strom Guthaben
meter.clearcredit.type.description=Guthabenart
meter.clearcredit.type.help=WÃ¤hlen Sie die Art von Guthaben das gelÃ¶scht werden musss
meter.coords=HinzufÃ¼gen von Breite und LÃ¤nge.-Koordinaten des Einsatzortes, um Vertriebsstelle in der NÃ¤he zu sehen.
meter.user=Benutzer
meter.serial=Fortsetzung
meter.date=Datum
meter.date.mod.column=Datum geÃ¤ndert
meter.user.by.column=Durch Benutzer
meter.action.column=Aktion
meter.status.column=Status
meter.number.column=ZÃ¤hler Nr.
meter.serial.column=Fortsetzung
meter.uniqueid.column=Eindeutige ID
meter.unique.external.column=Externe eindeutige ID
meter.techtoken.column=TT
meter.alg.column=Alg
meter.supplygroup.column=SG
meter.keyrevision.column=KR
meter.tariffindex.column=TI
meter.enddevicestore.column=Lager
meter.units.kwh=Einheit (kWh)
meter.units.kwh.help=Geben Sie die benÃ¶tigten kWh an.
meter.units.watts=Einheiten (Watt)
meter.units.watts.help=Geben Sie das maximale Phasenlimit in Watt an.
meter.currency=WÃ¤hrung
meter.currency.help=Geben Sie den gewÃ¼nschten Zahlungsbetrag an.
meter.free.description=Beschreibung
meter.free.description.help=Geben Sie eine Beschreibung fÃ¼r diese kostenlose Ausgabe an.
meter.setphase=Einstellen der max. Phase
meter.setphase.description=Beschreibung
meter.setphase.description.help=Geben Sie eine Beschreibung fÃ¼r diese Einstellung des Phasenguthabens.
meter.token.error=Guthaben konnte nicht abgerufen werden. ÃberprÃ¼fen Sie die Fehler.
meter.error.units=WÃ¤hlen Sie einen gÃ¼ltigen Wert fÃ¼r die erforderliche Anzahl der Einheiten 
meter.error.amount=WÃ¤hlen Sie einen gÃ¼ltigen Wert fÃ¼r die erforderliche Menge
meter.txn.type=Art
meter.txn.token.type=Guthabenart
meter.txn.receipt=Beleg
meter.txn.token=Guthaben
meter.txn.amount=Betrag inkl MwSt
meter.txn.tax=Steuer
meter.txn.units=Einheiten
meter.txn.tariff=Tarif
meter.txn.date=Datum
meter.txn.ref=Referenz
meter.txn.receiptnum=Belegnummer
meter.txn.customer=Kunden
meter.txn.client=Auftraggeber
meter.txn.term=Laufzeit
meter.txn.revref=Stornierung Ref
meter.txn.isreversed=storniert
meter.txn.amount.column=Betrag
meter.txn.usagepoint=Einsatzort
meter.txn.user=Benutzer
meter.txn.description=Beschreibung
meter.clear.description=Beschreibung
meter.clear.description.help=Geben Sie eine Beschreibung fÃ¼r diese LÃ¶schung der Manipulation an.
meter.changekey=SchlÃ¼ssel Ã¤ndern
meter.changekey.instructions=For Key Change Tokens:\n1. Open the Meter Panel.\n2. In the STS information block, change the Current Supply Group Code.\n3. This will add a new listbox with key change options - select what you require.\n4. Upon saving the meter, a popup box will be shown to input further details.
meter.new.supplygroupcode.help=Geben Sie den neuen Versorgungsgruppencode an.
meter.new.supplygroupcode=Neuer Versorgungsgruppencode
meter.old.supplygroupcode=Alter Versorgungsgruppencode
meter.new.tariffindex=Neuer Tarifindex
meter.old.tariffindex=Alter Tarifindex
meter.new.keyrevisionnum=Neue SchlÃ¼ssel Revisionsnummer
meter.old.keyrevisionnum=Alte SchlÃ¼ssel Revisionsnummer
meter.new.tariffindex.help=Geben Sie den neuen Tarifindex an.
meter.txn.filter=Filter
mdc.txn.show.connect.disconnect.only=Zeige nur Verbinden und Unterbrechen
mdc.txn.show.balance.messages.only=Zeige nur Guthabenmeldungen
meter.engtoken.filter=Filter
meter.history.filter=Filter
meter.generate.keychange=Erzeuge SchlÃ¼sselÃ¤nderungs-Guthaben
meter.generate.keychange.help=Wenn der ZÃ¤hler aktualisiert werden muss, um mit den neuen STS Details Ã¼bereinzustimmen, dann mÃ¼ssen SchlÃ¼sselÃ¤nderungs Token erzeugt werden. Wenn der Datensatz aktualisiert wird, um mit den ZÃ¤hlerdetails Ã¼bereinzustimmen, dann gibt es keine Notwendigkeit ein Token zu erzeugen.
meter.luhncheck.failed=Fehlerhafte ZÃ¤hlernummer (Luhn-Check gescheitert)
meter.error.alreadyexists=Ein ZÃ¤hler mit dieser Nummer existiert bereits.
meter.keychange.none=SchlÃ¼sselwechsel-Token nicht generieren
meter.keychange.now=Jetzt SchlÃ¼sselwechsel Token erzeugen
meter.keychange.flag=Mit nÃ¤chsten Verkauf auf erzeuge SchlÃ¼sselwechsel Token setzen
meter.pending.keychange=* Ausstehende SchlÃ¼sselÃ¤nderung: 
meter.warning.sg.transactions=HINWEIS: Es gibt bereits mehr als 3 Transaktionen auf diesem ZÃ¤hler. 
meter.error.save=ZÃ¤hler kann nicht gespeichert werden.
stsmeter.error.save=STS ZÃ¤hler kann nicht gespeichert werden.
meter.select.store.move=Verschiebe aktuellen ZÃ¤hler in das folgende Lager: 
meter.select.store.help=Der aktuelle ZÃ¤hler muss zu einem Lager hinzugefÃ¼gt werden. Wenn er einem Einsatzort zugeordnet wird, wird er automatisch aus dem Lager entfernt. Dies ist ein Pflichtfeld - der Datensatz kann nicht ohne gespeichert werden.
meter.message.type=Nachrichtenart
meter.message.type.help=Senden Sie eine Nachricht fÃ¼r den ZÃ¤hler
meter.assigned.to.usagepoint=ZÃ¤hler ist noch zugeordnet zu Einsatzort: 
meter.error.invalid=UngÃ¼ltiger ZÃ¤hler spezifiziert.
meter.error.invalid.datemanu=Herstellungsdatum ist ungÃ¼ltig.
meter.error.cannot.activate=Kann nicht aktivieren
meter.error.required.for.activation=FÃ¼r die Aktivierung erforderlich
meter.partial.search=Kein genau passender ZÃ¤hler. Erweiterte Suche...
meter.install.date.required=Das Datum und die Zeit bei der der neue ZÃ¤hler am Einsatzort {0} installiert wurde. 
meter.installed.at=ZÃ¤hler wurde installiert am {0} um {1}
meter.connect.disconnect.error=Fehler beim Senden der ZÃ¤hlerinstruktion : {0}
meter.connect.disconnect.connection.error=Keine Antwort empfangen. Dienst nicht verfÃ¼gbar. Bitte informieren Sie Ihren Systemadministrator.
meter.connect.disconnect.ok.mdc000=ZÃ¤hler {0} Befehl wurde erfolgreich abgeschlossen. Referenz={1}.
meter.connect.disconnect.ok.mdc010=ZÃ¤hler {0} Befehl akzeptiert. Referenz={1}.
meter.connect.disconnect.ok.mdc011=ZÃ¤hler {0} Nachricht wurde eingereiht. Referenz={1}.
meter.manufacturer.code.length=Manufacturer code
meter.manufacturer.code.length.help=The length of the manufacturer code, 2 digit or 4 digit
meter.2digit.manufacturer.code=2 digit code
meter.4digit.manufacturer.code=4 digit code
meter.found.incorrect.group=Meter found but in different group to user
usagepoint.found.incorrect.group=Usage Point found but in different group to user
meter.attach.cancelled=Attachment Process Cancelled. Meter {0} has been created in device store, but not assigned to this usage point.
meter.attach.instructions=Attach Meter to Usage Point
meter.attach.cancel.button=Cancel Attachment Process

### Customer ###
customer.title=Kunde
customer.search.instructions=Kunden Suche nach:
customer.add=Neuer Kunde hinzufÃ¼gen
customer.add.new=Neuer Kunde hinzufÃ¼gen
customer.info.title=Kundeninformation
customer.show.info=Kundeninformation anzeigen
customer.showing.info=Zeige Kundeninformation
customer.active.help=Ist dieser Kunde aktiv? Diese Option ist deaktiviert, bis alle erforderlichen Kundendaten gespeichert wurden.
customer.active=Aktive
customer.unassign=Kundenzuordnung von Einsatzort aufheben
customer.assign=Kunde dem Einsatzort zuordnen
customer.assign.short=Kunden zuweisen
customer.assign.help= Bestehenden Kunden diesem Einsatzort zuweisen. 
customer.unassign.help= Entferne den aktuellen Kunden von diesem Einsatzort.
customer.fetch=Fetch Customer
customer.fetch.help=Fetch existing Customer
customer.fetch.duplicate=Customer {0} already on page. 
customer.open=Ãffne Kunden
customer.field.title.help=Geben Sie den Titel des Kunden an (zB Herr, Frau, Dr.).
customer.field.title=Titel
customer.initials.help=Geben Sie die Kundeninitialen an.
customer.initials=Initialen
customer.firstnames.help=Geben Sie den Vornamen des Kunden an.
customer.firstnames=Vorname
customer.surname.help=Geben Sie den Nachnamen des Kunden an. Dies ist ein Pflichtfeld - der Kunde kann nicht ohne gespeichert werden.
customer.surname=Nachname
customer.surname.instr=Geben Sie den Nachnamen des Kunden an
customer.company.help=Geben Sie den Firmennamen an.
customer.company=Firmenname
customer.tax.help=Geben Sie die Steuernummer des Kunden an.
customer.tax=Steuernummer
customer.emails.help=Geben Sie die E-Mail-Adressen des Kunden an.
customer.phones.help=Geben Sie Telefonnummern des Kunden an.
customer.address.physical=Physikalische Adresse
customer.agreement=Kundenvertrag
customer.agreementref.help=Geben Sie die Vertragsreferenz mit dem Kunden an. Dies ist ein Pflichtfeld - der Kunde kann nicht ohne gespeichert werden.
customer.agreementref=Vertragsreferenz
customer.startdate.help=Geben Sie das Datum des Vertragsbeginn mit dem Kunden an. Obwohl der Datensatz ohne gespeichert werden kann, kann der Kunde nicht aktiviert werden wenn er nicht korrekt ausgefÃ¼llt wurde.
customer.startdate=Startdatum
customer.freeissue.help=WÃ¤hlen Sie das Zusatzkonto fÃ¼r kostenloses Guthaben, wobei der Wert des Guthabens durch hinzufÃ¼gen des Betrags auf das ausgewÃ¤hlte Konto wiedererlangt wird.
customer.freeissue=Kostenloses Zusatzkonto
customer.required=* \= Pflichtfeld
customer.required.activation=** \= Erforderlich zur Aktivierung
customer.save.errors=Kunden nicht gespeichert. Korrigieren Sie die angezeigten Fehler.
customer.sync.accbal.error=Ein Fehler ist bei der Synchronisierung des Kontostandes aufgetreten. Referenz={0}
customer.sync.accbal.connection.error=Keine Antwort vom Dienst empfangen.
customer.sync.accbal.ok.mdc000=Synchronisieren des Kontostandes erfolgreich abgeschlossen. Referenz={0}
customer.sync.accbal.ok.mdc010=Synchronisiere Kontostand Befehl akzeptiert. Referenz={0}
customer.sync.accbal.ok.mdc011=Synchronisiere Kontostand Nachricht wurde eingereiht. Referenz={0}
customer.changes.cleared=Ãnderungen wurden gelÃ¶scht.
customer.auxaccount.adjust=Adjust Auxiliary Account
customer.title.find=Suche Kunde
customer.assigned=Kann Kunde nicht zuordnen, er ist bereits zugeordnet.
customer.auxaccount=Auxiliary Account: {0}
customer.auxaccount.addedit=Zusatzkonten
customer.auxaccount.active=Aktive
customer.auxaccount.active.help=Ist das Konto aktiv? Diese Option ist deaktiviert, bis alle erforderlichen Kontoinformationen gespeichert wurden.
customer.auxaccount.type.help=WÃ¤hlen Sie die Art des Zusatzkontos dass Sie hinzufÃ¼gen.
customer.auxaccount.balance=Kontostand
customer.auxaccount.balance.help=Geben Sie das aktuelle Kontostand dieses Kontos an.
customer.auxaccount.balance.pos=A positive balance indicates a REFUND
customer.auxaccount.balance.neg=A negative balance indicates a DEBT
customer.auxaccount.priority=PrioritÃ¤t
customer.auxaccount.priority.help=Geben Sie die PrioritÃ¤t fÃ¼r dieses Konto an (1 ist die hÃ¶chste PrioritÃ¤t).
customer.auxaccount.chargeschedule=GebÃ¼hrenschema
customer.auxaccount.chargeschedule.help=WÃ¤hlen Sie das GebÃ¼hrenschema.
customer.auxaccount.txn.history=Auxiliary Account Transaction History for  : {0}.
customer.auxaccount.txn.description=Previous Transactions for this Auxiliary Account
customer.title.auxaccounts=Zusatzkonten
customer.title.auxaccounts.current=Aktuelle Zusatzkonten
customer.title.auxaccounts.description=Anzeigen und Bearbeiten von aktuellen Zusatzkonten und neue anlegen von Zusatzkonten.
customer.title.txnhistory=Kontotransaktionen Verlauf
customer.title.history=Kunden Historie
customer.title.reports=Reporte fÃ¼r Kunden
customer.title.generalreports=Allgemeine Reporte fÃ¼r Kunden
customer.title.chargescheduledetails=Informationen fÃ¼r ZusatzgebÃ¼hrenschema.
customer.title.chargeschedule=GebÃ¼hrenschema
customer.chargeschedule.startdate=Startdatum
customer.chargeschedule.vendpor=Verkaufsanteil
customer.chargeschedule.currpor=Aktueller Anteil
customer.chargeschedule.minamt=Min Betrag
customer.chargeschedule.maxamt=Max Betrag
customer.chargeschedule.status=Status
customer.auxaccount.filter=Filter
customer.auxaccount.date=Datum
customer.auxaccount.freeissue=kostenlose Ausgabe
customer.auxaccount.edit=Bearbeiten
customer.auxaccount.name=Kontoname
customer.auxaccount.name.help=Geben Sie einen Namen fÃ¼r dieses Konto ein.
customer.auxaccount.type=Kontoart
customer.auxaccount.type.column=Art
customer.auxaccount.status=Status
customer.auxaccount.add=Zusatzkonto hinzufÃ¼gen
customer.auxaccount.update=Zusatzkonto aktualisieren
customer.auxaccount.error.save=Zusatzkonto kann nicht gespeichert werden.
customer.auxaccount.error.id=Unable to apply auxiliary account adjustment against Customer Account. Contact support!
customer.auxaccount.error.unique.priority=Priority is required and must be unique. May not be smaller than or equal to zero.
customer.freeissue.error.save=Kundenvertrag kann nicht aktualisiert werden. 
customer.partial.search=Keine genau passender Kunde. Erweiterte Suche ...
customer.agreement.partial.search=Keine genau passender Vertrag. Erweiterte Suche ...
customer.account.partial.search=Keine genau passender Kontoname. Erweiterte Suche ...

customer.date.mod.column=Ãnderungsdatum
customer.user.by.column=Durch Benutzer
customer.action.column=Aktion
customer.status.column=Status
customer.title.column=Titel
customer.initials.column=Initialen
customer.firstnames.column=Vorname
customer.surname.column=Nachname
customer.company.column=Firma
customer.email1.column=E-Mail
customer.email2.column=E-Mail 2
customer.phone1.column=Telefon
customer.phone2.column=Telefon 2

customer.user=Benutzer
customer.date=Datum
customer.history=Kunden Historie
customer.history.description=Bisherige Ãnderungen bei diesem Kunden (Ãnderungen markiert)
customer.history.filter=Filter

customer.agreement.user= Benutzer
customer.agreement.date= Datum
customer.agreement.history=Kundenvertrags Historie
customer.agreement.history.description=Bisherige Ãnderungen an diesem Kundenvertrag (Ãnderungen markiert)
customer.agreement.history.filter=Filter
customer.agreement.date.mod.column=Ãnderungsdatum
customer.agreement.user.by.column=Durch Benutzer
customer.agreement.action.column=Aktion
customer.agreement.status.column=Status
customer.agreement.customer.column=Kunde
customer.agreement.ref.column=Vertrag Ref
customer.agreement.start.column=Startdatum
customer.agreement.freeaux.column=Freies Zusatzkonto
customer.error.save=Kann den Kunden nich speichern.
customeraggreement.error.save=Kann den Kundenvertrag nicht speichern.

customer.account=Kundenkonto
customer.account.name=Kontoname
customer.account.name.help=Geben Sie einen Namen fÃ¼r dieses Konto an
customer.account.balance=Kontostand
customer.account.balance.help=Der aktuelle Kontostand
customer.account.sync=Kontostand synchronisieren
customer.account.sync.help=Synchronisiere den Kontostand mit dem ZÃ¤hler Kontostand
customer.account.low.balance.threshold=Niedriger-Kontostand Grenzwert
customer.account.low.balance.threshold.help=Wenn der Kontostand diese Schwelle erreicht wird eine Meldung an den Kunden gesendet.
customer.account.credit.limit=Kreditlimit
customer.account.credit.limit.help=Das erlaubte Kreditlimit auf diesem Konto
customer.account.note= Ein Kundenkonto ist nur notwendig, wenn die ausgewÃ¤hlet Preisstruktur fÃ¼r diesen Einsatzort (unten) es erfordert.
customer.account.notification.email=Benachrichtigungs E-Mail Adresse
customer.account.notification.email.help=Eine E-Mail-Adresse an die Konto relevante Benachrichtigungen gesendet werden kÃ¶nnen (zB. wenn die niedrige Kontostandschwelle erreicht wird)
customer.account.notification.phone=Benachrichtigungstelefonnummer
customer.account.notification.phone.help= Eine Telefonnummer an die Konto relevante Benachrichtigungen gesendet werden kÃ¶nnen (zB. wenn die niedrige Kontostandschwelle erreicht wird)
customeraccount.error.save=Kundenkonto kann nicht gespeichert werden.

customer.txn.filter=Filter
customer.txn.history=Kontotransaktionen Verlauf
customer.txn.description= Bisherige Konto Transaktionen dieses Kundenvertragess
customer.txn.ent.date=Datum eingetragen
customer.txn.user=Benutzer eingetragen
customer.txn.name=Kunde
customer.txn.agreement.ref=Vertrags Ref
customer.txn.trans.type=Art der Transaktion
customer.txn.trans.date=Transaktions Datum
customer.txn.comment=Kommentar
customer.txn.our.ref=Unsere Referenz
customer.txn.amt=Betrag inkl MwSt
customer.txn.tax=Steuer
customer.txn.bal=Kontostand
customer.txn.input=Konto abgleichen
customer.txn.acc.ref=Konto Referenz
customer.txn.amt.incl.tax=Betrag inkl MwSt
customer.txn.successful.adjustment=Konto erfolgreich angepasst und neuer Kontostand
customer.txn.no.agreement=Es gibt KEINE Kundenvertrag abzugleichen
customer.txn.error.amt.and.tax.zero=Betrag und Steuern kÃ¶nnen nicht beide Null sein
customer.txn.error.update=Fehler beim Aktualisieren des Kundenkontostandes
customer.txn.error.insert=Fehler beim EinfÃ¼gen der neuen Anpassungstransaktionen
customer.txn.error.no.usagepoint=Einsatzort nicht gefunden
customer.txn.notification.failure=Kontoanpassungsbenachrichtigung kann nicht gesendet werden. Bitte informieren Sie Ihren Systemadministrator.
customer.txn.send.email.failure=Kontoanpassungsmail zum Kunden kann nicht gesendet werden. Bitte informieren Sie Ihren Systemadministrator.

# Customer transaction upload
customer.trans.upload=Transaction Upload
customer.trans.upload.heading=Customer Transaction Upload
customer.trans.upload.data.title=Import Customer Account Balance Adjustment Transactions
customer.trans.upload.data.description=Select the CSV file containing the Customer transactions for importing into the Meter Management system.
customer.trans.upload.file.help=Select a file containing customer transaction information in the specified csv format for importing into the system  
customer.trans.upload.file=Select Transaction File
customer.trans.upload.csv.button=Upload CSV Data
customer.process.trans.button=Process Transactions
customer.trans.upload.identifierType=Identifier Type
customer.trans.upload.identifier=Identifier
customer.trans.upload.amt.incl.tax=Amt incl Tax
customer.trans.upload.amt.tax=Tax Amt
customer.trans.upload.trans.date=Transaction date
customer.trans.upload.account.ref=Account Reference
customer.trans.upload.comment=Comment
customer.trans.upload.errors=Errors
customer.trans.upload.table.heading.errors=Transactions : Errors
customer.trans.upload.Bizswitch.down=No response received. BizSwitch service not available. Please notify your System Administrator.
customer.trans.upload.table.heading.valid=Valid Transactions : sample of first 15 lines in file
customer.trans.upload.invalid.cannot.create.dir=ERROR! Cannot create the directory. Please contact Support.
customer.trans.upload.filename=  Selected filename={0}
customer.trans.upload.invalid.filename=Improper filename - hyphen or period missing. Filenames expected as AccountTrans-reference.csv where <reference> is saved as 'our Ref' on transactions
customer.trans.upload.invalid.filename.changed=Filename has changed between steps! Was {0}; now {1}
customer.trans.upload.invalid.unexpected.commas=Commas inside fields - cannot identify separate fields accurately
customer.trans.upload.invalid.identifiertype=IdentifierType must be usagePointName / accountName / meterNumber
customer.trans.upload.invalid.identifier=Invalid Identifier - not in database
customer.trans.upload.invalid.agreement=Usage Point does not have a customer agreement in place
customer.trans.upload.invalid.usagepoint.or.agreement=Meter has no usage point or the usage point has no agreement
customer.trans.upload.invalid.usagepoint=Customer Account does not belong to a usage point 
customer.trans.upload.invalid.amt.incl.tax=Amount incl. Tax is not numeric
customer.trans.upload.invalid.amt.tax=Tax Amount is not numeric
customer.trans.upload.invalid.trans.date=Transaction date must be either empty (defaults to date of processing) or formatted as yyyy-mm-dd hh:mm:ss, example: 2015-09-23 22:14:55
customer.trans.upload.invalid.account.ref=Account Reference maximum 100 chars
customer.trans.upload.invalid.comment=Comment maximum 255 chars
customer.trans.upload.invalid.duplicate=Duplicate transaction of identifierType: {0}, identifier: {1}. Both target the same Customer Account. 
customer.trans.upload.file.action.unknown=Unknown file upload action, contact Support
customer.trans.upload.file.none=No file was selected to be uploaded
customer.trans.upload.file.error=Error while uploading the file
customer.trans.upload.file.process.error=Error while processing the file
customer.trans.upload.successful.counts=Total of {0} transactions in batch - {1} were successfully processed, {2} were duplicates and ignored in this run.
customer.trans.upload.process.failed=System Error on transaction:identifierType= {0}, identifier= {1}, accountRef= {2}. Try resubmitting the file. 
customer.trans.upload.trans.validation.errors=Validation errors found. Please repair and resubmit. Maximum 15 errors are processed at any one time

### Location ###
location.field.erfnumber=Erf Nummmer
location.field.erfnumber.help=Geben Sie die ERF Nummer dieses Ortes an
location.field.address=Addresse
location.field.address.help=Geben Sie die Adresse dieser Stelle an
location.field.city=Stadt
location.field.city.help=Geben Sie die Stadt von diesem Standort an
location.field.province=Bundesland
location.field.province.help=Geben Sie das Bundesland von diesem Standort an
location.field.country=Land
location.field.country.help=Geben Sie das Land von diesem Standort an
location.field.postalcode=Postleitzahl
location.field.postalcode.help=Geben Sie die Postleitzahl von diesem Standort an
location.field.lat=Breitengrad
location.field.lat.help=Geben Sie den Breitengrad des Standorts an
location.field.long=LÃ¤ngengrad
location.field.long.help=Geben Sie den LÃ¤ngengrad des Standorts an
location.latitude.invalid=Breitengrad ist ungÃ¼ltig.
location.longitude.invalid=LÃ¤ngengrad ist ungÃ¼ltig.
location.field.streetnumber=Hausnummer
location.field.streetnumber.help=Geben Sie die Hausnummer an
location.field.buildingname=GebÃ¤udename
location.field.buildingname.help=Geben Sie den GebÃ¤udenamen an
location.field.suitenumber=Wohnungsnummer
location.field.suitenumber.help=Geben Sie die Nummer der Wohnung an
location.error.save=Standort kann nicht gespeichert werden.
location.user=Benutzer
location.date=Datum
location.history=Standort Historie
location.history.description=Bisherige Ãnderungen an dieser Adresse.
location.history.date.mod.column=Ãnderungsdatum
location.history.user.by.column=Durch Benutzer
location.history.action.column=Aktion
location.history.status.column=Status
location.history.address1.column=Addresse 
location.history.address2.column=
location.history.address3.column=
location.history.erfnumber.column=Erf Nr   
location.history.latitude.column=Breitengrad   
location.history.longitude.column=LÃ¤ngengrad   
location.history.group.column=Gruppe   
location.history.streetnum.column=StraÃen Nr   
location.history.buildingname.column=GebÃ¤ude    
location.history.suitenum.column=Wohnungs Nr 
location.history.physical.address= Physicalischen Addressverlauf

## Display Tokens
displaytokens.title=Guthaben anzeigen
display.initiate=ZÃ¤hlertest einleiten
display.testload=1. Test der Lastschalter
display.testdisplay=2. Testen des Ableseinformationsanzeige
display.totals=3. Anzeige kumulative kWh gesamt Energieregister
display.krn=4. Anzeige der KRN
display.ti=5. Anzeige der TI
display.testreader=6. Test des Wertkarten-LesegerÃ¤ts
display.powerlimit=7. Anzeige maximale Leistungsgrenze
display.tamper=8. Anzeige Sabotagestatus
display.consumption=9. Anzeige Stromverbrauch
display.version=10. Anzeige Softwareversion
display.phase=11. Anzeige Phasenungleichheitsgrenze

## Change Group
changegroup.change=Gruppe Ã¤ndern
changegroup.username=Benutzername
changegroup.current=Aktuelle Gruppe
usergroup.field.grouphierarchy= Gruppenebene
changegroup.set=Gruppe einstellen
changegroup.select=Durchsuchen Sie die fÃ¼r Sie verfÃ¼gbaren Zugriffskontrollgruppen und wÃ¤hlen Sie eine Gruppe.
changegroup.available=VerfÃ¼gbare Gruppen
changegroup.error.group.none=WÃ¤hlen Sie eine gÃ¼ltige Gruppe.
changegroup.error.same=Die ausgewÃ¤hlte Gruppe ist die gleiche Gruppe wie die aktuelle Gruppe.
changegroup.assigned=Zugewiesene Zugriffskontrollgruppe
changegroup.group.changed=Ihre aktuelle Gruppe wurde geÃ¤ndert.
changegroup.group.cleared=Your current group was cleared, now using assigned group.
changegroup.error.clear=Cleared Group back to assigned group, but you still need to select the current group to use.
changegroup.current.details=Aktuelle Informationen
changegroup.username.help=Beginnen Sie mit dem Benutzernamen des Benutzers und wÃ¤hlen Sie den richtigen Benutzer aus der Benutzerliste.
changegroup.warning=Hinweis: Ãnderung Ihrer Gruppe erfordert Aktualisierung und/oder SchlieÃen einer Registerkarten, die Gruppenbezogenen ist. 

## User Group
usergroup.title=Benutzer-Zugriffsgruppe
usergroup.header=Benutzer-Zugriffsgruppe
usergroup.title.current=Aktuelle Benutzer-Zugriffsgruppe
usergroup.label.help=WÃ¤hlen Sie die Zugangsgruppe die der Benutzer angehÃ¶rt fÃ¼r die Anzeige und das Ãndern von Daten
usergroup.error.user=UngÃ¼ltige Benutzer angegeben.
usergroup.error.group=UngÃ¼ltige Gruppe angegeben.
usergroup.error.save=Benutzergruppe kann nicht gespeichert werden. 
usergroup.error.update=Benutzergruppe kann nicht aktualisiert werden. 
usergroup.field.user=Benutzer
usergroup.field.usergroup=Zugewiesene Zugriffsgruppe
usergroup.title.add=Benutzerzugriffsgruppe hinzufÃ¼gen
usergroup.title.update=Benutzerzugriffsgruppe aktualisieren
usergroup.instructions=Jeder Benutzer kann einer Zugriffsgruppe zugeordnet werden. Die Zugriffsgruppe des Benutzers bestimmt welche Daten dem Benutzer zur VerfÃ¼gung stehen, zum Anzeigen und zur Bearbeitung.
usergroup.confirm.clear=Sind Sie sicher, dass Sie die Zugriffsgruppe lÃ¶schen wollen? That user will no longer be able to access the system.
usergroup.clear.yourself=Cannot clear your own user's access group while you are logged on to this session.
usergroup.cleared=Die Benutzer-Zugriffsgruppe wurde gelÃ¶scht.
usergroup.error.usergroup.none=Es gibt keine aktuelle Zugriffsgruppe fÃ¼r den Benutzer.
usergroup.error.delete=Benutzer-Zugriffsgruppe kann nicht gelÃ¶scht werden.
usergroup.no.accessgroup=Keine Zugriffgruppenart wurde fÃ¼r den Datenzugriff festgelegt. Auf der Seite Gruppenarten, eine Zugriffgruppenart festlegen.
usergroup.accessgroup.none=Keine Benutzer-Zugriffsgruppe kann zugeordnet werden, da es keine Gruppen fÃ¼r die Benutzer-Zugriffskontrolle verfÃ¼gbar sind.

## Access Groups
accessgroups.title=Zugriffsgruppen
accessgroup.access.label=Gruppenart:
accessgroups.title.current=Aktuelle Zugriffsgruppen
accessgroup.access.instructions=Ihre aktuell zugewiesenen Zugriffsgruppe kann ebenfalls aktualisiert werden, wie einer ihrer Untergruppen. Neue Untergruppen Ihrem Zugriffsgruppe kÃ¶nnen ebenfalls hinzugefÃ¼gt werden. Wenn es keine verknÃ¼pften Daten gibt, kÃ¶nnen Sie auch Untergruppen lÃ¶schen.

locationgroups.title=Standortgruppen
locationgroup.instructions=Standortgruppen werden verwendet um Standorte in einer Hierarchie zu gruppieren. 
locationgroups.title.current=Aktuelle Standortgruppen

## Advanced Search
search.advanced.title=Erweiterte Suche
search.advanced.header=Erweiterte Suche
search.advanced.instructions=Geben Sie einen Teil oder vollstÃ¤ndige Suchdaten in die verschiedenen Felder ein und klicken Sie auf die SchaltflÃ¤che Suchen, um die Ergebnisse zu sehen.
search.advanced.results.header=Erweiterte Suchergebnisse
search.meter.number=ZÃ¤hlernummer
search.meter.model=ZÃ¤hlerversion
search.meter.no.usage.point=ZÃ¤hler ohne Einsatzort
search.type=Suchart
search.type.agreement=Vertragssucheart 
search.startswith=Beginnt mit
search.contains=EnthÃ¤lt
search.customer.name=Name
search.customer.surname=Nachname
search.customer.title=Titel
search.customer.agreement=Vertrag
search.customer.agreement.ref=Referenz
search.customer.no.usage.point=Kunden ohne Einsatzort
search.account=konto
search.account.name=Name
search.usagepoint.name=Name
search.meter=ZÃ¤hlersuche
search.customer=Kundensuche
search.usagepoint=Einsatzortsuche
search.usage.point.no.customer=Einsatzorte ohne Kunden
search.usage.point.no.meter=Usage Points no meter
search.error.no.criteria=Keine gÃ¼ltige Suchkriterien wurden angegeben.
search.no.results=Keine passenden Suchergebnisse gefunden.
search.meter.result=ZÃ¤hler
search.meter.model.result=ZÃ¤hlerversion
search.customer.result=Kunde
search.usagepoint.result=Einsatzort
search.pricingStructure.result=Preisstruktur
search.paymentMode.result=Zahlungsweise
search.name=Name
search.no.meter=Keine genau passender ZÃ¤hler gefunden. Versuchen Sie eine Suche mit TeilzÃ¤hlernummer: {0}.

meterreadings.title=ZÃ¤hlerablesungen
meterreadings.chart.title.single=ZÃ¤hlerstÃ¤nde
meterreadings.chart.title.balancing=Energiebilanzierung
meterreadings.header.graph=Grafik  
meterreadings.title.graph=ZÃ¤hler Ablesegrafik
meterreadings.error.none=Beginnen Sie mit der Eingabe einer ZÃ¤hlernummer und wÃ¤hlen Sie den entsprechenden ZÃ¤hler.
meterreadings.error.none.selected=WÃ¤hlen Sie einen gÃ¼ltigen ZÃ¤hler aus den verfÃ¼gbaren ZÃ¤hlern.
meterreadings.start=Startdatum
meterreadings.end=Enddatum
meterreadings.error.dates=Startdatum muss vor dem Enddatum sein.
meterreadings.error.type=WÃ¤hlen Sie einen Ableseart.
meterreadings.type=Ableseart
meterreadings.error.start=Startdatum ist erforderlich.
meterreadings.error.start.format=Startdatum muss mit {0} Ã¼bereinstimmen.
meterreadings.error.end=Enddatum ist erforderlich.
meterreadings.error.end.format=Enddatum muss mit {0} Ã¼bereinstimmen.
meterreadings.noreadings=Keine entsprechenden ZÃ¤hlerstÃ¤nde wurden gefunden.
meterreadings.chart.title=ZÃ¤hlerstÃ¤nde
meterreadings.chart.subtitle=kWh Verbrauch
meterreadings.chart.ytitle=kWh
meterreadings.chart.xtitle=Datum und Uhrzeit
meterreadings.series.name=kWh
meterreadings.graph.type=Grafikart
meterreadings.type.graph.single=EinzelzÃ¤hler
meterreadings.type.graph.balancing=Energiebilanzierung
meterreadings.error.type.reading.unknown=Ausleseart ist unbekannt.
meterreadings.balancing=HauptzÃ¤hler
meterreadings.error.balancing=HauptzÃ¤hlerr ist erforderlich.
meterreadings.error.type.graph=Unbekannte Grafikart.
meterreadings.meter.super=HauptzÃ¤hler Ablesung
meterreadings.meter.totals=Sub-ZÃ¤hler gesamt
meterreadings.meter.field.super=HauptzÃ¤hler
meterreadings.meter.field.subs=Sub-ZÃ¤hler
meterreadings.header.table=Report
meterreadings.title.table=ZÃ¤hlerablesereport
meterreadings.table.meter=ZÃ¤hler
meterreadings.table.reading=Ablesung
meterreadings.table.start=Startzeit
meterreadings.table.end=Endzeit
meterreadings.report.type=Reportsart
meterreadings.label.supermeter.total=HauptzÃ¤hler
meterreadings.label.singlemeters.total=Sub-ZÃ¤hler
meterreadings.report.results=ZÃ¤hlerstÃ¤nde

energybalancing.header=Energiebilanzierung
energybalancing.title=Energiebilanzierung
energybalancing.start=Startdatum
energybalancing.end=Enddatum
energybalancing.variation=Variation
energybalancing.error.start=Startdatum ist erforderlich.
energybalancing.error.end=Enddatum ist erforderlich.
energybalancing.error.dates=Startdatum muss vor dem Enddatum sein.
energybalancing.error.percent=Differenz muss ein positiver Prozentwert sein.
energybalancing.error.readingtype=ZÃ¤hlerablesungsart ist erforderlich.
energybalancing.supermeter=HauptzÃ¤hler
energybalancing.supermeter.reading=HauptzÃ¤hler gesamt
energybalancing.submeters.total=Sub-ZÃ¤hler gesamt
energybalancing.none=Keine passende Energiebilanzierungsablesung wurde gefunden.
energybalancing.view.graph=Ansicht Grafik

energybalancing.meter.header=EnergiebilanzierungszÃ¤hler
energybalancing.meter.title=EnergiebilanzierungszÃ¤hler
energybalancing.meter.super=BilanzierungszÃ¤hlernummer
energybalancing.meter.sub=Sub-ZÃ¤hler Nummer
energybalancing.meter.subs=AusgewÃ¤hlte  Sub-ZÃ¤hler
energybalancing.meter.super.help=Geben Sie einen ZÃ¤hlernummer an und wÃ¤hlen Sie den entsprechenden ZÃ¤hler.
energybalancing.meter.sub.help=Geben Sie einen ZÃ¤hlernummer an und wÃ¤hlen Sie den entsprechenden ZÃ¤hler. Klicken Sie dann auf die SchaltflÃ¤che >, um sie als Sub-ZÃ¤hler auszuwÃ¤hlen. 
energybalancing.meter.subs.help=Alle ausgewÃ¤hlten ZÃ¤hler werden als Sub-ZÃ¤hler des aktuellen BillanzierungszÃ¤hlers.
energybalancing.meter.instructions=WÃ¤hlen Sie einen ZÃ¤hler als die BilanzierungszÃ¤hler. WÃ¤hlen Sie und fÃ¼gen anderen Sub-ZÃ¤hler hinzu oder entfernen sie vorhandene Sub-ZÃ¤hler.
energybalancing.error.super=Ein gÃ¼ltiger, ausgewÃ¤hlter BilanzierungszÃ¤hler ist erforderlich.
energybalancing.error.sub.selected=Ein gÃ¼ltiger, ausgewÃ¤hlter Sub-ZÃ¤hler ist erforderlich.
energybalancing.error.super.id=UngÃ¼ltige BilanzierungszÃ¤hler-ID.
energybalancing.error.sub.ids=UngÃ¼ltige Sub-ZÃ¤hler-ID.
energybalancing.error.save=Balanzierungs / Sub-ZÃ¤hler kann nicht gespeichert werden.
energybalancing.meter.error.no.sub=WÃ¤hlen Sie zunÃ¤chst ein Sub-ZÃ¤hler.
energybalancing.meter.error.same.meter=Der Sub-ZÃ¤hler kann nicht der BilanzierungszÃ¤hler sein.
energybalancing.meter.save=Bilanzierungs.- und Sub-ZÃ¤hler gespeichert.
energybalancing.confirm.delete=Sind Sie sicher, dass sie diesen BilanzierungszÃ¤hler und alle Sub-ZÃ¤hler lÃ¶schen wollen?
energybalancing.meter.deleted=BilanzierungszÃ¤hler und Sub-ZÃ¤hler wurden gelÃ¶scht.
energybalancing.error.delete.none=Keine BilanzierungszÃ¤hler existieren fÃ¼r den angegebenen ZÃ¤hler

meterrecharge.chart.title=ZÃ¤hler ladet
meterrecharge.chart.subtitle=Lade BetrÃ¤ge
meterrecharge.chart.xtitle=Datum Zeit
meterrecharge.chart.ytitle=Kosten
meterrecharge.chart.price=Preis
meterrecharge.chart.purchaseprice=Kaufpreis

# Manufacturer 
meter.manufacturers=ZÃ¤hlerhersteller
meter.manufacturers.title=Aktuelle ZÃ¤hlerhersteller
meter.manufacturers.title.add=ZÃ¤hlerhersteller hinzufÃ¼gen
meter.manufacturers.title.update=ZÃ¤hlerhersteller aktualisieren
meter.manufacturer.name=Hertsteller
meter.manufacturers.field.name=Name
meter.manufacturers.field.description=Beschreibung
meter.manufacturers.field.active=Aktive
meter.manufacturers.field.status=Status
meter.manufacturers.field.name.help=Der eindeutige Name des Herstellers.
meter.manufacturers.field.description.help=Die Beschreibung des Herstellers.
meter.manufacturers.field.active.help=Nur aktive Hersteller kÃ¶nnen verwendet werden.
meter.manufacturer.name.duplicate=Doppelter Name fÃ¼r einen Hersteller: {0}.

# Mdc (Meter Data Collector) 
meter.mdc=MDC (ZÃ¤hlerdatensammler)
meter.mdc.title=Aktuelle ZÃ¤hlerdatensammler
meter.mdc.title.add=ZÃ¤hlerdatensammler hinzufÃ¼gen
meter.mdc.title.update=ZÃ¤hlerdatensammler aktualisieren
meter.mdc.name=MDC (ZÃ¤hlerdatensammler)
meter.mdc.field.name=Name
meter.mdc.field.description=Beschreibung
meter.mdc.field.active=Aktive
meter.mdc.field.status=Status
meter.mdc.field.value=Wert
meter.mdc.field.name.help=Eindeutiger Name des MDC.
meter.mdc.field.description.help=Die Beschreibung des MDC.
meter.mdc.field.value.help=Verwendet in NachrichtenÃ¼bertragung mit dem MDC.
meter.mdc.field.active.help=Nur aktive MDC kÃ¶nnen verwendet werden.
meter.mdc.name.duplicate=Doppelte Namen fÃ¼r eine MDC: {0}.
meter.mdc.value.duplicate=Doppelte Wert fÃ¼r eine mdc: {0}.
mdc.txn.messages=MDC Nachrichten
mdc.txn.message=MDC Nachrichtendetails - Ziehen Sie hier
mdc.txn.messages.description=MDC Nachrichten fÃ¼r diesen ZÃ¤hler
mdc.txn.ref=Referenz
mdc.txn.meter= ZÃ¤hler
mdc.txn.usagepoint=Einsatzort
mdc.txn.customer=Kunde
mdc.txn.reqreceived=Anforderung empfangen
mdc.txn.reqsent=Anforderung gesendet
mdc.txn.reqtype=Anfrageart
mdc.txn.controltype=Steuerungsart
mdc.txn.cmdaccrec=Befehl akzeptiert
mdc.txn.params=Param.
mdc.txn.recdate=Antwort erhalten
mdc.txn.repcount=Wiederholungen
mdc.txn.status=Status
mdc.txn.timecompld=Zeit beeendet
mdc.txn.client=Client
mdc.txn.term=Laufzeit
mdc.txn.refreceived=Ref empfangen
mdc.txn.identifier=Identifikator  
mdc.txn.identifiertype=Identifikatorart
mdc.txn.rescodereceived=Antwort Code
mdc.txn.scheduledate=Zeitplan Datum
mdc.txn.success=Erfolgreich
mdc.txn.pending=Unerledigt
mdc.txn.discarded=Verworfen
mdc.txn.successful=Erfolgreich
mdc.txn.failed=Fehlgeschlagen
mdc.txn.popup.label=Daten
mdc.txn.popup.value=Wert
mdc.txn.send.message.title=Sende MDC Nachricht
mdc.button.viewchannels=View Channels
mdc.error.noneselected=No current Mdc selected
mdc.txn.connect=Connect
mdc.txn.disconnect=Disconnect
mdc.txn.disconnect.enable=Disconnect_Enable

# Mdc Channel
channel.header=Channels
channel.title=Current MDC Channels
channel.value.duplicate=Duplicate value for an mdc channel: {0}
channel.field.titlename=Mdc Channel
channel.field.value=Value
channel.field.name=Name
channel.field.descrip=Description
channel.field.billingdet=Billing Determinant
channel.field.status=Status
channel.title.add=Add Mdc Channel
channel.title.update=Update Mdc Channel
channel.field.value.help=Enter Channel identifier.
channel.field.billingdetname=Billing Determinant
channel.field.billingdetname.channel.help=Select Billing determinant. Can be blank IF channel readings are not to be used for billing.
channel.field.meter.reading.type=Meter Reading Type
channel.field.maxsize=Reading Max Size
channel.field.active.help=The current activity status of this channel
channel.field.active=Active
channel.billingdet.confirm=You have not selected a billing determinant which implies that this channel is not used for billing. Continue?

#Mdc Channel Initial Readings
channel.readings.header=Assign Initial Channel Readings for Meter: 
channel.readings.meter.model=ZÃ¤hlerversion:
channel.readings.mdc=MDC:
channel.readings.pricing.structure=Preisstruktur:
channel.readings.installdate=Installationsdatum: 
channel.readings.table.heading=Initial Reading  
channel.readings.reading.error=Error Channel {0} : Reading value must be a positive numeric value smaller than or equal to the maximum size {1}

# Billing Determinant
billingdet.tab.label=Billing Det
billingdet.header=Billing Determinants
billingdet.title=Current Billing Determinants
billingdet.name=Billing Determinant
billingdet.title.add=Add Billing Determinant
billingdet.title.update=Update Billing Determinant
billingdet.field.name=Name
billingdet.field.name.help=Name of Billing Determinant
billingdet.field.description=Description
billingdet.field.description.help=Description of Billing Determinant
billingdet.active=Active
billingdet.active.help=Check to activate, must be Active to be in use.
billingdet.error.save=Unable to save the Billing Determinant.
billingdet.error.update=Unable to update the Billing Determinant

# Meter Model
meter.models=ZÃ¤hlerversion
meter.models.title=Aktuelle ZÃ¤hlerversionen
meter.models.name=ZÃ¤hlerversion
meter.models.field.manufacturer=Hersteller
meter.models.field.manufacturer.help=WÃ¤hlen Sie den richtigen Hersteller fÃ¼r die ZÃ¤hlerversion.
meter.models.field.name=Name
meter.models.field.description=Beschreibung
meter.models.field.active=Aktive
meter.models.field.status=Status
meter.models.field.name.help=Eindeutigen Namen der ZÃ¤hlerversion.
meter.models.field.description.help=Die Beschreibung der ZÃ¤hlerversion.
meter.models.field.active.help=Nur aktive ZÃ¤hlerversionen kÃ¶nnen verwendet werden.
meter.models.field.resource=Wartungsmittel
meter.models.field.resource.help=WÃ¤hlen Sie das richtige Wartungsmittel fÃ¼r die ZÃ¤hlerversion.
meter.models.field.metertype=ZÃ¤hlerart
meter.models.field.metertype.help=WÃ¤hlen Sie die richtige ZÃ¤hlerart fÃ¼r die ZÃ¤hlerversion.
meter.models.field.toa=UnterstÃ¼tzt Guthaben Over the Air
meter.models.field.toa.help=Dies zeigt an, ob diese Version das Senden von Guthaben wie STS-Guthaben direkt an den ZÃ¤hler Ã¼ber ein Netzwerk.
meter.models.field.mdc=MDC ZÃ¤hlerdatensammler
meter.models.field.mdc.help=VerknÃ¼pfen Sie ZÃ¤hlerversionen mit einem MDC.
meter.models.field.paymentmodes=Zahlungsarten
meter.models.field.paymentmodes.help=Verwenden Sie die Strg-Taste, um mehrere Zahlungsarten fÃ¼r diese ZÃ¤hlerversion auszuwÃ¤hlen.
meter.models.title.add=ZÃ¤hlerversion hinzufÃ¼gen
meter.models.title.update=ZÃ¤hlerversion aktualisieren
meter.models.paymentmodes.required=Mindestens eine Zahlungsart ist erforderlich.
meter.models.name.duplicate=Doppelte Namen fÃ¼r eine ZÃ¤hlerversion: {0}.
meter.models.field.balance.sync=UnterstÃ¼tzt Guthabenssynchronisierung
meter.models.field.balance.sync.help=Dies zeigt an, ob die ZÃ¤hlerversion eine Guthabenssynchronisierung unterstÃ¼tzt. 
meter.models.field.needs.breaker.id=Needs Breaker Id
meter.models.field.needs.breaker.id.help=Indicates whether the meter requires a breaker Id.
meter.model.unset.breaker.id.error=Cannot change the Breaker Id requirement - there are already meters with breakerId's using this meter model.

ptr.serviceresource=Wartungsmittel
ptr.metertype=ZÃ¤hlerart
ptr.paymentmode=Zahlungsart

tou.thin.field.calendar=Kalender
tou.thin.field.calendar.help=WÃ¤hlen Sie den entsprechenden Kalender fÃ¼r den Tarif.
tou.thin.field.monthlydemand=Monatlicher Leistungspreis
tou.thin.field.monthlydemandtype=Monatliche Leistungsmessungsart
tou.thin.field.monthlydemandtype.help=Geben Sie die monatliche Leistungsmessungsart fÃ¼r die Rechnung an.
tou.thin.field.servicecharge=TÃ¤gliche WartungsgebÃ¼hr
tou.thin.field.servicecharge.descrip=Description
tou.thin.field.servicecharge.descrip.help=Enter a line item description for the service charge.
tou.thin.field.servicechargecycle=Wartungskosten Zyklus
tou.thin.field.servicechargecycle.help=Wann die WartungsgebÃ¼hr angewendet werden soll.
tou.thin.field.enablereadingtypes=Aktiviere ZÃ¤hlerablesearten
tou.thin.field.enablereadingtypes.help=WÃ¤hlen Sie die Ablesearten fÃ¼r diesen Tarif.
tou.thin.charges.button=GebÃ¼hren erfassen
tou.thin.field.charges=GebÃ¼hren
tou.thin.field.charges.specialday=FeiertagsgebÃ¼hren
tou.thin.change.calendar=Sind Sie sicher, dass Sie alle Ihre aktuellen GebÃ¼hren verwerfen wollen und einen neuen Kalender zu erstellen?
tou.thin.error.no.calendar=WÃ¤hlen Sie einen Kalender.
tou.thin.error.no.types=WÃ¤hlen Sie mindestens eine Art.
tou.thin.error.no.tax=Steuer ist erforderlich.
tou.thin.monthlydemandtype.required=Ableseart ist erforderlich.
tou.thin.monthlydemand.required=GebÃ¼hr ist erforderlich.
tou.thin.monthlydemand.positive=GebÃ¼hr muss positiv sein.
tou.thin.monthlydemand.invalid=GebÃ¼hr muss ein gÃ¼ltiger numerischer Wert sein.
tou.thin.cycle.required=Zyklus ist erforderlich.
tou.thin.servicecharge.invalid=WartungsgebÃ¼hr muss ein gÃ¼ltiger numerischer Wert sein.
tou.thin.tax.invalid=Steuer muss ein gÃ¼ltiger numerischer Wert sein.
tou.thin.servicecharge.required=WartungsgebÃ¼hr ist erforderlich.
tou.thin.servicecharge.positive=WartungsgebÃ¼hr muss positiv sein.
tou.thin.no.tariff.data.available= Keine Tarifdaten zur VerfÃ¼gung, um aktualisiert und gespeichert zu werden.
tou.thin.charges.none=GebÃ¼hren mÃ¼ssen fÃ¼r den Tarif erfasst werden.
tou.thin.charges.invalid=VerrechnungssÃ¤tze mÃ¼ssen ein positiver, gÃ¼ltiger numerischer Werte sein.
tou.thin.specialdayscharges.none=FeiertagsgebÃ¼hren mÃ¼ssen fÃ¼r den Tarif erfasst werden.
tou.thin.specialdayscharges.invalid=FeiertagssÃ¤tze mÃ¼ssen ein positiver, gÃ¼ltiger numerischer Werte sein.
tou.thin.rates.none=No rates entered
tou.thin.rates.invalid=Rates must be positive and not zero

# Register Reading Tariffs
register.reading.billing.determinant.title=Billing Determinants
register.reading.billing.determinant.help=Select the Billing Determinants for which to capture rates for this pricing structure
register.reading.rates.button=Capture Rates
register.reading.rates.title=Rates
register.reading.billing.determinant.column.title=Billing Determinant
register.reading.error.no.tax=Tax is required.
register.reading.rates.none= No rates entered
register.reading.rates.invalid=Rates must be positive and not zero
register.reading.error.no.billingdet=Select at least one billing determinant.
register.reading.no.tariff.data.available=No tariff data available to be updated and saved.
register.reading.billing.det.change=Billing Determinants in selected list not the same as current charges for this Tariff. Previous charges will be deleted, all charges must be re-entered. Continue?
register.reading.servicecharge.descrip.required=Service Charge Description is required. 
register.reading.cannot.change.charge=Tariff already active, cannot change rates.

# Register Reading Information tab
register.reading.txn.label=Register Readings
register.reading.txn.description=Register Readings for this meter for the time period selected
register.reading.txn.timestamp=Timestamp
register.reading.txn.channel=Channel
register.reading.txn.determinant=Determinant
register.reading.txn.readingtype=Meter Reading Type
register.reading.txn.readingvalue=Value
register.reading.txn.error.enddate=End date must be equal or greater than startdate.
register.reading.txn.filter=Filter
register.reading.txn.noreadings=No corresponding register readings were found.

# AppSetting 
appsetting.header=Anwendungseinstellungen
appsetting.title=Aktuelle Anwendungseinstellungen
appsetting.title.update=Anwendungseinstellungen aktualisieren
appsetting.name=Anwendungseinstellung
appsetting.field.name=Name
appsetting.field.value=Wert
appsetting.field.description=Beschreibung
appsetting.field.name.help=Name der Anwendungseinstellung. Can be edited for customised translation purposes. For Custom Fields, we recommmend you keep the words 'Label' or 'Status' as part of the name for readability sake...
appsetting.field.value.help=Der Wert, der fÃ¼r diese Einstellung angewendet wird. For custom field Labels, enter the Label you wish to see on the input component for this field.
appsetting.field.description.help=Beschreibung der Anwendungseinstellung.
appsetting.name.duplicate=Doppelte Namen fÃ¼r eine Anwendungseinstellung: {0}.
appsetting.error.new=Application Setting {0} not found. Please contact your System Administrator.
appsetting.error.disconnect.greater.emergency.credit=Emergency Credit must be greater than or equal to Disconnect.
appsetting.error.disconnect.greater.reconnect=Reconnect must be greater than or equal to Disconnect.
appsetting.error.disconnect.greater.both=Disconnect must be smaller than or equal to Reconnect AND smaller than or equal to Emergency Credit
appsetting.error.maxvend.smaller.minvend=Maximum Vend Amount must be greater than or equal to Minimum Vend Amount 
appsetting.error.emergency.credit.greater.low.balance=Emergency Credit must be smaller than or equal to Low Balance.
appsetting.error.emergency.credit.errors=Emergency Credit must be greater than or equal to Disconnect AND smaller than or equal to Low Balance.
appsetting.error.invalid.custom.status=Custom Field status invalid! Must be one of OPTIONAL, REQUIRED or UNAVAILABLE. 

demo.addmeterreadings.title.criteria=ZÃ¤hlerablesekriterien
demo.addmeterreadings.interval=ZÃ¤hlerableseinterval
demo.addmeterreadings.readingtypes=ZÃ¤hlerableseart 
demo.addmeterreadings.tariffcalc=Sende Tarifermittlungsabfrage nach Ablesungen
demo.addmeterreadings.error.paymentmode=Zahlungsart ist erforderlich.
demo.addmeterreadings.error.meter=Geben Sie eine ZÃ¤hlernummer ein und wÃ¤hlen Sie einen von der verfÃ¼gbaren Auswahl.
demo.addmeterreadings.error.meter.select=Bitte wÃ¤hlen Sie einen ZÃ¤hler von der verfÃ¼gbaren Auswahl.
demo.addmeterreadings.no.usagepoint1=ZÃ¤hler hat keinen Einsatzort.
demo.addmeterreadings.no.usagepoint2=Tarifermittlungsabfrage ist nicht mÃ¶glich.
demo.addmeterreadings.no.usagepoint3=Unable to read usage point for Meter. Please inform Support.
demo.addmeterreadings.error.start=Start ist erforderlich.
demo.addmeterreadings.error.end=Ende ist erforderlich.
demo.addmeterreadings.error.dates=Start muss vor Ende sein.
demo.addmeterreadings.error.end.future=Ende muss vor dem heutigen Tag liegen.
demo.addmeterreadings.error.interval=Ableseintervall ist erforderlich.
demo.addmeterreadings.error.types=Mindestens eine Ableseart ist erforderlich.
demo.addmeterreadings.invalid.input=UngÃ¼ltige Eingabe fÃ¼r ZÃ¤hlerablesungen hinzufÃ¼gen.
demo.addmeterreadings.error.insert=Fehler beim EinfÃ¼gen der ZÃ¤hlerablesung.
demo.addmeterreadings.error.insert.fact=Fehler beim EinfÃ¼gen der ZÃ¤hlerablesedaten.
demo.addmeterreadings.error.insert.timeDim=Fehler beim EinfÃ¼gen eines ZeitDim fÃ¼r eine neue ZÃ¤hlerablesung.
demo.addmeterreadings.error.duplicates=ZÃ¤hler hat vorhandene ZÃ¤hlerablesungen fÃ¼r den aktuellen Zeitraum. Ãndern Sie den Datumsbereich oder wÃ¤hlen Sie die vorhandenen ZÃ¤hlerablesungen zu lÃ¶schen.
demo.addmeterreadings.error.duplicate.Facts=ZÃ¤hler hat vorhandene ZÃ¤hlerablesegedaten fÃ¼r den aktuellen Zeitraum.  Ãndern Sie den Datumsbereich oder wÃ¤hlen Sie die vorhandenen ZÃ¤hlerablesungen zu lÃ¶schen.
demo.addmeterreadings.minutes.15=15 Minuten
demo.addmeterreadings.minutes.30=30 Minuten
demo.addmeterreadings.minutes.60=60 Minuten

demo.addsupermeterreadings.link=[DEMO] Add Balancing Meter's Interval Readings
demo.addsupermeterreadings.header=ZÃ¤hlerablesungen hinzufÃ¼gen
demo.addsupermeterreadings.title=ZÃ¤hlerablesungen abgleichen
demo.addsupermeterreadings.title.criteria=ZÃ¤hlerablesungenkriterien
demo.addsupermeterreadings.readingtype=Ableseart
demo.addsupermeterreadings.variation=Ableseartvariante
demo.addsupermeterreadings.variations=ZÃ¤hlerabgleichvarianten
demo.addsupermeterreadings.variations.help=ZÃ¤hlerablesungen abgleichen, vergleicht das Gesamte mit seinen Sub-ZÃ¤hler. Sie kÃ¶nnen Stunden und ein Prozentsatz spezifizieren, die verwendet werden um die Abgleichsablesung zu reduzieren, damit sie sich von ihren Sub-ZÃ¤hlern unterscheidet.
demo.addsupermeterreadings.hour=Stunde
demo.addsupermeterreadings.supermeter=BilanzierungszÃ¤hler
demo.addsupermeterreadings.super.delete.readings=LÃ¶sche vorhandene BilanzierungszÃ¤hlerablesungen
demo.addsupermeterreadings.subs.regen.readings=Neuerstellen Sub-ZÃ¤hlerablesungen                   
demo.addsupermeterreadings.error.supermeter=BilanzierungszÃ¤hler ist erforderlich.
demo.addsupermeterreadings.error.type=ZÃ¤hlerableseart ist erforderlich.
demo.addsupermeterreadings.hour.required=Stunde ist erforderlich.
demo.addsupermeterreadings.percentage.required=Prozentsatz ist erforderlich.
demo.addsupermeterreadings.variation.duplicate=Stunde wird bereits in einer bestehenden Variante verwendet. 
demo.addsupermeterreadings.invalid.input=UngÃ¼ltige Eingabe, um die ZÃ¤hlerablesungen der BilanzierungszÃ¤hler zu erzeugen.
demo.addsupermeterreadings.invalid.input.submeters=Keine Zub-ZÃ¤hler fÃ¼r BilanzierungszÃ¤hler verfÃ¼gbar.
demo.addsupermeterreadings.success=BilanzierungszÃ¤hlerablesungen wurden erfolgreich hinzugefÃ¼gt.
demo.addsupermeterreadings.error.super.duplicates=BilanzierungszÃ¤hler verfÃ¼gt Ã¼ber bestehende Ablesungen fÃ¼r den aktuellen Zeitraum. Ãndern Sie den Datumsbereich oder wÃ¤hlen Sie die bestehenden BilanzierungszÃ¤hlerlesungen zu lÃ¶schen.
demo.addsupermeterreadings.error.sub.duplicates=Es gibt vorhandenen ZÃ¤hlerablesungen fÃ¼r den aktuellen Zeitraum. Ãndern Sie den Datumsbereich oder wÃ¤hlen Sie die ZÃ¤hlerablesungen zu regenerieren.

export.error=Fehler: Daten kÃ¶nnen nicht erfolgreich exportiert werden.
export.error.nodata=Fehler: Keine Daten waren zum Exportieren vorhanden.
export.error.nofile=Fehler: Der Dateiname konnte nicht ermittelt werden.
export.field.meter=ZÃ¤hler
export.field.metertype=ZÃ¤hlerart
export.field.date=Ablesedatum
export.field.start=Startzeit
export.field.end=Endzeit
export.field.reading=Ablesung
export.denied=Zugriff verweigert. Sie mÃ¼ssen angemeldet sein, um diese FunktionalitÃ¤t zu nutzen.
export.denied.group=Zugriff verweigert. Sie gehÃ¶ren nicht zu der gleichen Gruppe wie der ZÃ¤hler.
export.singlemeter.filename.prefix=ZÃ¤hlerablesungen-ZÃ¤hler
export.energybalancing.filename.prefix=Energiesummierung-HaupzÃ¤hler

taskschedule.title.add=Einen Aufgabenplan hinzufÃ¼gen
taskschedule.title.update=Aufgabenplan aktualisieren
taskschedule.header=Aktuelle AufgabenplÃ¤ne
taskschedule.title=AufgabenplÃ¤ne
taskschedule.title.single=Aufgabenplan
taskschedule.type=Aufgabenplan
taskschedule.name=Aufgabenplanname
taskschedule.name.help=Geben Sie einen Namen an, um Ihren Aufgabenplan zu identifizieren.
taskschedule.active=Aktive
taskschedule.active.help=Ob der Arbeitsplan aktiv ist und wie geplant ausgefÃ¼hrt wird.
taskschedule.schedule=Datum/Zeitplan
taskschedule.schedule.help=Geben Sie an wann der Aufgabenplan lÃ¤uft, durch die Auswahl einer entsprechenden Option und AusfÃ¼llen der Felder.
taskschedule.status=Status
taskschedule.scheduledtask=Aufgabenart
taskschedule.schedule.daily=Einmal am Tag
taskschedule.schedule.weekly=Einmal die Woche
taskschedule.schedule.monthly=Einmal im Monat
taskschedule.schedule.repeatedly=Wiederholungen pro Minuten/Stunden
taskschedule.hours=Stunden
taskschedule.minutes=Minuten
taskschedule.time=Zeit
taskschedule.day=Tag der Woche
taskschedule.daymonth=Tag des Monats
taskschedule.days=Sonntag, Montag, Dienstag, Mittwoch, Donnerstag, Freitag, Samstag
taskschedule.error.time=Zeit ist ein Pflichtfeld.
taskschedule.error.day=Tag ist erforderlich.
taskschedule.error.daymonth=Tag ist erforderlich.
taskschedule.users=Aufgabenbenutzer
taskschedule.error.no.user=Suchen Sie zunÃ¤chst nach einem Benutzer und fÃ¼gen sie ihen dann als Aufgabenbenutzer hinzu.
taskschedule.error.taskusers=Wenigstens ein Aufgabenbenutzer oder ein Kunde wird benÃ¶tigt.
taskschedule.error.customer.notselected=Des Kundenname entspricht nicht dem ausgewÃ¤hlten Kundennamen. Geben Sie einen Nachnamen an, fÃ¼r die Auswahl eines gÃ¼ltigen Kunden.
taskschedule.user.noemail=Benutzer hat keine gÃ¼ltige Email-Adresse. Aufgabenbenutzer mÃ¼ssen eine gÃ¼ltige E-Mail-Adresse haben, um eingeplante Aufgaben E-Mails zu erhalten.
taskschedule.meter.single=EinzelzÃ¤hler
taskschedule.meter.super=HauptzÃ¤hler  
taskschedule.meter.readingtype=ZÃ¤hler Ableseart
taskschedule.timeperiods=Zeitraum
taskschedule.class.error.singlemeter.none=EinzelzÃ¤hler ist erforderlich.
taskschedule.class.error.singlemeter.select=Beginnen Sie mit einer ZÃ¤hlernummer und wÃ¤hlen Sie einen ZÃ¤hler aus der Liste.
taskschedule.class.error.type=ZÃ¤hler Ableseart ist erforderlich.
taskschedule.class.error.time=Zeitraum ist erforderlich.
taskschedule.class.error.supermeter=HauptzÃ¤hler   ist erforderlich.
taskschedule.class.error.hours=Stunden muss eine positive Zahl sein.
taskschedule.taskclass.previous.day=letzter Tag
taskschedule.taskclass.previous.week=letzte Woche
taskschedule.taskclass.previous.month=letztes Monat
taskschedule.error.taskschedule.save=Fehler beim Speichern des Aufgabenplans.
taskschedule.error.taskschedule.none=Kein Aufgabenplan festgelegt.
taskschedule.error.scheduledtask.save= Fehler beim Speichern der geplanten Aufgabe.
taskschedule.error.scheduledtaskuser.delete=Fehler beim LÃ¶schen des Benutzer fÃ¼r geplante Aufgabe.
taskschedule.error.scheduledtaskuser.save=Fehler beim Speichern des Benutzer fÃ¼r geplante Aufgabe.
taskschedule.error.scheduling=Feheler beim festlegen des Aufgabenplans.
taskschedule.error.descheduling=Fehler bei der Aufhebung des bestehenden Aufgabenplans.
taskschedule.every=Jede
taskschedule.of=Anzahl von
taskschedule.repeatedly.error.number=Eine positive Zahl ist erforderlich.
taskschedule.repeatedly.error.units=WÃ¤hlen Sie ein Intervallart.
taskschedule.all.supermeters=Alle HauptzÃ¤hler  

scheduledtask.title=Geplante Aufgaben
scheduledtask.header=Aktuelle geplante Aufgaben
scheduledtask.type=Geplante Aufgabe
scheduledtask.field.name=Geplante Aufgabename
scheduledtask.field.name.help=Geben Sie einen Namen an, um Ihren Aufgabenplan zu identifizieren.
scheduledtask.field.class=Aufgabenart
scheduledtask.field.class.help=WÃ¤hlen Sie die Art der Aufgabe, auf dem geplanten Zeitpunkt ausgefÃ¼hrt werden.Select the type of task to be run on the scheduled date and time.
scheduledtask.title.add=Geplante Aufgabe hinzufÃ¼gen
scheduledtask.title.update=Geplante Aufgaben aktualisieren
scheduledtask.previous.hours=letzte Stunden
scheduledtask.customer.name=Kunde
scheduledtask.customer.name.help=Geben Sie den Nachnamen eines Kunden an und wÃ¤hlen Sie den entsprechenden Kunden, der mit dieser geplanten Aufgabe verknÃ¼pft werden soll.
scheduledtask.error.delete.none.selected=Keine geplante Aufgabe ist ausgewÃ¤hl.
scheduledtask.delete.confirm=Sind Sie sicher, dass Sie den geplanten Task lÃ¶schen mÃ¶chten?
scheduledtask.error.delete.none=Keine geplante Aufgabe ist angegeben.
scheduledtask.error.delete=Die geplante Aufgabe kann nicht gelÃ¶scht werden.
scheduledtask.deleted=Geplante Aufgabe wurde erfolgreich gelÃ¶scht.

scheduledtask.email.subject.taskschedule=ZÃ¤hlerverwaltung - Aufgabenplan:
scheduledtask.email.subject.scheduledtask=\u0020Aufgabe:\u0020
scheduledtask.email.message.taskschedule=Aufgabeplan:\u0020
scheduledtask.email.message.scheduledtask=Aufgabe:\u0020
scheduledtask.email.message.meter=ZÃ¤hler:\u0020
scheduledtask.email.message.start=Startdatum:\u0020
scheduledtask.email.message.end=Enddatum:\u0020
scheduledtask.email.message=Im Anhang finden Sie das Ergebnis Ihrer Aufgabe.
scheduledtask.email.message.supermeter=HauptzÃ¤hler  :\u0020

password.change.header=Passwort Ã¤ndern
password.old=Aktuelles Passwort
password.new=Neues Passwort
password.confirm=Neues Passwort bestÃ¤tigen
password.old.required=Aktuelles Passwort ist erforderlich.
password.new.required=Neues Passwort ist erforderlich.
password.confirm.required=BestÃ¤tigung des neuen Passwortes ist erforderlich.
password.new.nonmatching=Neues Passwort und BestÃ¤tigung stimmen nicht Ã¼berein.
password.changed=Passwort wurde erfolgreich geÃ¤ndert.
password.expiry=Ihr Passwort wird in {0} Tag(e) ablaufen.
password.ldap=Ihre Benutzer ist durch LDAP authentifiziert, daher ist eine PasswortÃ¤nderung hier nicht mÃ¶glich.
password.login.expired=Sie mÃ¼ssen Ihr Passwort zu Ã¤ndern, weil es abgelaufen ist.
password.login.reset=Sie mÃ¼ssen Ihr Passwort zu Ã¤ndern, weil es zurÃ¼ckgesetzt wurde.
password.user.change=Sie mÃ¼ssen Ihr Kennwort Ã¤ndern, bevor Sie die Anwendung verwenden kÃ¶nnen.
password.locked=Ihr Konto wurde gesperrt.
password.validate.current=Falsches aktuelles Passwort.
password.validation.minUppercase=Passwort muss mindestens {0} GroÃbuchstaben haben.
password.validation.minDigits=Passwort muss mindestens {0} Zeichen haben.
password.validation.minLength=Passwort muss eine MindestlÃ¤nge von {0} Stellen haben.
password.validation.maxLength=Passwort darf eine maximale LÃ¤nge von {0} Zeichen haben.
password.validation.numHistory=Sie kÃ¶nnen kein Passwort verwenden, dass mit einem der letzten {0} Ã¼bereinstimmt.
password.required=Passwort ist erforderlich.
username.required=Benutzername ist erforderlich.
logged.in=Sie haben sich erfolgreich angemeldet.
login.session.timedout=Ihre aktuelle Sitzung ist abgelaufen. Bitte erneut anmelden, um die Seite weiterhin zu verwenden.

# Dashboard
dashboard.title=Ãbersicht                                                
dashboard.meter.count.title=Anzahl der ZÃ¤hler
dashboard.meter.count.description=Die aktuelle Anzahl der ZÃ¤hler im System, per ZÃ¤hlerversion.
dashboard.meter.model.name=ZÃ¤hlerversion
dashboard.meter.count=#

## Custom fields
user.custom.fields.title=Custom Fields
user.custom.fields.error.get=Database error getting custom field application settings for {0}! Contact Support.
user.custom.fields.error.unknown.setting=Custom field application setting {0} not catered for. Inform Support.
user.custom.field.status.optional=OPTIONAL
user.custom.field.status.required=REQUIRED
user.custom.field.status.unavailable=UNAVAILABLE

#Import Data
import.data.header=Daten importieren
import.meters.title=ZÃ¤hler importieren
import.data.metermodel.help=Wenn eine ZÃ¤hlerversion in der importierten Datei nicht spezifiziert ist, wird stattdessen die in der Auswahlliste gewÃ¤hlte Version verwendet
import.data.metermodel=Standard ZÃ¤hlerversion
import.data.file.help=WÃ¤hlen Sie eine Datei, die ZÃ¤hlerinformationen im spezifiziertem csv-Format enthÃ¤lt, fÃ¼r den Import in das System aus
import.data.file=WÃ¤hle ZÃ¤hlerdatei
import.data.button=ZÃ¤hler importieren
import.meters.heading=ZÃ¤hler importieren auf GerÃ¤telager
import.meters.description=WÃ¤hlen Sie die csv-Datei mit ZÃ¤hlerdaten fÃ¼r den Import in das ZÃ¤hlerverwaltungssystem. <br/> WÃ¤hlen Sie einen Standard ZÃ¤hlerversion aus der Auswahlliste. Diese wird verwendet, wenn ein ZÃ¤hlerversion nicht der importierten Datei enthalten ist.

timezone.change=Zeitzone Ã¤ndern
timezone.header=Aktuelle Zeitzone einstellen
timezone.label=Zeitzone

############################################################
#### IpayXml Messages for account balance notifications ####
############################################################
# The defaultAccountAdjustmentProcessor.notification messages have the following arguments:
# arg0=customer.title
# arg1=customer.initials
# arg2=customer.firstnames
# arg3=customer.surname
# arg4=meter number
# arg5=customer agreement ref
# arg6=usage point name
# arg7=account balance
# arg8=low balance threshold
# arg9=emergency credit threshold
# arg10=disconnect threshold
# arg11=reconnect threshold
# currency format example for account balance: {7,number,currency}
defaultAccountAdjustmentProcessor.notification.disconnect.email.subject=Kontostand fÃ¼r ZÃ¤hler {4} ist erschÃ¶pft
defaultAccountAdjustmentProcessor.notification.disconnect.email.message=Sehr geehrter Kunde, \n\nihr ZÃ¤hler wird unterbrochen.\n\nIhr aktuellen Kontostand ist:\n Ihr Kontostand: {7,number,currency}\n Niedrige Guthabensbenachrichtungschwelle: {9,number,currency}\n\nGrÃ¼sse vom,\nSupport Team
defaultAccountAdjustmentProcessor.notification.disconnect.sms.message=Guthaben fÃ¼r ZÃ¤hler {4} ist erschÃ¶pft und wird getrennt. Guthaben ist {7,number,currency} GrÃ¼sse vom,\nSupport Team
defaultAccountAdjustmentProcessor.notification.lowBalance.email.subject=Niedriger Kontostand fÃ¼r ZÃ¤hler {4}
defaultAccountAdjustmentProcessor.notification.lowBalance.email.message=Sehr geehrter Kunde,\n\nihr aktueller Kontostatus ist: \n  Kontostand: {7,number,currency}\n  Niedrige Guthabensbenachrichtungschwelle: {9,number,currency}\n\nGrÃ¼sse vom,\nSupport Team
defaultAccountAdjustmentProcessor.notification.lowBalance.sms.message=Guthaben fÃ¼r ZÃ¤hler {4} geht zur Neige. Guthaben ist {7,number,currency} GrÃ¼sse vom, Support Team
