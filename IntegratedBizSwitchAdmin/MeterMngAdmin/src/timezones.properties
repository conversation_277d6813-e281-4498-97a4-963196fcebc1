# Names of the available time zones
timezone.names=southafrica,singapore,australia_darwin,australia_brisbane,pacific_apia,unitedkingdom

# Display names for each time zone
southafrica.name=South Africa
singapore.name=Singapore
australia_darwin.name=Australia/Darwin
australia_brisbane.name=Australia/Brisbane
pacific_apia.name=Pacific/Apia
unitedkingdom.name=United Kingdom

# <PERSON><PERSON> used to construct the time zone on GWT client-side
southafrica.json={"id": "Africa/Johannesburg", "transitions": [], "names": ["SAST", "South Africa Standard Time"], "std_offset": 120}
singapore.json={"id": "Asia/Singapore", "transitions": [], "names": ["SGT", "Singapore Standard Time"], "std_offset": 480}
australia_darwin.json={"id": "Australia/Darwin", "transitions": [], "names": ["ACST", "Australian Central Standard Time", "ACDT", "Australian Central Daylight Time"], "std_offset": 570}
australia_brisbane.json={"id": "Australia/Brisbane", "transitions": [16024, 60, 18880, 0, 173776, 60, 176800, 0, 182512, 60, 185536, 0, 191248, 60, 194272, 0], "names": ["AEST", "Australian Eastern Standard Time", "AEDT", "Australian Eastern Daylight Time"], "std_offset": 600}
pacific_apia.json={"id": "Pacific/Apia", "transitions": [357083, 60, 361598, 0, 365798, 60, 370334, 0, 374702, 60, 379238, 0, 383438, 60, 387974, 0, 392174, 60, 396710, 0, 400910, 60, 405446, 0, 409646, 60, 414182, 0, 418382, 60, 422918, 0, 427286, 60, 431822, 0, 436022, 60, 440558, 0, 444758, 60, 449294, 0, 453494, 60, 458030, 0, 462230, 60, 466766, 0, 470966, 60, 475670, 0, 479870, 60, 484406, 0, 488606, 60, 493142, 0, 497342, 60, 501878, 0, 506078, 60, 510614, 0, 514814, 60, 519350, 0, 523718, 60, 528254, 0, 532454, 60, 536990, 0, 541190, 60, 545726, 0, 549926, 60, 554462, 0, 558662, 60, 563198, 0, 567398, 60, 571934, 0, 576302, 60, 580838, 0, 585038, 60, 589574, 0, 593774, 60], "names": ["SST", "Samoa Standard Time", "Samoa Daylight Time", "Samoa Daylight Time"], "std_offset": 780}
unitedkingdom.json={"id": "Europe/London", "transitions": [19394, 60, 24770, 0, 28130, 60, 33506, 0, 36866, 60, 42242, 0, 45602, 60, 50978, 0, 54506, 60, 59714, 0, 63242, 60, 68450, 0, 71978, 60, 77354, 0, 80714, 60, 86090, 0, 89450, 60, 94826, 0, 98521, 60, 103561, 0, 107257, 60, 112297, 0, 115993, 60, 121033, 0, 124729, 60, 129937, 0, 133633, 60, 138673, 0, 142369, 60, 147409, 0, 151105, 60, 156145, 0, 159841, 60, 164881, 0, 168577, 60, 173785, 0, 177313, 60, 182521, 0, 186217, 60, 191257, 0, 194953, 60, 199993, 0, 203689, 60, 208729, 0, 212425, 60, 217465, 0, 221161, 60, 226201, 0, 230065, 60, 235105, 0, 238801, 60, 243841, 0, 247537, 60, 252577, 0, 256273, 60, 261481, 0, 265009, 60, 270217, 0, 273745, 60, 278953, 0, 282649, 60, 287689, 0, 291385, 60, 296425, 0, 300121, 60, 305329, 0, 308857, 60, 314065, 0, 317593, 60, 322801, 0, 326329, 60, 331537, 0, 335233, 60, 340273, 0, 343969, 60, 349009, 0, 352705, 60, 357913, 0, 361441, 60, 366649, 0, 370177, 60, 375385, 0, 379081, 60, 384121, 0, 387817, 60, 392857, 0, 396553, 60, 401593, 0, 405289, 60, 410497, 0, 414025, 60, 419233, 0, 422761, 60, 427969, 0, 431665, 60, 436705, 0, 440401, 60, 445441, 0, 449137, 60, 454345, 0, 457873, 60, 463081, 0, 466609, 60, 471817, 0, 475513, 60, 480553, 0, 484249, 60, 489289, 0, 492985, 60, 498025, 0, 501721, 60, 506929, 0, 510457, 60, 515665, 0, 519193, 60, 524401, 0, 528097, 60, 533137, 0, 536833, 60, 541873, 0, 545569, 60, 550777, 0, 554305, 60, 559513, 0, 563041, 60, 568249, 0, 571777, 60, 576985, 0, 580681, 60, 585721, 0, 589417, 60, 594457, 0], "names": ["GMT", "Greenwich Mean Time", "BST", "British Summer Time"], "std_offset": 0}