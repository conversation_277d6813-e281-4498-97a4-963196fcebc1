package za.co.ipay.metermng.shared.dto;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

import za.co.ipay.metermng.shared.MeterOnlineBulkData;

public class MeterOnlineBulkDto implements Serializable {

    private static final long serialVersionUID = -4215323064827848866L;
    
    private Integer resultCount = 0;
    private List<MeterOnlineBulkData> moblist = new ArrayList<MeterOnlineBulkData>();
    
    public MeterOnlineBulkDto() {
    }

    public Integer getResultCount() {
        return resultCount;
    }

    public void setResultCount(Integer resultCount) {
        this.resultCount = resultCount;
    }

    public List<MeterOnlineBulkData> getMoblist() {
        return moblist;
    }

    public void setMoblist(List<MeterOnlineBulkData> moblist) {
        this.moblist = moblist;
    }

}
