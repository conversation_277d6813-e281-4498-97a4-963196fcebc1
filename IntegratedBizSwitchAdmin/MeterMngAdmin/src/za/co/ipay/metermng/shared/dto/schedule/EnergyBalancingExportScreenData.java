package za.co.ipay.metermng.shared.dto.schedule;

import java.io.Serializable;
import java.util.ArrayList;

import za.co.ipay.metermng.mybatis.custom.model.MeterDto;
import za.co.ipay.metermng.mybatis.generated.model.MeterReadingType;

public class EnergyBalancingExportScreenData implements Serializable {

    private static final long serialVersionUID = 1L;

    public ArrayList<MeterReadingType> meterReadingTypes;
    public ArrayList<MeterDto> superMeters;
    
    public EnergyBalancingExportScreenData() {
        this.meterReadingTypes = new ArrayList<MeterReadingType>();
        this.superMeters = new ArrayList<MeterDto>();
    }

    public ArrayList<MeterReadingType> getMeterReadingTypes() {
        return meterReadingTypes;
    }

    public void setMeterReadingTypes(ArrayList<MeterReadingType> meterReadingTypes) {
        this.meterReadingTypes = meterReadingTypes;
    }

    public ArrayList<MeterDto> getSuperMeters() {
        return superMeters;
    }

    public void setSuperMeters(ArrayList<MeterDto> superMeters) {
        this.superMeters = superMeters;
    }    
}
