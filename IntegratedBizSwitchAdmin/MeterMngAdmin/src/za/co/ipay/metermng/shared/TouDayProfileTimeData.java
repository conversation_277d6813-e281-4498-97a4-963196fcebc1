package za.co.ipay.metermng.shared;

import java.util.ArrayList;

import za.co.ipay.metermng.mybatis.generated.model.TouDayProfileTime;
import za.co.ipay.metermng.mybatis.generated.model.TouPeriod;

import com.google.gwt.user.client.rpc.IsSerializable;

public class TouDayProfileTimeData extends TouDayProfileTime implements Comparable<TouDayProfileTimeData>, IsSerializable {

    private static final long serialVersionUID = 4788247536018907224L;
    
    private TouPeriod period;
    private ArrayList<Integer> completehours = new ArrayList<Integer>();
    
    private long start;
    private long end;
    
    public TouDayProfileTimeData() {
        super();
    }
    
    public TouDayProfileTimeData(TouDayProfileTime touDayProfileTime) {
        super();
        this.setId(touDayProfileTime.getId());
        this.setTouDayProfileId(touDayProfileTime.getTouDayProfileId());
        this.setTouPeriodId(touDayProfileTime.getTouPeriodId());
        this.setStartHour(touDayProfileTime.getStartHour());
        this.setStartMinute(touDayProfileTime.getStartMinute());
        this.setEndHour(touDayProfileTime.getEndHour());
        this.setEndMinute(touDayProfileTime.getEndMinute());
        if (this.getId() != null)
            this.setCompleteHours();
    }

    public TouPeriod getPeriod() {
        return period;
    }

    public void setPeriod(TouPeriod period) {
        this.period = period;
    }
   
    public long getStartMs() {
        return start;
    }
    
    public long getEndMs() {
        return end;
    }
    
    public void setStartMs(long ms) {
        start = ms;
    }
    
    public void setEndMs(long ms) {
        end = ms;
    }
    
    public ArrayList<Integer> getCompletehours() {
        return completehours;
    }
    
    public void setCompleteHours() {
        completehours.clear();
        int sh = getStartHour();
        int smi = getStartMinute();
        int eh = getEndHour();
        int emi = getEndMinute();
        
        if (sh == eh) {
            if (smi==0 && emi==59) {
                completehours.add(sh);
            }
        } else {
            for (int i=0; i<(eh-sh); i++) {
                completehours.add(sh+i);
            } 
            if (emi==59) {
                completehours.add(eh);
            }
        }
    }

    @Override
    public int compareTo(TouDayProfileTimeData o) {
        int i = getStartHour().compareTo(o.getStartHour());
        return (i != 0 ? i : getStartMinute().compareTo(o.getStartMinute()));
    }
}
