package za.co.ipay.metermng.shared;

import java.util.ArrayList;

import com.google.gwt.user.client.ui.SuggestOracle;

import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.rpc.callback.ClientCallback;

public class CustomerSuggestionsOracle extends SuggestOracle {

    private ClientFactory clientFactory;
    private boolean withUsagePointInfo;
    private boolean isIdNumberSearch;
    private boolean isAgrRefSearch;
    
    private ArrayList<Suggestion> storeSuggestions;
    
    public CustomerSuggestionsOracle(ClientFactory clientFactory, boolean withUsagePointInfo, boolean isIdNumberSearch, boolean isAgrRefSearch) {
        super();
        this.clientFactory = clientFactory;
        this.withUsagePointInfo = withUsagePointInfo;
        this.isIdNumberSearch = isIdNumberSearch;
        this.isAgrRefSearch = isAgrRefSearch;
    }

    @Override
    public void requestSuggestions(Request request, Callback callback) {
        if (request.getQuery().trim().length() > 2) {
            clientFactory.getSearchRpc().getCustomerSuggestions(request, withUsagePointInfo, isIdNumberSearch, isAgrRefSearch, request.getLimit(), new ItemRequestCallback(request, callback));
        }
    }

    public boolean isDisplayStringHTML() {
        return true;
    }

    public class ItemRequestCallback extends ClientCallback<Response> {
        
        private SuggestOracle.Request req;
        private SuggestOracle.Callback callback;

        public void onSuccess(Response result) {
            storeSuggestions = (ArrayList<Suggestion>) result.getSuggestions();
            callback.onSuggestionsReady(req, result);
        }

        public ItemRequestCallback(SuggestOracle.Request _req, SuggestOracle.Callback _callback) {
            this.req = _req;
            this.callback = _callback;
        }
    }

    public boolean isWithUsagePointInfo() {
        return withUsagePointInfo;
    }

    public void setWithUsagePointInfo(boolean withUsagePointInfo) {
        this.withUsagePointInfo = withUsagePointInfo;
    }

    public boolean isIdNumberSearch() {
        return isIdNumberSearch;
    }

    public void setIdNumberSearch(boolean isIdNumberSearch) {
        this.isIdNumberSearch = isIdNumberSearch;
    }

    public boolean isAgrRefSearch() {
        return isAgrRefSearch;
    }

    public void setAgrRefSearch(boolean agrRefSearch) {
        isAgrRefSearch = agrRefSearch;
    }

    public CustomerSuggestion getFirstSuggestion() {
        return (CustomerSuggestion) (storeSuggestions).get(0);
    }
}
