package za.co.ipay.metermng.shared.dto.meter;

import java.io.Serializable;
import java.math.BigDecimal;

public class MeterReadingVariation implements Serializable {

    private static final long serialVersionUID = 1L;

    private int hourOfDay; // 1 = 1AM, 14 = 2PM, etc
    private BigDecimal percentage; // 90% would be 0.9, etc

    public MeterReadingVariation() {
        this.hourOfDay = -1;
        this.percentage = BigDecimal.ONE;
    }
    
    public MeterReadingVariation(int hourOfDay, BigDecimal percentage) {
        this.hourOfDay = hourOfDay;
        this.percentage = percentage;
    }
    
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("MeterReadingVariation: ");
        sb.append(" hourOfDay:").append(hourOfDay);
        sb.append(" percentage:").append(percentage.toPlainString());
        return sb.toString();
    }
    
    public int getHourOfDay() {
        return hourOfDay;
    }

    public void setHourOfDay(int hourOfDay) {
        this.hourOfDay = hourOfDay;
    }

    public BigDecimal getPercentage() {
        return percentage;
    }

    public void setPercentage(BigDecimal percentage) {
        this.percentage = percentage;
    }
}
