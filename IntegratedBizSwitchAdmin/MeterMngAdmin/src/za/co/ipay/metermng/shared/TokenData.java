package za.co.ipay.metermng.shared;

import java.util.ArrayList;

import com.google.gwt.user.client.rpc.IsSerializable;

public class TokenData implements IsSerializable {
	
	public static final int TOKEN_TYPE_FREE_ISSUE_UNITS = 1;
	public static final int TOKEN_TYPE_FREE_ISSUE_CURRENCY = 2;
	public static final int TOKEN_TYPE_CLEAR_TAMPER = 3;
	public static final int TOKEN_TYPE_POWER_LIMIT = 4;
	public static final int TOKEN_TYPE_CLEAR_CREDIT = 5;
	public static final int TOKEN_TYPE_KEY_CHANGE = 6;
	public static final int TOKEN_TYPE_SET_PHASE = 7;
	
	private String resCode;
	private String errorMsg;
	private String customerMsg;
	
	private ArrayList<String> standardTokenCodes;
	private ArrayList<String> bsstTokenCodes;
	private ArrayList<String> engineeringTokenCodes;
	
	public ArrayList<String> getEngineeringTokenCodes() {
		return engineeringTokenCodes;
	}
	public void setEngineeringTokenCodes(ArrayList<String> tokenCodes) {
		this.engineeringTokenCodes = tokenCodes;
	}
	public void addEngineeringTokenCode(String tokenCode) {
	    if (engineeringTokenCodes==null) {
	        engineeringTokenCodes = new ArrayList<String>();
	    }
	    engineeringTokenCodes.add(tokenCode);
	}
	
	public ArrayList<String> getStandardTokenCodes() {
		return standardTokenCodes;
	}
	public void setStandardTokens(ArrayList<String> standardTokenCodes) {
		this.standardTokenCodes = standardTokenCodes;
	}
	public ArrayList<String> getBsstTokenCodes() {
		return bsstTokenCodes;
	}
	public void setBsstTokens(ArrayList<String> bsstTokens) {
		this.bsstTokenCodes = bsstTokens;
	}
    
    public String getResCode() {
        return resCode;
    }
    
    public void setResCode(String resCode) {
        this.resCode = resCode;
    }
    
    public String getErrorMsg() {
        return errorMsg;
    }
    
    public void setErrorMsg(String errorMsg) {
        this.errorMsg = errorMsg;
    }
    
    public String getCustomerMsg() {
        return customerMsg;
    }
    
    public void setCustomerMsg(String customerMsg) {
        this.customerMsg = customerMsg;
    }
    
    @Override
    public String toString() {
        StringBuilder builder = new StringBuilder();
        builder.append("TokenData [resCode=").append(resCode).append(", errorMsg=").append(errorMsg).append(", customerMsg=").append(customerMsg).append(", standardTokenCodes=").append(standardTokenCodes).append(", bsstTokenCodes=").append(bsstTokenCodes)
                .append(", engineeringTokenCodes=").append(engineeringTokenCodes).append("]");
        return builder.toString();
    }
    
    
	
}
