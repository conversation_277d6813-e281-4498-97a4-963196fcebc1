package za.co.ipay.metermng.shared;

import java.util.ArrayList;

import za.co.ipay.metermng.mybatis.generated.model.NdpSchedule;
import za.co.ipay.metermng.mybatis.generated.model.NdpSeason;
import za.co.ipay.metermng.mybatis.generated.model.NdpSpecialDay;

import com.google.gwt.user.client.rpc.IsSerializable;

public class NdpScheduleData implements IsSerializable {

   
    private static final long serialVersionUID = 1L;
    
    private NdpSchedule ndpSchedule; 
    private ArrayList<NdpSeason> ndpSeasonList;
    private ArrayList<NdpSpecialDay> ndpSpecialDayList;
    
    boolean ndpTimeEntered = false;
    
    public NdpScheduleData() {
    }
    
    public NdpScheduleData(NdpSchedule ndpSchedule) {
        this.ndpSchedule = ndpSchedule;
        ndpSeasonList = new ArrayList<NdpSeason>();
        ndpSpecialDayList = new ArrayList<NdpSpecialDay>();
    }
    
    
    public NdpSchedule getNdpSchedule() {
        return ndpSchedule;
    }

    public void setNdpSchedule(NdpSchedule ndpSchedule) {
        this.ndpSchedule = ndpSchedule;
    }

    public ArrayList<NdpSeason> getNdpSeasonList() {
        return ndpSeasonList;
    }

    public void setNdpSeasonList(ArrayList<NdpSeason> ndpSeasonList) {
        this.ndpSeasonList = ndpSeasonList;
    }

    public ArrayList<NdpSpecialDay> getNdpSpecialDayList() {
        return ndpSpecialDayList;
    }

    public void setNdpSpecialDayList(ArrayList<NdpSpecialDay> ndpSpecialDayList) {
        this.ndpSpecialDayList = ndpSpecialDayList;
    }

    public boolean isNdpTimeEntered() {
        return ndpTimeEntered;
    }

    public void setNdpTimeEntered(boolean ndpTimeEntered) {
        this.ndpTimeEntered = ndpTimeEntered;
    }
    
}
