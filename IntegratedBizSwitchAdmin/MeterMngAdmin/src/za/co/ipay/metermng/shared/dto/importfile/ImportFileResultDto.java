package za.co.ipay.metermng.shared.dto.importfile;

import java.io.Serializable;

public class ImportFileResultDto implements Serializable {
    private static final long serialVersionUID = 1L;
    
    private Boolean success;
    private String abortMsg;
    private Integer numFailedUpload;
    private Boolean stoppedImport = false;
    
    public ImportFileResultDto() {
    }
    
    public ImportFileResultDto(Boolean success, String abortMsg, Integer numFailedUpload) {
        this.success = success;
        this.abortMsg = abortMsg;
        this.numFailedUpload = numFailedUpload;
    }
    
    public ImportFileResultDto(Boolean success, String abortMsg, Integer numFailedUpload, Boolean stoppedImport) {
        this.success = success;
        this.abortMsg = abortMsg;
        this.numFailedUpload = numFailedUpload;
        this.stoppedImport = stoppedImport;
    }

    public Boolean getSuccess() {
        return success;
    }

    public void setSuccess(Boolean success) {
        this.success = success;
    }

    public String getAbortMsg() {
        return abortMsg;
    }

    public void setAbortMsg(String abortMsg) {
        this.abortMsg = abortMsg;
    }

    public Integer getNumFailedUpload() {
        return numFailedUpload;
    }

    public void setNumFailedUpload(Integer numFailedUpload) {
        this.numFailedUpload = numFailedUpload;
    }

    public Boolean getStoppedImport() {
        return stoppedImport;
    }

    public void setStoppedImport(Boolean stoppedImport) {
        this.stoppedImport = stoppedImport;
    }

}
