package za.co.ipay.metermng.shared;

import za.co.ipay.metermng.mybatis.generated.model.AccountTrans;

import com.google.gwt.user.client.rpc.IsSerializable;
import za.co.ipay.metermng.mybatis.generated.model.SpecialActionReasonsLog;

/**
 * CustomerAccountTransData is a wrapper around an account transaction (table: account_trans). 
 */
public class CustomerAccountTransData extends AccountTrans implements IsSerializable {

    private static final long serialVersionUID = -7657743504532207428L;

    String customerName;
    String agreementRef;
    String transType;
    SpecialActionReasonsLog specialActionReasonsLog;
 	
    public CustomerAccountTransData() {
        super();
    }
    
	public CustomerAccountTransData(AccountTrans accountTrans) {
	    super();
	    this.setId(accountTrans.getId());
	    this.setCustomerAccountId(accountTrans.getCustomerAccountId());
	    this.setAuxAccountId(accountTrans.getAuxAccountId());
	    this.setCustomerAgreementId(accountTrans.getCustomerAgreementId());
	    this.setAccountTransTypeId(accountTrans.getAccountTransTypeId());
	    this.setCustomerTransId(accountTrans.getCustomerTransId());
	    this.setPayTypeId(accountTrans.getPayTypeId());
	    this.setTransDate(accountTrans.getTransDate());
	    this.setDateEntered(accountTrans.getDateEntered());
	    this.setComment(accountTrans.getComment());
	    this.setAccountRef(accountTrans.getAccountRef());
	    this.setOurRef(accountTrans.getOurRef());
	    this.setAmtInclTax(accountTrans.getAmtInclTax());
	    this.setAmtTax(accountTrans.getAmtTax());
	    this.setResultantBalance(accountTrans.getResultantBalance());
	    this.setUserRecEntered(accountTrans.getUserRecEntered());
	}

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    public String getAgreementRef() {
        return agreementRef;
    }

    public void setAgreementRef(String agreementRef) {
        this.agreementRef = agreementRef;
    }

    public String getTransType() {
        return transType;
    }

    public void setTransType(String transType) {
        this.transType = transType;
    }

    public SpecialActionReasonsLog getSpecialActionReasonsLog() {
        return specialActionReasonsLog;
    }

    public void setSpecialActionReasonsLog(SpecialActionReasonsLog specialActionReasonsLog) {
        this.specialActionReasonsLog = specialActionReasonsLog;
    }
}
