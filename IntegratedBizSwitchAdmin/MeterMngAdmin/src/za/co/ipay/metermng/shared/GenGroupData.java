package za.co.ipay.metermng.shared;

import java.util.ArrayList;
import java.util.List;

import com.google.gwt.user.client.ui.SuggestOracle;

import za.co.ipay.metermng.mybatis.generated.model.GenGroup;

public class GenGroupData extends GenGroup implements SuggestOracle.Suggestion {

    private static final long serialVersionUID = 1L;

    private GenGroupData parent;
    private List<GenGroupData> children;
    private boolean newEntry = false;
    private ArrayList<Long> path;
    private Long groupTypeId;
    
    public GenGroupData() {
        this.children = new ArrayList<GenGroupData>();
    }
    
    
    @Override
    public String toString() {
        StringBuilder builder = new StringBuilder();
        builder.append("[GenGroupData:");
        builder.append(" id:").append(getId());
        builder.append(" name:").append(getName());
        builder.append(" parent:").append(parent);
        builder.append(" parentId:").append(getParentId());
        builder.append(" groupHierarchyId:").append(getGroupHierarchyId());
        builder.append(" thresholdId:").append(getCustomerAccThresholdsId());
        builder.append(" ndpScheduleId:").append(getNdpScheduleId());
        builder.append(" notficationId:").append(getCustAccNotifyId());
        builder.append(" newEntry:").append(newEntry);
        builder.append(" metadata:").append(getMetadata());
        builder.append(" number of childre:").append(getChildren() == null ? 0 : getChildren().size());
        builder.append("]     ");
        return builder.toString();
    }

    public GenGroupData getParent() {
        return parent;
    }

    public void setParent(GenGroupData parent) {
        this.parent = parent;
    }

    public List<GenGroupData> getChildren() {
        return children;
    }

    public void setChildren(List<GenGroupData> children) {
        this.children = children;
    }

    public boolean isNewEntry() {
        return newEntry;
    }

    public void setNewEntry(boolean newEntry) {
        this.newEntry = newEntry;
    }

    public ArrayList<Long> getPath() {
        return path;
    }

    public void setPath(ArrayList<Long> thePath) {
        this.path = thePath;
    }


    public Long getGroupTypeId() {
        return groupTypeId;
    }


    public void setGroupTypeId(Long groupTypeId) {
        this.groupTypeId = groupTypeId;
    }
   

    @Override
    public String getDisplayString() {
        String returnString = this.getName();
        if (this.parent != null) {
            returnString += (" ("+this.parent.getName()+")"); 
        }
        return returnString;
    }


    @Override
    public String getReplacementString() {
        return this.getName();
    }    
    
    
}
