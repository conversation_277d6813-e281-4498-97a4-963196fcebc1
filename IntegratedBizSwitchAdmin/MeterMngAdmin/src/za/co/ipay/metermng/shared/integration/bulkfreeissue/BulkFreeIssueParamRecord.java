package za.co.ipay.metermng.shared.integration.bulkfreeissue;

import com.google.gwt.user.client.rpc.IsSerializable;
import za.co.ipay.metermng.shared.dto.BulkParamRecord;

/**
 * Parameter record for bulk free issue operations.
 * Contains the parameters that apply to all meters in the bulk operation.
 */
public class BulkFreeIssueParamRecord extends BulkParamRecord implements IsSerializable {

    private static final long serialVersionUID = 1L;
    
    private Integer units;
    private String description;
    private String userReference;
    private Long specialActionReasonsId;
    private String reasonText;

    public BulkFreeIssueParamRecord() {
        super();
    }

    public Integer getUnits() {
        return units;
    }

    public void setUnits(Integer units) {
        this.units = units;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getUserReference() {
        return userReference;
    }

    public void setUserReference(String userReference) {
        this.userReference = userReference;
    }

    public Long getSpecialActionReasonsId() {
        return specialActionReasonsId;
    }

    public void setSpecialActionReasonsId(Long specialActionReasonsId) {
        this.specialActionReasonsId = specialActionReasonsId;
    }

    public String getReasonText() {
        return reasonText;
    }

    public void setReasonText(String reasonText) {
        this.reasonText = reasonText;
    }

    @Override
    public String toString() {
        return "BulkFreeIssueParamRecord [units=" + units + ", description=" + description 
                + ", userReference=" + userReference + ", specialActionReasonsId=" + specialActionReasonsId 
                + ", reasonText=" + reasonText + "]";
    }
}
