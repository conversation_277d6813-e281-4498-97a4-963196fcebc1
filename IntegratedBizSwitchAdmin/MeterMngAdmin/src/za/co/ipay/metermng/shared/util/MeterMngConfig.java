package za.co.ipay.metermng.shared.util;

import com.google.gwt.user.client.rpc.IsSerializable;

public class MeterMngConfig implements IsSerializable {
    
    /** Whether the application is running in demo mode or not. */
    private boolean demoMode;
    /** The application's logo URL which can be relative to the application's context root. */
    private String logoUrl;
    /** The application's CSS style sheet file's URL which can be relative to the application's context root. */
    private String cssUrl;
    /** Whether this application is only for Thin Smart functionality (some features are not visible in UI  */
    private boolean enableSTS;
    /** Whether to use the Manco logo on the About popup from the User Menu */
    private boolean useMancoLogo;
    /** Whether this application supports Centian functionality (some data are not visible in UI, if not)  */
    private boolean enableCentianSTS;
    /** Enable manually reversing - only the LAST vend / topup transaction can be reversed */
    private boolean allowReversalsLastTrans = false;
    /** Enable manually reversing - any vend / topup transaction can be reversed from transactions made AFTER the reversal code was implemented in the MMA System. */
    private boolean allowReversalsOlderTrans = false;
    /** Whether this application supports non-billable agreements to process readings for consumption meters  */
    private boolean enableNonBillable;
    /** Whether to use groups from Access Control versus internal groups*/
    private boolean enableAccessGroups = false;
    

    private String googleMapsKey;
    private String storeLocations;
    public MeterMngConfig() {
        this.demoMode = false;
        this.logoUrl = "";
        this.cssUrl = "";
        this.googleMapsKey = "";
        this.storeLocations = "";
        this.enableSTS = true;
        this.useMancoLogo = false;
        this.enableCentianSTS = false;
        this.allowReversalsLastTrans = false;
        this.allowReversalsOlderTrans = false;
        this.enableNonBillable = false;
        this.enableAccessGroups = false;
    }
    
    public boolean isDemoMode() {
        return demoMode;
    }

    public void setDemoMode(boolean demoMode) {
        this.demoMode = demoMode;
    }

    public String getLogoUrl() {
        return logoUrl;
    }

    public void setLogoUrl(String logoUrl) {
        this.logoUrl = logoUrl;
    }

    public String getCssUrl() {
        return cssUrl;
    }

    public void setCssUrl(String cssUrl) {
        this.cssUrl = cssUrl;
    }

    public boolean isEnableSTS() {
        return enableSTS;
    }

    public void setEnableSTS(boolean enableSTS) {
        this.enableSTS = enableSTS;
    }

    public boolean isUseMancoLogo() {
        return useMancoLogo;
    }

    public void setUseMancoLogo(boolean useMancoLogo) {
        this.useMancoLogo = useMancoLogo;
    }
    
    public boolean isEnableCentianSTS() {
        return enableCentianSTS;
    }

    public void setEnableCentianSTS(boolean enableCentianSTS) {
        this.enableCentianSTS = enableCentianSTS;
    }

	public boolean isAllowReversalsLastTrans() {
		return allowReversalsLastTrans;
	}

	public void setAllowReversalsLastTrans(boolean allowReversalsLastTrans) {
		this.allowReversalsLastTrans = allowReversalsLastTrans;
	}

	public boolean isAllowReversalsOlderTrans() {
		return allowReversalsOlderTrans;
	}

	public void setAllowReversalsOlderTrans(boolean allowReversalsOlderTrans) {
		this.allowReversalsOlderTrans = allowReversalsOlderTrans;
	}
    
    public String getGoogleMapsKey() {
        return googleMapsKey;
    }
	public boolean isEnableNonBillable() {
		return enableNonBillable;
	}

	public void setEnableNonBillable(boolean enableNonBillable) {
		this.enableNonBillable = enableNonBillable;
	}

    public void setGoogleMapsKey(String googleMapsKey) {
        this.googleMapsKey = googleMapsKey;
    }

    public String getStoreLocations() {
        return storeLocations;
    }

    public void setStoreLocations(String storeLocations) {
        this.storeLocations = storeLocations;
    }

    public boolean isEnableAccessGroups() {
        return enableAccessGroups;
    }

    public void setEnableAccessGroups(boolean enableAccessGroups) {
        this.enableAccessGroups = enableAccessGroups;
    }

}
