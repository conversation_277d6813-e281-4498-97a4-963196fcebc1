package za.co.ipay.metermng.shared;

import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.rpc.callback.ClientCallback;

import com.google.gwt.user.client.ui.SuggestOracle;

public class CustomerAgreementSuggestionsOracle extends SuggestOracle {

    private ClientFactory clientFactory;
 
    public CustomerAgreementSuggestionsOracle(ClientFactory clientFactory) {
        super();
        this.clientFactory = clientFactory;
    }

    @Override
    public void requestSuggestions(Request request, Callback callback) {
        if (request.getQuery().trim().length() > 2) {
            clientFactory.getSearchRpc().getCustomerAgreementSuggestions(request,
                    new ItemRequestCallback(request, callback));
        }
    }

    public boolean isDisplayStringHTML() {
        return true;
    }

    public class ItemRequestCallback extends ClientCallback<Response> {
        
        private SuggestOracle.Request req;
        private SuggestOracle.Callback callback;

        public void onSuccess(Response result) {
            callback.onSuggestionsReady(req, result);
        }

        public ItemRequestCallback(SuggestOracle.Request _req, SuggestOracle.Callback _callback) {
            this.req = _req;
            this.callback = _callback;
        }
    }
}
