package za.co.ipay.metermng.shared.util;

import com.google.gwt.regexp.shared.RegExp;

import za.co.ipay.gwt.common.client.form.Format.FormatUtil;
import za.co.ipay.gwt.common.shared.validation.ValidateUtil;

public class MeterMngSharedUtils {

	final private static String STRIP_THESE_OUT = "[()\\.\\-]";

	public MeterMngSharedUtils() {
		// no instances required
	}

	public static boolean isNumber(String string) {
		if (string == null || string.isEmpty()) {
			return false;
		}
		int i = 0;
		if (string.charAt(0) == '-') {
			if (string.length() > 1) {
				i++;
			} else {
				return false;
			}
		}
		for (; i < string.length(); i++) {
			if (!Character.isDigit(string.charAt(i))) {
				return false;
			}
		}
		return true;
	}
	
    public static boolean isValidCellPhone(String value) {
        if (ValidateUtil.isNotNullOrBlank(value)) {
            String strippedValue = value.replaceAll(STRIP_THESE_OUT, "").replaceAll(" ", "");
            if (strippedValue != null && FormatUtil.getInstance().getCellRegexPattern().test(strippedValue)) {
                return true;
            } else {
                return false;
            }
        } else {
            return false;
        }
    }
    
    public static boolean isValidDateFormat(String dateString, String testRegex) {
        if (testRegex == null) {   //default to "yyyy-MM-dd HH:mm:ss"
            testRegex = "\\d\\d\\d\\d-\\d\\d-\\d\\d \\d\\d:\\d\\d:\\d\\d";
        }    
        if (ValidateUtil.isNotNullOrBlank(dateString)) {
            RegExp regPattern = RegExp.compile(testRegex);
            if (regPattern.test(dateString)) {
                return true;
            } else {
                return false;
            }
        } else {
            return false;
        }
    }
}
