package za.co.ipay.metermng.shared;

import java.io.Serializable;

import za.co.ipay.metermng.mybatis.generated.model.MdcTrans;

public class MdcTransData extends MdcTrans implements Serializable {
	
    private static final long serialVersionUID = 7590805055920228889L;
	
	private String usagePointName;
	private String meterNum;
	private String custName;
	
	public MdcTransData() {
	    super();
	}
    
    public MdcTransData(MdcTrans mdcTrans) {
        super();
        this.setId(mdcTrans.getId());
        this.setClient(mdcTrans.getClient());
        this.setTerminal(mdcTrans.getTerminal());
        this.setRefReceived(mdcTrans.getRefReceived());
        this.setMdcId(mdcTrans.getMdcId());
        this.setMeterId(mdcTrans.getMeterId());
        this.setUsagePointId(mdcTrans.getUsagePointId());
        this.setCustomerAgreementId(mdcTrans.getCustomerAgreementId());
        this.setIdentifier(mdcTrans.getIdentifier());
        this.setIdentifierType(mdcTrans.getIdentifierType());
        this.setReqType(mdcTrans.getReqType());
        this.setOverride(mdcTrans.getOverride());
        this.setControltype(mdcTrans.getControltype());
        this.setParams(mdcTrans.getParams());
        this.setReqReceived(mdcTrans.getReqReceived());
        this.setReqSent(mdcTrans.getReqSent());
        this.setResReceived(mdcTrans.getResReceived());
        this.setResCodeReceived(mdcTrans.getResCodeReceived());
        this.setScheduleDate(mdcTrans.getScheduleDate());
        this.setSuccessful(mdcTrans.isSuccessful());
        this.setTimeCompleted(mdcTrans.getTimeCompleted());
        this.setRepCount(mdcTrans.getRepCount());
        this.setCmdAcceptedReceived(mdcTrans.getCmdAcceptedReceived());
    }

    public String getUsagePointName() {
        return usagePointName;
    }

    public void setUsagePointName(String usagePointName) {
        this.usagePointName = usagePointName;
    }

    public String getMeterNum() {
        return meterNum;
    }

    public void setMeterNum(String meterNum) {
        this.meterNum = meterNum;
    }

    public String getCustName() {
        return custName;
    }

    public void setCustName(String custName) {
        this.custName = custName;
    }
	
	
}
