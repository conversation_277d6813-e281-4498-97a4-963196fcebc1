package za.co.ipay.metermng.shared.dto.search;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;

import za.co.ipay.metermng.shared.MeterMngStatics;

/**
 * SearchData is a wrapper around a search's criteria and the corresponding search results.
 * <AUTHOR>
 */
public class SearchData implements Serializable {

    private static final long serialVersionUID = 1L;

    /** The start counter for retrieving the search results. */
    private int start;
    /** The number of results to retrieve. */
    private int pageSize;
    /** The total number of search results. */
    private Integer totalResults;
    /** The map of search criteria which contains fields name and value pairs. */
    private HashMap<String, Object> criteria;
    /** The corresponding results. */
    private ArrayList<SearchResultData> results;
    
    public SearchData() {
        this.start = 0;
        this.pageSize = MeterMngStatics.DEFAULT_PAGE_SIZE;
        this.totalResults = null;
        this.criteria = new HashMap<String, Object>();
        this.results = new ArrayList<SearchResultData>();
    }
    
    public SearchData(int start, int pageSize, Integer totalResults) {
        this.start = start;
        this.pageSize = pageSize;
        this.totalResults = totalResults;
        this.criteria = new HashMap<String, Object>();
        this.results = new ArrayList<SearchResultData>();
    }
    
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("SearchData:");
        sb.append(" start:").append(start);
        sb.append(" pageSize:").append(pageSize);
        sb.append(" totalResults:").append(totalResults);
        sb.append(" criteria:").append(criteria.toString());
        sb.append(" results:").append(results.toString());
        return sb.toString();
    }

    public void addCriteria(String name, Object value) {
        criteria.put(name, value);
    }
    
    public void addResults(ArrayList<SearchResultData> r) {
        results.addAll(r);
    }
    
    public HashMap<String, Object> getCriteria() {
        return criteria;
    }

    public ArrayList<SearchResultData> getResults() {
        return results;
    }

    public int getStart() {
        return start;
    }

    public void setStart(int start) {
        this.start = start;
    }

    public int getPageSize() {
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }

    public Integer getTotalResults() {
        return totalResults;
    }

    public void setTotalResults(Integer totalResults) {
        this.totalResults = totalResults;
    }
    
    public String getCriteriaAsJsonString() {
        if (criteria != null) {
            StringBuilder searchCriteria = new StringBuilder();
            searchCriteria.append("{");
            for(HashMap.Entry<String, Object> entry : criteria.entrySet()) {
                String key = entry.getKey();
                Object value = entry.getValue();
                if(value != null) {
                    if(searchCriteria.length()>1) {
                        searchCriteria.append(",");
                    }
                    if (key.equals(MeterMngStatics.METER_MODEL_ID_SEARCH) || key.equals(MeterMngStatics.PAYMENT_MODE_ID_SEARCH)) {
                        searchCriteria.append("\"" + key + "\":");
                        searchCriteria.append(value.toString());
                    } else {
                        searchCriteria.append("\"" + key + "\":");
                        searchCriteria.append("\"" +value+"\"" );
                    }
                }
            }
            searchCriteria.append("}");
            return searchCriteria.toString();
        } else {
            return "{}";
        }
    }
}
