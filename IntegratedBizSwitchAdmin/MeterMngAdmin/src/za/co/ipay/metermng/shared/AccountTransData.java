package za.co.ipay.metermng.shared;

import za.co.ipay.metermng.mybatis.generated.model.AccountTrans;
import za.co.ipay.metermng.mybatis.generated.model.SpecialActionReasonsLog;

import com.google.gwt.user.client.rpc.IsSerializable;

public class AccountTransData extends AccountTrans implements IsSerializable {
	
	private static final long serialVersionUID = 5606231252235517087L;
	
	protected SpecialActionReasonsLog specialActionReasonsLog;

	public AccountTransData() {
		super();
	}
	
	public AccountTransData(AccountTrans accountTrans) {
		super();
		this.setAccountRef(accountTrans.getAccountRef());
		this.setAccountTransTypeId(accountTrans.getAccountTransTypeId());
		this.setAmtInclTax(accountTrans.getAmtInclTax());
		this.setAmtTax(accountTrans.getAmtTax());
		this.setAuxAccountId(accountTrans.getAuxAccountId());
		this.setComment(accountTrans.getComment());
		this.setCustomerAccountId(accountTrans.getCustomerAccountId());
		this.setCustomerAgreementId(accountTrans.getCustomerAgreementId());
		this.setCustomerTransId(accountTrans.getCustomerTransId());
		this.setDateEntered(accountTrans.getDateEntered());
		this.setId(accountTrans.getId());
		this.setOurRef(accountTrans.getOurRef());
		this.setPayTypeId(accountTrans.getPayTypeId());
		this.setResultantBalance(accountTrans.getResultantBalance());
		this.setTransDate(accountTrans.getTransDate());
		this.setUserRecEntered(accountTrans.getUserRecEntered());
	}

	public SpecialActionReasonsLog getSpecialActionReasonsLog() {
		return specialActionReasonsLog;
	}

	public void setSpecialActionReasonsLog(SpecialActionReasonsLog specialActionReasonsLog) {
		this.specialActionReasonsLog = specialActionReasonsLog;
	}
}
