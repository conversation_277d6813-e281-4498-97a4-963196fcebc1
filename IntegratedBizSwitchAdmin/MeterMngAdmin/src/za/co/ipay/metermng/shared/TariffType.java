package za.co.ipay.metermng.shared;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

public class TariffType implements Serializable {
    
        private static final long serialVersionUID = 1L;
        
        private String displayName;
	private String className;
	
	public TariffType() {
	}
	
	public static TariffType BASIC_STEP;
	public static TariffType BASIC;
	
	static {
		BASIC_STEP = new TariffType("Basic Step Tariff", "com.innoforge.bizswitch.components.translators.ipayxmlelec_ststokentranslator.BasicStepTariff");
		BASIC = new TariffType("Cost Per Unit Tariff", "com.innoforge.bizswitch.components.translators.ipayxmlelec_ststokentranslator.CostPerUnitTariff");
	}
	
	private TariffType(String displayName, String className) {
		this.displayName = displayName;
		this.className = className;
	}
	
	public static final TariffType getByClassName(String className) {
		List<TariffType> types = getTariffTypes();
		for (TariffType tariffType : types) {
			if(tariffType.getClassName().equals(className))
				return tariffType;
		}
		throw new RuntimeException("Unknown tariff type");
	}

	public static final List<TariffType> getTariffTypes() {
		ArrayList<TariffType> list = new ArrayList<TariffType>(2);
		list.add(BASIC_STEP);
		list.add(BASIC);
		return list;
	}

	public String getDisplayName() {
		return displayName;
	}

	public String getClassName() {
		return className;
	}

	public void setDisplayName(String displayName) {
		this.displayName = displayName;
	}

	public void setClassName(String className) {
		this.className = className;
	}
	
}
