package za.co.ipay.metermng.shared.integration.bulkfreeissue;

import com.google.gwt.user.client.rpc.IsSerializable;
import za.co.ipay.metermng.shared.integration.GenericImportRecord;

/**
 * Import record for bulk free issue operations.
 * Contains meter number and free issue parameters.
 */
public class BulkFreeIssueImportRecord implements IsSerializable, GenericImportRecord {

    private static final long serialVersionUID = 1L;
    
    private String meterNum;
    private Integer units;
    private String description;
    private String userReference;
    private String reason;
    private String customReason;

    public BulkFreeIssueImportRecord() {
        super();
    }

    public String getMeterNum() {
        return meterNum;
    }

    public void setMeterNum(String meterNum) {
        this.meterNum = meterNum;
    }

    public Integer getUnits() {
        return units;
    }

    public void setUnits(Integer units) {
        this.units = units;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getUserReference() {
        return userReference;
    }

    public void setUserReference(String userReference) {
        this.userReference = userReference;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public String getCustomReason() {
        return customReason;
    }

    public void setCustomReason(String customReason) {
        this.customReason = customReason;
    }

    @Override
    public String toString() {
        return "BulkFreeIssueImportRecord [meterNum=" + meterNum + ", units=" + units 
                + ", description=" + description + ", userReference=" + userReference 
                + ", reason=" + reason + ", customReason=" + customReason + "]";
    }
}
