package za.co.ipay.metermng.shared.dto.importfile;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

public class ImportFileListDto implements Serializable {

    private static final long serialVersionUID = -4215323064827848866L;
    
    private Integer resultCount = 0;
    private List<ImportFileDto> listImportFileDto = new ArrayList<ImportFileDto>();
    
    public ImportFileListDto() {
    }

    public Integer getResultCount() {
        return resultCount;
    }

    public void setResultCount(Integer resultCount) {
        this.resultCount = resultCount;
    }

    public List<ImportFileDto> getListImportFileDto() {
        return listImportFileDto;
    }

    public void setListImportFileDto(List<ImportFileDto> listImportFileDto) {
        this.listImportFileDto = listImportFileDto;
    }

}
