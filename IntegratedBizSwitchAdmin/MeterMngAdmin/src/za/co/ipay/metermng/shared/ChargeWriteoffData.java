package za.co.ipay.metermng.shared;

import java.util.Date;

import com.google.gwt.user.client.rpc.IsSerializable;

import za.co.ipay.metermng.datatypes.CyclicChargeCalcAtE;
import za.co.ipay.metermng.mybatis.generated.model.ChargeWriteoff;
import za.co.ipay.metermng.shared.dto.UsagePointData;

public class ChargeWriteoffData extends ChargeWriteoff implements IsSerializable {

    private static final long serialVersionUID = 417908156735530472L;

    private UsagePointData usagePointData;
    private CustomerTransItemData customerTransItemData;

    public ChargeWriteoffData() {
        super();
    }

    public ChargeWriteoffData(ChargeWriteoff chargeWriteoff) {
        super();
        this.setId(chargeWriteoff.getId());
        this.setCustomerAgreementId(chargeWriteoff.getCustomerAgreementId());
        this.setUsagePointId(chargeWriteoff.getUsagePointId());
        this.setMeterId(chargeWriteoff.getMeterId());
        this.setLastCyclicChargeDate(chargeWriteoff.getLastCyclicChargeDate());
        this.setNewCyclicChargeDate(chargeWriteoff.getNewCyclicChargeDate());
        this.setUserRecEntered(chargeWriteoff.getUserRecEntered());
        this.setTransDate(chargeWriteoff.getTransDate());
        this.setCycleId(chargeWriteoff.getCycleId());
        this.setChargeName(chargeWriteoff.getChargeName());
        this.setDescription(chargeWriteoff.getDescription());
        this.setTariff(chargeWriteoff.getTariff());
        this.setAmtInclTax(chargeWriteoff.getAmtInclTax());
        this.setAmtTax(chargeWriteoff.getAmtTax());
        this.setCyclicChargeCalcAt(chargeWriteoff.getCyclicChargeCalcAt());
        this.setWriteoffReasonLogId(chargeWriteoff.getWriteoffReasonLogId());
    }

    public ChargeWriteoffData(UsagePointData usagePointData, CustomerTransItemData customerTransItemData, String userRecEntered, 
            Date newCyclicChargeDate, CyclicChargeCalcAtE cyclicChargeCalcAtE, Long writeoffReasonLogId) {
        super();
        this.setNewCyclicChargeDate(newCyclicChargeDate);
        this.setUserRecEntered(userRecEntered);
        this.setTransDate(new Date());
        this.setWriteoffReasonLogId(writeoffReasonLogId);
        // Set UP properties
        setUsagePointData(usagePointData, cyclicChargeCalcAtE);
        // Set CustomerTransItem properties
        setCustomerTransItemData(customerTransItemData);
    }

    public UsagePointData getUsagePointData() {
        return usagePointData;
    }

    public void setUsagePointData(UsagePointData usagePointData, CyclicChargeCalcAtE cyclicChargeCalcAtE) {
        this.usagePointData = usagePointData;

        if (this.usagePointData != null) {
            this.setCustomerAgreementId(usagePointData.getCustomerAgreementId());
            this.setUsagePointId(usagePointData.getId());
            this.setMeterId(usagePointData.getMeterId());
            if (cyclicChargeCalcAtE.equals(CyclicChargeCalcAtE.V)) {
                this.setLastCyclicChargeDate(usagePointData.getLastCyclicChargeDate());
            } else {
                this.setLastCyclicChargeDate(usagePointData.getLastBillingCyclicChargeDate());
            }
            this.setCyclicChargeCalcAt(cyclicChargeCalcAtE.toString());
        }
    }

    public CustomerTransItemData getCustomerTransItemData() {
        return customerTransItemData;
    }

    public void setCustomerTransItemData(CustomerTransItemData customerTransItemData) {
        this.customerTransItemData = customerTransItemData;
        
        if (this.customerTransItemData != null) {
            this.setCycleId(customerTransItemData.getCycleId());
            this.setChargeName(customerTransItemData.getToken());
            this.setDescription(customerTransItemData.getDescription());
            this.setTariff(customerTransItemData.getTariff());
            this.setAmtInclTax(customerTransItemData.getAmtInclTax());
            this.setAmtTax(customerTransItemData.getAmtTax());
        }
    }

}
