package za.co.ipay.metermng.shared;

import java.io.Serializable;


public class TariffModel implements Serializable {
    
        private static final long serialVersionUID = 1L;
    
        private Long tariffId;
	private String tariffName;
	private String tariffDescription;
	private TariffType tariffType;
	private String calcContents;
	
	public Long getTariffId() {
		return tariffId;
	}
	public void setTariffId(Long id) {
		this.tariffId = id;
	}
	public String getTariffName() {
		return tariffName;
	}
	public void setTariffName(String name) {
		this.tariffName = name;
	}
	public String getTariffDescription() {
		return tariffDescription;
	}
	public void setTariffDescription(String desc) {
		this.tariffDescription = desc;
	}
	public TariffType getTariffType() {
		return tariffType;
	}
	public void setTariffType(TariffType tariffType) {
		this.tariffType = tariffType;
	}
	public String getCalcContents() {
		return calcContents;
	}
	public void setCalcContents(String contents) {
		this.calcContents = contents;
	}
	
	
	
}
