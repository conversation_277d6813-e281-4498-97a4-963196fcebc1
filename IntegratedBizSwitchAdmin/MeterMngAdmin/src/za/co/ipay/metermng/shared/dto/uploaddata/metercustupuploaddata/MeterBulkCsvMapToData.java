package za.co.ipay.metermng.shared.dto.uploaddata.metercustupuploaddata;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import za.co.ipay.metermng.mybatis.generated.model.AppSetting;
import za.co.ipay.metermng.shared.appsettings.CustomFieldDto;
import za.co.ipay.metermng.shared.bulkupload.BulkUploadException;
import za.co.ipay.metermng.shared.bulkupload.ICsvMapToData;

import static za.co.ipay.metermng.integration.offlinebulkuploads.metercustup.CustomFieldsUtil.CUSTOM_FIELD_METER_LENGTH;
import static za.co.ipay.metermng.integration.offlinebulkuploads.metercustup.CustomFieldsUtil.CUSTOM_FIELD_METER_PREFIX;
import static za.co.ipay.metermng.integration.offlinebulkuploads.metercustup.CustomFieldsUtil.CUSTOM_FIELD_STATUS_UNAVAILABLE;

public class MeterBulkCsvMapToData implements ICsvMapToData {

    private HashMap<String, String> csvHeadingToDataNameMap = new HashMap<String, String>();
    private HashMap<Integer, String> csvFieldMap = new HashMap<Integer, String>();
    private List<CustomFieldDto> meterCustomFieldDtoList;

    public MeterBulkCsvMapToData(List<AppSetting> appSettings, String customFieldStatusUnavailable) {
        meterCustomFieldDtoList = generateMeterCustomFields(appSettings, customFieldStatusUnavailable);
        //Meter
        csvHeadingToDataNameMap.put("Meter Type", "meterType");
        csvHeadingToDataNameMap.put("Meter Num", "meterNum");
        csvHeadingToDataNameMap.put("Serial Num", "serialNum");
        csvHeadingToDataNameMap.put("External Meter Id", "mrid");
        csvHeadingToDataNameMap.put("Meter Model Name", "meterModelName");
        csvHeadingToDataNameMap.put("Breaker Id", "breakerId");
        csvHeadingToDataNameMap.put("Meter Store Name", "endDeviceStoreName");
        csvHeadingToDataNameMap.put("Encryption Key", "encKey");
        csvHeadingToDataNameMap.put("Power Limit", "powerLimit");
        for (CustomFieldDto cfd : meterCustomFieldDtoList) {
            String fieldName = cfd.getName();
            String dataName = "meterCustom" + fieldName.substring(0, 1).toUpperCase() + fieldName.substring(1);
            switch (fieldName) {
                case "varchar1":
                case "varchar2":
                case "numeric1":
                case "numeric2":
                case "timestamp1":
                case "timestamp2":
                    csvHeadingToDataNameMap.put(cfd.getLabel().trim(), dataName);
                    break;
            }
        }

        //STS Meter
        csvHeadingToDataNameMap.put("STS Token Tech Code", "stsTokenTechCode");
        csvHeadingToDataNameMap.put("STS Algorithm Code", "stsAlgorithmCode");
        csvHeadingToDataNameMap.put("STS Supply Group Code", "stsSupplyGroupCode");
        csvHeadingToDataNameMap.put("STS Key Revision Num", "stsKeyRevisionNum");
        csvHeadingToDataNameMap.put("STS Tariff Index", "stsTariffIndex");

        //Meter URI
        csvHeadingToDataNameMap.put("Meter URI Address", "meterUriAddress");
        csvHeadingToDataNameMap.put("Meter URI Port", "meterUriPort");
        csvHeadingToDataNameMap.put("Meter URI Protocol", "meterUriProtocol");
        csvHeadingToDataNameMap.put("Meter URI Parameters", "meterUriParams");

    }

    private List<CustomFieldDto> generateMeterCustomFields(List<AppSetting> appSettings, String customFieldStatusUnavailable) {
        Map<String, CustomFieldDto> customFieldsDto = new HashMap<>();
        setupDefaultCustomStatus(customFieldsDto);
        for (AppSetting as : appSettings) {
            String appSettingName = as.getKey().toLowerCase();
            String meterPrefix = appSettingName.substring(0, CUSTOM_FIELD_METER_PREFIX.length());
            if (meterPrefix.equals(CUSTOM_FIELD_METER_PREFIX)) {
                populateCustomFieldSettings(as, CUSTOM_FIELD_METER_LENGTH, customFieldsDto);
            }
        }
        List<CustomFieldDto> meterCustomFieldDtoList = new ArrayList<>();
        for (CustomFieldDto cfd : customFieldsDto.values()) {
            if (cfd.getStatus().equals(CUSTOM_FIELD_STATUS_UNAVAILABLE) || cfd.getStatus().equals(customFieldStatusUnavailable)) {
                continue;
            }
            meterCustomFieldDtoList.add(cfd);
        }
        return meterCustomFieldDtoList;
    }

    private void setupDefaultCustomStatus(Map<String, CustomFieldDto> customFieldsRequiredMap) {
        addFieldToMap("varchar1", customFieldsRequiredMap);
        addFieldToMap("varchar2", customFieldsRequiredMap);
        addFieldToMap("numeric1", customFieldsRequiredMap);
        addFieldToMap("numeric2", customFieldsRequiredMap);
        addFieldToMap("timestamp1", customFieldsRequiredMap);
        addFieldToMap("timestamp2", customFieldsRequiredMap);
    }

    private void addFieldToMap(String fieldName, Map<String, CustomFieldDto> customFieldsRequiredMap) {
        customFieldsRequiredMap.put(fieldName, new CustomFieldDto(fieldName, CUSTOM_FIELD_STATUS_UNAVAILABLE));
    }

    private void populateCustomFieldSettings(AppSetting as, int startVarNameIndx, Map<String, CustomFieldDto> customFieldsRequiredMap) {
        String appSettingName = as.getKey().toLowerCase();
        String key = appSettingName.substring(startVarNameIndx, appSettingName.indexOf(".", startVarNameIndx));

        CustomFieldDto cfd = customFieldsRequiredMap.get(key);
        if (cfd == null)
            return;

        if (as.getKey().contains("status")) {
            cfd.setStatus(as.getValue().toLowerCase());
            cfd.setStatusId(as.getId());
            customFieldsRequiredMap.put(key, cfd);
        }

        if (as.getKey().contains("label")) {
            cfd.setLabel(as.getValue());
            customFieldsRequiredMap.put(key, cfd);
        }
    }

    public List<CustomFieldDto> getMeterCustomFieldDtoList() {
        return meterCustomFieldDtoList;
    }

    public HashMap<Integer, String> constructCsvFieldMap(String stringOfHeadingsCommaSeperated) throws BulkUploadException {
        String[] strArray = stringOfHeadingsCommaSeperated.split(",");
        for (int i = 0; i < strArray.length; i++) {
            String dataName = csvHeadingToDataNameMap.get(strArray[i].trim());
            if (dataName == null) {
                throw new BulkUploadException(ICsvMapToData.UNKNOWN_COLUMN_HEADING + ": >" + strArray[i].trim() + "<");
            }
            csvFieldMap.put(i, dataName);
        }
        return csvFieldMap;
    }

    public HashMap<String, String> getCsvHeadingToDataNameMap() {
        return csvHeadingToDataNameMap;
    }
}
