package za.co.ipay.metermng.shared.dto.dashboard;

import java.io.Serializable;
import java.util.ArrayList;

public class SalesPerResourceContainerDto implements Serializable {

    /**
     *<AUTHOR>
     */
    private static final long serialVersionUID = 1L;

    private String resourceName;
    private ArrayList<SalesPerResourceDto> dailySalesPerResourceDtos;

    private ArrayList<SalesPerResourceDto> monthlySalesPerResourceDtos;

    public SalesPerResourceContainerDto() {}

    /**
     * @return the resourceName
     */
    public String getResourceName() {
        return this.resourceName;
    }

    /**
     * @param resourceName the resourceName to set
     */
    public void setResourceName(String resourceName) {
        this.resourceName = resourceName;
    }

    /**
     * @return the dailySalesPerResourceDtos
     */
    public ArrayList<SalesPerResourceDto> getDailySalesPerResourceDtos() {
        return this.dailySalesPerResourceDtos;
    }

    /**
     * @param dailySalesPerResourceDtos the dailySalesPerResourceDtos to set
     */
    public void setDailySalesPerResourceDtos(
            ArrayList<SalesPerResourceDto> dailySalesPerResourceDtos) {
        this.dailySalesPerResourceDtos = dailySalesPerResourceDtos;
    }

    /**
     * @return the monthlySalesPerResourceDtos
     */
    public ArrayList<SalesPerResourceDto> getMonthlySalesPerResourceDtos() {
        return this.monthlySalesPerResourceDtos;
    }

    /**
     * @param monthlySalesPerResourceDtos the monthlySalesPerResourceDtos to set
     */
    public void setMonthlySalesPerResourceDtos(
            ArrayList<SalesPerResourceDto> monthlySalesPerResourceDtos) {
        this.monthlySalesPerResourceDtos = monthlySalesPerResourceDtos;
    }
}
