package za.co.ipay.metermng.shared.dto.importfile;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

public class ImportFileItemListDto implements Serializable {

    private static final long serialVersionUID = -4215323064827848866L;
    
    private Integer resultCount = 0;
    private List<ImportFileItemDto> listOfImportFileItemDto = new ArrayList<ImportFileItemDto>();
    
    public ImportFileItemListDto() {
    }

    public Integer getResultCount() {
        return resultCount;
    }

    public void setResultCount(Integer resultCount) {
        this.resultCount = resultCount;
    }

    public List<ImportFileItemDto> getListOfImportFileItemDto() {
        return listOfImportFileItemDto;
    }

    public void setListOfImportFileItemDto(
            List<ImportFileItemDto> listOfImportFileItemDto) {
        this.listOfImportFileItemDto = listOfImportFileItemDto;
    }

}
