package za.co.ipay.metermng.shared;

import za.co.ipay.metermng.mybatis.generated.model.CustomerAgreement;
import za.co.ipay.metermng.mybatis.generated.model.CustomerAgreementHist;

import com.google.gwt.user.client.rpc.IsSerializable;

public class CustomerAgreementHistData extends CustomerAgreementHist implements IsSerializable {
    
	private static final long serialVersionUID = 1899979204972738382L;
    
	protected boolean idChanged = false;
	protected boolean customerIdChanged = false;
	protected boolean mridChanged = false;
    protected boolean mridExternalChanged = false;
    protected boolean agreementRefChanged = false;
    protected boolean startDateChanged = false;
    protected boolean recordStatusChanged = false;
	protected String customerName;
    
	public CustomerAgreementHistData() {
	}
	
	public CustomerAgreementHistData(CustomerAgreement customerAgreement, String user) {
        this.setId(customerAgreement.getId());
        this.setCustomerId(customerAgreement.getCustomerId());
        this.setMrid(customerAgreement.getMrid());
        this.setMridExternal(customerAgreement.isMridExternal());
        this.setAgreementRef(customerAgreement.getAgreementRef());
        this.setStartDate(customerAgreement.getStartDate());
        this.setRecordStatus(customerAgreement.getRecordStatus());
        this.setUserRecEntered(user);
	}
    
	public CustomerAgreementHistData(CustomerAgreementHist customerAgreementHist) {
        this.setId(customerAgreementHist.getId());
        this.setCustomerId(customerAgreementHist.getCustomerId());
        this.setMrid(customerAgreementHist.getMrid());
        this.setMridExternal(customerAgreementHist.isMridExternal());
        this.setAgreementRef(customerAgreementHist.getAgreementRef());
        this.setStartDate(customerAgreementHist.getStartDate());
        this.setRecordStatus(customerAgreementHist.getRecordStatus());
        this.setUserRecEntered(customerAgreementHist.getUserRecEntered());
        this.setUserAction(customerAgreementHist.getUserAction());
        this.setDateRecModified(customerAgreementHist.getDateRecModified());
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    public boolean isIdChanged() {
        return idChanged;
    }

    public void setIdChanged(boolean idChanged) {
        this.idChanged = idChanged;
    }

    public boolean isCustomerIdChanged() {
        return customerIdChanged;
    }

    public void setCustomerIdChanged(boolean customerIdChanged) {
        this.customerIdChanged = customerIdChanged;
    }

    public boolean isMridChanged() {
        return mridChanged;
    }

    public void setMridChanged(boolean mridChanged) {
        this.mridChanged = mridChanged;
    }

    public boolean isMridExternalChanged() {
        return mridExternalChanged;
    }

    public void setMridExternalChanged(boolean mridExternalChanged) {
        this.mridExternalChanged = mridExternalChanged;
    }

    public boolean isAgreementRefChanged() {
        return agreementRefChanged;
    }

    public void setAgreementRefChanged(boolean agreementRefChanged) {
        this.agreementRefChanged = agreementRefChanged;
    }

    public boolean isStartDateChanged() {
        return startDateChanged;
    }

    public void setStartDateChanged(boolean startDateChanged) {
        this.startDateChanged = startDateChanged;
    }

    public boolean isRecordStatusChanged() {
        return recordStatusChanged;
    }

    public void setRecordStatusChanged(boolean recordStatusChanged) {
        this.recordStatusChanged = recordStatusChanged;
    }
	
	

    
}
