package za.co.ipay.metermng.shared.dto.dashboard;

import java.io.Serializable;
import java.math.BigDecimal;

public class TsDataBigDecimalDto implements Serializable{

    /**
     * <p>Generic data transfer object to transfer data representing a table with a string
     * column representing a timestamp (yyyy-mm-dd) or (yyyy-mm) and an BigDecimal column
     * representing a bigDecimal value. This is to be used for getting data for dashboard graph
     * panels.
     *
     * <AUTHOR>
     */
    private static final long serialVersionUID = 1L;

    private String tsData;
    private BigDecimal bigDec;

    public TsDataBigDecimalDto(){}

    public TsDataBigDecimalDto(String valueDate,
                               BigDecimal bigDec) {
        super();
        this.tsData = valueDate;
        this.bigDec = bigDec;
    }

    public String getTsData() {
        return this.tsData;
    }

    public void setTsData(String valueDate) {
        this.tsData = valueDate;
    }

    public BigDecimal getbigDec() {
        return this.bigDec;
    }

    public void setbigDec(BigDecimal bigDec) {
        this.bigDec = bigDec;
    }
}
