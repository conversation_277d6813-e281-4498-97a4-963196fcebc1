package za.co.ipay.metermng.shared.dto.customer;

import java.io.Serializable;

import za.co.ipay.gwt.common.shared.validation.ValidateUtil;

public class CustomerNotifyData implements Serializable {

    private static final long serialVersionUID = 1L;

    //The customer's details
    private Long customerId;
    private String firstName;
    private String lastName;
    //The customer's account details
    private Long customerAccountId;
    private String notifyEmail;
    private String notifySms;    
    
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("CustomerNotifyData: ");
        sb.append(" customerId: ").append(customerId);
        sb.append(" firstName: ").append(firstName);
        sb.append(" lastName: ").append(lastName);
        sb.append(" customerAccountId: ").append(customerAccountId);
        sb.append(" notifyEmail: ").append(notifyEmail);
        sb.append(" notifySms: ").append(notifySms);
        return sb.toString();
    }
    
    public String getCustomerNames() {
        String first = (getFirstName() != null ? getFirstName() : "");
        String last = (getLastName() != null ? getLastName() : "");
        return last+", "+first;
    }
    
    public String getCustomerDisplay() {
        String first = (getFirstName() != null ? getFirstName() : "");
        String last = (getLastName() != null ? getLastName() : "");
        StringBuilder notify = new StringBuilder();
        notify.append("(");
        if (getNotifyEmail() != null) {
            notify.append(getNotifyEmail());
            notify.append(" ");
        } 
        if (ValidateUtil.isNotNullOrBlank(getNotifySms())) {
            notify.append(getNotifySms());
        }
        notify.append(")");
        return first+" "+last+notify.toString();
    }

    public Long getCustomerId() {
        return customerId;
    }

    public void setCustomerId(Long customerId) {
        this.customerId = customerId;
    }

    public String getFirstName() {
        return firstName;
    }

    public void setFirstName(String firstName) {
        this.firstName = firstName;
    }

    public String getLastName() {
        return lastName;
    }

    public void setLastName(String lastName) {
        this.lastName = lastName;
    }

    public Long getCustomerAccountId() {
        return customerAccountId;
    }

    public void setCustomerAccountId(Long customerAccountId) {
        this.customerAccountId = customerAccountId;
    }

    public String getNotifyEmail() {
        return notifyEmail;
    }

    public void setNotifyEmail(String notifyEmail) {
        this.notifyEmail = notifyEmail;
    }

    public String getNotifySms() {
        return notifySms;
    }

    public void setNotifySms(String notifySms) {
        this.notifySms = notifySms;
    }  
}