package za.co.ipay.metermng.shared;

import za.co.ipay.metermng.mybatis.generated.model.BillPayTrans;

import com.google.gwt.user.client.rpc.IsSerializable;

public class BillPayTransData extends BillPayTrans implements IsSerializable {

    private static final long serialVersionUID = 7151421049647727245L;

    private String payTypeName;
    private String payTypeDetailsName;
    private String usagePointName;
    private String voteName;
    private String reversalReason;
    private String reversedBy;

    public BillPayTransData() {
        super();
    }

    public BillPayTransData(BillPayTrans billPayTrans) {
        super();
        this.setId(billPayTrans.getId());
        this.setClient(billPayTrans.getClient());
        this.setTerminal(billPayTrans.getTerminal());
        this.setProvider(billPayTrans.getProvider());
        this.setRefReceived(billPayTrans.getRefReceived());
        this.setTransDate(billPayTrans.getTransDate());
        this.setRevRefReceived(billPayTrans.getRevRefReceived());
        this.setRevReqReceived(billPayTrans.getRevReqReceived());
        this.setReversed(billPayTrans.isReversed());
        this.setReceiptNum(billPayTrans.getReceiptNum());
        this.setMeterNum(billPayTrans.getMeterNum());
        this.setCustAccNum(billPayTrans.getCustAccNum());
        this.setAmtTax(billPayTrans.getAmtTax());
        this.setAmtInclTax(billPayTrans.getAmtInclTax());
        this.setPayTypeId(billPayTrans.getPayTypeId());
        this.setPayTypeDetailsId(billPayTrans.getPayTypeDetailsId());
        this.setCustomerAgreementId(billPayTrans.getCustomerAgreementId());
        this.setUsagePointId(billPayTrans.getUsagePointId());
        this.setMeterId(billPayTrans.getMeterId());
        this.setVoteId(billPayTrans.getVoteId());
        this.setBillPayTransTypeId(billPayTrans.getBillPayTransTypeId());
    }

    public String getPayTypeName() {
        return payTypeName;
    }

    public void setPayTypeName(String payTypeName) {
        this.payTypeName = payTypeName;
    }

    public String getPayTypeDetailsName() {
        return payTypeDetailsName;
    }

    public void setPayTypeDetailsName(String payTypeDetailsName) {
        this.payTypeDetailsName = payTypeDetailsName;
    }

    public String getUsagePointName() {
        return usagePointName;
    }

    public void setUsagePointName(String usagePointName) {
        this.usagePointName = usagePointName;
    }

    public String getVoteName() {
        return voteName;
    }

    public void setVoteName(String voteName) {
        this.voteName = voteName;
    }

    public String getReversalReason() {
        return reversalReason;
    }

    public void setReversalReason(String reversalReason) {
        this.reversalReason = reversalReason;
    }

    public String getReversedBy() {
        return reversedBy;
    }

    public void setReversedBy(String reversedBy) {
        this.reversedBy = reversedBy;
    }
}
