package za.co.ipay.metermng.shared.dto;

import java.io.Serializable;
import java.util.ArrayList;

import za.co.ipay.gwt.common.shared.dto.IdNameDto;

public class PtrScreenDataDto implements Serializable {

    private static final long serialVersionUID = 1L;

    protected ArrayList<IdNameDto> serviceResources;
    protected ArrayList<IdNameDto> meterTypes;
    protected ArrayList<IdNameDto> paymentModes;
    protected ArrayList<IdNameDto> meterPhases;
    protected ArrayList<IdNameDto> dataDecoders;

    public ArrayList<IdNameDto> getServiceResources() {
        return serviceResources;
    }

    public void setServiceResources(ArrayList<IdNameDto> serviceResources) {
        this.serviceResources = serviceResources;
    }

    public ArrayList<IdNameDto> getMeterTypes() {
        return meterTypes;
    }

    public void setMeterTypes(ArrayList<IdNameDto> meterTypes) {
        this.meterTypes = meterTypes;
    }

    public ArrayList<IdNameDto> getPaymentModes() {
        return paymentModes;
    }

    public void setPaymentModes(ArrayList<IdNameDto> paymentModes) {
        this.paymentModes = paymentModes;
    }

    public ArrayList<IdNameDto> getMeterPhases() {
        return meterPhases;
    }

    public void setMeterPhases(ArrayList<IdNameDto> meterPhases) {
        this.meterPhases = meterPhases;
    }

    public ArrayList<IdNameDto> getDataDecoders() {
        return dataDecoders;
    }

    public void setDataDecoders(ArrayList<IdNameDto> dataDecoders) {
        this.dataDecoders = dataDecoders;
    }
}
