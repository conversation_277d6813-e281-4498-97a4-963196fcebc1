package za.co.ipay.metermng.shared;

import com.google.gwt.user.client.rpc.IsSerializable;
import za.co.ipay.metermng.shared.dto.MdcChannelReadingsDto;
import za.co.ipay.metermng.mybatis.generated.model.SpecialActionReasonsLog;
import za.co.ipay.metermng.mybatis.generated.model.StsMeter;
import za.co.ipay.metermng.shared.dto.UpGenGroupLinkData;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

public class MeterOnlineBulkData extends MeterOnlineBulkKernelData implements IsSerializable {

    private static final long serialVersionUID = -1L;
    
    //Location group link data
    ArrayList<Long> locationGroupDepthList;
    Long locationGroupTypeId;
    
    private HashMap<Long, UpGenGroupLinkData> upgengroups;
    
    private BigDecimal freeIssueUnits;
    private SpecialActionReasonsLog freeIssueTokenReasonsLog;
    private String freeIssueTokenUserReference;

    //free issue toekn gen returns:
    private TokenData tokenData;
    private StsMeter stsMeter;
    private boolean sendSms;
    
    //MdcChannelReading if Pricing Structure is RegRead
    List<MdcChannelReadingsDto> mdcChannelReadingsDtoList;

    //MDC
    private Long mdcId;
    private String mdcName;
    private boolean hasChannels;

    //UpMeterInstall
    private Long upMeterInstallId;

    public MeterOnlineBulkData() {
        super();
    }
    
    public void setKernelData(MeterOnlineBulkKernelData kernel) {
        this.setUsagePointId(kernel.getUsagePointId());
        this.setUsagePointName(kernel.getUsagePointName());
        this.setInstallationDate(kernel.getInstallationDate());
        this.setCustomerAgreementId(kernel.getCustomerAgreementId()); 
        this.setServiceLocationId(kernel.getServiceLocationId());
        this.setRecordStatus(kernel.getRecordStatus());
        
        this.setMeterId(kernel.getMeterId());
        this.setMeterNum(kernel.getMeterNum());
        this.setDeviceStoreId(kernel.getDeviceStoreId());
        this.setBreakerId(kernel.getBreakerId());
        this.setReplaceReasonLogId(kernel.getReplaceReasonLogId());
        this.setEncKey(kernel.getEncKey());
        
        this.setStsCurrSupplyGroupCodeId(kernel.getStsCurrSupplyGroupCodeId());
        this.setStsCurrSupplyGroupCode(kernel.getStsCurrSupplyGroupCode());
        this.setStsTokenTechCodeId(kernel.getStsTokenTechCodeId());
        this.setStsAlgorithmCodeId(kernel.getStsAlgorithmCodeId());
        this.setStsCurrKeyRevisionNum(kernel.getStsCurrKeyRevisionNum());
        this.setStsCurrTariffIndex(kernel.getStsCurrTariffIndex());

        this.setMeterUriAddress(kernel.getMeterUriAddress());
        this.setMeterUriPort(kernel.getMeterUriPort());
        this.setMeterUriProtocol(kernel.getMeterUriProtocol());
        this.setMeterUriParams(kernel.getMeterUriParams());

        this.setMeterTypeId(kernel.getMeterTypeId());
        this.setMeterTypeName(kernel.getMeterTypeName());
        
        this.setMeterModelId(kernel.getMeterModelId());
        this.setMeterModelName(kernel.getMeterModelName());
        
        this.setSurname(kernel.getSurname());
        this.setPhone1(kernel.getPhone1());
        
        this.setCurrentPricingStructureId(kernel.getCurrentPricingStructureId());
        this.setCurrentPricingStructureName(kernel.getCurrentPricingStructureName());
        this.setUpPricingStructureData(kernel.getUpPricingStructureData());
        
        this.setSuiteNum(kernel.getSuiteNum());
        
        this.setDeviceStoreName(kernel.getDeviceStoreName());
    }
    
    public HashMap<Long, UpGenGroupLinkData> getUpgengroups() {
        return upgengroups;
    }

    public void setUpgengroups(HashMap<Long, UpGenGroupLinkData> usagepointgroups) {
        this.upgengroups = usagepointgroups;
    }

    public UpGenGroupLinkData getSelectedGroupByGroupType(Long groupTypeId) {
        if (upgengroups == null)
            return null;
        return upgengroups.get(groupTypeId);
    }

    public ArrayList<UpGenGroupLinkData> getSelectedGroups() {
        if (upgengroups == null) {
            return null;
        }
        ArrayList<UpGenGroupLinkData> thelist = new ArrayList<UpGenGroupLinkData>(upgengroups.values());
        return thelist;
    }

    public ArrayList<Long> getLocationDepthList() {
        return locationGroupDepthList;
    }

    public void setLocationGroupDepthList(ArrayList<Long> locationGroupDepthList) {
        this.locationGroupDepthList = locationGroupDepthList;
    }

    public Long getLocationGroupTypeId() {
        return locationGroupTypeId;
    }

    public void setLocationGroupTypeId(Long locationGroupTypeId) {
        this.locationGroupTypeId = locationGroupTypeId;
    }
    
    public BigDecimal getFreeIssueUnits() {
        return freeIssueUnits;
    }

    public void setFreeIssueUnits(BigDecimal freeIssueUnits) {
        this.freeIssueUnits = freeIssueUnits;
    }

    public SpecialActionReasonsLog getFreeIssueTokenReasonsLog() {
        return freeIssueTokenReasonsLog;
    }

    public void setFreeIssueTokenReasonsLog(SpecialActionReasonsLog freeIssueTokenReasonsLog) {
        this.freeIssueTokenReasonsLog = freeIssueTokenReasonsLog;
    }

    public String getFreeIssueTokenUserReference() {
        return freeIssueTokenUserReference;
    }

    public void setFreeIssueTokenUserReference(String freeIssueTokenUserReference) {
        this.freeIssueTokenUserReference = freeIssueTokenUserReference;
    }

    
    public StsMeter getStsMeter() {
        return stsMeter;
    }

    public void setStsMeter(StsMeter stsMeter) {
        this.stsMeter = stsMeter;
    }

    public TokenData getTokenData() {
        return tokenData;
    }

    public void setTokenData(TokenData tokenData) {
        this.tokenData = tokenData;
    }

    public boolean isSendSms() {
        return sendSms;
    }

    public void setSendSms(boolean sendSms) {
        this.sendSms = sendSms;
    }

    public List<MdcChannelReadingsDto> getMdcChannelReadingsDtoList() {
        return mdcChannelReadingsDtoList;
    }

    public void setMdcChannelReadingsDtoList(List<MdcChannelReadingsDto> mdcChannelReadingsDtoList) {
        this.mdcChannelReadingsDtoList = mdcChannelReadingsDtoList;
    }
    public Long getMdcId() {
        return mdcId;
    }

    public void setMdcId(Long mdcId) {
        this.mdcId = mdcId;
    }

    public String getMdcName() {
        return mdcName;
    }

    public void setMdcName(String mdcName) {
        this.mdcName = mdcName;
    }

    public boolean isHasChannels() {
        return hasChannels;
    }

    public void setHasChannels(boolean hasChannels) {
        this.hasChannels = hasChannels;
    }

    public Long getUpMeterInstallId() {
        return upMeterInstallId;
    }

    public void setUpMeterInstallId(Long upMeterInstallId) {
        this.upMeterInstallId = upMeterInstallId;
    }

    @Override
    public String toString() {
        return "MeterOnlineBulkData [kernelData=" + super.toString() + ", locationGroupDepthList="
                + locationGroupDepthList + ", locationGroupTypeId="
                + locationGroupTypeId + ", upgengroups=" + upgengroups
                + ", freeIssueUnits="
                + freeIssueUnits + ", freeIssueTokenReasonsLog="
                + freeIssueTokenReasonsLog + ", freeIssueTokenUserReference="
                + freeIssueTokenUserReference + ", tokenData=" + tokenData
                + ", stsMeter=" + stsMeter + ", sendSms=" + sendSms
                + ", mdcChannelReadingsDtoList=" + mdcChannelReadingsDtoList
                + ", mdcId=" + mdcId + ", mdcName=" + mdcName + ", hasChannels="
                + hasChannels + ", upMeterInstallId=" + upMeterInstallId + "]";
    }

}
