package za.co.ipay.metermng.shared;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.logging.Logger;

import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.rpc.callback.ClientCallback;
import za.co.ipay.metermng.mybatis.custom.model.MeterDto;
import za.co.ipay.metermng.mybatis.generated.model.AppSetting;
import za.co.ipay.metermng.shared.dto.meter.MeterSearchDto;

import com.google.gwt.user.client.ui.SuggestOracle;

public class BaseMeterSuggestOracle extends SuggestOracle {

    protected ClientFactory clientFactory;
    private boolean isEnabled = true;
    private int characterThreshold = 2;
    private Date appSettingsChangedDate = null;

    private static Logger logger = Logger.getLogger(BaseMeterSuggestOracle.class.getName());

    public BaseMeterSuggestOracle(ClientFactory clientFactory) {
        this.clientFactory = clientFactory;
        updateSuggestionBoxSettings(null, null);
    }

    @Override
    public void requestSuggestions(final Request request, final Callback callback) {
        if (clientFactory.hasAppSettingsChanged(appSettingsChangedDate)) {
            updateSuggestionBoxSettings(request, callback);
        } else {
            if (isEnabled && request.getQuery().trim().length() >= characterThreshold) {
                getSuggestions(request, callback);
            } else {
                Response response = new Response(Collections.<Suggestion>emptyList());
                callback.onSuggestionsReady(request, response);
            }
        }
    }

    protected void getSuggestions(final Request request, final Callback callback) {
        logger.info("Getting meter suggestions: " + request.getQuery());
        clientFactory.getSearchRpc().getMetersAssignedToUpSuggestions(new MeterSearchDto(request.getQuery()),
                request.getLimit(), new ClientCallback<List<MeterDto>>() {
                    @Override
                    public void onSuccess(List<MeterDto> meters) {
                        ArrayList<Suggestion> result = new ArrayList<Suggestion>();
                        for (MeterDto meter : meters) {
                            result.add(new MeterSuggestion(meter));
                        }
                        Response response = new Response(result);
                        callback.onSuggestionsReady(request, response);
                    }
                });
    }

    private void updateSuggestionBoxSettings(final Request request, final Callback callback) {
        clientFactory.getAppSettingRpc().getAppSettingByKey(
                MeterMngStatics.APP_SETTING_SEARCH_BY_METER_NUMBER_AUTOMATICALLY, new ClientCallback<AppSetting>() {
                    @Override
                    public void onSuccess(AppSetting result) {
                        appSettingsChangedDate = new Date();
                        if ("false".equalsIgnoreCase(result.getValue())) {
                            isEnabled = false;
                        } else {
                            isEnabled = true;
                            clientFactory.getAppSettingRpc().getAppSettingByKey(
                                    MeterMngStatics.APP_SETTING_SEARCH_BY_METER_NUMBER_CHARACTER_THRESHOLD,
                                    new ClientCallback<AppSetting>() {
                                        @Override
                                        public void onSuccess(AppSetting result) {
                                            characterThreshold = Integer.parseInt(result.getValue());
                                            if (request != null) {
                                                requestSuggestions(request, callback);
                                            }
                                        }
                                    });
                        }
                    }
                });
    }
}
