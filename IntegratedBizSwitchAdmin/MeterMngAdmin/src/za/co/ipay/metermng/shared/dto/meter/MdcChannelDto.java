package za.co.ipay.metermng.shared.dto.meter;

import java.io.Serializable;
import java.util.List;

import za.co.ipay.gwt.common.shared.dto.IdNameDto;
import za.co.ipay.metermng.datatypes.RecordStatus;
import za.co.ipay.metermng.mybatis.custom.model.HasStatus;
import za.co.ipay.metermng.mybatis.generated.model.MdcChannel;

public class MdcChannelDto implements Serializable, HasStatus {

    private static final long serialVersionUID = 1L;

    private MdcChannel mdcChannel;
    private List<IdNameDto> billingDetList;
    private String meterReadingTypeName;
    private String timeIntervalName;
    private RecordStatus recordStatus;
    
    private List<String> overrideMeterModelsList;
    
    public MdcChannelDto() {
    }
    
    public MdcChannelDto(MdcChannel mdcChannel, List<IdNameDto> billingDetNameList, 
                                                String meterReadingTypeName, 
                                                String timeIntervalName, 
                                                List<String> overrideMeterModelsList) {
        this.mdcChannel = mdcChannel;
        this.billingDetList = billingDetNameList;
        this.meterReadingTypeName = meterReadingTypeName;   //used by modelChannelConfig display
        this.timeIntervalName = timeIntervalName;
        this.overrideMeterModelsList = overrideMeterModelsList;
        setRecordStatus(mdcChannel.getRecordStatus());
    }

    
    public MdcChannel getMdcChannel() {
        return mdcChannel;
    }
    public void setMdcChannel(MdcChannel mdcChannel) {
        this.mdcChannel = mdcChannel;
    }

    public List<IdNameDto> getBillingDetList() {
        return billingDetList;
    }

    public void setBillingDetList(List<IdNameDto> billingDetList) {
        this.billingDetList = billingDetList;
    }

    public String getMeterReadingTypeName() {
        return meterReadingTypeName;
    }

    public void setMeterReadingTypeName(String meterReadingTypeName) {
        this.meterReadingTypeName = meterReadingTypeName;
    }

    public String getTimeIntervalName() {
        return timeIntervalName;
    }

    public void setTimeIntervalName(String timeIntervalName) {
        this.timeIntervalName = timeIntervalName;
    }

    @Override
    public RecordStatus getRecordStatus() {
        return mdcChannel.getRecordStatus();
    }

    @Override
    public void setRecordStatus(RecordStatus recordStatus) {
        this.recordStatus = recordStatus;
        mdcChannel.setRecordStatus(recordStatus);
    }

    public List<String> getOverrideMeterModelsList() {
        return overrideMeterModelsList;
    }

    public void setOverrideMeterModelsList(List<String> overrideMeterModelsList) {
        this.overrideMeterModelsList = overrideMeterModelsList;
    }
    
}
