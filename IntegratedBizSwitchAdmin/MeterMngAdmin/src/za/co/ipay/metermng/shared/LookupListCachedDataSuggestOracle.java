package za.co.ipay.metermng.shared;

import za.co.ipay.gwt.common.shared.LookupListItem;
import za.co.ipay.metermng.client.widget.suggestboxtree.CachedDataSuggestOracle;


import java.util.List;
import java.util.Objects;

public class LookupListCachedDataSuggestOracle extends CachedDataSuggestOracle<String, LookupListItem> {

    private final String label;

    public LookupListCachedDataSuggestOracle(List<LookupListItem> sourceData, String label) {
        super(sourceData);
        this.label = label;
    }

    @Override
    public String getLabel() {
        return label;
    }

    @Override
    public void findById(String id, FindSuggestionCallback callback) {
        for (LookupListItem lookupListItem : sourceData) {
            if(Objects.equals(id.toLowerCase(),lookupListItem.getValue().toLowerCase())) {
                callback.found(lookupListItem);
                return;
            }
        }
        callback.found(null);
    }


}
