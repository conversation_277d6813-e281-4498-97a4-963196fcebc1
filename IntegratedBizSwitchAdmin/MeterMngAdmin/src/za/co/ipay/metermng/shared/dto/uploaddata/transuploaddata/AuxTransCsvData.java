package za.co.ipay.metermng.shared.dto.uploaddata.transuploaddata;

import java.io.Serializable;
import java.util.HashMap;

public class AuxTransCsvData extends TransCsvData implements Serializable {

	private static final long serialVersionUID = 1L;

	private String auxAccountName;
	private String agreementRef;

	public AuxTransCsvData(HashMap<Integer, String> csvFieldMap, String stringOfFieldsCommaSeperated,
			boolean withErrorString) {
		super(csvFieldMap, stringOfFieldsCommaSeperated, withErrorString);

		this.setAuxAccountName(beanDataMap.get("auxAccountName"));
		this.setAgreementRef(beanDataMap.get("agreementRef"));
	}

	public String getAuxAccountName() {
		return auxAccountName;
	}

	public void setAuxAccountName(String auxAccountName) {
		this.auxAccountName = auxAccountName;
	}

	public String getAgreementRef() {
		return agreementRef;
	}

	public void setAgreementRef(String agreementRef) {
		this.agreementRef = agreementRef;
	}

	@Override
	public String toString() {
		return "AuxTransCsvData [auxAccountName=" + auxAccountName + ", agreementRef=" + agreementRef + ", "
				+ super.toString() + "]";
	}

}
