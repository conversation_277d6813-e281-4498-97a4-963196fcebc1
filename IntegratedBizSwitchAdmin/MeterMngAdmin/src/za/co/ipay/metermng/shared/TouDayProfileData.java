package za.co.ipay.metermng.shared;

import java.util.ArrayList;
import java.util.Collections;

import za.co.ipay.metermng.mybatis.generated.model.TouDayProfile;

import com.google.gwt.user.client.rpc.IsSerializable;

public class TouDayProfileData extends TouDayProfile implements IsSerializable {
    
    private static final long serialVersionUID = 8350375137175557533L;
    
    private ArrayList<TouDayProfileTimeData> dayProfileTimesList;
    
    public TouDayProfileData() {
        super();
    }
    
    public TouDayProfileData(TouDayProfile touDayProfile) {
        super();
        this.setId(touDayProfile.getId());
        this.setTouCalendarId(touDayProfile.getTouCalendarId());
        this.setName(touDayProfile.getName());
        this.setCode(touDayProfile.getCode());
    }

    public ArrayList<TouDayProfileTimeData> getDayProfileTimesList() {
        return dayProfileTimesList;
    }
    
    public void setDayProfileTimesList(ArrayList<TouDayProfileTimeData> dayProfileTimesList) {
        this.dayProfileTimesList = dayProfileTimesList;
    }
    
    
    public boolean isDayProfileComplete() {
        if (dayProfileTimesList == null || dayProfileTimesList.isEmpty()) {
            return false;
        } else {
            Collections.sort(dayProfileTimesList);
            TouDayProfileTimeData prevDpt = null;
            for (TouDayProfileTimeData tdptd : dayProfileTimesList) {
                if(prevDpt == null) {
                    // this is the first one, so must start with 00:00 hours
                    if(tdptd.getStartHour() != 0 || tdptd.getStartMinute() != 0) {
                        return false;
                    }
                } else {
                    if(prevDpt.getTouPeriodId().equals(tdptd.getTouPeriodId())) {
                        //fail  must merge them into one as it is redundant to have 2 entries
                    }
                    // this dp's start must be one minute ahead of the prevDp's end
                    int currentMinute = prevDpt.getEndMinute() == 59 ? 0 : prevDpt.getEndMinute() + 1;
                    int currentHour = prevDpt.getEndMinute() == 59 ? prevDpt.getEndHour() + 1 : prevDpt.getEndHour();
                    if(tdptd.getStartHour() != currentHour || tdptd.getStartMinute() != currentMinute) {
                        return false;
                    }                    
                    // can't go beyond 23 hours
                    if(tdptd.getEndHour() > 23) {
                        return false;
                    }
                }
                prevDpt = tdptd;
            }
            // last one must end with 23:59
            if(prevDpt == null || (prevDpt.getEndHour() != 23 && prevDpt.getEndMinute() != 59)) {
                return false;
            }
        }
        return true;
    }
}
