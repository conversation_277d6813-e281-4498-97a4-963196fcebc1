package za.co.ipay.metermng.shared.dto;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

/**
 * ScreenData is a basic wrapper around data that will be loaded server-side for a screen's fields such as drop-down fields, etc.
 * <AUTHOR>
 */
public class ScreenData implements Serializable {

    private static final long serialVersionUID = 1L;

    private Map<String, Object> data; 
    
    public ScreenData() {
        this.data = new HashMap<String, Object>();
    }
    
    public void addData(String key, Object value) {
        data.put(key, value);
    }
    
    public Object getData(String key) {
        return data.get(key);
    }

    public Map<String, Object> getData() {
        return data;
    }
}
