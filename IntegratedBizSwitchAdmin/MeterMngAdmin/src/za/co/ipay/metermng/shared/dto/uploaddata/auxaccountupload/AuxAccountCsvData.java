package za.co.ipay.metermng.shared.dto.uploaddata.auxaccountupload;

import java.io.Serializable;
import java.util.HashMap;

import za.co.ipay.metermng.shared.bulkupload.dto.BulkCsvData;

public class AuxAccountCsvData extends BulkCsvData implements Serializable {

	private static final long serialVersionUID = 1L;

	private String identifierType;
	private String identifier;
	private String auxAccountName;
	private String auxTypeName;
	private String auxAccountPriority;
	private String chargeScheduleName;
	private String principleAmount;
	private String balance;
	private String balanceType;
	private String suspendUntil;
	private String startDate;

	public AuxAccountCsvData(HashMap<Integer, String> csvFieldMap, String stringOfFieldsCommaSeperated, boolean withErrorString) {
		super(csvFieldMap, stringOfFieldsCommaSeperated, withErrorString);

		this.setIdentifierType(beanDataMap.get("identifierType"));
		this.setIdentifier(beanDataMap.get("identifier"));
		this.setAuxAccountName(beanDataMap.get("auxAccountName"));
		this.setAuxTypeName(beanDataMap.get("auxTypeName"));
		this.setAuxAccountPriority(beanDataMap.get("auxAccountPriority"));
		this.setChargeScheduleName(beanDataMap.get("chargeScheduleName"));
		this.setPrincipleAmount(beanDataMap.get("principleAmount"));
		this.setBalance(beanDataMap.get("balance"));
		this.setBalanceType(beanDataMap.get("balanceType"));
		this.setSuspendUntil(beanDataMap.get("suspendUntil"));
		this.setStartDate(beanDataMap.get("startDate"));
	}

	public String getIdentifierType() {
		return identifierType;
	}

	public void setIdentifierType(String identifierType) {
		this.identifierType = identifierType;
	}

	public String getIdentifier() {
		return identifier;
	}

	public void setIdentifier(String identifier) {
		this.identifier = identifier;
	}

	public String getAuxAccountName() {
		return auxAccountName;
	}

	public void setAuxAccountName(String auxAccountName) {
		this.auxAccountName = auxAccountName;
	}

	public String getAuxTypeName() {
		return auxTypeName;
	}

	public void setAuxTypeName(String auxTypeName) {
		this.auxTypeName = auxTypeName;
	}

	public String getAuxAccountPriority() {
		return auxAccountPriority;
	}

	public void setAuxAccountPriority(String auxAccountPriority) {
		this.auxAccountPriority = auxAccountPriority;
	}

	public String getChargeScheduleName() {
		return chargeScheduleName;
	}

	public void setChargeScheduleName(String chargeScheduleName) {
		this.chargeScheduleName = chargeScheduleName;
	}

	public String getPrincipleAmount() {
		return principleAmount;
	}

	public void setPrincipleAmount(String principleAmount) {
		this.principleAmount = principleAmount;
	}

	public String getBalance() {
		return balance;
	}

	public void setBalance(String balance) {
		this.balance = balance;
	}

	public String getBalanceType() {
		return balanceType;
	}

	public void setBalanceType(String balanceType) {
		this.balanceType = balanceType;
	}

	public String getSuspendUntil() {
		return suspendUntil;
	}

	public void setSuspendUntil(String suspendUntil) {
		this.suspendUntil = suspendUntil;
	}

    public String getStartDate() {
        return startDate;
    }

    public void setStartDate(String startDate) {
        this.startDate = startDate;
    }
}
