package za.co.ipay.metermng.shared;

import com.google.gwt.user.client.rpc.IsSerializable;

import za.co.ipay.metermng.mybatis.generated.model.StsMeter;
import za.co.ipay.metermng.mybatis.generated.model.StsMeterHist;

public class STSMeterHistData extends StsMeterHist implements IsSerializable {
	

	private static final long serialVersionUID = 903068560180520208L;
	
	protected boolean idChanged = false;
	protected boolean mridChanged = false;
	protected boolean mridExternalChanged = false;
	protected boolean serialNumChanged = false;
	protected boolean meterNumChanged = false;
	protected boolean meterNumChecksumChanged = false;
	protected boolean dateManufacturedChanged = false;
	protected boolean stsForceKeychangeAfterChanged = false;
	protected boolean stsTokenTechCodeChanged = false;
	protected boolean stsAlgorithmCodeChanged = false;
	protected boolean stsPrevSupplyGroupIdChanged = false;
	protected boolean stsPrevSupplyGroupCodeChanged = false;
	protected boolean stsPrevKeyRevisionNumChanged = false;
	protected boolean stsPrevTariffIndexChanged = false;
	protected boolean stsCurrSupplyGroupIdChanged = false;
	protected boolean stsCurrSupplyGroupCodeChanged = false;
	protected boolean stsCurrKeyRevisionNumChanged = false;
	protected boolean stsCurrTariffIndexChanged = false;
	protected boolean stsNewSupplyGroupIdChanged = false;
	protected boolean stsNewSupplyGroupCodeChanged = false;
	protected boolean stsNewKeyRevisionNumChanged = false;
	protected boolean stsNewTariffIndexChanged = false;
	protected boolean recordStatusChanged = false;
	protected boolean endDeviceStoreChanged = false;
	
	protected String tokentechcode;
	protected String algCode;
	protected String endDeviceStoreName;
    
	public STSMeterHistData() {
		
	}
	
	
    public STSMeterHistData(StsMeter stsmeter, String user) {
    	this.setId(stsmeter.getId());
    	this.setMrid(stsmeter.getMrid());
    	this.setMridExternal(stsmeter.isMridExternal());
    	this.setSerialNum(stsmeter.getSerialNum());
    	this.setMeterNum(stsmeter.getMeterNum());
    	this.setMeterNumChecksum(stsmeter.getMeterNumChecksum());
    	this.setStsForceKeychangeAfter(stsmeter.getStsForceKeychangeAfter());
    	this.setStsTokenTechId(stsmeter.getStsTokenTechId());
    	this.setStsAlgorithmCodeId(stsmeter.getStsAlgorithmCodeId());
    	this.setStsPrevSupplyGroupId(stsmeter.getStsPrevSupplyGroupId());
    	this.setStsPrevSupplyGroupCode(stsmeter.getStsPrevSupplyGroupCode());
    	this.setStsPrevKeyRevisionNum(stsmeter.getStsPrevKeyRevisionNum());
    	this.setStsPrevTariffIndex(stsmeter.getStsCurrTariffIndex());
    	this.setStsCurrSupplyGroupId(stsmeter.getStsCurrSupplyGroupId());
    	this.setStsCurrSupplyGroupCode(stsmeter.getStsCurrSupplyGroupCode());
    	this.setStsCurrKeyRevisionNum(stsmeter.getStsCurrKeyRevisionNum());
    	this.setStsCurrTariffIndex(stsmeter.getStsCurrTariffIndex());
    	this.setStsNewSupplyGroupId(stsmeter.getStsNewSupplyGroupId());
    	this.setStsNewSupplyGroupCode(stsmeter.getStsCurrSupplyGroupCode());
    	this.setStsNewKeyRevisionNum(stsmeter.getStsNewKeyRevisionNum());
    	this.setStsNewTariffIndex(stsmeter.getStsNewTariffIndex());
    	this.setRecordStatus(stsmeter.getRecordStatus());
        this.setEndDeviceStoreId(stsmeter.getEndDeviceStoreId());
    	this.setUserRecEntered(user);
        
    }

    public STSMeterHistData(StsMeterHist stsmeterhist) {
    	this.setId(stsmeterhist.getId());
    	this.setMrid(stsmeterhist.getMrid());
    	this.setMridExternal(stsmeterhist.isMridExternal());
    	this.setSerialNum(stsmeterhist.getSerialNum());
    	this.setMeterNum(stsmeterhist.getMeterNum());
    	this.setMeterNumChecksum(stsmeterhist.getMeterNumChecksum());
    	this.setStsForceKeychangeAfter(stsmeterhist.getStsForceKeychangeAfter());
    	this.setStsTokenTechId(stsmeterhist.getStsTokenTechId());
    	this.setStsAlgorithmCodeId(stsmeterhist.getStsAlgorithmCodeId());
    	this.setStsPrevSupplyGroupId(stsmeterhist.getStsPrevSupplyGroupId());
    	this.setStsPrevSupplyGroupCode(stsmeterhist.getStsPrevSupplyGroupCode());
    	this.setStsPrevKeyRevisionNum(stsmeterhist.getStsPrevKeyRevisionNum());
    	this.setStsPrevTariffIndex(stsmeterhist.getStsCurrTariffIndex());
    	this.setStsCurrSupplyGroupId(stsmeterhist.getStsCurrSupplyGroupId());
    	this.setStsCurrSupplyGroupCode(stsmeterhist.getStsCurrSupplyGroupCode());
    	this.setStsCurrKeyRevisionNum(stsmeterhist.getStsCurrKeyRevisionNum());
    	this.setStsCurrTariffIndex(stsmeterhist.getStsCurrTariffIndex());
    	this.setStsNewSupplyGroupId(stsmeterhist.getStsNewSupplyGroupId());
    	this.setStsNewSupplyGroupCode(stsmeterhist.getStsCurrSupplyGroupCode());
    	this.setStsNewKeyRevisionNum(stsmeterhist.getStsNewKeyRevisionNum());
    	this.setStsNewTariffIndex(stsmeterhist.getStsNewTariffIndex());
    	this.setRecordStatus(stsmeterhist.getRecordStatus());
    	this.setEndDeviceStoreId(stsmeterhist.getEndDeviceStoreId());
    	this.setUserRecEntered(stsmeterhist.getUserRecEntered());
    	this.setUserAction(stsmeterhist.getUserAction());
    	this.setDateRecModified(stsmeterhist.getDateRecModified());
    }
    
	public boolean isIdChanged() {
		return idChanged;
	}

	public void setIdChanged(boolean idChanged) {
		this.idChanged = idChanged;
	}

	public boolean isMridChanged() {
		return mridChanged;
	}

	public void setMridChanged(boolean mridChanged) {
		this.mridChanged = mridChanged;
	}

	public boolean isMridExternalChanged() {
		return mridExternalChanged;
	}

	public void setMridExternalChanged(boolean mridExternalChanged) {
		this.mridExternalChanged = mridExternalChanged;
	}

	public boolean isSerialNumChanged() {
		return serialNumChanged;
	}

	public void setSerialNumChanged(boolean serialNumChanged) {
		this.serialNumChanged = serialNumChanged;
	}

	public boolean isMeterNumChanged() {
		return meterNumChanged;
	}

	public void setMeterNumChanged(boolean meterNumChanged) {
		this.meterNumChanged = meterNumChanged;
	}

	public boolean isMeterNumChecksumChanged() {
		return meterNumChecksumChanged;
	}

	public void setMeterNumChecksumChanged(boolean meterNumChecksumChanged) {
		this.meterNumChecksumChanged = meterNumChecksumChanged;
	}

	public boolean isDateManufacturedChanged() {
		return dateManufacturedChanged;
	}

	public void setDateManufacturedChanged(boolean dateManufacturedChanged) {
		this.dateManufacturedChanged = dateManufacturedChanged;
	}

	public boolean isStsForceKeychangeAfterChanged() {
		return stsForceKeychangeAfterChanged;
	}

	public void setStsForceKeychangeAfterChanged(
			boolean stsForceKeychangeAfterChanged) {
		this.stsForceKeychangeAfterChanged = stsForceKeychangeAfterChanged;
	}

	public boolean isStsTokenTechCodeChanged() {
		return stsTokenTechCodeChanged;
	}

	public void setStsTokenTechCodeChanged(boolean stsTokenTechCodeChanged) {
		this.stsTokenTechCodeChanged = stsTokenTechCodeChanged;
	}

	public boolean isStsAlgorithmCodeChanged() {
		return stsAlgorithmCodeChanged;
	}

	public void setStsAlgorithmCodeChanged(boolean stsAlgorithmCodeChanged) {
		this.stsAlgorithmCodeChanged = stsAlgorithmCodeChanged;
	}

	public boolean isStsPrevSupplyGroupIdChanged() {
		return stsPrevSupplyGroupIdChanged;
	}

	public void setStsPrevSupplyGroupIdChanged(boolean stsPrevSupplyGroupIdChanged) {
		this.stsPrevSupplyGroupIdChanged = stsPrevSupplyGroupIdChanged;
	}

	public boolean isStsPrevSupplyGroupCodeChanged() {
		return stsPrevSupplyGroupCodeChanged;
	}

	public void setStsPrevSupplyGroupCodeChanged(
			boolean stsPrevSupplyGroupCodeChanged) {
		this.stsPrevSupplyGroupCodeChanged = stsPrevSupplyGroupCodeChanged;
	}

	public boolean isStsPrevKeyRevisionNumChanged() {
		return stsPrevKeyRevisionNumChanged;
	}

	public void setStsPrevKeyRevisionNumChanged(boolean stsPrevKeyRevisionNumChanged) {
		this.stsPrevKeyRevisionNumChanged = stsPrevKeyRevisionNumChanged;
	}

	public boolean isStsPrevTariffIndexChanged() {
		return stsPrevTariffIndexChanged;
	}

	public void setStsPrevTariffIndexChanged(boolean stsPrevTariffIndexChanged) {
		this.stsPrevTariffIndexChanged = stsPrevTariffIndexChanged;
	}

	public boolean isStsCurrSupplyGroupIdChanged() {
		return stsCurrSupplyGroupIdChanged;
	}

	public void setStsCurrSupplyGroupIdChanged(boolean stsCurrSupplyGroupIdChanged) {
		this.stsCurrSupplyGroupIdChanged = stsCurrSupplyGroupIdChanged;
	}

	public boolean isStsCurrSupplyGroupCodeChanged() {
		return stsCurrSupplyGroupCodeChanged;
	}

	public void setStsCurrSupplyGroupCodeChanged(
			boolean stsCurrSupplyGroupCodeChanged) {
		this.stsCurrSupplyGroupCodeChanged = stsCurrSupplyGroupCodeChanged;
	}

	public boolean isStsCurrKeyRevisionNumChanged() {
		return stsCurrKeyRevisionNumChanged;
	}

	public void setStsCurrKeyRevisionNumChanged(boolean stsCurrKeyRevisionNumChanged) {
		this.stsCurrKeyRevisionNumChanged = stsCurrKeyRevisionNumChanged;
	}

	public boolean isStsCurrTariffIndexChanged() {
		return stsCurrTariffIndexChanged;
	}

	public void setStsCurrTariffIndexChanged(boolean stsCurrTariffIndexChanged) {
		this.stsCurrTariffIndexChanged = stsCurrTariffIndexChanged;
	}

	public boolean isStsNewSupplyGroupIdChanged() {
		return stsNewSupplyGroupIdChanged;
	}

	public void setStsNewSupplyGroupIdChanged(boolean stsNewSupplyGroupIdChanged) {
		this.stsNewSupplyGroupIdChanged = stsNewSupplyGroupIdChanged;
	}

	public boolean isStsNewSupplyGroupCodeChanged() {
		return stsNewSupplyGroupCodeChanged;
	}

	public void setStsNewSupplyGroupCodeChanged(boolean stsNewSupplyGroupCodeChanged) {
		this.stsNewSupplyGroupCodeChanged = stsNewSupplyGroupCodeChanged;
	}

	public boolean isStsNewKeyRevisionNumChanged() {
		return stsNewKeyRevisionNumChanged;
	}

	public void setStsNewKeyRevisionNumChanged(boolean stsNewKeyRevisionNumChanged) {
		this.stsNewKeyRevisionNumChanged = stsNewKeyRevisionNumChanged;
	}

	public boolean isStsNewTariffIndexChanged() {
		return stsNewTariffIndexChanged;
	}

	public void setStsNewTariffIndexChanged(boolean stsNewTariffIndexChanged) {
		this.stsNewTariffIndexChanged = stsNewTariffIndexChanged;
	}

	public boolean isRecordStatusChanged() {
		return recordStatusChanged;
	}

	public void setRecordStatusChanged(boolean recordStatusChanged) {
		this.recordStatusChanged = recordStatusChanged;
	}

	public String getTokentechcode() {
		return tokentechcode;
	}

	public void setTokentechcode(String tokentechcode) {
		this.tokentechcode = tokentechcode;
	}

	public String getAlgCode() {
		return algCode;
	}

	public void setAlgCode(String algCode) {
		this.algCode = algCode;
	}

    public boolean isEndDeviceStoreChanged() {
        return endDeviceStoreChanged;
    }

    public void setEndDeviceStoreChanged(boolean endDeviceStoreChanged) {
        this.endDeviceStoreChanged = endDeviceStoreChanged;
    }

    public String getEndDeviceStoreName() {
        return endDeviceStoreName;
    }

    public void setEndDeviceStoreName(String endDeviceStoreName) {
        this.endDeviceStoreName = endDeviceStoreName;
    }    
	
}
