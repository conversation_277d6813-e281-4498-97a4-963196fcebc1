package za.co.ipay.metermng.shared.dto.user;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

public class UserData implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;    
    private String username;
    private String email;
    
    public UserData() {
        this.id = null;
        this.username = "";
        this.email = "";
    }
    
    public UserData(Long id, String username, String email) {
        this.id = id;
        this.username = username;
        this.email = email;
    }
    
    public UserData(String email) {
        this.username = "";
        this.email = email;
    }
    
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("UserData: ");
        sb.append(" id:").append(id);
        sb.append(" username:").append(username);
        sb.append(" email:").append(email);
        return sb.toString();
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }
    
    public static ArrayList<UserData> from(List<String> addresses) {
        ArrayList<UserData> userDataList = new ArrayList<UserData>(addresses.size());
        for (String email : addresses) {
            userDataList.add(new UserData(email));
        }
        return userDataList;
    }
}
