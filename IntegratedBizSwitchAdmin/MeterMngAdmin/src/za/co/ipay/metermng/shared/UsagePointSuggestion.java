package za.co.ipay.metermng.shared;

import java.util.Date;

import com.google.gwt.user.client.rpc.IsSerializable;
import com.google.gwt.user.client.ui.SuggestOracle;

import za.co.ipay.metermng.shared.dto.UpPricingStructureData;

public class UsagePointSuggestion implements IsSerializable, SuggestOracle.Suggestion {
    
    private Long usagePointId;
    private String usagePointName;
    private Date lastCyclicChargeDate;
    private Long meterId;
    private Long customerAgreementId;
    private String surname; 
    private UpPricingStructureData upPricingStructureData;
    private String suggestion;
    
    public UsagePointSuggestion() {
    }

    public UsagePointSuggestion(Long usagePointId, String usagePointName, Date lastCyclicChargeDate) {
        this.usagePointId = usagePointId;
        this.usagePointName = usagePointName;
        this.lastCyclicChargeDate = lastCyclicChargeDate;
    }

    @Override
    public String getDisplayString() {
        return suggestion;
    }

    @Override
    public String getReplacementString() {
        return usagePointName;
    }

    public String getUsagePointName() {
        return usagePointName;
    }

    public void setUsagePointName(String usagePointName) {
        this.usagePointName = usagePointName;
    }

    public Long getUsagePointId() {
        return usagePointId;
    }

    public void setUsagePointId(Long usagePointId) {
        this.usagePointId = usagePointId;
    }

    public Long getMeterId() {
        return meterId;
    }

    public void setMeterId(Long meterId) {
        this.meterId = meterId;
    }

    public Long getCustomerAgreementId() {
        return customerAgreementId;
    }

    public void setCustomerAgreementId(Long customerAgreementId) {
        this.customerAgreementId = customerAgreementId;
    }

    public UpPricingStructureData getUpPricingStructureData() {
        return upPricingStructureData;
    }

    public void setUpPricingStructureData(UpPricingStructureData upPricingStructureData) {
        this.upPricingStructureData = upPricingStructureData;
    }

    public String getSuggestion() {
        return suggestion;
    }

    public void setSuggestion(String suggestion) {
        this.suggestion = suggestion;
    }

    public String getSurname() {
        return surname;
    }

    public void setSurname(String surname) {
        this.surname = surname;
    }

    public Date getLastCyclicChargeDate() {
        return lastCyclicChargeDate;
    }

    public void setLastCyclicChargeDate(Date lastCyclicChargeDate) {
        this.lastCyclicChargeDate = lastCyclicChargeDate;
    }
}
