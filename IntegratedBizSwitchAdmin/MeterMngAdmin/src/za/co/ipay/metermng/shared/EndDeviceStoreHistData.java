package za.co.ipay.metermng.shared;


import com.google.gwt.user.client.rpc.IsSerializable;

import za.co.ipay.metermng.mybatis.generated.model.EndDeviceStoreHist;

public class EndDeviceStoreHistData extends EndDeviceStoreHist implements IsSerializable {

    private static final long serialVersionUID = 7393464304725508641L;

    /**
     * private Long endDeviceStoreHistId;

    private Date dateRecModified;

    private String userRecEntered;

    private String userAction;

     */

    protected boolean nameChanged = false;
    protected boolean descriptionChanged = false;
    protected boolean locationIdChanged = false;
    protected boolean recordStatusChanged = false;
    
    public EndDeviceStoreHistData() {
        super();
    }
    
    public EndDeviceStoreHistData(EndDeviceStoreHist endDeviceStoreHist) {
        super();
        this.setId(endDeviceStoreHist.getId());
        this.setName(endDeviceStoreHist.getName());
        this.setDescription(endDeviceStoreHist.getDescription());
        this.setLocationId(endDeviceStoreHist.getLocationId());
        this.setGenGroupId(endDeviceStoreHist.getGenGroupId());
        this.setRecordStatus(endDeviceStoreHist.getRecordStatus());
        
        this.setUserRecEntered(endDeviceStoreHist.getUserRecEntered());
        this.setUserAction(endDeviceStoreHist.getUserAction());
        this.setDateRecModified(endDeviceStoreHist.getDateRecModified());
    }

    public boolean isNameChanged() {
        return nameChanged;
    }

    public boolean isDescriptionChanged() {
        return descriptionChanged;
    }

    public boolean isLocationIdChanged() {
        return locationIdChanged;
    }

    public boolean isRecordStatusChanged() {
        return recordStatusChanged;
    }

    public void setNameChanged(boolean nameChanged) {
        this.nameChanged = nameChanged;
    }

    public void setDescriptionChanged(boolean descriptionChanged) {
        this.descriptionChanged = descriptionChanged;
    }

    public void setLocationIdChanged(boolean locationIdChanged) {
        this.locationIdChanged = locationIdChanged;
    }

    public void setRecordStatusChanged(boolean recordStatusChanged) {
        this.recordStatusChanged = recordStatusChanged;
    }
    
    

   

}
