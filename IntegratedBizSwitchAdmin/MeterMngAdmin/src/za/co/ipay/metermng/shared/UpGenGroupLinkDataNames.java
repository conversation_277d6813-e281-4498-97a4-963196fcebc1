package za.co.ipay.metermng.shared;

import java.util.ArrayList;

import com.google.gwt.user.client.rpc.IsSerializable;

public class UpGenGroupLinkDataNames implements IsSerializable  {

    private static final long serialVersionUID = 8621174633194084536L;
    
    String groupTypeName;
    ArrayList<String> hierarchyNamesList;
    ArrayList<String> depthNamesList;
    Long groupTypeId;
    
    
    /* Example 
     * select * from up_gen_group_lnk where usage_point_id=7;
         up_gen_group_lnk_id | usage_point_id | gen_group_id 
         --------------------+----------------+--------------
                          15 |              7 |           12     [Medupi-Feeder1]
                  
         group_type = 4              [Networks] 
         hierarchyList = 16, 17, 19  [Powerplant,    Substation,             Feeder Station ]      
         depthList = 9, 11, 12       [Medupi,        Medupi-Sub1,            Medupi-Feeder1 ]
     */
    
    public UpGenGroupLinkDataNames() {
    }


    public String getGroupTypeName() {
        return groupTypeName;
    }


    public void setGroupTypeName(String groupTypeName) {
        this.groupTypeName = groupTypeName;
    }


    public ArrayList<String> getHierarchyNamesList() {
        return hierarchyNamesList;
    }


    public void setHierarchyNamesList(ArrayList<String> hierarchyNamesList) {
        this.hierarchyNamesList = hierarchyNamesList;
    }


    public ArrayList<String> getDepthNamesList() {
        return depthNamesList;
    }


    public void setDepthNamesList(ArrayList<String> depthNamesList) {
        this.depthNamesList = depthNamesList;
    }

    public Long getGroupTypeId() {
        return groupTypeId;
    }


    public void setGroupTypeId(Long groupTypeId) {
        this.groupTypeId = groupTypeId;
    }
    
}
