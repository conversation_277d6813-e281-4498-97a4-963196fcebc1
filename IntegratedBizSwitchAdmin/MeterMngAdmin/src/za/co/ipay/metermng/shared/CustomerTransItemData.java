package za.co.ipay.metermng.shared;

import com.google.gwt.user.client.rpc.IsSerializable;

import za.co.ipay.metermng.mybatis.generated.model.CustomerTransItem;
import za.co.ipay.metermng.mybatis.generated.model.TransItemType;

public class CustomerTransItemData extends CustomerTransItem implements IsSerializable {

   private static final long serialVersionUID = 7974719193888977446L;
    
   private TransItemType theTransItemType;

   
    public CustomerTransItemData() {
	super();
    }

    public CustomerTransItemData(CustomerTransItem customerTransItem) {
        super();
        this.setId(customerTransItem.getId());
        this.setCustomerTransId(customerTransItem.getCustomerTransId());
        this.setTransItemType(customerTransItem.getTransItemType());
        this.setAmtInclTax(customerTransItem.getAmtInclTax());
        this.setAmtTax(customerTransItem.getAmtTax());
        this.setToken(customerTransItem.getToken());
        this.setDescription(customerTransItem.getDescription());
        this.setReceiptNum(customerTransItem.getReceiptNum());
        this.setUnits(customerTransItem.getUnits());
        this.setMsg(customerTransItem.getMsg());
        this.setExpDate(customerTransItem.getExpDate());
        this.setTariff(customerTransItem.getTariff());
        this.setBsstDate(customerTransItem.getBsstDate());
        this.setOpenBalance(customerTransItem.getOpenBalance());
        this.setRemBalance(customerTransItem.getRemBalance());
        this.setTouTariffCalendarId(customerTransItem.getTouTariffCalendarId());
        this.setCycleId(customerTransItem.getCycleId());
    }
    
    public TransItemType getTransItemTypeDetails() {
	    return theTransItemType;
    }

    public void setTransItemTypeDetails(TransItemType transItemType) {
	this.theTransItemType = transItemType;
    }
}
