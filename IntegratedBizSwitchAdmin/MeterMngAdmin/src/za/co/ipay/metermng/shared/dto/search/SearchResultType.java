package za.co.ipay.metermng.shared.dto.search;

/**
 * SearchResultType defines the type of search results being returned from a specific search.
 * <AUTHOR>
 */
public enum SearchResultType {

    METER, METER_MODEL, CUSTOMER, CUSTOMER_ID_NUMBER, CUSTOMER_AGREEMENT, ACCOUNT_NAME, USAGE_POINT, PRICING_STRUCTURE, PAYMENT_MODE, LOCATION;

    public static SearchResultType getType(String t) {
        if (t != null && !t.trim().equals("")) {
            for(SearchResultType type : SearchResultType.values()) {
                if (type.name().equalsIgnoreCase(t)) {
                    return type;
                }
            }
        }
        return null;
    }    
}
