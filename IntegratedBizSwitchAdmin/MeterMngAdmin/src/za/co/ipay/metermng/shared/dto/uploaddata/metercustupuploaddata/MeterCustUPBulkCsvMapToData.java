package za.co.ipay.metermng.shared.dto.uploaddata.metercustupuploaddata;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import za.co.ipay.metermng.mybatis.generated.model.AppSetting;
import za.co.ipay.metermng.mybatis.generated.model.FormFields;
import za.co.ipay.metermng.shared.appsettings.CustomFieldDto;
import za.co.ipay.metermng.shared.bulkupload.ICsvMapToData;
import za.co.ipay.metermng.shared.bulkupload.dto.metercustupuploaddata.MeterCustUPCommonCsvMapToData;
import za.co.ipay.metermng.shared.bulkupload.metercustup.CsvColumnData;
import za.co.ipay.metermng.shared.group.GroupComponentType;
import za.co.ipay.metermng.shared.group.GroupTypeData;
import za.co.ipay.metermng.shared.userinterface.UserInterfaceFormFields;

public abstract class MeterCustUPBulkCsvMapToData extends MeterCustUPCommonCsvMapToData implements ICsvMapToData {

    public MeterCustUPBulkCsvMapToData(ArrayList<AppSetting> customFieldList, String customFieldsStatusUnavailable,
            List<GroupTypeData> groupTypeHierarchies, boolean enableNonBillable,
            Map<String, FormFields> formFieldsMap) {
        super(customFieldList, customFieldsStatusUnavailable, groupTypeHierarchies, enableNonBillable, formFieldsMap);
    }

    public void initDataMap(boolean isMetersInStore) {
        String requiredHeader = getHeaderText("template.required");
        // Meter
        csvHeadingToDataNameMap.put(getHeaderText("meternum"), new CsvColumnData("meterNum", true, getHeaderText("template.required.first")));

        if (!isMetersInStore) {  // These fields not required because meter already in store
            csvHeadingToDataNameMap.put(getHeaderText("metertype"),
                    new CsvColumnData("meterType", true, requiredHeader));
            addOptionalHeading(getHeaderText("serialnum"), "serialNum", UserInterfaceFormFields.SERIAL_NUMBER);
            csvHeadingToDataNameMap.put(getHeaderText("external.unique.id"), new CsvColumnData("meterMrid"));
            csvHeadingToDataNameMap.put(getHeaderText("metermodelname"),
                    new CsvColumnData("meterModelName", true, requiredHeader));
            csvHeadingToDataNameMap.put(getHeaderText("breakerid"), new CsvColumnData("breakerId"));
            csvHeadingToDataNameMap.put(getHeaderText("enc.key"), new CsvColumnData("encKey"));
            csvHeadingToDataNameMap.put(getHeaderText("powerlimit.key"), new CsvColumnData("powerLimit"));
            csvHeadingToDataNameMap.putAll(createColumnsForCustomFieldData(meterCustomFieldDtoList, "meterCustom", requiredHeader));

            // STS Meter
            String requiredStsHeader = getHeaderText("template.sts.required");
            csvHeadingToDataNameMap.put(getHeaderText("ststokentechcode"),
                    new CsvColumnData("stsTokenTechCode", true, requiredStsHeader));
            csvHeadingToDataNameMap.put(getHeaderText("stsalgorithmcode"),
                    new CsvColumnData("stsAlgorithmCode", true, requiredStsHeader));
            csvHeadingToDataNameMap.put(getHeaderText("stssupplygroupcode"),
                    new CsvColumnData("stsSupplyGroupCode", true, requiredStsHeader));
            csvHeadingToDataNameMap.put(getHeaderText("stskeyrevisionnum"),
                    new CsvColumnData("stsKeyRevisionNum", true, requiredStsHeader));
            csvHeadingToDataNameMap.put(getHeaderText("ststariffindex"),
                    new CsvColumnData("stsTariffIndex", true, requiredStsHeader));
        }

        // Meter URI
        csvHeadingToDataNameMap.put(getHeaderText("meter.uri.address"), new CsvColumnData("meterUriAddress"));
        csvHeadingToDataNameMap.put(getHeaderText("meter.uri.port"), new CsvColumnData("meterUriPort"));
        csvHeadingToDataNameMap.put(getHeaderText("meter.uri.protocol"), new CsvColumnData("meterUriProtocol"));
        csvHeadingToDataNameMap.put(getHeaderText("meter.uri.params"), new CsvColumnData("meterUriParams"));

        csvHeadingToDataNameMap.put(getHeaderText("usagepointname"), 
                generateColumnData("usagePointName", UserInterfaceFormFields.UP_NAME));
        csvHeadingToDataNameMap.put(getHeaderText("installationdate.format"),
                new CsvColumnData("installationDate", true, requiredHeader));
        csvHeadingToDataNameMap.put(getHeaderText("pricingstructurename"),
                new CsvColumnData("pricingStructureName", true, requiredHeader));
        csvHeadingToDataNameMap.put(getHeaderText("external.up.unique.id"), new CsvColumnData("upMrid"));
        addOptionalHeading(getHeaderText("uperfnumber"), "upErfNumber", UserInterfaceFormFields.UP_ERF_NUMBER);
        addOptionalHeading(getHeaderText("upstreetnum"), "upStreetNum", UserInterfaceFormFields.UP_STREET_NUM);
        addOptionalHeading(getHeaderText("upbuildingname"), "upBuildingName", UserInterfaceFormFields.UP_BUILDING_NAME);
        addOptionalHeading(getHeaderText("upsuitenum"), "upSuiteNum", UserInterfaceFormFields.UP_SUITE_NUM);
        addOptionalHeading(getHeaderText("upaddressline1"), "upAddressLine1", UserInterfaceFormFields.UP_ADDRESS_1);
        addOptionalHeading(getHeaderText("upaddressline2"), "upAddressLine2", UserInterfaceFormFields.UP_ADDRESS_2);
        addOptionalHeading(getHeaderText("upaddressline3"), "upAddressLine3", UserInterfaceFormFields.UP_ADDRESS_3);
        addOptionalHeading(getHeaderText("uplatitude"), "upLatitude", UserInterfaceFormFields.UP_LATITUDE);
        addOptionalHeading(getHeaderText("uplongitude"), "upLongitude", UserInterfaceFormFields.UP_LONGITUDE);

        // UP Location groups - add the csv column names to the list
        if (locationGroupWithHierarchy != null) {
            addGroupHierarchyData("UP", locationGroupWithHierarchy, GroupComponentType.LOCATION_GROUP);
        }

        csvHeadingToDataNameMap.putAll(createColumnsForCustomFieldData(upCustomFieldDtoList, "up", requiredHeader));

        csvHeadingToDataNameMap.put(getHeaderText("recordstatus"),
                new CsvColumnData("recordStatus", true, requiredHeader));

        // Units Account
        csvHeadingToDataNameMap.put(getHeaderText("unitsaccountname"), new CsvColumnData("unitsAccountName"));
        addOptionalHeading(getHeaderText("unitslowbalancethreshold"), "unitsLowBalanceThreshold",
                UserInterfaceFormFields.UNITS_ACC_LOW_BAL_THRESHOLD);
        addOptionalHeading(getHeaderText("unitsnotificationemail"), "unitsNotificationEmail",
                UserInterfaceFormFields.UNITS_ACC_NOTIF_EMAIL);
        addOptionalHeading(getHeaderText("unitsnotificationphone"), "unitsNotificationPhone",
                UserInterfaceFormFields.UNITS_ACC_NOTIF_PHONE);

        // Customer
        csvHeadingToDataNameMap.put(getHeaderText("customerkind"), new CsvColumnData("customerKind"));
        addOptionalHeading(getHeaderText("companyname"), "companyName", UserInterfaceFormFields.COMPANY_NAME);
        addOptionalHeading(getHeaderText("taxnum"), "taxNum", UserInterfaceFormFields.TAX_NUMBER);
        addOptionalHeading(getHeaderText("firstnames"), "firstnames", UserInterfaceFormFields.FIRST_NAMES);
        csvHeadingToDataNameMap.put(getHeaderText("surname"), new CsvColumnData("surname", true, requiredHeader));
        addOptionalHeading(getHeaderText("initials"), "initials", UserInterfaceFormFields.INITIALS);
        addOptionalHeading(getHeaderText("title"), "title", UserInterfaceFormFields.TITLE);
        addOptionalHeading(getHeaderText("idNumber"), "idNumber", UserInterfaceFormFields.ID_NUMBER);
        addOptionalHeading(getHeaderText("email1"), "email1", UserInterfaceFormFields.EMAIL_1);
        addOptionalHeading(getHeaderText("email2"), "email2", UserInterfaceFormFields.EMAIL_2);
        addOptionalHeading(getHeaderText("phone1"), "phone1", UserInterfaceFormFields.PHONE_1);
        addOptionalHeading(getHeaderText("phone2"), "phone2", UserInterfaceFormFields.PHONE_2);
        csvHeadingToDataNameMap.put(getHeaderText("cust.ref"), new CsvColumnData("customerReference"));
        csvHeadingToDataNameMap.put(getHeaderText("external.cust.unique.id"), new CsvColumnData("custMrid"));
        addOptionalHeading(getHeaderText("custerfnumber"), "custErfNumber", UserInterfaceFormFields.CUST_ERF_NUMBER);
        addOptionalHeading(getHeaderText("custstreetnum"), "custStreetNum", UserInterfaceFormFields.CUST_STREET_NUMBER);
        addOptionalHeading(getHeaderText("custbuildingname"), "custBuildingName",
                UserInterfaceFormFields.CUST_BUILDING_NAME);
        addOptionalHeading(getHeaderText("custsuitenum"), "custSuiteNum", UserInterfaceFormFields.CUST_SUITE_NUMBER);
        addOptionalHeading(getHeaderText("custaddressline1"), "custAddressLine1",
                UserInterfaceFormFields.CUST_ADDRESS_1);
        addOptionalHeading(getHeaderText("custaddressline2"), "custAddressLine2",
                UserInterfaceFormFields.CUST_ADDRESS_2);
        addOptionalHeading(getHeaderText("custaddressline3"), "custAddressLine3",
                UserInterfaceFormFields.CUST_ADDRESS_3);
        addOptionalHeading(getHeaderText("custlatitude"), "custLatitude", UserInterfaceFormFields.CUST_LATITUDE);
        addOptionalHeading(getHeaderText("custlongitude"), "custLongitude", UserInterfaceFormFields.CUST_LONGITUDE);
        // Cust Location groups - add the csv column names to the list
        if (locationGroupWithHierarchy != null) {
            addGroupHierarchyData("Cust", locationGroupWithHierarchy, GroupComponentType.LOCATION_GROUP);
        }
        csvHeadingToDataNameMap.putAll(createColumnsForCustomFieldData(customerCustomFieldDtoList, "cust", requiredHeader));

        // Customer Agreement
        csvHeadingToDataNameMap.put(getHeaderText("agreementref"), 
                generateColumnData("agreementRef", UserInterfaceFormFields.CUST_AGR_REF));
        addOptionalHeading(getHeaderText("startdate"), "startDate", UserInterfaceFormFields.CUST_AGR_START_DATE);
        if (this.enableNonBillable) {
            csvHeadingToDataNameMap.put(getHeaderText("billable"), new CsvColumnData("billable"));
        }

        // Customer Account
        csvHeadingToDataNameMap.put(getHeaderText("accountname"), 
                generateColumnData("accountName", UserInterfaceFormFields.CUST_ACC_NAME));
        csvHeadingToDataNameMap.put(getHeaderText("accountbalance"), new CsvColumnData("accountBalance"));
        addOptionalHeading(getHeaderText("lowbalancethreshold"), "lowBalanceThreshold",
                UserInterfaceFormFields.CUST_ACC_LOW_BAL_THRESHOLD);
        addOptionalHeading(getHeaderText("notificationemail"), "notificationEmail",
                UserInterfaceFormFields.CUST_ACC_NOTIF_EMAIL);
        addOptionalHeading(getHeaderText("notificationphone"), "notificationPhone",
                UserInterfaceFormFields.CUST_ACC_NOTIF_PHONE);

        // Usage Point Groups Hierarchies
        for (GroupTypeData gtd : usagePointGroupTypeHierarchies) {
            addGroupHierarchyData(null, gtd, GroupComponentType.USAGE_POINT_GROUP);
        }
    }

    private String getHeaderText(String headingKey) {
        return getMessage("bulk.upload." + headingKey);
    }

    private Map<String, CsvColumnData> createColumnsForCustomFieldData(List<CustomFieldDto> customFieldsDto,
            String dataNamePrefix, String columnInfoHeader) {
        Map<String, CsvColumnData> columnDataMap = new LinkedHashMap<>();
        for (CustomFieldDto cfd : customFieldsDto) {
            boolean customFieldRequired = "REQUIRED".equals(cfd.getStatus().trim().toUpperCase());
            String fieldName = cfd.getName();
            csvHeadingToDataNameMap.put(cfd.getLabel().trim(),
                    new CsvColumnData(dataNamePrefix + fieldName.substring(0, 1).toUpperCase() + fieldName.substring(1),
                            customFieldRequired, customFieldRequired ? columnInfoHeader : ""));
        }
        return columnDataMap;
    }

    protected abstract String getMessage(String key);
    
    private CsvColumnData generateColumnData(String fieldName, String staticNameRef) {
        if (formFieldsMap.get(staticNameRef).getValidationRegex() != null) {
            return new CsvColumnData(fieldName, true, getHeaderText("template.required"));
        }
        return new CsvColumnData(fieldName);
    }
}
