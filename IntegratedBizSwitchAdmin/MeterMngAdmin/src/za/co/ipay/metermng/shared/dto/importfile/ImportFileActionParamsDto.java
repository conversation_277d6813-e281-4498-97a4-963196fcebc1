package za.co.ipay.metermng.shared.dto.importfile;

import java.io.Serializable;

import za.co.ipay.metermng.mybatis.generated.model.ImportFile;
import za.co.ipay.metermng.shared.dto.BulkParamRecord;

public class ImportFileActionParamsDto implements Serializable{

    private static final long serialVersionUID = -1L;
    private ImportFile importFile;
    private String importFileTypeClass;
    private BulkParamRecord bulkParamRecord;
    
    public ImportFileActionParamsDto() {
        super();
    }

    public ImportFile getImportFile() {
        return importFile;
    }

    public void setImportFile(ImportFile importFile) {
        this.importFile = importFile;
    }

    public String getImportFileTypeClass() {
        return importFileTypeClass;
    }

    public void setImportFileTypeClass(String importFileTypeClass) {
        this.importFileTypeClass = importFileTypeClass;
    }

    public BulkParamRecord getBulkParamRecord() {
        return bulkParamRecord;
    }

    public void setBulkParamRecord(BulkParamRecord bulkParamRecord) {
        this.bulkParamRecord = bulkParamRecord;
    }
}
