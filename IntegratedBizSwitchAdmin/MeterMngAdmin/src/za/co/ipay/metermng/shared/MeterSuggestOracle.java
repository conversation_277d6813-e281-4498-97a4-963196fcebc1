package za.co.ipay.metermng.shared;

import java.util.ArrayList;
import java.util.List;
import java.util.logging.Logger;

import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.rpc.callback.ClientCallback;
import za.co.ipay.metermng.mybatis.custom.model.MeterDto;
import za.co.ipay.metermng.shared.dto.meter.MeterSearchDto;

import com.google.gwt.user.client.ui.ListBox;

public class MeterSuggestOracle extends BaseMeterSuggestOracle {

    private ListBox paymentModeBox;

    private ArrayList<Suggestion> storeSuggestions;
    private static Logger logger = Logger.getLogger(MeterSuggestOracle.class.getName());

    public MeterSuggestOracle(ClientFactory clientFactory) {
        super(clientFactory);
        this.paymentModeBox = null;
    }

    @Override
    protected void getSuggestions(final Request request, final Callback callback) {
        String paymentMode = getPaymentMode();
        logger.info("Getting meter suggestions: " + request.getQuery() + " " + paymentMode);
        clientFactory.getSearchRpc().getMeterSuggestions(new MeterSearchDto(request.getQuery(), paymentMode),
                request.getLimit(), new ClientCallback<List<MeterDto>>() {
                    @Override
                    public void onSuccess(List<MeterDto> meters) {
                        ArrayList<Suggestion> result = new ArrayList<Suggestion>();
                        for (MeterDto meter : meters) {
                            result.add(new MeterSuggestion(meter));
                        }
                        storeSuggestions = result;
                        Response response = new Response(result);
                        callback.onSuggestionsReady(request, response);
                    }
                });
    }

    private String getPaymentMode() {
        if (paymentModeBox == null) {
            return null;
        }
        int index = paymentModeBox.getSelectedIndex();
        if (index > -1) {
            return paymentModeBox.getValue(index);
        } else {
            return "";
        }
    }

    public void setPaymentModeBox(ListBox paymentModeBox) {
        this.paymentModeBox = paymentModeBox;
    }

    public MeterSuggestion getFirstSuggestion() {
        if (storeSuggestions == null || storeSuggestions.isEmpty()) {
            return null;
        }
        return (MeterSuggestion) storeSuggestions.get(0);
    }
}
