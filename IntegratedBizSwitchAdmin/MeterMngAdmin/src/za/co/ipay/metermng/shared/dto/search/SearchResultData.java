package za.co.ipay.metermng.shared.dto.search;

import java.io.Serializable;
import java.util.HashMap;

import com.google.gwt.place.shared.PlaceHistoryMapper;

import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.history.CustomerPlace;
import za.co.ipay.metermng.client.history.MeterPlace;
import za.co.ipay.metermng.client.history.UsagePointPlace;
import za.co.ipay.metermng.shared.MeterMngStatics;

/**
 * SearchResultData is a wrapper around some data retrieved as part of a search. 
 * <AUTHOR>
 */
public class SearchResultData implements Serializable {

    private static final long serialVersionUID = 1L;
    
    private SearchResultType type;
    private Long id;    
    private HashMap<String, Long> ids;
    private HashMap<String, Object> details;
    
    public SearchResultData() {
        this.ids = new HashMap<String, Long>();
        this.details = new HashMap<String, Object>();
    }
    
    public SearchResultData(SearchResultType type, Long id) {
        this.type = type;
        this.id = id;
        this.ids = new HashMap<String, Long>();
        this.details = new HashMap<String, Object>();
    }
    
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("SearchResultData");
        sb.append(" type:").append(type);
        sb.append(" id:").append(id);
        sb.append(" ids:").append(ids.toString());
        sb.append(" details:").append(details);
        return sb.toString();
    }
    
    public static String getHistoryToken(ClientFactory clientFactory, SearchResultType type, SearchResultData data,
            String currentToken) {
        PlaceHistoryMapper historyMapper = clientFactory.getPlaceHistoryMapper();
        switch (type) {
        case METER:
            String meterNumber = (String) data.getDetails().get(MeterMngStatics.METER_NUMBER_SEARCH);
            if (meterNumber != null) {
                return historyMapper.getToken(new MeterPlace(meterNumber, currentToken));
            } else {
                return null;
            }
        case ACCOUNT_NAME:
            // when multiple usage points (and thus possibly customer agreements / accounts)
            // are allowed a customer accountName PARTIAL search will pick a random usage
            // point so rather not put a link to customer Account name and let them open a
            // specific usage point to avoid randomness and hopefully reduce some confusion
            return getCustomerHistoryToken(clientFactory, data, historyMapper, currentToken,
                    MeterMngStatics.ACCOUNT_NAME_SEARCH, CustomerPlace.SEARCH_BY_ACCOUNT_NAME);
        case CUSTOMER_AGREEMENT:
            // when multiple usage points (and thus customer agreements) are allowed a
            // customer agreement PARTIAL search will pick a random usage point so rather
            // not put a link to customer agreement and let them open a specific usage point
            // to avoid randomness and hopefully reduce some confusion
            return getCustomerHistoryToken(clientFactory, data, historyMapper, currentToken,
                    MeterMngStatics.CUSTOMER_AGREEMENT_SEARCH, CustomerPlace.SEARCH_BY_AGREEMENT_REF);
        case CUSTOMER_ID_NUMBER:
            // for when searching from ID number on the menu search when multiple usage
            // points are allowed a customer PARTIAL search will pick a random usage point
            // so rather not put a link to customer and let them open a specific usage point
            // to avoid randomness and hopefully reduce some confusion
            return getCustomerHistoryToken(clientFactory, data, historyMapper, currentToken,
                    MeterMngStatics.CUSTOMER_ID_NUMBER_SEARCH, CustomerPlace.SEARCH_BY_ID_NUMBER);
        case CUSTOMER:
            // when multiple usage points are allowed a customer search will pick a random
            // usage point so rather not put a link to customer and let them open a specific
            // usage point to avoid randomness and hopefully reduce some confusion
            return getCustomerHistoryToken(clientFactory, data, historyMapper, currentToken,
                    MeterMngStatics.CUSTOMER_ID_SEARCH, CustomerPlace.SEARCH_BY_ID);
        case USAGE_POINT:
            return getUsagePointHistoryToken(data, historyMapper, currentToken);
        default:
            return null;
        }
    }

    private static String getUsagePointHistoryToken(SearchResultData data, PlaceHistoryMapper historyMapper,
            String currentToken) {
        Object usagePointName = data.getDetails().get(MeterMngStatics.USAGE_POINT_NAME_SEARCH);
        if (usagePointName == null) {
            return null;
        }
        return historyMapper.getToken(new UsagePointPlace(usagePointName.toString(), currentToken));
    }

    private static String getCustomerHistoryToken(ClientFactory clientFactory, SearchResultData data,
            PlaceHistoryMapper historyMapper, String currentToken, String detail, int searchType) {
        if (clientFactory.isEnableMultiUp()) {
            String value = getUsagePointHistoryToken(data, historyMapper, currentToken);
            if (value != null) return value;
        }
        if (clientFactory.isEnableMultiCustAgr()) {
           searchType = CustomerPlace.SEARCH_BY_AGREEMENT_REF;
           detail = MeterMngStatics.CUSTOMER_AGREEMENT_SEARCH;
        }
        Object term;
        if (MeterMngStatics.CUSTOMER_ID_SEARCH.equals(detail)) {
            if (data.getType() == SearchResultType.CUSTOMER) {
                term = data.getId();
            } else {
                term = data.getIds().get(MeterMngStatics.CUSTOMER_ID_SEARCH);
            }
        } else {
            term = data.getDetails().get(detail);
        }
        if (term == null) {
            return null;
        }
        return historyMapper.getToken(new CustomerPlace(term.toString(), searchType, currentToken));
    }
    
    public void addId(String name, Long id) {
        ids.put(name, id);
    }
    
    public void addDetail(String name, Object value) {
        details.put(name, value);
    }

    public SearchResultType getType() {
        return type;
    }

    public Long getId() {
        return id;
    }

    public HashMap<String, Object> getDetails() {
        return details;
    }

    public HashMap<String, Long> getIds() {
        return ids;
    }    
}
