package za.co.ipay.metermng.shared.dto.meter;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;

import za.co.ipay.metermng.mybatis.generated.model.MeterReadingType;

/**
 * MeterReadingsDto is a wrapper around data that can be displayed on a graph as meter reading points.
 * <AUTHOR>
 */
public class MeterReadingsDto implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    public static final String SINGLE_METER = "singleMeter";
    public static final String SUPER_METER = "superMeter";
    public static final String METER_TOTALS = "totals";

    //The type of meter readings.
    private MeterReadingType meterReadingType;
    //The initial start date for all the readings
    private Date startDate;
    //The last date for all the readings
    private Date endDate;
    //The smallest reading for all the readings
    private double minReading = 0.0;
    //The largest reading for all the readings
    private double maxReading = 0.0;
    //Readings for each meter number to be displayed on the graph
    private HashMap<String, ArrayList<MeterReadingDto>> readings;
    //The name of the balancing meter or super meter.
    private String balancingMeter;
    //The sub-meter numbers which is used if the readings are not added to the readings map. The map is to display all
    //the sub-meters readings but there might be hundreds of them so instead of getting and displaying all their 
    //readings, just keep track of their meter numbers.
    private ArrayList<String> subMeters;

    public MeterReadingsDto() {
        this.readings = new HashMap<String, ArrayList<MeterReadingDto>>();
        this.balancingMeter = "";
        this.subMeters = new ArrayList<String>();
    }
    
    public MeterReadingsDto(MeterReadingType meterReadingType, Date startDate, Date endDate, double minReading, double maxReading, String name, ArrayList<MeterReadingDto> readings) {
        this.meterReadingType = meterReadingType;
        this.startDate = startDate;
        this.endDate = endDate;
        this.minReading = minReading;
        this.maxReading = maxReading;    
        this.readings = new HashMap<String, ArrayList<MeterReadingDto>>();
        this.readings.put(name, readings);
        this.balancingMeter = "";
        this.subMeters = new ArrayList<String>();
    }
    
    public MeterReadingsDto(MeterReadingType meterReadingType, Date startDate, Date endDate, double minReading, double maxReading, HashMap<String, ArrayList<MeterReadingDto>> readings) {
        this.meterReadingType = meterReadingType;
        this.startDate = startDate;
        this.endDate = endDate;
        this.minReading = minReading;
        this.maxReading = maxReading;
        this.readings = readings;
        this.balancingMeter = "";
        this.subMeters = new ArrayList<String>();
    }
    
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("MeterReadingsDto:");
        sb.append(" meterReadingType:").append(meterReadingType);
        sb.append(" ").append(startDate);
        sb.append(" to ").append(endDate);
        sb.append(" ").append(minReading);
        sb.append(" - ").append(maxReading);
        sb.append(" readings:").append(readings.size());
        sb.append(" balancingMeter:").append(balancingMeter);
        sb.append(" subMeters:").append(subMeters.toString());
        return sb.toString();
    }

    public MeterReadingType getMeterReadingType() {
        return meterReadingType;
    }

    public void setMeterReadingType(MeterReadingType meterReadingType) {
        this.meterReadingType = meterReadingType;
    }

    public Date getStartDate() {
        return startDate;
    }

    public Date getEndDate() {
        return endDate;
    }

    public double getMinReading() {
        return minReading;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    public void setMinReading(double minReading) {
        this.minReading = minReading;
    }

    public void setMaxReading(double maxReading) {
        this.maxReading = maxReading;
    }

    public double getMaxReading() {
        return maxReading;
    }

    public HashMap<String, ArrayList<MeterReadingDto>> getReadings() {
        return readings;
    }

    public String getBalancingMeter() {
        return balancingMeter;
    }

    public void setBalancingMeter(String balancingMeter) {
        this.balancingMeter = balancingMeter;
    }

    public ArrayList<String> getSubMeters() {
        return subMeters;
    }    
}
