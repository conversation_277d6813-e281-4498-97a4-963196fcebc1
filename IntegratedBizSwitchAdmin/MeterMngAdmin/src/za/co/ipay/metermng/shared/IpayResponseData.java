package za.co.ipay.metermng.shared;

import com.google.gwt.user.client.rpc.IsSerializable;

public class IpayResponseData implements IsSerializable {
	
	private String resCode;
	private String resMsg;
	private String resRef;
	private String origRef;
	private String extraData;
    
    public String getResCode() {
        return resCode;
    }
    
    public void setResCode(String resCode) {
        this.resCode = resCode;
    }
    
    public String getResMsg() {
        return resMsg;
    }
    
    public void setResMsg(String msg) {
        this.resMsg = msg;
    }

    public String getResRef() {
        return resRef;
    }

    public void setResRef(String resRef) {
        this.resRef = resRef;
    }

	public String getOrigRef() {
		return origRef;
	}

	public void setOrigRef(String origRef) {
		this.origRef = origRef;
	}

    public String getExtraData() {
        return extraData;
    }

    public void setExtraData(String extraData) {
        this.extraData = extraData;
    }
	
}
