package za.co.ipay.metermng.shared.dto.tariff;

import java.io.Serializable;

import za.co.ipay.metermng.mybatis.generated.model.PricingStructure;
import za.co.ipay.metermng.mybatis.generated.model.TariffClass;
import za.co.ipay.metermng.shared.tariff.TariffWithData;

public class TariffPanelDto implements Serializable {

    private static final long serialVersionUID = 1L;
    
    private PricingStructure pricingStructure;
    private TariffClass tariffClass;
    private TariffWithData tariffWithData;
    
    public TariffPanelDto() {

    }

    public PricingStructure getPricingStructure() {
        return pricingStructure;
    }

    public void setPricingStructure(PricingStructure pricingStructure) {
        this.pricingStructure = pricingStructure;
    }

    public TariffClass getTariffClass() {
        return tariffClass;
    }

    public void setTariffClass(TariffClass tariffClass) {
        this.tariffClass = tariffClass;
    }

    public TariffWithData getTariffWithData() {
        return tariffWithData;
    }

    public void setTariffWithData(TariffWithData tariffWithData) {
        this.tariffWithData = tariffWithData;
    }

}
