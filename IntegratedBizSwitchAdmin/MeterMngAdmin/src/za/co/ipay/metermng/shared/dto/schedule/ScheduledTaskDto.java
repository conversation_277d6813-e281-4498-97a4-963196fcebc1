package za.co.ipay.metermng.shared.dto.schedule;

import java.io.Serializable;
import java.util.ArrayList;

import za.co.ipay.metermng.mybatis.generated.model.ScheduledTask;
import za.co.ipay.metermng.shared.dto.customer.CustomerNotifyData;
import za.co.ipay.metermng.shared.dto.user.UserData;

public class ScheduledTaskDto implements Serializable {

    private static final long serialVersionUID = 1L;

    private ScheduledTask scheduledTask;
    private String taskClass;
    private ArrayList<UserData> users;
    private CustomerNotifyData customer;
    
    public ScheduledTaskDto() {
        this.scheduledTask = new ScheduledTask();
        this.taskClass = "";
        this.users = new ArrayList<UserData>();
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();        
        sb.append(" scheduledTask:");
        if (scheduledTask != null) {
            sb.append(" id:").append(scheduledTask.getId());
            sb.append(" name:").append(scheduledTask.getScheduledTaskName());
            sb.append(" taskClassId:").append(scheduledTask.getTaskClassId());
        } else {
            sb.append("null");
        }
        sb.append(" taskClass:").append(taskClass);
        return sb.toString();
    }
    
    public ScheduledTask getScheduledTask() {
        return scheduledTask;
    }

    public void setScheduledTask(ScheduledTask scheduledTask) {
        this.scheduledTask = scheduledTask;
    }

    public String getTaskClass() {
        return taskClass;
    }

    public void setTaskClass(String taskClass) {
        this.taskClass = taskClass;
    }

    public ArrayList<UserData> getUsers() {
        return users;
    }

    public void setUsers(ArrayList<UserData> users) {
        this.users = users;
    }

    public CustomerNotifyData getCustomer() {
        return customer;
    }

    public void setCustomer(CustomerNotifyData customer) {
        this.customer = customer;
    }
}
