/**
 *
 */
package za.co.ipay.metermng.shared.dto;

import java.io.Serializable;

/**
 * <AUTHOR>
 *
 */
public class ItemValuePairDto implements Serializable {

    private static final long serialVersionUID = 1L;

    private String item;
    private String value;

    public ItemValuePairDto() {}

    /**
     * @param item
     * @param value
     */
    public ItemValuePairDto(String item, String value) {
        super();
        this.item = item;
        this.value = value;
    }

    /**
     * @return the item
     */
    public String getItem() {
        return this.item;
    }

    /**
     * @param item the item to set
     */
    public void setItem(String item) {
        this.item = item;
    }

    /**
     * @return the value
     */
    public String getValue() {
        return this.value;
    }

    /**
     * @param value the value to set
     */
    public void setValue(String value) {
        this.value = value;
    }
}
