package za.co.ipay.metermng.shared.dto.uploaddata.transuploaddata;

import java.io.Serializable;
import java.util.HashMap;

public class CustomerTransCsvData extends TransCsvData implements Serializable {

	private static final long serialVersionUID = 1L;

	private String identifierType;
	private String identifier;

	public CustomerTransCsvData(HashMap<Integer, String> csvFieldMap, String stringOfFieldsCommaSeperated,
			boolean withErrorString) {
		super(csvFieldMap, stringOfFieldsCommaSeperated, withErrorString);

		this.setIdentifierType(beanDataMap.get("identifierType"));
		this.setIdentifier(beanDataMap.get("identifier"));
	}

	public String getIdentifierType() {
		return identifierType;
	}

	public void setIdentifierType(String identifierType) {
		this.identifierType = identifierType;
	}

	public String getIdentifier() {
		return identifier;
	}

	public void setIdentifier(String identifier) {
		this.identifier = identifier;
	}

	@Override
	public String toString() {
		return "CustomerTransCsvData [identifierType=" + identifierType + ", identifier=" + identifier + ", "
				+ super.toString() + "]";
	}

}