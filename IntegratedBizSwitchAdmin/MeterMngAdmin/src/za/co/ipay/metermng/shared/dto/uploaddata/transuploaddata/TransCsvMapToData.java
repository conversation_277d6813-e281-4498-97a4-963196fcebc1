package za.co.ipay.metermng.shared.dto.uploaddata.transuploaddata;

import java.util.HashMap;

import za.co.ipay.metermng.shared.bulkupload.BulkUploadException;
import za.co.ipay.metermng.shared.bulkupload.ICsvMapToData;

/**
 * <PERSON><PERSON> creating headers for both Customer and Aux Transactions uploads. 
 * If more class specific functions emerge in future subclass
 * CustomerTransCsvMapToData and AuxTransCsvMapToData may be defined.
 */
public class TransCsvMapToData implements ICsvMapToData {

	protected HashMap<String, String> csvHeadingToDataNameMap = new HashMap<String, String>();
	protected HashMap<Integer, String> csvFieldMap = new HashMap<Integer, String>();

	public TransCsvMapToData(boolean isCustomerTransUpload) {
		if (isCustomerTransUpload) {
			csvHeadingToDataNameMap.put("Identifier Type", "identifierType");
			csvHeadingToDataNameMap.put("Identifier", "identifier");
		} else {
			csvHeadingToDataNameMap.put("Aux Account Name", "auxAccountName");
			csvHeadingToDataNameMap.put("Agreement Ref", "agreementRef");
		}
		csvHeadingToDataNameMap.put("Amount Including Tax", "amtInclTax");
		csvHeadingToDataNameMap.put("Tax Amount", "amtTax");
		csvHeadingToDataNameMap.put("Transaction Date: yyyy-MM-dd HH:mm:ss", "transDate");
		csvHeadingToDataNameMap.put("Account Reference", "accountRef");
		csvHeadingToDataNameMap.put("Comment", "comment");
	}

	public HashMap<Integer, String> constructCsvFieldMap(String stringOfHeadingsCommaSeperated) throws BulkUploadException {
		String[] strArray = stringOfHeadingsCommaSeperated.split(",");
		for (int i = 0; i < strArray.length; i++) {
			String dataName = csvHeadingToDataNameMap.get(strArray[i].trim());
			if (dataName == null) {
				throw new BulkUploadException(ICsvMapToData.UNKNOWN_COLUMN_HEADING + ": >" + strArray[i].trim() + "<");
			}
			csvFieldMap.put(i, dataName);
		}
		return csvFieldMap;
	}

	public HashMap<String, String> getCsvHeadingToDataNameMap() {
		return csvHeadingToDataNameMap;
	}
}
