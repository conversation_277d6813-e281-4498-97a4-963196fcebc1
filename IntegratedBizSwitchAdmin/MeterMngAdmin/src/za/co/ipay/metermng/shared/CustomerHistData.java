package za.co.ipay.metermng.shared;

import za.co.ipay.metermng.mybatis.generated.model.Customer;
import za.co.ipay.metermng.mybatis.generated.model.CustomerHist;

import com.google.gwt.user.client.rpc.IsSerializable;

public class CustomerHistData extends CustomerHist implements IsSerializable {
	
    private static final long serialVersionUID = 3719119097649442277L;
    
    protected boolean customerHistIdChanged = false;
    protected boolean idChanged = false;
    protected boolean mridChanged = false;
    protected boolean mridExternalChanged = false;
    protected boolean customerKindIdChanged = false;
    protected boolean companyNameChanged = false;
    protected boolean firstnamesChanged = false;
    protected boolean surnameChanged = false;
    protected boolean idNumberChanged = false;
    protected boolean initialsChanged = false;
    protected boolean titleChanged = false;
    protected boolean email1Changed = false;
    protected boolean email2Changed = false;
    protected boolean phone1Changed = false;
    protected boolean phone2Changed = false;
    protected boolean physicalLocationIdChanged = false;
    protected boolean recordStatusChanged = false;
    protected boolean localeValueChanged = false;
    
    protected boolean customVarchar1Changed = false;
    protected boolean customVarchar2Changed = false;
    protected boolean customNumeric1Changed = false;
    protected boolean customNumeric2Changed = false;
    protected boolean customTimestamp1Changed = false;
    protected boolean customTimestamp2Changed = false;
    
	public CustomerHistData() {
		
	}
	
    public CustomerHistData(Customer customer, String user) {
    	this.setId(customer.getId());
    	this.setMrid(customer.getMrid());
    	this.setCustomerKindId(customer.getCustomerKindId());
    	this.setCompanyName(customer.getCompanyName());
    	this.setFirstnames(customer.getFirstnames());
    	this.setSurname(customer.getSurname());
    	this.setIdNumber(customer.getIdNumber());
    	this.setInitials(customer.getInitials());
    	this.setTitle(customer.getTitle());
    	this.setEmail1(customer.getEmail1());
    	this.setEmail2(customer.getEmail2());
    	this.setPhone1(customer.getPhone1());
    	this.setPhone2(customer.getPhone2());
    	this.setLocaleValue(customer.getLocaleValue());
    	this.setPhysicalLocationId(customer.getPhysicalLocationId());
    	this.setRecordStatus(customer.getRecordStatus());
        
        this.setUserRecEntered(user);
        
        this.setCustomVarchar1(customer.getCustomVarchar1());
        this.setCustomVarchar2(customer.getCustomVarchar2());
        this.setCustomNumeric1(customer.getCustomNumeric1());
        this.setCustomNumeric2(customer.getCustomNumeric2());
        this.setCustomTimestamp1(customer.getCustomTimestamp1());
        this.setCustomTimestamp2(customer.getCustomTimestamp2());
    }

    public CustomerHistData(CustomerHist customerhist) {
        this.setId(customerhist.getId());
        this.setMrid(customerhist.getMrid());
        this.setCustomerKindId(customerhist.getCustomerKindId());
        this.setCompanyName(customerhist.getCompanyName());
        this.setFirstnames(customerhist.getFirstnames());
        this.setSurname(customerhist.getSurname());
        this.setIdNumber(customerhist.getIdNumber());
        this.setInitials(customerhist.getInitials());
        this.setTitle(customerhist.getTitle());
        this.setEmail1(customerhist.getEmail1());
        this.setEmail2(customerhist.getEmail2());
        this.setPhone1(customerhist.getPhone1());
        this.setPhone2(customerhist.getPhone2());
        this.setLocaleValue(customerhist.getLocaleValue());
        this.setPhysicalLocationId(customerhist.getPhysicalLocationId());
        this.setRecordStatus(customerhist.getRecordStatus());
    	
    	this.setUserRecEntered(customerhist.getUserRecEntered());
    	this.setUserAction(customerhist.getUserAction());
    	this.setDateRecModified(customerhist.getDateRecModified());
    	
    	this.setCustomVarchar1(customerhist.getCustomVarchar1());
    	this.setCustomVarchar2(customerhist.getCustomVarchar2());
    	this.setCustomNumeric1(customerhist.getCustomNumeric1());
    	this.setCustomNumeric2(customerhist.getCustomNumeric2());
    	this.setCustomTimestamp1(customerhist.getCustomTimestamp1());
    	this.setCustomTimestamp2(customerhist.getCustomTimestamp2());
    	
    }

    public boolean isCustomerHistIdChanged() {
        return customerHistIdChanged;
    }

    public void setCustomerHistIdChanged(boolean customerHistIdChanged) {
        this.customerHistIdChanged = customerHistIdChanged;
    }

    public boolean isIdChanged() {
        return idChanged;
    }

    public void setIdChanged(boolean idChanged) {
        this.idChanged = idChanged;
    }

    public boolean isMridChanged() {
        return mridChanged;
    }

    public void setMridChanged(boolean mridChanged) {
        this.mridChanged = mridChanged;
    }

    public boolean isMridExternalChanged() {
        return mridExternalChanged;
    }

    public void setMridExternalChanged(boolean mridExternalChanged) {
        this.mridExternalChanged = mridExternalChanged;
    }

    public boolean isCustomerKindIdChanged() {
        return customerKindIdChanged;
    }

    public void setCustomerKindIdChanged(boolean customerKindIdChanged) {
        this.customerKindIdChanged = customerKindIdChanged;
    }

    public boolean isCompanyNameChanged() {
        return companyNameChanged;
    }

    public void setCompanyNameChanged(boolean companyNameChanged) {
        this.companyNameChanged = companyNameChanged;
    }

    public boolean isFirstnamesChanged() {
        return firstnamesChanged;
    }

    public void setFirstnamesChanged(boolean firstnamesChanged) {
        this.firstnamesChanged = firstnamesChanged;
    }

    public boolean isSurnameChanged() {
        return surnameChanged;
    }

    public void setSurnameChanged(boolean surnameChanged) {
        this.surnameChanged = surnameChanged;
    }

    public boolean isIdNumberChanged() {
        return idNumberChanged;
    }

    public void setIdNumberChanged(boolean idNumberChanged) {
        this.idNumberChanged = idNumberChanged;
    }

    public boolean isInitialsChanged() {
        return initialsChanged;
    }

    public void setInitialsChanged(boolean initialsChanged) {
        this.initialsChanged = initialsChanged;
    }

    public boolean isTitleChanged() {
        return titleChanged;
    }

    public void setTitleChanged(boolean titleChanged) {
        this.titleChanged = titleChanged;
    }

    public boolean isEmail1Changed() {
        return email1Changed;
    }

    public void setEmail1Changed(boolean email1Changed) {
        this.email1Changed = email1Changed;
    }

    public boolean isEmail2Changed() {
        return email2Changed;
    }

    public void setEmail2Changed(boolean email2Changed) {
        this.email2Changed = email2Changed;
    }

    public boolean isPhone1Changed() {
        return phone1Changed;
    }

    public void setPhone1Changed(boolean phone1Changed) {
        this.phone1Changed = phone1Changed;
    }

    public boolean isPhone2Changed() {
        return phone2Changed;
    }

    public void setPhone2Changed(boolean phone2Changed) {
        this.phone2Changed = phone2Changed;
    }

    public boolean isPhysicalLocationIdChanged() {
        return physicalLocationIdChanged;
    }

    public void setPhysicalLocationIdChanged(boolean physicalLocationIdChanged) {
        this.physicalLocationIdChanged = physicalLocationIdChanged;
    }

    public boolean isRecordStatusChanged() {
        return recordStatusChanged;
    }

    public void setRecordStatusChanged(boolean recordStatusChanged) {
        this.recordStatusChanged = recordStatusChanged;
    }

    public boolean isLocaleValueChanged() {
        return localeValueChanged;
    }

    public void setLocaleValueChanged(boolean localeValueChanged) {
        this.localeValueChanged = localeValueChanged;
    }

    public boolean isCustomVarchar1Changed() {
        return customVarchar1Changed;
    }

    public void setCustomVarchar1Changed(boolean customVarchar1Changed) {
        this.customVarchar1Changed = customVarchar1Changed;
    }

    public boolean isCustomVarchar2Changed() {
        return customVarchar2Changed;
    }

    public void setCustomVarchar2Changed(boolean customVarchar2Changed) {
        this.customVarchar2Changed = customVarchar2Changed;
    }

    public boolean isCustomNumeric1Changed() {
        return customNumeric1Changed;
    }

    public void setCustomNumeric1Changed(boolean customNumeric1Changed) {
        this.customNumeric1Changed = customNumeric1Changed;
    }

    public boolean isCustomNumeric2Changed() {
        return customNumeric2Changed;
    }

    public void setCustomNumeric2Changed(boolean customNumeric2Changed) {
        this.customNumeric2Changed = customNumeric2Changed;
    }

    public boolean isCustomTimestamp1Changed() {
        return customTimestamp1Changed;
    }

    public void setCustomTimestamp1Changed(boolean customTimestamp1Changed) {
        this.customTimestamp1Changed = customTimestamp1Changed;
    }

    public boolean isCustomTimestamp2Changed() {
        return customTimestamp2Changed;
    }

    public void setCustomTimestamp2Changed(boolean customTimestamp2Changed) {
        this.customTimestamp2Changed = customTimestamp2Changed;
    }

}