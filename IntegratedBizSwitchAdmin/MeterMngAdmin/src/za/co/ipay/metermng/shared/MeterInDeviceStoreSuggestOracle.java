package za.co.ipay.metermng.shared;

import java.util.ArrayList;
import java.util.List;
import java.util.logging.Logger;

import za.co.ipay.gwt.common.client.widgets.IpayListBox;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.rpc.callback.ClientCallback;
import za.co.ipay.metermng.mybatis.custom.model.MeterDto;
import za.co.ipay.metermng.shared.dto.meter.MeterSearchDto;

public class MeterInDeviceStoreSuggestOracle extends BaseMeterSuggestOracle {
    private IpayListBox lstbxSelectStore;
    private static Logger logger = Logger.getLogger(MeterInDeviceStoreSuggestOracle.class.getName());

    public MeterInDeviceStoreSuggestOracle(ClientFactory clientFactory) {
        super(clientFactory);
        this.lstbxSelectStore = null;
    }

    @Override
    protected void getSuggestions(final Request request, final Callback callback) {
        Long deviceStoreId = getSelectedDeviceStoreId();
        logger.info("Getting meter suggestions: " + request.getQuery() + "  deviceStoreId=" + deviceStoreId);
        clientFactory.getSearchRpc().getMetersInDeviceStoresSuggestions(
                new MeterSearchDto(request.getQuery(), deviceStoreId), request.getLimit(),
                new ClientCallback<List<MeterDto>>() {
                    @Override
                    public void onSuccess(List<MeterDto> meters) {
                        ArrayList<Suggestion> result = new ArrayList<Suggestion>();
                        for (MeterDto meter : meters) {
                            result.add(new MeterSuggestion(meter));
                        }
                        Response response = new Response(result);
                        callback.onSuggestionsReady(request, response);
                    }
                });
    }

    private Long getSelectedDeviceStoreId() {
        if (lstbxSelectStore == null) {
            logger.info("MeterInDeviceStoreSuggestOracle: no devicestore listbox");
            return null;
        }
        int index = lstbxSelectStore.getSelectedIndex();
        if (index > -1) {
            return Long.parseLong(lstbxSelectStore.getValue(index)); // id
        } else {
            logger.info("MeterInDeviceStoreSuggestOracle: no devicestore selected");
            return null;
        }
    }

    public void setLstbxSelectStore(IpayListBox lstbxSelectStore) {
        this.lstbxSelectStore = lstbxSelectStore;
    }
}
