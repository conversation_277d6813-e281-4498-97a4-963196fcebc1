package za.co.ipay.metermng.shared.dto.meter;

import java.io.Serializable;
import java.util.ArrayList;

import za.co.ipay.metermng.datatypes.RecordStatus;
import za.co.ipay.metermng.mybatis.custom.model.HasStatus;
import za.co.ipay.metermng.mybatis.custom.model.ModelSupportsPaymentModeData;
import za.co.ipay.metermng.mybatis.generated.model.Manufacturer;
import za.co.ipay.metermng.mybatis.generated.model.Mdc;
import za.co.ipay.metermng.mybatis.generated.model.MeterDataDecoder;
import za.co.ipay.metermng.mybatis.generated.model.MeterModel;
import za.co.ipay.metermng.mybatis.generated.model.MeterPhase;
import za.co.ipay.metermng.mybatis.generated.model.MeterType;
import za.co.ipay.metermng.mybatis.generated.model.ServiceResource;

public class MeterModelDto implements Serializable, HasStatus {

    private static final long serialVersionUID = 1L;

    private MeterModel meterModel;
    private Manufacturer manufacturer;
    private ServiceResource serviceResource;
    private MeterType meterType;
    private Mdc mdc;
    private MeterPhase meterPhase;
    private MeterDataDecoder meterDataDecoder;
    private boolean meterAttachedToMeterModel;

    private ArrayList<ModelSupportsPaymentModeData> supportedPaymentModes;

    public MeterModelDto() {
        this.meterModel = new MeterModel();
    }

    public MeterModel getMeterModel() {
        return meterModel;
    }

    public void setMeterModel(MeterModel meterModel) {
        this.meterModel = meterModel;
    }

    public Manufacturer getManufacturer() {
        return manufacturer;
    }

    public void setManufacturer(Manufacturer manufacturer) {
        this.manufacturer = manufacturer;
    }

    public ServiceResource getServiceResource() {
        return serviceResource;
    }

    public void setServiceResource(ServiceResource serviceResource) {
        this.serviceResource = serviceResource;
    }

    public MeterType getMeterType() {
        return meterType;
    }

    public void setMeterType(MeterType meterType) {
        this.meterType = meterType;
    }

    public Mdc getMdc() {
        return mdc;
    }

    public void setMdc(Mdc mdc) {
        this.mdc = mdc;
    }

    @Override
    public RecordStatus getRecordStatus() {
        return meterModel.getRecordStatus();
    }

    @Override
    public void setRecordStatus(RecordStatus recordStatus) {
        meterModel.setRecordStatus(recordStatus);
    }

    public ArrayList<ModelSupportsPaymentModeData> getSupportedPaymentModes() {
        return supportedPaymentModes;
    }

    public void setSupportedPaymentModes(ArrayList<ModelSupportsPaymentModeData> supportedPaymentModes) {
        this.supportedPaymentModes = supportedPaymentModes;
    }

    public MeterPhase getMeterPhase() {
        return meterPhase;
    }

    public void setMeterPhase(MeterPhase meterPhase) {
        this.meterPhase = meterPhase;
    }

    public MeterDataDecoder getMeterDataDecoder() {
        return meterDataDecoder;
    }

    public void setMeterDataDecoder(MeterDataDecoder meterDataDecoder) {
        this.meterDataDecoder = meterDataDecoder;
    }

    public void setMeterAttachedToMeterModel(boolean meterAttachedToMeterModel) {
        this.meterAttachedToMeterModel = meterAttachedToMeterModel;
    }

    public boolean isMeterAttachedToMeterModel() {
        return meterAttachedToMeterModel;
    }
}
