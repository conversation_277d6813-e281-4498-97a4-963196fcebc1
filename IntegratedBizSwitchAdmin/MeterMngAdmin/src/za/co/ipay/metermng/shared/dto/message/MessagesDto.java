package za.co.ipay.metermng.shared.dto.message;

import java.io.Serializable;

import za.co.ipay.gwt.common.shared.message.MessageBundleData;

public class MessagesDto implements Serializable {

    private static final long serialVersionUID = 1L;

    private MessageBundleData messages;
    private MessageBundleData formats;
    
    public MessagesDto() {
        
    }
    
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("MessagesDto: ");
        sb.append(" messages: ").append(messages);
        sb.append(" formats: ").append(formats);
        return sb.toString();
    }

    public MessageBundleData getMessages() {
        return messages;
    }

    public void setMessages(MessageBundleData messages) {
        this.messages = messages;
    }

    public MessageBundleData getFormats() {
        return formats;
    }

    public void setFormats(MessageBundleData formats) {
        this.formats = formats;
    }    
}
