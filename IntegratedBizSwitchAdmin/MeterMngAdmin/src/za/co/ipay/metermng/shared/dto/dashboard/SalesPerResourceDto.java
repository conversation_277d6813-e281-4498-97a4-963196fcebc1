package za.co.ipay.metermng.shared.dto.dashboard;

import java.io.Serializable;
import java.math.BigDecimal;

public class SalesPerResourceDto implements Serializable{

    /**
     * <AUTHOR>
     */
    private static final long serialVersionUID = 1L;

    private String tsData;
    private BigDecimal salesPerResourceTotal;

    public SalesPerResourceDto(){}

    public SalesPerResourceDto(String valueDate,
            BigDecimal salesPerResourceTotal) {
        super();
        this.tsData = valueDate;
        this.salesPerResourceTotal = salesPerResourceTotal;
    }

    /**
     * @return the tsData
     */
    public String getTsData() {
        return this.tsData;
    }

    /**
     * @param tsData the tsData to set
     */
    public void setTsData(String tsData) {
        this.tsData = tsData;
    }

    /**
     * @return the salesPerResourceTotal
     */
    public BigDecimal getSalesPerResourceTotal() {
        return this.salesPerResourceTotal;
    }

    /**
     * @param salesPerResourceTotal the salesPerResourceTotal to set
     */
    public void setSalesPerResourceTotal(BigDecimal salesPerResourceTotal) {
        this.salesPerResourceTotal = salesPerResourceTotal;
    }

}
