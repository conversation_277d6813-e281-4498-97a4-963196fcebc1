package za.co.ipay.metermng.shared.dto;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Comparator;

import com.google.gwt.user.client.ui.SuggestOracle.Suggestion;

/**
 * SelectionDataItem is one item in a hierarchy of group types and their actual group data. It is used to display the
 * item's name for selection - typically on the UI in a drop-down field.
 */
public class SelectionDataItem implements Serializable, Comparable<SelectionDataItem>, Suggestion {

    private static final long serialVersionUID = -6306579879676996964L;

    public enum SelectionDataType { GROUP_TYPE, GROUP_DATA }
    public static Long NO_VALUES_ACTUAL_ID = new Long(-1);
    public static String NO_VALUES_ID_KEY = "G" + NO_VALUES_ACTUAL_ID;
    public static Long EMPTY_ITEM_ACTUAL_ID = new Long(-2);
    public static String EMPTY_ITEM_ID_KEY = "G" + EMPTY_ITEM_ACTUAL_ID;
    public static String GROUP_PREFIX = "G";
        
    private String id;
    private String parentId;
    private String name;
    private String label;
    private Long groupHierarchyId;
    private SelectionDataType type;
    private boolean lastLevel;
    private ArrayList<SelectionDataItem> children;
    private boolean required;
    private Integer layoutOrder;
 
    
    public SelectionDataItem() {
        this.name = "";
        this.label = "";
        this.lastLevel = false;
        this.children = new ArrayList<SelectionDataItem>();
    }
    
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("SelectionDataItem:");
        sb.append("[id:").append(id);
        sb.append(" parentId:").append(parentId);
        sb.append(" name:").append(name);
        sb.append(" label:").append(label);
        sb.append(" type:").append(type);
        sb.append(" lastLevel:").append(lastLevel);
        sb.append(" children: ").append(children.size());
        sb.append("]");
        return sb.toString();
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((id == null) ? 0 : id.hashCode());
        return result;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        SelectionDataItem other = (SelectionDataItem) obj;
        if (id == null) {
            if (other.id != null)
                return false;
        } else if (!id.equals(other.id))
            return false;
        return true;
    }

    public ArrayList<SelectionDataItem> getChildren() {
        return children;
    }
    
    public void setChildren(ArrayList<SelectionDataItem> children) {
        this.children = children;
    }
    
    public String getName() {
        return name;
    }
    
    public void setName(String name) {
        this.name = name;
    }
    
    public String getId() {
        return id;
    }
    
    public void setId(String id) {
        this.id = id;
    }

    public boolean isLastLevel() {
        return lastLevel;
    }

    public void setLastLevel(boolean lastLevel) {
        this.lastLevel = lastLevel;
    }

    public String getParentId() {
        return parentId;
    }

    public void setParentId(String parentId) {
        this.parentId = parentId;
    }

    public SelectionDataType getType() {
        return type;
    }

    public void setType(SelectionDataType type) {
        this.type = type;
    }

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }

    public Long getGroupHierarchyId() {
        return groupHierarchyId;
    }

    public void setGroupHierarchyId(Long groupHierarchyId) {
        this.groupHierarchyId = groupHierarchyId;
    }

    public boolean isRequired() {
        return required;
    }

    public void setRequired(boolean required) {
        this.required = required;
    }

    @Override
    public int compareTo(SelectionDataItem o) {
        return this.name.compareTo(o.name);
    }

    public Long getActualId() {
        return fromIdKeyToActualId(id);
    }

    public String getActualIdAsString() {
        return fromIdKeyToActualIdString(id);
    }

    public static String toIdKey(SelectionDataType selectionDataType, Long id) {
        if(id == null)
            return null;
        StringBuilder sb = new StringBuilder();
        sb.append((selectionDataType == SelectionDataType.GROUP_TYPE ? "T" : "G"));
        sb.append(id.toString());
        return sb.toString();
    }

    public static Long fromIdKeyToActualId(String idKey) {
        if(idKey == null)
            return null;
        Long retId = Long.valueOf(idKey.substring(1));
        return retId;
    }
    
    public static String fromIdKeyToActualIdString(String idKey) {
        return idKey.substring(1);
    }

    public static boolean isActualGroupId(Long groupId) {
        return groupId != null && ! groupId.equals(NO_VALUES_ACTUAL_ID) && ! groupId.equals(EMPTY_ITEM_ACTUAL_ID);
    }

    public Integer getLayoutOrder() {
        return layoutOrder;
    }

    public void setLayoutOrder(Integer layoutOrder) {
        if (layoutOrder == null) {
            this.layoutOrder= 99999;
        }
        this.layoutOrder = layoutOrder;
    }

    public static Comparator<SelectionDataItem> layoutOrderComparator() {
        return new Comparator<SelectionDataItem>() {
            public int compare(SelectionDataItem o1, SelectionDataItem o2) {
                if (o1.getLayoutOrder() == null && o2.getLayoutOrder() == null) {
                    return o1.getName().compareTo(o2.getName());
                } else if (o1.getLayoutOrder() == null && o2.getLayoutOrder() != null) {
                    return 1;
                } else if (o2.getLayoutOrder() == null && o1.getLayoutOrder() != null) {
                    return -1;
                }

                return o1.getLayoutOrder().compareTo(o2.getLayoutOrder());
            }
        };
    }

    @Override
    public String getDisplayString() {
        return getName();
    }

    @Override
    public String getReplacementString() {
        return getName();
    }

}
