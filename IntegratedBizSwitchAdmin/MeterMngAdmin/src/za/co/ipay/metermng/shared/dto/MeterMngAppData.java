package za.co.ipay.metermng.shared.dto;

import java.io.Serializable;

import za.co.ipay.metermng.shared.dto.message.MessagesDto;
import za.co.ipay.metermng.shared.util.MeterMngConfig;

/**
 * MeterMngAppData is a wrapper around the application start-up data that needs to be loaded from the server, in order
 * to configure the application for use.
 * 
 * <AUTHOR>
 */
public class MeterMngAppData implements Serializable {

    private static final long serialVersionUID = 1L;

    private MeterMngConfig meterMngConfig;
    private MessagesDto messages;
    private String version;
    
    public MeterMngAppData() {
        //default constructor
    }

    public MeterMngConfig getMeterMngConfig() {
        return meterMngConfig;
    }

    public void setMeterMngConfig(MeterMngConfig meterMngConfig) {
        this.meterMngConfig = meterMngConfig;
    }

    public MessagesDto getMessages() {
        return messages;
    }

    public void setMessages(MessagesDto messages) {
        this.messages = messages;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }
    
    
}
