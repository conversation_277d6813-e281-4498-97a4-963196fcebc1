package za.co.ipay.metermng.shared.dto.dashboard;

import java.io.Serializable;

public class TsDataCountTableDto implements Serializable{

    /**
     * <p>Generic data transfer object to transfer data representing a table with a string
     * column representing a timestamp (yyyy-mm-dd) or (yyyy-mm) and an integer column
     * representing a sum total. This is to be used for getting data for dashboard graph
     * panels.
     *
     * <AUTHOR>
     */
    private static final long serialVersionUID = 1L;

    private String tsData;
    private Integer count;

    public TsDataCountTableDto(){}

    public TsDataCountTableDto(String valueDate,
            Integer count) {
        super();
        this.tsData = valueDate;
        this.count = count;
    }

    public String getTsData() {
        return this.tsData;
    }

    public void setTsData(String valueDate) {
        this.tsData = valueDate;
    }

    public Integer getCount() {
        return this.count;
    }

    public void setCount(Integer count) {
        this.count = count;
    }
}
