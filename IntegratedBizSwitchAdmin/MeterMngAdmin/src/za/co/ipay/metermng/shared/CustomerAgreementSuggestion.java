package za.co.ipay.metermng.shared;

import com.google.gwt.user.client.rpc.IsSerializable;
import com.google.gwt.user.client.ui.SuggestOracle.Suggestion;

public class CustomerAgreementSuggestion implements IsSerializable, Suggestion {

    private String suggestion;
    private String custAgrRef;
    private String originalQuery;
    private boolean unassigned;


    public CustomerAgreementSuggestion() {
    }

    public CustomerAgreementSuggestion(String suggestion, String custAgrRef) {
        this.suggestion = suggestion;
        this.custAgrRef = custAgrRef;
    }

    @Override
    public String getDisplayString() {
        return suggestion;
    }

    @Override
    public String getReplacementString() {
        return suggestion;
    }


    public String getSuggestion() {
		return suggestion;
	}

	public void setSuggestion(String suggestion) {
		this.suggestion = suggestion;
	}

	public String getCustAgrRef() {
		return custAgrRef;
	}

	public void setCustAgrRef(String custAgrRef) {
		this.custAgrRef = custAgrRef;
	}

	public boolean isUnassigned() {
		return unassigned;
	}

	public void setUnassigned(boolean unassigned) {
		this.unassigned = unassigned;
	}

	public String getOriginalQuery() {
		return originalQuery;
	}

	public void setOriginalQuery(String originalQuery) {
		this.originalQuery = originalQuery;
	}

	@Override
	public String toString() {
		StringBuilder builder = new StringBuilder();
		builder.append("CustomerAgreementSuggestion [suggestion=")
				.append(suggestion).append(", custAgrRef=").append(custAgrRef)
				.append(", originalQuery=").append(originalQuery)
				.append(", unassigned=").append(unassigned).append("]");
		return builder.toString();
	}

}
