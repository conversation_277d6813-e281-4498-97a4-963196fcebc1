package za.co.ipay.metermng.shared.dto.meter;

import java.io.Serializable;
import java.util.Date;

public class VerifyTokenDto implements Serializable {

    private static final long serialVersionUID = 1L;

    private Integer tokenId;
    private int transferAmt;
    private int tokenClass;
    private int subClass;
    private Date dateGenerated;
    private String tokenClassName;
    private String subClassName;
    private String verificationError = null;

    public VerifyTokenDto() {

    }

    public VerifyTokenDto(Integer tokenId, int transferAmt, int tokenClass, int subClass) {
        this.tokenId = tokenId;
        this.transferAmt = transferAmt;
        this.tokenClass = tokenClass;
        this.subClass = subClass;
    }

    public VerifyTokenDto(String verificationError) {
        this.verificationError = verificationError;
    }

    public Integer getTokenId() {
        return tokenId;
    }

    public int getTransferAmt() {
        return transferAmt;
    }

    public int getTokenClass() {
        return tokenClass;
    }

    public int getSubClass() {
        return subClass;
    }

    public String getTokenClassName() {
        return tokenClassName;
    }

    public void setTokenClassName(String tokenClassName) {
        this.tokenClassName = tokenClassName;
    }

    public String getSubClassName() {
        return subClassName;
    }

    public void setSubClassName(String subClassName) {
        this.subClassName = subClassName;
    }

    public String getVerificationError() {
        return verificationError;
    }

    public Date getDateGenerated() {
        return dateGenerated;
    }

    public void setDateGenerated(Date dateGenerated) {
        this.dateGenerated = dateGenerated;
    }

    public boolean verifyTokenFailed() {
        return tokenClass==0 && subClass==0 && getTokenId()==0 && transferAmt==0;
    }
}
