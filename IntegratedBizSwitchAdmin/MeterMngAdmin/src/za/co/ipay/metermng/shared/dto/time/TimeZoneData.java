package za.co.ipay.metermng.shared.dto.time;

import java.io.Serializable;

public class TimeZoneData implements Serializable {

    private static final long serialVersionUID = 1L;

    private String name;
    private String displayName;
    private String json;
    
    public TimeZoneData() {
        //default constructor
    }
    
    public TimeZoneData(String name, String displayName, String json) {
        this.name = name;
        this.displayName = displayName;
        this.json = json;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDisplayName() {
        return displayName;
    }

    public void setDisplayName(String displayName) {
        this.displayName = displayName;
    }

    public String getJson() {
        return json;
    }

    public void setJson(String json) {
        this.json = json;
    }

    @Override
    public String toString() {
        return "TimeZoneData{" +
                "name='" + name + '\'' +
                ", displayName='" + displayName + '\'' +
                ", json='" + json + '\'' +
                '}';
    }
}
