package za.co.ipay.metermng.shared;

import za.co.ipay.metermng.datatypes.RecordStatus;
import za.co.ipay.metermng.mybatis.custom.model.HasStatus;
import za.co.ipay.metermng.mybatis.generated.model.MeterHist;

import com.google.gwt.user.client.rpc.IsSerializable;

public class MeterHistData implements HasStatus, IsSerializable {
    
    private boolean meterNumChanged = false;
    private boolean serialNumChanged = false;
    private boolean recordStatusChanged = false;
    private boolean endDeviceStoreChanged = false;
    private boolean mridChanged =  false;
    private boolean mridExternalChanged =  false;

    private MeterHist meterHist;
    private String endDeviceStoreName;
    
    public MeterHistData() {
        super();
    }
    
    public MeterHistData(MeterHist meterHist) {
        this.meterHist = meterHist;
    }

    public MeterHist getMeterHist() {
        return meterHist;
    }
    public void setMeterHist(MeterHist meterHist) {
        this.meterHist = meterHist;
    }
    
    public String getEndDeviceStoreName() {
        return endDeviceStoreName;
    }
    public void setEndDeviceStoreName(String endDeviceStoreName) {
        this.endDeviceStoreName = endDeviceStoreName;
    }

    public boolean isMeterNumChanged() {
        return meterNumChanged;
    }
    public void setMeterNumChanged(boolean meterNumChanged) {
        this.meterNumChanged = meterNumChanged;
    }

    public boolean isSerialNumChanged() {
        return serialNumChanged;
    }
    public void setSerialNumChanged(boolean serialNumChanged) {
        this.serialNumChanged = serialNumChanged;
    }

    public boolean isRecordStatusChanged() {
        return recordStatusChanged;
    }
    public void setRecordStatusChanged(boolean recordStatusChanged) {
        this.recordStatusChanged = recordStatusChanged;
    }

    public boolean isEndDeviceStoreChanged() {
        return endDeviceStoreChanged;
    }
    public void setEndDeviceStoreChanged(boolean endDeviceStoreChanged) {
        this.endDeviceStoreChanged = endDeviceStoreChanged;
    }

    @Override
    public RecordStatus getRecordStatus() {
        return getMeterHist().getRecordStatus();
    }
    @Override
    public void setRecordStatus(RecordStatus recordStatus) {
        //in order to use StatusTableColumn, must extend HasStatus & implement the RecordStatus methods
        getMeterHist().setRecordStatus(recordStatus);
    }

    public boolean isMridChanged() {
        return mridChanged;
    }
    public void setMridChanged(boolean mridChanged) {
        this.mridChanged = mridChanged;
    }

    public boolean isMridExternalChanged() {
        return mridExternalChanged;
    }
    public void setMridExternalChanged(boolean mridExternalChanged) {
        this.mridExternalChanged = mridExternalChanged;
    }
    
}
