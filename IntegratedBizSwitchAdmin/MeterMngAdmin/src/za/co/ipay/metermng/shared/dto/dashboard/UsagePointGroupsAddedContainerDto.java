package za.co.ipay.metermng.shared.dto.dashboard;

import java.io.Serializable;
import java.util.ArrayList;

public class UsagePointGroupsAddedContainerDto implements Serializable {

    /**
     *<AUTHOR>
     */
    private static final long serialVersionUID = 1L;

    private String upGroupName;
    private ArrayList<TsDataCountTableDto> dailyUsagePointGroupsAddedDtos;
    private ArrayList<TsDataCountTableDto> monthlyUsagePointGroupsAddedDtos;

    public UsagePointGroupsAddedContainerDto() {}

    /**
     * @return the upGroupName
     */
    public String getUpGroupName() {
        return this.upGroupName;
    }

    /**
     * @param upGroupName the upGroupName to set
     */
    public void setUpGroupName(String upGroupName) {
        this.upGroupName = upGroupName;
    }

    /**
     * @return the dailyUsagePointGroupsAddedDtos
     */
    public ArrayList<TsDataCountTableDto> getDailyUsagePointGroupsAddedDtos() {
        return this.dailyUsagePointGroupsAddedDtos;
    }

    /**
     * @param dailyUsagePointGroupsAddedDtos the dailyUsagePointGroupsAddedDtos to set
     */
    public void setDailyUsagePointGroupsAddedDtos(ArrayList<TsDataCountTableDto> dailyUsagePointGroupsAddedDtos) {
        this.dailyUsagePointGroupsAddedDtos = dailyUsagePointGroupsAddedDtos;
    }

    /**
     * @return the monthlyUsagePointGroupsAddedDtos
     */
    public ArrayList<TsDataCountTableDto> getMonthlyUsagePointGroupsAddedDtos() {
        return this.monthlyUsagePointGroupsAddedDtos;
    }

    /**
     * @param monthlyUsagePointGroupsAddedDtos the monthlyUsagePointGroupsAddedDtos to set
     */
    public void setMonthlyUsagePointGroupsAddedDtos(ArrayList<TsDataCountTableDto> monthlyUsagePointGroupsAddedDtos) {
        this.monthlyUsagePointGroupsAddedDtos = monthlyUsagePointGroupsAddedDtos;
    }
}
