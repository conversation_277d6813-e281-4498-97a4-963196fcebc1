package za.co.ipay.metermng.shared.dto.meter;

import java.io.Serializable;
import java.util.List;

import za.co.ipay.metermng.datatypes.RecordStatus;
import za.co.ipay.metermng.mybatis.custom.model.HasStatus;
import za.co.ipay.metermng.mybatis.generated.model.ModelChannelConfig;

public class ModelChannelConfigDto implements Serializable, HasStatus {

    private static final long serialVersionUID = 1L;

    private ModelChannelConfig modelChannelConfig;
    private MdcChannelDto mdcChannelDto;
    private String mdcName;
    private String timeIntervalName;
    private RecordStatus recordStatus;
    
    public ModelChannelConfigDto() {
    }
    
    public ModelChannelConfigDto(ModelChannelConfig modelChannelConfig, MdcChannelDto mdcChannelDto, String mdcName, 
                                                                                               String timeIntervalName) {
        this.modelChannelConfig = modelChannelConfig;
        this.mdcChannelDto = mdcChannelDto;
        this.mdcName = mdcName;
        this.timeIntervalName = timeIntervalName;
    }

    public ModelChannelConfig getModelChannelConfig() {
        return modelChannelConfig;
    }

    public void setModelChannelConfig(ModelChannelConfig modelChannelConfig) {
        this.modelChannelConfig = modelChannelConfig;
    }

    public MdcChannelDto getMdcChannelDto() {
        return mdcChannelDto;
    }

    public void setMdcChannelDto(MdcChannelDto mdcChannelDto) {
        this.mdcChannelDto = mdcChannelDto;
    }

    public String getMdcName() {
        return mdcName;
    }

    public void setMdcName(String mdcName) {
        this.mdcName = mdcName;
    }

    public String getTimeIntervalName() {
        return timeIntervalName;
    }

    public void setTimeIntervalName(String timeIntervalName) {
        this.timeIntervalName = timeIntervalName;
    }

    public RecordStatus getRecordStatus() {
        return mdcChannelDto.getMdcChannel().getRecordStatus();
    }

    public void setRecordStatus(RecordStatus recordStatus) {
        this.recordStatus = recordStatus;
    }
}
