package za.co.ipay.metermng.shared;

import com.google.gwt.user.client.rpc.IsSerializable;

import za.co.ipay.metermng.mybatis.generated.model.EndDeviceStore;
import za.co.ipay.metermng.shared.dto.LocationData;

public class EndDeviceStoreData extends EndDeviceStore implements IsSerializable {

    private static final long serialVersionUID = 2344535078915839991L;

    private LocationData location;

    public EndDeviceStoreData() {
        super();
    }
    
    public EndDeviceStoreData(EndDeviceStore endDeviceStore) {
        super();
        this.setId(endDeviceStore.getId());
        this.setName(endDeviceStore.getName());
        this.setDescription(endDeviceStore.getDescription());
        this.setLocationId(endDeviceStore.getLocationId());
        this.setRecordStatus(endDeviceStore.getRecordStatus());
        this.setGenGroupId(endDeviceStore.getGenGroupId());
        this.setAccessGroupId(endDeviceStore.getAccessGroupId());
        this.setCustomMessage(endDeviceStore.getCustomMessage());
        this.setStoresOtherVendorsMeter(endDeviceStore.isStoresOtherVendorsMeter());
    }

    public LocationData getLocation() {
        return location;
    }

    public void setLocation(LocationData location) {
        this.location = location;
    }
}
