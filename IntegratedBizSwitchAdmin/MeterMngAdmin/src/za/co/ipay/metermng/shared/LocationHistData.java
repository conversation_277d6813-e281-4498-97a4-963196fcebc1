package za.co.ipay.metermng.shared;


import za.co.ipay.metermng.mybatis.generated.model.Location;
import za.co.ipay.metermng.mybatis.generated.model.LocationHist;

import com.google.gwt.user.client.rpc.IsSerializable;

public class LocationHistData extends LocationHist implements IsSerializable {
	
    private static final long serialVersionUID = 7589864837197022331L;

    protected boolean idChanged = false;
    protected boolean mridChanged = false;
    protected boolean mridExternalChanged = false;
    protected boolean countryIsoChanged = false;
    protected boolean addressLine1Changed = false;
    protected boolean addressLine2Changed = false;
    protected boolean addressLine3Changed = false;
    protected boolean cityChanged = false;
    protected boolean provinceChanged = false;
    protected boolean countryChanged = false;
    protected boolean postalCodeChanged = false;
    protected boolean erfNumberChanged = false;
    protected boolean phoneContact1Changed = false;
    protected boolean phone1Changed = false;
    protected boolean phoneContact2Changed = false;
    protected boolean phone2Changed = false;
    protected boolean latitudeChanged = false;
    protected boolean longitudeChanged = false;
    protected boolean accessMethodChanged = false;
    protected boolean siteAccessProblemChanged = false;
    protected boolean directionChanged = false;
   	protected boolean recordStatusChanged = false;
    protected boolean locGroupIdChanged = false;
    protected boolean streetNumChanged = false;
    protected boolean buildingNameChanged = false;
    protected boolean suiteNumChanged = false;
   	
    private String locationGroup;
   	
	public LocationHistData() {
		
	}
	
	
    public LocationHistData(Location location, String user) {
    	this.setId(location.getId());
    	this.setMrid(location.getMrid());
    	this.setCountryIso(location.getCountryIso());
    	this.setAddressLine1(location.getAddressLine1());
    	this.setAddressLine2(location.getAddressLine2());
    	this.setAddressLine3(location.getAddressLine3());
    	this.setCity(location.getCity());
    	this.setProvince(location.getProvince());
    	this.setCountry(location.getCountry());
    	this.setPostalCode(location.getPostalCode());
    	this.setErfNumber(location.getErfNumber());
    	this.setPhoneContact1(location.getPhoneContact1());
    	this.setPhone1(location.getPhone1());
    	this.setPhoneContact2(location.getPhoneContact2());
    	this.setPhone2(location.getPhone2());
    	this.setLatitude(location.getLatitude());
    	this.setLongitude(location.getLongitude());
    	this.setRecordStatus(location.getRecordStatus());
    	this.setLocGroupId(location.getLocGroupId());
    	this.setStreetNum(location.getStreetNum());
    	this.setBuildingName(location.getBuildingName());
    	this.setSuiteNum(location.getSuiteNum());
        this.setUserRecEntered(user);
    }

    public LocationHistData(LocationHist locationhist) {
        this.setId(locationhist.getId());
        this.setMrid(locationhist.getMrid());
        this.setCountryIso(locationhist.getCountryIso());
        this.setAddressLine1(locationhist.getAddressLine1());
        this.setAddressLine2(locationhist.getAddressLine2());
        this.setAddressLine3(locationhist.getAddressLine3());
        this.setCity(locationhist.getCity());
        this.setProvince(locationhist.getProvince());
        this.setCountry(locationhist.getCountry());
        this.setPostalCode(locationhist.getPostalCode());
        this.setErfNumber(locationhist.getErfNumber());
        this.setPhoneContact1(locationhist.getPhoneContact1());
        this.setPhone1(locationhist.getPhone1());
        this.setPhoneContact2(locationhist.getPhoneContact2());
        this.setPhone2(locationhist.getPhone2());
        this.setLatitude(locationhist.getLatitude());
        this.setLongitude(locationhist.getLongitude());
        this.setLocGroupId(locationhist.getLocGroupId());
        this.setStreetNum(locationhist.getStreetNum());
        this.setBuildingName(locationhist.getBuildingName());
        this.setSuiteNum(locationhist.getSuiteNum());
        this.setRecordStatus(locationhist.getRecordStatus());
        this.setUserRecEntered(locationhist.getUserRecEntered());
    	this.setUserAction(locationhist.getUserAction());
    	this.setDateRecModified(locationhist.getDateRecModified());
    }


    public boolean isIdChanged() {
        return idChanged;
    }


    public void setIdChanged(boolean idChanged) {
        this.idChanged = idChanged;
    }


    public boolean isMridChanged() {
        return mridChanged;
    }


    public void setMridChanged(boolean mridChanged) {
        this.mridChanged = mridChanged;
    }


    public boolean isMridExternalChanged() {
        return mridExternalChanged;
    }


    public void setMridExternalChanged(boolean mridExternalChanged) {
        this.mridExternalChanged = mridExternalChanged;
    }


    public boolean isCountryIsoChanged() {
        return countryIsoChanged;
    }


    public void setCountryIsoChanged(boolean countryIsoChanged) {
        this.countryIsoChanged = countryIsoChanged;
    }


    public boolean isAddressLine1Changed() {
        return addressLine1Changed;
    }


    public void setAddressLine1Changed(boolean addressLine1Changed) {
        this.addressLine1Changed = addressLine1Changed;
    }


    public boolean isAddressLine2Changed() {
        return addressLine2Changed;
    }


    public void setAddressLine2Changed(boolean addressLine2Changed) {
        this.addressLine2Changed = addressLine2Changed;
    }


    public boolean isAddressLine3Changed() {
        return addressLine3Changed;
    }


    public void setAddressLine3Changed(boolean addressLine3Changed) {
        this.addressLine3Changed = addressLine3Changed;
    }


    public boolean isCityChanged() {
        return cityChanged;
    }


    public void setCityChanged(boolean cityChanged) {
        this.cityChanged = cityChanged;
    }


    public boolean isProvinceChanged() {
        return provinceChanged;
    }


    public void setProvinceChanged(boolean provinceChanged) {
        this.provinceChanged = provinceChanged;
    }


    public boolean isCountryChanged() {
        return countryChanged;
    }


    public void setCountryChanged(boolean countryChanged) {
        this.countryChanged = countryChanged;
    }


    public boolean isPostalCodeChanged() {
        return postalCodeChanged;
    }


    public void setPostalCodeChanged(boolean postalCodeChanged) {
        this.postalCodeChanged = postalCodeChanged;
    }


    public boolean isErfNumberChanged() {
        return erfNumberChanged;
    }


    public void setErfNumberChanged(boolean erfNumberChanged) {
        this.erfNumberChanged = erfNumberChanged;
    }


    public boolean isPhoneContact1Changed() {
        return phoneContact1Changed;
    }


    public void setPhoneContact1Changed(boolean phoneContact1Changed) {
        this.phoneContact1Changed = phoneContact1Changed;
    }


    public boolean isPhone1Changed() {
        return phone1Changed;
    }


    public void setPhone1Changed(boolean phone1Changed) {
        this.phone1Changed = phone1Changed;
    }


    public boolean isPhoneContact2Changed() {
        return phoneContact2Changed;
    }


    public void setPhoneContact2Changed(boolean phoneContact2Changed) {
        this.phoneContact2Changed = phoneContact2Changed;
    }


    public boolean isPhone2Changed() {
        return phone2Changed;
    }


    public void setPhone2Changed(boolean phone2Changed) {
        this.phone2Changed = phone2Changed;
    }


    public boolean isLatitudeChanged() {
        return latitudeChanged;
    }


    public void setLatitudeChanged(boolean latitudeChanged) {
        this.latitudeChanged = latitudeChanged;
    }


    public boolean isLongitudeChanged() {
        return longitudeChanged;
    }


    public void setLongitudeChanged(boolean longitudeChanged) {
        this.longitudeChanged = longitudeChanged;
    }


    public boolean isAccessMethodChanged() {
        return accessMethodChanged;
    }


    public void setAccessMethodChanged(boolean accessMethodChanged) {
        this.accessMethodChanged = accessMethodChanged;
    }


    public boolean isSiteAccessProblemChanged() {
        return siteAccessProblemChanged;
    }


    public void setSiteAccessProblemChanged(boolean siteAccessProblemChanged) {
        this.siteAccessProblemChanged = siteAccessProblemChanged;
    }


    public boolean isDirectionChanged() {
        return directionChanged;
    }


    public void setDirectionChanged(boolean directionChanged) {
        this.directionChanged = directionChanged;
    }


    public boolean isRecordStatusChanged() {
        return recordStatusChanged;
    }


    public void setRecordStatusChanged(boolean recordStatusChanged) {
        this.recordStatusChanged = recordStatusChanged;
    }


    public boolean isLocGroupIdChanged() {
        return locGroupIdChanged;
    }


    public boolean isStreetNumChanged() {
        return streetNumChanged;
    }


    public boolean isBuildingNameChanged() {
        return buildingNameChanged;
    }


    public boolean isSuiteNumChanged() {
        return suiteNumChanged;
    }


    public void setLocGroupIdChanged(boolean locGroupIdChanged) {
        this.locGroupIdChanged = locGroupIdChanged;
    }


    public void setStreetNumChanged(boolean streetNumChanged) {
        this.streetNumChanged = streetNumChanged;
    }


    public void setBuildingNameChanged(boolean buildingNameChanged) {
        this.buildingNameChanged = buildingNameChanged;
    }


    public void setSuiteNumChanged(boolean suiteNumChanged) {
        this.suiteNumChanged = suiteNumChanged;
    }


    public String getLocationGroup() {
        return locationGroup;
    }


    public void setLocationGroup(String locationGroup) {
        this.locationGroup = locationGroup;
    }
    
	
}
