package za.co.ipay.metermng.shared.dto.user;

import java.util.ArrayList;

import za.co.ipay.accesscontrol.gwt.shared.GWTAdminUser;
import za.co.ipay.metermng.mybatis.generated.model.GroupHierarchy;

/**
 * MeterMngUserGroup is an extension to the usual user but also contains more information about their assigned group
 * for viewing and editing this group on the UserGroup screen.
 * 
 * <AUTHOR>
 */
public class MeterMngUserGroup extends MeterMngUser {

    private static final long serialVersionUID = 1L;

    /** The path from the parent group to the last child for the assigned group. This is used to display which group
     *  the user is currently assigned to on the UI screens. */
    protected ArrayList<Long> groupPath;
    /** The user's assigned group's hierarchy for displaying on the UI. */
    protected GroupHierarchy assignedGroupHierarchy;
    
    /** Default constructor for GWT, etc. */
    public MeterMngUserGroup() {
        this.groupPath = new ArrayList<Long>();
    }
    
    /** Copy style constructor using GWTAdminUser. */
    public MeterMngUserGroup(GWTAdminUser adminUser) {
        super();
        if (adminUser != null) {
            this.id = adminUser.getId();
            this.userName = adminUser.getUserName();
            this.permissions = adminUser.getPermissions();
            this.roles = adminUser.getRoles();
        }
    }
    
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("MeterMngUserGroup: ");
        sb.append(" id:").append(getId());
        sb.append(" username:").append(getUserName());
        sb.append(" assignedGroup:");
        if (assignedGroup != null) {
            sb.append(" name:").append(assignedGroup.getName());
        }        
        sb.append(" currentGroupId:");
        if (currentGroup != null) {
            sb.append(currentGroup.getName());
        }
        if (groupPath != null) {
            sb.append(" groupPath: ").append(groupPath.toString());
        }                
        if (assignedGroupHierarchy != null) {
            sb.append(" assignedGroupHierarchy: ").append(assignedGroupHierarchy.getName());
        }
        sb.append(" roles:").append(getRoles().toString());                
        sb.append(" permissions:").append(getPermissions().toString());
        return sb.toString();
    }

    public ArrayList<Long> getGroupPath() {
        return groupPath;
    }

    public void setGroupPath(ArrayList<Long> groupPath) {
        this.groupPath = groupPath;
    }

    public GroupHierarchy getAssignedGroupHierarchy() {
        return assignedGroupHierarchy;
    }

    public void setAssignedGroupHierarchy(GroupHierarchy assignedGroupHierarchy) {
        this.assignedGroupHierarchy = assignedGroupHierarchy;
    }    
}
