package za.co.ipay.metermng.shared;

import com.google.gwt.user.client.rpc.IsSerializable;
import com.google.gwt.user.client.ui.SuggestOracle.Suggestion;

public class PeriodSuggestion implements IsSerializable, Suggestion {

    private String suggestion; 
    private Long periodId;
    private String periodName;
    private String periodCode;
    private String originalQuery;
    
    
    public PeriodSuggestion() { } 

    public PeriodSuggestion(String suggestion, Long periodId, String periodName, String periodCode) {
        this.suggestion = suggestion;
        this.periodId = periodId;
        this.periodName = periodName;
        this.periodCode = periodCode;
    }
    
    @Override
    public String getDisplayString() {
        return suggestion;
    }

    @Override
    public String getReplacementString() {
        return periodCode;
    }

    public String getSuggestion() {
        return suggestion;
    }

    public void setSuggestion(String suggestions) {
        this.suggestion = suggestions;
    }

    public Long getId() {
        return periodId;
    }

    public void setId(Long periodId) {
        this.periodId = periodId;
    }

	public String getName() {
		return periodName;
	}

	public void setPeriodName(String periodName) {
		this.periodName = periodName;
	}
	
	public String getPeriodCode() {
        return periodCode;
    }

    public void setPeriodCode(String periodCode) {
        this.periodCode = periodCode;
    }
	
	public String getOriginalQuery() {
		return originalQuery;
	}

	public void setOriginalQuery(String originalQuery) {
		this.originalQuery = originalQuery;
	}

}
