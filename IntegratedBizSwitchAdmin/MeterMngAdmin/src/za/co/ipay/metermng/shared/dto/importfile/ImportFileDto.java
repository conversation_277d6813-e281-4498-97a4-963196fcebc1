package za.co.ipay.metermng.shared.dto.importfile;

import java.io.Serializable;

import za.co.ipay.metermng.mybatis.generated.model.ImportFile;

public class ImportFileDto extends ImportFile implements Serializable{

    private static final long serialVersionUID = -1L;
    
    private String importFileTypeName;
    private String importFileTypeClass;
    private boolean allowItemsMultipleImport;
    private boolean hasActionParams;
    private boolean allowExportFailedItems;          
    //allowExportFailedItems is added later in code from the handler class setting. See ImportFileDataService.selectImportFiles(..)
    private Integer numFailedUploads = 0;
    private boolean isExtractAvailable = false;
    private Long numSuccessfulImports = 0L;

    //For imports that include active usage points, currently only MeterCustUpBulkImportHandler
    private int licenceCount = 0;
    private int currentCount = 0;
    
    public ImportFileDto() {
        super();
    }

    public ImportFileDto(ImportFile importFile) {
        super();
        this.setId(importFile.getId());
        this.setImportFileTypeId(importFile.getImportFileTypeId());
        this.setImportFolder(importFile.getImportFolder());
        this.setImportFilename(importFile.getImportFilename());
        this.setUploadUsername(importFile.getUploadUsername());
        this.setUploadStart(importFile.getUploadStart());
        this.setUploadEnd(importFile.getUploadEnd());
        this.setNumItems(importFile.getNumItems());
        this.setLastImportUsername(importFile.getLastImportUsername());
        this.setLastImportStart(importFile.getLastImportStart());
        this.setLastImportEnd(importFile.getLastImportEnd());
        this.setLastImportTypeAll(importFile.getLastImportTypeAll());
        this.setStopImport(importFile.getStopImport());
        this.setLastStopImport(importFile.getLastStopImport());
        this.setActionParams(importFile.getActionParams());
        this.setBulkRef(importFile.getBulkRef());
        this.setAccessGroupId(importFile.getAccessGroupId());
    }

    public String getImportFileTypeName() {
        return importFileTypeName;
    }

    public void setImportFileTypeName(String importFileTypeName) {
        this.importFileTypeName = importFileTypeName;
    }

    public String getImportFileTypeClass() {
        return importFileTypeClass;
    }

    public void setImportFileTypeClass(String importFileTypeClass) {
        this.importFileTypeClass = importFileTypeClass;
    }

    public Boolean getAllowItemsMultipleImport() {
        return allowItemsMultipleImport;
    }

    public void setAllowItemsMultipleImport(Boolean allowItemsMultipleImport) {
        this.allowItemsMultipleImport = allowItemsMultipleImport;
    }

    public boolean isHasActionParams() {
        return hasActionParams;
    }

    public void setHasActionParams(boolean hasActionParams) {
        this.hasActionParams = hasActionParams;
    }
    
    public boolean isAllowExportFailedItems() {
        return allowExportFailedItems;
    }

    public void setAllowExportFailedItems(boolean allowExportFailedItems) {
        this.allowExportFailedItems = allowExportFailedItems;
    }

    public Integer getNumFailedUploads() {
        return numFailedUploads;
    }

    public void setNumFailedUploads(Integer numFailedUploads) {
        this.numFailedUploads = numFailedUploads;
    }

    public boolean isExtractAvailable() {
        return isExtractAvailable;
    }

    public void setExtractAvailable(boolean isExtractAvailable) {
        this.isExtractAvailable = isExtractAvailable;
    }

    public Long getNumSuccessfulImports() {
        return numSuccessfulImports;
    }

    public void setNumSuccessfulImports(Long numSuccessfulImports) {
        this.numSuccessfulImports = numSuccessfulImports;
    }

    public int getLicenceCount() {
        return licenceCount;
    }

    public void setLicenceCount(int licenceCount) {
        this.licenceCount = licenceCount;
    }

    public int getCurrentCount() {
        return currentCount;
    }

    public void setCurrentCount(int currentCount) {
        this.currentCount = currentCount;
    }
}
