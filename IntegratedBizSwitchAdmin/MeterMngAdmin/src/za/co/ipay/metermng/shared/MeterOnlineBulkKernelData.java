package za.co.ipay.metermng.shared;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import za.co.ipay.metermng.datatypes.RecordStatus;
import za.co.ipay.metermng.mybatis.custom.model.HasStatus;
import za.co.ipay.metermng.shared.dto.UpPricingStructureData;

public class MeterOnlineBulkKernelData implements HasStatus, Serializable {

    private static final long serialVersionUID = -4215323064827848866L;

    //UsagePoint Fields
    private Long usagePointId;
    private String usagePointName;
    private Date installationDate;
    private Long customerAgreementId;           
    private Long serviceLocationId;
    private RecordStatus recordStatus;
    
    //Meter
    private Long meterId;                     
    private String meterNum;
    private Long deviceStoreId;
    private String breakerId;

    private Long replaceReasonLogId;
    private String encKey;
    
    //STS Meter
    private Long stsCurrSupplyGroupCodeId;
    private String stsCurrSupplyGroupCode;
    private Long stsTokenTechCodeId;
    private Long stsAlgorithmCodeId;
    private Integer stsCurrKeyRevisionNum;
    private String stsCurrTariffIndex;

    //Meter URI
    private String meterUriAddress;
    private String meterUriProtocol;
    private String meterUriParams;
    private BigDecimal meterUriPort;

    //MeterType
    private Long meterTypeId;           
    private String meterTypeName;
    
    //MeterModel
    private Long meterModelId;          
    private String meterModelName;
    private Long mdcId;
    
    //Customer
    private String surname;
    private String phone1;
    
    //PricingStructure
    private Long currentPricingStructureId;
    private String currentPricingStructureName;
    private UpPricingStructureData upPricingStructureData;
    
    //Location   (serviceLocation)
    private String suiteNum;
    
    //device store name in case a meter is in a different store... if its in the same store as in Panel selection, this will be null.
    //only fetch if different
    private String deviceStoreName;

    
    public MeterOnlineBulkKernelData() {
        super();
    }

    public Long getUsagePointId() {
        return usagePointId;
    }

    public void setUsagePointId(Long usagePointId) {
        this.usagePointId = usagePointId;
    }

    public String getUsagePointName() {
        return usagePointName;
    }

    public void setUsagePointName(String usagePointName) {
        this.usagePointName = usagePointName;
    }

    public Date getInstallationDate() {
        return installationDate;
    }

    public void setInstallationDate(Date installationDate) {
        this.installationDate = installationDate;
    }

    public Long getServiceLocationId() {
        return serviceLocationId;
    }

    public void setServiceLocationId(Long serviceLocationId) {
        this.serviceLocationId = serviceLocationId;
    }

    public RecordStatus getRecordStatus() {
        return recordStatus;
    }

    public void setRecordStatus(RecordStatus recordStatus) {
        this.recordStatus = recordStatus;
    }

    public String getMeterNum() {
        return meterNum;
    }

    public void setMeterNum(String meterNum) {
        this.meterNum = meterNum;
    }

    public String getStsCurrSupplyGroupCode() {
        return stsCurrSupplyGroupCode;
    }

    public void setStsCurrSupplyGroupCode(String stsCurrSupplyGroupCode) {
        this.stsCurrSupplyGroupCode = stsCurrSupplyGroupCode;
    }

    public Long getStsCurrSupplyGroupCodeId() {
        return stsCurrSupplyGroupCodeId;
    }
    
    public void setStsCurrSupplyGroupCodeId(Long stsCurrSupplyGroupCodeId) {
        this.stsCurrSupplyGroupCodeId = stsCurrSupplyGroupCodeId;
    }

    public Long getStsTokenTechCodeId() {
        return stsTokenTechCodeId;
    }

    public void setStsTokenTechCodeId(Long stsTokenTechCodeId) {
        this.stsTokenTechCodeId = stsTokenTechCodeId;
    }

    public Long getStsAlgorithmCodeId() {
        return stsAlgorithmCodeId;
    }

    public void setStsAlgorithmCodeId(Long stsAlgorithmCodeId) {
        this.stsAlgorithmCodeId = stsAlgorithmCodeId;
    }

    public Integer getStsCurrKeyRevisionNum() {
        return stsCurrKeyRevisionNum;
    }

    public void setStsCurrKeyRevisionNum(Integer stsCurrKeyRevisionNum) {
        this.stsCurrKeyRevisionNum = stsCurrKeyRevisionNum;
    }

    public String getStsCurrTariffIndex() {
        return stsCurrTariffIndex;
    }

    public void setStsCurrTariffIndex(String stsCurrTariffIndex) {
        this.stsCurrTariffIndex = stsCurrTariffIndex;
    }

    public String getMeterTypeName() {
        return meterTypeName;
    }

    public void setMeterTypeName(String meterTypeName) {
        this.meterTypeName = meterTypeName;
    }

    public String getMeterModelName() {
        return meterModelName;
    }

    public void setMeterModelName(String meterModelName) {
        this.meterModelName = meterModelName;
    }

    public String getSurname() {
        return surname;
    }

    public void setSurname(String surname) {
        this.surname = surname;
    }

    public String getPhone1() {
        return phone1;
    }

    public void setPhone1(String phone1) {
        this.phone1 = phone1;
    }

    public Long getCurrentPricingStructureId() {
        return currentPricingStructureId;
    }

    public void setCurrentPricingStructureId(Long currentPricingStructureId) {
        this.currentPricingStructureId = currentPricingStructureId;
    }

    public String getCurrentPricingStructureName() {
        return currentPricingStructureName;
    }

    public void setCurrentPricingStructureName(String currentPricingStructureName) {
        this.currentPricingStructureName = currentPricingStructureName;
    }

    public UpPricingStructureData getUpPricingStructureData() {
        return upPricingStructureData;
    }

    public void setUpPricingStructureData(UpPricingStructureData upPricingStructureData) {
        this.upPricingStructureData = upPricingStructureData;
    }

    public String getSuiteNum() {
        return suiteNum;
    }

    public void setSuiteNum(String suiteNum) {
        this.suiteNum = suiteNum;
    }

    public Long getCustomerAgreementId() {
        return customerAgreementId;
    }

    public void setCustomerAgreementId(Long customerAgreementId) {
        this.customerAgreementId = customerAgreementId;
    }

    public Long getMeterId() {
        return meterId;
    }

    public void setMeterId(Long meterId) {
        this.meterId = meterId;
    }

    public Long getMeterTypeId() {
        return meterTypeId;
    }

    public void setMeterTypeId(Long meterTypeId) {
        this.meterTypeId = meterTypeId;
    }

    public Long getMeterModelId() {
        return meterModelId;
    }

    public void setMeterModelId(Long meterModelId) {
        this.meterModelId = meterModelId;
    }

    public Long getMdcId() {
        return mdcId;
    }

    public void setMdcId(Long mdcId) {
        this.mdcId = mdcId;
    }

    public String getBreakerId() {
        return breakerId;
    }

    public void setBreakerId(String breakerId) {
        this.breakerId = breakerId;
    }

    public Long getReplaceReasonLogId() {
        return replaceReasonLogId;
    }

    public void setReplaceReasonLogId(Long replaceReasonLogId) {
        this.replaceReasonLogId = replaceReasonLogId;
    }

    public Long getDeviceStoreId() {
        return deviceStoreId;
    }

    public void setDeviceStoreId(Long deviceStoreId) {
        this.deviceStoreId = deviceStoreId;
    }

    public String getDeviceStoreName() {
        return deviceStoreName;
    }

    public void setDeviceStoreName(String deviceStoreName) {
        this.deviceStoreName = deviceStoreName;
    }

    public String getEncKey() {
        return encKey;
    }

    public void setEncKey(String encKey) {
        this.encKey = encKey;
    }

    public String getMeterUriAddress() {
        return meterUriAddress;
    }

    public void setMeterUriAddress(String meterUriAddress) {
        this.meterUriAddress = meterUriAddress;
    }

    public BigDecimal getMeterUriPort() {
        return meterUriPort;
    }

    public void setMeterUriPort(BigDecimal meterUriPort) {
        this.meterUriPort = meterUriPort;
    }

    public String getMeterUriParams() {
        return meterUriParams;
    }

    public void setMeterUriParams(String meterUriParams) {
        this.meterUriParams = meterUriParams;
    }

    public String getMeterUriProtocol() {
        return meterUriProtocol;
    }

    public void setMeterUriProtocol(String meterUriProtocol) {
        this.meterUriProtocol = meterUriProtocol;
    }

    @Override
    public String toString() {
        StringBuilder builder = new StringBuilder();
        builder.append("MeterOnlineBulkKernelData [usagePointId=").append(usagePointId).append(", usagePointName=").append(usagePointName).append(", installationDate=").append(installationDate).append(", customerAgreementId=").append(customerAgreementId)
                .append(", serviceLocationId=").append(serviceLocationId).append(", recordStatus=").append(recordStatus).append(", meterId=").append(meterId).append(", meterNum=").append(meterNum).append(", deviceStoreId=").append(deviceStoreId)
                .append(", breakerId=").append(breakerId).append(", replaceReasonLogId=").append(replaceReasonLogId).append(", stsCurrSupplyGroupCodeId=").append(stsCurrSupplyGroupCodeId).append(", stsCurrSupplyGroupCode=").append(stsCurrSupplyGroupCode)
                .append(", stsTokenTechCodeId=").append(stsTokenTechCodeId).append(", stsAlgorithmCodeId=").append(stsAlgorithmCodeId).append(", stsCurrKeyRevisionNum=").append(stsCurrKeyRevisionNum).append(", stsCurrTariffIndex=")
                .append(stsCurrTariffIndex).append(", meterUriAddress=").append(", meterUriPort=").append(meterUriPort).append(meterUriAddress).append(", meterUriProtocol=").append(meterUriProtocol).append(", meterUriParams=").append(meterUriParams)
                .append(", meterTypeId=").append(meterTypeId).append(", meterTypeName=").append(meterTypeName).append(", meterModelId=").append(meterModelId).append(", meterModelName=").append(meterModelName)
                .append(", surname=").append(surname).append(", phone1=").append(phone1).append(", currentPricingStructureId=").append(currentPricingStructureId).append(", currentPricingStructureName=").append(currentPricingStructureName)
                .append(", suiteNum=").append(suiteNum)
                .append(", deviceStoreName=").append(deviceStoreName)
                .append(", encKey=").append(encKey).append("]");
        return builder.toString();
    }
    
}
