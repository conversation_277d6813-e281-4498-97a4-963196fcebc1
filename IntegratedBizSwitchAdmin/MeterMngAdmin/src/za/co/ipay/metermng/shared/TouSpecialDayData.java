package za.co.ipay.metermng.shared;

import za.co.ipay.metermng.mybatis.generated.model.TouDayProfile;
import za.co.ipay.metermng.mybatis.generated.model.TouSpecialDay;

import com.google.gwt.user.client.rpc.IsSerializable;

public class TouSpecialDayData extends TouSpecialDay implements IsSerializable {
    
    private static final long serialVersionUID = 370016388288836287L;
    
    private TouDayProfile dayProfile;
    
    public TouSpecialDayData() {
        super();
    }
    
    public TouSpecialDayData(TouSpecialDay touSpecialDay) {
        super();
        this.setId(touSpecialDay.getId());
        this.setTouCalendarId(touSpecialDay.getTouCalendarId());
        this.setTouDayProfileId(touSpecialDay.getTouDayProfileId());
        this.setName(touSpecialDay.getName());
        this.setSdDay(touSpecialDay.getSdDay());
        this.setSdMonth(touSpecialDay.getSdMonth());
        this.setSdYear(touSpecialDay.getSdYear());
    }

    public TouDayProfile getDayProfile() {
        return dayProfile;
    }

    public void setDayProfile(TouDayProfile dayProfile) {
        this.dayProfile = dayProfile;
    }
}
