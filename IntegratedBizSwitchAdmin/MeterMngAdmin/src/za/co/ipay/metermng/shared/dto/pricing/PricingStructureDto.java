package za.co.ipay.metermng.shared.dto.pricing;

import java.io.Serializable;

import za.co.ipay.metermng.datatypes.RecordStatus;
import za.co.ipay.metermng.mybatis.custom.model.HasStatus;
import za.co.ipay.metermng.mybatis.generated.model.PricingStructure;

public class PricingStructureDto implements Serializable, HasStatus {

    private static final long serialVersionUID = 1L;

    private int pricingStructurePagerIndex;
    
	private PricingStructure pricingStructure;
    private int tariffCount;
    
    public PricingStructureDto() {
        this.pricingStructure = new PricingStructure();
        this.tariffCount = 0;
    }

    public PricingStructure getPricingStructure() {
        return pricingStructure;
    }

    public void setPricingStructure(PricingStructure pricingStructure) {
        this.pricingStructure = pricingStructure;
    }

    public int getTariffCount() {
        return tariffCount;
    }

    public void setTariffCount(int tariffCount) {
        this.tariffCount = tariffCount;
    }

    @Override
    public RecordStatus getRecordStatus() {
        return pricingStructure.getRecordStatus();
    }

    @Override
    public void setRecordStatus(RecordStatus recordStatus) {
        pricingStructure.setRecordStatus(recordStatus);
    }  
    
    public int getPricingStructurePagerIndex() {
		return this.pricingStructurePagerIndex;
	}

	public void setPricingStructurePagerIndex(int pricingStructurePagerIndex) {
		this.pricingStructurePagerIndex = pricingStructurePagerIndex;
	}
}
