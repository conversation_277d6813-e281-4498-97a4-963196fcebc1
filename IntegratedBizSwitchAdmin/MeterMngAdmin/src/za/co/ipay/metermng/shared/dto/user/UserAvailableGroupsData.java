package za.co.ipay.metermng.shared.dto.user;

import java.io.Serializable;
import java.util.ArrayList;

import za.co.ipay.metermng.shared.dto.SelectionDataItem;

/**
 * UserAvailableGroupsData is used to wrap the various group related data that is required to display a user's available groups
 * so that they can select a new group.
 * <AUTHOR>
 */
public class UserAvailableGroupsData implements Serializable {

    private static final long serialVersionUID = 1L;
    
    /** The available access control groups. */
    private ArrayList<SelectionDataItem> groups;
    /** The path for the user's current group from itself to its parent. This allows the current group to be selected on the UI. */
    private ArrayList<Long> currentGroupPath;
    
    public UserAvailableGroupsData() {        
        this.groups = new ArrayList<SelectionDataItem>();
        this.currentGroupPath = new ArrayList<Long>();        
    }

    public ArrayList<SelectionDataItem> getGroups() {
        return groups;
    }

    public void setGroups(ArrayList<SelectionDataItem> groups) {
        this.groups = groups;
    }

    public ArrayList<Long> getCurrentGroupPath() {
        return currentGroupPath;
    }

    public void setCurrentGroupPath(ArrayList<Long> currentGroupPath) {
        this.currentGroupPath = currentGroupPath;
    }    
}
