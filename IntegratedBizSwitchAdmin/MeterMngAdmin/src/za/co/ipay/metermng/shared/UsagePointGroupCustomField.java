package za.co.ipay.metermng.shared;

public class UsagePointGroupCustomField {
    private String fieldName;
    private String fieldLabel;
    private String fieldStatus;
    private String fieldHelpMsg;
    private String fieldErrorMsg;
    private String fieldType;
    private String defaultValue;
    
    public String getDefaultValue() {
		return defaultValue;
	}
	public void setDefaultValue(String defaultValue) {
		this.defaultValue = defaultValue;
	}
	public String getFieldType() {
		return fieldType;
	}
	public void setFieldType(String fieldType) {
		this.fieldType = fieldType;
	}
	public String getFieldName() {
        return fieldName;
    }
    public void setFieldName(String fieldName) {
        this.fieldName = fieldName;
    }
    public String getFieldLabel() {
        return fieldLabel;
    }
    public void setFieldLabel(String fieldLabel) {
        this.fieldLabel = fieldLabel;
    }
    public String getFieldStatus() {
        return fieldStatus;
    }
    public void setFieldStatus(String fieldStatus) {
        this.fieldStatus = fieldStatus;
    }
    public String getFieldHelpMsg() {
        return fieldHelpMsg;
    }
    public void setFieldHelpMsg(String fieldHelpMsg) {
        this.fieldHelpMsg = fieldHelpMsg;
    }
    public String getFieldErrorMsg() {
        return fieldErrorMsg;
    }
    public void setFieldErrorMsg(String fieldErrorMsg) {
        this.fieldErrorMsg = fieldErrorMsg;
    }
}
