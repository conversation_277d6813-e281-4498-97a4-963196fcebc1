package za.co.ipay.metermng.shared;

import com.google.gwt.user.client.rpc.IsSerializable;
import com.google.gwt.user.client.ui.SuggestOracle.Suggestion;

public class DayProfileSuggestion implements IsSerializable, Suggestion {

    private String suggestion; 
    private Long touDayProfileId;
    @SuppressWarnings("unused")
    private Long calendarId;
    private Long seasonId;
    private String name;
    private String code;
    private String originalQuery;
    private boolean displaySuggestedCode;
    
    public DayProfileSuggestion() { } 

    public DayProfileSuggestion(Long calendarId, String suggestion, Long touDayProfileId, String dayProfileName, String dayProfileCode, boolean displaySelectedCodeOnly) {
        this.suggestion = suggestion;
        this.touDayProfileId = touDayProfileId;
        this.calendarId = calendarId;
        this.name = dayProfileName;
        this.code = dayProfileCode;
        this.displaySuggestedCode = displaySelectedCodeOnly;
    }
    
    @Override
    public String getDisplayString() {
        return suggestion;
    }

    @Override
    public String getReplacementString() {
        if (displaySuggestedCode) {
            return code;
        }
        return suggestion;
    }

    public String getSuggestion() {
        return suggestion;
    }

    public void setSuggestion(String suggestions) {
        this.suggestion = suggestions;
    }

    public Long getId() {
        return touDayProfileId;
    }

    public void setId(Long touDayProfileId) {
        this.touDayProfileId = touDayProfileId;
    }

	public String getName() {
		return name;
	}

	public void setDayProfileName(String name) {
		this.name = name;
	}
	
	public String getDayProfileCode() {
        return code;
    }

    public void setDayProfileCode(String code) {
        this.code = code;
    }
	
	public String getOriginalQuery() {
		return originalQuery;
	}

	public void setOriginalQuery(String originalQuery) {
		this.originalQuery = originalQuery;
	}

    public Long getSeasonId() {
        return seasonId;
    }

    public void setSeasonId(Long seasonId) {
        this.seasonId = seasonId;
    }

	
}
