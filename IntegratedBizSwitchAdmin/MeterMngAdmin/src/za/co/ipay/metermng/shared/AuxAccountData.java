package za.co.ipay.metermng.shared;

import za.co.ipay.metermng.mybatis.generated.model.AuxAccount;
import za.co.ipay.metermng.mybatis.generated.model.AuxChargeSchedule;
import za.co.ipay.metermng.mybatis.generated.model.AuxType;
import za.co.ipay.metermng.mybatis.generated.model.SpecialActionReasonsLog;

import com.google.gwt.user.client.rpc.IsSerializable;
import za.co.ipay.metermng.shared.dto.HistoryData;

public class AuxAccountData extends AuxAccount implements IsSerializable {
	
	private static final long serialVersionUID = 6212804424138399208L;
	
	protected AuxChargeSchedule auxChargeSchedule;
	protected AuxType auxType;
	protected String debtStatus;
	
	protected Long usagePointId = null;
    protected String errorMessage = null;
    protected boolean freeIssueAccount;
    protected SpecialActionReasonsLog specialActionReasonsLog;
	protected HistoryData historyData;


	public AuxAccountData() {
		super();
	}

	public AuxAccountData(AuxAccount auxAccount) {
		super();
		this.setId(auxAccount.getId());
		this.setMrid(auxAccount.getMrid());
		this.setMridExternal(auxAccount.isMridExternal());
		this.setAccountName(auxAccount.getAccountName());
		this.setBalance(auxAccount.getBalance());
		this.setPrincipleAmount(auxAccount.getPrincipleAmount());
		this.setAccountPriority(auxAccount.getAccountPriority());
		this.setCustomerAgreementId(auxAccount.getCustomerAgreementId());
		this.setAuxTypeId(auxAccount.getAuxTypeId());
		this.setRecordStatus(auxAccount.getRecordStatus());
		this.setSuspendUntil(auxAccount.getSuspendUntil());
		this.setAuxChargeScheduleId(auxAccount.getAuxChargeScheduleId());
		this.setCreateReasonLogId(auxAccount.getCreateReasonLogId());
		this.setUpdateReasonLogId(auxAccount.getUpdateReasonLogId());
		this.setStartDate(auxAccount.getStartDate());
		this.setLastAuxPaymentDate(auxAccount.getLastAuxPaymentDate());
	}

	public AuxChargeSchedule getAuxChargeSchedule() {
		return auxChargeSchedule;
	}
	
	public void setAuxChargeSchedule(AuxChargeSchedule auxChargeSchedule) {
		this.auxChargeSchedule = auxChargeSchedule;
	}
	
	public AuxType getAuxType() {
		return auxType;
	}
	
	public void setAuxType(AuxType auxType) {
		this.auxType = auxType;
	}
	
	public String getDebtStatus() {
        return debtStatus;
    }

    public void setDebtStatus(String debtStatus) {
        this.debtStatus = debtStatus;
    }

    public Long getUsagePointId() {
		return usagePointId;
	}
	
	public void setUsagePointId(Long usagePointId) {
		this.usagePointId = usagePointId;
	}
	
	public boolean isFreeIssueAccount() {
		return freeIssueAccount;
	}

	public void setFreeIssueAccount(boolean freeIssueAccount) {
		this.freeIssueAccount = freeIssueAccount;
	}

	public String getErrorMessage() {
		return errorMessage;
	}
    
	public boolean isValid() {
		boolean valid = true;
		errorMessage = null;
		
		return valid;
	}
	
	public SpecialActionReasonsLog getSpecialActionReasonsLog() {
		return specialActionReasonsLog;
	}

	public void setSpecialActionReasonsLog(SpecialActionReasonsLog specialActionReasonsLog) {
		this.specialActionReasonsLog = specialActionReasonsLog;
	}

}
