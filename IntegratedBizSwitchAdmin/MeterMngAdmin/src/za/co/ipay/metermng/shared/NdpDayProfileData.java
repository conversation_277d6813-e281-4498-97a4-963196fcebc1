package za.co.ipay.metermng.shared;

import za.co.ipay.metermng.mybatis.generated.model.NdpDayProfile;

import com.google.gwt.user.client.rpc.IsSerializable;

public class NdpDayProfileData implements IsSerializable {

   
    private static final long serialVersionUID = -5529151338861867578L;
    
    private NdpDayProfile ndpDayProfile;
    private long startMs; 
    private long endMs;
    
    public NdpDayProfileData() {
    }

    public NdpDayProfileData(NdpDayProfile ndpDayProfile) {
        this.ndpDayProfile = ndpDayProfile;
    }
    
    public NdpDayProfile getNdpDayProfile() {
        return ndpDayProfile;
    }

    public void setNdpDayProfile(NdpDayProfile ndpDayProfile) {
        this.ndpDayProfile = ndpDayProfile;
    }

    public long getStartMs() {
        return startMs;
    }

    public void setStartMs(long startMs) {
        this.startMs = startMs;
    }

    public long getEndMs() {
        return endMs;
    }

    public void setEndMs(long endMs) {
        this.endMs = endMs;
    }
    
}
