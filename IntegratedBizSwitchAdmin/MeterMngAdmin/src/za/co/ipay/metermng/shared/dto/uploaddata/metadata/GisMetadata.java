package za.co.ipay.metermng.shared.dto.uploaddata.metadata;

import java.io.Serializable;

import com.google.gwt.regexp.shared.MatchResult;
import com.google.gwt.regexp.shared.RegExp;
import com.google.gwt.user.client.rpc.IsSerializable;

public class GisMetadata implements Serializable, IsSerializable {
    public static final String GIS_REGEXP = "\\{gis:\\{lat:(\\-?\\d{1,2}\\.\\d+),lon:(\\-?\\d{1,3}\\.\\d+)\\}\\}";
    private static final long serialVersionUID = 181110754590190L;
    private static final double MAX_ABS_LAT = 90;
    private static final double MAX_ABS_LON = 180;
    private double lat, lon;
    private String desc;

    public GisMetadata() {
    }

    public GisMetadata(double lat, double lon) {
        this.lat = lat;
        this.lon = lon;
    }

    public GisMetadata(double lat, double lon, String desc) {
        this(lat, lon);
        this.desc = desc;
    }

    public static GisMetadata parse(String gisInfo) {
        RegExp regExp = RegExp.compile(GIS_REGEXP);
        if (regExp.test(gisInfo)) {
            MatchResult result = regExp.exec(gisInfo);
            double lat = Double.parseDouble(result.getGroup(1));
            double lon = Double.parseDouble(result.getGroup(2));
            if (Math.abs(lat) <= MAX_ABS_LAT && Math.abs(lon) <= MAX_ABS_LON) {
                return new GisMetadata(lat, lon);
            }
        }
        return null;
    }

    public static boolean isValidLat(double lat) {
        return Math.abs(lat) <= MAX_ABS_LAT;
    }

    public static boolean isValidLon(double lat) {
        return Math.abs(lat) <= MAX_ABS_LON;
    }

    public double getLat() {
        return lat;
    }

    public void setLat(double lat) {
        this.lat = lat;
    }

    public double getLon() {
        return lon;
    }

    public void setLon(double lon) {
        this.lon = lon;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public String toJson() {
        return "{gis:{lat:" + lat + ",lon:" + lon + "}}";
    }

    @Override
    public String toString() {
        return getClass().getSimpleName() + "[lat:" + lat + ",lon:" + lon + "]";
    }

    @Override
    public boolean equals(Object obj) {
        if (obj == this) return true;
        if (obj instanceof GisMetadata) {
            return ((GisMetadata) obj).lat == this.lat && ((GisMetadata) obj).lon == this.lon;
        }
        return false;
    }

    @Override
    public int hashCode() {
        return toJson().hashCode();
    }
}
