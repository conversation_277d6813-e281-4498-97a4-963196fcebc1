package za.co.ipay.metermng.shared;

import za.co.ipay.metermng.datatypes.RecordStatus;
import za.co.ipay.metermng.mybatis.generated.model.GroupHierarchy;

import com.google.gwt.user.client.rpc.IsSerializable;

/**
 * GroupHierarchyData is an extension to the usual GroupHierarchy class which also have a list of children GroupHierarchy
 * instances for the current GroupHierarchy. This allows the data to be viewed in a hierarchical structure like a tree.
 * <AUTHOR>
 */
public class GroupHierarchyData extends GroupHierarchy implements IsSerializable {

    private static final long serialVersionUID = 1L;

    private Integer level;
    
    public GroupHierarchyData() {
        setRecordStatus(RecordStatus.ACT);
    }
    
    public Integer getLevel() {
        return level;
    }

    public void setLevel(Integer level) {
        this.level = level;
    }

    @Override
    public String toString() {
        return "GroupHiearchyData: [id="+getId()+"]";
    }
}
