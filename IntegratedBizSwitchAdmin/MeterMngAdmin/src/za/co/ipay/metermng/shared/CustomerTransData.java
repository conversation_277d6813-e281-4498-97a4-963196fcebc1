package za.co.ipay.metermng.shared;

import java.util.ArrayList;

import za.co.ipay.metermng.mybatis.generated.model.CustomerTrans;
import za.co.ipay.metermng.mybatis.generated.model.CustomerTransType;
import za.co.ipay.metermng.mybatis.generated.model.UsagePoint;
import za.co.ipay.metermng.shared.dto.CustomerAgreementData;

import com.google.gwt.user.client.rpc.IsSerializable;

public class CustomerTransData extends CustomerTrans implements IsSerializable {
	
	private static final long serialVersionUID = 4977589775578775660L;
	
	private CustomerAgreementData customerAgreementData;
	private UsagePoint usagePoint;
	private ArrayList<CustomerTransItemData> transItems = new ArrayList<CustomerTransItemData>();
	private CustomerTransType transType;
	
	public CustomerTransData() {
	    super();
	}

	public CustomerTransData(CustomerTrans customerTrans) {
	    super();
	    this.setId(customerTrans.getId());
	    this.setCustomerAgreementId(customerTrans.getCustomerAgreementId());
	    this.setUsagePointId(customerTrans.getUsagePointId());
	    this.setMeterId(customerTrans.getMeterId());
	    this.setClient(customerTrans.getClient());
	    this.setTerminal(customerTrans.getTerminal());
	    this.setVendRefReceived(customerTrans.getVendRefReceived());
	    this.setRevRefReceived(customerTrans.getRevRefReceived());
	    this.setReceiptNum(customerTrans.getReceiptNum());
	    this.setSerialNum(customerTrans.getSerialNum());
	    this.setMeterNumber(customerTrans.getMeterNumber());
	    this.setTransDate(customerTrans.getTransDate());
	    this.setAmtTax(customerTrans.getAmtTax());
	    this.setAmtInclTax(customerTrans.getAmtInclTax());
	    this.setTariffClass(customerTrans.getTariffClass());
	    this.setTariffCalcDetails(customerTrans.getTariffCalcDetails());
	    this.setHasEngineeringTokens(customerTrans.isHasEngineeringTokens());
	    this.setIsFreeIssue(customerTrans.isIsFreeIssue());
	    this.setUnitsRoundingError(customerTrans.getUnitsRoundingError());
	    this.setUnitsRoundingMode(customerTrans.getUnitsRoundingMode());
	    this.setUserRecEntered(customerTrans.getUserRecEntered());
	    this.setPayTypeId(customerTrans.getPayTypeId());
	    this.setPayTypeDetailsId(customerTrans.getPayTypeDetailsId());
	    this.setAuxAccountId(customerTrans.getAuxAccountId());
	    this.setCustomerAccountId(customerTrans.getCustomerAccountId());
	    this.setCustomerTransTypeId(customerTrans.getCustomerTransTypeId());
	    this.setPaymentModeId(customerTrans.getPaymentModeId());
	    this.setServiceResourceId(customerTrans.getServiceResourceId());
	    this.setMeterTypeId(customerTrans.getMeterTypeId());
	    this.setReversed(customerTrans.isReversed());
	}

	public ArrayList<CustomerTransItemData> getTransItems() {
		return transItems;
	}

	public void setTransItems(ArrayList<CustomerTransItemData> transItems) {
		this.transItems = transItems;
	}

	public CustomerAgreementData getCustomerAgreementData() {
		return customerAgreementData;
	}

	public void setCustomerAgreementData(CustomerAgreementData customerAgreementData) {
		this.customerAgreementData = customerAgreementData;
	}

	public UsagePoint getUsagePoint() {
		return usagePoint;
	}

	public void setUsagePoint(UsagePoint usagePoint) {
		this.usagePoint = usagePoint;
	}

    public CustomerTransType getTransType() {
        return transType;
    }

    public void setTransType(CustomerTransType transType) {
        this.transType = transType;
    }
	
	

}
