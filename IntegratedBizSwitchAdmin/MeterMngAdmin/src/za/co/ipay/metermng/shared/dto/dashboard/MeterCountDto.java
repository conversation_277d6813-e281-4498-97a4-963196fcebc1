package za.co.ipay.metermng.shared.dto.dashboard;

import java.io.Serializable;

public class MeterCountDto implements Serializable {

    private static final long serialVersionUID = -4156002556573668528L;
    
    private String theName; //eg meterModel name if counting by meterModel
    private int meterCount;
    
    
    public MeterCountDto() {
        
    }

    public MeterCountDto(String name, int count) {
        super();
        this.theName = name;
        this.meterCount = count;
    }


    public String getName() {
        return theName;
    }


    public int getCount() {
        return meterCount;
    }


    public void setName(String theName) {
        this.theName = theName;
    }


    public void setCount(int theCount) {
        this.meterCount = theCount;
    }

    
    
}
