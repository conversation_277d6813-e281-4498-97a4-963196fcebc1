package za.co.ipay.metermng.shared.dto.meter;

import java.io.Serializable;

public class MeterSearchDto implements Serializable {

    private static final long serialVersionUID = 1L;

    private String meterNumber;
    private String paymentMode;
    private Long deviceStoreId;
    
    public MeterSearchDto() {
        //default constructor
    }
    
    public MeterSearchDto(String meterNumber) {
        this.meterNumber = meterNumber;
        this.paymentMode = null;
    }
    
    public MeterSearchDto(String meterNumber, String paymentMode) {
        this.meterNumber = meterNumber;
        this.paymentMode = paymentMode;
    }
    
    public MeterSearchDto(String meterNumber, Long deviceStoreId) {
        this.meterNumber = meterNumber;
        this.deviceStoreId = deviceStoreId;
    }

    public String getMeterNumber() {
        return meterNumber;
    }

    public void setMeterNumber(String meterNumber) {
        this.meterNumber = meterNumber;
    }

    public String getPaymentMode() {
        return paymentMode;
    }

    public void setPaymentMode(String paymentMode) {
        this.paymentMode = paymentMode;
    }

    public Long getDeviceStoreId() {
        return deviceStoreId;
    }

    public void setDeviceStoreId(Long deviceStoreId) {
        this.deviceStoreId = deviceStoreId;
    }
    
}