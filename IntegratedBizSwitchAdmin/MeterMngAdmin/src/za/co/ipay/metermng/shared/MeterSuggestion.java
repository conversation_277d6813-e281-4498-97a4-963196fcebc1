package za.co.ipay.metermng.shared;

import za.co.ipay.metermng.mybatis.custom.model.MeterDto;

import com.google.gwt.user.client.ui.SuggestOracle;

public class MeterSuggestion implements SuggestOracle.Suggestion {
    
    private MeterDto meter;
    
    public MeterSuggestion(MeterDto meter) {
        this.meter = meter;
    }

    @Override
    public String getDisplayString() {
        return meter.getNumber();
    }

    @Override
    public String getReplacementString() {
        return meter.getNumber();
    }

    public MeterDto getMeter() {
        return meter;
    }
}
