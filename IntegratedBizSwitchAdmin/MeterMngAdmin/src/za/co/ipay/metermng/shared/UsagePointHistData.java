package za.co.ipay.metermng.shared;

import za.co.ipay.metermng.mybatis.generated.model.UsagePoint;
import za.co.ipay.metermng.mybatis.generated.model.UsagePointHist;

import com.google.gwt.user.client.rpc.IsSerializable;

public class UsagePointHistData extends UsagePointHist implements IsSerializable {

	private static final long serialVersionUID = -377249777686031484L;

	protected boolean usagePointHistIdChanged = false;
    protected boolean idChanged = false;
    protected boolean mridChanged = false;
    protected boolean mridExternalChanged = false;
    protected boolean nameChanged = false;
    protected boolean serviceLocationIdChanged = false;
    protected boolean upLocationIdChanged = false;
    protected boolean meterIdChanged = false;
    protected boolean customerAgreementIdChanged = false;
    protected boolean blockingTypeIdChanged = false;
    protected boolean replaceReasonLogIdChanged = false;
    protected boolean blockReasonLogIdChanged = false;
    protected boolean activeStatusReasonLogIdChanged = false;
    protected boolean serviceKindIdChanged = false;
    protected boolean recordStatusChanged = false;
    protected boolean deviceMoveRefChanged = false;
    
    protected boolean customVarchar1Changed = false;
    protected boolean customVarchar2Changed = false;
    protected boolean customNumeric1Changed = false;
    protected boolean customNumeric2Changed = false;
    protected boolean customTimestamp1Changed = false;
    protected boolean customTimestamp2Changed = false;
    
    protected String meterNumber;
    protected String initials;
    protected String surname;
    protected String serviceLocationErf;
    protected String customerAgreementRef;
    protected String blockingTypeName;

    private String replaceReasonLogText;
    private String blockReasonLogText;
    private String statusReasonLogText;

	public UsagePointHistData() {
		
	}
	
	public UsagePointHistData(UsagePoint usagepoint, String user) {
		this.setId(usagepoint.getId());
		this.setMrid(usagepoint.getMrid());
		this.setMridExternal(usagepoint.isMridExternal());
		this.setName(usagepoint.getName());
		this.setServiceLocationId(usagepoint.getServiceLocationId());
		this.setUpLocationId(usagepoint.getUpLocationId());
		this.setMeterId(usagepoint.getMeterId());
		this.setCustomerAgreementId(usagepoint.getCustomerAgreementId());
		this.setBlockingTypeId(usagepoint.getBlockingTypeId());
		this.setBlockingStartDate(usagepoint.getBlockingStartDate());
		this.setBlockReasonLogId(usagepoint.getBlockReasonLogId());
		this.setReplaceReasonLogId(usagepoint.getReplaceReasonLogId());
		this.setActiveStatusReasonLogId(usagepoint.getActiveStatusReasonLogId());
		this.setRecordStatus(usagepoint.getRecordStatus());

        this.setCustomVarchar1(usagepoint.getCustomVarchar1());
        this.setCustomVarchar2(usagepoint.getCustomVarchar2());
        this.setCustomNumeric1(usagepoint.getCustomNumeric1());
        this.setCustomNumeric2(usagepoint.getCustomNumeric2());
        this.setCustomTimestamp1(usagepoint.getCustomTimestamp1());
        this.setCustomTimestamp2(usagepoint.getCustomTimestamp2());
		
		this.setUserRecEntered(user);
	}
	
    public UsagePointHistData(UsagePoint usagepoint, String user, String meterNumber, String initials, String surname,
            String serviceLocationErf, String customerAgreementRef,
            String blockingTypeName, String blockReasonLogText, String replaceReasonLogText) {
        this(usagepoint, user);

        this.setMeterNumber(meterNumber);
        this.setInitials(initials);
        this.setSurname(surname);
        this.setServiceLocationErf(serviceLocationErf);
        this.setCustomerAgreementRef(customerAgreementRef);
        this.setBlockingTypeName(blockingTypeName);
        this.setBlockReasonLogText(blockReasonLogText);
        this.setReplaceReasonLogText(replaceReasonLogText);
    }
	
	public UsagePointHistData(UsagePointHist usagepointhist) {
		this.setId(usagepointhist.getId());
		this.setMrid(usagepointhist.getMrid());
		this.setMridExternal(usagepointhist.isMridExternal());
		this.setName(usagepointhist.getName());
		this.setServiceLocationId(usagepointhist.getServiceLocationId());
		this.setUpLocationId(usagepointhist.getUpLocationId());
		this.setMeterId(usagepointhist.getMeterId());
		this.setCustomerAgreementId(usagepointhist.getCustomerAgreementId());
		this.setBlockingTypeId(usagepointhist.getBlockingTypeId());
		this.setBlockingStartDate(usagepointhist.getBlockingStartDate());
		this.setBlockReasonLogId(usagepointhist.getBlockReasonLogId());
		this.setReplaceReasonLogId(usagepointhist.getReplaceReasonLogId());
		this.setActiveStatusReasonLogId(usagepointhist.getActiveStatusReasonLogId());
		this.setRecordStatus(usagepointhist.getRecordStatus());
		
		this.setUserRecEntered(usagepointhist.getUserRecEntered());
    	this.setUserAction(usagepointhist.getUserAction());
    	this.setDateRecModified(usagepointhist.getDateRecModified());
        
        this.setCustomVarchar1(usagepointhist.getCustomVarchar1());
        this.setCustomVarchar2(usagepointhist.getCustomVarchar2());
        this.setCustomNumeric1(usagepointhist.getCustomNumeric1());
        this.setCustomNumeric2(usagepointhist.getCustomNumeric2());
        this.setCustomTimestamp1(usagepointhist.getCustomTimestamp1());
        this.setCustomTimestamp2(usagepointhist.getCustomTimestamp2());
	}

	public boolean isUsagePointHistIdChanged() {
		return usagePointHistIdChanged;
	}

	public void setUsagePointHistIdChanged(boolean usagePointHistIdChanged) {
		this.usagePointHistIdChanged = usagePointHistIdChanged;
	}

	public boolean isIdChanged() {
		return idChanged;
	}

	public void setIdChanged(boolean idChanged) {
		this.idChanged = idChanged;
	}

	public boolean isMridChanged() {
		return mridChanged;
	}

	public void setMridChanged(boolean mridChanged) {
		this.mridChanged = mridChanged;
	}

	public boolean isMridExternalChanged() {
		return mridExternalChanged;
	}

	public void setMridExternalChanged(boolean mridExternalChanged) {
		this.mridExternalChanged = mridExternalChanged;
	}

	public boolean isNameChanged() {
		return nameChanged;
	}

	public void setNameChanged(boolean nameChanged) {
		this.nameChanged = nameChanged;
	}

	public boolean isServiceLocationIdChanged() {
		return serviceLocationIdChanged;
	}

	public void setServiceLocationIdChanged(boolean serviceLocationIdChanged) {
		this.serviceLocationIdChanged = serviceLocationIdChanged;
	}

	public boolean isUpLocationIdChanged() {
		return upLocationIdChanged;
	}

	public void setUpLocationIdChanged(boolean upLocationIdChanged) {
		this.upLocationIdChanged = upLocationIdChanged;
	}

	public boolean isMeterIdChanged() {
		return meterIdChanged;
	}

	public void setMeterIdChanged(boolean meterIdChanged) {
		this.meterIdChanged = meterIdChanged;
	}

	public boolean isCustomerAgreementIdChanged() {
		return customerAgreementIdChanged;
	}

	public void setCustomerAgreementIdChanged(boolean customerAgreementIdChanged) {
		this.customerAgreementIdChanged = customerAgreementIdChanged;
	}

	public boolean isServiceKindIdChanged() {
		return serviceKindIdChanged;
	}

	public void setServiceKindIdChanged(boolean serviceKindIdChanged) {
		this.serviceKindIdChanged = serviceKindIdChanged;
	}

	public boolean isRecordStatusChanged() {
		return recordStatusChanged;
	}

	public void setRecordStatusChanged(boolean recordStatusChanged) {
		this.recordStatusChanged = recordStatusChanged;
	}

	public String getMeterNumber() {
		return meterNumber;
	}

	public void setMeterNumber(String meterNumber) {
		this.meterNumber = meterNumber;
	}

	public String getCustomerName() {
	    if (initials == null && surname == null) {
	        return "";
	    } 
	    if (initials == null && surname != null) {
            return surname;
        } 
	    if (initials != null && surname == null) {
            return initials;
        } 
		return initials + " " + surname;
	}

	public String getServiceLocationErf() {
		return serviceLocationErf;
	}

	public void setServiceLocationErf(String serviceLocationErf) {
		this.serviceLocationErf = serviceLocationErf;
	}

	public String getCustomerAgreementRef() {
		return customerAgreementRef;
	}

	public void setCustomerAgreementRef(String customerAgreementRef) {
		this.customerAgreementRef = customerAgreementRef;
	}

    public String getInitials() {
		return initials;
	}

	public void setInitials(String initials) {
		this.initials = initials;
	}

	public String getSurname() {
		return surname;
	}

	public void setSurname(String surname) {
		this.surname = surname;
	}

	public String getBlockingTypeName() {
		return blockingTypeName;
	}

	public void setBlockingTypeName(String blockingTypeName) {
		this.blockingTypeName = blockingTypeName;
	}

	public String getReplaceReasonLogText() {
		return replaceReasonLogText;
	}

	public void setReplaceReasonLogText(String replaceReasonLogText) {
		this.replaceReasonLogText = replaceReasonLogText;
	}

	public String getBlockReasonLogText() {
		return blockReasonLogText;
	}

	public void setBlockReasonLogText(String blockReasonLogText) {
		this.blockReasonLogText = blockReasonLogText;
	}

	public String getStatusReasonLogText() {
		return statusReasonLogText;
	}

	public void setStatusReasonLogText(String statusReasonLogText) {
		this.statusReasonLogText = statusReasonLogText;
	}

	public boolean isCustomVarchar1Changed() {
        return customVarchar1Changed;
    }

    public void setCustomVarchar1Changed(boolean customVarchar1Changed) {
        this.customVarchar1Changed = customVarchar1Changed;
    }

    public boolean isCustomVarchar2Changed() {
        return customVarchar2Changed;
    }

    public void setCustomVarchar2Changed(boolean customVarchar2Changed) {
        this.customVarchar2Changed = customVarchar2Changed;
    }

    public boolean isCustomNumeric1Changed() {
        return customNumeric1Changed;
    }

    public void setCustomNumeric1Changed(boolean customNumeric1Changed) {
        this.customNumeric1Changed = customNumeric1Changed;
    }

    public boolean isCustomNumeric2Changed() {
        return customNumeric2Changed;
    }

    public void setCustomNumeric2Changed(boolean customNumeric2Changed) {
        this.customNumeric2Changed = customNumeric2Changed;
    }

    public boolean isCustomTimestamp1Changed() {
        return customTimestamp1Changed;
    }

    public void setCustomTimestamp1Changed(boolean customTimestamp1Changed) {
        this.customTimestamp1Changed = customTimestamp1Changed;
    }

    public boolean isCustomTimestamp2Changed() {
        return customTimestamp2Changed;
    }

    public void setCustomTimestamp2Changed(boolean customTimestamp2Changed) {
        this.customTimestamp2Changed = customTimestamp2Changed;
    }

	public boolean isBlockingTypeIdChanged() {
		return blockingTypeIdChanged;
	}

	public void setBlockingTypeIdChanged(boolean blockingTypeIdChanged) {
		this.blockingTypeIdChanged = blockingTypeIdChanged;
	}

	public boolean isReplaceReasonLogIdChanged() {
		return replaceReasonLogIdChanged;
	}

	public void setReplaceReasonLogIdChanged(boolean replaceReasonLogIdChanged) {
		this.replaceReasonLogIdChanged = replaceReasonLogIdChanged;
	}

	public boolean isBlockReasonLogIdChanged() {
		return blockReasonLogIdChanged;
	}

	public void setBlockReasonLogIdChanged(boolean blockReasonLogIdChanged) {
		this.blockReasonLogIdChanged = blockReasonLogIdChanged;
	}

	public boolean isActiveStatusReasonLogIdChanged() {
		return activeStatusReasonLogIdChanged;
	}

	public void setActiveStatusReasonLogIdChanged(boolean activeStatusReasonLogIdChanged) {
		this.activeStatusReasonLogIdChanged = activeStatusReasonLogIdChanged;
	}

    public void setDeviceMoveRefChanged(boolean deviceMoveRefChanged) {
        this.deviceMoveRefChanged = deviceMoveRefChanged;
    }

    public boolean isDeviceMoveRefChanged() {
        return deviceMoveRefChanged;
    }

}
