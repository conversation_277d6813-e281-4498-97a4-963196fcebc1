package za.co.ipay.metermng.shared.dto.uploaddata.transuploaddata;

import java.io.Serializable;
import java.util.HashMap;

import za.co.ipay.metermng.shared.bulkupload.dto.BulkCsvData;

public class TransCsvData extends BulkCsvData implements Serializable {

	private static final long serialVersionUID = 1L;

	private String amtInclTax;
	private String amtTax;
	private String transDate;
	private String accountRef;
	private String comment;

	public TransCsvData(HashMap<Integer, String> csvFieldMap, String stringOfFieldsCommaSeperated, boolean withErrorString) {
		super(csvFieldMap, stringOfFieldsCommaSeperated, withErrorString);

		this.setAmtInclTax(beanDataMap.get("amtInclTax"));
		this.setAmtTax(beanDataMap.get("amtTax"));
		this.setTransDate(beanDataMap.get("transDate"));
		this.setAccountRef(beanDataMap.get("accountRef"));
		this.setComment(beanDataMap.get("comment"));
	}

	public String getAmtInclTax() {
		return amtInclTax;
	}

	public void setAmtInclTax(String amtInclTax) {
		this.amtInclTax = amtInclTax;
	}

	public String getAmtTax() {
		return amtTax;
	}

	public void setAmtTax(String amtTax) {
		this.amtTax = amtTax;
	}

	public String getTransDate() {
		return transDate;
	}

	public void setTransDate(String transDate) {
		this.transDate = transDate;
	}

	public String getAccountRef() {
		return accountRef;
	}

	public void setAccountRef(String accountRef) {
		this.accountRef = accountRef;
	}

	public String getComment() {
		return comment;
	}

	public void setComment(String comment) {
		this.comment = comment;
	}

	@Override
	public String toString() {
		return "TransCsvData [amtInclTax=" + amtInclTax + ", amtTax=" + amtTax + ", transDate=" + transDate
				+ ", accountRef=" + accountRef + ", comment=" + comment + "]";
	}

}
