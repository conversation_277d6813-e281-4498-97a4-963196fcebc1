package za.co.ipay.metermng.shared.dto.meter;

import java.io.Serializable;
import java.util.Date;

public class MeterReadingDto implements Serializable {
    
    private static final long serialVersionUID = 1L;

    private String meterNumber;
    private Date created;
    private Date start;
    private Date end;    
    private Double reading;
    private String receiptNum;
    
    public MeterReadingDto() {
      
    }
    
    public MeterReadingDto(String meterNumber, Date created, Date start, Date end, Double reading, String receiptNum) {
        this.meterNumber = meterNumber;
        this.created = created;
        this.start = start;
        this.end = end;
        this.reading = reading;
        this.receiptNum = receiptNum;
    }

    public String getMeterNumber() {
        return meterNumber;
    }
    
    public Date getCreated() {
        return created;
    }

    public void setCreated(Date created) {
        this.created = created;
    }

    public Date getStart() {
        return start;
    }

    public void setStart(Date start) {
        this.start = start;
    }

    public Date getEnd() {
        return end;
    }

    public void setEnd(Date end) {
        this.end = end;
    }

    public double getReading() {
        return reading;
    }

    public void setReading(double reading) {
        this.reading = reading;
    }

	public String getReceiptNum() {
		return receiptNum;
	}

	public void setReceiptNum(String receiptNum) {
		this.receiptNum = receiptNum;
	}
}
