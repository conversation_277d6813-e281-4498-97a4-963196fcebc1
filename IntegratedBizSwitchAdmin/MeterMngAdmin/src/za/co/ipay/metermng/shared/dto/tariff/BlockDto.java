package za.co.ipay.metermng.shared.dto.tariff;

import java.io.Serializable;

import za.co.ipay.metermng.shared.tariff.block.Block;

public class BlockDto implements Serializable {

    private static final long serialVersionUID = 1L;
    
    private Block block;
    private int position;

    public BlockDto() {
        block = new Block();
        position = 1;
    }
    
    public BlockDto(Block block, int position) {
        this.block = block;
        this.position = position;
    }
    
    public int getPosition() {
        return position;
    }

    public void setPosition(int position) {
        this.position = position;
    }

    public Block getBlock() {
        return block;
    }

    public void setBlock(Block block) {
        this.block = block;
    }    
}
