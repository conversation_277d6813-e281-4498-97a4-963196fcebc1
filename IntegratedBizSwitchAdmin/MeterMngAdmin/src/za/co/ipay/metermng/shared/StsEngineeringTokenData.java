package za.co.ipay.metermng.shared;

import com.google.gwt.user.client.rpc.IsSerializable;
import za.co.ipay.metermng.mybatis.generated.model.StsEngineeringToken;

public class StsEngineeringTokenData extends StsEngineeringToken implements IsSerializable {

    private static final long serialVersionUID = -8665323739801767436L;
    private String usagePointName;
    private String meterNumber;
    private String tokenTypeName;
    private String stsAlgorithmCode;
    private String importFileName;
    
    public StsEngineeringTokenData() {
        super();
    }
    
    public StsEngineeringTokenData(StsEngineeringToken stsEngineeringToken) {
        super();
        this.setId(stsEngineeringToken.getId());
        this.setCustomerTransId(stsEngineeringToken.getCustomerTransId());
        this.setUsagePointId(stsEngineeringToken.getUsagePointId());
        this.setMeterId(stsEngineeringToken.getMeterId());
        this.setStsEngTokenTypeId(stsEngineeringToken.getStsEngTokenTypeId());
        this.setToken1(stsEngineeringToken.getToken1());
        this.setToken2(stsEngineeringToken.getToken2());
        this.setToken3(stsEngineeringToken.getToken3());
        this.setToken4(stsEngineeringToken.getToken4());
        this.setOldSupGroup(stsEngineeringToken.getOldSupGroup());
        this.setNewSupGroup(stsEngineeringToken.getNewSupGroup());
        this.setOldTariffIdx(stsEngineeringToken.getOldTariffIdx());
        this.setNewTariffIdx(stsEngineeringToken.getNewTariffIdx());
        this.setOldKeyRev(stsEngineeringToken.getOldKeyRev());
        this.setNewKeyRev(stsEngineeringToken.getNewKeyRev());
        this.setUnits(stsEngineeringToken.getUnits());
        this.setClearTid(stsEngineeringToken.isClearTid());
        this.setDescription(stsEngineeringToken.getDescription());
        this.setTransDate(stsEngineeringToken.getTransDate());
        this.setUserRecEntered(stsEngineeringToken.getUserRecEntered());
        this.setUserRef(stsEngineeringToken.getUserRef());
        this.setBulkRef(stsEngineeringToken.getBulkRef());
    }

    public String getUsagePointName() {
        return usagePointName;
    }

    public void setUsagePointName(String usagePointName) {
        this.usagePointName = usagePointName;
    }

    public String getMeterNumber() {
        return meterNumber;
    }

    public void setMeterNumber(String meterNumber) {
        this.meterNumber = meterNumber;
    }

    public String getTokenTypeName() {
        return tokenTypeName;
    }

    public void setTokenTypeName(String tokenTypeName) {
        this.tokenTypeName = tokenTypeName;
    }

    public String getStsAlgorithmCode() {
        return stsAlgorithmCode;
    }

    public void setStsAlgorithmCode(String stsAlgorithmCode) {
        this.stsAlgorithmCode = stsAlgorithmCode;
    }

    public String getImportFileName() {
        return importFileName;
    }

    public void setImportFileName(String importFileName) {
        this.importFileName = importFileName;
    }

    @Override
    public String toString() {
        return "StsEngineeringTokenData [usagePointName=" + usagePointName
                + ", meterNumber=" + meterNumber + ", tokenTypeName="
                + tokenTypeName + ", stsAlgorithmCode=" + stsAlgorithmCode
                + ", importFileName=" + importFileName + "]";
    }
}
