package za.co.ipay.metermng.rebind;

import java.io.PrintWriter;
import java.lang.annotation.Annotation;
import java.util.ArrayList;
import java.util.List;

import com.google.gwt.core.ext.Generator;
import com.google.gwt.core.ext.GeneratorContext;
import com.google.gwt.core.ext.TreeLogger;
import com.google.gwt.core.ext.UnableToCompleteException;
import com.google.gwt.core.ext.typeinfo.JClassType;
import com.google.gwt.core.ext.typeinfo.JMethod;
import com.google.gwt.core.ext.typeinfo.NotFoundException;
import com.google.gwt.core.ext.typeinfo.TypeOracle;
import com.google.gwt.i18n.client.LocalizableResource;
import com.google.gwt.user.rebind.ClassSourceFileComposerFactory;
import com.google.gwt.user.rebind.SourceWriter;

public class UiMessagesGenerator extends Generator {

    private static final String SUPER_CLASS = null;
    private static final String THE_INTERFACE = "UiMessages";
    private static final String MESSAGES_SETTER_NAME = "setMessages";

    @Override
    public String generate(TreeLogger logger, GeneratorContext context, String typeName) throws UnableToCompleteException {
        logger.log(TreeLogger.DEBUG, "Starting Generator");
        try {
            TypeOracle types = context.getTypeOracle();
            JClassType type = types.getType(typeName);
            String packageName = type.getPackage().getName();
            String simpleClassName = type.getSimpleSourceName();
            String implName = simpleClassName + "Impl";
            String qualifiedName = packageName + "." + implName;

            List<String> theImports = new ArrayList<String>();
            theImports.add("java.util.Map");
            theImports.add("java.util.HashMap");

            SourceWriter srcWriter = getSourceWriter(logger, context, packageName, implName, SUPER_CLASS, THE_INTERFACE, theImports);
            if (srcWriter != null) {
                logger.log(TreeLogger.DEBUG, "Starting to generate code");

                writeClassAttributes(logger, srcWriter);
                writeConstructor(logger, srcWriter, implName);
                writeMessagesSetterMethod(logger, srcWriter, implName);
                for (JMethod method : type.getMethods()) {
                    if (!method.isStatic() && !MESSAGES_SETTER_NAME.equals(method.getName())) {
                        writeMethod(logger, srcWriter, implName, method);
                    }
                }

                srcWriter.commit(logger);
                logger.log(TreeLogger.DEBUG, "Finished generating code");
            }

            return qualifiedName;
        } catch (NotFoundException e) {
            throw new UnableToCompleteException();
        }
    }

    private SourceWriter getSourceWriter(TreeLogger logger, GeneratorContext context, String packageName, String className, String superclassName, String theInterface, List<String> imports) {
        PrintWriter printWriter = context.tryCreate(logger, packageName, className);
        if (printWriter == null) {
            return null;
        }
        ClassSourceFileComposerFactory composerFactory = new ClassSourceFileComposerFactory(packageName, className);
        if (imports != null) {
            for (String imprt : imports) {
                composerFactory.addImport(imprt);
            }
        }
        if (superclassName != null) {
            composerFactory.setSuperclass(superclassName);
        }
        if (theInterface != null) {
            composerFactory.addImplementedInterface(theInterface);
        }
        return composerFactory.createSourceWriter(context, printWriter);
    }

    private void writeClassAttributes(TreeLogger logger, SourceWriter writer) {
        writer.println("");
        writer.println("private Map<String, String> messages;");
        writer.println("");
    }

    private void writeConstructor(TreeLogger logger, SourceWriter writer, String simpleClassName) {
        logger.log(TreeLogger.DEBUG, "Writing constructor");
        writer.println("public UiMessagesImpl() {");
        writer.indent();
        writer.println("super();");
        writer.println("this.messages = new HashMap<String, String>();");
        writer.outdent();
        writer.println("}");
        writer.println("");
    }
    
    private void writeMessagesSetterMethod(TreeLogger logger, SourceWriter writer, String simpleClassName) {
        logger.log(TreeLogger.DEBUG, "Writing message's setter");
        writer.println("public void setMessages(Map<String, String> messages) {");
        writer.indent();
        writer.println("this.messages = messages;");
        writer.outdent();
        writer.println("}");
        writer.println("");
    }

    public void writeMethod(TreeLogger logger, SourceWriter writer, String typeName, JMethod method) {
        if (method.isPrivate()) {
            return;
        }
        logger.log(TreeLogger.DEBUG, "Implementing " + method.getReadableDeclaration());

        writer.println("@Override");
        writer.println("public String "+method.getName()+"()");
        writer.println("{");
        writer.indent();
        String key = getKey(logger, method);
        writer.print("String key = \"");
        writer.print(key);
        writer.println("\";");
        writer.println("if (messages.containsKey(key)) {");
        writer.println("return messages.get(key);");
        writer.println("} else {");
        writer.println("return key;");
        writer.println("}");
        writer.outdent();
        writer.println("}");
        writer.println();
    }
    
    private String getKey(TreeLogger logger, JMethod method) {
        String key = method.getName();
        if (method.getAnnotations() != null) {
            for(Annotation a : method.getAnnotations()) {
                logger.log(TreeLogger.DEBUG, "Current annotation: "+a.toString());
                if (a instanceof LocalizableResource.Key) {
                    LocalizableResource.Key keyAnnotation = (LocalizableResource.Key) a;
                    String value = keyAnnotation.value();
                    if (value != null && !value.trim().equals("")) {
                        logger.log(TreeLogger.DEBUG, "Using key: "+value);
                        return value;
                    }
                }
            }
        }
        return key;
    }
}
