package za.co.ipay.metermng.client.history;

import java.util.Date;
import java.util.logging.Logger;

import za.co.ipay.gwt.common.shared.validation.ValidateUtil;
import za.co.ipay.metermng.shared.MeterMngStatics;

import com.google.gwt.i18n.client.DateTimeFormat;
import com.google.gwt.place.shared.Place;
import com.google.gwt.place.shared.PlaceTokenizer;
import com.google.gwt.place.shared.Prefix;

/**
 * MeterReadingsPlace is used to get to the Meter Readings screen.
 * <AUTHOR>
 */
public class MeterReadingsPlace extends Place {
    
    public static final String ALL_SUPER_METERS = "all";
    public static final String USAGE_POINT_METER = "usagePointMeter";
        
    private String superMeter;
    private String graphType;
    private String readingType;
    private Date startDate;
    private Date endDate;    
    
    private static Logger logger = Logger.getLogger(MeterReadingsPlace.class.getName());

    //Default constructor to just go to the meter readings default view.
    public MeterReadingsPlace(String meter) {
        this.superMeter = meter;
    }
        
    public MeterReadingsPlace(String meter, String graphType) {
        this.superMeter = meter;
        this.graphType = graphType;
    }
    
    //Constructor to go to the view for a specific meter readings.
    public MeterReadingsPlace(String superMeter, String graphType, String readingType, Date startDate, Date endDate) {
        this.superMeter = superMeter;
        this.graphType = graphType;        
        this.readingType = readingType;
        this.startDate = startDate;
        this.endDate = endDate;
    }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        } 
        if (!(o instanceof MeterReadingsPlace)) {
            return false;
        }
        MeterReadingsPlace p = (MeterReadingsPlace) o;
        if (p.getGraphType() == null && p.getReadingType() == null) {
            return ValidateUtil.isEqual(p.getSuperMeter(), superMeter);
        } else {
            if (ValidateUtil.isEqual(p.getSuperMeter(), superMeter)) {
                if (ValidateUtil.isEqual(p.getGraphType(), graphType)) {
                    if (ValidateUtil.isEqual(p.getReadingType(), readingType)) {
                        if (ValidateUtil.isEqual(p.getStartDate(), startDate)) {
                            return ValidateUtil.isEqual(p.getEndDate(), endDate);
                        }
                    }
                }
            }
            return false;
        }
    }
    
    @Override
    public int hashCode() {
        if (graphType == null && readingType == null) {
            if (superMeter != null) {
                return superMeter.hashCode();
            } else {
                return 27;
            }
        } else {
            if (superMeter != null) {
                return superMeter.hashCode() + graphType.hashCode() + readingType.hashCode();
            } else {
                return graphType.hashCode() + readingType.hashCode();
            }
        }
    }
    
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("MeterReadingsPlace:");
        sb.append(" superMeter:").append(superMeter);
        sb.append(" graphType:").append(graphType);
        sb.append(" readingType:").append(readingType);
        sb.append(" startDate:").append(startDate);
        sb.append(" endDate:").append(endDate);
        return sb.toString();
    }
    
    public String getSuperMeter() {
        return superMeter;
    }

    public String getGraphType() {
        return graphType;
    }

    public String getReadingType() {
        return readingType;
    }

    public Date getStartDate() {
        return startDate;
    }

    public Date getEndDate() {
        return endDate;
    }

    public static String getPlaceAsString(MeterReadingsPlace place) {
        return "meterreadings:" + new MeterReadingsPlace.Tokenizer().getToken(place);
    }
    
    @Prefix(value = "meterreadings")
    public static class Tokenizer implements PlaceTokenizer<MeterReadingsPlace> {
        
        @Override
        public String getToken(MeterReadingsPlace place) {
            String token = "";
            if (ALL_SUPER_METERS.equals(place.getSuperMeter())) {
                token = place.getSuperMeter();    
            } else {
                DateTimeFormat df = DateTimeFormat.getFormat(MeterMngStatics.URL_DATE_TIME_PICKER_FORMAT);
                StringBuilder sb = new StringBuilder();
                sb.append(place.getSuperMeter()).append(MeterMngStatics.PLACE_TOKEN_SEPARATOR);
                sb.append(place.getGraphType()).append(MeterMngStatics.PLACE_TOKEN_SEPARATOR);
                sb.append(place.getReadingType()).append(MeterMngStatics.PLACE_TOKEN_SEPARATOR);
                if (place.getStartDate() != null) {
                    sb.append(df.format(place.getStartDate())).append(MeterMngStatics.PLACE_TOKEN_SEPARATOR);
                } else {
                    sb.append("").append(MeterMngStatics.PLACE_TOKEN_SEPARATOR);
                }
                if (place.getEndDate() != null) {
                    sb.append(df.format(place.getEndDate()));
                } else {
                    sb.append("");
                }
                token = sb.toString();
            }
            logger.info("MeterReadingsPlace.Tokenizer: getToken:"+token);
            return token;
        }

        @Override
        public MeterReadingsPlace getPlace(String token) {
            if (token != null) {
                String[] tokens = token.split(MeterMngStatics.PLACE_TOKEN_SEPARATOR_REG_EXP);
                if (tokens != null && tokens.length > 0) {
                    DateTimeFormat df = DateTimeFormat.getFormat(MeterMngStatics.URL_DATE_TIME_PICKER_FORMAT);
                    String superMeter = null;
                    String graphType = null;
                    String readingType = null;
                    Date startDate = null;
                    Date endDate = null;
                    for(int i=0;i<tokens.length;i++) {
                        switch (i) {
                        case 0: superMeter = tokens[i]; break;
                        case 1: graphType = tokens[i]; break;
                        case 2: readingType = tokens[i]; break;
                        case 3: if (tokens[i] != null && !tokens[i].equals("")) startDate = df.parse(tokens[i]); break;
                        case 4: if (tokens[i] != null && !tokens[i].equals("")) endDate = df.parse(tokens[i]); break;
                        }
                    }
                    return new MeterReadingsPlace(superMeter, graphType, readingType, startDate, endDate);
                } else {
                    return new MeterReadingsPlace(token);
                }
            } else {
                return new MeterReadingsPlace(ALL_SUPER_METERS);
            }
        }
    }    
}
