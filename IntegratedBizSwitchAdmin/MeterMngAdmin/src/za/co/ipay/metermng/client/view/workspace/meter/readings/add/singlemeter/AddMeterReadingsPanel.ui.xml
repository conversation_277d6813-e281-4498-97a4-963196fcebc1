<!DOCTYPE ui:UiBinder SYSTEM "http://dl.google.com/gwt/DTD/xhtml.ent">
<ui:UiBinder xmlns:ui="urn:ui:com.google.gwt.uibinder" 
             xmlns:g="urn:import:com.google.gwt.user.client.ui"
             xmlns:p1="urn:import:za.co.ipay.gwt.common.client.form" 
             xmlns:g2="urn:import:com.google.gwt.user.datepicker.client"
             xmlns:w="urn:import:za.co.ipay.gwt.common.client.widgets">
	
    <ui:with field="msg" type="za.co.ipay.metermng.client.i18n.UiMessages" />

    <g:FlowPanel>
    
        <p1:FormRowPanel>
            <p1:FormElement labelText="{msg.getDemoAddMeterReadingsReadingVariants}:">
                <g:RadioButton ui:field="radioIntervalReadings" name="groupReadingVariants" value="true" text="{msg.getMeterReadingsLink}"/>
                <g:RadioButton ui:field="radioRegisterReadings" name="groupReadingVariants" text="{msg.getRegisterReadingTxnLabel}"/>
            </p1:FormElement>
        </p1:FormRowPanel>
    
        <p1:FormRowPanel>
            <p1:FormElement ui:field="meterNumberElement" labelText="{msg.getMeterNumber}:" required="true">
                <g:SuggestBox ui:field="meterNumberBox" styleName="gwt-TextBox" />
            </p1:FormElement>
        </p1:FormRowPanel>
        
        <p1:FormRowPanel>
            <p1:FormElement labelText="{msg.getDemoAddMeterReadingsEarliestReading}:">
                <g2:DateBox ui:field="firstBox" styleName="gwt-TextBox" enabled="false"/>
            </p1:FormElement>

            <p1:FormElement labelText="{msg.getDemoAddMeterReadingsLatestReading}:">
                <g2:DateBox ui:field="lastBox" styleName="gwt-TextBox" enabled="false"/>
            </p1:FormElement>
        </p1:FormRowPanel>
        
        <p1:FormRowPanel>
            <p1:FormElement ui:field="startElement" labelText="{msg.getMeterReadingsStart}:" required="true">
                <g2:DateBox ui:field="startBox" styleName="gwt-TextBox" />
            </p1:FormElement>

            <p1:FormElement ui:field="endElement" labelText="{msg.getMeterReadingsEnd}:" required="true">
                <g2:DateBox ui:field="endBox" styleName="gwt-TextBox" />
            </p1:FormElement>
        </p1:FormRowPanel>
        
        <p1:FormRowPanel>
            <g:CheckBox ui:field="checkZero" text="{msg.getDemoAddMeterReadingsZeroCheckboxText}"/>
        </p1:FormRowPanel>
        <p1:FormGroupPanel ui:field="formZero" visible="false" title="{msg.getDemoAddMeterReadingsZeroFormTitle}">
            <p1:FormRowPanel>
                <p1:FormElement ui:field="zeroStartElement" labelText="{msg.getMeterReadingsStart}:">
                    <g2:DateBox ui:field="zeroStartBox" styleName="gwt-TextBox"/>
                </p1:FormElement>
                <p1:FormElement ui:field="zeroEndElement" labelText="{msg.getMeterReadingsEnd}:">
                    <g2:DateBox ui:field="zeroEndBox" styleName="gwt-TextBox"/>
                </p1:FormElement>
            </p1:FormRowPanel>
            <p1:FormRowPanel>
                <p1:FormElement labelText="{msg.getDemoAddMeterReadingsAlgorithmLogic}:">
                    <g:RadioButton ui:field="radioZeroConsecutive" name="groupZero" value="true" text="{msg.getDemoAddMeterReadingsConsecutive}"/>
                    <g:RadioButton ui:field="radioZeroRandom" name="groupZero" text="{msg.getDemoAddMeterReadingsRandom}"/>
                </p1:FormElement>
                <p1:FormElement ui:field="formZeroRandom" labelText="{msg.getDemoAddMeterReadingsPercentageInstances}:" visible="false">
                    <g:TextBox ui:field="textZeroRandom" styleName="gwt-TextBox"/>
                </p1:FormElement>
            </p1:FormRowPanel>
        </p1:FormGroupPanel>
        
        <p1:FormRowPanel>
            <g:CheckBox ui:field="checkMissing" text="{msg.getDemoAddMeterReadingsMissingCheckboxText}"/>
        </p1:FormRowPanel>
        <p1:FormGroupPanel ui:field="formMissing" visible="false" title="{msg.getDemoAddMeterReadingsMissingFormTitle}">
            <p1:FormRowPanel>
                <p1:FormElement ui:field="missingStartElement" labelText="{msg.getMeterReadingsStart}:">
                    <g2:DateBox ui:field="missingStartBox" styleName="gwt-TextBox"/>
                </p1:FormElement>
                <p1:FormElement ui:field="missingEndElement" labelText="{msg.getMeterReadingsEnd}:">
                    <g2:DateBox ui:field="missingEndBox" styleName="gwt-TextBox"/>
                </p1:FormElement>
            </p1:FormRowPanel>
            <p1:FormRowPanel>
                <p1:FormElement labelText="{msg.getDemoAddMeterReadingsAlgorithmLogic}:">
                    <g:RadioButton ui:field="radioMissingConsecutive" name="groupMissing" value="true" text="{msg.getDemoAddMeterReadingsConsecutive}"/>
                    <g:RadioButton ui:field="radioMissingRandom" name="groupMissing" text="{msg.getDemoAddMeterReadingsRandom}"/>
                </p1:FormElement>
                <p1:FormElement ui:field="formMissingRandom" labelText="{msg.getDemoAddMeterReadingsPercentageInstances}:" visible="false">
                    <g:TextBox ui:field="textMissingRandom" styleName="gwt-TextBox"/>
                </p1:FormElement>
            </p1:FormRowPanel>
        </p1:FormGroupPanel>
        
        <p1:FormRowPanel>
            <p1:FormElement ui:field="readingTypesElement" labelText="{msg.getMeterReadingTypes}:" required="true">
                <g:ListBox ui:field="readingTypesBox" multipleSelect="true" visibleItemCount="5" />
            </p1:FormElement>
        </p1:FormRowPanel>
        
        <p1:FormRowPanel>
            <p1:FormElement ui:field="readingIntervalElement" labelText="{msg.getMeterReadingInterval}:" required="true">
                <g:ListBox ui:field="readingIntervalBox" />
            </p1:FormElement>
        </p1:FormRowPanel>
        
        <p1:FormRowPanel ui:field="formMdcChannels" visible="false">
            <p1:FormElement ui:field="mdcChannelsElement" labelText="{msg.getChannelFieldTitlename}:" required="true">
                <g:ListBox ui:field="mdcChannelsBox" multipleSelect="true"/>
            </p1:FormElement>
        </p1:FormRowPanel>
        
        <p1:FormRowPanel>
            <p1:FormElement ui:field="formDeleteReadings" labelText="{msg.getDeleteExistingMeterReadings}:">
                <g:RadioButton ui:field="radioDeleteAll" name="groupDeleteReadings" value="true" text="{msg.getDemoAddMeterReadingsDeleteAll}"/>
                <g:RadioButton ui:field="radioDeleteSelected" name="groupDeleteReadings" text="{msg.getDemoAddMeterReadingsDeleteSelected}"/>
                <g:RadioButton ui:field="radioAppend" name="groupDeleteReadings" text="{msg.getDemoAddMeterReadingsAppend}"/>
            </p1:FormElement>
        </p1:FormRowPanel>
                
        <p1:FormRowPanel>
            <p1:FormElement ui:field="tariffCalcElement" >
                <g:CheckBox ui:field="tariffCalcBox" text="{msg.getTariffCalcAfterMeterReadingsTitle}" value="true" />
            </p1:FormElement>
        </p1:FormRowPanel>
        
    </g:FlowPanel>
	
</ui:UiBinder> 