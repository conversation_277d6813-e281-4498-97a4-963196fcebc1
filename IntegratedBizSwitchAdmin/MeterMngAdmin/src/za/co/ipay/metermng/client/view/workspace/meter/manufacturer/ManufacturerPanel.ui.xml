<!DOCTYPE ui:UiBinder SYSTEM "http://dl.google.com/gwt/DTD/xhtml.ent">
<ui:UiBinder xmlns:ui="urn:ui:com.google.gwt.uibinder" 
             xmlns:g="urn:import:com.google.gwt.user.client.ui"
             xmlns:p1="urn:import:za.co.ipay.gwt.common.client.form">
	<ui:style>	
	</ui:style>
	
	  <ui:with field="msg" type="za.co.ipay.metermng.client.i18n.UiMessages" />

	  <g:FlowPanel>
	    <p1:FormRowPanel debugId="manufacturerPanel">
	      <p1:FormElement debugId="nameElement" ui:field="nameElement" labelText="{msg.getManufacturerName}:" helpMsg="{msg.getManufacturerNameHelp}"
	        required="true">
	        <g:TextBox debugId="nameBox" text="" ui:field="nameTextBox" title="{msg.getManufacturerName}" />
	      </p1:FormElement>
	      <p1:FormElement debugId="descriptionElement" ui:field="descriptionElement" labelText="{msg.getManufacturerDescription}:" helpMsg="{msg.getManufacturerDescriptionHelp}">
	        <g:TextBox debugId="descriptionBox" ui:field="descriptionTextBox" title="{msg.getManufacturerDescription}" />
	      </p1:FormElement>
	      <p1:FormElement ui:field="activeElement" labelText="{msg.getManufacturerActive}:" helpMsg="{msg.getManufacturerActiveHelp}">
	        <g:CheckBox debugId="activeCheckBox" ui:field="activeBox" />
	      </p1:FormElement>
	    </p1:FormRowPanel>
	  </g:FlowPanel>
	
</ui:UiBinder> 