package za.co.ipay.metermng.client.widget.search;

import static com.google.gwt.dom.client.BrowserEvents.CLICK;
import static com.google.gwt.dom.client.BrowserEvents.KEYDOWN;

import com.google.gwt.cell.client.AbstractSafeHtmlCell;
import com.google.gwt.cell.client.ValueUpdater;
import com.google.gwt.dom.client.Element;
import com.google.gwt.dom.client.NativeEvent;
import com.google.gwt.safehtml.shared.SafeHtml;
import com.google.gwt.safehtml.shared.SafeHtmlBuilder;
import com.google.gwt.text.shared.SafeHtmlRenderer;
import com.google.gwt.user.client.History;

import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.shared.dto.search.SearchResultData;
import za.co.ipay.metermng.shared.dto.search.SearchResultType;

/**
 * SearchResultClickableCell displays a specific detail from a SearchResultData as a click-able link to view the detail
 * in its own screen, for example: a meter or a customer.
 * <AUTHOR>
 */
public class SearchResultClickableCellComposite extends AbstractSafeHtmlCell<SearchResultData> {

    private SearchResultType columnType;
    private ClientFactory clientFactory;
    private String currentToken;
    
    public SearchResultClickableCellComposite(ClientFactory clientFactory, String currentToken, SearchResultType columnType, String[] detailKeys) {
      super(MyRenderer.getInstance(detailKeys), CLICK, KEYDOWN);
      this.columnType = columnType;
      this.clientFactory = clientFactory;
      this.currentToken = currentToken;
    }

    @Override
    public void onBrowserEvent(Context context, Element parent, SearchResultData value, NativeEvent event, ValueUpdater<SearchResultData> valueUpdater) {      
      if (CLICK.equals(event.getType())) {
          String link = SearchResultData.getHistoryToken(clientFactory, columnType, value, currentToken);
          if (link != null) {
              History.newItem(link);
              return;
          }
      } 
      super.onBrowserEvent(context, parent, value, event, valueUpdater); //default 
    }

    @Override
    protected void render(Context context, SafeHtml value, SafeHtmlBuilder sb) {
      if (value != null) {
        sb.append(value);
      }
    }
    
    private static class MyRenderer implements SafeHtmlRenderer<SearchResultData> {
        
        public static MyRenderer getInstance(String[] detailKeys) {
            return new MyRenderer(detailKeys);
        }
        
        private String[] detailKeys;       

        private MyRenderer(String[] detailKeys) {
            this.detailKeys = detailKeys;
        }
        
        @Override
        public SafeHtml render(SearchResultData data) {
            SafeHtmlBuilder sb = new SafeHtmlBuilder();
            if (data != null) {                
                Object o = makeCompositeString(data);
                if (o instanceof String) {
                    sb.appendEscaped((String) o);
                } else if (o != null) {
                    sb.appendEscaped(o.toString());
                }
            }
            return sb.toSafeHtml();
        }

        @Override
        public void render(SearchResultData data, SafeHtmlBuilder sb) {
            if (data != null) {                
                Object o = makeCompositeString(data);
                if (o instanceof String) {
                    sb.appendEscaped((String) o);
                } else if (o != null) {
                    sb.appendEscaped(o.toString());
                }
            }
        }
        private Object makeCompositeString(SearchResultData data) {
            StringBuilder sb = new StringBuilder();
            for (String dKey : detailKeys) {
                Object str = data.getDetails().get(dKey);
                if (str != null) {
                    sb.append(str + " ");
                }
            }
            return sb.substring(0, sb.length() - 1);
        }
    }    
  }