package za.co.ipay.metermng.client.view.workspace.meter.manufacturer;

import java.util.ArrayList;
import java.util.logging.Logger;

import za.co.ipay.gwt.common.client.Dialogs;
import za.co.ipay.gwt.common.client.form.FormManager;
import za.co.ipay.gwt.common.client.handler.ConfirmHandler;
import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.gwt.common.client.validation.ClientValidatorUtil;
import za.co.ipay.gwt.common.client.workspace.SimpleTableView;
import za.co.ipay.gwt.common.client.workspace.WorkspaceNotification;
import za.co.ipay.gwt.common.client.workspace.WorkspaceNotification.NotificationType;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.history.ManufacturerPlace;
import za.co.ipay.metermng.client.resource.image.MediaResource.MediaResourceUtil;
import za.co.ipay.metermng.client.rpc.callback.ClientCallback;
import za.co.ipay.metermng.client.view.workspace.BaseWorkspace;
import za.co.ipay.metermng.client.widget.StatusTableColumn;
import za.co.ipay.metermng.datatypes.RecordStatus;
import za.co.ipay.metermng.mybatis.generated.model.Manufacturer;
import za.co.ipay.metermng.shared.MeterMngStatics;

import com.google.gwt.core.client.GWT;
import com.google.gwt.event.dom.client.ClickEvent;
import com.google.gwt.event.dom.client.ClickHandler;
import com.google.gwt.place.shared.Place;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.user.cellview.client.Column;
import com.google.gwt.user.cellview.client.ColumnSortEvent;
import com.google.gwt.user.cellview.client.ColumnSortEvent.AsyncHandler;
import com.google.gwt.user.cellview.client.ColumnSortList;
import com.google.gwt.user.cellview.client.TextColumn;
import com.google.gwt.user.client.ui.Widget;
import com.google.gwt.view.client.AsyncDataProvider;
import com.google.gwt.view.client.HasData;
import com.google.gwt.view.client.Range;

public class ManufacturerWorkspaceView extends BaseWorkspace implements FormManager<Manufacturer> {
    
    @UiField SimpleTableView<Manufacturer> view;
    private ManufacturerPanel panel;
    private AsyncDataProvider<Manufacturer> dataProvider;
    private Manufacturer manufacturer;
    
    private static Logger logger = Logger.getLogger(ManufacturerWorkspaceView.class.getName());

    private static ManufacturerWorkspaceViewUiBinder uiBinder = GWT.create(ManufacturerWorkspaceViewUiBinder.class);

    interface ManufacturerWorkspaceViewUiBinder extends UiBinder<Widget, ManufacturerWorkspaceView> {
    }

    public ManufacturerWorkspaceView(ClientFactory clientFactory, ManufacturerPlace place) {
        this.clientFactory = clientFactory;
        initWidget(uiBinder.createAndBindUi(this));
        setPlaceString(ManufacturerPlace.getPlaceAsString(place));
        setHeaderText(MessagesUtil.getInstance().getMessage("meter.manufacturers"));
        initUi();
    }

    private void initUi() {
        initView();
        initForm();
        createTable();
        actionPermissions();
    }

    private void initView() {
        view.setFormManager(this);
    }

    private void initForm() {
        panel = new ManufacturerPanel(view.getForm());
        panel.activeBox.setValue(true);

        view.getForm().setHasDirtyDataManager(this);
        view.getForm().getFormFields().add(panel);

        view.getForm().getSaveBtn().setText(MessagesUtil.getInstance().getMessage("button.create"));
        view.getForm().getSaveBtn().ensureDebugId("manufacturerSaveButton");
        view.getForm().getSaveBtn().addClickHandler(new ClickHandler() {
            @Override
            public void onClick(ClickEvent event) {
                onSave();
            }
        });

        view.getForm().getOtherBtn().setText(MessagesUtil.getInstance().getMessage("button.cancel"));
        view.getForm().getOtherBtn().ensureDebugId("manufacturerCancelButton");
        view.getForm().getOtherBtn().addClickHandler(new ClickHandler() {
            @Override
            public void onClick(ClickEvent event) {
                view.getForm().checkDirtyData(new ConfirmHandler() {
                    @Override
                    public void confirmed(boolean confirm) {
                        if (confirm) {
                            view.getForm().setDirtyData(false);
                            displaySelected(null);
                        }
                    }
                });
            }
        });

        view.getForm().getFormPanel().setHeadingText(MessagesUtil.getInstance().getMessage("meter.manufacturers.title.add"));
    }

    private void createTable() {
        if (dataProvider == null) {
            Column<Manufacturer, ?> nameColumn = createNameColumn();
            Column<Manufacturer, ?> descriptionColumn = createDescriptionColumn();
            StatusTableColumn<Manufacturer> statusColumn = new StatusTableColumn<Manufacturer>();
            view.getTable().addColumn(nameColumn, MessagesUtil.getInstance().getMessage("meter.manufacturers.field.name"));
            view.getTable().addColumn(descriptionColumn, MessagesUtil.getInstance().getMessage("meter.manufacturers.field.description"));
            view.getTable().addColumn(statusColumn, MessagesUtil.getInstance().getMessage("meter.manufacturers.field.status"));

            // Set the range to display
            view.getTable().setVisibleRange(0, getPageSize());
            view.getTable().ensureDebugId("manufacturerTable");

            // Set the data provider for the table
            dataProvider = new AsyncDataProvider<Manufacturer>() {
                @Override
                protected void onRangeChanged(HasData<Manufacturer> display) {
                    final int start = display.getVisibleRange().getStart();
                    String sortColumn = null;
                    boolean isAscending = true;
                    ColumnSortList sortList = view.getTable().getColumnSortList();
                    if (sortList != null && sortList.size() != 0) {
                        @SuppressWarnings("unchecked")
                        Column<Manufacturer, ?> sColumn = (Column<Manufacturer, ?>) sortList.get(0).getColumn();
                        Integer columnIndex = view.getTable().getColumnIndex(sColumn);
                        sortColumn = getColumnName(columnIndex);
                        isAscending = sortList.get(0).isAscending();
                    }
                    clientFactory.getManufacturerRpc().getManufacturers(start, getPageSize(), sortColumn, isAscending,
                            new ClientCallback<ArrayList<Manufacturer>>() {
                                @Override
                                public void onSuccess(ArrayList<Manufacturer> result) {
                                    dataProvider.updateRowData(start, result);
                                }
                            });
                }
            };
            dataProvider.addDataDisplay(view.getTable());

            // Create the table's pager
            view.getPager().setDisplay(view.getTable());

            // Set the table's column sorter handler
            AsyncHandler columnSortHandler = new AsyncHandler(view.getTable()) {
                @Override
                public void onColumnSort(ColumnSortEvent event) {
                    final int start = view.getTable().getVisibleRange().getStart();
                    @SuppressWarnings("unchecked")
                    int sortIndex = view.getTable().getColumnIndex((Column<Manufacturer, ?>) event.getColumn());
                    String sortColumn = getColumnName(sortIndex);
                    boolean isAscending = event.isSortAscending();
                    clientFactory.getManufacturerRpc().getManufacturers(start, getPageSize(), sortColumn, isAscending,
                            new ClientCallback<ArrayList<Manufacturer>>() {
                                public void onSuccess(ArrayList<Manufacturer> result) {
                                    dataProvider.updateRowData(start, result);
                                }
                            });
                }
            };
            view.getTable().addColumnSortHandler(columnSortHandler);
            view.getTable().getColumnSortList().push(statusColumn);
            view.getTable().getColumnSortList().push(descriptionColumn);
            view.getTable().getColumnSortList().push(nameColumn);
        }
    }

    private String getColumnName(int index) {
        return "name";
    }

    private Column<Manufacturer, ?> createNameColumn() {
        TextColumn<Manufacturer> nameColumn = new TextColumn<Manufacturer>() {
            @Override
            public String getValue(Manufacturer object) {
                return object.getName();
            }
        };
        nameColumn.setSortable(true);
        return nameColumn;
    }

    private Column<Manufacturer, ?> createDescriptionColumn() {
        return new TextColumn<Manufacturer>() {
            @Override
            public String getValue(Manufacturer m) {
                return m.getDescription();
            }
        };
    }
    
    @Override
    public void onArrival(Place place) {
        getManufacturersCount(false);
    }

    private void getManufacturersCount(final boolean refreshTable) {
        clientFactory.getManufacturerRpc().getManufacturersCount(new ClientCallback<Integer>() {
            @Override
            public void onFailureClient() {
                view.getTable().setRowCount(0, true);
                dataProvider.updateRowCount(0, true);
            }
            @Override
            public void onSuccess(Integer result) {
                view.getTable().setRowCount(result, true);
                dataProvider.updateRowCount(result, true);
                // Force a table update - reloads data from the back end
                if (refreshTable) {
                    refreshTable(false);
                }
            }
        });
    }

    @Override
    public void onLeaving() {

    }

    @Override
    public void onSelect() {

    }

    @Override
    public void onClose() {

    }

    @Override
    public boolean handles(Place place) {
        return (place instanceof ManufacturerPlace);
    }

    @Override
    public void displaySelected(Manufacturer selected) {
        displayManufacturer(selected);
    }

    private void displayManufacturer(Manufacturer selected) {
        clear();
        this.manufacturer = selected;
        if (manufacturer == null) {
            manufacturer = new Manufacturer();
            view.getForm().getSaveBtn().setText(MessagesUtil.getInstance().getMessage("button.create"));
            view.getForm().getFormPanel().setHeadingText(MessagesUtil.getInstance().getMessage("meter.manufacturers.title.add"));
            view.clearTableSelection();
        } else {
            view.getForm().getSaveBtn().setText(MessagesUtil.getInstance().getMessage("button.update"));
            view.getForm().getFormPanel().setHeadingText(MessagesUtil.getInstance().getMessage("meter.manufacturers.title.update"));
        }

        panel.nameTextBox.setText(manufacturer.getName());
        panel.descriptionTextBox.setText(manufacturer.getDescription());
        panel.activeBox.setValue(RecordStatus.ACT.equals(manufacturer.getRecordStatus()));
    }

    private void clear() {
        manufacturer = null;
        view.getForm().getFormPanel().setHeadingText(MessagesUtil.getInstance().getMessage("meter.manufacturers.title.add"));
        view.getForm().getSaveBtn().setText(MessagesUtil.getInstance().getMessage("button.create"));
        panel.clearFields();
        panel.clearErrors();  
        view.clearTableSelection();
    }

    private boolean isValidInput() {
        boolean valid = true;
        panel.clearErrors();
        
        Manufacturer dto = updateManufacturer();
        
        if (!ClientValidatorUtil.getInstance().validateField(dto, "name", panel.nameElement)) {
            valid = false;
            logger.info("Invalid dto name: "+dto.getName());
        }
        if (!ClientValidatorUtil.getInstance().validateField(dto, "description", panel.descriptionElement)) {
            valid = false;
        }
        if (!ClientValidatorUtil.getInstance().validateField(dto, "recordStatus", panel.activeElement)) {
            valid = false;
        }
        
        return valid;
    }

    private Manufacturer updateManufacturer() {
        Manufacturer m = new Manufacturer();
        updateManufacturer(m);
        return m;
    }

    private void updateManufacturer(Manufacturer dto) {
        if (manufacturer != null) {
            dto.setId(manufacturer.getId());
        }
        dto.setName(panel.nameTextBox.getText());
        dto.setDescription(panel.descriptionTextBox.getText());
        dto.setRecordStatus(panel.activeBox.getValue() ? RecordStatus.ACT : RecordStatus.DAC);
    }

    private void onSave() {
        if (isValidInput()) {
            Manufacturer m = updateManufacturer();
            final Long id = m.getId(); // keep track whether the table's total row count is increasing or not
            clientFactory.getManufacturerRpc().saveManufacturer(
                    m,
                    new ClientCallback<Void>(view.getForm().getSaveBtn().getAbsoluteLeft(), view.getForm().getSaveBtn().getAbsoluteTop()) {
                        @Override
                        public void onSuccess(Void result) {
                            view.getForm().setDirtyData(false);
                            boolean refresh = false;
                            if (id == null) {
                                refresh = true;
                            }
                            refreshTable(refresh);
                            Dialogs.displayInformationMessage(
                                    MessagesUtil.getInstance()
                                            .getSavedMessage(
                                                    new String[] { MessagesUtil.getInstance().getMessage("meter.manufacturer.name") }), MediaResourceUtil.getInstance()
                                            .getInformationIcon(), view.getForm().getSaveBtn().getAbsoluteLeft(), view
                                            .getForm().getSaveBtn().getAbsoluteTop(), MessagesUtil.getInstance()
                                            .getMessage("button.close"));
                            clear();
                            sendNotification();
                        }
                    });
        }
    }
    
    private void sendNotification() {
        //Notify any affected tabs                        
        clientFactory.getWorkspaceContainer().notifyWorkspaces(
                new WorkspaceNotification(NotificationType.DATA_UPDATED, MeterMngStatics.MANUFACTURER_DATA));
    }

    //Method to force the table to refresh its current page. A new row could of been added or just the data should be 
    //reloaded due to other changes like disabled user.
    private void refreshTable(boolean insertedNew) {
        if (insertedNew) {
            view.getTable().setRowCount(view.getTable().getRowCount() + 1, true);
        }
        Range range = view.getTable().getVisibleRange();
        view.getTable().setVisibleRangeAndClearData(range, true);
    }
    
    private void actionPermissions() {
        if (!clientFactory.getUser().hasPermission(MeterMngStatics.EDIT_PERMISSION_MM_METER_MANUFACTURER)) {
            view.getForm().getButtons().removeFromParent();
        }
    }
}