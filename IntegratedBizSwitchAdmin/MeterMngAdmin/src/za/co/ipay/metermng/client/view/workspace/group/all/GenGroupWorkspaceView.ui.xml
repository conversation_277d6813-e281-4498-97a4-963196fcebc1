<!DOCTYPE ui:UiBinder SYSTEM "http://dl.google.com/gwt/DTD/xhtml.ent">
<ui:UiBinder xmlns:ui="urn:ui:com.google.gwt.uibinder" 
             xmlns:g="urn:import:com.google.gwt.user.client.ui"
             xmlns:p1="urn:import:za.co.ipay.gwt.common.client.form" 
             xmlns:g2="urn:import:com.google.gwt.user.cellview.client"
             xmlns:p3="urn:import:za.co.ipay.gwt.common.client.widgets"
             xmlns:p4="urn:import:za.co.ipay.metermng.client.view.component.group"
             xmlns:p5="urn:import:za.co.ipay.gwt.common.client.workspace">
  <ui:style>
        h3 {
           border-bottom-width: 2px;
           border-bottom-style: solid;
           border-color: LightGray;
        }
  </ui:style>

  <ui:with field="msg" type="za.co.ipay.metermng.client.i18n.UiMessages" />
  <ui:with field="treeFactory" type="za.co.ipay.gwt.common.client.workspace.TreeFactory" />
  
  <g:DeckLayoutPanel height="100%" width="100%" ui:field="mainLayoutPanel" animationVertical="false">
        
    <!-- 1st deck -->
    <p5:SimpleTreeView ui:field="view" header="{msg.getUsagePointGroupsHeader}" treeFactory="{treeFactory}" />
      
    <!-- 2nd deck -->
    <p5:SimpleFormView ui:field="view2" header="{msg.getGroupEntityHeader}" />
    
 </g:DeckLayoutPanel>     
</ui:UiBinder> 