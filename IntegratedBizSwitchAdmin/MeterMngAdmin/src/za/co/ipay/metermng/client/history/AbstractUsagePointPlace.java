package za.co.ipay.metermng.client.history;

import com.google.gwt.place.shared.Place;

public abstract class AbstractUsagePointPlace extends Place {
    
    /** The history token to get back to the previous screen that created this place. */
    protected String previousHistoryToken;
    
    public abstract boolean isFromURL();
    public abstract boolean isNew();
    
    public String getPreviousHistoryToken() {
        return previousHistoryToken;
    }
}
