package za.co.ipay.metermng.client.history;

import com.google.gwt.place.shared.Place;
import com.google.gwt.place.shared.PlaceTokenizer;
import com.google.gwt.place.shared.Prefix;

public class MeterOnlineBulkPlace extends Place {
	
	public static MeterOnlineBulkPlace ALL_PLACE = new MeterOnlineBulkPlace("all");

    private String name;
    
    public MeterOnlineBulkPlace(String name) {
        this.name = name;
    }
    
    public String getName() {
        return name;
    }
    
    public static String getPlaceAsString(MeterOnlineBulkPlace p) {
        return "meteronlinebulk:"+p.getName();
    }
    
    public static String getPlaceAsString() {
        return "meteronlinebulk:all";
    }

    @Prefix(value = "meteronlinebulk")
    public static class Tokenizer implements PlaceTokenizer<MeterOnlineBulkPlace> {
        
        @Override
        public String getToken(MeterOnlineBulkPlace place) {
            return "all";
        }

        @Override
        public MeterOnlineBulkPlace getPlace(String token) {
            return new MeterOnlineBulkPlace(token);
        }
    }

}
