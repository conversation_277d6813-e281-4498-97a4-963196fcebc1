package za.co.ipay.metermng.client.view.workspace.userinterface;

import za.co.ipay.gwt.common.client.Dialogs;
import za.co.ipay.gwt.common.client.resource.Messages;
import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.gwt.common.client.workspace.WorkspaceCreateCallback;
import za.co.ipay.gwt.common.client.workspace.WorkspaceFactory;
import za.co.ipay.gwt.common.shared.exception.AccessControlException;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.history.UserInterfacePlace;
import za.co.ipay.metermng.client.resource.image.MediaResource.MediaResourceUtil;
import za.co.ipay.metermng.shared.MeterMngStatics;

import com.google.gwt.place.shared.Place;

public class UserInterfaceWorkspaceFactory implements WorkspaceFactory {

    private ClientFactory clientFactory;

    public UserInterfaceWorkspaceFactory(ClientFactory clientFactory) {
        this.clientFactory = clientFactory;
        clientFactory.getWorkspaceContainer().register(this);
    }

    @Override
    public void createWorkspace(Place place, WorkspaceCreateCallback workspaceCreateCallback) {
        if (!clientFactory.getUser().hasPermission(MeterMngStatics.ACCESS_PERMISSION_MM_CONFIGURE_USER_INTERFACE)) {
            Messages messages = MessagesUtil.getInstance();
            Dialogs.displayErrorMessage(messages.getMessage("error.accessdenied"),
                    MediaResourceUtil.getInstance().getLockedIcon(), messages.getMessage("button.close"));
            workspaceCreateCallback.onWorkspaceCreationFailed(new AccessControlException("Access is Denied"));
            return;
        }
        try {
            UserInterfaceWorkspaceView view = new UserInterfaceWorkspaceView(clientFactory, (UserInterfacePlace) place);
            workspaceCreateCallback.onWorkspaceCreated(view);
        } catch (Exception e) {
            workspaceCreateCallback.onWorkspaceCreationFailed(e);
        }
    }

    @Override
    public boolean handles(Place place) {
        return place instanceof UserInterfacePlace;
    }
}