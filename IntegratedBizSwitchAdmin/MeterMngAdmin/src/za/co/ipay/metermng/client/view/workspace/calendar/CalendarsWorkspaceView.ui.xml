<!DOCTYPE ui:UiBinder SYSTEM "http://dl.google.com/gwt/DTD/xhtml.ent">
<ui:UiBinder xmlns:ui="urn:ui:com.google.gwt.uibinder"
	xmlns:g="urn:import:com.google.gwt.user.client.ui" xmlns:g2="urn:import:com.google.gwt.user.cellview.client"
	xmlns:form="urn:import:za.co.ipay.gwt.common.client.form" xmlns:widget="urn:import:za.co.ipay.gwt.common.client.widgets"
	xmlns:tc="urn:import:za.co.ipay.metermng.client.view.component.tariff.calendar">
	<ui:style>
	
	</ui:style>
	<ui:with field="msg" type="za.co.ipay.metermng.client.i18n.UiMessages" />
	<g:DockLayoutPanel ui:field="dockLayoutPanel" styleName="mainPanel">

		<g:north size="30">
			<form:PageHeader heading="{msg.getCalendarsHeading}" ui:field="pageHeader" />
		</g:north>

		<g:center>
            <g:ScrollPanel>
                <g:FlowPanel width="99%" height="100%">
                    <g:VerticalPanel spacing="15" > 
			            <g:HTMLPanel ui:field="calendarsPanel">
			                <g:HTML text="{msg.getCalendarsTitle}" styleName="dataTitle" />
			                <g:HTML text="{msg.getCalendarsDescription}" styleName="dataDescription" />     
			                <g2:CellTable ui:field="calendarsTable" debugId="calendarsTable" />
			                <widget:TablePager ui:field="calendarsPager" styleName="pager" location="CENTER" />
			            </g:HTMLPanel>    
                        <g:FlowPanel>
                            <g:Label ui:field="pricingStructureNote" debugId="pricingStructureNote" styleName="gwt-Label-header" visible="false"></g:Label> 
                            <g:Label ui:field="formHeading" styleName="sectionTitle"></g:Label> 
                            <form:FormRowPanel>
			                    <form:FormElement ui:field="nameElement" debugId="nameElement" labelText="{msg.getCalendarName}:" helpMsg="{msg.getCalendarNameHelp}" required="true">
			                        <g:TextBox text="" ui:field="nameTextBox" debugId="nameTextBox" title="{msg.getCalendarName}" />
			                    </form:FormElement>
			                    <form:FormElement ui:field="descriptionElement" debugId="descriptionElement" labelText="{msg.getCalendarDescription}:" helpMsg="{msg.getCalendarDescriptionHelp}" required="true">
                                    <g:TextBox text="" ui:field="descriptionTextBox" debugId="descriptionTextBox" title="{msg.getCalendarDescription}" />
                                </form:FormElement>
			                    <form:FormElement ui:field="activeElement" labelText="{msg.getCalendarActive}:" helpMsg="{msg.getCalendarActiveHelp}">
			                        <g:CheckBox ui:field="activeBox" debugId="activeBox"/>
			                    </form:FormElement>
			                </form:FormRowPanel> 
			                
			                <g:HorizontalPanel spacing="5" ui:field="buttons">
                                <g:Button ui:field="btnSave" debugId="btnSave" text="{msg.getSaveButton}" />
                                <g:Button ui:field="btnCancel" debugId="btnCancel" text="{msg.getCancelButton}" />
                            </g:HorizontalPanel>  
                        </g:FlowPanel>
                        <g:FlowPanel>
			                <g:DisclosurePanel ui:field="monthsPanel" debugId="monthsPanel" width="100%" styleName="gwt-DisclosurePanel-insidecomponent" visible="false">
                                 <g:customHeader>
								 <g:FlowPanel styleName="gwt-DisclosurePanel-component .header">
					                <g:HorizontalPanel>
					                    <g:Cell width="16px" height="16px">
					                        <g:Image styleName="horizontalFlow" ui:field="monthsopenorclosearrow" width="16px" height="16px"/>
					                    </g:Cell>
					                    <g:Cell>
					                        <g:Label styleName="gwt-DisclosurePanel-component .header" ui:field="monthsPanelHeader" debugId="monthsPanelHeader" text="{msg.getCalendarAssignSeasonHeading}"/>
					                    </g:Cell>
					                    <g:Cell>
					                       <g:Label styleName="gwt-Label-iPayLink" horizontalAlignment="ALIGN_RIGHT" ui:field="monthsCompleteHeader" debugId="monthsCompleteHeader" />
					                    </g:Cell>
					                </g:HorizontalPanel>
					            </g:FlowPanel>
					            </g:customHeader>
                                <g:FlowPanel  styleName="simple-center" >
                                    <tc:AssignSeasonsPanel ui:field="monthsInSeason" debugId="monthsInSeason"/>
                                </g:FlowPanel>
                            </g:DisclosurePanel>
                        </g:FlowPanel>
                        <g:FlowPanel>
                            <g:DisclosurePanel ui:field="dayprofilesPanel" debugId="dayprofilesPanel" width="100%" styleName="gwt-DisclosurePanel-insidecomponent" visible="false">
                                <g:customHeader>
                                 <g:FlowPanel styleName="gwt-DisclosurePanel-component .header">
                                    <g:HorizontalPanel>
                                        <g:Cell width="16px" height="16px">
                                            <g:Image styleName="horizontalFlow" ui:field="dpopenorclosearrow" width="16px" height="16px"/>
                                        </g:Cell>
                                        <g:Cell>
                                            <g:Label styleName="gwt-DisclosurePanel-component .header" ui:field="dpPanelHeader" debugId="dpPanelHeader" text="{msg.getCalendarDayProfilesHeading}"/>
                                        </g:Cell>
                                        <g:Cell>
                                           <g:Label styleName="gwt-Label-iPayLink" horizontalAlignment="ALIGN_RIGHT" ui:field="dpCompleteHeader" debugId="dpCompleteHeader" />
                                        </g:Cell>
                                    </g:HorizontalPanel>
                                </g:FlowPanel>
                                </g:customHeader>
                                <g:FlowPanel  styleName="simple-center">
                                    <tc:DayProfilesPanel ui:field="dayProfiles" debugId="dayProfiles"></tc:DayProfilesPanel>
                                </g:FlowPanel>
                            </g:DisclosurePanel>
                        </g:FlowPanel>
                        <g:FlowPanel>    
                            <g:DisclosurePanel ui:field="calendarSeasonProfilesPanel" debugId="calendarSeasonProfilesPanel" width="100%" styleName="gwt-DisclosurePanel-insidecomponent" visible="false">
                                <g:customHeader>
                                 <g:FlowPanel styleName="gwt-DisclosurePanel-component .header">
                                    <g:HorizontalPanel>
                                        <g:Cell width="16px" height="16px">
                                            <g:Image styleName="horizontalFlow" ui:field="cspopenorclosearrow" width="16px" height="16px"/>
                                        </g:Cell>
                                        <g:Cell>
                                            <g:Label styleName="gwt-DisclosurePanel-component .header" ui:field="cspPanelHeader" debugId="cspPanelHeader" text="{msg.getCalendarAssignDayProfilesHeading}"/>
                                        </g:Cell>
                                        <g:Cell>
                                           <g:Label styleName="gwt-Label-iPayLink" horizontalAlignment="ALIGN_RIGHT" ui:field="cspCompleteHeader" debugId="cspCompleteHeader" />
                                        </g:Cell>
                                    </g:HorizontalPanel>
                                </g:FlowPanel>
                                </g:customHeader>
                                <g:FlowPanel  styleName="simple-center">
                                    <tc:AssignDayProfilesPanel ui:field="assignDayProfiles" debugId="assignDayProfiles"></tc:AssignDayProfilesPanel>
                             </g:FlowPanel>
                            </g:DisclosurePanel>
                        </g:FlowPanel>
                        
                        
                        <g:FlowPanel>
                            <g:DisclosurePanel ui:field="specialdayPanel" debugId="specialdayPanel" width="100%" styleName="gwt-DisclosurePanel-insidecomponent" visible="false">
                                <g:customHeader>
                                 <g:FlowPanel styleName="gwt-DisclosurePanel-component .header">
                                    <g:HorizontalPanel>
                                        <g:Cell width="16px" height="16px">
                                            <g:Image styleName="horizontalFlow" ui:field="sdopenorclosearrow" width="16px" height="16px"/>
                                        </g:Cell>
                                        <g:Cell>
                                            <g:Label styleName="gwt-DisclosurePanel-component .header" ui:field="sdPanelHeader" debugId="sdPanelHeader" text="{msg.getCalendarSpecialdayHeading}"/>
                                        </g:Cell>
                                        <g:Cell>
                                           <g:Label styleName="gwt-Label-iPayLink" horizontalAlignment="ALIGN_RIGHT" ui:field="sdCompleteHeader" debugId="sdCompleteHeader" />
                                        </g:Cell>
                                    </g:HorizontalPanel>
                                </g:FlowPanel>
                                </g:customHeader>
                                <g:FlowPanel  styleName="simple-center">
                                    <tc:SpecialDayPanel ui:field="specialdays" debugId="specialdays"></tc:SpecialDayPanel>
                                </g:FlowPanel>
                            </g:DisclosurePanel>
                        </g:FlowPanel>
                    </g:VerticalPanel>        
				</g:FlowPanel>
			</g:ScrollPanel>
		</g:center>
	</g:DockLayoutPanel>

</ui:UiBinder> 
