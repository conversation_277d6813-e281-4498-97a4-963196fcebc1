package za.co.ipay.metermng.client.history.mapper;

import com.google.gwt.place.shared.PlaceHistoryMapper;
import com.google.gwt.place.shared.WithTokenizers;

import za.co.ipay.metermng.client.history.AddMeterReadingsPlace;
import za.co.ipay.metermng.client.history.AdminDashboardPlace;
import za.co.ipay.metermng.client.history.AppSettingPlace;
import za.co.ipay.metermng.client.history.AuxAccountUploadPlace;
import za.co.ipay.metermng.client.history.AuxChargeSchedulePlace;
import za.co.ipay.metermng.client.history.AuxTransUploadPlace;
import za.co.ipay.metermng.client.history.AuxTypePlace;
import za.co.ipay.metermng.client.history.BillingDetPlace;
import za.co.ipay.metermng.client.history.BlockingTypePlace;
import za.co.ipay.metermng.client.history.CalendarPlace;
import za.co.ipay.metermng.client.history.ChangePasswordPlace;
import za.co.ipay.metermng.client.history.CustomerPlace;
import za.co.ipay.metermng.client.history.CustomerTransUploadPlace;
import za.co.ipay.metermng.client.history.DashboardPlace;
import za.co.ipay.metermng.client.history.DeviceStorePlace;
import za.co.ipay.metermng.client.history.DisplayTokensPlace;
import za.co.ipay.metermng.client.history.EnergyBalancingAlertPlace;
import za.co.ipay.metermng.client.history.EnergyBalancingMeterPlace;
import za.co.ipay.metermng.client.history.GlobalNdpPlace;
import za.co.ipay.metermng.client.history.GroupPlace;
import za.co.ipay.metermng.client.history.ImportFilePlace;
import za.co.ipay.metermng.client.history.ManufacturerPlace;
import za.co.ipay.metermng.client.history.MdcPlace;
import za.co.ipay.metermng.client.history.MetadataUploadPlace;
import za.co.ipay.metermng.client.history.MeterBulkUploadPlace;
import za.co.ipay.metermng.client.history.MeterCustUPUploadPlace;
import za.co.ipay.metermng.client.history.MeterModelPlace;
import za.co.ipay.metermng.client.history.MeterOnlineBulkPlace;
import za.co.ipay.metermng.client.history.MeterPlace;
import za.co.ipay.metermng.client.history.MeterReadingsPlace;
import za.co.ipay.metermng.client.history.PricingStructurePlace;
import za.co.ipay.metermng.client.history.SearchPlace;
import za.co.ipay.metermng.client.history.SelectAccessGroupPlace;
import za.co.ipay.metermng.client.history.SpecialActionsPlace;
import za.co.ipay.metermng.client.history.SupplyGroupPlace;
import za.co.ipay.metermng.client.history.TaskSchedulePlace;
import za.co.ipay.metermng.client.history.UsagePointPlace;
import za.co.ipay.metermng.client.history.UserGroupPlace;
import za.co.ipay.metermng.client.history.UserInterfacePlace;

@WithTokenizers({MeterPlace.Tokenizer.class,
        CustomerPlace.Tokenizer.class,
        UsagePointPlace.Tokenizer.class,
        GroupPlace.Tokenizer.class,
        PricingStructurePlace.Tokenizer.class,
        AuxChargeSchedulePlace.Tokenizer.class,
        AuxTypePlace.Tokenizer.class,
        SupplyGroupPlace.Tokenizer.class,
        DisplayTokensPlace.Tokenizer.class,
        DeviceStorePlace.Tokenizer.class,
        BlockingTypePlace.Tokenizer.class,
        UserGroupPlace.Tokenizer.class,
        SearchPlace.Tokenizer.class,
        MeterReadingsPlace.Tokenizer.class,
        CalendarPlace.Tokenizer.class,
        EnergyBalancingAlertPlace.Tokenizer.class,
		EnergyBalancingMeterPlace.Tokenizer.class,
        ManufacturerPlace.Tokenizer.class,
        MeterModelPlace.Tokenizer.class,
        MdcPlace.Tokenizer.class,
        AddMeterReadingsPlace.Tokenizer.class,
        TaskSchedulePlace.Tokenizer.class,
        ChangePasswordPlace.Tokenizer.class,
        SelectAccessGroupPlace.Tokenizer.class,
        DashboardPlace.Tokenizer.class,
        AdminDashboardPlace.Tokenizer.class,
        AppSettingPlace.Tokenizer.class,
        CustomerTransUploadPlace.Tokenizer.class,
        AuxTransUploadPlace.Tokenizer.class,
        GlobalNdpPlace.Tokenizer.class,
        BillingDetPlace.Tokenizer.class,
        MeterCustUPUploadPlace.Tokenizer.class,
        MeterBulkUploadPlace.Tokenizer.class,
        AuxAccountUploadPlace.Tokenizer.class,
        SpecialActionsPlace.Tokenizer.class,
        MeterOnlineBulkPlace.Tokenizer.class,
        ImportFilePlace.Tokenizer.class,
        MetadataUploadPlace.Tokenizer.class,
        UserInterfacePlace.Tokenizer.class})
public interface WorkspacePlaceToHistoryMapper extends PlaceHistoryMapper {

}

