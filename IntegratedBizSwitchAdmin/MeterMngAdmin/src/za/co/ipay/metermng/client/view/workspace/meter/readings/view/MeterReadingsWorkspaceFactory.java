package za.co.ipay.metermng.client.view.workspace.meter.readings.view;

import za.co.ipay.gwt.common.client.Dialogs;
import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.gwt.common.client.workspace.WorkspaceCreateCallback;
import za.co.ipay.gwt.common.client.workspace.WorkspaceFactory;
import za.co.ipay.gwt.common.shared.exception.AccessControlException;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.history.MeterReadingsPlace;
import za.co.ipay.metermng.client.resource.image.MediaResource.MediaResourceUtil;
import za.co.ipay.metermng.shared.MeterMngStatics;
import za.co.ipay.metermng.shared.dto.user.MeterMngUser;

import com.google.gwt.place.shared.Place;

public class MeterReadingsWorkspaceFactory implements WorkspaceFactory {
    
    private ClientFactory clientFactory;

    public MeterReadingsWorkspaceFactory(ClientFactory clientFactory) {
        this.clientFactory = clientFactory;
        clientFactory.getWorkspaceContainer().register(this);
    }

    @Override
    public void createWorkspace(Place place, WorkspaceCreateCallback workspaceCreateCallback) {
        MeterMngUser user = clientFactory.getUser();
        if (!user.hasPermission(MeterMngStatics.ACCESS_PERMISSION_MM_METER_READINGS)
                && !user.hasPermission(MeterMngStatics.ACCESS_PERMISSION_MM_METER_READINGS_VIEW)) {
            Dialogs.displayErrorMessage(MessagesUtil.getInstance().getMessage("error.accessdenied"), 
                    MediaResourceUtil.getInstance().getLockedIcon(), 
                    MessagesUtil.getInstance().getMessage("button.close"));
            workspaceCreateCallback.onWorkspaceCreationFailed(new AccessControlException("Access is Denied"));
            return;               
        }
        try {
            MeterReadingsWorkspaceView view = new MeterReadingsWorkspaceView(clientFactory, (MeterReadingsPlace) place);
            workspaceCreateCallback.onWorkspaceCreated(view);
        } catch (Exception e) {
            workspaceCreateCallback.onWorkspaceCreationFailed(e);
        }
    }

    @Override
    public boolean handles(Place place) {
        if (place instanceof MeterReadingsPlace) {
            return true;
        } else {
            return false;
        }
    }
}
