package za.co.ipay.metermng.client.view.workspace.specialactions;

import za.co.ipay.gwt.common.client.form.FormElement;
import za.co.ipay.gwt.common.client.form.SimpleForm;
import za.co.ipay.gwt.common.client.handler.FormDataChangeHandler;
import za.co.ipay.gwt.common.client.handler.FormDataClickHandler;
import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.metermng.client.form.SimpleFormPanel;

import com.google.gwt.core.client.GWT;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.user.client.ui.CheckBox;
import com.google.gwt.user.client.ui.ListBox;
import com.google.gwt.user.client.ui.TextArea;
import com.google.gwt.user.client.ui.TextBox;
import com.google.gwt.user.client.ui.Widget;

public class SpecialActionsPanel extends SimpleFormPanel {

    @UiField TextBox nameTextBox;
    @UiField TextArea descriptionTextArea;
    @UiField CheckBox reasonRequiredBox;
    @UiField ListBox inputTypeBox;
    
    @UiField FormElement nameElement;
    @UiField FormElement descriptionElement;
    @UiField FormElement reasonRequiredElement;
    @UiField FormElement inputTypeElement;

    private static SpecialActionsPanelUiBinder uiBinder = GWT.create(SpecialActionsPanelUiBinder.class);

    interface SpecialActionsPanelUiBinder extends UiBinder<Widget, SpecialActionsPanel> {
    }

    public SpecialActionsPanel(SimpleForm form) {
        super(form);
        initWidget(uiBinder.createAndBindUi(this));        
        addFieldHandlers();
        populateInputTypeListBox();
    }
    
    public void clearFields() {
        form.setDirtyData(false);
        nameTextBox.setText("");
        descriptionTextArea.setText("");
        reasonRequiredBox.setValue(false);
        inputTypeBox.setItemSelected(0, true);
    }

    public void clearErrors() {
        nameElement.clearErrorMsg();
        descriptionElement.clearErrorMsg();
        reasonRequiredElement.clearErrorMsg();
        inputTypeElement.clearErrorMsg();
    }

    @Override
    public void addFieldHandlers() {
        nameTextBox.addChangeHandler(new FormDataChangeHandler(form));
        descriptionTextArea.addChangeHandler(new FormDataChangeHandler(form));
        reasonRequiredBox.addClickHandler(new FormDataClickHandler(form));
        inputTypeBox.addChangeHandler(new FormDataChangeHandler(form));
    }
    
    private void populateInputTypeListBox() {
        inputTypeBox.clear();
        inputTypeBox.addItem(MessagesUtil.getInstance().getMessage("special.actions.reason.inputtype.freetext"), SpecialActionsView.ReasonsInputType.FTXT.toString());
        inputTypeBox.addItem(MessagesUtil.getInstance().getMessage("special.actions.reason.inputtype.selected"), SpecialActionsView.ReasonsInputType.SLCT.toString());
        inputTypeBox.addItem(MessagesUtil.getInstance().getMessage("special.actions.reason.inputtype.both"), SpecialActionsView.ReasonsInputType.BOTH.toString());
    }
}
