package za.co.ipay.metermng.client.history;

import com.google.gwt.place.shared.Place;
import com.google.gwt.place.shared.PlaceTokenizer;
import com.google.gwt.place.shared.Prefix;

public class UserInterfacePlace extends Place {

    public static final String ALL = "all";

    private String name;

    public UserInterfacePlace(String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }

    public static String getPlaceAsString(UserInterfacePlace place) {
        return "userInterface:" + place.getName();
    }

    @Prefix(value = "userInterface")
    public static class Tokenizer implements PlaceTokenizer<UserInterfacePlace> {

        @Override
        public String getToken(UserInterfacePlace place) {
            return place.getName();
        }

        @Override
        public UserInterfacePlace getPlace(String token) {
            return new UserInterfacePlace(token);
        }
    }
}
