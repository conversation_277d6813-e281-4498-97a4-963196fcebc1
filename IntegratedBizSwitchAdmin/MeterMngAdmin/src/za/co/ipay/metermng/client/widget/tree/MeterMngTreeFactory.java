package za.co.ipay.metermng.client.widget.tree;

import com.google.gwt.core.client.GWT;
import com.google.gwt.user.cellview.client.CellTree;
import com.google.gwt.user.cellview.client.CellTree.CellTreeMessages;
import com.google.gwt.view.client.TreeViewModel;

import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.gwt.common.client.workspace.TreeFactory;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.resource.tree.CustomCellTreeResources;

public class MeterMngTreeFactory implements TreeFactory {
    public static final int DEFAULT_GROUP_TREE_DISPLAY_SIZE = 500;
    private TreeViewModel model;
    private ClientFactory clientFactory;
    
    public MeterMngTreeFactory(TreeViewModel model, ClientFactory clientFactory) {
        this.model = model;
        this.clientFactory = clientFactory;
    }

    public static int groupTreeDisplaySizeSetting(String value) {
        Integer defaultNodeSize = DEFAULT_GROUP_TREE_DISPLAY_SIZE;
        try {
            if(value != null) {
                defaultNodeSize = Integer.parseInt(value);
                if(defaultNodeSize <= 0) {
                    defaultNodeSize = MeterMngTreeFactory.DEFAULT_GROUP_TREE_DISPLAY_SIZE;
                }
            }
        } catch (NumberFormatException e) {
        }
        return defaultNodeSize;
    }
    
    @Override
    public CellTree createTree() {
        CellTreeMessages cellTreeMessages = new CellTreeMessages() {
            
            @Override
            public String showMore() {
                return MessagesUtil.getInstance().getMessage("grouptree.show_more");
            }
            
            @Override
            public String emptyTree() {
                return MessagesUtil.getInstance().getMessage("grouptree.empty");
            }
        };
        return new CellTree(model, null, (CustomCellTreeResources) GWT.create(CustomCellTreeResources.class), cellTreeMessages, clientFactory.getGroupsTreeDisplaySize());
    }
}
