package za.co.ipay.metermng.client.view.workspace.bulkupload.custtransbulkupload;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.logging.Logger;

import com.google.gwt.cell.client.AbstractCell;
import com.google.gwt.core.client.GWT;
import com.google.gwt.place.shared.Place;
import com.google.gwt.safehtml.shared.SafeHtmlBuilder;
import com.google.gwt.safehtml.shared.SafeHtmlUtils;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.user.cellview.client.CellTable;
import com.google.gwt.user.cellview.client.Column;
import com.google.gwt.user.cellview.client.TextColumn;
import com.google.gwt.user.client.Window;
import com.google.gwt.user.client.ui.DialogBox;
import com.google.gwt.user.client.ui.ScrollPanel;
import com.google.gwt.user.client.ui.Widget;
import com.google.gwt.view.client.ListDataProvider;

import za.co.ipay.gwt.common.client.Dialogs;
import za.co.ipay.gwt.common.client.factory.ResourcesFactoryUtil;
import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.history.CustomerTransUploadPlace;
import za.co.ipay.metermng.client.resource.image.MediaResource.MediaResourceUtil;
import za.co.ipay.metermng.client.view.component.bulkupload.FileUploadPanel;
import za.co.ipay.metermng.client.view.workspace.BaseWorkspace;
import za.co.ipay.metermng.client.view.workspace.bulkupload.ParentUpload;
import za.co.ipay.metermng.shared.MeterMngStatics;
import za.co.ipay.metermng.shared.dto.uploaddata.transuploaddata.CustomerTransCsvData;
import za.co.ipay.metermng.shared.dto.uploaddata.transuploaddata.TransCsvMapToData;

public class CustomerTransUploadWorkspaceView extends BaseWorkspace implements ParentUpload<CustomerTransCsvData> {

	@UiField(provided = true) FileUploadPanel<CustomerTransCsvData> fileUploadPanel;

	private CellTable<CustomerTransCsvData> clltblTransactions;
	private Column<CustomerTransCsvData, String> errorColumn;
	private boolean isCsvDataValid;

	private CellTable<CustomerTransCsvData> selectedTable;
	private ListDataProvider<CustomerTransCsvData> selectedTableDataProvider;

	private static final int DEFAULT_PAGE_SIZE = 15;
	private static final boolean IS_CUSTOMER_TRANS_UPLOAD = true;

	private static Logger logger = Logger.getLogger(CustomerTransUploadWorkspaceView.class.getName());

	private static CustomerTransUploadWorkspaceViewUiBinder uiBinder = GWT.create(CustomerTransUploadWorkspaceViewUiBinder.class);

	interface CustomerTransUploadWorkspaceViewUiBinder extends UiBinder<Widget, CustomerTransUploadWorkspaceView> {
	}

	public CustomerTransUploadWorkspaceView(ClientFactory clientFactory, CustomerTransUploadPlace place) {
		this.clientFactory = clientFactory;
		initTable();
		createTable();
		fileUploadPanel = new FileUploadPanel<CustomerTransCsvData>(clientFactory, MeterMngStatics.CUST_TRANSACTION_UPLOAD, this);
		initWidget(uiBinder.createAndBindUi(this));
		setPlaceString(CustomerTransUploadPlace.getPlaceAsString(place));
		setHeaderText(MessagesUtil.getInstance().getMessage("customer.trans.upload.heading"));
		createSelectedTable();
	}

	private void initTable() {
		clltblTransactions = new CellTable<CustomerTransCsvData>(DEFAULT_PAGE_SIZE, ResourcesFactoryUtil.getInstance().getCellTableResources());
	}

	private void createTable() {
		AbstractCell<String> errorCell = new AbstractCell<String>() {
			@Override
			public void render(Context context, String value, SafeHtmlBuilder sb) {
				if (value == null) {
					return;
				}
				sb.appendHtmlConstant("<span class=\"errorInlineNotBold\">" + value + "</span>");
			}
		};
		errorColumn = new Column<CustomerTransCsvData, String>(errorCell) {
			@Override
			public String getValue(CustomerTransCsvData object) {
				return object.getErrors();
			}
		};

		TextColumn<CustomerTransCsvData> identifierTypeColumn = new TextColumn<CustomerTransCsvData>() {
			@Override
			public String getValue(CustomerTransCsvData object) {
				return object.getIdentifierType();
			}
		};

		TextColumn<CustomerTransCsvData> identifierColumn = new TextColumn<CustomerTransCsvData>() {
			@Override
			public String getValue(CustomerTransCsvData object) {
				return object.getIdentifier();
			}
		};

		TextColumn<CustomerTransCsvData> amtInclTaxColumn = new TextColumn<CustomerTransCsvData>() {
			@Override
			public String getValue(CustomerTransCsvData object) {
				return object.getAmtInclTax();
			}
		};

		TextColumn<CustomerTransCsvData> amtTaxColumn = new TextColumn<CustomerTransCsvData>() {
			@Override
			public String getValue(CustomerTransCsvData object) {
				return object.getAmtTax();
			}
		};

		TextColumn<CustomerTransCsvData> transDateColumn = new TextColumn<CustomerTransCsvData>() {
			@Override
			public String getValue(CustomerTransCsvData object) {
				return object.getTransDate();
			}
		};

		TextColumn<CustomerTransCsvData> accountRefColumn = new TextColumn<CustomerTransCsvData>() {
			@Override
			public String getValue(CustomerTransCsvData object) {
				if (object.getAccountRef() != null) {
					if (object.getAccountRef().length() > 12) {
						return object.getAccountRef().substring(0, 13) + "...";
					}
				}
				return object.getAccountRef();
			}
		};

		/*	Moved column to selectedTable
		 * 	TextColumn<CustomerTransCsvData> commentColumn = new TextColumn<CustomerTransCsvData>() {
		 *		@Override 
		 *		public String getValue(CustomerTransCsvData object) { 
		 *			if (object.getComment().length() > 12) { 
		 *				return object.getComment().substring(0,13) + "..."; 
		 *			} 
		 *		return object.getComment(); 
		 *		} 
		 *	};
		 */

		// Add the columns.
		clltblTransactions.addColumn(errorColumn, SafeHtmlUtils.fromSafeConstant("<span class=\"error\">" + MessagesUtil.getInstance().getMessage("customer.trans.upload.errors") + "</span>"));
		clltblTransactions.addColumn(identifierTypeColumn, MessagesUtil.getInstance().getMessage("customer.trans.upload.identifierType"));
		clltblTransactions.addColumn(identifierColumn, MessagesUtil.getInstance().getMessage("customer.trans.upload.identifier"));
		clltblTransactions.addColumn(amtInclTaxColumn, MessagesUtil.getInstance().getMessage("customer.trans.upload.amt.incl.tax"));
		clltblTransactions.addColumn(amtTaxColumn, MessagesUtil.getInstance().getMessage("customer.trans.upload.amt.tax"));
		clltblTransactions.addColumn(transDateColumn, MessagesUtil.getInstance().getMessage("customer.trans.upload.trans.date"));
		clltblTransactions.addColumn(accountRefColumn, MessagesUtil.getInstance().getMessage("customer.trans.upload.account.ref"));
		// clltblTransactions.addColumn(commentColumn, MessagesUtil.getInstance().getMessage("customer.trans.upload.comment"));

	}

	private void createSelectedTable() {

		TextColumn<CustomerTransCsvData> identifierTypeColumn = new TextColumn<CustomerTransCsvData>() {
			@Override
			public String getValue(CustomerTransCsvData object) {
				return object.getIdentifierType();
			}
		};

		TextColumn<CustomerTransCsvData> identifierColumn = new TextColumn<CustomerTransCsvData>() {
			@Override
			public String getValue(CustomerTransCsvData object) {
				return object.getIdentifier();
			}
		};

		TextColumn<CustomerTransCsvData> amtInclTaxColumn = new TextColumn<CustomerTransCsvData>() {
			@Override
			public String getValue(CustomerTransCsvData object) {
				return object.getAmtInclTax();
			}
		};

		TextColumn<CustomerTransCsvData> amtTaxColumn = new TextColumn<CustomerTransCsvData>() {
			@Override
			public String getValue(CustomerTransCsvData object) {
				return object.getAmtTax();
			}
		};

		TextColumn<CustomerTransCsvData> transDateColumn = new TextColumn<CustomerTransCsvData>() {
			@Override
			public String getValue(CustomerTransCsvData object) {
				return object.getTransDate();
			}
		};

		TextColumn<CustomerTransCsvData> accountRefColumn = new TextColumn<CustomerTransCsvData>() {
			@Override
			public String getValue(CustomerTransCsvData object) {
				return object.getAccountRef();
			}
		};

		TextColumn<CustomerTransCsvData> commentColumn = new TextColumn<CustomerTransCsvData>() {
			@Override
			public String getValue(CustomerTransCsvData object) {
				return object.getComment();
			}
		};

		selectedTable = new CellTable<CustomerTransCsvData>();
		selectedTable.addColumn(identifierTypeColumn, MessagesUtil.getInstance().getMessage("customer.trans.upload.identifierType"));
		selectedTable.addColumn(identifierColumn, MessagesUtil.getInstance().getMessage("customer.trans.upload.identifier"));
		selectedTable.addColumn(amtInclTaxColumn, MessagesUtil.getInstance().getMessage("customer.trans.upload.amt.incl.tax"));
		selectedTable.addColumn(amtTaxColumn, MessagesUtil.getInstance().getMessage("customer.trans.upload.amt.tax"));
		selectedTable.addColumn(transDateColumn, MessagesUtil.getInstance().getMessage("customer.trans.upload.trans.date"));
		selectedTable.addColumn(accountRefColumn, MessagesUtil.getInstance().getMessage("customer.trans.upload.account.ref"));
		selectedTable.addColumn(commentColumn, MessagesUtil.getInstance().getMessage("customer.trans.upload.comment"));

		selectedTableDataProvider = new ListDataProvider<CustomerTransCsvData>();
		selectedTableDataProvider.addDataDisplay(selectedTable);
	}

	// START : ParentUpload methods
	@Override
	public String getPageHeaderKey() {
		return "customer.trans.upload.heading";
	}

	@Override
	public String getDataTitleKey() {
		return "customer.trans.upload.data.title";
	}

	@Override
	public String getUploadDescriptionKey() {
		return "customer.trans.upload.data.description";
	}

	@Override
	public String getUrlHandlerMapping() {
		return "secure/customertransbulkupload.do";
	}

	@Override
	public CellTable<CustomerTransCsvData> getTable() {
		return clltblTransactions;
	}

	@Override
	public void displaySelected(CustomerTransCsvData selected, int left, int top) {
		selectedTableDataProvider.getList().clear();
		selectedTableDataProvider.getList().add(selected);
		selectedTableDataProvider.refresh();

		// Throw out a popup with a limited width and horizontal scrollbar
		DialogBox simplePopup = new DialogBox(true);
		simplePopup.setText(MessagesUtil.getInstance().getMessage(""));
		simplePopup.setAnimationEnabled(true);
		ScrollPanel scrollPanel = new ScrollPanel(selectedTable);
		int popupwidth = Window.getClientWidth() - clientFactory.getPrimaryLayoutView().getSidePanelStackWidth() - 100;
		scrollPanel.setWidth(popupwidth + "px");
		simplePopup.setWidget(scrollPanel);
		simplePopup.setPopupPosition(left, top);
		simplePopup.show();
	}

	@Override
	public Column<CustomerTransCsvData, String> getErrorColumn() {
		return errorColumn;
	}

	@Override
	public List<CustomerTransCsvData> getTransCsvList(String result) {
		TransCsvMapToData csvMapData = new TransCsvMapToData(IS_CUSTOMER_TRANS_UPLOAD);
		HashMap<Integer, String> csvFieldMap = new HashMap<Integer, String>();

		isCsvDataValid = true;

		List<CustomerTransCsvData> transCsvDataList = new ArrayList<CustomerTransCsvData>();
		String[] transStringArray = result.split("\r\n|[\r\n]"); // System.lineSeparator());
		for (String trans : transStringArray) {
			if (trans != null && !trans.isEmpty()) {
				if (trans.contains("Info: Required")) {
					continue;
				}
				if (trans.contains("Identifier Type") && csvFieldMap.isEmpty()) {
					// for headerLine construct the index to DataName Map
					try {
						csvFieldMap = csvMapData.constructCsvFieldMap(trans);
						continue;
					} catch (Exception e) {
						String[] paramArr = new String[] { e.getMessage().substring(e.getMessage().indexOf(':') + 1) }; // Strips out "Unknown Column Heading:", leaves Heading Name
						Dialogs.centreErrorMessage(MessagesUtil.getInstance().getMessage("bulk.upload.file.unrecognized.heading.error", paramArr),
								MediaResourceUtil.getInstance().getErrorIcon(),
								MessagesUtil.getInstance().getMessage("button.close"));
					}
				}

				CustomerTransCsvData custTrans = null;
				try {
					custTrans = new CustomerTransCsvData(csvFieldMap, trans, true);
				} catch (Exception e) {
					logger.info("CUSTOMERTRANSUPLOAD ERROR CREATING CustomerTransCsvData!! Exception= " + e.getMessage());
					Dialogs.centreErrorMessage(MessagesUtil.getInstance().getMessage("bulk.upload.object.creation.error", new String[] { "CustomerTrans" }),
							MediaResourceUtil.getInstance().getErrorIcon(),
							MessagesUtil.getInstance().getMessage("button.close"));
				}
				if (custTrans == null || !custTrans.getErrors().isEmpty()) {
					isCsvDataValid = false;
				}
				transCsvDataList.add(custTrans);
			}
		}
		return transCsvDataList;
	}

	@Override
	public boolean isCsvDataValid() {
		return isCsvDataValid;
	}
	// END : ParentUpload methods

	// START : Workspace methods
	@Override
	public void onArrival(Place place) {
	}

	@Override
	public void onLeaving() {
	}

	@Override
	public void onSelect() {
	}

	@Override
	public void onClose() {
	}

	@Override
	public boolean handles(Place place) {
		return (place instanceof CustomerTransUploadPlace);
	}
	// END : Workspace methods
}