package za.co.ipay.metermng.client.view.component.importfile.filetypehelperimpl;

import java.util.Map;
import java.util.logging.Logger;

import com.google.gwt.user.cellview.client.CellTable;
import com.google.gwt.user.cellview.client.Column;

import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.view.component.importfile.BulkFreeIssueImportDialogueBox;
import za.co.ipay.metermng.client.view.component.importfile.ImportFileItemBaseDialogueBox;
import za.co.ipay.metermng.client.view.component.importfile.ImportFileItemView;
import za.co.ipay.metermng.shared.dto.importfile.ImportFileDto;
import za.co.ipay.metermng.shared.dto.importfile.ImportFileItemDto;

public class BulkFreeIssueHelper extends BaseFiletypeHelper {
    
    public BulkFreeIssueHelper(ClientFactory clientfactory, ImportFileItemView parent) {
        super(clientfactory, parent);
    }
    
    @Override
    public ImportFileItemBaseDialogueBox setImportFileItemBaseDialogueBox() {
        return new BulkFreeIssueImportDialogueBox(clientFactory, parent);
    }

    @Override
    public String getMeterColumnValue(ImportFileItemDto object) {
        return object.getBulkFreeIssueImportRecord().getMeterNum();
    }

    @SuppressWarnings({ "unchecked", "rawtypes" })
    @Override
    public void addCustomColumnsToTable(CellTable<ImportFileItemDto> table, Map<String, Column> tableColumnMap) {
        table.addColumn(tableColumnMap.get("meterCol"), messagesInstance.getMessage("import.meter.label"));
    }
    
    @Override
    public void generateCsvDownload(Logger logger, ImportFileDto importFileDto) {
        // TODO: Implement CSV download for generated tokens
        // This will be similar to BulkKeyChangeHelper.generateCsvDownload
        // Will download the generated free issue tokens in CSV format
    }
}
