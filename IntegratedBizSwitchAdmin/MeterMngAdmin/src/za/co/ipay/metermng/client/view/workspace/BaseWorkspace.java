package za.co.ipay.metermng.client.view.workspace;

import com.google.gwt.place.shared.Place;
import com.google.gwt.uibinder.client.UiFactory;

import za.co.ipay.gwt.common.client.workspace.AbstractWorkspace;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.i18n.UiMessages;
import za.co.ipay.metermng.client.i18n.UiMessagesUtil;
import za.co.ipay.metermng.shared.MeterMngStatics;

/**
 * BaseWorkspace provides common functionality for all the workspaces to use.
 *
 * <AUTHOR>
 */
public abstract class BaseWorkspace extends AbstractWorkspace {

    protected ClientFactory clientFactory;

    /**
     * This is a Ui factory method that allows all the workspace's  UiBinder XML files to use the same instance of the UiMessages.
     *
     * @return The UiMessages used to get i18n strings for the UiBinder XML files.
     */
    @UiFactory
    public UiMessages getUiMessages() {
        return UiMessagesUtil.getInstance();
    }

    public int getPageSize() {
        return MeterMngStatics.DEFAULT_PAGE_SIZE;
    }

    public int getAnimationTime() {
        return MeterMngStatics.ANIMATION_TIME;
    }

    /*
     * These defaults are extensively used and it's only reasonable to have them here defaulted as empty implementations
     */
    //<start-defaults>
    public void onArrival(Place place) {
    }

    @Override
    public void onClose() {
    }

    @Override
    public void onLeaving() {
    }

    @Override
    public void onSelect() {
    }
    //</end-defaults>
}
