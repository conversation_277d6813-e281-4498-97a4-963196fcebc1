package za.co.ipay.metermng.client.view.workspace.specialactions;


import za.co.ipay.gwt.common.client.form.FormElement;
import za.co.ipay.gwt.common.client.form.SimpleForm;
import za.co.ipay.gwt.common.client.handler.FormDataChangeHandler;
import za.co.ipay.gwt.common.client.handler.FormDataClickHandler;
import za.co.ipay.metermng.client.form.SimpleFormPanel;
import za.co.ipay.metermng.client.view.component.MridComponent;
import za.co.ipay.metermng.mybatis.generated.model.SpecialActionReasons;
import za.co.ipay.metermng.mybatis.generated.model.SpecialActions;

import com.google.gwt.core.client.GWT;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.user.client.ui.CheckBox;
import com.google.gwt.user.client.ui.TextBox;
import com.google.gwt.user.client.ui.Widget;

public class SpecialActionReasonsPanel extends SimpleFormPanel {
    
    @UiField TextBox nameTextBox;
    @UiField TextBox descriptionTextBox;
    @UiField CheckBox activeBox;
    
    @UiField FormElement activeElement;
    @UiField FormElement nameElement;
    @UiField FormElement descriptionElement;
    @UiField (provided=true)MridComponent mridComponent;
	
    private static SpecialActionReasonsPanelUiBinder uiBinder = GWT.create(SpecialActionReasonsPanelUiBinder.class);

    interface SpecialActionReasonsPanelUiBinder extends UiBinder<Widget, SpecialActionReasonsPanel> {
    }

    public SpecialActionReasonsPanel(SimpleForm form) {
        super(form);
        mridComponent = new MridComponent();
        initWidget(uiBinder.createAndBindUi(this));
        addFieldHandlers();
    }
    
    public void initData(SpecialActions specialActions, SpecialActionReasons specialActionReasonsList) {
        
    }
    
     @Override
    public void addFieldHandlers() {
        nameTextBox.addChangeHandler(new FormDataChangeHandler(form));
        descriptionTextBox.addChangeHandler(new FormDataChangeHandler(form));
        activeBox.addClickHandler(new FormDataClickHandler(form));
        mridComponent.addFieldHandlers(form);
    }

    @Override
    public void clearFields() {
    	form.setDirtyData(false);
        nameTextBox.setText("");
        descriptionTextBox.setText("");
        activeBox.setValue(false);
        mridComponent.setMrid(null);
    }
    
    @Override
    public void clearErrors() {
    	nameElement.clearErrorMsg();
        descriptionElement.clearErrorMsg();
        activeElement.clearErrorMsg();
        mridComponent.clearErrorMsg();
    }

}