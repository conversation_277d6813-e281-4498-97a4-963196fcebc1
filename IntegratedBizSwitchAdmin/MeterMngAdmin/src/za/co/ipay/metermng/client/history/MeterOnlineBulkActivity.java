package za.co.ipay.metermng.client.history;

import za.co.ipay.metermng.client.event.MeterOnlineBulkEvent;
import za.co.ipay.metermng.client.factory.ClientFactory;

import com.google.gwt.activity.shared.AbstractActivity;
import com.google.gwt.event.shared.EventBus;
import com.google.gwt.user.client.ui.AcceptsOneWidget;

public class MeterOnlineBulkActivity extends AbstractActivity {
	
	private ClientFactory clientFactory;
	private MeterOnlineBulkPlace meterOnlineBulkPlace;

	public MeterOnlineBulkActivity(MeterOnlineBulkPlace meterOnlineBulkPlace, ClientFactory clientFactory) {
		super();
		this.clientFactory = clientFactory;
		this.meterOnlineBulkPlace = meterOnlineBulkPlace;
	}

	@Override
	public void start(AcceptsOneWidget panel, EventBus eventBus) {
		clientFactory.getEventBus().fireEvent(new MeterOnlineBulkEvent(meterOnlineBulkPlace.getName()));
	}

}
