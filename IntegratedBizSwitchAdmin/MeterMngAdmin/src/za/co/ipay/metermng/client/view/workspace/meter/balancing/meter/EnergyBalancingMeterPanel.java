package za.co.ipay.metermng.client.view.workspace.meter.balancing.meter;

import java.util.ArrayList;

import com.google.gwt.core.client.GWT;
import com.google.gwt.event.dom.client.ClickEvent;
import com.google.gwt.event.dom.client.FocusEvent;
import com.google.gwt.event.dom.client.FocusHandler;
import com.google.gwt.event.logical.shared.SelectionEvent;
import com.google.gwt.event.logical.shared.SelectionHandler;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.uibinder.client.UiHandler;
import com.google.gwt.user.client.ui.Button;
import com.google.gwt.user.client.ui.ListBox;
import com.google.gwt.user.client.ui.SuggestBox;
import com.google.gwt.user.client.ui.SuggestOracle;
import com.google.gwt.user.client.ui.SuggestOracle.Suggestion;
import com.google.gwt.user.client.ui.Widget;

import za.co.ipay.gwt.common.client.form.FormElement;
import za.co.ipay.gwt.common.client.form.SimpleForm;
import za.co.ipay.gwt.common.client.handler.FormDataClickHandler;
import za.co.ipay.gwt.common.client.handler.FormDataValueChangeHandler;
import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.form.SimpleFormPanel;
import za.co.ipay.metermng.client.rpc.callback.ClientCallback;
import za.co.ipay.metermng.mybatis.custom.model.MeterDto;
import za.co.ipay.metermng.shared.MeterSuggestOracle;
import za.co.ipay.metermng.shared.MeterSuggestion;
import za.co.ipay.metermng.shared.dto.meter.SuperSubMeterDto;

public class EnergyBalancingMeterPanel extends SimpleFormPanel {
    
    ClientFactory clientFactory;
    
    @UiField FormElement superMeterElement;
    @UiField FormElement subMeterElement;
    @UiField FormElement selectedMetersElement;
    
    @UiField(provided=true) SuggestBox superMeterBox;
    MeterSuggestOracle superSuggest;
    MeterDto selectedSuperMeter;
    
    @UiField(provided=true) SuggestBox subMeterBox;
    MeterSuggestOracle subSuggest;
    MeterDto selectedSubMeter;
    
    @UiField ListBox selectedMetersBox;

    @UiField Button addButton;
    @UiField Button removeButton;
    
    private static EnergyBalancingMeterPanelUiBinder uiBinder = GWT.create(EnergyBalancingMeterPanelUiBinder.class);

    interface EnergyBalancingMeterPanelUiBinder extends UiBinder<Widget, EnergyBalancingMeterPanel> {
    }

    public EnergyBalancingMeterPanel(ClientFactory clientFactory, SimpleForm form) {
        super(form);
        this.clientFactory = clientFactory;
        createMeterBoxes(clientFactory);
        initWidget(uiBinder.createAndBindUi(this));
        initUi();
    }
    
    private void createMeterBoxes(ClientFactory clientFactory) {
        superSuggest = new MeterSuggestOracle(clientFactory);
        superMeterBox = new SuggestBox(superSuggest);
        superMeterBox.addSelectionHandler(new SelectionHandler<SuggestOracle.Suggestion>() {            
            @Override
            public void onSelection(SelectionEvent<Suggestion> event) {
                if (event.getSelectedItem() instanceof MeterSuggestion) {
                    selectedSuperMeter = ((MeterSuggestion) event.getSelectedItem()).getMeter();
                    loadSubMeters(selectedSuperMeter);
                }                
            }
        });
        superMeterBox.getValueBox().addFocusHandler(new FocusHandler() {            
            @Override
            public void onFocus(FocusEvent event) {
                superMeterElement.setErrorMsg(null);
                superMeterBox.showSuggestionList();
            }
        });
        
        subSuggest = new MeterSuggestOracle(clientFactory);
        subMeterBox = new SuggestBox(subSuggest);
        subMeterBox.addSelectionHandler(new SelectionHandler<SuggestOracle.Suggestion>() {            
            @Override
            public void onSelection(SelectionEvent<Suggestion> event) {
                if (event.getSelectedItem() instanceof MeterSuggestion) {
                    selectedSubMeter = ((MeterSuggestion) event.getSelectedItem()).getMeter();
                }                
            }
        });
        subMeterBox.getValueBox().addFocusHandler(new FocusHandler() {
            @Override
            public void onFocus(FocusEvent event) {
                superMeterElement.setErrorMsg(null);
                if (selectedSuperMeter == null) {
                    MeterSuggestion firstSuggestion = superSuggest.getFirstSuggestion();
                    if (firstSuggestion != null) {
                        MeterDto md = firstSuggestion.getMeter();
                        if (md.getNumber() != superMeterBox.getText()) {
                            superMeterElement
                                    .setErrorMsg(MessagesUtil.getInstance().getMessage("energybalancing.error.super"));
                            superMeterBox.setFocus(true);
                            return;
                        } else {
                            selectedSuperMeter = md;
                        }
                    }
                }
                subMeterBox.showSuggestionList();
            }
        });
    }
 
    private void loadSubMeters(MeterDto selectedSuperMeter) {
        if (selectedSuperMeter != null) {
            clientFactory.getMeterRpc().getSubMeters(selectedSuperMeter.getId(), new ClientCallback<SuperSubMeterDto>() {
                @Override
                public void onSuccess(SuperSubMeterDto result) {
                    dispaySubMeters(result);
                }
            });
        }
    }
    
    private void dispaySubMeters(SuperSubMeterDto meters) {
        if (selectedSuperMeter != null && selectedSuperMeter.getId().equals(meters.getSuperMeterId())) {
            selectedMetersBox.clear();
            for(MeterDto m : meters.getSubMeters()) {
                selectedMetersBox.addItem(m.getNumber(), m.getId().toString());
            }
        }
    }
    
    protected ArrayList<Long> getSelectedSubMeters() {
        ArrayList<Long> ids = new ArrayList<Long>();
        for(int i=0;i<selectedMetersBox.getItemCount();i++) {
            ids.add(Long.valueOf(selectedMetersBox.getValue(i)));
        }
        return ids;
    }
    
    private void initUi() {
        clearFields();
        addFieldHandlers();
    }

    @Override
    public void addFieldHandlers() {
        subMeterBox.addValueChangeHandler(new FormDataValueChangeHandler<String>(form));
        addButton.addClickHandler(new FormDataClickHandler(form)); 
        removeButton.addClickHandler(new FormDataClickHandler(form));
    }

    @Override
    public void clearFields() {
        superMeterBox.setText("");
        selectedSuperMeter = null;
        subMeterBox.setText("");
        selectedSubMeter = null;
        selectedMetersBox.clear();
    }

    @Override
    public void clearErrors() {
        superMeterElement.setErrorMsg(null);
        subMeterElement.setErrorMsg(null);
        selectedMetersElement.setErrorMsg(null);        
    }    
    
    @UiHandler("addButton")
    public void onAdd(ClickEvent e) {
        clearErrors();
        if (subMeterBox.getText() == null || subMeterBox.getText().isEmpty()) {
            subMeterElement.setErrorMsg(MessagesUtil.getInstance().getMessage("energybalancing.meter.error.no.sub"));
            subMeterBox.setFocus(true);
            return;
        }
        if (selectedSubMeter == null) {
            MeterDto md = subSuggest.getFirstSuggestion().getMeter();
            if (md.getNumber() != subMeterBox.getText()) {
                subMeterElement.setErrorMsg(MessagesUtil.getInstance().getMessage("energybalancing.meter.error.no.sub"));
                subMeterBox.setFocus(true);
                return;
            } else {
                selectedSubMeter = md;
            }
        } 
        if (selectedSubMeter != null) {
            if (selectedSuperMeter != null && selectedSuperMeter.getId().equals(selectedSubMeter.getId())) {
                subMeterElement.setErrorMsg(MessagesUtil.getInstance().getMessage("energybalancing.meter.error.same.meter"));
                selectedSubMeter = null;
            } else {
                clearErrors();
                String id = selectedSubMeter.getId().toString();
                String number = selectedSubMeter.getNumber();
                boolean exists = false;
                for (int i = 0; i < selectedMetersBox.getItemCount(); i++) {
                    if (selectedMetersBox.getValue(i).equals(id)) {
                        exists = true;
                        break;
                    }
                }
                if (exists) {
                    subMeterElement.setErrorMsg(
                            MessagesUtil.getInstance().getMessage("energybalancing.error.duplicate.selected.meters"));
                } else {
                    selectedMetersBox.addItem(number, id);
                    subMeterBox.setText("");
                    selectedSubMeter = null;
                }
            }
        } else {
            //still null
            subMeterElement.setErrorMsg(MessagesUtil.getInstance().getMessage("energybalancing.meter.error.no.sub"));
        }
    }
    
    @UiHandler("removeButton")
    public void onRemove(ClickEvent e) {
        for(int i=selectedMetersBox.getItemCount()-1;i>=0;i--) {
            if (selectedMetersBox.isItemSelected(i)) {
                selectedMetersBox.removeItem(i);
            }
        }
    }
}