package za.co.ipay.metermng.client.widget.suggestboxtree;

import com.google.gwt.user.client.ui.SuggestOracle.Suggestion;

/**
 * This factory is used by a SuggestBoxTree to create oracles as needed as one traverses 
 * the hierarchy.
 * 
 * It uses callbacks for getting the oracle allowing the implementation to do asynchronous 
 * calls to get the initial data from the server on first load if needed.
 *  
 * <AUTHOR>
 * @param <T> the type of id of the suggestion eg. long or a string
 */
public interface SuggestionOracleFactory<T> {
    
    /**
     * This must create the IdentifiableSuggestOracle for the root node's SettableSuggestBox.
     * Once created the callback method must be called on the provided 
     * IdentifiableSuggestOracleCallback. This allows for asynchronous return of the oracle after making 
     * server side calls.
     * 
     * @param callback
     */
    void createInitialOracle(IdentifiableSuggestOracleCallback<T> callback);
    
    /**
     * This must create the IdentifiableSuggestOracle for any sub node on any level based on the 
     * selection made on a higher level which is the provided Suggestion.
     * 
     * Once created the callback method must be called on the provided 
     * IdentifiableSuggestOracleCallback. This allows for asynchronous return of the oracle after making 
     * server side calls.
     * 
     * @param suggestion the selected Suggestion from the node higher in the hierarchy.
     * @param callback 
     */
    void createNextOracle(Suggestion suggestion, IdentifiableSuggestOracleCallback<T> callback);
    
    /**
     * Once an oracle is created the callback method is to be called for the SuggestBoxTree to be 
     * able to use it to create a SettableSuggestBox.
     *  
     * <AUTHOR>
     * @param <T> the type of id of the suggestion eg. long or a string
     */
    public interface IdentifiableSuggestOracleCallback<T> {
        public void callback(IdentifiableSuggestOracle<T> oracle);
    }
}
