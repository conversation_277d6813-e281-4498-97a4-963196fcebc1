package za.co.ipay.metermng.client.event;

import com.google.gwt.event.shared.GwtEvent;

public class OpenDeviceStoreEvent extends GwtEvent<OpenDeviceStoreEventHandler> {

    public static Type<OpenDeviceStoreEventHandler> TYPE = new Type<OpenDeviceStoreEventHandler>();
    
    @Override
    public Type<OpenDeviceStoreEventHandler> getAssociatedType() {
        return TYPE;
    }

    @Override
    protected void dispatch(OpenDeviceStoreEventHandler handler) {
        handler.openDeviceStore(this);
    }

}