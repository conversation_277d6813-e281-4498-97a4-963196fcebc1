package za.co.ipay.metermng.client.view.workspace.meter.mdc;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.logging.Logger;

import com.google.gwt.event.dom.client.ClickEvent;
import com.google.gwt.event.dom.client.ClickHandler;
import com.google.gwt.user.cellview.client.ColumnSortEvent;
import com.google.gwt.user.cellview.client.ColumnSortEvent.ListHandler;
import com.google.gwt.user.cellview.client.TextColumn;
import com.google.gwt.user.client.ui.Anchor;
import com.google.gwt.view.client.ListDataProvider;
import za.co.ipay.gwt.common.client.Dialogs;
import za.co.ipay.gwt.common.client.factory.ResourcesFactoryUtil;
import za.co.ipay.gwt.common.client.form.FormManager;
import za.co.ipay.gwt.common.client.handler.ConfirmHandler;
import za.co.ipay.gwt.common.client.resource.Messages;
import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.gwt.common.client.validation.ClientValidatorUtil;
import za.co.ipay.gwt.common.client.workspace.SessionCheckCallback;
import za.co.ipay.gwt.common.client.workspace.SimpleTableView;
import za.co.ipay.gwt.common.client.workspace.WorkspaceNotification;
import za.co.ipay.gwt.common.client.workspace.WorkspaceNotification.NotificationType;
import za.co.ipay.gwt.common.shared.dto.IdNameDto;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.resource.image.MediaResource.MediaResourceUtil;
import za.co.ipay.metermng.client.rpc.callback.ClientCallback;
import za.co.ipay.metermng.client.view.component.BaseComponent;
import za.co.ipay.metermng.client.widget.StatusTableColumn;
import za.co.ipay.metermng.datatypes.RecordStatus;
import za.co.ipay.metermng.mybatis.generated.model.Mdc;
import za.co.ipay.metermng.mybatis.generated.model.MdcChannel;
import za.co.ipay.metermng.shared.ChannelCompatibilityE;
import za.co.ipay.metermng.shared.MeterMngStatics;
import za.co.ipay.metermng.shared.dto.MdcChannelMatchDto;
import za.co.ipay.metermng.shared.dto.meter.MdcChannelDto;

public class MdcChannelView extends BaseComponent implements FormManager<MdcChannelDto> {

    private MdcWorkspaceView parentWorkspace;

    private SimpleTableView<MdcChannelDto> view;
    MdcChannelPanel panel;

    private ListDataProvider<MdcChannelDto> dataProvider;
    private ListHandler<MdcChannelDto> columnSortHandler;
    private TextColumn<MdcChannelDto> valueColumn;
    private TextColumn<MdcChannelDto> nameColumn;
    private TextColumn<MdcChannelDto> timeIntervalColumn;
    private StatusTableColumn<MdcChannelDto> statusColumn;

    private Mdc mdc;
    private MdcChannel mdcChannel;
    private MdcChannelDto selected;

    private static Logger logger = Logger.getLogger(MdcChannelView.class.getName());

    public MdcChannelView(ClientFactory clientFactory, MdcWorkspaceView parentWorkspace, SimpleTableView<MdcChannelDto> view) {
        this.clientFactory = clientFactory;
        this.parentWorkspace = parentWorkspace;
        this.view = view;
        initView();
        initForm();
        createTable();
    }

    private void initView() {
        view.setFormManager(this);
    }

    private void initForm() {
        Anchor back = new Anchor(MessagesUtil.getInstance().getMessage("meter.mdc"));
        back.addClickHandler(new ClickHandler() {
            @Override
            public void onClick(ClickEvent event) {
                goBack();
            }
        });
        view.getPageHeader().addPageHeaderLink(back);

        panel = new MdcChannelPanel(clientFactory, view.getForm());
        panel.activeBox.setValue(true);

        view.getForm().setHasDirtyDataManager(parentWorkspace);
        view.getForm().getFormFields().add(panel);
        view.getTable().ensureDebugId("mdcChannelTable");
        view.getForm().getSaveBtn().ensureDebugId("saveChannelButton");
        view.getForm().getOtherBtn().ensureDebugId("cancelChannelButton");
        view.getForm().getBackBtn().ensureDebugId("backChannelButton");

        view.getForm().getSaveBtn().setText(MessagesUtil.getInstance().getMessage("button.create"));
        view.getForm().getSaveBtn().addClickHandler(new ClickHandler() {
            @Override
            public void onClick(ClickEvent event) {
                onSave();
            }
        });

        view.getForm().getOtherBtn().setText(MessagesUtil.getInstance().getMessage("button.cancel"));
        view.getForm().getOtherBtn().addClickHandler(new ClickHandler() {
            @Override
            public void onClick(ClickEvent event) {
                view.getForm().checkDirtyData(new ConfirmHandler() {
                    @Override
                    public void confirmed(boolean confirm) {
                        if (confirm) {
                            view.getForm().setDirtyData(false);
                            displaySelected(null);
                        }
                    }
                });
            }
        });

        view.getForm().getBackBtn().setText(MessagesUtil.getInstance().getMessage("button.back"));
        view.getForm().getBackBtn().addClickHandler(new ClickHandler() {
            @Override
            public void onClick(ClickEvent event) {
                goBack();
            }
        });
        view.getForm().getBackBtn().setVisible(true);

        view.getForm().getFormPanel().setHeadingText(MessagesUtil.getInstance().getMessage("channel.title.add"));
    }

    private void goBack() {
        view.getForm().checkDirtyData(new ConfirmHandler() {
            @Override
            public void confirmed(boolean confirm) {
                if (confirm) {
                    view.getForm().setDirtyData(false);
                    parentWorkspace.showMdc();
                }
            }});
    }

    private void createTable() {
        if (dataProvider == null) {
            valueColumn = new TextColumn<MdcChannelDto>() {
                @Override
                public String getValue(MdcChannelDto object) {
                    return object.getMdcChannel().getValue();
                }
            };
            valueColumn.setSortable(true);

            nameColumn = new TextColumn<MdcChannelDto>() {
                @Override
                public String getValue(MdcChannelDto object) {
                    return object.getMdcChannel().getName();
                }
            };
            nameColumn.setSortable(true);

            TextColumn<MdcChannelDto> descripColumn = new TextColumn<MdcChannelDto>() {
                @Override
                public String getValue(MdcChannelDto object) {
                    return object.getMdcChannel().getDescription();
                }
            };

            timeIntervalColumn = new TextColumn<MdcChannelDto>() {
                @Override
                public String getValue(MdcChannelDto object) {
                    return object.getTimeIntervalName();
                }
            };
            timeIntervalColumn.setSortable(true);

            statusColumn = new StatusTableColumn<MdcChannelDto>();
            statusColumn.setSortable(true);

            view.getTable().addColumn(valueColumn, MessagesUtil.getInstance().getMessage("channel.field.value"));
            view.getTable().addColumn(nameColumn, MessagesUtil.getInstance().getMessage("channel.field.name"));
            view.getTable().addColumn(descripColumn, MessagesUtil.getInstance().getMessage("channel.field.descrip"));
            view.getTable().addColumn(timeIntervalColumn, MessagesUtil.getInstance().getMessage("channel.field.time.interval"));
            view.getTable().addColumn(statusColumn, MessagesUtil.getInstance().getMessage("channel.field.status"));

            // Set the data provider for the table
            dataProvider = new ListDataProvider<MdcChannelDto>();
            dataProvider.addDataDisplay(view.getTable());

            // Create the table's pager
            view.getPager().setDisplay(view.getTable());

        }
    }

    public void setMdc(Mdc mdc) {
        clear();
        this.mdc = mdc;
        view.setDataDetails(mdc.getName(), mdc.getDescription());
        loadChannels();
    }

    protected void loadChannels() {
        clientFactory.getMdcChannelRpc().getMdcChannelDtos(mdc.getId(), new ClientCallback<List<MdcChannelDto>>() {
            @Override
            public void onSuccess(List<MdcChannelDto> data) {
                displayData(data);
                view.getTable().setPageStart(0);
            }
        });
    }

    private void displayData(List<MdcChannelDto> data) {
        logger.info("Displaying retrieved Mdc Channels: "+data.size());
        if (dataProvider != null && dataProvider.getList() != null) {
            dataProvider.getList().clear();
            dataProvider.getList().addAll(data);
        }
        if (columnSortHandler == null || columnSortHandler.getList() == null) {
            columnSortHandler = new ListHandler<MdcChannelDto>(dataProvider.getList());
            columnSortHandler.setComparator(valueColumn, new Comparator<MdcChannelDto>() {
                public int compare(MdcChannelDto o1, MdcChannelDto o2) {
                    if (o1 == o2) {
                        return 0;
                    }
                    if (o1 != null) {
                        return (o2 != null) ? o1.getMdcChannel().getValue().compareTo(o2.getMdcChannel().getValue()) : 1;
                    }
                    return -1;
                }
            });
            view.getTable().addColumnSortHandler(columnSortHandler);

            columnSortHandler.setComparator(nameColumn, new Comparator<MdcChannelDto>() {
                public int compare(MdcChannelDto o1, MdcChannelDto o2) {
                    if (o1 == o2) {
                        return 0;
                    }

                    // Compare the name columns.
                    if (o1 != null) {
                        return (o2 != null) ? o1.getMdcChannel().getName().compareTo(o2.getMdcChannel().getName()) : 1;
                    }
                    return -1;
                }
            });
            view.getTable().addColumnSortHandler(columnSortHandler);

            columnSortHandler.setComparator(timeIntervalColumn, new Comparator<MdcChannelDto>() {
                public int compare(MdcChannelDto o1, MdcChannelDto o2) {
                    if (o1 == o2) {
                        return 0;
                    }

                    // Compare the name columns.
                    if (o1 != null) {
                        return (o2 != null) ? o1.getMdcChannel().getTimeIntervalId().compareTo(o2.getMdcChannel().getTimeIntervalId()) : 1;
                    }
                    return -1;
                }
            });
            view.getTable().addColumnSortHandler(columnSortHandler);

            columnSortHandler.setComparator(statusColumn, new Comparator<MdcChannelDto>() {
                public int compare(MdcChannelDto o1, MdcChannelDto o2) {
                    if (o1 == o2) {
                        return 0;
                    }

                    // Compare the name columns.
                    if (o1 != null) {
                        return (o2 != null) ? o1.getMdcChannel().getRecordStatus().compareTo(o2.getMdcChannel().getRecordStatus()) : 1;
                    }
                    return -1;
                }
            });
            view.getTable().addColumnSortHandler(columnSortHandler);

            // We know that the data is sorted by value by default.
            view.getTable().getColumnSortList().push(valueColumn);
            ColumnSortEvent.fire(view.getTable(), view.getTable().getColumnSortList());
        } else {
            columnSortHandler.setList(dataProvider.getList());
            ColumnSortEvent.fire(view.getTable(), view.getTable().getColumnSortList());
        }
    }

    @Override
    public void displaySelected(MdcChannelDto selected) {
        displayMdcChannel(selected);
    }

    private void displayMdcChannel(MdcChannelDto selected) {
        this.selected = selected;
        clear();
        if (selected == null) {
            view.getForm().getSaveBtn().setText(MessagesUtil.getInstance().getMessage("button.create"));
            view.getForm().getFormPanel().setHeadingText(MessagesUtil.getInstance().getMessage("channel.title.add"));
            view.clearTableSelection();
        } else {
            view.getForm().getSaveBtn().setText(MessagesUtil.getInstance().getMessage("button.update"));
            view.getForm().getFormPanel().setHeadingText(MessagesUtil.getInstance().getMessage("channel.title.update"));

            this.mdcChannel = selected.getMdcChannel();
            panel.channelValueBox.setValue(mdcChannel.getValue());
            panel.nameBox.setText(mdcChannel.getName());
            panel.descripBox.setText(mdcChannel.getDescription());
            setBillingDetSelection(selected.getBillingDetList());
            setMeterReadingTypeSelection(mdcChannel.getMeterReadingTypeId());
            setTimeIntervalSelection(mdcChannel.getTimeIntervalId());
            panel.maxSizeBox.setText(mdcChannel.getMaxSize().toPlainString());
            if (mdcChannel.getReadingMultiplier() != null) {
                panel.readingMultiplierBox.setText(mdcChannel.getReadingMultiplier().toPlainString());
            }
            panel.activeBox.setValue(mdcChannel.getRecordStatus().equals(RecordStatus.ACT));
            setListOverridingMeterModels(selected.getOverrideMeterModelsList());
        }
    }

    private void setBillingDetSelection(List<IdNameDto> billingDetList) {
        panel.billingDetListBox.setItemSelected(0, false);;
        if (billingDetList != null && !billingDetList.isEmpty()) {
            for (int j=0;j<billingDetList.size();j++) {
                for(int i=0;i<panel.billingDetListBox.getItemCount();i++) {
                    if (panel.billingDetListBox.getValue(i).equals(billingDetList.get(j).getId().toString())) {
                        panel.billingDetListBox.setItemSelected(i, true);
                    }
                }
            }
        } else {
            panel.billingDetListBox.setSelectedIndex(0);
        }
    }

    private void setMeterReadingTypeSelection(Long id) {
        for(int i=0;i<panel.meterReadingTypeListBox.getItemCount();i++) {
            if (panel.meterReadingTypeListBox.getValue(i).equals(id.toString())) {
                panel.meterReadingTypeListBox.setSelectedIndex(i);
                break;
            }
        }
    }

    private void setTimeIntervalSelection(Long id) {
        if (id != null) {
            for(int i=0;i<panel.timeIntervalListBox.getItemCount();i++) {
                if (panel.timeIntervalListBox.getValue(i).equals(id.toString())) {
                    panel.timeIntervalListBox.setSelectedIndex(i);
                    break;
                }
            }
        }
    }

    private void setListOverridingMeterModels(List<String> overrideMeterModelsList) {
        StringBuilder bldr = new StringBuilder();
        for (String s: overrideMeterModelsList) {
            bldr.append(s).append(", ");
        }
        String overrideMM = bldr.toString();
        if (!overrideMM.isEmpty()) {
            overrideMM = overrideMM.substring(0, overrideMM.length() - 2);
        } else {
            overrideMM = MessagesUtil.getInstance().getMessage("mdc.channel.override.metermodels.none");
        }
        panel.overrideMeterModels.setText(overrideMM);
    }

    protected void clear() {
        mdcChannel = null;
        view.getForm().getFormPanel().setHeadingText(MessagesUtil.getInstance().getMessage("channel.title.add"));
        view.getForm().getSaveBtn().setText(MessagesUtil.getInstance().getMessage("button.create"));
        panel.clearFields();
        panel.clearErrors();
        view.clearTableSelection();
    }

    private boolean isValidInput() {
        boolean valid = true;
        panel.clearErrors();

        if (panel.channelValueBox.getText() == null || panel.channelValueBox.getText().isEmpty()) {
            panel.channelValueElement.setErrorMsg(MessagesUtil.getInstance().getMessage("error.field.value.null"));
            valid = false;
        }
        if (panel.nameBox.getText() == null || panel.nameBox.getText().isEmpty()){
            panel.nameElement.setErrorMsg(MessagesUtil.getInstance().getMessage("error.field.value.null"));
            valid = false;
        }
        if (panel.meterReadingTypeListBox.getSelectedIndex() == 0){
            panel.meterReadingTypeElement.setErrorMsg(MessagesUtil.getInstance().getMessage("error.field.value.null"));
            valid = false;
        }
        if (panel.maxSizeBox.getText() == null || panel.maxSizeBox.getText().isEmpty()){
            panel.maxSizeElement.setErrorMsg(MessagesUtil.getInstance().getMessage("error.field.value.null"));
            valid = false;
        }
        if (!valid) {
            return valid;
        }

        MdcChannelDto dto = updateMdcChannel();
        MdcChannel chan = dto.getMdcChannel();

        if (!ClientValidatorUtil.getInstance().validateField(chan, "value", panel.channelValueElement)) {
            valid = false;
        }
        if (!ClientValidatorUtil.getInstance().validateField(chan, "name", panel.nameElement)) {
            valid = false;
            logger.info("Invalid dto name: "+chan.getName());
        }
        if (!ClientValidatorUtil.getInstance().validateField(chan, "description", panel.descripElement)) {
            valid = false;
        }
        if (!ClientValidatorUtil.getInstance().validateField(chan, "meterReadingTypeId", panel.meterReadingTypeElement)) {
            valid = false;
        }
        if (!ClientValidatorUtil.getInstance().validateField(chan, "maxSize", panel.maxSizeElement)) {
            valid = false;
        }
        if (!ClientValidatorUtil.getInstance().validateField(chan, "readingMultiplier", panel.readingMultiplierElement)) {
            valid = false;
        }
        if (!ClientValidatorUtil.getInstance().validateField(chan, "recordStatus", panel.activeElement)) {
            valid = false;
        }

        return valid;
    }

    private MdcChannelDto updateMdcChannel() {
        MdcChannelDto dto = new MdcChannelDto();
        updateMdcChannel(dto);
        return dto;
    }

    private void updateMdcChannel(MdcChannelDto dto) {
        MdcChannel mdcChannelToUpdate = new MdcChannel();
        if (mdcChannel != null) {
            mdcChannelToUpdate.setId(mdcChannel.getId());
        }
        mdcChannelToUpdate.setMdcId(mdc.getId());
        mdcChannelToUpdate.setValue(panel.channelValueBox.getText());
        mdcChannelToUpdate.setName(panel.nameBox.getText());
        mdcChannelToUpdate.setDescription(panel.descripBox.getText());
        mdcChannelToUpdate.setMeterReadingTypeId(Long.valueOf(panel.meterReadingTypeListBox.getValue(panel.meterReadingTypeListBox.getSelectedIndex())));
        if (panel.timeIntervalListBox.getSelectedIndex() !=0) {
            mdcChannelToUpdate.setTimeIntervalId(Long.valueOf(panel.timeIntervalListBox.getValue(panel.timeIntervalListBox.getSelectedIndex())));
        } else {
            mdcChannelToUpdate.setTimeIntervalId(null);
        }
        mdcChannelToUpdate.setMaxSize(new BigDecimal(panel.maxSizeBox.getText()));
        mdcChannelToUpdate.setReadingMultiplier(panel.readingMultiplierBox.getValue());
        dto.setMdcChannel(mdcChannelToUpdate);
        dto.setRecordStatus(panel.activeBox.getValue() ? RecordStatus.ACT : RecordStatus.DAC);
        dto.setBillingDetList(getBillingDets());
    }

    private ArrayList<IdNameDto> getBillingDets() {
        ArrayList<IdNameDto> selected = new ArrayList<IdNameDto>();
        for(int i=1;i<panel.billingDetListBox.getItemCount();i++) {
            if (panel.billingDetListBox.isItemSelected(i)) {
                selected.add(new IdNameDto(Long.valueOf(panel.billingDetListBox.getValue(i)), panel.billingDetListBox.getItemText(i)));
            }
        }
        return selected;
    }

    private void onSave() {
        disableButtons();
        if (isValidInput()) {
            int numSelected = getBillingDets().size();
            if (panel.billingDetListBox.getSelectedIndex() == 0 && numSelected == 0) {
                Dialogs.confirm(
                      ResourcesFactoryUtil.getInstance().getMessages().getMessage("channel.billingdet.confirm"),
                      ResourcesFactoryUtil.getInstance().getMessages().getMessage("option.positive"),
                      ResourcesFactoryUtil.getInstance().getMessages().getMessage("option.negative"),
                      ResourcesFactoryUtil.getInstance().getQuestionIcon(),
                      new ConfirmHandler() {
                          @Override
                          public void confirmed(boolean confirm) {
                              if (confirm) {
                                  onSaveCheckActive();
                              }
                              else {
                                  enableButtons();
                                  return;
                              }
                          }
                      }
                        );
            } else {
                onSaveCheckActive();
            }
        } else {
            enableButtons();
        }
    }

    private void onSaveCheckActive() {
        //Issue warning if changing the MdcChannels on an MDC, whether that MDC is already linked to meterModel/s of active meters
        SessionCheckCallback sessionCheckCallback = new SessionCheckCallback() {
            @Override
            public void callback(SessionCheckResolution resolution) {
                if (view.getForm().isDirtyData()) {    // && mdcChannel != null && mdcChannel.getId() != null) {
                    MdcChannelDto updatedMdcChannel = updateMdcChannel();
                    clientFactory.getUsagePointRpc().getMdcChannelUpdateCompatibility(mdc.getId(), updatedMdcChannel, clientFactory.getUser().getUserName(), new ClientCallback<MdcChannelMatchDto>() {
                        @Override
                        public void onSuccess(MdcChannelMatchDto result) {
                            Integer act = result.getActiveUpCount();
                            Integer dac = result.getInactiveUpCount();
                            boolean isOnlyThinUnits = (act + dac) == result.getUpWithThinUnitsPsCount();
                            final Messages messages = MessagesUtil.getInstance();
                            logger.info("MdcChannelView: on change mdcChannel or add new one in an MDC: : match = " + result.getMatch().toString() + " countActiveUpWithMdc = " + act + " inactive=" + dac + " Potential WARNING or ERROR displayed to user: " + clientFactory.getUser().getUserName());
                            //if no_match error
                            if (ChannelCompatibilityE.NO_DATA.equals(result.getMatch())) {
                                //could be TOU PS with an MDC;  ChannelCompatibilityE only compares to regRead, else returns NO_DATA
                                if (!isOnlyThinUnits && act + dac > 0) {
                                    displayWarningMessage (messages, "warning.change.mdc.channel.NO.DATA", act, dac);
                                } else {
                                    saveNow();
                                }
                            } else if (ChannelCompatibilityE.NONE_MATCH.equals(result.getMatch()) && act > 0) {          //UP with future installation dates are included because they are already marked active. If manually activate a UP, crosschecks are performed again at that point, so allow change for inactive meters even with NONE_MATCH
                                Dialogs.centreErrorMessage(messages.getMessage("error.change.mdc.channel.NONE.MATCH.active.up", new String[] {act.toString()}),
                                        MediaResourceUtil.getInstance().getErrorIcon(),
                                        MessagesUtil.getInstance().getMessage("button.close"),
                                        new ClickHandler() {
                                            @Override
                                            public void onClick(ClickEvent arg0) {
                                                displayMdcChannel(selected);
                                                enableButtons();
                                            }
                                        });
                                return;
                            } else if (act + dac > 0) {    //i.e. PARTIAL MATCH or EXACT MATCH with UP
                                displayWarningMessage (messages, "warning.change.mdc.channel.PARTIAL.or.TOTAL.with.regreadPS", act, dac);
                            } else {
                                saveNow();           //no UP
                            }
                        }
                    });
                } else {
                    saveNow();
                }
            }
        };
        clientFactory.handleSessionCheckCallback(sessionCheckCallback);
    }


    private void displayWarningMessage (Messages messages, String messageKey, Integer act, Integer dac) {
        Dialogs.confirm(messages.getMessage(messageKey, new String[] {act.toString(), dac.toString()}),
                messages.getMessage("button.yes"),
                messages.getMessage("button.no"),
                MediaResourceUtil.getInstance().getQuestionIcon(),
                new ConfirmHandler() {
                    @Override
                    public void confirmed(boolean confirm) {
                        if(confirm) {
                            saveNow();
                        } else {
                            //redisplay selected
                            if (selected != null) {
                                displaySelected(selected);
                            } else {
                                panel.clearFields();
                            }
                            enableButtons();
                            return;
                        }
                    }
                },
                null, null);  //if make position null, will centre confirm
    }

    private void saveNow() {
        MdcChannelDto m = updateMdcChannel();
        clientFactory.getMdcChannelRpc().saveMdcChannel(
                m,
                new ClientCallback<MdcChannelDto>(view.getForm().getSaveBtn().getAbsoluteLeft(), view.getForm().getSaveBtn().getAbsoluteTop()) {
                    @Override
                    public void onSuccess(MdcChannelDto result) {
                        view.getForm().setDirtyData(false);
                        loadChannels();
                        Dialogs.displayInformationMessage(
                                MessagesUtil.getInstance()
                                .getSavedMessage(
                                        new String[] { MessagesUtil.getInstance().getMessage("channel.field.titlename") }),
                                        MediaResourceUtil.getInstance().getInformationIcon(),
                                        MessagesUtil.getInstance().getMessage("button.close"),
                                        null);
                        clear();
                        selected = null;
                        enableButtons();
                        sendNotification();
                    }
                    @Override
                    public void onFailureClient() {
                        enableButtons();
                    }
                });
    }

    private void disableButtons() {
        view.getForm().getSaveBtn().setEnabled(false);
        view.getForm().getOtherBtn().setEnabled(false);
        view.getForm().getBackBtn().setEnabled(false);
    }


    private void enableButtons() {
        view.getForm().getSaveBtn().setEnabled(true);
        view.getForm().getOtherBtn().setEnabled(true);
        view.getForm().getBackBtn().setEnabled(true);
    }

    private void sendNotification() {
        //Notify any affected tabs
        clientFactory.getWorkspaceContainer().notifyWorkspaces(new WorkspaceNotification(NotificationType.DATA_UPDATED, MeterMngStatics.MDC_CHANNEL_MODIFIED));
    }
}
