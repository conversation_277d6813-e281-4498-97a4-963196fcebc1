package za.co.ipay.metermng.client.history.mapper;

import java.util.logging.Logger;

import com.google.gwt.activity.shared.Activity;
import com.google.gwt.activity.shared.ActivityMapper;
import com.google.gwt.place.shared.Place;

import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.history.AddMeterReadingsActivity;
import za.co.ipay.metermng.client.history.AddMeterReadingsPlace;
import za.co.ipay.metermng.client.history.AdminDashboardActivity;
import za.co.ipay.metermng.client.history.AdminDashboardPlace;
import za.co.ipay.metermng.client.history.AppSettingActivity;
import za.co.ipay.metermng.client.history.AppSettingPlace;
import za.co.ipay.metermng.client.history.AuxAccountUploadActivity;
import za.co.ipay.metermng.client.history.AuxAccountUploadPlace;
import za.co.ipay.metermng.client.history.AuxChargeScheduleActivity;
import za.co.ipay.metermng.client.history.AuxChargeSchedulePlace;
import za.co.ipay.metermng.client.history.AuxTransUploadActivity;
import za.co.ipay.metermng.client.history.AuxTransUploadPlace;
import za.co.ipay.metermng.client.history.AuxTypeActivity;
import za.co.ipay.metermng.client.history.AuxTypePlace;
import za.co.ipay.metermng.client.history.BillingDetActivity;
import za.co.ipay.metermng.client.history.BillingDetPlace;
import za.co.ipay.metermng.client.history.BlockingTypeActivity;
import za.co.ipay.metermng.client.history.BlockingTypePlace;
import za.co.ipay.metermng.client.history.CalendarActivity;
import za.co.ipay.metermng.client.history.CalendarPlace;
import za.co.ipay.metermng.client.history.ChangePasswordActivity;
import za.co.ipay.metermng.client.history.ChangePasswordPlace;
import za.co.ipay.metermng.client.history.CustomerActivity;
import za.co.ipay.metermng.client.history.CustomerPlace;
import za.co.ipay.metermng.client.history.CustomerTransUploadActivity;
import za.co.ipay.metermng.client.history.CustomerTransUploadPlace;
import za.co.ipay.metermng.client.history.DashboardActivity;
import za.co.ipay.metermng.client.history.DashboardPlace;
import za.co.ipay.metermng.client.history.DeviceStoreActivity;
import za.co.ipay.metermng.client.history.DeviceStorePlace;
import za.co.ipay.metermng.client.history.DisplayTokensActivity;
import za.co.ipay.metermng.client.history.DisplayTokensPlace;
import za.co.ipay.metermng.client.history.EnergyBalancingAlertActivity;
import za.co.ipay.metermng.client.history.EnergyBalancingAlertPlace;
import za.co.ipay.metermng.client.history.EnergyBalancingMeterActivity;
import za.co.ipay.metermng.client.history.EnergyBalancingMeterPlace;
import za.co.ipay.metermng.client.history.GlobalNdpActivity;
import za.co.ipay.metermng.client.history.GlobalNdpPlace;
import za.co.ipay.metermng.client.history.GroupActivity;
import za.co.ipay.metermng.client.history.GroupPlace;
import za.co.ipay.metermng.client.history.ImportFileActivity;
import za.co.ipay.metermng.client.history.ImportFilePlace;
import za.co.ipay.metermng.client.history.ManufacturerActivity;
import za.co.ipay.metermng.client.history.ManufacturerPlace;
import za.co.ipay.metermng.client.history.MdcActivity;
import za.co.ipay.metermng.client.history.MdcPlace;
import za.co.ipay.metermng.client.history.MetadataUploadActivity;
import za.co.ipay.metermng.client.history.MetadataUploadPlace;
import za.co.ipay.metermng.client.history.MeterActivity;
import za.co.ipay.metermng.client.history.MeterBulkUploadActivity;
import za.co.ipay.metermng.client.history.MeterBulkUploadPlace;
import za.co.ipay.metermng.client.history.MeterCustUPUploadActivity;
import za.co.ipay.metermng.client.history.MeterCustUPUploadPlace;
import za.co.ipay.metermng.client.history.MeterModelActivity;
import za.co.ipay.metermng.client.history.MeterModelPlace;
import za.co.ipay.metermng.client.history.MeterOnlineBulkActivity;
import za.co.ipay.metermng.client.history.MeterOnlineBulkPlace;
import za.co.ipay.metermng.client.history.MeterPlace;
import za.co.ipay.metermng.client.history.MeterReadingsActivity;
import za.co.ipay.metermng.client.history.MeterReadingsPlace;
import za.co.ipay.metermng.client.history.PricingStructureActivity;
import za.co.ipay.metermng.client.history.PricingStructurePlace;
import za.co.ipay.metermng.client.history.SearchActivity;
import za.co.ipay.metermng.client.history.SearchPlace;
import za.co.ipay.metermng.client.history.SelectAccessGroupActivity;
import za.co.ipay.metermng.client.history.SelectAccessGroupPlace;
import za.co.ipay.metermng.client.history.SpecialActionsActivity;
import za.co.ipay.metermng.client.history.SpecialActionsPlace;
import za.co.ipay.metermng.client.history.SupplyGroupActivity;
import za.co.ipay.metermng.client.history.SupplyGroupPlace;
import za.co.ipay.metermng.client.history.TaskScheduleActivity;
import za.co.ipay.metermng.client.history.TaskSchedulePlace;
import za.co.ipay.metermng.client.history.UsagePointActivity;
import za.co.ipay.metermng.client.history.UsagePointPlace;
import za.co.ipay.metermng.client.history.UserGroupActivity;
import za.co.ipay.metermng.client.history.UserGroupPlace;
import za.co.ipay.metermng.client.history.UserInterfaceActivity;
import za.co.ipay.metermng.client.history.UserInterfacePlace;

public class PlaceToActivityMapper implements ActivityMapper {

    private static Logger logger = Logger.getLogger("PlaceToActivityMapper");
    private ClientFactory clientFactory;

    public PlaceToActivityMapper(ClientFactory clientFactory) {
        super();
        this.clientFactory = clientFactory;
    }

    @Override
    public Activity getActivity(Place place) {
        if (place instanceof MeterPlace) {
            return new MeterActivity((MeterPlace) place, clientFactory);
        }
        if (place instanceof CustomerPlace) {
            return new CustomerActivity((CustomerPlace) place, clientFactory);
        }
        if (place instanceof UsagePointPlace) {
            return new UsagePointActivity((UsagePointPlace) place, clientFactory);
        }
        if (place instanceof GroupPlace) {
            return new GroupActivity((GroupPlace) place, clientFactory);
        }
        if (place instanceof PricingStructurePlace) {
            return new PricingStructureActivity((PricingStructurePlace) place, clientFactory);
        }
        if (place instanceof AuxChargeSchedulePlace) {
            return new AuxChargeScheduleActivity(clientFactory, ((AuxChargeSchedulePlace) place));
        }
        if (place instanceof AuxTypePlace) {
            return new AuxTypeActivity(clientFactory);
        }
        if (place instanceof SupplyGroupPlace) {
            return new SupplyGroupActivity(clientFactory);
        }
        if (place instanceof DisplayTokensPlace) {
            return new DisplayTokensActivity(clientFactory);
        }
        if (place instanceof DeviceStorePlace) {
            return new DeviceStoreActivity(clientFactory);
        }
		if (place instanceof BlockingTypePlace) {
			return new BlockingTypeActivity(clientFactory, ((BlockingTypePlace) place));
		}
        if (place instanceof UserGroupPlace) {
            return new UserGroupActivity((UserGroupPlace) place, clientFactory);
        }
        if (place instanceof SearchPlace) {
            return new SearchActivity((SearchPlace) place, clientFactory);
        }
        if (place instanceof MeterReadingsPlace) {
            return new MeterReadingsActivity((MeterReadingsPlace) place, clientFactory);
        }
        if (place instanceof CalendarPlace) {
            return new CalendarActivity((CalendarPlace) place, clientFactory);
        }
        if (place instanceof EnergyBalancingAlertPlace) {
            return new EnergyBalancingAlertActivity((EnergyBalancingAlertPlace) place, clientFactory);
        }
        if (place instanceof EnergyBalancingMeterPlace) {
            return new EnergyBalancingMeterActivity((EnergyBalancingMeterPlace) place, clientFactory);
        }
        if (place instanceof ManufacturerPlace) {
            return new ManufacturerActivity((ManufacturerPlace) place, clientFactory);
        }
        if (place instanceof MeterModelPlace) {
            return new MeterModelActivity((MeterModelPlace) place, clientFactory);
        }
        if (place instanceof MdcPlace) {
            return new MdcActivity((MdcPlace) place, clientFactory);
        }
        if (place instanceof AddMeterReadingsPlace) {
            return new AddMeterReadingsActivity((AddMeterReadingsPlace) place, clientFactory);
        }
        if (place instanceof TaskSchedulePlace) {
            return new TaskScheduleActivity((TaskSchedulePlace) place, clientFactory);
        }
        if (place instanceof ChangePasswordPlace) {
            return new ChangePasswordActivity((ChangePasswordPlace) place, clientFactory);
        }
        if (place instanceof SelectAccessGroupPlace) {
            return new SelectAccessGroupActivity(clientFactory);
        }
        if (place instanceof DashboardPlace) {
            return new DashboardActivity(clientFactory);
        }
        if (place instanceof AdminDashboardPlace) {
            return new AdminDashboardActivity(clientFactory);
        }
        if (place instanceof AppSettingPlace) {
            return new AppSettingActivity((AppSettingPlace) place, clientFactory);
        }
        if (place instanceof CustomerTransUploadPlace) {
            return new CustomerTransUploadActivity((CustomerTransUploadPlace) place, clientFactory);
        }
        if (place instanceof AuxTransUploadPlace) {
            return new AuxTransUploadActivity((AuxTransUploadPlace) place, clientFactory);
        }
        if (place instanceof GlobalNdpPlace) {
            return new GlobalNdpActivity((GlobalNdpPlace) place, clientFactory);
        }
        if (place instanceof BillingDetPlace) {
            return new BillingDetActivity((BillingDetPlace) place, clientFactory);
        }
        if (place instanceof MeterCustUPUploadPlace) {
            return new MeterCustUPUploadActivity((MeterCustUPUploadPlace) place, clientFactory);
        }
        if (place instanceof MeterBulkUploadPlace) {
            return new MeterBulkUploadActivity((MeterBulkUploadPlace) place, clientFactory);
        }
        if (place instanceof SpecialActionsPlace) {
            return new SpecialActionsActivity((SpecialActionsPlace) place, clientFactory);
        }
        if (place instanceof AuxAccountUploadPlace) {
            return new AuxAccountUploadActivity((AuxAccountUploadPlace) place, clientFactory);
        }
        if (place instanceof MeterOnlineBulkPlace) {
            return new MeterOnlineBulkActivity((MeterOnlineBulkPlace) place, clientFactory);
        }
        if (place instanceof ImportFilePlace) {
            return new ImportFileActivity((ImportFilePlace) place, clientFactory);
        }
        if (place instanceof MetadataUploadPlace) {
            return new MetadataUploadActivity((MetadataUploadPlace) place, clientFactory);
        }
        if (place instanceof UserInterfacePlace) {
            return new UserInterfaceActivity((UserInterfacePlace) place, clientFactory);
        }
        logger.info("Unknown Activity for place: " + place.getClass().getName());
        return null;
    }
}
