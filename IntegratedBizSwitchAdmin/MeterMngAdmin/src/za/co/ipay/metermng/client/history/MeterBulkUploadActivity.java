package za.co.ipay.metermng.client.history;

import com.google.gwt.activity.shared.AbstractActivity;
import com.google.gwt.event.shared.EventBus;
import com.google.gwt.user.client.ui.AcceptsOneWidget;

import za.co.ipay.metermng.client.event.MeterBulkUploadEvent;
import za.co.ipay.metermng.client.factory.ClientFactory;

public class MeterBulkUploadActivity extends AbstractActivity {
	
	private ClientFactory clientFactory;
	private MeterBulkUploadPlace meterBulkUploadPlace;

	public MeterBulkUploadActivity(MeterBulkUploadPlace meterBulkUploadPlace, ClientFactory clientFactory) {
		super();
		this.clientFactory = clientFactory;
		this.meterBulkUploadPlace = meterBulkUploadPlace;
	}

	@Override
	public void start(AcceptsOneWidget panel, EventBus eventBus) {
		clientFactory.getEventBus().fireEvent(new MeterBulkUploadEvent(meterBulkUploadPlace.getName()));
	}

}
