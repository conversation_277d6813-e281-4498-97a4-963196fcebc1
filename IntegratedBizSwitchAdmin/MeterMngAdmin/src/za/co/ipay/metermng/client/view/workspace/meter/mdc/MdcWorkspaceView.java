package za.co.ipay.metermng.client.view.workspace.meter.mdc;

import java.util.logging.Logger;

import com.google.gwt.core.client.GWT;
import com.google.gwt.place.shared.Place;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.user.client.ui.DeckLayoutPanel;
import com.google.gwt.user.client.ui.Widget;
import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.gwt.common.client.workspace.SessionCheckCallback;
import za.co.ipay.gwt.common.client.workspace.SimpleTableView;
import za.co.ipay.gwt.common.client.workspace.WorkspaceNotification;
import za.co.ipay.gwt.common.client.workspace.WorkspaceNotification.NotificationType;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.history.MdcPlace;
import za.co.ipay.metermng.client.view.workspace.BaseWorkspace;
import za.co.ipay.metermng.mybatis.generated.model.Mdc;
import za.co.ipay.metermng.shared.MeterMngStatics;
import za.co.ipay.metermng.shared.dto.meter.MdcChannelDto;

public class MdcWorkspaceView extends BaseWorkspace {

    @UiField DeckLayoutPanel mainLayoutPanel;
    @UiField SimpleTableView<Mdc> view;
    private MdcView mdcView;

    @UiField SimpleTableView<MdcChannelDto> view2;
    private MdcChannelView mdcChannelView;

    private static Logger logger = Logger.getLogger(MdcWorkspaceView.class.getName());

    private static MdcWorkspaceViewUiBinder uiBinder = GWT.create(MdcWorkspaceViewUiBinder.class);

    interface MdcWorkspaceViewUiBinder extends UiBinder<Widget, MdcWorkspaceView> {
    }

    public MdcWorkspaceView(ClientFactory clientFactory, MdcPlace place) {
        this.clientFactory = clientFactory;
        initWidget(uiBinder.createAndBindUi(this));
        setPlaceString(MdcPlace.getPlaceAsString(place));
        setHeaderText(MessagesUtil.getInstance().getMessage("meter.mdc"));
        initUi();
    }

    private void initUi() {
        mdcView = new MdcView(clientFactory, this, view);
        mdcChannelView = new MdcChannelView(clientFactory, this, view2);
        mainLayoutPanel.showWidget(view);
    }

    @Override
    public void onArrival(Place place) {
        logger.info("Arrived at Mdc...");
        mdcView.onArrival(place);
    }

    public void showMdcChannels(Mdc mdc) {
        mdcChannelView.setMdc(mdc);
        mainLayoutPanel.showWidget(view2);
        mainLayoutPanel.animate(getAnimationTime());
    }

    public void showMdc() {
        mainLayoutPanel.showWidget(view);
        mainLayoutPanel.animate(getAnimationTime());
    }

    @Override
    public void onLeaving() {

    }

    @Override
    public void onSelect() {

    }

    @Override
    public void onClose() {

    }

    @Override
    public boolean handles(Place place) {
        return (place instanceof MdcPlace);
    }

    @Override
    public void handleNotification(final WorkspaceNotification notification) {
        logger.info("Received notification: "+notification);
        if (NotificationType.DATA_UPDATED == notification.getNotificationType()) {
            SessionCheckCallback sessionCheckCallback = new SessionCheckCallback() {
                @Override
                public void callback(SessionCheckResolution resolution) {
                    if (notification.getDataType().equals("billingDet")){
                        mdcChannelView.panel.populateBillingDetListBox();
                    } else if (notification.getDataType().equals(MeterMngStatics.MODEL_CHANNEL_CONFIG_MODIFIED)){
                        mdcChannelView.clear();
                        mdcChannelView.loadChannels();
                    }
                }
            };
            clientFactory.handleSessionCheckCallback(sessionCheckCallback);
        }
    }
}
