package za.co.ipay.metermng.client.event;

import com.google.gwt.event.shared.GwtEvent;

public class GlobalNdpEvent extends GwtEvent<GlobalNdpEventHandler> {

    public static Type<GlobalNdpEventHandler> TYPE = new Type<GlobalNdpEventHandler>();

    private String name;

    public GlobalNdpEvent(String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }

    @Override
    public Type<GlobalNdpEventHandler> getAssociatedType() {
        return TYPE;
    }

    @Override
    protected void dispatch(GlobalNdpEventHandler handler) {
        handler.handleEvent(this);
    }
}
