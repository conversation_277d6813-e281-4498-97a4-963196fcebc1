package za.co.ipay.metermng.client.widget.suggestboxtree;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

import com.google.gwt.dom.client.Style.Unit;
import com.google.gwt.event.dom.client.ClickEvent;
import com.google.gwt.event.dom.client.ClickHandler;
import com.google.gwt.event.dom.client.FocusEvent;
import com.google.gwt.event.dom.client.FocusHandler;
import com.google.gwt.event.dom.client.KeyCodes;
import com.google.gwt.event.dom.client.KeyUpEvent;
import com.google.gwt.event.dom.client.KeyUpHandler;
import com.google.gwt.event.logical.shared.SelectionEvent;
import com.google.gwt.event.logical.shared.SelectionHandler;
import com.google.gwt.event.logical.shared.ValueChangeEvent;
import com.google.gwt.event.logical.shared.ValueChangeHandler;
import com.google.gwt.i18n.client.LocalizableResource.DefaultLocale;
import com.google.gwt.i18n.client.Messages;
import com.google.gwt.user.client.ui.Button;
import com.google.gwt.user.client.ui.Composite;
import com.google.gwt.user.client.ui.FlowPanel;
import com.google.gwt.user.client.ui.Label;
import com.google.gwt.user.client.ui.SuggestBox.DefaultSuggestionDisplay;
import com.google.gwt.user.client.ui.SuggestOracle;
import com.google.gwt.user.client.ui.SuggestOracle.Suggestion;
import com.google.gwt.user.client.ui.Widget;

import za.co.ipay.gwt.common.client.form.FormElement;
import za.co.ipay.gwt.common.client.form.FormRowPanel;
import za.co.ipay.gwt.common.client.form.HasDirtyData;
import za.co.ipay.gwt.common.client.handler.FormDataValueChangeHandler;
import za.co.ipay.gwt.common.client.widgets.Message;
import za.co.ipay.metermng.client.widget.suggestboxtree.SuggestionOracleFactory.IdentifiableSuggestOracleCallback;

/**
 * This widget is used to select data from a hierarchy using SuggestBox widgets rather than 
 * drop down lists for performance.
 * 
 * A SuggestionOracleFactory is used to create the oracles for each widget when needed. When 
 * the first suggest box is created for the top level nodes this widget will call 
 * createInitialOracle on the factory. This can then retrieve data as any normal 
 * SuggestionOracle would except that all oracles need to implement extra methods by extending 
 * IdentifiableSuggestOracle. This provides for example the label for the specific level of 
 * the hierarchy and also provides a way to find specific suggestions in order to preload 
 * existing data (also refer to SettableSuggestBox for this).
 * 
 * Once a selection is made on the top level, a call is made to the factory to get the next 
 * level based on the chosen Suggestion, so createNextOracle is called on each subsequent level.
 * 
 * 
 * <AUTHOR>
 * @param <R> the type of id of the suggestion eg. long or a string
 * @param <S> a sub type of Suggestion
 * @param <T> a sub type of TreeInfo giving information around the tree like its label
 */
public class SuggestBoxTree<R, S extends Suggestion, T extends SuggestBoxTree.TreeInfo> extends Composite {
    private FlowPanel mainPanel;
    private FlowPanel formPanel;
    private FlowPanel suggestBoxesPanel;
    private ArrayList<ContainerWithSuggestBox> suggestBoxes = new ArrayList<>();
    private SuggestionOracleFactory<R> oracleFactory;
    private Suggestion currentSelectedItem;
    private Label captionLabel;
    private T treeInfo;
    private List<R> initialData;
    private boolean initialDataInProgress = false;
    private Message errorMessage;
    private HasDirtyData hasDirty;
    private SuggestBoxTreeMessages messages;
    private boolean addClearBtn;
    private int suggestionsDisplaySize;

    public SuggestBoxTree(SuggestionOracleFactory<R> oracleFactory, HasDirtyData hasDirty, T treeInfo,
            SuggestBoxTreeMessages messages, boolean addClearBtn, int suggestionsDisplaySize) {
        super();
        this.oracleFactory = oracleFactory;
        this.treeInfo = treeInfo;
        this.hasDirty = hasDirty;
        this.messages = messages;
        this.addClearBtn = addClearBtn;
        this.suggestionsDisplaySize = suggestionsDisplaySize;
        mainPanel = new FlowPanel();
        initWidget(mainPanel);
        init();
    }

    protected void init() {
        //TODO create custom styles
        formPanel = new FlowPanel();
        formPanel.setStyleName("pseudoCaptionPanel");
        captionLabel = new Label(treeInfo.getCaptionLabel());
        captionLabel.setStyleName("pseudoCaptionLabel");
        formPanel.add(captionLabel);
        suggestBoxesPanel = new FlowPanel();
        formPanel.add(suggestBoxesPanel);
        Widget bottomPanel = createBottomFormPanel();
        if(bottomPanel != null) {
            formPanel.add(bottomPanel);
        }
        mainPanel.add(formPanel);
        errorMessage = new Message();
        errorMessage.ensureDebugId("selectionDataError");
        mainPanel.add(errorMessage);
        errorMessage.setVisible(false);
        createInitialOracle();
    }

    private void createInitialOracle() {
        oracleFactory.createInitialOracle(new IdentifiableSuggestOracleCallback<R>() {

            @Override
            public void callback(IdentifiableSuggestOracle<R> suggestOracle) {
                createNextLevel(suggestOracle, true);
            }
        });
    }

    private void clearLowerLevels(Widget suggestBoxContainer) {
        Iterator<ContainerWithSuggestBox> iterator = suggestBoxes.iterator();
        boolean found = false;
        ContainerWithSuggestBox cws;
        do {
            // go through all the previous suggest box containers if there are any
            cws = iterator.next();
            if (found) {
                cws.container.removeFromParent();
                iterator.remove();
            } else if (suggestBoxContainer.equals(cws.container)) {
                found = true;
            }
        } while (iterator.hasNext());
    }

    private void createNextLevel(final IdentifiableSuggestOracle<R> suggestOracle, boolean firstLevel) {
        final SettableSuggestBox<R> suggestBox = createSuggestBox(suggestOracle, firstLevel);
        final Widget container = createSuggestBoxContainer(suggestBox, suggestOracle.getLabel(), treeInfo.isRequired());
        suggestBoxesPanel.add(container);
        ContainerWithSuggestBox cws = new ContainerWithSuggestBox(container, suggestBox);
        suggestBoxes.add(cws);
        suggestBox.ensureDebugId("selectionDataBox" + captionLabel.getText().replaceAll(" ", "").replaceAll(":", "")
                + suggestBoxes.size());
        suggestBox.addSelectionHandler(new SelectionHandler<SuggestOracle.Suggestion>() {
            @Override
            public void onSelection(SelectionEvent<Suggestion> event) {
                Suggestion selectedItem = event.getSelectedItem();
                if (suggestOracle.isEmptyItem(selectedItem)) {
                    return;
                }
                clearError();
                currentSelectedItem = selectedItem;
                oracleFactory.createNextOracle(currentSelectedItem, new IdentifiableSuggestOracleCallback<R>() {
                    @Override
                    public void callback(IdentifiableSuggestOracle<R> oracle) {
                        if (oracle == null) {
                            // no further levels
                            return;
                        }
                        clearLowerLevels(container);
                        createNextLevel(oracle, false);
                    }
                });
            }
        });
        if (initialDataInProgress) {
            // Current depth can be determined by the size list of suggest
            // boxes already created
            int depth = suggestBoxes.size();
            if (initialData.size() > depth) {
                // we still have more to go
                suggestBox.setById(initialData.get(depth - 1), true);
            } else if (initialData.size() == depth) {
                // we are at the last node
                suggestBox.setById(initialData.get(depth - 1), true);
                initialDataInProgress = false;
                initialData = null;
            } else {
                // we don't care about lower nodes
                initialDataInProgress = false;
                initialData = null;
            }
        } else {
            if(firstLevel) {
                activatePlaceholder(suggestBox);
            }
        }
    }

    private void activatePlaceholder(final SettableSuggestBox<R> suggestBox) {
        suggestBox.getElement().setAttribute("placeHolder", messages.placeholderText());
    }
    
    private Widget findSuggestBoxContainer(SettableSuggestBox<R> suggestBox) {
        Widget container = null;
        for (ContainerWithSuggestBox cws : suggestBoxes) {
            container = cws.container;
            if (cws.suggestBox == suggestBox) {
                break;
            }
        }
        return container;
    }

    /**
     * Clear the data in the widget which leaves only the top level node
     * suggest box without any selection.
     */
    public void clear() {
        clearError();
        currentSelectedItem = null;
        if (!suggestBoxes.isEmpty()) {
            ContainerWithSuggestBox cws = suggestBoxes.get(0);
            clearLowerLevels(cws.container);
            cws.suggestBox.setLastSelectedValue(null);
            cws.suggestBox.setValue(null);
        }
    }

    protected void clear(SettableSuggestBox<R> suggestBox) {
        Widget container = findSuggestBoxContainer(suggestBox);
        clearLowerLevels(container);
        suggestBox.setLastSelectedValue(null);
        suggestBox.setValue("");
        currentSelectedItem = null;
    }

    /**
     * This will reload the widget by clearing existing data and 
     * recreating the initial oracle.
     * 
     * This could be triggered by another process that might 
     * have changed the underlying hierarchical data and now 
     * needs to refresh the data.
     * 
     */
    public void reload() {
        clearError();
        currentSelectedItem = null;
        for (ContainerWithSuggestBox cws : suggestBoxes) {
            cws.container.removeFromParent();
        }
        suggestBoxes.clear();
        createInitialOracle();
    }

    /**
     * This will reload the widget but with a set of initial 
     * data. The list specifies the id's of the suggestions to 
     * select in order starting with the root node at 0. This 
     * could cause the initial oracle to need to retrieve 
     * data from the server depending on the type of oracle.
     * 
     * This could be triggered by another process that might 
     * have changed the underlying hierarchical data and now 
     * needs to refresh the data.
     *
     * If the underlying data has not changed have a look at 
     * setValue rather.
     * 
     * @param initialData the ids for the items to pre-select
     */
    public void reload(List<R> initialData) {
        this.initialData = initialData;
        initialDataInProgress = true;
        reload();
    }

    /**
     * This will load the widget but with a set of initial 
     * data. The list specifies the id's of the suggestions to 
     * select in order starting with the root node at 0. It 
     * does not recreate the initial oracle so it preserves 
     * data that might already be locally cached if needed.
     * 
     * If the underlying data has changed have a look at 
     * reload rather.
     * 
     * @param initialData
     */
    public void setValue(List<R> initialData) {
        this.initialData = initialData;
        clearError();
        initialDataInProgress = true;
        // The idea is to set initial data, then set the
        // root box's value, then when each new lower level
        // is created it uses this initialData object to set
        // the value for each level as it is being created.
        // This is a bit async, but it is by nature as each suggestbox might
        // do an async call to populate its data. The
        if (!suggestBoxes.isEmpty() && initialData != null && !initialData.isEmpty()) {
            suggestBoxes.get(0).suggestBox.setById(initialData.get(0), true);
        } else {
            throw new IllegalStateException("This widget is not initilized yet, can't set inital data");
        }
    }

    /**
     * This gets the current lowest level selected Suggestion.
     * 
     * @return
     */
    @SuppressWarnings("unchecked")
    public S getCurrentSelectedItem() {
        if (treeInfo.mustSelectLastLevel()) {
            if (suggestBoxes.isEmpty()) {
                return null;
            }
            SettableSuggestBox<R> latestSuggestBox = suggestBoxes.get(suggestBoxes.size() - 1).suggestBox;
            Suggestion lastSelectedValue = latestSuggestBox.getLastSelectedValue();
            return (S) lastSelectedValue;
        }
        return (S) currentSelectedItem;
    }

    /**
     * Set an error message on this widget.
     * 
     * @param error
     */
    public void setError(String error) {
        errorMessage.setText(error);
        errorMessage.setType(Message.MESSAGE_TYPE_ERROR);
        errorMessage.setVisible(true);
    }

    /**
     * Clear the error message set on this widget.
     */
    public void clearError() {
        errorMessage.setText("");
        errorMessage.setVisible(false);
    }

    /**
     * Gets the factory this widget was initialized with.
     *  
     * @return
     */
    public SuggestionOracleFactory<R> getFactory() {
        return oracleFactory;
    }

    /**
     * Gets the tree info this widget was created with.
     * 
     * @return
     */
    public T getTreeInfo() {
        return treeInfo;
    }

    /**
     * Provides information about the tree structure represented by this widget.
     * 
     * <AUTHOR>
     *
     */
    public interface TreeInfo {
        String getCaptionLabel();

        boolean isRequired();

        boolean mustSelectLastLevel();
    }

    /**
     * Provides text for specific parts of the tree like the caption and clear button.
     * 
     * <AUTHOR>
     */
    @DefaultLocale("en_ZA")
    public interface SuggestBoxTreeMessages extends Messages {
        @DefaultMessage("Tree Data:")
        String captionLabel();

        @DefaultMessage("Clear")
        String clear();

        @DefaultMessage("Type to search...")
        String placeholderText();
    }
    
    /**
     * Creates a SettableSuggestBox for a specific level in the hierarchy.
     * 
     * @param oracle
     * @param firstLevel if true then this represents the oracle created from createInitialOracle on the factory
     * @return
     */
    protected SettableSuggestBox<R> createSuggestBox(IdentifiableSuggestOracle<R> oracle, final boolean firstLevel) {
        final SettableSuggestBox<R> suggestBox = new SettableSuggestBox<R>(oracle);
        suggestBox.setLimit(suggestionsDisplaySize);
        if (firstLevel) {
            if (hasDirty != null) {
                suggestBox.addValueChangeHandler(new FormDataValueChangeHandler<String>(hasDirty));
            }
        }
        return suggestBox;
    }

    /**
     * Create the container for the SettableSuggestBox. This may for example be overridden by subclasses to 
     * add additional buttons next to the suggest box that provide additional actions based on the current 
     * selection.
     * 
     * @param suggestBox
     * @param label
     * @param required
     * @return
     */
    protected Widget createSuggestBoxContainer(final SettableSuggestBox<R> suggestBox, String label, boolean required) {
        FormRowPanel formRowPanel = new FormRowPanel();
        FormElement formElement = new FormElement();
        formElement.add(suggestBox);
        formElement.setLabelText(label);
        formElement.setRequired(required);
        formRowPanel.add(formElement);
        return formRowPanel;
    }

    /**
     * The bottom form panel is by default used for the clear button, but subclasses may override this 
     * behaviour, if for example clear should not be needed or is implemented in a different way.
     * 
     * @return
     */
    protected Widget createBottomFormPanel() {
        if (! addClearBtn) {
            return null;
        }
        FormRowPanel formRowPanel = new FormRowPanel();
        Button clearBtn = new Button(messages.clear());
        clearBtn.getElement().getStyle().setFontSize(0.8, Unit.EM);
        clearBtn.addClickHandler(new ClickHandler() {
            @Override
            public void onClick(ClickEvent event) {
                clear();
            }
        });
        formRowPanel.add(clearBtn);
        return formRowPanel;
    }

    private void showSuggestionsIfNotShowingCurrently(final SettableSuggestBox<R> suggestBox) {
        DefaultSuggestionDisplay display = (DefaultSuggestionDisplay) suggestBox.getSuggestionDisplay();
        if (!display.isSuggestionListShowing()) {
            suggestBox.showSuggestionList();
        }
    }

    private void hideSuggestionsIfShowingCurrently(final SettableSuggestBox<R> suggestBox) {
        DefaultSuggestionDisplay display = (DefaultSuggestionDisplay) suggestBox.getSuggestionDisplay();
        if (display.isSuggestionListShowing()) {
            display.hideSuggestions();
        }
    }

    private class ContainerWithSuggestBox {
        Widget container;
        SettableSuggestBox<R> suggestBox;

        public ContainerWithSuggestBox(Widget container, SettableSuggestBox<R> suggestBox) {
            super();
            this.container = container;
            this.suggestBox = suggestBox;
        }
    }

}
