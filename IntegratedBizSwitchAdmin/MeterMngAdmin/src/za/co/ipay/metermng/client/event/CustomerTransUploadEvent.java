package za.co.ipay.metermng.client.event;

import com.google.gwt.event.shared.GwtEvent;

public class CustomerTransUploadEvent extends GwtEvent<CustomerTransUploadEventHandler> {
    
    public static Type<CustomerTransUploadEventHandler> TYPE = new Type<CustomerTransUploadEventHandler>();
    
    private String name;
    
    public CustomerTransUploadEvent(String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }

    @Override
    public Type<CustomerTransUploadEventHandler> getAssociatedType() {
        return TYPE;
    }

    @Override
    protected void dispatch(CustomerTransUploadEventHandler handler) {
        handler.handleEvent(this);
    }
}
