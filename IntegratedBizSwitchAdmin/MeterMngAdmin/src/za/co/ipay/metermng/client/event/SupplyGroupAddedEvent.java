package za.co.ipay.metermng.client.event;

import com.google.gwt.event.shared.GwtEvent;

public class SupplyGroupAddedEvent extends GwtEvent<SupplyGroupAddedEventHandler> {

    public static Type<SupplyGroupAddedEventHandler> TYPE = new Type<SupplyGroupAddedEventHandler>();
    
    
    public SupplyGroupAddedEvent() {
    }
    
	@Override
    public Type<SupplyGroupAddedEventHandler> getAssociatedType() {
        return TYPE;
    }

    @Override
    protected void dispatch(SupplyGroupAddedEventHandler handler) {
        handler.processSupplyGroupAddedEvent(this);
    }


}
