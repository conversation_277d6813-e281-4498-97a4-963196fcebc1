package za.co.ipay.metermng.client.view.workspace.schedule;

import java.util.ArrayList;
import java.util.logging.Logger;

import za.co.ipay.gwt.common.client.Dialogs;
import za.co.ipay.gwt.common.client.form.FormManager;
import za.co.ipay.gwt.common.client.handler.ConfirmHandler;
import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.gwt.common.client.validation.ClientValidatorUtil;
import za.co.ipay.gwt.common.client.workspace.SessionCheckCallback;
import za.co.ipay.gwt.common.client.workspace.SimpleTableView;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.resource.image.MediaResource.MediaResourceUtil;
import za.co.ipay.metermng.client.rpc.callback.ClientCallback;
import za.co.ipay.metermng.client.view.component.BaseComponent;
import za.co.ipay.metermng.client.view.workspace.schedule.cron.CronExpressionParser;
import za.co.ipay.metermng.client.view.workspace.schedule.cron.DailySchedulePanel;
import za.co.ipay.metermng.client.view.workspace.schedule.cron.MonthlySchedulePanel;
import za.co.ipay.metermng.client.view.workspace.schedule.cron.RepeatedlySchedulePanel;
import za.co.ipay.metermng.client.view.workspace.schedule.cron.ScheduleUi;
import za.co.ipay.metermng.client.view.workspace.schedule.cron.WeeklySchedulePanel;
import za.co.ipay.metermng.client.widget.StatusTableColumn;
import za.co.ipay.metermng.datatypes.RecordStatus;
import za.co.ipay.metermng.mybatis.generated.model.TaskSchedule;
import za.co.ipay.metermng.shared.MeterMngStatics;

import com.google.gwt.event.dom.client.ChangeEvent;
import com.google.gwt.event.dom.client.ChangeHandler;
import com.google.gwt.event.dom.client.ClickEvent;
import com.google.gwt.event.dom.client.ClickHandler;
import com.google.gwt.user.cellview.client.Column;
import com.google.gwt.user.cellview.client.ColumnSortEvent;
import com.google.gwt.user.cellview.client.ColumnSortEvent.AsyncHandler;
import com.google.gwt.user.cellview.client.ColumnSortList;
import com.google.gwt.user.cellview.client.TextColumn;
import com.google.gwt.user.client.ui.Button;
import com.google.gwt.view.client.AsyncDataProvider;
import com.google.gwt.view.client.HasData;
import com.google.gwt.view.client.Range;

public class TaskScheduleView extends BaseComponent implements FormManager<TaskSchedule> {

    private TaskScheduleWorkspaceView parentWorkspace;
    private SimpleTableView<TaskSchedule> view;
    private TaskSchedulePanel panel;
    private AsyncDataProvider<TaskSchedule> dataProvider;
    private TaskSchedule taskSchedule;
    private Button showTasksBtn;

    private static Logger logger = Logger.getLogger(TaskScheduleView.class.getName());

    public TaskScheduleView(TaskScheduleWorkspaceView parentWorkspace,
            ClientFactory clientFactory,
            SimpleTableView<TaskSchedule> view) {
        this.parentWorkspace = parentWorkspace;
        this.clientFactory = clientFactory;
        this.view = view;
        initUi();
    }

    private void initUi() {
        initView();
        initForm();
        createTable();
        loadInitData();
    }

    private void initView() {
        view.setFormManager(this);
    }

    private void initForm() {
        panel = new TaskSchedulePanel(view.getForm(), clientFactory);
        panel.clearFields();

        view.getForm().setHasDirtyDataManager(parentWorkspace);
        view.getForm().getFormFields().add(panel);

        view.getForm().getSaveBtn().setText(MessagesUtil.getInstance().getMessage("button.create"));
        view.getForm().getSaveBtn().addClickHandler(new ClickHandler() {
            @Override
            public void onClick(ClickEvent event) {
                onSave();
            }
        });

        view.getForm().getOtherBtn().setText(MessagesUtil.getInstance().getMessage("button.cancel"));
        view.getForm().getOtherBtn().addClickHandler(new ClickHandler() {
            @Override
            public void onClick(ClickEvent event) {
                view.getForm().checkDirtyData(new ConfirmHandler() {
                    @Override
                    public void confirmed(boolean confirm) {
                        if (confirm) {
                            view.getForm().setDirtyData(false);
                            displaySelected(null);
                        }
                    }
                });
            }
        });

        view.getForm().getFormPanel().setHeadingText(MessagesUtil.getInstance().getMessage("taskschedule.title.add"));

        panel.scheduleBox.addChangeHandler(new ChangeHandler() {
            @Override
            public void onChange(ChangeEvent event) {
                displaySchedulePanel();
            }
        });

        showTasksBtn = new Button(MessagesUtil.getInstance().getMessage("button.view.scheduledtasks"));
        showTasksBtn.addClickHandler(new ClickHandler() {
            @Override
            public void onClick(ClickEvent event) {
                view.getForm().checkDirtyData(new ConfirmHandler() {
                    @Override
                    public void confirmed(boolean confirm) {
                        if (confirm) {
                            if (view.getForm().isDirtyData()) {
                                view.getForm().setDirtyData(false);
                                displayTaskSchedule(taskSchedule);
                            }
                            parentWorkspace.goToScheduledTasks(taskSchedule);
                        }
                    }
                });
            }
        });
        showTasksBtn.setVisible(false);
        view.getForm().getSecondaryButtons().add(showTasksBtn);
        view.getForm().getSecondaryButtons().setVisible(true);
    }

    private void displaySchedulePanel() {
        panel.clearSchedulePanel();
        int index = panel.scheduleBox.getSelectedIndex();
        if (index > 0) {
            String value = panel.scheduleBox.getValue(index);
            ScheduleUi scheduleUi = null;
            if (MeterMngStatics.DAILY_SCHEDULE_VALUE.equals(value)) {
                scheduleUi = new DailySchedulePanel(view.getForm());
            } else if (MeterMngStatics.WEEKLY_SCHEDULE_VALUE.equals(value)) {
                scheduleUi = new WeeklySchedulePanel(view.getForm());
            } else if (MeterMngStatics.MONTHLY_SCHEDULE_VALUE.equals(value)) {
                scheduleUi = new MonthlySchedulePanel(view.getForm());
            } else if (MeterMngStatics.REPEATEDLY_SCHEDULE_VALUE.equals(value)) {
                scheduleUi = new RepeatedlySchedulePanel(view.getForm());
            }
            if (scheduleUi != null) {
                panel.schedulePanel.add(scheduleUi);
                if (taskSchedule != null) {
                    scheduleUi.setCronExpression(taskSchedule.getCronExpression());
                }
            } else {
                logger.severe("Unknown ScheduleUi for value:"+value);
            }
        }
    }

    private void createTable() {
        if (dataProvider == null) {
            Column<TaskSchedule, ?> nameColumn = createNameColumn();
            StatusTableColumn<TaskSchedule> statusColumn = new StatusTableColumn<TaskSchedule>();
            statusColumn.setSortable(true);

            view.getTable().addColumn(nameColumn, MessagesUtil.getInstance().getMessage("taskschedule.name"));
            view.getTable().addColumn(statusColumn, MessagesUtil.getInstance().getMessage("taskschedule.status"));

            // Set the range to display
            view.getTable().setVisibleRange(0, getPageSize());

            // Set the data provider for the table
            dataProvider = new AsyncDataProvider<TaskSchedule>() {
                @Override
                protected void onRangeChanged(HasData<TaskSchedule> display) {
                    final int start = display.getVisibleRange().getStart();
                    String sortColumn = null;
                    boolean isAscending = true;
                    ColumnSortList sortList = view.getTable().getColumnSortList();
                    if (sortList != null && sortList.size() != 0) {
                        @SuppressWarnings("unchecked")
                        Column<TaskSchedule, ?> sColumn = (Column<TaskSchedule, ?>) sortList.get(0).getColumn();
                        Integer columnIndex = view.getTable().getColumnIndex(sColumn);
                        sortColumn = getColumnName(columnIndex);
                        isAscending = sortList.get(0).isAscending();
                    }
                    clientFactory.getScheduleRpc().getTaskSchedules(start, getPageSize(), sortColumn, isAscending,
                            new ClientCallback<ArrayList<TaskSchedule>>() {
                                @Override
                                public void onSuccess(ArrayList<TaskSchedule> result) {
                                    logger.info("Got tasks: "+result.size());
                                    dataProvider.updateRowData(start, result);
                                }
                            });
                }
            };
            dataProvider.addDataDisplay(view.getTable());

            // Create the table's pager
            view.getPager().setDisplay(view.getTable());

            // Set the table's column sorter handler
            AsyncHandler columnSortHandler = new AsyncHandler(view.getTable()) {
                @Override
                public void onColumnSort(ColumnSortEvent event) {
                    final int start = view.getTable().getVisibleRange().getStart();
                    @SuppressWarnings("unchecked")
                    int sortIndex = view.getTable().getColumnIndex((Column<TaskSchedule, ?>) event.getColumn());
                    String sortColumn = getColumnName(sortIndex);
                    boolean isAscending = event.isSortAscending();
                    clientFactory.getScheduleRpc().getTaskSchedules(start, getPageSize(), sortColumn, isAscending,
                            new ClientCallback<ArrayList<TaskSchedule>>() {
                                public void onSuccess(ArrayList<TaskSchedule> result) {
                                    logger.info("Got sorted tasks: "+result.size());
                                    dataProvider.updateRowData(start, result);
                                }
                            });
                }
            };
            view.getTable().addColumnSortHandler(columnSortHandler);
            view.getTable().getColumnSortList().push(nameColumn);
        }
    }

    private String getColumnName(int index) {
        logger.info("Sort column index: "+index);
        if (index == 0) {
            return "name";
        } else if (index == 1) {
            return "status";
        } else {
            return "task";
        }
    }

    private Column<TaskSchedule, ?> createNameColumn() {
        TextColumn<TaskSchedule> nameColumn = new TextColumn<TaskSchedule>() {
            @Override
            public String getValue(TaskSchedule object) {
                return object.getTaskScheduleName();
            }
        };
        nameColumn.setSortable(true);
        return nameColumn;
    }

    private void loadInitData() {
        panel.scheduleBox.clear();
        panel.scheduleBox.addItem("", "");
        panel.scheduleBox.addItem(MessagesUtil.getInstance().getMessage("taskschedule.schedule.daily"), MeterMngStatics.DAILY_SCHEDULE_VALUE);
        panel.scheduleBox.addItem(MessagesUtil.getInstance().getMessage("taskschedule.schedule.weekly"), MeterMngStatics.WEEKLY_SCHEDULE_VALUE);
        panel.scheduleBox.addItem(MessagesUtil.getInstance().getMessage("taskschedule.schedule.monthly"), MeterMngStatics.MONTHLY_SCHEDULE_VALUE);
        panel.scheduleBox.addItem(MessagesUtil.getInstance().getMessage("taskschedule.schedule.repeatedly"), MeterMngStatics.REPEATEDLY_SCHEDULE_VALUE);
    }

    @Override
    public void displaySelected(TaskSchedule selected) {
        displayTaskSchedule(selected);
    }

    private void displayTaskSchedule(TaskSchedule selected) {
        clear();
        this.taskSchedule = selected;
        if (taskSchedule == null) {
            taskSchedule = new TaskSchedule();
            view.getForm().getSaveBtn().setText(MessagesUtil.getInstance().getMessage("button.create"));
            view.getForm().getFormPanel().setHeadingText(MessagesUtil.getInstance().getMessage("taskschedule.title.add"));
            view.clearTableSelection();
            showTasksBtn.setVisible(false);
        } else {
            view.getForm().getSaveBtn().setText(MessagesUtil.getInstance().getMessage("button.update"));
            view.getForm().getFormPanel().setHeadingText(MessagesUtil.getInstance().getMessage("taskschedule.title.update"));
            showTasksBtn.setVisible(true);
        }

        panel.nameTextBox.setText(taskSchedule.getTaskScheduleName());
        panel.activeBox.setValue(RecordStatus.ACT.equals(taskSchedule.getRecordStatus()));
        //Schedule
        setCronExpression(taskSchedule.getCronExpression());
    }

    private void clear() {
        taskSchedule = null;
        view.getForm().getFormPanel().setHeadingText(MessagesUtil.getInstance().getMessage("taskschedule.title.add"));
        view.getForm().getSaveBtn().setText(MessagesUtil.getInstance().getMessage("button.create"));
        panel.clearFields();
        panel.clearErrors();
        view.clearTableSelection();
        showTasksBtn.setVisible(false);
    }

    private boolean isValidInput() {
        boolean valid = true;
        panel.clearErrors();

        TaskSchedule dto = updateTaskSchedule();

        if (!ClientValidatorUtil.getInstance().validateField(dto, "taskScheduleName", panel.nameElement)) {
            valid = false;
        }

        if (!ClientValidatorUtil.getInstance().validateField(dto, "recordStatus", panel.activeElement)) {
            valid = false;
        }

        if (panel.scheduleBox.getSelectedIndex() > 0) {
            if (panel.schedulePanel.getWidgetCount() > 0) {
                if (!valid || !((ScheduleUi) panel.schedulePanel.getWidget(0)).isValidInput()) {
                	valid = false;
                }
            }
        } else if (!ClientValidatorUtil.getInstance().validateField(dto, "cronExpression", panel.scheduleElement)) {
            valid = false;
        }

        return valid;
    }

    private TaskSchedule updateTaskSchedule() {
        TaskSchedule m = new TaskSchedule();
        updateTaskSchedule(m);
        return m;
    }

    private void updateTaskSchedule(TaskSchedule dto) {
        if (taskSchedule != null) {
            dto.setId(taskSchedule.getId());
        }
        dto.setTaskScheduleName(panel.nameTextBox.getText());
        dto.setRecordStatus(panel.activeBox.getValue() ? RecordStatus.ACT : RecordStatus.DAC);
        dto.setCronExpression(getCronExpression());
    }

    private void setCronExpression(String cronExp) {
        ScheduleUi ui = null;
        for(int i=0;i<panel.scheduleBox.getItemCount();i++) {
            if (MeterMngStatics.DAILY_SCHEDULE_VALUE.equals(panel.scheduleBox.getValue(i))
                    && CronExpressionParser.isDaily(cronExp)) {
                panel.scheduleBox.setSelectedIndex(i);
                ui = new DailySchedulePanel(view.getForm());
                ui.setCronExpression(cronExp);
                panel.schedulePanel.add(ui);
                return;
            } else if (MeterMngStatics.WEEKLY_SCHEDULE_VALUE.equals(panel.scheduleBox.getValue(i))
                    && CronExpressionParser.isWeekly(cronExp)) {
                panel.scheduleBox.setSelectedIndex(i);
                ui = new WeeklySchedulePanel(view.getForm());
                ui.setCronExpression(cronExp);
                panel.schedulePanel.add(ui);
                return;
            } else if (MeterMngStatics.MONTHLY_SCHEDULE_VALUE.equals(panel.scheduleBox.getValue(i))
                    && CronExpressionParser.isMonthly(cronExp)) {
                panel.scheduleBox.setSelectedIndex(i);
                ui = new MonthlySchedulePanel(view.getForm());
                ui.setCronExpression(cronExp);
                panel.schedulePanel.add(ui);
                return;
            } else if (MeterMngStatics.REPEATEDLY_SCHEDULE_VALUE.equals(panel.scheduleBox.getValue(i))
                    && CronExpressionParser.isRepeatedly(cronExp)) {
                panel.scheduleBox.setSelectedIndex(i);
                ui = new RepeatedlySchedulePanel(view.getForm());
                ui.setCronExpression(cronExp);
                panel.schedulePanel.add(ui);
                return;
            }
        }
    }

    private String getCronExpression() {
        if (panel.schedulePanel.getWidgetCount() > 0) {
            return ((ScheduleUi) panel.schedulePanel.getWidget(0)).getCronExpression();
        } else {
            return null;
        }
    }

    private void onSave() {
        if (isValidInput()) {
            final TaskSchedule dto = updateTaskSchedule();
            final Long id = dto.getId(); // keep track whether the table's total row count is increasing or not
            SessionCheckCallback sessionCheckCallback = new SessionCheckCallback() {
                @Override
                public void callback(SessionCheckResolution resolution) {
                    clientFactory.getScheduleRpc().saveTaskSchedule(dto,
                            new ClientCallback<Void>(view.getForm().getSaveBtn().getAbsoluteLeft(), view.getForm().getSaveBtn().getAbsoluteTop()) {
                                @Override
                                public void onSuccess(Void result) {
                                    view.getForm().setDirtyData(false);
                                    boolean refresh = false;
                                    if (id == null) {
                                        refresh = true;
                                    }
                                    refreshTable(refresh);
                                    Dialogs.displayInformationMessage(
                                            MessagesUtil.getInstance()
                                                    .getSavedMessage(
                                                            new String[] { MessagesUtil.getInstance().getMessage("taskschedule.type") }),
                                            MediaResourceUtil.getInstance().getInformationIcon(),
                                            view.getForm().getSaveBtn().getAbsoluteLeft(),
                                            view.getForm().getSaveBtn().getAbsoluteTop(),
                                            MessagesUtil.getInstance().getMessage("button.close"));
                                    clear();
                                }
                            });
                }
            };
            clientFactory.handleSessionCheckCallback(sessionCheckCallback);
        }
    }

    //Method to force the table to refresh its current page. A new row could of been added or just the data should be
    //reloaded due to other changes like disabled user.
    private void refreshTable(boolean insertedNew) {
        if (insertedNew) {
            view.getTable().setRowCount(view.getTable().getRowCount() + 1, true);
        }
        Range range = view.getTable().getVisibleRange();
        view.getTable().setVisibleRangeAndClearData(range, true);
    }

    protected void getCount(final boolean refreshTable) {
        clientFactory.getScheduleRpc().getTaskScheduleCount(new ClientCallback<Integer>() {
            @Override
            public void onFailureClient() {
                view.getTable().setRowCount(0, true);
                dataProvider.updateRowCount(0, true);
            }
            @Override
            public void onSuccess(Integer result) {
                view.getTable().setRowCount(result, true);
                dataProvider.updateRowCount(result, true);
                // Force a table update - reloads data from the back end
                if (refreshTable) {
                    refreshTable(false);
                }
                logger.info("Set count: "+result);
            }
        });
    }
}
