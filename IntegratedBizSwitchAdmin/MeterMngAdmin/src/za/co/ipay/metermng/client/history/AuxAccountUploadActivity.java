package za.co.ipay.metermng.client.history;

import com.google.gwt.activity.shared.AbstractActivity;
import com.google.gwt.event.shared.EventBus;
import com.google.gwt.user.client.ui.AcceptsOneWidget;

import za.co.ipay.metermng.client.event.AuxAccountUploadEvent;
import za.co.ipay.metermng.client.factory.ClientFactory;

public class AuxAccountUploadActivity extends AbstractActivity {

	private ClientFactory clientFactory;
	private AuxAccountUploadPlace auxAccountUploadPlace;

	public AuxAccountUploadActivity(AuxAccountUploadPlace auxAccountUploadPlace, ClientFactory clientFactory) {
		super();
		this.clientFactory = clientFactory;
		this.auxAccountUploadPlace = auxAccountUploadPlace;
	}

	@Override
	public void start(AcceptsOneWidget panel, EventBus eventBus) {
		clientFactory.getEventBus().fireEvent(new AuxAccountUploadEvent(auxAccountUploadPlace.getName()));
	}

}
