package za.co.ipay.metermng.client.event;

import com.google.gwt.event.shared.GwtEvent;

public class OpenGroupEvent extends GwtEvent<OpenGroupEventHandler> {

    public static Type<OpenGroupEventHandler> TYPE = new Type<OpenGroupEventHandler>();
    private final String grouptype;
    
    public OpenGroupEvent(String grouptype) {
        super();
        this.grouptype = grouptype;
    }

    
    public String getGrouptype() {
        return grouptype;
    }

    @Override
    public Type<OpenGroupEventHandler> getAssociatedType() {
        return TYPE;
    }

    @Override
    protected void dispatch(OpenGroupEventHandler handler) {
        handler.openGroup(this);
    }

}
