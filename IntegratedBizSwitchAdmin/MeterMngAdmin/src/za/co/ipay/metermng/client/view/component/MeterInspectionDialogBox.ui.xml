<!DOCTYPE ui:UiBinder SYSTEM "http://dl.google.com/gwt/DTD/xhtml.ent">
<ui:UiBinder xmlns:ui="urn:ui:com.google.gwt.uibinder"
    xmlns:g="urn:import:com.google.gwt.user.client.ui" 
    xmlns:p3="urn:import:za.co.ipay.metermng.client.view.component"
    xmlns:p2="urn:import:com.google.gwt.user.datepicker.client"
    xmlns:p1="urn:import:za.co.ipay.gwt.common.client.form"
    xmlns:ipay="urn:import:za.co.ipay.gwt.common.client.widgets">
    
    <ui:style>
    </ui:style>

    <ui:with field="msg" type="za.co.ipay.metermng.client.i18n.UiMessages" />

    <g:HTMLPanel>
        <table>
            <tr>
                <td>
                    <g:VerticalPanel horizontalAlignment="ALIGN_CENTER" spacing="10">
                    	<ipay:Message ui:field="feedBack" debugId="feedback"/>
                    	<p3:SpecialActionsReasonComponent ui:field="specialactionreasons"></p3:SpecialActionsReasonComponent>
                         <g:FlowPanel>
                            <p1:FormElement debugId="txtbxComment" ui:field="commentElement" helpMsg="{msg.getUsagePointMeterInspectionCommentHelp}" labelText="{msg.getUsagePointMeterInspectionComment}">
                                <g:TextBox debugId="txtbxComment" ui:field="txtbxComment" styleName="gwt-TextBox" />
                            </p1:FormElement>
                        </g:FlowPanel>
                        <g:FlowPanel>
                            <g:HorizontalPanel spacing="3">
                                <g:Button ui:field="sendBtn" debugId="sendBtn" text="{msg.getSendButton}" />
                                <g:Button ui:field="cancelBtn" debugId="cancelBtn" text="{msg.getCancelButton}" />
                                <g:Button ui:field="closeBtn" debugId="closeBtn" text="{msg.getCloseButton}" visible="false"/>
                            </g:HorizontalPanel>
                        </g:FlowPanel>
                    </g:VerticalPanel>
                </td>
            </tr>
        </table>
    </g:HTMLPanel>
    
</ui:UiBinder> 