<!DOCTYPE ui:UiBinder SYSTEM "http://dl.google.com/gwt/DTD/xhtml.ent">
<ui:UiBinder xmlns:ui="urn:ui:com.google.gwt.uibinder"
	xmlns:g="urn:import:com.google.gwt.user.client.ui" 
	xmlns:c="urn:import:com.google.gwt.user.cellview.client"
	xmlns:p1="urn:import:za.co.ipay.gwt.common.client.widgets">
	
	<ui:style>
	    .marginAbove {
            margin-top:15px;
        }
	
	</ui:style>

	<ui:with field="msg" type="za.co.ipay.metermng.client.i18n.UiMessages" />

	<g:FlowPanel ui:field="flowPanel">
        <g:VerticalPanel ui:field="vendTimeChargesPanel" >
            <g:HorizontalPanel spacing="5">
                <g:Label text="{msg.getUsagePointChargeWriteoffVendHeading}: "></g:Label>
                <g:HTML ui:field="lastVendCyclicChargeDate" debugId="lastVendCyclicChargeDate"/> 
            </g:HorizontalPanel>
 
		    <g:ScrollPanel ui:field="scrollPanel">
			    <c:CellTable ui:field="clltbltransitems" />
		    </g:ScrollPanel>

		    <g:HorizontalPanel spacing="5">
			    <g:Label ui:field="vendTimeTotalLbl" text="{msg.getUsagePointChargeWriteoffVendTotalLbl}: " styleName="gwt-Label-bold"></g:Label>
			    <g:HTML ui:field="vendTotalAmount" debugId="vendTotalAmount" />
		    </g:HorizontalPanel>
        </g:VerticalPanel>
  
        <g:VerticalPanel ui:field="billingTimeChargesPanel" styleName="{style.marginAbove}" visible="false" >
            <g:HorizontalPanel spacing="5">
                <g:Label text="{msg.getUsagePointChargeWriteoffBillingHeading}: " ></g:Label>
                <g:HTML ui:field="lastBillingCyclicChargeDate" debugId="lastBillingCyclicChargeDate"/>
            </g:HorizontalPanel>
            
            <g:ScrollPanel ui:field="scrollPanelBilling">
                <c:CellTable ui:field="clltbltransitemsBilling" />
            </g:ScrollPanel>

            <g:HorizontalPanel spacing="5">
                <g:Label text="{msg.getUsagePointChargeWriteoffBillingTotalLbl}: " styleName="gwt-Label-bold"></g:Label>
                <g:HTML ui:field="billingTotalAmount" debugId="billingTotalAmount" />
            </g:HorizontalPanel>
        </g:VerticalPanel>

        <g:HorizontalPanel ui:field="totalChargesPanel" spacing="15" visible="false">
            <g:Label text="{msg.getUsagePointChargeWriteoffTotalBothLbl}: "></g:Label>
            <g:HTML ui:field="totalChargesAmount" debugId="totalChargesAmount" />
        </g:HorizontalPanel>
        
        <g:HorizontalPanel ui:field="writeoffChargeReasonPanel"></g:HorizontalPanel>

        <g:HorizontalPanel ui:field="completedMessage" spacing="5" visible="false">
            <g:Label text="{msg.getWriteoffSuccess}" styleName="gwt-Label-bold"></g:Label>
        </g:HorizontalPanel>
		
		<g:HorizontalPanel spacing="5">
			<g:Button ui:field="writeoffButton" debugId="writeoffButton" text="{msg.getWriteoffButton}" />
			<g:Button ui:field="cancelButton" debugId="cancelButton" text="{msg.getCancelButton}" />
            <g:Button ui:field="closeButton" debugId="closeButton" text="{msg.getCloseButton}" visible="false"/>
		</g:HorizontalPanel>
	</g:FlowPanel>

</ui:UiBinder> 