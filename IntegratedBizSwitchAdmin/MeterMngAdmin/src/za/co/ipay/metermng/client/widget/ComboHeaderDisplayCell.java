package za.co.ipay.metermng.client.widget;

import com.google.gwt.cell.client.AbstractCell;
import com.google.gwt.cell.client.Cell;
import com.google.gwt.cell.client.FieldUpdater;
import com.google.gwt.cell.client.HasCell;
import com.google.gwt.safehtml.shared.SafeHtmlBuilder;

/**
 * ComboHeaderDisplayCell is used to display a heading either as the main heading or as a sub-heading below the main
 * heading.
 * <AUTHOR>
 */
public class ComboHeaderDisplayCell implements HasCell<ComboHeaderNames, ComboHeaderNames> {
    
    private String text;
    private boolean mainHeader;
    private boolean start;
    private boolean end;    
    /** The cell that displays the actual data. */
    private Cell<ComboHeaderNames> cell = new AbstractCell<ComboHeaderNames>() {
     
        @Override
        public void render(Context context, ComboHeaderNames data, SafeHtmlBuilder sb) {            
            if (mainHeader) {
                sb.appendHtmlConstant("<div>");
            } else {
                if (start) {
                    sb.appendHtmlConstant("<table width='100%'><tr>");    
                }
                sb.appendHtmlConstant("<td><span class='searchResultSmallText'>");
            }
            sb.appendEscaped(text);    
            if (mainHeader) {
                sb.appendHtmlConstant("</div>");
            } else {
                if (end) {
                    sb.appendHtmlConstant("</tr></table>");
                }
                sb.appendHtmlConstant("</span></td>"); 
            }
        }
    };
    
    public ComboHeaderDisplayCell(String text) {
        this.text = text;
        this.mainHeader = true;
        this.start = false;
        this.end = false;
    }
    
    public ComboHeaderDisplayCell(String text, boolean start, boolean end) {
        this.text = text;
        this.mainHeader = false;
        this.start = start;
        this.end = end;
    }

    @Override
    public Cell<ComboHeaderNames> getCell() {
        return cell;
    }

    @Override
    public FieldUpdater<ComboHeaderNames, ComboHeaderNames> getFieldUpdater() {
        return null;
    }

    @Override
    public ComboHeaderNames getValue(ComboHeaderNames object) {
        return object;
    }
}
