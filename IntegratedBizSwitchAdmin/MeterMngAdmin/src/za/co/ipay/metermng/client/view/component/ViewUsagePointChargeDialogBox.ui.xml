<!DOCTYPE ui:UiBinder SYSTEM "http://dl.google.com/gwt/DTD/xhtml.ent">
<ui:UiBinder xmlns:ui="urn:ui:com.google.gwt.uibinder"
	xmlns:g="urn:import:com.google.gwt.user.client.ui" 
	xmlns:p2="urn:import:com.google.gwt.user.datepicker.client"
	xmlns:p1="urn:import:za.co.ipay.gwt.common.client.form"
	xmlns:ipay="urn:import:za.co.ipay.gwt.common.client.widgets">
	
	<ui:style>
		.hpMarginAbove{
			margin-top:10px;
		}
	</ui:style>

	<ui:with field="msg" type="za.co.ipay.metermng.client.i18n.UiMessages" />

	<g:HTMLPanel>
		<table>
			<tr>
				<td>
					<g:VerticalPanel horizontalAlignment="ALIGN_CENTER">
   					    <ipay:Message ui:field="feedBack" debugId="feedback"/>
                        <g:FlowPanel>
						    <p1:FormElement ui:field="vendCyclicChargeDateElement" debugId="vendCyclicChargeDateElement" labelText="{msg.getUsagePointLastCyclicDateInfo}:" helpMsg="{msg.getWriteoffLastCyclicDateHelp}">
                                <g:HorizontalPanel spacing="5">
                                    <g:Label ui:field="lastVendCyclicDateLabel" debugId="lastVendCyclicDateLabel" text = "{msg.getUsagePointLastCyclicVendDate}:" styleName="gwt-Label-bold" visible="false"/>
							        <g:HTML ui:field="lastVendCyclicChargeDate" debugId="lastVendCyclicChargeDate"/> 
                                </g:HorizontalPanel>
					        </p1:FormElement>
                        </g:FlowPanel>

                        <g:FlowPanel ui:field="lastBillingCyclicChargeDateElement" debugId="lastBillingCyclicChargeDateElement" visible="false">
                                <g:HorizontalPanel spacing="5">
                                    <g:Label text = "{msg.getUsagePointLastCyclicBillingDate}:" debugId="lastBillingCyclicDateLabel" styleName="gwt-Label-bold" />
                                    <g:HTML ui:field="lastBillingCyclicChargeDate" debugId="lastBillingCyclicChargeDate"/>
                                </g:HorizontalPanel>
                        </g:FlowPanel>

                        <g:FlowPanel>
							<g:HorizontalPanel styleName="{style.hpMarginAbove}">
								<p1:FormElement ui:field="cyclicChargeDateElement" labelText="{msg.getUsagePointChargeViewDialogDateLbl}:" helpMsg="{msg.getUsagePointChargeFilterDateHelp}" 
                                                required="true" debugId="cyclicChargeDateElement">
								    <ipay:IpayDateBox ui:field="cyclicChargefilterDate" debugId="cyclicChargefilterDate" styleName="gwt-TextBox" />
								</p1:FormElement>
							</g:HorizontalPanel>
						</g:FlowPanel>
      
						<g:FlowPanel>
							<g:HorizontalPanel spacing="3">
								<g:Button ui:field="viewOutstandingChargeBtn" debugId="viewOutstandingChargeBtn" text="{msg.getViewButton}" />
								<g:Button ui:field="cancelOutstandingChargeBtn" debugId="cancelOutstandingChargeBtn" text="{msg.getCancelButton}" />
							</g:HorizontalPanel>
						</g:FlowPanel>
					</g:VerticalPanel>
				</td>
			</tr>
		</table>
	</g:HTMLPanel>
	
</ui:UiBinder> 