package za.co.ipay.metermng.client.event;

import com.google.gwt.event.shared.GwtEvent;

public class AddMeterReadingsEvent extends GwtEvent<AddMeterReadingsEventHandler> {
    
    public static Type<AddMeterReadingsEventHandler> TYPE = new Type<AddMeterReadingsEventHandler>();
    
    private String meterType;
    private String paymentMode;
    
    public AddMeterReadingsEvent() {
        this.meterType = "";
        this.paymentMode = "";
    }
    
    public AddMeterReadingsEvent(String meterType, String paymentMode) {
        this.meterType = meterType;
        this.paymentMode = paymentMode;
    }    
    
    public String getMeterType() {
        return meterType;
    }

    public String getPaymentMode() {
        return paymentMode;
    }

    @Override
    public Type<AddMeterReadingsEventHandler> getAssociatedType() {
        return TYPE;
    }

    @Override
    protected void dispatch(AddMeterReadingsEventHandler handler) {
        handler.handleEvent(this);
    }
}
