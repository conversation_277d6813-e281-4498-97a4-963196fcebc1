package za.co.ipay.metermng.client.view.workspace.customer;

import za.co.ipay.metermng.shared.dto.customer.CustomerNotifyData;

import com.google.gwt.user.client.ui.SuggestOracle;

public class CustomerNotifySuggestion implements SuggestOracle.Suggestion {

    private CustomerNotifyData customer;
    
    public CustomerNotifySuggestion(CustomerNotifyData customer) {
        this.customer = customer;
    }
        
    @Override
    public String getDisplayString() {
        return customer.getCustomerDisplay();
    }

    @Override
    public String getReplacementString() {
        return customer.getCustomerNames();
    }

    public CustomerNotifyData getCustomer() {
        return customer;
    }
}
