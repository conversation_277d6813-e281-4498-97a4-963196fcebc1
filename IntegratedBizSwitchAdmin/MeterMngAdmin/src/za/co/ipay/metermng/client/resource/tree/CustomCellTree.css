/** 
  This is a copy of the standard GWT style sheet used by the CellTree to do styling. 
  Change the styles in here and use the corresponding CustomCellTreeResources as your tree's resources.
**/
.cellTreeWidget {
 
}

.cellTreeEmptyMessage {
  padding-left: 20px;
  font-style: italic;
  font-size: x-small;
}

.cellTreeItem {
  padding-top: 4px;
  padding-bottom: 4px;
  cursor: hand;
  cursor: pointer;
  zoom: 1;
}

.cellTreeItemImage {
 
}

.cellTreeItemImageValue {
  zoom: 1;
}

.cellTreeItemValue {
  padding-left: 3px;
  padding-right: 3px;
  outline: none;
}

.cellTreeOpenItem {
 
}

.cellTreeTopItem {
	font-size: small;
  font-weight: bold;
  color: #4b4a4a;
  /** margin-top: 20px; **/
  padding: 3px 13px 3px 10px !important;
}

.cellTreeTopItemImage {
 
}

.cellTreeTopItemImageValue {
  /** border-bottom: 1px solid #6f7277; **/
  padding-bottom: 1px;
}

.cellTreeKeyboardSelectedItem {
  /** background-color: #ffc; **/
  background-color: #f0f0f0;
  outline: none;
}

@sprite .cellTreeSelectedItem {
  gwt-image: 'cellTreeSelectedBackground';
  background-color: #f0f0f0;
  /* background-color: #D3D3D3; */
  height: auto;
  overflow: visible;
}

.cellTreeShowMoreButton {
  padding-top: 2EM;
  padding-left: 2EM;
  outline: none;
}