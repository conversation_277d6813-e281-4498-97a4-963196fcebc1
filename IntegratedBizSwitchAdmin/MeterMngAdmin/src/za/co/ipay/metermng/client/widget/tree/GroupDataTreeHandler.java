package za.co.ipay.metermng.client.widget.tree;

import java.util.ArrayList;

import com.google.gwt.dom.client.Element;
import com.google.gwt.user.cellview.client.CellTree;

import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.shared.GenGroupData;

/**
 * GroupDataTreeHandler is used to handle call back requests from a tree's root cell, nodes, model etc to the actual 
 * component that is controlling the tree, eg: the workspace or panel. All the code use to be within a workspace class
 * but this prevented re-use of the tree classes for other screens so they were split out and this interface forms the
 * bridge between the tree classes and the workspace/panel class.
 * <AUTHOR>
 */
public interface GroupDataTreeHandler {

    public void setPosition(Element element);
    
    public Long getSelectedGroupTypeId();
    
    public String getSelectedGroupTypeName();
    
    public void addNewGroup(ClientFactory clientFactory, Long groupTypeId, GenGroupData parent, GenGroupData current);
    
    public void deleteGroup(GenGroupData genGroupData);
    
    public void saveGenGroup(GenGroupData group);
    
    public void viewGroupEntity(GenGroupData group);
    
    public String getLevelName(GenGroupData data);
    
    public void setPageFocus();

    public CellTree getTree();
    
    //Method to get the last path where a group was selected, added, etc.
    public ArrayList<Long> getLastGenGroupPath();
    
    //Method to check if the group is at the lowest level in the current hierarchy. 
    public boolean isLowestLevel(GenGroupData data);
    
    //Method to check whether the tree is showing data for a single group type or multiple group types.
    public boolean isSingleSelection();
    
    //Method to get the user's assigned group's id or null if there is none.
    public Long getAssignedId();
    //Method to get the path to the user's assigned group or empty list if there is no relevant path.
    public ArrayList<Long> getAssignedPath();
    
    //Method to check whether the current group is editable or not.
    public boolean isGroupViewOnly(GenGroupData group);
    
    //Method to open edit panel for group so that group name and mrid can be edited.
    public void openGenGroupEditPanel(GenGroupData group);
}
