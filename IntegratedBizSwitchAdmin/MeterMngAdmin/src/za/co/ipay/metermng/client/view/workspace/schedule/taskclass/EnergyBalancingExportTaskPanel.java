package za.co.ipay.metermng.client.view.workspace.schedule.taskclass;

import java.util.ArrayList;

import za.co.ipay.gwt.common.client.form.FormElement;
import za.co.ipay.gwt.common.client.form.SimpleForm;
import za.co.ipay.gwt.common.client.handler.FormDataChangeHandler;
import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.metermng.client.form.SimpleFormPanel;
import za.co.ipay.metermng.mybatis.custom.model.MeterDto;
import za.co.ipay.metermng.mybatis.generated.model.MeterReadingType;
import za.co.ipay.metermng.shared.MeterMngStatics;
import za.co.ipay.metermng.shared.schedule.MeterReadingsExportTaskData;
import za.co.ipay.metermng.shared.schedule.MeterReadingsExportTaskUtil;
import za.co.ipay.metermng.shared.utils.MeterMngCommonUtil;

import com.google.gwt.core.client.GWT;
import com.google.gwt.event.shared.HandlerRegistration;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.user.client.ui.ListBox;
import com.google.gwt.user.client.ui.Widget;

public class EnergyBalancingExportTaskPanel extends SimpleFormPanel implements TaskClassUi {
    
    @UiField FormElement superMeterElement;
    @UiField FormElement meterReadingTypeElement;
    @UiField FormElement timePeriodElement;
    
    @UiField ListBox superMeterBox;
    @UiField ListBox meterReadingTypeBox;
    @UiField ListBox timePeriodBox;
    
    HandlerRegistration superMeterBoxReg;
    HandlerRegistration meterReadingTypeBoxReg;
    HandlerRegistration timePeriodBoxReg;


    private static EnergyBalancingExportTaskPanelUiBinder uiBinder = GWT.create(EnergyBalancingExportTaskPanelUiBinder.class);

    interface EnergyBalancingExportTaskPanelUiBinder extends UiBinder<Widget, EnergyBalancingExportTaskPanel> {
    }

    public EnergyBalancingExportTaskPanel(SimpleForm form) {
        super(form);        
        initWidget(uiBinder.createAndBindUi(this));
        addFieldHandlers();        
    }

    @Override
    public void setTaskClassContents(String contents) {
        clearFields();
        if (contents != null && !contents.trim().equals("")) {
            MeterReadingsExportTaskData data = MeterReadingsExportTaskUtil.getTaskClassContents(contents);
            setValue(superMeterBox, data.getMeterNumber());
            setValue(meterReadingTypeBox, data.getMeterReadingType());
            setValue(timePeriodBox, data.getTimePeriod());
        }        
    }

    @Override
    public String getTaskClassContents() {
        return MeterReadingsExportTaskUtil.getTaskClassContents(
                new MeterReadingsExportTaskData(superMeterBox.getValue(superMeterBox.getSelectedIndex()), 
                                                meterReadingTypeBox.getValue(meterReadingTypeBox.getSelectedIndex()), 
                                                timePeriodBox.getValue(timePeriodBox.getSelectedIndex())));
    }

    @Override
    public boolean isValidInput() {
        clearErrors();
        boolean valid = true;
        if (superMeterBox.getSelectedIndex() < 1) {
            valid = false;
            superMeterElement.setErrorMsg(MessagesUtil.getInstance().getMessage("taskschedule.class.error.supermeter"));
        }
        if (meterReadingTypeBox.getSelectedIndex() < 1) {
            valid = false;
            meterReadingTypeElement.setErrorMsg(MessagesUtil.getInstance().getMessage("taskschedule.class.error.type"));
        }
        if (timePeriodBox.getSelectedIndex() < 1) {
            valid = false;
            timePeriodElement.setErrorMsg(MessagesUtil.getInstance().getMessage("taskschedule.class.error.time"));
        }
        return valid;
    }

    @Override
    public void addFieldHandlers() {
        superMeterBoxReg = superMeterBox.addChangeHandler(new FormDataChangeHandler(form));
        meterReadingTypeBoxReg = meterReadingTypeBox.addChangeHandler(new FormDataChangeHandler(form));
        timePeriodBoxReg = timePeriodBox.addChangeHandler(new FormDataChangeHandler(form));        
    }
    
    @Override
    public void removeFieldHandlers() {
        superMeterBoxReg.removeHandler();
        meterReadingTypeBoxReg.removeHandler();
        timePeriodBoxReg.removeHandler();
    }

    @Override
    public void clearFields() {
        superMeterBox.setSelectedIndex(0);
        meterReadingTypeBox.setSelectedIndex(0);
        timePeriodBox.setSelectedIndex(0);
    }

    @Override
    public void clearErrors() {
        superMeterElement.setErrorMsg(null);
        meterReadingTypeElement.setErrorMsg(null);
        timePeriodElement.setErrorMsg(null);        
    }
    
    public void setSuperMeters(ArrayList<MeterDto> meters) {
        superMeterBox.clear();
        superMeterBox.addItem("");
        for(MeterDto meter : meters) {
            superMeterBox.addItem(meter.getNumber(), meter.getNumber());
        }
    }

    public void setMeterReadingTypes(ArrayList<MeterReadingType> types) {
        meterReadingTypeBox.clear();
        meterReadingTypeBox.addItem("");
        for(MeterReadingType type : types) {
            meterReadingTypeBox.addItem(
                    type.getName() + " (" + MeterMngCommonUtil.getCorrectedUnitOfMeasure(type.getUnitOfMeasure()) + ")",
                    type.getValue());
        }
    }
    
    public void setTimePeriods() {
        timePeriodBox.clear();
        timePeriodBox.addItem("");
        timePeriodBox.addItem(MessagesUtil.getInstance().getMessage("taskschedule.taskclass.previous.day"), MeterMngStatics.PREVIOUS_DAY_VALUE);
        timePeriodBox.addItem(MessagesUtil.getInstance().getMessage("taskschedule.taskclass.previous.week"), MeterMngStatics.PREVIOUS_WEEK_VALUE);
        timePeriodBox.addItem(MessagesUtil.getInstance().getMessage("taskschedule.taskclass.previous.month"), MeterMngStatics.PREVIOUS_MONTH_VALUE);
    }
    
    private void setValue(ListBox box, String value) {
        if (value == null || value.trim().equals("")) {
            box.setSelectedIndex(0);
        } else {
            for(int i=0;i<box.getItemCount();i++) {
                if (box.getValue(i).equals(value)) {
                    box.setSelectedIndex(i);
                    return;
                }
            }
        }
    }
}
