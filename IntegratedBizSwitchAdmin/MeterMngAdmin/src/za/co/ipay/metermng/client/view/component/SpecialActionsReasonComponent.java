package za.co.ipay.metermng.client.view.component;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import com.google.gwt.core.client.GWT;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.user.client.ui.FlowPanel;
import com.google.gwt.user.client.ui.Label;
import com.google.gwt.user.client.ui.ListBox;
import com.google.gwt.user.client.ui.TextBox;
import com.google.gwt.user.client.ui.VerticalPanel;
import com.google.gwt.user.client.ui.Widget;

import za.co.ipay.gwt.common.client.form.FormElement;
import za.co.ipay.gwt.common.client.form.HasDirtyData;
import za.co.ipay.gwt.common.client.handler.FormDataChangeHandler;
import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.metermng.client.event.SpecialActionReasonsUpdatedEvent;
import za.co.ipay.metermng.client.event.SpecialActionReasonsUpdatedEventHandler;
import za.co.ipay.metermng.client.event.SpecialActionsUpdatedEvent;
import za.co.ipay.metermng.client.event.SpecialActionsUpdatedEventHandler;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.rpc.callback.ClientCallback;
import za.co.ipay.metermng.client.view.workspace.specialactions.SpecialActionsView;
import za.co.ipay.metermng.client.view.workspace.specialactions.SpecialActionsView.ReasonsInputType;
import za.co.ipay.metermng.mybatis.generated.model.SpecialActionReasons;
import za.co.ipay.metermng.mybatis.generated.model.SpecialActionReasonsLog;
import za.co.ipay.metermng.mybatis.generated.model.SpecialActions;

public class SpecialActionsReasonComponent extends BaseComponent {

    private static SpecialActionsReasonComponentUiBinder uiBinder = GWT.create(SpecialActionsReasonComponentUiBinder.class);

    @UiField TextBox txtbxReasons;
    @UiField ListBox lstbxReasons;
    @UiField Label andOrLabel;

    @UiField FormElement enterReason;
    @UiField FormElement selectReason;

    @UiField FlowPanel freetextflowpanel;
    @UiField FlowPanel selectreasonflowpanel;

    @UiField VerticalPanel parentPanel;

    private Long specialActionReasonsId;

    private String specialActionValue;
    private SpecialActions specialActions;

    private List<SpecialActionReasons> specialActionReasonsList;
	private HasDirtyData hasDirtyData;

    interface SpecialActionsReasonComponentUiBinder extends
            UiBinder<Widget, SpecialActionsReasonComponent> {
    }

	/**
	 * hasDirtyData is optional, should be supplied with forms that have save/cancel approach in non-modal forms
	 */
	public SpecialActionsReasonComponent(ClientFactory clientFactory, HasDirtyData hasDirtyData, String specialActionValue, String textBoxLabel, String listBoxLabel) {
		this(clientFactory, hasDirtyData, specialActionValue);
        setListBoxLabel(listBoxLabel);
        setTextBoxLabel(textBoxLabel);
    }

    /**
     * hasDirtyData is optional, should be supplied with forms that have save/cancel approach in non-modal forms
     */
	public SpecialActionsReasonComponent(ClientFactory clientFactory, HasDirtyData hasDirtyData, String specialActionValue) {
	    this.hasDirtyData = hasDirtyData;
        initWidget(uiBinder.createAndBindUi(this));
        this.clientFactory = clientFactory;
        this.specialActionValue = specialActionValue;
        initUi();
		addFieldHandlers();
		txtbxReasons.ensureDebugId("reasonsTextBox" + specialActionValue);
    }

    private void initUi() {
        setSpecialActions();
        //Handle updated reasons event
        clientFactory.getEventBus().addHandler(SpecialActionReasonsUpdatedEvent.TYPE, new SpecialActionReasonsUpdatedEventHandler() {

            @Override
            public void processSpecialActionReasonsUpdatedEvent(SpecialActionReasonsUpdatedEvent event) {
                if (specialActions != null) {
                    getSpecialActionReasons();
                }
            }
        });

        clientFactory.getEventBus().addHandler(SpecialActionsUpdatedEvent.TYPE, new SpecialActionsUpdatedEventHandler() {

            @Override
            public void processSpecialActionsUpdatedEvent(
                    SpecialActionsUpdatedEvent event) {
                if (specialActionValue != null) {
                    setSpecialActions();
                }
            }


        });

    }

    public void setSpecialActionValue(String specialActionValue) {
        this.specialActionValue = specialActionValue;
        setSpecialActions();
    }

    private void setSpecialActions() {

        ClientCallback<SpecialActions> specialActionsSvcAsyncCallback = new ClientCallback<SpecialActions>() {

            @Override
            public void onSuccess(SpecialActions result) {
                specialActions = result;
                if (specialActions==null) {
                    removeFromParent();
                } else {
                    parentPanel.clear();
                    if (specialActions.getInputType().equals(SpecialActionsView.ReasonsInputType.FTXT.toString())) {
                        parentPanel.add(freetextflowpanel);
                    } else {
                        if (specialActions.getInputType().equals(SpecialActionsView.ReasonsInputType.BOTH.toString())) {
                            parentPanel.add(freetextflowpanel);
                            parentPanel.add(andOrLabel);
                        }
                        parentPanel.add(selectreasonflowpanel);
                        getSpecialActionReasons();
                    }

                    enterReason.setRequired(specialActions.isReasonRequired());
                    selectReason.setRequired(specialActions.isReasonRequired());
                    finishComponentSetup();
                }
            }

        };

        if (clientFactory != null) {
            clientFactory.getSpecialActionsRpc().getSpecialActionsByValue(specialActionValue, specialActionsSvcAsyncCallback);
        }
    }

    private void getSpecialActionReasons() {
        ClientCallback<ArrayList<SpecialActionReasons>> specialActionReasonsSvcAsyncCallback = new ClientCallback<ArrayList<SpecialActionReasons>>() {

            @Override
            public void onSuccess(ArrayList<SpecialActionReasons> result) {
                specialActionReasonsList = result;
                populateReasonsListBox();
            }

        };
        clientFactory.getSpecialActionsRpc().getActiveSpecialActionReasons(specialActions.getId(), specialActionReasonsSvcAsyncCallback);
    }

    private void populateReasonsListBox() {
        lstbxReasons.clear();
        lstbxReasons.addItem("", "-1");
        for (SpecialActionReasons specialActionReasons : specialActionReasonsList) {
            lstbxReasons.addItem(specialActionReasons.getReasonName(), String.valueOf(specialActionReasons.getId()));
        }
        if (specialActionReasonsId != null) {
            setListBoxReasonsSelection(specialActionReasonsId);
        }
    }

    public boolean validate() {
        boolean valid = true;
        if (specialActions.isReasonRequired()) {
            if (specialActions.getInputType().equals(SpecialActionsView.ReasonsInputType.FTXT.toString())
                    && txtbxReasons.getValue().trim().isEmpty()) {
                enterReason.setErrorMsg(MessagesUtil.getInstance().getMessage("error.special.action.reason.required"));
                valid = false;
            }
            if (specialActions.getInputType().equals(SpecialActionsView.ReasonsInputType.SLCT.toString())
                    && lstbxReasons.getSelectedIndex() == 0) {
                selectReason.setErrorMsg(MessagesUtil.getInstance().getMessage("error.special.action.reason.required"));
                valid = false;
            }
            if (specialActions.getInputType().equals(SpecialActionsView.ReasonsInputType.BOTH.toString())
                    && (txtbxReasons.getValue().trim().isEmpty() && lstbxReasons.getSelectedIndex() == 0) ) {
                selectReason.setErrorMsg(MessagesUtil.getInstance().getMessage("error.special.action.reason.required"));
                valid = false;
            }
        }
        return valid;
    }

    public void clearErrorMessages() {
        enterReason.clearErrorMsg();
        selectReason.clearErrorMsg();
    }

    public void clearFields() {
        txtbxReasons.setValue(null);
        lstbxReasons.setSelectedIndex(0);
    }

    public String getReasonsInputType() {
        return specialActions.getInputType();
    }

    public String getTextBoxReasons() {
        return txtbxReasons.getValue();
    }

    private String getListBoxReasonsId() {
        return lstbxReasons.getValue(lstbxReasons.getSelectedIndex());
    }

    private String getListBoxReasonsText() {
        return lstbxReasons.getItemText(lstbxReasons.getSelectedIndex());
    }

    public void setListBoxReasonsSelection(Long reasonId) {
        this.specialActionReasonsId = reasonId;
        for (int i=0; i< lstbxReasons.getItemCount(); i++) {
            if (lstbxReasons.getValue(i).equals(String.valueOf(reasonId))) {
                lstbxReasons.setSelectedIndex(i);
                break;
            }
        }
    }

    public void setDefaultListBoxReasonsSelection() {
        if (lstbxReasons.getItemCount() > 0) {
            lstbxReasons.setSelectedIndex(1);
        } else {
            lstbxReasons.setSelectedIndex(0);
        }

    }

    public SpecialActions getSpecialActions() {
        return specialActions;
    }

    private void setTextBoxLabel(String label) {
        this.enterReason.setLabelText(label);
    }

    private void setListBoxLabel(String label) {
        this.selectReason.setLabelText(label);
    }

    public SpecialActionReasonsLog getLogEntry() {
        return getLogEntry(false);
    }

    public SpecialActionReasonsLog getLogEntry(boolean addLogForNoReason) {
        SpecialActionReasonsLog logEntry = new SpecialActionReasonsLog();
        logEntry.setActionDate(new Date());
        logEntry.setSpecialActionsId(this.specialActions.getId());
        logEntry.setUsername(clientFactory.getUser().getUserName());
        String reasonText = MessagesUtil.getInstance().getMessage("special.action.reason.no.reason");

        if (addLogForNoReason) {
            if (getReasonsInputType().equals(ReasonsInputType.FTXT.toString())) {
                if (!this.getTextBoxReasons().trim().isEmpty()) {
                    reasonText = this.getTextBoxReasons(); // text box has input
                }
                logEntry.setReasonText(reasonText); // reason could be custom or no reason
                logEntry.setSpecialActionReasonsId(null);
            } else if (getReasonsInputType().equals(ReasonsInputType.SLCT.toString())) {
                if (getListBoxReasonsId().equals("-1")) {
                    logEntry.setReasonText(reasonText); // 'no reason given'
                    logEntry.setSpecialActionReasonsId(null);
                } else {
                    logEntry.setReasonText(getListBoxReasonsText());
                    logEntry.setSpecialActionReasonsId(Long.valueOf(getListBoxReasonsId()));
                }
            } else { // BOTH input types
                if (getTextBoxReasons().trim().isEmpty()) { // text box has NO input
                    logEntry.setReasonText(reasonText); // 'no reason given'
                } else {
                    logEntry.setReasonText(getTextBoxReasons());
                }
                if (getListBoxReasonsId().equals("-1")) { // empty list box BUT not necessarily empty text box
                    logEntry.setSpecialActionReasonsId(null);
                } else {
                    logEntry.setSpecialActionReasonsId(Long.valueOf(getListBoxReasonsId()));
                    if (logEntry.getReasonText().equals(reasonText)) { // text box had no input - 'no reason given'
                        logEntry.setReasonText(getListBoxReasonsText());
                    }
                }
            }
        } else { // don't return 'no reason given' - only null
            if (getReasonsInputType().equals(ReasonsInputType.FTXT.toString())) {
                logEntry.setReasonText(getTextBoxReasons());
                logEntry.setSpecialActionReasonsId(null);
                if (this.getTextBoxReasons().trim().isEmpty()) {
                    logEntry = null; // text box is empty on FTXT
                }
            } else if (getReasonsInputType().equals(ReasonsInputType.SLCT.toString())) {
                if (getListBoxReasonsId().equals("-1")) {
                    logEntry = null; // list box is empty on SLCT
                } else {
                    logEntry.setReasonText(getListBoxReasonsText());
                    logEntry.setSpecialActionReasonsId(Long.valueOf(getListBoxReasonsId()));
                }
            } else {
                logEntry.setReasonText(getTextBoxReasons());
                if (getListBoxReasonsId().equals("-1")) {
                    logEntry.setSpecialActionReasonsId(null);
                    if (this.getTextBoxReasons().trim().isEmpty()) {
                        logEntry = null; // both are empty
                    }
                } else {
                    logEntry.setSpecialActionReasonsId(Long.valueOf(getListBoxReasonsId()));
                    if (logEntry.getReasonText() == null || logEntry.getReasonText().trim().isEmpty()) {
                        logEntry.setReasonText(getListBoxReasonsText());
                    }
                }
            }
        }
        return logEntry;
    }

    private void addFieldHandlers() {
        if(hasDirtyData != null) {
            txtbxReasons.addChangeHandler(new FormDataChangeHandler(hasDirtyData));
            lstbxReasons.addChangeHandler(new FormDataChangeHandler(hasDirtyData));
        }
    }

    void finishComponentSetup() {
        // for parent components to override. this method will be called once this
        // component is done drawing
    }

    public void setTextBoxReasons(String reasonText) {
        txtbxReasons.setValue(reasonText);
    }
}
