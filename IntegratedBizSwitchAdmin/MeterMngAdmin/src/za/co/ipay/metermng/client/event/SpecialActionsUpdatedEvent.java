package za.co.ipay.metermng.client.event;

import com.google.gwt.event.shared.GwtEvent;

public class SpecialActionsUpdatedEvent extends GwtEvent<SpecialActionsUpdatedEventHandler> {

    public static Type<SpecialActionsUpdatedEventHandler> TYPE = new Type<SpecialActionsUpdatedEventHandler>();
    
    public SpecialActionsUpdatedEvent() {
        
    }
    
	@Override
    public Type<SpecialActionsUpdatedEventHandler> getAssociatedType() {
        return TYPE;
    }

    @Override
    protected void dispatch(SpecialActionsUpdatedEventHandler handler) {
        handler.processSpecialActionsUpdatedEvent(this);
    }
}
