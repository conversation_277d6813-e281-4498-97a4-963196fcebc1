package za.co.ipay.metermng.client.event;

import com.google.gwt.event.shared.GwtEvent;

public class OpenBlockingTypeEvent extends GwtEvent<OpenBlockingTypeEventHandler> {

	public static Type<OpenBlockingTypeEventHandler> TYPE = new Type<OpenBlockingTypeEventHandler>();

	private String blockingTypeId;

	public OpenBlockingTypeEvent(String blockingTypeId) {
		this.blockingTypeId = blockingTypeId;
	}

	public String getBlockingTypeId() {
		return blockingTypeId;
	}

	@Override
	public Type<OpenBlockingTypeEventHandler> getAssociatedType() {
		return TYPE;
	}

	@Override
	protected void dispatch(OpenBlockingTypeEventHandler handler) {
		handler.openBlockingType(this);
	}

}
