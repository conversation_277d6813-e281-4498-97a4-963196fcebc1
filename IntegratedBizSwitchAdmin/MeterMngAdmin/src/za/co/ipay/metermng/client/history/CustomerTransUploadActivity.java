package za.co.ipay.metermng.client.history;

import za.co.ipay.metermng.client.event.CustomerTransUploadEvent;
import za.co.ipay.metermng.client.factory.ClientFactory;

import com.google.gwt.activity.shared.AbstractActivity;
import com.google.gwt.event.shared.EventBus;
import com.google.gwt.user.client.ui.AcceptsOneWidget;

public class CustomerTransUploadActivity extends AbstractActivity {

    private ClientFactory clientFactory;
    private CustomerTransUploadPlace place;

    public CustomerTransUploadActivity(CustomerTransUploadPlace place, ClientFactory clientFactory) {
        super();
        this.clientFactory = clientFactory;
        this.place = place;
    }

    @Override
    public void start(AcceptsOneWidget panel, EventBus eventBus) {
        clientFactory.getEventBus().fireEvent(new CustomerTransUploadEvent(place.getName()));
    }
}
