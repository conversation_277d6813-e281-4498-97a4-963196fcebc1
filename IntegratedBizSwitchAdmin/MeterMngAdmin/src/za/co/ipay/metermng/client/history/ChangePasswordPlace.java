package za.co.ipay.metermng.client.history;

import com.google.gwt.place.shared.Place;
import com.google.gwt.place.shared.PlaceTokenizer;
import com.google.gwt.place.shared.Prefix;

public class ChangePasswordPlace extends Place {
    
    public static final String LOGGED_IN_USER = "loggedIn";
        
    private String user;
    
    public ChangePasswordPlace() {
        this.user = "";
    }
    
    public ChangePasswordPlace(String user) {
        this.user = user;
    }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        } 
        if (!(o instanceof ChangePasswordPlace)) {
            return false;
        }
        ChangePasswordPlace p = (ChangePasswordPlace) o;
        if (user == null && p.getUser() == null) {
            return true;
        } else if (user != null) {
            return user.equals(p.getUser());
        } else {
            return p.getUser().equals(user);
        }
    }
    
    @Override
    public int hashCode() {
        return (user != null ? user.hashCode() : 27);
    }
    
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("ChangePasswordPlace:");
        sb.append(" user:").append(user);
        return sb.toString();
    }

    public String getUser() {
        return user;
    }
    
    public static String getPlaceAsString(ChangePasswordPlace p) {
        return "changepassword:"+p.getUser();
    }
    
    @Prefix(value = "changepassword")
    public static class Tokenizer implements PlaceTokenizer<ChangePasswordPlace> {
        
        @Override
        public String getToken(ChangePasswordPlace place) {
            return place.getUser();
        }

        @Override
        public ChangePasswordPlace getPlace(String token) {
            return new ChangePasswordPlace(token);
        }        
    }    
}

