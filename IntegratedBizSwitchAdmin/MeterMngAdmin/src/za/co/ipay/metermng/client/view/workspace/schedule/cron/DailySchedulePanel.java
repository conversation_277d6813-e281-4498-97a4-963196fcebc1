package za.co.ipay.metermng.client.view.workspace.schedule.cron;

import com.google.gwt.core.client.GWT;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.user.client.ui.Widget;
import za.co.ipay.gwt.common.client.form.SimpleForm;
import za.co.ipay.metermng.client.form.SimpleFormPanel;

public class DailySchedulePanel extends SimpleFormPanel implements ScheduleUi {

    @UiField(provided=true) DayPanel dayPanel;
    
    private static DailySchedulePanelUiBinder uiBinder = GWT.create(DailySchedulePanelUiBinder.class);

    interface DailySchedulePanelUiBinder extends UiBinder<Widget, DailySchedulePanel> {
    }

    public DailySchedulePanel(SimpleForm form) {
        super(form);
        dayPanel = new DayPanel(form);
        initWidget(uiBinder.createAndBindUi(this));
        addFieldHandlers();
    }
    
    @Override
    public void setCronExpression(String cronExpression) {
        CronExpressionParser parser = new CronExpressionParser(cronExpression);
        int hour = -1;
        int min = -1;
        String hours = parser.getHours();
        if (hours != null && !hours.trim().equals("")) {
            hour = Integer.valueOf(hours);
        }
        String minutes = parser.getMinutes();
        if (minutes != null && !minutes.trim().equals("")) {
            min = Integer.valueOf(minutes);
        }
        if (hour != -1 && min != -1) {
            dayPanel.setTime(hour, min);
        }
    }
    
    @Override
    public boolean isValidInput() {
        return dayPanel.isValidInput();
    }
    
    @Override
    public String getCronExpression() {        
        return CronExpressionParser.getCronExpression(
                "0",
                dayPanel.getMinute(),
                dayPanel.getHour(),
                CronExpressionParser.ANY);
    }

    @Override
    public void addFieldHandlers() {
    }
    
    @Override
    public void removeFieldHandlers() {
        dayPanel.removeFieldHandlers();
    }

    @Override
    public void clearFields() {
        dayPanel.clearFields();
    }

    @Override
    public void clearErrors() {
        dayPanel.clearErrors();
    }
}
