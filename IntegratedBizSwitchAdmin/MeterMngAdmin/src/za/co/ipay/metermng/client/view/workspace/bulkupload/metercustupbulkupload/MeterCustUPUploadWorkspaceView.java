package za.co.ipay.metermng.client.view.workspace.bulkupload.metercustupbulkupload;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import com.google.gwt.core.client.GWT;
import com.google.gwt.place.shared.Place;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.user.client.ui.DeckLayoutPanel;
import com.google.gwt.user.client.ui.Widget;

import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.gwt.common.client.workspace.SimpleView;
import za.co.ipay.gwt.common.client.workspace.WorkspaceNotification;
import za.co.ipay.gwt.common.client.workspace.WorkspaceNotification.NotificationType;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.history.MeterCustUPUploadPlace;
import za.co.ipay.metermng.client.rpc.callback.ClientCallback;
import za.co.ipay.metermng.client.view.workspace.BaseWorkspace;
import za.co.ipay.metermng.mybatis.generated.model.AppSetting;
import za.co.ipay.metermng.mybatis.generated.model.FormFields;
import za.co.ipay.metermng.shared.MeterMngStatics;
import za.co.ipay.metermng.shared.appsettings.AppSettings;
import za.co.ipay.metermng.shared.group.GroupTypeData;

public class MeterCustUPUploadWorkspaceView extends BaseWorkspace {
    @UiField DeckLayoutPanel deckPanel;
    
    @UiField SimpleView view;
    MeterCustUPGenerateCsvTemplateView generateCsvTemplateView;

	private ArrayList<AppSetting> customFieldList;
	private String customFieldsStatusUnavailable;
	private ClientMeterCustUPBulkCsvMapToData clientMeterCustUPBulkCsvMapToData;
	private List<GroupTypeData> groupTypeHierarchies;

    private static MeterCustUPUploadWorkspaceViewUiBinder uiBinder = GWT.create(MeterCustUPUploadWorkspaceViewUiBinder.class);

    interface MeterCustUPUploadWorkspaceViewUiBinder extends UiBinder<Widget, MeterCustUPUploadWorkspaceView> {
    }

    public MeterCustUPUploadWorkspaceView(ClientFactory clientFactory, MeterCustUPUploadPlace place) {
        this.clientFactory = clientFactory;
        initWidget(uiBinder.createAndBindUi(this));
        setPlaceString(MeterCustUPUploadPlace.getPlaceAsString(place));
        setHeaderText(MessagesUtil.getInstance().getMessage("bulk.upload.heading.metercustup"));
        updateCustomFields(true);
    }

    private void updateCustomFields(final boolean doInitialization) {
        clientFactory.getAppSettingRpc()
                .getAppSettingsForUPAndMeterAndCustomerCustomFields(new ClientCallback<ArrayList<AppSetting>>() {
                    @Override
                    public void onSuccess(ArrayList<AppSetting> result) {
                        customFieldList = result;
                        if (doInitialization) {
                            updateGroups();
                        } else {
                            updateFormFieldsInfo();
                        }
                    }
                });
    }

    private void updateGroups() {
        clientFactory.getGroupRpc().getActiveGroupTypesWithHierarchy(new ClientCallback<ArrayList<GroupTypeData>>() {
            @Override
            public void onSuccess(ArrayList<GroupTypeData> result) {
                groupTypeHierarchies = result;
                updateFormFieldsInfo();
            }
        });
    }
    
    private void updateFormFieldsInfo() {
        view.getViewForm().clear();
        clientFactory.getUserInterfaceRpcAsync().getFormFields(new ClientCallback<Map<String, FormFields>>() {
            @Override
            public void onSuccess(Map<String, FormFields> formFieldsMap) {
                clientMeterCustUPBulkCsvMapToData = new ClientMeterCustUPBulkCsvMapToData(customFieldList,
                        customFieldsStatusUnavailable, groupTypeHierarchies, clientFactory.isEnableNonBillable(),
                        formFieldsMap);
                initDataMapAndUi();
            }
        });
    }

    private void initDataMapAndUi() {
        clientFactory.getAppSettingRpc().getAppSettingByKey(AppSettings.METERS_IN_STORE,
                new ClientCallback<AppSetting>() {
                    @Override
                    public void onSuccess(AppSetting result) {
                        if (result != null) {
                            boolean isMetersInStore = (Boolean.parseBoolean(result.getValue()));
                            clientMeterCustUPBulkCsvMapToData.initDataMap(isMetersInStore);
                            initUi();
                        }
                    }
                });
    }

    private void initUi() {
    	generateCsvTemplateView = new MeterCustUPGenerateCsvTemplateView(this, clientFactory, view);
        deckPanel.showWidget(view);
    }

    @Override
    public void onArrival(Place place) {
    }

    @Override
    public void onLeaving() {

    }

    @Override
    public void onSelect() {

    }

    @Override
    public void onClose() {

    }

    @Override
    public boolean handles(Place place) {
        return (place instanceof MeterCustUPUploadPlace);
    }
    
    @Override
    public void handleNotification(WorkspaceNotification notification) {
        String dataType = notification.getDataType();
        if (MeterMngStatics.APPSETTINGS_MODIFIED.equals(dataType)
                && NotificationType.DATA_UPDATED == notification.getNotificationType()) {

            // Redraw all panels if a related appsetting is changed.
            String notificationObjAsString = (String) notification.getObject();

            if (notificationObjAsString.toLowerCase().contains("usagepoint.custom")
                    || notificationObjAsString.toLowerCase().contains("customer.custom")) {
                updateCustomFields(false);
            }
        } else if (MeterMngStatics.USER_INTERFACE_CONFIGURATION_MODIFIED.equals(dataType)) {
            updateFormFieldsInfo();
        } else if (MeterMngStatics.GROUP_TYPE.equals(dataType)
                && NotificationType.DATA_UPDATED == notification.getNotificationType()) {
            updateGroups();
        }
    }

	public ClientMeterCustUPBulkCsvMapToData getClientMeterCustUPBulkCsvMapToData() {
		return clientMeterCustUPBulkCsvMapToData;
	}    
}
