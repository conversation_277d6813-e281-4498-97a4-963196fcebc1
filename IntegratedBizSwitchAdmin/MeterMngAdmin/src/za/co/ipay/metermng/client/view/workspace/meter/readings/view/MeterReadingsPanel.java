package za.co.ipay.metermng.client.view.workspace.meter.readings.view;

import static za.co.ipay.metermng.datatypes.ServiceResourceE.ELEC;
import static za.co.ipay.metermng.datatypes.ServiceResourceE.GAS;
import static za.co.ipay.metermng.datatypes.ServiceResourceE.WATER;

import java.util.ArrayList;
import java.util.Date;
import java.util.logging.Logger;

import com.google.gwt.core.client.GWT;
import com.google.gwt.event.dom.client.ChangeEvent;
import com.google.gwt.event.dom.client.ChangeHandler;
import com.google.gwt.event.dom.client.FocusEvent;
import com.google.gwt.event.dom.client.FocusHandler;
import com.google.gwt.event.logical.shared.SelectionEvent;
import com.google.gwt.event.logical.shared.SelectionHandler;
import com.google.gwt.i18n.client.DateTimeFormat;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.user.client.ui.ListBox;
import com.google.gwt.user.client.ui.SuggestBox;
import com.google.gwt.user.client.ui.SuggestOracle;
import com.google.gwt.user.client.ui.SuggestOracle.Suggestion;
import com.google.gwt.user.client.ui.Widget;
import com.google.gwt.user.datepicker.client.DateBox;

import za.co.ipay.gwt.common.client.form.FormElement;
import za.co.ipay.gwt.common.client.form.Format;
import za.co.ipay.gwt.common.client.form.Format.FormatUtil;
import za.co.ipay.gwt.common.client.form.SimpleForm;
import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.gwt.common.client.widgets.StrictDateFormat;
import za.co.ipay.gwt.common.shared.validation.ValidateUtil;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.form.SimpleFormPanel;
import za.co.ipay.metermng.client.rpc.callback.ClientCallback;
import za.co.ipay.metermng.datatypes.MeterReadingTypeE;
import za.co.ipay.metermng.datatypes.ServiceResourceE;
import za.co.ipay.metermng.mybatis.custom.model.MeterDto;
import za.co.ipay.metermng.mybatis.generated.model.MeterReadingType;
import za.co.ipay.metermng.shared.MeterMngStatics;
import za.co.ipay.metermng.shared.MeterSuggestOracle;
import za.co.ipay.metermng.shared.MeterSuggestion;
import za.co.ipay.metermng.shared.utils.MeterMngCommonUtil;
import za.co.ipay.metermng.shared.dto.UsagePointData;

public class MeterReadingsPanel extends SimpleFormPanel {
    
    private MeterReadingsViews parent;
    private ClientFactory clientFactory;
    private ArrayList<String> graphTypes;
    private String readingType;
    private boolean usagePointMeter;
    private Long serviceResourceId;
    
    @UiField FormElement meterNumberElement;
    @UiField FormElement startElement;
    @UiField FormElement endElement;
    @UiField FormElement readingTypeElement;
    @UiField FormElement graphTypeElement;
    @UiField FormElement balancingMeterElement;
    
    @UiField(provided=true) SuggestBox meterBox;    
    MeterDto selectedMeter;
    @UiField DateBox startBox;
    @UiField DateBox endBox;
    @UiField ListBox readingTypeBox;
    @UiField ListBox graphTypeBox;
    @UiField ListBox balancingMeterBox;
    
    private UsagePointData selectedUsagePointData;

    private boolean doView = false;
    
    private static Logger logger = Logger.getLogger(MeterReadingsPanel.class.getName());
    
    private static MeterReadingsPanelUiBinder uiBinder = GWT.create(MeterReadingsPanelUiBinder.class);

    interface MeterReadingsPanelUiBinder extends UiBinder<Widget, MeterReadingsPanel> {
    }
    
    public MeterReadingsPanel(ClientFactory clientFactory, SimpleForm form, ArrayList<String> graphTypes, MeterReadingsViews parent, boolean usagePointMeter) {
        super(form);
        logger.info("Creating MeterReadingsPanel... ");
        this.parent = parent;
        this.clientFactory = clientFactory;
        this.graphTypes = graphTypes;
        this.usagePointMeter = usagePointMeter;
        loadBalancingMeters();
        createMeterBox(clientFactory);
        initWidget(uiBinder.createAndBindUi(this));
        initUi();
    }

    public MeterReadingsPanel(ClientFactory clientFactory, SimpleForm form, ArrayList<String> graphTypes,
            String superMeter, String graphType, String readingType, Date startDate, Date endDate, MeterReadingsViews parent) {
        super(form);
        this.parent = parent;
        this.clientFactory = clientFactory;
        this.graphTypes = graphTypes;
        this.readingType = readingType;
        this.doView = true;
        this.usagePointMeter = false;
        loadBalancingMeters(superMeter, graphType, startDate, endDate);
        createMeterBox(clientFactory);
        initWidget(uiBinder.createAndBindUi(this));
        initUi();
    }
    
    private void loadBalancingMeters() {
        clientFactory.getMeterRpc().getSuperMeters(new ClientCallback<ArrayList<MeterDto>>() {
            @Override
            public void onSuccess(ArrayList<MeterDto> result) {
                balancingMeterBox.clear();
                for(MeterDto m : result) {
                    balancingMeterBox.addItem(m.getNumber(), m.getId().toString());
                } 
            }
        });
    }
    
    private void loadBalancingMeters(final String superMeter, final String graphType, final Date startDate, final Date endDate) {
        clientFactory.getMeterRpc().getSuperMeters(new ClientCallback<ArrayList<MeterDto>>() {
            @Override
            public void onSuccess(ArrayList<MeterDto> result) {
                balancingMeterBox.clear();
                for(MeterDto m : result) {
                    balancingMeterBox.addItem(m.getNumber(), m.getId().toString());
                } 
                setFields(superMeter, graphType, startDate, endDate);
            }
        });
    }
    
    private void initUi() {
        Format format = FormatUtil.getInstance();
        StrictDateFormat strictDateFormat = new StrictDateFormat(
                DateTimeFormat.getFormat(format.getDateFormat() + " " + format.getTimeFormat()));
        startBox.setFormat(strictDateFormat);
        endBox.setFormat(strictDateFormat);
        startBox.setValue(getInitStartDate());
        endBox.setValue(getInitEndDate()); 
        
        for(String gType : graphTypes) {
            graphTypeBox.addItem(gType);
        }
        
        graphTypeBox.addChangeHandler(new ChangeHandler() {            
            @Override
            public void onChange(ChangeEvent event) {
                setUiForGraphType();
            }
        });
        
        if (usagePointMeter) {
            graphTypeBox.setEnabled(false);
            if (parent.isUsagePointTabSelected()) {
                meterBox.setEnabled(true);
            } else {
                meterBox.setEnabled(false);
            }
        }
    }
    
    protected void setMeterReadingTypes(ArrayList<MeterReadingType> types) {
        readingTypeBox.clear();
        readingTypeBox.addItem("");
        
        for(MeterReadingType type : types) {            
            readingTypeBox.addItem(
                    type.getName() + " - " + MeterMngCommonUtil.getCorrectedUnitOfMeasure(type.getUnitOfMeasure()),
                    type.getValue());
        }
        
        setReadingType(readingType);
        
        if (doView) {
            doView = false;
            parent.onView();
        }
        removeUnneededServiceResources(serviceResourceId, types);
    }
    
    protected void setFields(String superMeter, String graphType, Date startDate, Date endDate) {
        logger.info("Setting fields: "+superMeter+" "+graphType+" "+startDate+" "+endDate);
        setBalancingMeter(superMeter);
        setGraphType(graphType);        
        if (startDate != null) {
            startBox.setValue(startDate);            
        }
        if (endDate != null) {
            endBox.setValue(endDate);
        }
        setUiForGraphType();
    }
    
    private void setBalancingMeter(String superMeter) {
        if (balancingMeterBox.getItemCount() > 0) {
            for(int i=0;i<balancingMeterBox.getItemCount();i++) {
                if (balancingMeterBox.getItemText(i).equals(superMeter)) {
                    balancingMeterBox.setSelectedIndex(i);
                    return;
                }
            }
        }
    }
    
    private void setGraphType(String gType) {
        if (graphTypeBox.getItemCount() > 0) {
            for(int i=0;i<graphTypeBox.getItemCount();i++) {
                if (graphTypeBox.getItemText(i).equals(gType)) {
                    graphTypeBox.setSelectedIndex(i);
                    return;
                }
            }
        }
    }
    
    private void setReadingType(String rType) {
        if (rType == null || rType.trim().equals("")) {
            rType = MeterMngStatics.ENERGY_FORWARD_METER_READING_TYPE;
        }
        logger.info("Setting readingType: "+rType);
        
        if (readingTypeBox.getItemCount() > 0) {
            for(int i=0;i<readingTypeBox.getItemCount();i++) {
                if (readingTypeBox.getValue(i).equals(rType)) {
                    readingTypeBox.setSelectedIndex(i);
                    logger.info("Set readingType: "+i);
                    return;
                }
            }
        }
    }
    
    private void setUiForGraphType() {
        String gType = getGraphType();
        if (MessagesUtil.getInstance().getMessage("meterreadings.type.graph.single").equals(gType)) {
            meterNumberElement.setVisible(true);
            balancingMeterElement.setVisible(false);
        } else if (MessagesUtil.getInstance().getMessage("meterreadings.type.graph.balancing").equals(gType)) {
            meterNumberElement.setVisible(false);
            balancingMeterElement.setVisible(true);            
        }
        setReadingType(readingType);
    }
    
    private void createMeterBox(ClientFactory clientFactory) {
        this.meterBox = new SuggestBox(new MeterSuggestOracle(clientFactory));
        meterBox.addSelectionHandler(new SelectionHandler<SuggestOracle.Suggestion>() {            
            @Override
            public void onSelection(SelectionEvent<Suggestion> event) {
                if (event.getSelectedItem() instanceof MeterSuggestion) {
                    selectedMeter = ((MeterSuggestion) event.getSelectedItem()).getMeter();
                }                
            }
        });
        meterBox.getValueBox().addFocusHandler(new FocusHandler() {            
            @Override
            public void onFocus(FocusEvent event) {
                meterBox.showSuggestionList();
            }
        });
    }

    @Override
    public void addFieldHandlers() {
    }

    @Override
    public void clearFields() {
        startBox.setValue(getInitStartDate());
        endBox.setValue(getInitEndDate());
        readingTypeBox.setSelectedIndex(0);
        graphTypeBox.setSelectedIndex(0);
        setUiForGraphType();
    }
    
    private Date getInitStartDate() {
        String start = DateTimeFormat.getFormat("MM/yyyy").format(new Date());
        return DateTimeFormat.getFormat("dd/MM/yyyy HH:ss").parse("01/"+start+" 00:00");
    }
    
    private Date getInitEndDate() {
        return new Date(); //now
    }

    @Override
    public void clearErrors() {
        meterNumberElement.clearErrorMsg();
        balancingMeterElement.clearErrorMsg();
        startElement.clearErrorMsg();
        endElement.clearErrorMsg();
        readingTypeElement.clearErrorMsg();
        graphTypeElement.clearErrorMsg();
    }
    
    protected boolean isValidInput() {
        clearErrors();
        boolean valid = true;
        
        String gType = getGraphType();
        if (ValidateUtil.isNullOrBlank(gType)) {
            valid = false;
            graphTypeElement.showErrorMsg(MessagesUtil.getInstance().getMessage("meterreadings.error.type.graph"));
        } else if (MessagesUtil.getInstance().getMessage("meterreadings.type.graph.single").equals(gType)) {
            if (selectedMeter == null && ValidateUtil.isNullOrBlank(meterBox.getText())) {
                valid = false;
                meterNumberElement.showErrorMsg(MessagesUtil.getInstance().getMessage("meterreadings.error.none"));
            } else if (selectedMeter == null && !ValidateUtil.isNullOrBlank(meterBox.getText())) {
                MeterSuggestion ms = ((MeterSuggestOracle) meterBox.getSuggestOracle()).getFirstSuggestion();
                if (ms != null && ms.getMeter().getNumber().equals(meterBox.getText())) {
                    selectedMeter = ms.getMeter();
                } else {
                    valid = false;
                    meterNumberElement.showErrorMsg(MessagesUtil.getInstance().getMessage("meterreadings.error.none.selected"));
                }
            }
        } else if (MessagesUtil.getInstance().getMessage("meterreadings.type.graph.balancing").equals(gType)) {
            Long balancingId = getBalancingMeterId();
            logger.info("Current balancingMeterId: "+balancingId);
            if (balancingId == null) {
                valid = false;
                balancingMeterElement.showErrorMsg(MessagesUtil.getInstance().getMessage("meterreadings.error.balancing"));
            }
        } 
        
        Date start = getStartDate();
        String startInput = startBox.getTextBox().getText();
        Date end = getEndDate();
        String endInput = endBox.getTextBox().getText();
        String format = FormatUtil.getInstance().getDateFormat() + " " +FormatUtil.getInstance().getTimeFormat();
        logger.info("StartDate: "+start+" EndDate: "+end);
        if (start == null && startInput == null) {
            valid = false;
            startElement.showErrorMsg(MessagesUtil.getInstance().getMessage("meterreadings.error.start"));
        } else if (start == null && startInput != null) {
            valid = false;
            startElement.showErrorMsg(MessagesUtil.getInstance().getMessage("meterreadings.error.start.format", new String[]{format}));
        }
        
        if (end == null && endInput == null) {
            valid = false;
            endElement.showErrorMsg(MessagesUtil.getInstance().getMessage("meterreadings.error.end"));
        } else if (end == null && endInput != null) {
            valid = false;
            endElement.showErrorMsg(MessagesUtil.getInstance().getMessage("meterreadings.error.end.format", new String[]{format}));
        }        
        
        if (start != null && end != null && !start.before(end)) {
            valid = false;
            startElement.showErrorMsg(MessagesUtil.getInstance().getMessage("meterreadings.error.dates"));
        }
        
        String readingType = getMeterReadingType();
        if (ValidateUtil.isNullOrBlank(readingType)) {
            valid = false;
            readingTypeElement.showErrorMsg(MessagesUtil.getInstance().getMessage("meterreadings.error.type"));
        }
        
        return valid;
    }
    
    public MeterDto getSelectedMeter() {
        return selectedMeter;
    }

    public void setUsagePointData(UsagePointData usagePointData, Long serviceResourceId) {
        this.selectedUsagePointData = usagePointData;
        this.serviceResourceId = serviceResourceId;
    }
    
    public void setSelectedMeter(MeterDto selectedMeter, Long serviceResourceId) {  
        logger.info("Set selected meter: "+selectedMeter);
        this.selectedMeter = selectedMeter;
        if (selectedMeter != null) {
            this.meterBox.setText(selectedMeter.getNumber());            
        } else {
            this.meterBox.setText(null);
        }
        this.serviceResourceId = serviceResourceId;
    }
    
    private void removeUnneededServiceResources(long resourceId, ArrayList<MeterReadingType> meterReadingTypes) {
    	if(meterReadingTypes.size() == (readingTypeBox.getItemCount()-1)) {
            for (int i = meterReadingTypes.size() - 1; i >= 0; i--) {
                if (isRemovableItem(MeterReadingTypeE.fromId(meterReadingTypes.get(i).getId()),
                        ServiceResourceE.fromId(resourceId))) {
                    readingTypeBox.removeItem(i + 1);
                }
            }
    	}
        if (ELEC.getId() != resourceId) {
            readingTypeBox.setSelectedIndex(1);
        }
    }

    private boolean isRemovableItem(MeterReadingTypeE readingTypeId, ServiceResourceE resourceId) {
        switch (readingTypeId) {
        case REAL_ENERGY_FWD:
        case REAL_POWER_DEMAND:
        case REAL_ENERGY_REV:
        case REACTIVE_ENERGY_FWD:
        case APPARENT_ENERGY_FWD:
        case APPARENT_POWER_DEMAND:
            return ELEC != resourceId;
        case GAS_VOLUME:
            return GAS != resourceId;
        case POTABLE_WATER_VOLUME:
        case POTABLE_WATER_VOLUMETRIC_FLOW_RATE:
            return WATER != resourceId;
        }
        return false;
    }
    
    public Date getStartDate() {
        return startBox.getValue();
    }
    
    public Date getEndDate() {
        return endBox.getValue();
    }
    
    public String getMeterReadingType() {
        int index = readingTypeBox.getSelectedIndex();
        logger.info("Current meterReadingType index: "+index);
        if (index > -1) {
            return readingTypeBox.getValue(index);
        } else {
            return null;
        }
    }
    
    public String getGraphType() {
        int index = graphTypeBox.getSelectedIndex();
        if (index > -1) {
            return graphTypeBox.getItemText(index);
        } else {
            return null;
        }
    }
    
    public Long getBalancingMeterId() {
        int index = balancingMeterBox.getSelectedIndex();
        if (index > -1) {
            return Long.valueOf(balancingMeterBox.getValue(index));
        } else {
            return null;
        }
    }
    
}
