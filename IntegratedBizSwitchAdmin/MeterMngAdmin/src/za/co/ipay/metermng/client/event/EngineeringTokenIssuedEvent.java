package za.co.ipay.metermng.client.event;

import za.co.ipay.metermng.mybatis.generated.model.StsMeter;

import com.google.gwt.event.shared.GwtEvent;

public class EngineeringTokenIssuedEvent extends GwtEvent<EngineeringTokenIssuedEventHandler> {

    public static Type<EngineeringTokenIssuedEventHandler> TYPE = new Type<EngineeringTokenIssuedEventHandler>();
    
    StsMeter meter;
    int engineeringTokenType;
    
    public EngineeringTokenIssuedEvent(StsMeter meter, int tokenType) {
        this.meter = meter;
        engineeringTokenType = tokenType;
    }
    
    public StsMeter getMeter() {
        return meter;
    }
    
    public int getEngineeringTokenType() {
        return engineeringTokenType;
    }
    
    @Override
    public Type<EngineeringTokenIssuedEventHandler> getAssociatedType() {
        return TYPE;
    }

    @Override
    protected void dispatch(EngineeringTokenIssuedEventHandler handler) {
        handler.handleEngineeringTokenIssuedEvent(this);
    }


}
