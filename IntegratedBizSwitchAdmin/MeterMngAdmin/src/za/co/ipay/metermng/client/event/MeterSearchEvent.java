package za.co.ipay.metermng.client.event;

import za.co.ipay.metermng.client.history.MeterPlace;

import com.google.gwt.event.shared.GwtEvent;

public class MeterSearchEvent extends GwtEvent<MeterSearchEventHandler> {

    public static Type<MeterSearchEventHandler> TYPE = new Type<MeterSearchEventHandler>();
    private final MeterPlace meterPlace;
    private MeterPlace meterPlaceToClose = null;

    public MeterSearchEvent(MeterPlace meterPlace) {
        this.meterPlace = meterPlace;
    }

    public String getMeterNumber() {
        return meterPlace.getMeterNumber();
    }
    
    public boolean isSearchFromURL() {
        return meterPlace.isFromURL();
    }
    
    public MeterPlace getMeterPlace() {
        return meterPlace;
    }
    
    public MeterPlace getMeterPlaceToClose() {
        return meterPlaceToClose;
    }

    public void setMeterPlaceToClose(MeterPlace meterPlaceToClose) {
        this.meterPlaceToClose = meterPlaceToClose;
    }

    @Override
    public Type<MeterSearchEventHandler> getAssociatedType() {
        return TYPE;
    }

    @Override
    protected void dispatch(MeterSearchEventHandler handler) {
       handler.searchByMeterNumber(this);
    }

}
