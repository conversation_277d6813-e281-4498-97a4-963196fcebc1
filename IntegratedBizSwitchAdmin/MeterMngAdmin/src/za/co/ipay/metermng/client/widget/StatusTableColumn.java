package za.co.ipay.metermng.client.widget;

import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.gwt.common.client.resource.StyleNames;
import za.co.ipay.metermng.datatypes.RecordStatus;
import za.co.ipay.metermng.mybatis.custom.model.HasStatus;

import com.google.gwt.cell.client.Cell.Context;
import com.google.gwt.user.cellview.client.TextColumn;

public class StatusTableColumn<T extends HasStatus> extends TextColumn<T> {

    @Override
    public String getValue(T data) {
        if (data.getRecordStatus().equals(RecordStatus.ACT)) {
            return MessagesUtil.getInstance().getMessage("status.active");
        } else if (data.getRecordStatus().equals(RecordStatus.DAC)) {
            return MessagesUtil.getInstance().getMessage("status.inactive");
        } else if (data.getRecordStatus().equals(RecordStatus.DEL)) {
            return MessagesUtil.getInstance().getMessage("status.deleted");
        } else {
            return data.getRecordStatus().toString();
        }
    }

    @Override
    public String getCellStyleNames(Context context, T data) {
        if (data.getRecordStatus().equals(RecordStatus.ACT)) {
            return StyleNames.SUCCESS_STATUS;
        } else {
            return StyleNames.ERROR_STATUS;
        }
    }
}
