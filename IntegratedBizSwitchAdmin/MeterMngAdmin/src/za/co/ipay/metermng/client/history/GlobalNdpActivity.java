package za.co.ipay.metermng.client.history;

import za.co.ipay.metermng.client.event.GlobalNdpEvent;
import za.co.ipay.metermng.client.factory.ClientFactory;

import com.google.gwt.activity.shared.AbstractActivity;
import com.google.gwt.event.shared.EventBus;
import com.google.gwt.user.client.ui.AcceptsOneWidget;

public class GlobalNdpActivity extends AbstractActivity {

    private ClientFactory clientFactory;
    private GlobalNdpPlace place;

    public GlobalNdpActivity(GlobalNdpPlace place, ClientFactory clientFactory) {
        super();
        this.clientFactory = clientFactory;
        this.place = place;
    }

    @Override
    public void start(AcceptsOneWidget panel, EventBus eventBus) {
        clientFactory.getEventBus().fireEvent(new GlobalNdpEvent(place.getName()));
    }
}
