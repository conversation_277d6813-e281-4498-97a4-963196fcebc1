package za.co.ipay.metermng.client.view.workspace.schedule.taskclass;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.logging.Logger;

import za.co.ipay.gwt.common.client.form.FormElement;
import za.co.ipay.gwt.common.client.form.IntegerValueBox;
import za.co.ipay.gwt.common.client.form.SimpleForm;
import za.co.ipay.gwt.common.client.handler.FormDataChangeHandler;
import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.gwt.common.client.widgets.PercentageTextBox;
import za.co.ipay.metermng.client.form.SimpleFormPanel;
import za.co.ipay.metermng.client.view.workspace.schedule.cron.CronExpressionParser;
import za.co.ipay.metermng.mybatis.custom.model.MeterDto;
import za.co.ipay.metermng.mybatis.generated.model.MeterReadingType;
import za.co.ipay.metermng.shared.MeterMngStatics;
import za.co.ipay.metermng.shared.schedule.EnergyBalancingVarianceTaskData;
import za.co.ipay.metermng.shared.schedule.EnergyBalancingVarianceTaskUtil;
import za.co.ipay.metermng.shared.utils.MeterMngCommonUtil;

import com.google.gwt.core.client.GWT;
import com.google.gwt.event.shared.HandlerRegistration;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.user.client.ui.ListBox;
import com.google.gwt.user.client.ui.Widget;

public class EnergyBalancingVarianceTaskPanel extends SimpleFormPanel implements TaskClassUi {
    
    @UiField FormElement superMeterElement;
    @UiField FormElement meterReadingTypeElement;
    @UiField FormElement numberElement;
    @UiField FormElement unitsElement;
    @UiField FormElement varianceElement;
    
    @UiField ListBox superMeterBox;
    @UiField ListBox meterReadingTypeBox;
    @UiField IntegerValueBox numberBox;
    @UiField ListBox unitsBox;
    @UiField PercentageTextBox varianceBox;
    
    HandlerRegistration superMeterBoxReg;
    HandlerRegistration meterReadingTypeBoxReg;
    HandlerRegistration numberBoxReg;
    HandlerRegistration unitsBoxReg;
    HandlerRegistration varianceBoxReg;

    private String taskScheduleCronExp;
    
    private static Logger logger = Logger.getLogger(EnergyBalancingVarianceTaskPanel.class.getName());

    private static EnergyBalancingExportTaskPanelUiBinder uiBinder = GWT.create(EnergyBalancingExportTaskPanelUiBinder.class);

    interface EnergyBalancingExportTaskPanelUiBinder extends UiBinder<Widget, EnergyBalancingVarianceTaskPanel> {
    }

    public EnergyBalancingVarianceTaskPanel(SimpleForm form, String taskScheduleCronExp) {
        super(form);        
        initWidget(uiBinder.createAndBindUi(this));
        this.taskScheduleCronExp = taskScheduleCronExp;
        initUi();
        setTaskScheduleRepeat();
        addFieldHandlers();        
    }
    
    private void initUi() {
        unitsBox.addItem("");
        unitsBox.addItem(MessagesUtil.getInstance().getMessage("taskschedule.minutes"), MeterMngStatics.MINUTES_VALUE);
        unitsBox.addItem(MessagesUtil.getInstance().getMessage("taskschedule.hours"), MeterMngStatics.HOURS_VALUE);
    }
    
    private void setTaskScheduleRepeat() {        
        if (CronExpressionParser.isRepeatedly(taskScheduleCronExp)) {
            CronExpressionParser parser = new CronExpressionParser(taskScheduleCronExp);
            
            String hourly = parser.getHours();
            logger.info("hourly: "+hourly);
            if (hourly != null) {            
                String[] fields = hourly.split("\\/");
                if (fields != null && fields.length == 2) {
                    numberBox.setValue(Integer.valueOf(fields[1]));
                    unitsBox.setSelectedIndex(2);
                    return;
                }            
            } 
            
            String minutes = parser.getMinutes();
            logger.info("Minutes: "+minutes);
            if (minutes != null) {                
                String[] fields = minutes.split("\\/");
                if (fields != null && fields.length == 2) {
                    numberBox.setValue(Integer.valueOf(fields[1]));
                    unitsBox.setSelectedIndex(1);
                }            
            }
        }
    }

    @Override
    public void setTaskClassContents(String contents) {
        clearFields();
        if (contents != null && !contents.trim().equals("")) {
            EnergyBalancingVarianceTaskData data = EnergyBalancingVarianceTaskUtil.getTaskClassContents(contents);
            setValue(superMeterBox, data.getSuperMeterNumber());
            setValue(meterReadingTypeBox, data.getMeterReadingType());
            
            for(int i=0;i<unitsBox.getItemCount();i++) {
                if (unitsBox.getValue(i).equals(data.getMinsOrHours())) {
                    unitsBox.setSelectedIndex(i);
                    break;
                }
            }
            
            if (data.getNumber() != null && !data.getNumber().trim().equals("")) {
                numberBox.setValue(Integer.valueOf(data.getNumber()));
            }
            
            if (data.getVariance() != null && !data.getVariance().trim().equals("")) {
                varianceBox.setAmount(new BigDecimal(data.getVariance()));
            }
        } else {
            setTaskScheduleRepeat();
        }
    }

    @Override
    public String getTaskClassContents() {
        EnergyBalancingVarianceTaskData data = new EnergyBalancingVarianceTaskData();        
        data.setSuperMeterNumber(superMeterBox.getValue(superMeterBox.getSelectedIndex()));
        data.setMeterReadingType(meterReadingTypeBox.getValue(meterReadingTypeBox.getSelectedIndex()));
        
        if (numberBox.getValue() != null) {
            data.setNumber(numberBox.getValue().toString());
        }
        data.setMinsOrHours(unitsBox.getValue(unitsBox.getSelectedIndex()));
        
        if (varianceBox.getAmount() != null) {
            data.setVariance(varianceBox.getAmount().toPlainString());
        }
        return EnergyBalancingVarianceTaskUtil.getTaskClassContents(data);
    }

    @Override
    public boolean isValidInput() {
        logger.info("Validating...");
        
        clearErrors();
        boolean valid = true;
        if (superMeterBox.getSelectedIndex() < 1) {
            valid = false;
            superMeterElement.setErrorMsg(MessagesUtil.getInstance().getMessage("taskschedule.class.error.supermeter"));
        }
        if (meterReadingTypeBox.getSelectedIndex() < 1) {
            valid = false;
            meterReadingTypeElement.setErrorMsg(MessagesUtil.getInstance().getMessage("taskschedule.class.error.type"));
        }
        
        Integer hours = numberBox.getValue();
        if (hours == null || hours.intValue() <= 0) {
            valid = false;
            numberElement.setErrorMsg(MessagesUtil.getInstance().getMessage("taskschedule.repeatedly.error.number"));
        }
        
        if (unitsBox.getSelectedIndex() < 1) {
            valid = false;
            unitsElement.setErrorMsg(MessagesUtil.getInstance().getMessage("taskschedule.repeatedly.error.units"));
        }
        
        if (varianceBox.getAmount() == null || varianceBox.getAmount().doubleValue() < 0) {
            valid = false;
            varianceElement.showErrorMsg(MessagesUtil.getInstance().getMessage("energybalancing.error.percent"));
        }
        return valid;
    }

    @Override
    public void addFieldHandlers() {
        superMeterBoxReg = superMeterBox.addChangeHandler(new FormDataChangeHandler(form));
        meterReadingTypeBoxReg = meterReadingTypeBox.addChangeHandler(new FormDataChangeHandler(form));
        numberBoxReg = numberBox.addChangeHandler(new FormDataChangeHandler(form));   
        unitsBoxReg = unitsBox.addChangeHandler(new FormDataChangeHandler(form));
        varianceBoxReg = varianceBox.getBigDecimalValueBox().addChangeHandler(new FormDataChangeHandler(form));
    }
    
    @Override
    public void removeFieldHandlers() {
        superMeterBoxReg.removeHandler();
        meterReadingTypeBoxReg.removeHandler();
        numberBoxReg.removeHandler();
        unitsBoxReg.removeHandler();
        varianceBoxReg.removeHandler();
    }

    @Override
    public void clearFields() {
        superMeterBox.setSelectedIndex(0);
        meterReadingTypeBox.setSelectedIndex(0);
        numberBox.setValue(null);
        varianceBox.setAmount(null);
    }

    @Override
    public void clearErrors() {
        superMeterElement.setErrorMsg(null);
        meterReadingTypeElement.setErrorMsg(null);
        numberElement.setErrorMsg(null);        
        varianceElement.setErrorMsg(null);
    }
    
    public void setSuperMeters(ArrayList<MeterDto> meters) {
        superMeterBox.clear();
        superMeterBox.addItem("");
        superMeterBox.addItem(MessagesUtil.getInstance().getMessage("taskschedule.all.supermeters"), "*");
        for(MeterDto meter : meters) {
            superMeterBox.addItem(meter.getNumber(), meter.getNumber());
        }
    }

    public void setMeterReadingTypes(ArrayList<MeterReadingType> types) {
        meterReadingTypeBox.clear();
        meterReadingTypeBox.addItem("");
        for(MeterReadingType type : types) {
            meterReadingTypeBox.addItem(
                    type.getName() + " (" + MeterMngCommonUtil.getCorrectedUnitOfMeasure(type.getUnitOfMeasure()) + ")",
                    type.getValue());
        }
    }
    
    private void setValue(ListBox box, String value) {
        if (value == null || value.trim().equals("")) {
            box.setSelectedIndex(0);
        } else {
            for(int i=0;i<box.getItemCount();i++) {
                if (box.getValue(i).equals(value)) {
                    box.setSelectedIndex(i);
                    return;
                }
            }
        }
    }
}
