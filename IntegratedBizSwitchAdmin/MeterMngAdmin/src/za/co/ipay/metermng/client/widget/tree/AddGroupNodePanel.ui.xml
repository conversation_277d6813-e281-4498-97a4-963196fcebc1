<!DOCTYPE ui:UiBinder SYSTEM "http://dl.google.com/gwt/DTD/xhtml.ent">
<ui:UiBinder xmlns:ui="urn:ui:com.google.gwt.uibinder"
	xmlns:g="urn:import:com.google.gwt.user.client.ui"
	xmlns:ipay="urn:import:za.co.ipay.gwt.common.client.form"
	xmlns:p2="urn:import:za.co.ipay.metermng.client.view.component">
	<ui:style>
		
	</ui:style>
	<ui:with field="msg" type="za.co.ipay.metermng.client.i18n.UiMessages" />
	<g:VerticalPanel>

	   <ipay:FormElement ui:field="txtbxNewElement" helpMsg="" labelText="" required="true">
	       <g:TextBox ui:field="txtbxNew" visibleLength="40" maxLength="255" debugId="addGroupnodePanelTxtbxNew"/>
       </ipay:FormElement>
		
       <ipay:FormElement ui:field="accessGroupElement" debugId="accessGroupElement" labelText="{msg.getGroupNodeAccessGroup}:" helpMsg="{msg.getGroupNodeAccessGroupHelp}" required="true">
           <g:ListBox ui:field="accessGroupBox" debugId="accessGroupBox" title="{msg.getGroupNodeAccessGroup}" styleName="gwt-TextBox" />
       </ipay:FormElement>       
		
       <p2:MridComponent ui:field="mridComponent"></p2:MridComponent>
		
	   <g:HorizontalPanel spacing="2">
	       <g:Button ui:field="btnSave" text="{msg.getSaveButton}" debugId="addGroupnodePanelBtnSave"></g:Button>
	       <g:Button ui:field="btnCancel" text="{msg.getCancelButton}" debugId="addGroupnodePanelBtnCancel"></g:Button>
	   </g:HorizontalPanel>
	   
	</g:VerticalPanel>
</ui:UiBinder> 