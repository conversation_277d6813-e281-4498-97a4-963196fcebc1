package za.co.ipay.metermng.client.event;

import com.google.gwt.event.shared.GwtEvent;

public class SpecialActionsEvent extends GwtEvent<SpecialActionsEventHandler> {

    public static Type<SpecialActionsEventHandler> TYPE = new Type<SpecialActionsEventHandler>();

    private String name;
    private boolean updateEvent = false;

    public SpecialActionsEvent(String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }

    public boolean isUpdateEvent() {
		return updateEvent;
	}

	public void setUpdateEvent(boolean updateEvent) {
		this.updateEvent = updateEvent;
	}

	@Override
    public Type<SpecialActionsEventHandler> getAssociatedType() {
        return TYPE;
    }

    @Override
    protected void dispatch(SpecialActionsEventHandler handler) {
        handler.handleEvent(this);
    }
}
