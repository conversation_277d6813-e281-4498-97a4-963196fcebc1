package za.co.ipay.metermng.client.view.workspace.schedule;

import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.gwt.common.client.workspace.SimpleTableView;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.history.TaskSchedulePlace;
import za.co.ipay.metermng.client.view.workspace.BaseWorkspace;
import za.co.ipay.metermng.mybatis.generated.model.TaskSchedule;
import za.co.ipay.metermng.shared.dto.schedule.ScheduledTaskDto;

import com.google.gwt.core.client.GWT;
import com.google.gwt.place.shared.Place;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.user.client.ui.DeckLayoutPanel;
import com.google.gwt.user.client.ui.Widget;

public class TaskScheduleWorkspaceView extends BaseWorkspace {
    
    @UiField DeckLayoutPanel deckPanel;
    
    @UiField SimpleTableView<TaskSchedule> view;
    TaskScheduleView taskScheduleView;
    
    @UiField SimpleTableView<ScheduledTaskDto> view2;
    ScheduledTaskView scheduledTaskView;
    
    private static SchedulingWorkspaceViewUiBinder uiBinder = GWT.create(SchedulingWorkspaceViewUiBinder.class);

    interface SchedulingWorkspaceViewUiBinder extends UiBinder<Widget, TaskScheduleWorkspaceView> {
    }

    public TaskScheduleWorkspaceView(ClientFactory clientFactory, TaskSchedulePlace place) {
        this.clientFactory = clientFactory;
        initWidget(uiBinder.createAndBindUi(this));
        setPlaceString(TaskSchedulePlace.toPlaceString(place));
        setHeaderText(MessagesUtil.getInstance().getMessage("taskschedule.title"));
        initUi();
    }

    private void initUi() {
        taskScheduleView = new TaskScheduleView(this, clientFactory, view);
        scheduledTaskView = new ScheduledTaskView(this, clientFactory, view2);
        deckPanel.showWidget(view);
    }
    
    public void goToTaskSchedules(Long taskScheduleId) {
        scheduledTaskView.clear();        
        deckPanel.showWidget(0);
        deckPanel.animate(getAnimationTime());
    }
    
    public void goToScheduledTasks(TaskSchedule taskSchedule) {
        scheduledTaskView.setTaskSchedule(taskSchedule);
        deckPanel.showWidget(1);
        deckPanel.animate(getAnimationTime());
    }
    
    @Override
    public void onArrival(Place place) {
        taskScheduleView.getCount(false);
    }

    @Override
    public void onLeaving() {

    }

    @Override
    public void onSelect() {

    }

    @Override
    public void onClose() {

    }

    @Override
    public boolean handles(Place place) {
        return (place instanceof TaskSchedulePlace);
    }    
}