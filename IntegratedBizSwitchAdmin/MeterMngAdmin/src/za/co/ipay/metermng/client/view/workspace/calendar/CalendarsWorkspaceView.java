package za.co.ipay.metermng.client.view.workspace.calendar;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.logging.Logger;

import za.co.ipay.gwt.common.client.Dialogs;
import za.co.ipay.gwt.common.client.factory.ResourcesFactoryUtil;
import za.co.ipay.gwt.common.client.form.FormElement;
import za.co.ipay.gwt.common.client.form.HasDirtyData;
import za.co.ipay.gwt.common.client.handler.ConfirmHandler;
import za.co.ipay.gwt.common.client.handler.FormDataChangeHandler;
import za.co.ipay.gwt.common.client.handler.FormDataClickHandler;
import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.gwt.common.client.validation.ClientValidatorUtil;
import za.co.ipay.gwt.common.client.widgets.TablePager;
import za.co.ipay.gwt.common.client.workspace.SessionCheckCallback;
import za.co.ipay.metermng.client.event.CalendarUpdatedEvent;
import za.co.ipay.metermng.client.event.SeasonAssignedEvent;
import za.co.ipay.metermng.client.event.SeasonAssignedEventHandler;
import za.co.ipay.metermng.client.event.TariffUpdatedEvent;
import za.co.ipay.metermng.client.event.TariffUpdatedEventHandler;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.history.CalendarPlace;
import za.co.ipay.metermng.client.resource.image.MediaResource.MediaResourceUtil;
import za.co.ipay.metermng.client.rpc.callback.ClientCallback;
import za.co.ipay.metermng.client.view.component.tariff.calendar.AssignDayProfilesPanel;
import za.co.ipay.metermng.client.view.component.tariff.calendar.AssignSeasonsPanel;
import za.co.ipay.metermng.client.view.component.tariff.calendar.CalendarContainer;
import za.co.ipay.metermng.client.view.component.tariff.calendar.DayProfilesPanel;
import za.co.ipay.metermng.client.view.component.tariff.calendar.SpecialDayPanel;
import za.co.ipay.metermng.client.view.workspace.BaseWorkspace;
import za.co.ipay.metermng.client.widget.StatusTableColumn;
import za.co.ipay.metermng.datatypes.RecordStatus;
import za.co.ipay.metermng.mybatis.generated.model.PricingStructure;
import za.co.ipay.metermng.mybatis.generated.model.TouCalendar;
import za.co.ipay.metermng.shared.TouCalendarData;

import com.google.gwt.core.client.GWT;
import com.google.gwt.dom.client.BrowserEvents;
import com.google.gwt.event.dom.client.ClickEvent;
import com.google.gwt.event.logical.shared.CloseEvent;
import com.google.gwt.event.logical.shared.CloseHandler;
import com.google.gwt.event.logical.shared.OpenEvent;
import com.google.gwt.event.logical.shared.OpenHandler;
import com.google.gwt.place.shared.Place;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.uibinder.client.UiHandler;
import com.google.gwt.user.cellview.client.CellTable;
import com.google.gwt.user.cellview.client.ColumnSortEvent;
import com.google.gwt.user.cellview.client.ColumnSortEvent.ListHandler;
import com.google.gwt.user.cellview.client.TextColumn;
import com.google.gwt.user.client.Timer;
import com.google.gwt.user.client.ui.Button;
import com.google.gwt.user.client.ui.CheckBox;
import com.google.gwt.user.client.ui.DisclosurePanel;
import com.google.gwt.user.client.ui.HorizontalPanel;
import com.google.gwt.user.client.ui.Image;
import com.google.gwt.user.client.ui.Label;
import com.google.gwt.user.client.ui.ProvidesResize;
import com.google.gwt.user.client.ui.RequiresResize;
import com.google.gwt.user.client.ui.TextBox;
import com.google.gwt.user.client.ui.Widget;
import com.google.gwt.view.client.CellPreviewEvent;
import com.google.gwt.view.client.DefaultSelectionEventManager;
import com.google.gwt.view.client.ListDataProvider;
import com.google.gwt.view.client.SelectionChangeEvent;
import com.google.gwt.view.client.SingleSelectionModel;

public class CalendarsWorkspaceView extends BaseWorkspace implements ProvidesResize, RequiresResize, CalendarContainer {

    private static final int DEFAULT_PAGE_SIZE = 15;

    private Logger logger = Logger.getLogger("CalendarsWorkspaceView");
    @UiField(provided=true) CellTable<TouCalendar> calendarsTable;
    @UiField TablePager calendarsPager;
    @UiField(provided=true) AssignSeasonsPanel monthsInSeason;
    @UiField(provided=true) DayProfilesPanel dayProfiles;
    @UiField(provided=true) AssignDayProfilesPanel assignDayProfiles;
    @UiField(provided=true) SpecialDayPanel specialdays;
    @UiField DisclosurePanel monthsPanel;
    @UiField Image monthsopenorclosearrow;
    @UiField Label monthsPanelHeader;
    @UiField Label monthsCompleteHeader;
    @UiField DisclosurePanel calendarSeasonProfilesPanel;
    @UiField Image cspopenorclosearrow;
    @UiField Label cspPanelHeader;
    @UiField Label cspCompleteHeader;
    @UiField DisclosurePanel dayprofilesPanel;
    @UiField Image dpopenorclosearrow;
    @UiField Label dpPanelHeader;
    @UiField Label dpCompleteHeader;
    @UiField DisclosurePanel specialdayPanel;
    @UiField Image sdopenorclosearrow;
    @UiField Label sdPanelHeader;
    @UiField Label sdCompleteHeader;
    @UiField TextBox nameTextBox;
    @UiField FormElement nameElement;
    @UiField FormElement descriptionElement;
    @UiField FormElement activeElement;
    @UiField TextBox descriptionTextBox;
    @UiField CheckBox activeBox;
    @UiField HorizontalPanel buttons;
    @UiField Button btnCancel;
    @UiField Button btnSave;
    @UiField Label pricingStructureNote;

    @UiField Label formHeading;

    private ListDataProvider<TouCalendar> dataProvider;
    private SingleSelectionModel<TouCalendar> selectionModel;
    protected HasDirtyData hasDirtyData;
    private TouCalendarData calendar = new TouCalendarData();
    private ArrayList<TouCalendar> calendarsList;
    private TextColumn<TouCalendar> calendarName;
    private  String pricingstructures = "";
    ListHandler<TouCalendar> columnSortHandler;

    Boolean assignSeasonsComplete = null;
    Boolean setupDayProfilesComplete = null;
    Boolean assignDayProfilesComplete = null;
    private boolean readOnly = false;

    interface CalendarSettingsUiBinder extends UiBinder<Widget, CalendarsWorkspaceView> {
    }

    private static CalendarSettingsUiBinder uiBinder = GWT.create(CalendarSettingsUiBinder.class);

    public CalendarsWorkspaceView(ClientFactory clientFactory, CalendarPlace place) {
        this.clientFactory = clientFactory;
        monthsInSeason = new AssignSeasonsPanel(clientFactory, this);
        dayProfiles = new DayProfilesPanel(clientFactory, this);
        assignDayProfiles = new AssignDayProfilesPanel(clientFactory, this);
        specialdays = new SpecialDayPanel(clientFactory, this);
        createTable();
        initWidget(uiBinder.createAndBindUi(this));
        hasDirtyData = createAndRegisterHasDirtyData();
        setImages();
        setPlaceString(CalendarPlace.getPlaceAsString(place));
        setHeaderText(MessagesUtil.getInstance().getMessage("calendars.title"));
        initTable();
        addFieldHandlers();
        addHandlers();
        formHeading.setText(MessagesUtil.getInstance().getMessage("calendar.add"));
        activeBox.setEnabled(false);
    }

    private void setImages() {
        monthsopenorclosearrow.setResource(MediaResourceUtil.getInstance().getClosedArrowImage());
        dpopenorclosearrow.setResource(MediaResourceUtil.getInstance().getClosedArrowImage());
        cspopenorclosearrow.setResource(MediaResourceUtil.getInstance().getClosedArrowImage());
        sdopenorclosearrow.setResource(MediaResourceUtil.getInstance().getClosedArrowImage());
    }

    protected void createTable() {
        if (ResourcesFactoryUtil.getInstance() != null && ResourcesFactoryUtil.getInstance().getCellTableResources() != null) {
            calendarsTable = new CellTable<TouCalendar>(DEFAULT_PAGE_SIZE, ResourcesFactoryUtil.getInstance().getCellTableResources());
        } else {
            calendarsTable = new CellTable<TouCalendar>(DEFAULT_PAGE_SIZE);
        }
    }

    protected void addFieldHandlers() {
        nameTextBox.addChangeHandler(new FormDataChangeHandler(hasDirtyData));
        descriptionTextBox.addChangeHandler(new FormDataChangeHandler(hasDirtyData));
        activeBox.addClickHandler(new FormDataClickHandler(hasDirtyData));
    }

    private void addHandlers() {
        clientFactory.getEventBus().addHandler(SeasonAssignedEvent.TYPE, new SeasonAssignedEventHandler() {

            @Override
            public void processSeasonAssignedEvent(SeasonAssignedEvent event) {
                setCalendar(calendar);
            }
        });

        clientFactory.getEventBus().addHandler(TariffUpdatedEvent.TYPE, new TariffUpdatedEventHandler() {

            @Override
            public void processTariffUpdatedEvent(TariffUpdatedEvent event) {
                setCalendar(calendar);
            }
        });

        this.monthsPanel.addOpenHandler(new OpenHandler<DisclosurePanel>() {

            @Override
            public void onOpen(OpenEvent<DisclosurePanel> event) {
                monthsopenorclosearrow.setResource(MediaResourceUtil.getInstance().getOpenedArrowImage());
            }
        });

        this.monthsPanel.addCloseHandler(new CloseHandler<DisclosurePanel>() {

            @Override
            public void onClose(CloseEvent<DisclosurePanel> event) {
                monthsopenorclosearrow.setResource(MediaResourceUtil.getInstance().getClosedArrowImage());
            }
        });
        this.dayprofilesPanel.addOpenHandler(new OpenHandler<DisclosurePanel>() {

            @Override
            public void onOpen(OpenEvent<DisclosurePanel> event) {
                dpopenorclosearrow.setResource(MediaResourceUtil.getInstance().getOpenedArrowImage());
            }
        });

        this.dayprofilesPanel.addCloseHandler(new CloseHandler<DisclosurePanel>() {

            @Override
            public void onClose(CloseEvent<DisclosurePanel> event) {
                dpopenorclosearrow.setResource(MediaResourceUtil.getInstance().getClosedArrowImage());
            }
        });
        this.calendarSeasonProfilesPanel.addOpenHandler(new OpenHandler<DisclosurePanel>() {

            @Override
            public void onOpen(OpenEvent<DisclosurePanel> event) {
                cspopenorclosearrow.setResource(MediaResourceUtil.getInstance().getOpenedArrowImage());
            }
        });

        this.calendarSeasonProfilesPanel.addCloseHandler(new CloseHandler<DisclosurePanel>() {

            @Override
            public void onClose(CloseEvent<DisclosurePanel> event) {
                cspopenorclosearrow.setResource(MediaResourceUtil.getInstance().getClosedArrowImage());
            }
        });
        this.specialdayPanel.addOpenHandler(new OpenHandler<DisclosurePanel>() {

            @Override
            public void onOpen(OpenEvent<DisclosurePanel> event) {
                sdopenorclosearrow.setResource(MediaResourceUtil.getInstance().getOpenedArrowImage());
            }
        });

        this.specialdayPanel.addCloseHandler(new CloseHandler<DisclosurePanel>() {

            @Override
            public void onClose(CloseEvent<DisclosurePanel> event) {
                sdopenorclosearrow.setResource(MediaResourceUtil.getInstance().getClosedArrowImage());
            }
        });


    }


    protected void initTable() {
        if (dataProvider == null) {
            calendarName = new TextColumn<TouCalendar>() {
                @Override
                public String getValue(TouCalendar data) {
                    if (data.getName() != null) {
                        return data.getName();
                    }
                    return " ? ";
                }
            };
            calendarName.setSortable(true);

            TextColumn<TouCalendar> calendarDescription = new TextColumn<TouCalendar>() {
                @Override
                public String getValue(TouCalendar data) {
                    if (data.getDescription() != null) {
                        return data.getDescription();
                    }
                    return " ? ";
                }
            };
            TextColumn<TouCalendar> statusColumn = new StatusTableColumn<TouCalendar>();

            // Add the columns.
            calendarsTable.addColumn(calendarName, MessagesUtil.getInstance().getMessage("calendar.field.name"));
            calendarsTable.addColumn(calendarDescription, MessagesUtil.getInstance().getMessage("calendar.field.description"));
            calendarsTable.addColumn(statusColumn, MessagesUtil.getInstance().getMessage("calendar.field.active"));

            dataProvider = new ListDataProvider<TouCalendar>();
            dataProvider.addDataDisplay(calendarsTable);
            calendarsPager.setDisplay(calendarsTable);
            calendarsTable.setPageSize(getPageSize());

            logger.info("Created Calendar table");
        }

        selectionModel = new SingleSelectionModel<TouCalendar>();
        CellPreviewEvent.Handler<TouCalendar> handler = new CellPreviewEvent.Handler<TouCalendar>() {
            final CellPreviewEvent.Handler<TouCalendar> selectionEventManager = DefaultSelectionEventManager.createDefaultManager();
            @Override
            public void onCellPreview(final CellPreviewEvent<TouCalendar> event) {
                    if (BrowserEvents.CLICK.equals(event.getNativeEvent().getType())) {
                        if (!event.isCanceled()) {
                            if (hasDirtyData.isDirtyData()) {
                                Dialogs.confirmDiscardChanges(new ConfirmHandler() {
                                            @Override
                                            public void confirmed(boolean confirm) {
                                                if (confirm) {
                                                    hasDirtyData.setDirtyData(false);
                                                    event.getDisplay().getSelectionModel().setSelected(event.getValue(), true);
                                                    logger.info("Discarding changes for selection event");
                                                } else {
                                                    event.setCanceled(true);
                                                    logger.info("Cancelled selection event, staying with current selection");
                                                }

                                            }});
                            } else {
                                selectionEventManager.onCellPreview(event);
                            }

                        }
                } else {
                    selectionEventManager.onCellPreview(event);
                }
            }
        };
        calendarsTable.setSelectionModel(selectionModel, handler);
        selectionModel.addSelectionChangeHandler(new SelectionChangeEvent.Handler() {
            public void onSelectionChange(SelectionChangeEvent event) {

                final TouCalendar selected = selectionModel.getSelectedObject();
                if (selected != null) {
                    dayProfiles.setDayProfile(null);
                    setCalendar(selected);
                    calendarsTable.getSelectionModel().setSelected(selected, true);
                }
            }
        });
    }

    public void clearTableSelection() {
        TouCalendar selected = selectionModel.getSelectedObject();
        if (selected != null) {
            selectionModel.setSelected(selected, false);
        }
    }

    public void refreshTable() {
        clientFactory.getCalendarRpc().getCalendars(new ClientCallback<ArrayList<TouCalendar>>() {
            @Override
            public void onSuccess(ArrayList<TouCalendar> result) {
                logger.info("Got seasons: " + result.size());
                if (dataProvider != null && dataProvider.getList() != null) {
                    dataProvider.getList().clear();
                    dataProvider.getList().addAll(result);
                    setCalendarsList(result);
                }
                if (calendar != null) {
                    calendarsTable.getSelectionModel().setSelected(calendar, true);
                }
            }
        });
    }

    public void setCalendarsList(ArrayList<TouCalendar> thedata) {
        calendarsList = thedata;
        dataProvider.getList().clear();
        dataProvider.getList().addAll(calendarsList);
        dataProvider.refresh();
        if (columnSortHandler == null || columnSortHandler.getList() == null) {
            columnSortHandler = new ListHandler<TouCalendar>(dataProvider.getList());
            columnSortHandler.setComparator(calendarName, new Comparator<TouCalendar>() {
                public int compare(TouCalendar o1, TouCalendar o2) {
                    if (o1 == o2) {
                        return 0;
                    }
                    if (o1 != null && o1.getName() != null) {
                        return (o2 != null && o2.getName() != null) ? o1.getName().compareTo(o2.getName()) : 1;
                    }
                    return -1;
                }
            });
            calendarsTable.addColumnSortHandler(columnSortHandler);
            calendarsTable.getColumnSortList().push(calendarName);
            ColumnSortEvent.fire(calendarsTable, calendarsTable.getColumnSortList());
        } else {
            columnSortHandler.setList(dataProvider.getList());
            ColumnSortEvent.fire(calendarsTable, calendarsTable.getColumnSortList());
        }
    }

    private void setCalendar(TouCalendar thecalendar) {
        if (thecalendar != null && thecalendar.getId() != null) {
            clientFactory.getCalendarRpc().getCalendar(thecalendar.getId(), new  ClientCallback<TouCalendarData>() {

                @Override
                public void onSuccess(TouCalendarData result) {
                    calendar = result;
                    refreshCalendar();
                    monthsPanel.setVisible(true);
                    calendarSeasonProfilesPanel.setVisible(true);
                    dayprofilesPanel.setVisible(true);
                    specialdayPanel.setVisible(true);
                    monthsPanelHeader.setText(ResourcesFactoryUtil.getInstance().getMessages().getMessage("calendar.assign.season.heading") + " " + calendar.getName());
                    cspPanelHeader.setText(ResourcesFactoryUtil.getInstance().getMessages().getMessage("calendar.assign.dayprofile.heading") + " " + calendar.getName());
                    dpPanelHeader.setText(ResourcesFactoryUtil.getInstance().getMessages().getMessage("calendar.dayprofiles.heading") + " " + calendar.getName());
                    sdPanelHeader.setText(ResourcesFactoryUtil.getInstance().getMessages().getMessage("calendar.specialday.heading") + " " + calendar.getName());
                    sdCompleteHeader.setText(" ("+ResourcesFactoryUtil.getInstance().getMessages().getMessage("calendar.optional")+")");
                    sdCompleteHeader.setStylePrimaryName("success");
                }
            });
        } else {
            formHeading.setText(MessagesUtil.getInstance().getMessage("calendar.add"));
            monthsPanelHeader.setText(ResourcesFactoryUtil.getInstance().getMessages().getMessage("calendar.assign.season.heading"));
            cspPanelHeader.setText(ResourcesFactoryUtil.getInstance().getMessages().getMessage("calendar.assign.dayprofile.heading"));
            dpPanelHeader.setText(ResourcesFactoryUtil.getInstance().getMessages().getMessage("calendar.dayprofiles.heading"));
            sdPanelHeader.setText(ResourcesFactoryUtil.getInstance().getMessages().getMessage("calendar.specialday.heading"));
            calendar = null;
            clearTableSelection();
            refreshCalendar();
            monthsPanel.setVisible(false);
            calendarSeasonProfilesPanel.setVisible(false);
            dayprofilesPanel.setVisible(false);
            specialdayPanel.setVisible(false);
            pricingStructureNote.setText("");
            btnSave.setVisible(true);
            nameTextBox.setEnabled(true);
            descriptionTextBox.setEnabled(true);
            activeBox.setEnabled(true);
        }

    }

    private void refreshCalendar() {
        mapDataToForm();
        monthsInSeason.setTouCalendarData(calendar);
        dayProfiles.setTouCalendarData(calendar);
        assignDayProfiles.setTouCalendarData(calendar);
        specialdays.setTouCalendarData(calendar);
        if (calendar != null) {
            setCalendarReadOnly(calendar.getPricingStructures() != null && !(calendar.getPricingStructures().isEmpty()) );
        }
    }

    private void setCalendarReadOnly(boolean readOnly) {
        this.readOnly = readOnly;
        pricingstructures = "";
        if (!readOnly) {
            formHeading.setText(MessagesUtil.getInstance().getMessage("calendar.update").trim() + " "+calendar.getName());
        } else {
            for (PricingStructure ps : calendar.getPricingStructures()) {
                if (pricingstructures.isEmpty()) {
                    pricingstructures = ps.getName().trim();
                } else {
                    pricingstructures += (", "+ps.getName().trim());
                }
            }
            pricingStructureNote.setText(MessagesUtil.getInstance().getMessage("calendar.readOnly", new String[] {pricingstructures}));
            formHeading.setText(MessagesUtil.getInstance().getMessage("calendar.title")+" "+calendar.getName());
        }
        pricingStructureNote.setVisible(readOnly);
        btnSave.setVisible(!readOnly);
        nameTextBox.setEnabled(!readOnly);
        descriptionTextBox.setEnabled(!readOnly);
        activeBox.setEnabled(!readOnly);
        monthsInSeason.setReadOnly(readOnly);
        dayProfiles.setReadOnly(readOnly);
        assignDayProfiles.setReadOnly(readOnly);
        specialdays.setReadOnly(readOnly);
    }

    @UiHandler("btnSave")
    void handleSaveButton(ClickEvent event) {
        // Map form fields to data object
        mapFormToData();
        clearErrorMessages();
        if (calendar.getPricingStructures() != null && !(calendar.getPricingStructures().isEmpty())) {
            Dialogs.displayErrorMessage(MessagesUtil.getInstance().getMessage("calendar.readOnly", new String[] {pricingstructures}), MediaResourceUtil.getInstance().getErrorIcon(), btnSave.getAbsoluteLeft()+btnSave.getOffsetWidth(), btnSave.getAbsoluteTop()-btnSave.getOffsetHeight(), null);
            return;
        }
        if (validate()) {
            if (clientFactory != null) {
                SessionCheckCallback sessionCheckCallback = new SessionCheckCallback() {
                    @Override
                    public void callback(SessionCheckResolution resolution) {
                        clientFactory.getCalendarRpc().updateCalendar(calendar,  new  ClientCallback<TouCalendarData>() {

                            @Override
                            public void onSuccess(TouCalendarData result) {
                                hasDirtyData.setDirtyData(false);
                                clientFactory.getEventBus().fireEvent(new CalendarUpdatedEvent());
                                setCalendar(result);
                                refreshTable();
                                Dialogs.displayInformationMessage(
                                        MessagesUtil.getInstance().getMessage("message.saved",
                                                new String[] {MessagesUtil.getInstance().getMessage("calendar.title")}),
                                        MediaResourceUtil.getInstance().getInformationIcon(),
                                        btnSave.getAbsoluteLeft(), btnSave.getAbsoluteTop(),null);
                            }
                        });
                    }
                };
                clientFactory.handleSessionCheckCallback(sessionCheckCallback);
            }
        } else {
            Dialogs.displayErrorMessage(MessagesUtil.getInstance().getMessage("calendar.save.errors"), MediaResourceUtil.getInstance().getErrorIcon(), btnSave.getAbsoluteLeft()+btnSave.getOffsetWidth(), btnSave.getAbsoluteTop()-btnSave.getOffsetHeight(), null);
            btnSave.setEnabled(true);
        }
    }

    @UiHandler("btnCancel")
    void handleClearButton(ClickEvent event) {
        if(hasDirtyData.isDirtyData()) {
            Dialogs.confirmDiscardChanges(new ConfirmHandler() {
                @Override
                public void confirmed(boolean confirm) {
                    if (confirm) {
                        hasDirtyData.setDirtyData(false);
                        setCalendar(null);
                        Dialogs.displayInformationMessage(MessagesUtil.getInstance().getMessage("calendar.changes.cleared"), MediaResourceUtil.getInstance().getInformationIcon(), btnCancel.getAbsoluteLeft()+btnCancel.getOffsetWidth(), btnCancel.getAbsoluteTop()-btnCancel.getOffsetHeight(), null);
                    }
                }});
        } else {
            setCalendar(null);
            Dialogs.displayInformationMessage(MessagesUtil.getInstance().getMessage("calendar.changes.cleared"), MediaResourceUtil.getInstance().getInformationIcon(), btnCancel.getAbsoluteLeft()+btnCancel.getOffsetWidth(), btnCancel.getAbsoluteTop()-btnCancel.getOffsetHeight(), null);
        }
    }

    protected void mapDataToForm() {
        assignSeasonsComplete = null;
        setupDayProfilesComplete = null;
        assignDayProfilesComplete = null;
        if (calendar == null) {
            this.nameTextBox.setText("");
            this.descriptionTextBox.setText("");
            this.activeBox.setValue(false);
        } else {
            this.nameTextBox.setText(calendar.getName());
            this.descriptionTextBox.setText(calendar.getDescription());
            this.activeBox.setValue(calendar.getRecordStatus().equals(RecordStatus.ACT));
        }

    }

    private void mapFormToData() {
        if (calendar==null) {
            calendar = new TouCalendarData();
        }
        calendar.setName(nameTextBox.getText());
        calendar.setDescription(descriptionTextBox.getText());
        if (readOnly) {
            activeBox.setEnabled(false);
        } else {
            if (assignSeasonsComplete != null && setupDayProfilesComplete != null && assignDayProfilesComplete != null) {
                activeBox.setEnabled(assignSeasonsComplete && setupDayProfilesComplete && assignDayProfilesComplete);
             } else {
                 activeBox.setEnabled(false);
             }
        }

        if (activeBox.isEnabled() && activeBox.getValue()) {
            calendar.setRecordStatus(RecordStatus.ACT);
        } else {
            calendar.setRecordStatus(RecordStatus.DAC);
        }
        calendar.setCalendarSeasons(assignDayProfiles.getCalendarSeasons());

    }

    private boolean validate() {
        boolean validated = true;
        if (!ClientValidatorUtil.getInstance().validateField(calendar, "name", nameElement)) {
            validated = false;
        }

        if (!ClientValidatorUtil.getInstance().validateField(calendar, "description", descriptionElement)) {
            validated = false;
        }
        return validated;
    }


    public void clearErrorMessages() {
        nameElement.clearErrorMsg();
        descriptionElement.clearErrorMsg();
        activeElement.clearErrorMsg();
    }



    @Override
    public void onArrival(Place place) {
        logger.info("Arrived at Calendars...");
        refreshTable();
    }

    @Override
    public void onLeaving() {
    }

    @Override
    public void onSelect() {
    }

    @Override
    public void onClose() {
    }

    @Override
    public void setAssignSeasonsComplete(boolean complete) {
        this.assignSeasonsComplete = complete;
        if (!complete) {
            monthsCompleteHeader.setText("("+MessagesUtil.getInstance().getMessage("calendar.incomplete")+")");
            monthsCompleteHeader.setStylePrimaryName("error");
        } else {
            monthsCompleteHeader.setText("("+MessagesUtil.getInstance().getMessage("calendar.complete")+")");
            monthsCompleteHeader.setStylePrimaryName("success");
        }
        if (assignSeasonsComplete != null && setupDayProfilesComplete != null && assignDayProfilesComplete != null) {
            resetRecordStatus();
        }

    }

    @Override
    public void setSetupDayProfilesComplete(boolean complete) {
        this.setupDayProfilesComplete = complete;
        if (!complete) {
            dpCompleteHeader.setText("("+MessagesUtil.getInstance().getMessage("calendar.incomplete")+")");
            dpCompleteHeader.setStylePrimaryName("error");
        } else {
            dpCompleteHeader.setText("("+MessagesUtil.getInstance().getMessage("calendar.complete")+")");
            dpCompleteHeader.setStylePrimaryName("success");
        }
        if (assignSeasonsComplete != null && setupDayProfilesComplete != null && assignDayProfilesComplete != null) {
            resetRecordStatus();
        }

    }

    @Override
    public void setAssignDayProfilesComplete(boolean complete) {
        this.assignDayProfilesComplete = complete;
        if (!complete) {
            cspCompleteHeader.setText("("+MessagesUtil.getInstance().getMessage("calendar.incomplete")+")");
            cspCompleteHeader.setStylePrimaryName("error");
        } else {
            cspCompleteHeader.setText("("+MessagesUtil.getInstance().getMessage("calendar.complete")+")");
            cspCompleteHeader.setStylePrimaryName("success");
        }
        if (assignSeasonsComplete != null && setupDayProfilesComplete != null && assignDayProfilesComplete != null) {
            resetRecordStatus();
        }
    }

    private void resetRecordStatus() {
        if (assignSeasonsComplete && setupDayProfilesComplete && assignDayProfilesComplete) {
            activeBox.setEnabled(!readOnly);
        } else {
            activeBox.setEnabled(false);
            if (calendar.getRecordStatus().equals(RecordStatus.ACT)) {
                calendar.setRecordStatus(RecordStatus.DAC);
                if (clientFactory != null) {
                    clientFactory.getCalendarRpc().updateCalendar(calendar,  new  ClientCallback<TouCalendarData>() {

                        @Override
                        public void onSuccess(TouCalendarData result) {
                            setCalendar(result);
                            refreshTable();
                        }
                    });
                }
            }
        }
    }

    @Override
    public boolean handles(Place place) {
        if (place instanceof CalendarPlace
                && (CalendarPlace.CALENDARS_PLACE.getCalendarPlaceType().equals(((CalendarPlace) place).getCalendarPlaceType()))) {
            return true;
        } else {
            return false;
        }
    }

    @Override
    public void onResize() {
        new Timer() {
            @Override
            public void run() {

            }
        }.schedule(100);
    }

    @Override
    public String getPricingStructureNames() {
        return pricingstructures;
    }

    @Override
    public TouCalendar getCalendar() {
        return calendar;
    }


}
