package za.co.ipay.metermng.client.event;

import com.google.gwt.event.shared.GwtEvent;

public class OpenUserGroupEvent extends GwtEvent<OpenUserGroupEventHandler> {

    public static Type<OpenUserGroupEventHandler> TYPE = new Type<OpenUserGroupEventHandler>();

    @Override
    public Type<OpenUserGroupEventHandler> getAssociatedType() {
        return TYPE;
    }

    @Override
    protected void dispatch(OpenUserGroupEventHandler handler) {
        handler.openUserGroup(this);
    }
}