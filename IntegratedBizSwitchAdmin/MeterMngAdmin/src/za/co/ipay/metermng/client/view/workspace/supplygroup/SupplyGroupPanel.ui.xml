<!DOCTYPE ui:UiBinder SYSTEM "http://dl.google.com/gwt/DTD/xhtml.ent">
<ui:UiBinder xmlns:ui="urn:ui:com.google.gwt.uibinder" 
             xmlns:g="urn:import:com.google.gwt.user.client.ui"
             xmlns:p1="urn:import:za.co.ipay.gwt.common.client.form"
             xmlns:p2="urn:import:com.google.gwt.user.datepicker.client"
             xmlns:p3="urn:import:za.co.ipay.gwt.common.client.widgets">

  <ui:style>
    
  </ui:style>

  <ui:with field="msg" type="za.co.ipay.metermng.client.i18n.UiMessages" />

  <g:FlowPanel>
    <p1:FormRowPanel>
      <p1:FormElement ui:field="nameElement" debugId="nameElement" labelText="{msg.getSupplyGroupName}:" required="true">
        <g:TextBox ui:field="nameBox" debugId="nameBox" maxLength="100" visibleLength="20" />
      </p1:FormElement>
      <p1:FormElement ui:field="codeElement" debugId="codeElement" labelText="{msg.getSupplyGroupCode}:" required="true">
        <p1:IntegerValueBox ui:field="codeBox" debugId="codeBox" maxLength="6" visibleLength="10" styleName="gwt-TextBox" />
      </p1:FormElement>
      <p1:FormElement ui:field="isDefaultElement" debugId="isDefaultElement" labelText="{msg.isDefaultSupplyGroup}:">
        <g:CheckBox ui:field="isDefaultBox" debugId="isDefaultBox" />
      </p1:FormElement>
    </p1:FormRowPanel>
    <p1:FormRowPanel>
      <p1:FormElement ui:field="keyRevisionNumberElement" debugId="keyRevisionNumberElement" labelText="{msg.getSupplyGroupKeyRevisionNumber}:" required="true">
        <g:ListBox ui:field="keyRevisionNumberBox" debugId="keyRevisionNumberBox" styleName="wide" />
      </p1:FormElement>
    </p1:FormRowPanel>
     <p1:FormRowPanel>
        <p1:FormElement ui:field="kmcExpiryDateElement" debugId="kmcExpiryDateElement" labelText="{msg.getSupplyGroupKmcExpiry}:" helpMsg="{msg.getSupplyGroupKmcExpiryHelp}">
          <p2:DateBox ui:field="kmcExpiryDateBox" debugId="kmcExpiryDateBox" styleName="gwt-TextBox" />
        </p1:FormElement>
      </p1:FormRowPanel>
    <p1:FormRowPanel>
      <p1:FormElement ui:field="activeElement" labelText="{msg.getSupplyGroupActive}:">
        <g:CheckBox ui:field="activeBox" debugId="activeBox" />
      </p1:FormElement>
      <p1:FormElement labelText="{msg.getSgcBaseDateLabel}:" helpMsg="{msg.getSgcBaseDateLabelHelp}">
            <g:Label ui:field="baseDateLabel"></g:Label>
      </p1:FormElement>
    </p1:FormRowPanel>
	<p1:FormRowPanel>
	    <p1:FormElement ui:field="targetSupGrCdeElement" helpMsg="{msg.getSupplyGroupTargetLabelHelp}" labelText="{msg.getSupplyGroupTargetLabel}">
	        <p3:IpayListBox visibleItemCount="1" ui:field="lstbxTargetSupGrCde" styleName="gwt-ListBox-ipay" multipleSelect="false" />
	    </p1:FormElement>
    </p1:FormRowPanel>

    <g:Label ui:field="isInUseByMeterLbl" debugId="isInUseByMeterLbl" text="{msg.getIsInUseByMeterLbl}" styleName="warn" visible="false" width="400px" />

  </g:FlowPanel>

</ui:UiBinder> 
