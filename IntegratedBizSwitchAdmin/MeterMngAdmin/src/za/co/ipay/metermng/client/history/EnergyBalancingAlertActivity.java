package za.co.ipay.metermng.client.history;

import za.co.ipay.metermng.client.event.OpenEnergyBalancingEvent;
import za.co.ipay.metermng.client.factory.ClientFactory;

import com.google.gwt.activity.shared.AbstractActivity;
import com.google.gwt.event.shared.EventBus;
import com.google.gwt.user.client.ui.AcceptsOneWidget;

public class EnergyBalancingAlertActivity extends AbstractActivity {

    private EnergyBalancingAlertPlace energyBalancingAlertPlace;
    private ClientFactory clientFactory;
    
    public EnergyBalancingAlertActivity(EnergyBalancingAlertPlace place, ClientFactory clientFactory) {
        super();
        this.energyBalancingAlertPlace = place;
        this.clientFactory = clientFactory;
    }
    
    @Override
    public void start(AcceptsOneWidget panel, EventBus eventBus) {
        clientFactory.getEventBus().fireEvent(new OpenEnergyBalancingEvent(energyBalancingAlertPlace.getMeter()));
    }
}
