package za.co.ipay.metermng.client.event;

import com.google.gwt.event.shared.GwtEvent;

public class OpenImportDataEvent extends GwtEvent<OpenImportDataEventHandler> {

    public static Type<OpenImportDataEventHandler> TYPE = new Type<OpenImportDataEventHandler>();
    
    @Override
    public Type<OpenImportDataEventHandler> getAssociatedType() {
        return TYPE;
    }

    @Override
    protected void dispatch(OpenImportDataEventHandler handler) {
        handler.openImportData(this);
    }

}