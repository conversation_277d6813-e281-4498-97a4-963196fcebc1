package za.co.ipay.metermng.client.history;

import za.co.ipay.metermng.client.event.AddMeterReadingsEvent;
import za.co.ipay.metermng.client.factory.ClientFactory;

import com.google.gwt.activity.shared.AbstractActivity;
import com.google.gwt.event.shared.EventBus;
import com.google.gwt.user.client.ui.AcceptsOneWidget;

public class AddMeterReadingsActivity extends AbstractActivity {

    private AddMeterReadingsPlace addMeterReadingsPlace;
    private ClientFactory clientFactory;
    
    public AddMeterReadingsActivity(AddMeterReadingsPlace addMeterReadingsPlace, ClientFactory clientFactory) {
        super();
        this.addMeterReadingsPlace = addMeterReadingsPlace;
        this.clientFactory = clientFactory;
    }
    
    @Override
    public void start(AcceptsOneWidget panel, EventBus eventBus) {
        clientFactory.getEventBus().fireEvent(
                new AddMeterReadingsEvent(addMeterReadingsPlace.getMeterType(), addMeterReadingsPlace.getPaymentMode()));
    }
}
