package za.co.ipay.metermng.client.view.workspace.importfile;

import java.util.logging.Logger;

import com.google.gwt.core.client.GWT;
import com.google.gwt.place.shared.Place;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.user.client.ui.DeckLayoutPanel;
import com.google.gwt.user.client.ui.Widget;

import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.history.ImportFilePlace;
import za.co.ipay.metermng.client.view.component.importfile.ImportFileItemView;
import za.co.ipay.metermng.client.view.component.importfile.ImportFileView;
import za.co.ipay.metermng.client.view.workspace.BaseWorkspace;
import za.co.ipay.metermng.shared.dto.importfile.ImportFileDto;

public class ImportFileWorkspaceView extends BaseWorkspace {
    
    @UiField DeckLayoutPanel deckPanel;
    
    @UiField(provided=true) ImportFileView importFileView;
    
    @UiField(provided=true) ImportFileItemView importFileItemView;
    
    private Logger logger = Logger.getLogger("ImportFileWorkspaceView");
        
    private static ImportFileWorkspaceViewUiBinder uiBinder = GWT.create(ImportFileWorkspaceViewUiBinder.class);

    interface ImportFileWorkspaceViewUiBinder extends UiBinder<Widget, ImportFileWorkspaceView> {
    }
    
    public ImportFileWorkspaceView(ClientFactory clientFactory, ImportFilePlace place) {
        this.clientFactory = clientFactory;
        importFileView = new ImportFileView(this, clientFactory);
        importFileItemView = new ImportFileItemView(this, clientFactory);
        
        initWidget(uiBinder.createAndBindUi(this));
        setPlaceString(ImportFilePlace.getPlaceAsString(place));
        setHeaderText(MessagesUtil.getInstance().getMessage("import.upload.header"));
        initUi();
    }
    
    private void initUi() {                
        deckPanel.showWidget(importFileView);
    }
    
    public void goToImportFile() {
        importFileView.reset();
        deckPanel.showWidget(0);
        deckPanel.animate(getAnimationTime());
    }
    
    public void goToImportFileItemView(ImportFileDto importFileDto) {
        importFileItemView.setImportFileDto(importFileDto);
        deckPanel.showWidget(1);
        deckPanel.animate(getAnimationTime());
    }
    
    @Override
    public void onLeaving() {
    }
    
    @Override
    public void onSelect() {
    }

    @Override
    public void onArrival(Place place) {
        logger.info("Arrived at ImportFileWorkSpaceView: "+place);
        importFileView.onArrival(place);
    }

    @Override
    public void onClose() {
        onLeaving();
    }

    @Override
    public boolean handles(Place place) {
        return place instanceof ImportFilePlace;
    }
}