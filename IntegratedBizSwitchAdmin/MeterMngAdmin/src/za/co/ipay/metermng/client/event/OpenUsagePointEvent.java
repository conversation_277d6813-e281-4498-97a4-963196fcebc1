package za.co.ipay.metermng.client.event;

import za.co.ipay.metermng.client.history.AbstractUsagePointPlace;

import com.google.gwt.event.shared.GwtEvent;

public class OpenUsagePointEvent extends GwtEvent<OpenUsagePointEventHandler> {

    public static Type<OpenUsagePointEventHandler> TYPE = new Type<OpenUsagePointEventHandler>();
    public AbstractUsagePointPlace place;

    public OpenUsagePointEvent(AbstractUsagePointPlace place) {
        this.place = place;
    }
    
    public AbstractUsagePointPlace getPlace() {
        return place;
    }

    @Override
    public Type<OpenUsagePointEventHandler> getAssociatedType() {
        return TYPE;
    }

    @Override
    protected void dispatch(OpenUsagePointEventHandler handler) {
        handler.openUsagePoint(this);
    }
    
}
