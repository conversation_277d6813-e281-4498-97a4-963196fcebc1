<!DOCTYPE ui:UiBinder SYSTEM "http://dl.google.com/gwt/DTD/xhtml.ent">
<ui:UiBinder xmlns:ui="urn:ui:com.google.gwt.uibinder" 
   xmlns:g="urn:import:com.google.gwt.user.client.ui"
   xmlns:p1="urn:import:za.co.ipay.gwt.common.client.form"
   xmlns:w="urn:import:za.co.ipay.gwt.common.client.widgets" >
  <ui:style>
    
  </ui:style>
  
  <ui:with field="msg" type="za.co.ipay.metermng.client.i18n.UiMessages" />

  <g:FlowPanel>
    <p1:FormRowPanel>
      <p1:FormElement ui:field="acsNameElement" debugId="acsNameElement" labelText="{msg.getAuxChargeScheduleName}:" helpMsg="{msg.getAuxChargeScheduleNameHelp}" required="true">
        <g:TextBox ui:field="acsNameTextBox" debugId="acsNameTextBox" visibleLength="17" />
      </p1:FormElement>
      <p1:FormElement ui:field="minAmtElement" labelText="{msg.getAuxChargeScheduleMinAmount}:" helpMsg="{msg.getAuxChargeScheduleMinAmountHelp}">
        <w:CurrencyTextBox ui:field="minAmtTextBox" debugId="minAmtTextBox" maxLength="32" visibleLength="3" />
      </p1:FormElement>
      <p1:FormElement ui:field="maxAmtElement" labelText="{msg.getAuxChargeScheduleMaxAmount}:" helpMsg="{msg.getAuxChargeScheduleMaxAmountHelp}">
        <w:CurrencyTextBox ui:field="maxAmtTextBox" debugId="maxAmtTextBox" maxLength="32" visibleLength="3" />
      </p1:FormElement>
    </p1:FormRowPanel>
    <p1:FormRowPanel>
      <p1:FormElement ui:field="vendPortionElement" labelText="{msg.getAuxChargeScheduleVendPortion}:" helpMsg="{msg.getAuxChargeScheduleVendPortionHelp}">
        <w:PercentageTextBox ui:field="vendPortionTextBox" debugId="vendPortionTextBox" maxLength="32" visibleLength="3" />
      </p1:FormElement>
      <p1:FormElement ui:field="currentPortionElement" labelText="{msg.getAuxChargeScheduleCurrPortion}:" helpMsg="{msg.getAuxChargeScheduleCurrPortionHelp}">
         <w:PercentageTextBox ui:field="currentPortionTextBox" debugId="currentPortionTextBox" maxLength="32" visibleLength="3" />
      </p1:FormElement>
    </p1:FormRowPanel>
    <p1:FormRowPanel>
      <g:HTML ui:field="instalmentLabel" styleName="gwt-Label-bold-left" visible="false"/> 
    </p1:FormRowPanel>
    <p1:FormRowPanel>  
      <p1:FormElement ui:field="chargeCycleElement" labelText="{msg.getAuxChargeSchedCycleLabel}:" helpMsg="{msg.getAuxChargeSchedCycleLabelHelp}">
         <g:ListBox ui:field="chargeCycleBox" title="{msg.getAuxChargeSchedCycleLabel}" styleName="gwt-TextBox" />
      </p1:FormElement>
      <p1:FormElement ui:field="chargeAmtElement" labelText="{msg.getAuxChargeSchedCycleAmountLabel}:" helpMsg="{msg.getAuxChargeSchedCycleAmountLabelHelp}">
        <w:CurrencyTextBox ui:field="chargeAmtTextBox" debugId="chargeAmtTextBox" maxLength="32" visibleLength="10" />
      </p1:FormElement>
      <g:Label ui:field="error" debugId="errorLabel" visible="false" styleName="error"></g:Label>
    </p1:FormRowPanel>
    <p1:FormRowPanel>
      <p1:FormElement ui:field="activeElement" labelText="{msg.getAuxChargeScheduleActive}:" helpMsg="{msg.getAuxChargeScheduleActiveHelp}">
        <g:CheckBox ui:field="activeCheckBox" debugId="activeCheckBox" />
      </p1:FormElement>
    </p1:FormRowPanel>
  </g:FlowPanel>

</ui:UiBinder> 