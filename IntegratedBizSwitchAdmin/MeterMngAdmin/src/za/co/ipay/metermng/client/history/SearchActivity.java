package za.co.ipay.metermng.client.history;

import java.util.logging.Logger;

import za.co.ipay.metermng.client.event.SearchEvent;
import za.co.ipay.metermng.client.factory.ClientFactory;

import com.google.gwt.activity.shared.AbstractActivity;
import com.google.gwt.event.shared.EventBus;
import com.google.gwt.user.client.ui.AcceptsOneWidget;

public class SearchActivity extends AbstractActivity {

    private ClientFactory clientFactory;
    private SearchPlace thePlace;
    private static Logger logger = Logger.getLogger(SearchActivity.class.getName());

    public SearchActivity(SearchPlace place, ClientFactory clientFactory) {
        super();
        this.clientFactory = clientFactory;
        thePlace = place;
    }

    @Override
    public void start(AcceptsOneWidget panel, EventBus eventBus) {
        logger.info("Opening place: " + thePlace.getSearchType());
        clientFactory.getEventBus().fireEvent(
                new SearchEvent(thePlace.getSearchType(), thePlace.getDataType(), thePlace.getSearchText()));
    }
}
