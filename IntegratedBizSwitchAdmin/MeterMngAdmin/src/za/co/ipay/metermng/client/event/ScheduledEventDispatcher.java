package za.co.ipay.metermng.client.event;

import com.google.gwt.user.client.Timer;
import com.google.web.bindery.event.shared.EventBus;
import com.google.web.bindery.event.shared.Event;
import za.co.ipay.metermng.client.view.component.tariff.PricingStructureView;

import java.util.Date;
import java.util.logging.Logger;

/**
 * Utility to schedule a GWT EventBus event to be fired at a specific time.
 */
public class ScheduledEventDispatcher {

    private final EventBus eventBus;
    private static final Logger LOGGER = Logger.getLogger(ScheduledEventDispatcher.class.getName());

    public ScheduledEventDispatcher(EventBus eventBus) {
        this.eventBus = eventBus;
    }

    /**
     * Schedules an event to be fired at the specified date/time.
     *
     * @param event the event to fire
     * @param targetTime the exact time to fire the event
     */
    public void schedule(final Event<?> event, Date targetTime) {
        LOGGER.info("Scheduling event: " + event + " for " + targetTime);
        long delayMs = targetTime.getTime() - System.currentTimeMillis();
        if (delayMs <= 0) {
            // Fire immediately if time is in the past
            eventBus.fireEvent(event);
        } else if (delayMs < Integer.MAX_VALUE) {
            new Timer() {
                @Override
                public void run() {
                    eventBus.fireEvent(event);
                }
            }.schedule((int) delayMs);
        }
    }
}
