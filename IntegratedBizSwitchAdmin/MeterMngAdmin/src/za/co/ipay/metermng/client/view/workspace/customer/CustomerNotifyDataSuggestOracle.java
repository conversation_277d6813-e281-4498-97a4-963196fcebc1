package za.co.ipay.metermng.client.view.workspace.customer;

import java.util.Collection;
import java.util.Collections;
import java.util.LinkedList;
import java.util.List;
import java.util.logging.Logger;

import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.rpc.callback.ClientCallback;
import za.co.ipay.metermng.shared.dto.customer.CustomerNotifyData;

import com.google.gwt.user.client.ui.SuggestOracle;

public class CustomerNotifyDataSuggestOracle extends SuggestOracle {

    private ClientFactory clientFactory;
    private String notifyType;
    
    private static Logger logger = Logger.getLogger(CustomerNotifyDataSuggestOracle.class.getName());
    
    public CustomerNotifyDataSuggestOracle(ClientFactory clientFactory, String notifyType) {
        this.clientFactory = clientFactory;
        this.notifyType = notifyType;
    }

    @Override
    public void requestSuggestions(final Request request, final Callback callback) {
        if (request.getQuery().trim().length() > 1) {
            logger.info("Getting customerNotify suggestions: "+request.getQuery());
            clientFactory.getCustomerRpc().getCustomerNotifySuggestions(request.getQuery(), request.getLimit(), notifyType,
                    new ClientCallback<List<CustomerNotifyData>>() {
                        @Override
                        public void onSuccess(List<CustomerNotifyData> customers) {
                            Collection<Suggestion> result = new LinkedList<Suggestion>();
                            for (CustomerNotifyData customer : customers) {
                                result.add(new CustomerNotifySuggestion(customer));
                            }
                            Response response = new Response(result);
                            callback.onSuggestionsReady(request, response);
                        }                
                    }); 
        } else {
            Response response = new Response(Collections.<Suggestion> emptyList());
            callback.onSuggestionsReady(request, response);
        }
    }
}
