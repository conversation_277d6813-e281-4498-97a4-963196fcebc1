package za.co.ipay.metermng.client.view.workspace;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.logging.Logger;

import com.google.gwt.core.client.Scheduler;
import com.google.gwt.core.client.Scheduler.ScheduledCommand;
import com.google.gwt.place.shared.Place;
import com.google.gwt.user.client.ui.Label;

import za.co.ipay.gwt.common.client.Dialogs;
import za.co.ipay.gwt.common.client.factory.ResourcesFactoryUtil;
import za.co.ipay.gwt.common.client.handler.ConfirmHandler;
import za.co.ipay.gwt.common.client.resource.Messages;
import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.gwt.common.client.workspace.Workspace;
import za.co.ipay.gwt.common.client.workspace.WorkspaceCreateCallback;
import za.co.ipay.gwt.common.client.workspace.WorkspaceFactory;
import za.co.ipay.gwt.common.client.workspace.WorkspaceNotification;
import za.co.ipay.gwt.common.client.workspace.WorkspaceNotification.NotificationType;
import za.co.ipay.gwt.common.shared.exception.AccessControlException;
import za.co.ipay.gwt.common.shared.exception.ServiceException;
import za.co.ipay.metermng.client.event.EndDeviceStoreUpdatedEvent;
import za.co.ipay.metermng.client.event.LinkToEvent;
import za.co.ipay.metermng.client.event.LinkToEventHandler;
import za.co.ipay.metermng.client.event.UsagePointUpdatedEvent;
import za.co.ipay.metermng.client.event.UsagePointUpdatedEventHandler;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.history.AbstractUsagePointPlace;
import za.co.ipay.metermng.client.history.CustomerPlace;
import za.co.ipay.metermng.client.history.MeterPlace;
import za.co.ipay.metermng.client.history.UsagePointPlace;
import za.co.ipay.metermng.client.resource.image.MediaResource;
import za.co.ipay.metermng.client.resource.image.MediaResource.MediaResourceUtil;
import za.co.ipay.metermng.client.rpc.callback.ClientCallback;
import za.co.ipay.metermng.client.view.component.AssignChannelReadingsComponent;
import za.co.ipay.metermng.client.view.component.AssignChannelReadingsDialogueBox;
import za.co.ipay.metermng.client.view.component.AssignMeterDialogueBox;
import za.co.ipay.metermng.client.view.component.LinkToProcess;
import za.co.ipay.metermng.client.view.component.pricingstructure.UpWorkSpaceFactoryValidatePStoMM;
import za.co.ipay.metermng.datatypes.RecordStatus;
import za.co.ipay.metermng.mybatis.generated.model.AppSetting;
import za.co.ipay.metermng.mybatis.generated.model.SpecialActionReasonsLog;
import za.co.ipay.metermng.mybatis.generated.model.SpecialActions;
import za.co.ipay.metermng.mybatis.generated.model.UpPricingStructure;
import za.co.ipay.metermng.mybatis.generated.model.UsagePoint;
import za.co.ipay.metermng.shared.MeterMngStatics;
import za.co.ipay.metermng.shared.SpecialActionsData;
import za.co.ipay.metermng.shared.appsettings.AppSettings;
import za.co.ipay.metermng.shared.dto.CustomerAgreementData;
import za.co.ipay.metermng.shared.dto.HistoryData;
import za.co.ipay.metermng.shared.dto.LocationData;
import za.co.ipay.metermng.shared.dto.MdcChannelReadingsDto;
import za.co.ipay.metermng.shared.dto.MeterData;
import za.co.ipay.metermng.shared.dto.PSDto;
import za.co.ipay.metermng.shared.dto.UpPricingStructureData;
import za.co.ipay.metermng.shared.dto.UsagePointData;
import za.co.ipay.metermng.shared.dto.usagepoint.MeterUpMdcChannelInfo;
import za.co.ipay.metermng.shared.exception.CustomerNotFoundException;
import za.co.ipay.metermng.shared.exception.MeterNotFoundException;
import za.co.ipay.metermng.shared.exception.UsagePointNotFoundException;

public class UsagePointWorkspaceFactory implements WorkspaceFactory, AssignChannelReadingsComponent {
    
    private ClientFactory clientFactory;
    private static Logger logger = Logger.getLogger(UsagePointWorkspaceFactory.class.getName());    
    
    private ArrayList<AppSetting> customFieldList;
    private ArrayList<AppSetting> usagePointPageCustomFieldsList;
    private Long specialActionsIdForDeactivateUsagePoint;
    private Long specialActionsIdForActivateUsagePoint;
    
    private UsagePointWorkspaceFactoryDataForUpdate upWorkspaceFactoryData;     // when have to get init readings from activate UP in MMA UsagePointWorkspaceFactory, need extra data to complete save!

    public UsagePointWorkspaceFactory(final ClientFactory clientFactory) {
        logger.info("Constructing UsagePointWorkspaceFactory");
        this.clientFactory = clientFactory;
        clientFactory.getWorkspaceContainer().register(this);
        
        clientFactory.getEventBus().addHandler(UsagePointUpdatedEvent.TYPE, new UsagePointUpdatedEventHandler() {
            @Override
            public void updateUsagePoint(final UsagePointUpdatedEvent event) {
                logger.info("UsagePointUpdatedEvent received: " + this);
                final UsagePointData usagePointDataExEvent = event.getUsagePointData();
                int updateType = event.getUpdateType();
                switch (updateType) {
                case UsagePointUpdatedEvent.SAVE_USAGE_POINT:
                    logger.info("Save usage point: " + event.getUsagePointData());

                    clientFactory.getUsagePointRpc().updateUsagePointComponent(usagePointDataExEvent, usagePointDataExEvent.getServiceLocation(), usagePointDataExEvent.getUsagepointLocation(), event.getChannelReadingsList(),
                            new ClientCallback<UsagePointData>() {

                        @Override
                        public void onSuccess(UsagePointData result) {

                            UsagePointPlace newUsagePointPlace = new UsagePointPlace(result.getName());
                            logger.info("Save UsagePoint: newUsagePointPlace=" + newUsagePointPlace.getUsagePointName());

                            if (event.getUsagePointWorkspaceView().getPlace().isNew() 
                                    || event.getUsagePointWorkspaceView().getPlace() instanceof UsagePointPlace) {
                                event.getUsagePointWorkspaceView().setPlace(newUsagePointPlace);
                                event.getUsagePointWorkspaceView().setUsagePointData(result);
                                clientFactory.getWorkspaceContainer().refreshWorkspace(newUsagePointPlace);
                            } else {
                                event.getUsagePointWorkspaceView().setUsagePointData(result);
                                clientFactory.getWorkspaceContainer().refreshWorkspace(event.getUsagePointWorkspaceView().getPlace());
                            }

                            final UsagePointData savedUsagePointData = result;
                            if (savedUsagePointData.getRecordStatus() != RecordStatus.ACT 
                                    && savedUsagePointData.getId() != null 
                                    && savedUsagePointData.getMeterId() != null
                                    && savedUsagePointData.getCustomerAgreementId() != null 
                                    && savedUsagePointData.getCustomerAgreementData().getRecordStatus().equals(RecordStatus.ACT)
                                    && !event.isUserDeactivatedUP()) {

                                String savedUsagePointMsg = MessagesUtil.getInstance().getMessage("usagepoint.saved", new String[]{savedUsagePointData.getName()});
                                queryActivateAndSaveUsagePoint(
                                        savedUsagePointData,
                                        usagePointDataExEvent.getServiceLocation(),
                                        usagePointDataExEvent.getUsagepointLocation(),
                                        event.getUsagePointWorkspaceView(),
                                        new String[] {savedUsagePointMsg, MessagesUtil.getInstance().getMessage("usagepointworkspace.assign.usage.point.activated")},
                                        new String[] {savedUsagePointMsg, MessagesUtil.getInstance().getMessage("usagepointworkspace.save.usage.point.inactive")}
                                        );

                            } else {
                                Dialogs.displayInformationMessage(MessagesUtil.getInstance().getMessage("usagepoint.saved", new String[]{savedUsagePointData.getName()}), MediaResourceUtil.getInstance().getInformationIcon());
                                sendNotification(savedUsagePointData);
                                sendReloadNotification();
                            }
                        }

                        @Override
                        public void onFailureClient(Throwable caught) {
                            logger.info("UsagePointUpdatedEvent.SAVE_USAGE_POINT: onFailure: caught.message= " + caught.getMessage());
                            if (caught.getMessage() != null && caught.getMessage().contains("installdate.before.removaldate")) {
                                usagePointDataExEvent.setInstallationDate(null);                             //(see UsagePointComponent mapDataToForm)
                                event.getUsagePointWorkspaceView().setUsagePointData(usagePointDataExEvent);
                            }
                        }
                    });
                    break;
                case UsagePointUpdatedEvent.FETCH_USAGE_POINT:
                    logger.info("Fetch usagePoint");

                    UsagePointPlace newUsagePointPlace = new UsagePointPlace(usagePointDataExEvent.getName());
                    logger.info("Fetch UsagePoint: newUsagePointPlace=" + newUsagePointPlace.getUsagePointName());

                    if(clientFactory.getWorkspaceContainer().isWorkspaceOpen(newUsagePointPlace)) {
                        clientFactory.getWorkspaceContainer().closeWorkspaceNow(newUsagePointPlace);
                    }

                    if (event.getUsagePointWorkspaceView().getPlace().isNew() 
                            || event.getUsagePointWorkspaceView().getPlace() instanceof UsagePointPlace) {
                        event.getUsagePointWorkspaceView().setPlace(newUsagePointPlace);
                        event.getUsagePointWorkspaceView().setUsagePointData(usagePointDataExEvent);
                        clientFactory.getWorkspaceContainer().refreshWorkspace(newUsagePointPlace);
                    } else {
                        event.getUsagePointWorkspaceView().setUsagePointData(usagePointDataExEvent);
                        clientFactory.getWorkspaceContainer().refreshWorkspace(event.getUsagePointWorkspaceView().getPlace());
                    }

                    if (usagePointDataExEvent.getRecordStatus() != RecordStatus.ACT 
                            && usagePointDataExEvent.getId() != null 
                            && usagePointDataExEvent.getMeterId() != null
                            && usagePointDataExEvent.getCustomerAgreementId() != null 
                            && usagePointDataExEvent.getCustomerAgreementData().getRecordStatus().equals(RecordStatus.ACT)) {

                        queryActivateAndSaveUsagePoint(usagePointDataExEvent, usagePointDataExEvent.getServiceLocation(), usagePointDataExEvent.getUsagepointLocation(), event.getUsagePointWorkspaceView(),
                                new String[] {MessagesUtil.getInstance().getMessage("usagepointworkspace.assign.usage.point.activated")},
                                new String[] {MessagesUtil.getInstance().getMessage("usagepointworkspace.save.usage.point.inactive")});
                    } else{
                        if (usagePointDataExEvent.getMeterId() != null
                                || usagePointDataExEvent.getCustomerAgreementId() != null ) {
                            Dialogs.displayInformationMessage(MessagesUtil.getInstance().getMessage("usagepoint.saved", new String[]{usagePointDataExEvent.getName()}),
                                    MediaResourceUtil.getInstance().getInformationIcon());
                            sendNotification(usagePointDataExEvent);
                            sendReloadNotification();
                        }
                    } 
                    break;
                case UsagePointUpdatedEvent.UNASSIGN_CUSTOMER:
                case UsagePointUpdatedEvent.WRITEOFF_CHARGES:
                    UsagePointData usagePointData = event.getUsagePointData();
                    logger.info("Unassigning: " + usagePointData);
                    UsagePointWorkspaceView usagePointWorkspaceView = event.getUsagePointWorkspaceView();
                    if (updateType == UsagePointUpdatedEvent.WRITEOFF_CHARGES) {
                        Label lblAssign = usagePointWorkspaceView.getCustomerComponent().getLblAssign();
                        usagePointWorkspaceView.showViewUsagePointChargeDialogBox(usagePointData,
                                lblAssign.getAbsoluteLeft() + lblAssign.getOffsetWidth() - 250,
                                lblAssign.getAbsoluteTop() + lblAssign.getOffsetHeight(),
                                UsagePointWorkspaceFactory.this);
                    } else {
                        unassignCustomerFromUsagePoint(usagePointWorkspaceView, usagePointData);
                    }
                    break;
                case UsagePointUpdatedEvent.ASSIGN_CUSTOMER:
                    logger.info("Assign customer event");
                    assignCustomer(event.getUsagePointWorkspaceView(), event.getUsagePointData(), event.getAssignToCustomerId());
                    break;
                case UsagePointUpdatedEvent.SAVE_METER:
                    logger.info("Save meter");
                    logger.info("isAddNewMeterToExistingUsagePoint= " + event.isAddNewMeterToExistingUsagePoint());

                    if (!event.isAddNewMeterToExistingUsagePoint() && event.getUsagePointData().getId() != null && event.getUsagePointData().getMeterData().getRecordStatus() != RecordStatus.ACT) {
                        event.getUsagePointData().setRecordStatus(RecordStatus.DAC);
                        logger.info("SAVE_METER: updateUsagePointComponent recordStatus to DAC");

                        clientFactory.getUsagePointRpc().updateUsagePointComponent(event.getUsagePointData(), event.getUsagePointData().getServiceLocation(), event.getUsagePointData().getUsagepointLocation(), new ClientCallback<UsagePointData>() {
                            @Override
                            public void onFailure(Throwable caught) {
                                Dialogs.displayErrorMessage(MessagesUtil.getInstance().getMessage("usagepointworkspace.meter.saved.usagepoint.deactivation.failed", new String[]{event.getUsagePointData().getMeterData().getMeterNum()}), MediaResourceUtil.getInstance().getErrorIcon(), event.getLeft(), event.getTop(), MessagesUtil.getInstance().getMessage("button.close"));
                            }
                            @Override
                            public void onSuccess(UsagePointData result) {
                                Dialogs.displayInformationMessage(MessagesUtil.getInstance().getMessage("usagepointworkspace.meter.saved.usagepoint.deactivated", new String[]{event.getUsagePointData().getMeterData().getMeterNum()}), MediaResourceUtil.getInstance().getInformationIcon(), event.getLeft(), event.getTop(), null);
                                event.getUsagePointWorkspaceView().setUsagePointData(event.getUsagePointData());
                                sendNotification(result);
                                sendReloadNotification();
                            }
                        });
                    } else {
                        if (!event.isAddNewMeterToExistingUsagePoint()) {
                            if (event.getUsagePointWorkspaceView().getPlace().isNew()) {
                                MeterPlace newMeterPlace = new MeterPlace(event.getUsagePointData().getMeterData().getMeterNum());
                                event.getUsagePointWorkspaceView().setPlace(newMeterPlace);
                                event.getUsagePointWorkspaceView().setUsagePointData(event.getUsagePointData());
                                clientFactory.getWorkspaceContainer().refreshWorkspace(newMeterPlace);
                            } else {
                                event.getUsagePointWorkspaceView().setUsagePointData(event.getUsagePointData());
                            }
                            Dialogs.displayInformationMessage(MessagesUtil.getInstance().getMessage("usagepointworkspace.meter.saved", new String[]{event.getUsagePointData().getMeterData().getMeterNum()}), MediaResourceUtil.getInstance().getInformationIcon(), event.getLeft(), event.getTop(), null);
                            sendNotification(event.getUsagePointData());
                            sendReloadNotification();
                            setHistoryIds(usagePointDataExEvent);
                        } else {
                            logger.info("SAVE_METER: event.isAddNewMeterToExistingUsagePoint");
                            Dialogs.displayInformationMessage(MessagesUtil.getInstance().getMessage("usagepointworkspace.meter.saved", new String[]{event.getUsagePointData().getMeterData().getMeterNum()}), MediaResourceUtil.getInstance().getInformationIcon());
                            //now it IS isAddNewMeterToExistingUsagePoint()= true
                            clientFactory.getAppSettingRpc().getAppSettingByKey(AppSettings.USAGEPOINT_DEVICE_MOVEMENT_REFERENCE, new ClientCallback<AppSetting>(){
                                @Override
                                public void onSuccess(AppSetting result) {
                                    AssignMeterDialogueBox assignMeterDialogueBox = new AssignMeterDialogueBox(clientFactory, event.getUsagePointWorkspaceView());
                                    assignMeterDialogueBox.setUsagePointData(event.getUsagePointData());
                                    assignMeterDialogueBox.clear();
                                    assignMeterDialogueBox.setGlassEnabled(true);
                                    assignMeterDialogueBox.setAutoHideEnabled(false);   //must abort this process to stop it, so that unset the AddNewMeter switch on the usagepoint & clear the meter component!
                                    assignMeterDialogueBox.setAutoHideOnHistoryEventsEnabled(true);
                                    assignMeterDialogueBox.setAnimationEnabled(true);
                                    assignMeterDialogueBox.setInstallationDate(new Date());
                                    assignMeterDialogueBox.setMeterNum(event.getUsagePointData().getMeterData().getMeterNum());
                                    assignMeterDialogueBox.setDeviceMoveRefValue(result.getValue());
                                    assignMeterDialogueBox.setButtonTextAttach();
                                    assignMeterDialogueBox.setText(MessagesUtil.getInstance().getMessage("meter.attach")+":");
                                    assignMeterDialogueBox.center();
                                }
                            });
                        }
                    }
                    break;
                case UsagePointUpdatedEvent.ASSIGN_METER:
                    logger.info("Assign meter");
                    assignMeter(event.getUsagePointWorkspaceView(), event.getUsagePointData(),  event.getAssignToMeterNumber(), event.getChannelReadingsList());
                    break;
                case UsagePointUpdatedEvent.SAVE_CUSTOMER:
                    logger.info("Save customer");
                    if (event.getUsagePointWorkspaceView().getPlace().isNew()) {
                        CustomerPlace newCustomerPlace = new CustomerPlace(String.valueOf(event.getUsagePointData().getCustomerAgreementData().getCustomerId()));
                        event.getUsagePointWorkspaceView().setPlace(newCustomerPlace);
                        event.getUsagePointWorkspaceView().setUsagePointData(event.getUsagePointData());
                        clientFactory.getWorkspaceContainer().refreshWorkspace(newCustomerPlace);
                    } else {
                        event.getUsagePointWorkspaceView().setUsagePointData(event.getUsagePointData());
                    }
                    //now save usagepoint as well
                    if (event.getUsagePointData().getId() != null) {
                        clientFactory.getUsagePointRpc().updateUsagePointComponent(event.getUsagePointData(), event.getUsagePointData().getServiceLocation(), event.getUsagePointData().getUsagepointLocation(), 
                                new ClientCallback<UsagePointData>() {

                            @Override
                            public void onFailure(Throwable caught) {
                                logger.info("UsagePointUpdatedEvent.SAVE_CUSTOMER: updateUsagePointComponent... failed. Throwable caught = " + caught.toString());
                                Dialogs.displayErrorMessage(MessagesUtil.getInstance().getMessage("usagepointworkspace.customer.saved.usagepoint.failed", new String[]{event.getUsagePointData().getCustomerAgreementData().getCustomerData().getName()}), MediaResourceUtil.getInstance().getErrorIcon(), event.getLeft(), event.getTop(), MessagesUtil.getInstance().getMessage("button.close"));
                            }

                            @Override
                            public void onSuccess(UsagePointData result) {
                                //Dialogs.displayInformationMessage(MessagesUtil.getInstance().getMessage("usagepointworkspace.customer.saved.usagepoint.updated", new String[]{event.getUsagePointData().getCustomerAgreementData().getCustomerData().getName()}), MediaResourceUtil.getInstance().getInformationIcon(), event.getLeft(), event.getTop(), null);
                                //check if want to activate up if not ACT
                                final UsagePointData usagePointData = event.getUsagePointData();
                                if (usagePointData.getCustomerAgreementData().getRecordStatus().equals(RecordStatus.ACT)) {
                                    if (usagePointData.getRecordStatus() != RecordStatus.ACT) {
                                        if (usagePointData.getMeterId() != null){
                                            String customerSavedMsg = MessagesUtil.getInstance().getMessage("usagepointworkspace.customer.saved",
                                                    new String[]{usagePointData.getCustomerAgreementData().getCustomerData().getName(),usagePointData.getName()});
                                            queryActivateAndSaveUsagePoint(usagePointData, usagePointData.getServiceLocation(), usagePointData.getUsagepointLocation(),event.getUsagePointWorkspaceView(),
                                                    new String[] {customerSavedMsg, MessagesUtil.getInstance().getMessage("usagepointworkspace.assign.usage.point.activated")},
                                                    new String[] {customerSavedMsg, MessagesUtil.getInstance().getMessage("usagepointworkspace.save.usage.point.inactive")});

                                        } else {
                                            Dialogs.displayInformationMessages(new String[] 
                                                    {MessagesUtil.getInstance().getMessage("usagepointworkspace.customer.saved", new String[]{usagePointData.getCustomerAgreementData().getCustomerData().getName(),usagePointData.getName()}),
                                                            MessagesUtil.getInstance().getMessage("usagepointworkspace.save.usage.point.inactive")}, 
                                                    MediaResourceUtil.getInstance().getInformationIcon());
                                            sendNotification(usagePointData);
                                            sendReloadNotification();
                                        }
                                    } else {
                                        Dialogs.displayInformationMessage(MessagesUtil.getInstance().getMessage("usagepointworkspace.customer.saved.usagepoint.updated", new String[]{usagePointData.getCustomerAgreementData().getCustomerData().getName(),usagePointData.getName()}), MediaResourceUtil.getInstance().getInformationIcon());
                                        sendNotification(usagePointData);
                                        sendReloadNotification();
                                    }
                                }
                            }
                        });
                    } else {
                        Dialogs.displayInformationMessage(MessagesUtil.getInstance().getMessage("usagepointworkspace.customer.saved.no.usage.point", new String[]{event.getUsagePointData().getCustomerAgreementData().getCustomerData().getName()}), MediaResourceUtil.getInstance().getInformationIcon(), event.getLeft(), event.getTop(), null);
                        sendNotification(event.getUsagePointData());
                        sendReloadNotification();
                    }

                    break;
                case UsagePointUpdatedEvent.FETCH_CUSTOMER:
                    logger.info("Fetch customer");
                    //see before Switch: UsagePointData usagePointData = event.getUsagePointData();

                    if (usagePointDataExEvent != null 
                            && usagePointDataExEvent.getCustomerAgreementData() != null 
                            && usagePointDataExEvent.getCustomerAgreementData().getCustomerId().equals(event.getAssignToCustomerId())) {
                        logger.info("Nope: Trying to fetch the same customer");
                        Dialogs.displayInformationMessage(MessagesUtil.getInstance().getMessage("customer.fetch.duplicate", new String[]{usagePointDataExEvent.getCustomerAgreementData().getCustomerData().getSurname()}), MediaResourceUtil.getInstance().getInformationIcon(), event.getLeft(), event.getTop(), null);
                    } else {
                        clientFactory.getSearchRpc().getCustomerAgreementDatabyCustId(event.getAssignToCustomerId(), new ClientCallback<CustomerAgreementData>() {

                            @Override
                            public void onSuccess(CustomerAgreementData result) {
                                AbstractUsagePointPlace place = event.getUsagePointWorkspaceView().getPlace();
                                CustomerPlace newCustomerPlace = new CustomerPlace(String.valueOf(result.getCustomerId()));
                                if (place.isNew() && clientFactory.isEnableMultiUp()
                                    && clientFactory.getWorkspaceContainer().isWorkspaceOpen(newCustomerPlace)) {
                                    newCustomerPlace = new CustomerPlace(String.valueOf(result.getCustomerId()), CustomerPlace.ContractType.NEW);
                                }
                                logger.info("Fetch Customer: newCustomerPlace=" + newCustomerPlace.getCustomerId());
                                if(!clientFactory.isEnableMultiUp() && clientFactory.getWorkspaceContainer().isWorkspaceOpen(newCustomerPlace)) {
                                    logger.info("Customer place open, closing it");
                                    clientFactory.getWorkspaceContainer().closeWorkspaceNow(newCustomerPlace);
                                }

                                UsagePointData usagePointData = event.getUsagePointData();
                                usagePointData.setCustomerAgreementData(result);

                                if (place.isNew() || place instanceof CustomerPlace) {
                                    event.getUsagePointWorkspaceView().setPlace(newCustomerPlace);
                                    event.getUsagePointWorkspaceView().setUsagePointData(usagePointData);
                                    clientFactory.getWorkspaceContainer().refreshWorkspace(newCustomerPlace);
                                } else {
                                    event.getUsagePointWorkspaceView().setUsagePointData(usagePointData);
                                }
                                sendReloadNotification();
                                setHistoryIds(usagePointDataExEvent);
                            }
                        });

                    }
                    break;
                case UsagePointUpdatedEvent.REMOVE_METER:
                    logger.info("Remove meter from Usage Point");
                    removeMeter(event.getUsagePointWorkspaceView(), event.getUsagePointData());
                    break;
                default:
                    break;
                }
                setHistoryIds(usagePointDataExEvent, event.getUsagePointWorkspaceView());
            }
        });

        clientFactory.getEventBus().addHandler(LinkToEvent.TYPE, new LinkToEventHandler() {
            @Override
            public void linkTo(LinkToEvent event) {
                LinkToProcess linkToProcess = new LinkToProcess() {
                    @Override
                    protected void gotoLinkTo(LinkToEvent event, Workspace wkspace) {
                        if (wkspace instanceof UsagePointWorkspaceView) {
                            UsagePointWorkspaceView workspace = (UsagePointWorkspaceView) wkspace;
                            final int updateType = event.getUpdateType();
                            final int paramType = event.getLinkParamType();

                            switch (updateType) {
                            case LinkToEvent.ENGINEERING_TOKEN_HISTORY:
                                workspace.showInformation(UsagePointWorkspaceView.SHOW_INFORMATION_METER);
                                workspace.openEngineeringTokenHistory();
                                break;    
                            case LinkToEvent.CREDIT_TOKEN_HISTORY:
                                workspace.openTransactionHistory(paramType);
                                break;  
                            case LinkToEvent.REMOVE_METER_COMPONENT:
                                workspace.openRemoveMeterDialogue();
                                break;      
                            default:
                                break;
                            }
                        }
                    }
                };
                linkToProcess.processLinkTo(clientFactory, event);
            }
        });    

    }

    @Override
    public void createWorkspace(final Place place, final WorkspaceCreateCallback workspaceCreateCallback) {
        clientFactory.getAppSettingRpc().getAppSettingsForUPAndMeterAndCustomerCustomFields(new ClientCallback<ArrayList<AppSetting>>() {

            @Override
            public void onFailure(Throwable caught) {
                /*
                 * Note RC 2017-02-04: If site has >1 access group; having to reselect the user access group after timeout, 
                 * causes the UsagePointPage to close as per 
                 * In UsagePointWorkspaceView.handleNotification(...) the closeWorkspave is executed.
                 * 
                 * Note RC 2017-04-05: If timeout happens when click on the usagepoint link in the table on the MeterOnlineBulkWorkspaceView, 
                 * then get "USAGEPOINTWORKSPACEFACTORY: Timeout happened, cannot reconstruct page from stored usagePointPageCustomFieldsList. Its NULL" 
                 * because its looking for the UP page which you are TRYING to open to refresh it!! But not yet opened!
                 */
                if (usagePointPageCustomFieldsList != null) {
                    createWorkspace(place,  workspaceCreateCallback, usagePointPageCustomFieldsList);
                    logger.info("USAGEPOINTWORKSPACEFACTORY: Timeout happened, reconstruct page from stored usagePointPageCustomFieldsList.size()=" + usagePointPageCustomFieldsList.size());
                } else {
                    clientFactory.getWorkspaceContainer().closeWorkspaceNow(place);
                    logger.info("USAGEPOINTWORKSPACEFACTORY: Timeout happened, cannot reconstruct page from stored usagePointPageCustomFieldsList. Its NULL");
                    if (place instanceof CustomerPlace) {
                        workspaceCreateCallback.onWorkspaceCreationFailed(new ServiceException("error.customer.workspace.error", true));
                    } else if(place instanceof MeterPlace) {
                        workspaceCreateCallback.onWorkspaceCreationFailed(new ServiceException("error.meter.workspace.error", true));
                    } else if (place instanceof UsagePointPlace) {
                        workspaceCreateCallback.onWorkspaceCreationFailed(new ServiceException("error.usagepoint.workspace.error", true));
                    } else {
                        workspaceCreateCallback.onWorkspaceCreationFailed(new Exception("Unknown Place"));
                    }
                }
            }
            
            @Override
            public void onSuccess(ArrayList<AppSetting> result) {
                usagePointPageCustomFieldsList = result;
                createWorkspace(place,  workspaceCreateCallback, result);
            }
        });
        
    }
    
    private void createWorkspace(final Place place, final WorkspaceCreateCallback workspaceCreateCallback, final ArrayList<AppSetting> customFieldList) {
        this.customFieldList = customFieldList;
        
        if (place instanceof CustomerPlace) {
            createCustomerWorkspace(place, workspaceCreateCallback);
        } else if(place instanceof MeterPlace) {
            createMeterWorkspace(place, workspaceCreateCallback);
        } else if (place instanceof UsagePointPlace) {
            createUsagePointWorkspace(place, workspaceCreateCallback);
        } else {
            workspaceCreateCallback.onWorkspaceCreationFailed(new Exception("Unknown Place"));
        }
    }
    
    private void createCustomerWorkspace(final Place place, final WorkspaceCreateCallback workspaceCreateCallback) {
        final CustomerPlace customerPlace = (CustomerPlace) place;
        if (customerPlace.isNew()) {
            if (!clientFactory.getUser().hasPermission(MeterMngStatics.ACCESS_PERMISSION_MM_NEW_CUSTOMER)) {
                Dialogs.displayErrorMessage(MessagesUtil.getInstance().getMessage("error.accessdenied"), 
                        MediaResourceUtil.getInstance().getLockedIcon(), 
                        MessagesUtil.getInstance().getMessage("button.close"));
                workspaceCreateCallback.onWorkspaceCreationFailed(new AccessControlException("Access is Denied"));
                return;               
            }
            UsagePointWorkspaceView workspace = new UsagePointWorkspaceView(clientFactory, customerPlace, customFieldList);
            workspace.setUsagePointData(null);
            workspaceCreateCallback.onWorkspaceCreated(workspace);
        } else if (customerPlace.getSearchType() == CustomerPlace.SEARCH_BY_ID) {                
            clientFactory.getSearchRpc().getUsagePointByCustId(customerPlace.getCustomerId(), new ClientCallback<UsagePointData>() {    
                
                @Override
                protected void onFailureClient() {
                    workspaceCreateCallback.onWorkspaceCreationFailed(new ServiceException("error.customer.workspace.error", true));
                }
                
                @Override
                public void onSuccess(UsagePointData result) {
                    if (result != null) {
                        UsagePointWorkspaceView workspace = new UsagePointWorkspaceView(clientFactory, customerPlace, customFieldList);
                        workspace.setUsagePointData(result);
                        workspaceCreateCallback.onWorkspaceCreated(workspace);
                    } else {
                        workspaceCreateCallback.onWorkspaceCreationFailed(
                                new CustomerNotFoundException(
                                        MessagesUtil.getInstance().getMessage("error.missing", 
                                             new String[] {MessagesUtil.getInstance().getMessage("customer.title"), (new CustomerPlace.Tokenizer()).getToken(customerPlace)})));
                    }
                }
            });
        } else if (customerPlace.getSearchType() == CustomerPlace.SEARCH_BY_AGREEMENT_REF) { 
            clientFactory.getSearchRpc().getUsagePointByAgreementRef(customerPlace.getCustomerId(), new ClientCallback<UsagePointData>() {     
                
                @Override
                protected void onFailureClient() {
                    workspaceCreateCallback.onWorkspaceCreationFailed(
                            new ServiceException(MessagesUtil.getInstance().getMessage("error.customer.workspace.error")));
                }
                
                @Override
                public void onSuccess(UsagePointData result) {
                    if (result != null) {
                        UsagePointWorkspaceView workspace = new UsagePointWorkspaceView(clientFactory, customerPlace, customFieldList);
                        workspace.setUsagePointData(result);
                        workspaceCreateCallback.onWorkspaceCreated(workspace);
                    } else {
                        workspaceCreateCallback.onWorkspaceCreationFailed(
                                new CustomerNotFoundException(
                                        MessagesUtil.getInstance().getMessage("error.missing", 
                                             new String[] {MessagesUtil.getInstance().getMessage("customer.title"), (new CustomerPlace.Tokenizer()).getToken(customerPlace)})));
                    }
                }
            });
        } else if (customerPlace.getSearchType() == CustomerPlace.SEARCH_BY_ACCOUNT_NAME) {                
            clientFactory.getSearchRpc().getUsagePointByAccountName(customerPlace.getCustomerId(), new ClientCallback<UsagePointData>() {     
                
                @Override
                protected void onFailureClient() {
                    workspaceCreateCallback.onWorkspaceCreationFailed(
                            new ServiceException(MessagesUtil.getInstance().getMessage("error.customer.workspace.error")));
                }
                
                @Override
                public void onSuccess(UsagePointData result) {
                    if (result != null) {
                        UsagePointWorkspaceView workspace = new UsagePointWorkspaceView(clientFactory, customerPlace, customFieldList);
                        workspace.setUsagePointData(result);
                        workspaceCreateCallback.onWorkspaceCreated(workspace);
                    } else {
                        workspaceCreateCallback.onWorkspaceCreationFailed(
                                new CustomerNotFoundException(
                                        MessagesUtil.getInstance().getMessage("error.missing", 
                                             new String[] {MessagesUtil.getInstance().getMessage("customer.title"), (new CustomerPlace.Tokenizer()).getToken(customerPlace)})));
                    }
                }
            });
        } else if (customerPlace.getSearchType() == CustomerPlace.SEARCH_BY_ID_NUMBER) {                
            clientFactory.getSearchRpc().getUsagePointByCustomerIdNumber(customerPlace.getCustomerId(), new ClientCallback<UsagePointData>() {     
                
                @Override
                protected void onFailureClient() {
                    workspaceCreateCallback.onWorkspaceCreationFailed(
                            new ServiceException(MessagesUtil.getInstance().getMessage("error.customer.workspace.error")));
                }
                
                @Override
                public void onSuccess(UsagePointData result) {
                    if (result != null) {
                        UsagePointWorkspaceView workspace = new UsagePointWorkspaceView(clientFactory, customerPlace, customFieldList);
                        workspace.setUsagePointData(result);
                        workspaceCreateCallback.onWorkspaceCreated(workspace);
                    } else {
                        workspaceCreateCallback.onWorkspaceCreationFailed(
                                new CustomerNotFoundException(
                                        MessagesUtil.getInstance().getMessage("error.missing", 
                                             new String[] {MessagesUtil.getInstance().getMessage("customer.title"), (new CustomerPlace.Tokenizer()).getToken(customerPlace)})));
                    }
                }
            });
        }
    }
    
    private void createMeterWorkspace(final Place place, final WorkspaceCreateCallback workspaceCreateCallback) {
        final MeterPlace meterPlace = (MeterPlace) place;
        if (meterPlace.isNew()) {
            if (!clientFactory.getUser().hasPermission(MeterMngStatics.ACCESS_PERMISSION_MM_NEW_METER)) {
                Dialogs.displayErrorMessage(MessagesUtil.getInstance().getMessage("error.accessdenied"), 
                        MediaResourceUtil.getInstance().getLockedIcon(), 
                        MessagesUtil.getInstance().getMessage("button.close"));
                workspaceCreateCallback.onWorkspaceCreationFailed(new AccessControlException("Access is Denied"));
                return;               
            }
            UsagePointWorkspaceView workspace = new UsagePointWorkspaceView(clientFactory, meterPlace, customFieldList);
            workspace.setUsagePointData(null);
            workspaceCreateCallback.onWorkspaceCreated(workspace);
        } else {
            clientFactory.getSearchRpc().getUsagePointDataByMeterNum(
                    meterPlace.getMeterNumber(), 
                    new ClientCallback<UsagePointData>() { 
                        
                        @Override
                        protected void onFailureClient() {
                            workspaceCreateCallback.onWorkspaceCreationFailed(new ServiceException("error.meter.workspace.error", true));
                        }

                        @Override
                        public void onSuccess(UsagePointData result) {
                            if (result != null) {
                                if (result.getErrorMessage() != null) {
                                    workspaceCreateCallback.onWorkspaceCreationFailed(
                                            new ServiceException(MessagesUtil.getInstance().getMessage("error.meter.accessdenied")));
                                } else {
                                    UsagePointWorkspaceView workspace = new UsagePointWorkspaceView(clientFactory, meterPlace, customFieldList);
                                    workspace.setUsagePointData(result);
                                    workspaceCreateCallback.onWorkspaceCreated(workspace);
                                }
                            } else {
                                workspaceCreateCallback.onWorkspaceCreationFailed(
                                        new MeterNotFoundException(
                                                MessagesUtil.getInstance().getMessage("error.missing", 
                                                     new String[] {MessagesUtil.getInstance().getMessage("meter.title"), meterPlace.getMeterNumber()})));
                            }
                        }
                    });
        }
    }
    
    private void createUsagePointWorkspace(final Place place, final WorkspaceCreateCallback workspaceCreateCallback) {
        final UsagePointPlace usagePointPlace = (UsagePointPlace) place;
        clientFactory.getSearchRpc().getUsagePointDataByUsagePointName(
                usagePointPlace.getUsagePointName(),
                new ClientCallback<UsagePointData>() { 
                    
                    @Override
                    protected void onFailureClient() {
                        workspaceCreateCallback.onWorkspaceCreationFailed(new ServiceException("error.usagepoint.workspace.error", true));
                    }

                    @Override
                    public void onSuccess(UsagePointData result) {
                        if (result != null) {
                            UsagePointWorkspaceView workspace = new UsagePointWorkspaceView(clientFactory, usagePointPlace, customFieldList);
                            workspace.setUsagePointData(result);
                            workspaceCreateCallback.onWorkspaceCreated(workspace);
                        } else {
                            workspaceCreateCallback.onWorkspaceCreationFailed(
                                    new UsagePointNotFoundException(
                                            MessagesUtil.getInstance().getMessage("error.missing", 
                                                 new String[] {MessagesUtil.getInstance().getMessage("usagepoint.title"), usagePointPlace.getUsagePointName()})));
                        }
                    }
                });
    }

    @Override
    public boolean handles(Place place) {
        return place instanceof CustomerPlace || place instanceof MeterPlace || place instanceof UsagePointPlace;
    }

    
    public void unassignCustomerFromUsagePoint(final UsagePointWorkspaceView usagePointWorkspaceView, final UsagePointData usagePointData) {

        //update usage point - remove customer agreement id
        final ClientCallback<Void> unassignCustomerFromUsagepointSvcAsyncCallback = new ClientCallback<Void>() {
            @Override
            public void onSuccess(Void result) {
                logger.info("Successfully unassigned customer");
                final String customerName = usagePointData.getCustomerAgreementData().getCustomerData().getName();
                usagePointWorkspaceView.getCustomerComponent().clearCustomerComponent(null);
                usagePointData.setCustomerAgreementData(null);
                usagePointData.setCustomerAgreementId(null);
                if (usagePointData.getRecordStatus().equals(RecordStatus.ACT)) {
                    usagePointData.setRecordStatus(RecordStatus.DAC);
                    if (specialActionsIdForDeactivateUsagePoint != null) {
                        usagePointData.setActiveStatusUsagePointReasonsLog(createSpecialActionsReasonLog(specialActionsIdForDeactivateUsagePoint));
                        updateUsagePointForAutoDeactivation(usagePointData, usagePointWorkspaceView, customerName);
                    } else {
                        clientFactory.getSpecialActionsRpc().getSpecialActionsByValue(SpecialActionsData.DEACTIVATE_USAGE_POINT, new ClientCallback<SpecialActions>() {
                            @Override
                            public void onSuccess(SpecialActions result) {
                                specialActionsIdForDeactivateUsagePoint = result.getId();
                                usagePointData.setActiveStatusUsagePointReasonsLog(createSpecialActionsReasonLog(specialActionsIdForDeactivateUsagePoint));
                                updateUsagePointForAutoDeactivation(usagePointData, usagePointWorkspaceView, customerName);
                            }
                        });
                    }
                } else {
                    updateUsagePointForAutoDeactivation(usagePointData, usagePointWorkspaceView, customerName);
                }
            }
        };

        if (clientFactory != null) {
            clientFactory.getUsagePointRpc().removeCustomerFromUsagePoint(usagePointData,
                    unassignCustomerFromUsagepointSvcAsyncCallback);
        }
    }

    private void updateUsagePointForAutoDeactivation(final UsagePointData usagePointData,
            final UsagePointWorkspaceView usagePointWorkspaceView, final String customerName) {
        final String usagePointName = usagePointData.getName();
        if (usagePointWorkspaceView.getPlace() instanceof CustomerPlace) {
            MeterData meterData = usagePointData.getMeterData();
            AbstractUsagePointPlace place;
            if (meterData != null && meterData.getMeterNum() != null) {
                place = new MeterPlace(meterData.getMeterNum());
            } else {
                place = new UsagePointPlace(usagePointName);
            }
            usagePointWorkspaceView.setPlace(place);
            usagePointWorkspaceView.setUsagePointData(usagePointData);
            clientFactory.getWorkspaceContainer().refreshWorkspace(place);
            sendNotification(usagePointData);
        }

        final Messages messages = MessagesUtil.getInstance();
        final MediaResource mediaResource = MediaResourceUtil.getInstance();
        clientFactory.getUsagePointRpc().updateUsagePointComponent(usagePointData, usagePointData.getServiceLocation(),
                usagePointData.getUsagepointLocation(), new ClientCallback<UsagePointData>() {
                    @Override
                    public void onFailure(Throwable caught) {
                        Dialogs.displayErrorMessage(
                                messages.getMessage(
                                        "usagepointworkspace.customer.unassigned.usagepoint.deactivation.failed",
                                        new String[] { customerName, usagePointName }),
                                mediaResource.getErrorIcon(), messages.getMessage("button.close"));
                    }

                    @Override
                    public void onSuccess(final UsagePointData result) {
                        usagePointWorkspaceView.customercomponent.mapDataToForm();
                        Dialogs.displayInformationMessage(
                                messages.getMessage("usagepointworkspace.customer.unassigned.usagepoint.deactivated",
                                        new String[] { customerName, usagePointName }),
                                mediaResource.getInformationIcon());
                        usagePointWorkspaceView.setUsagePointData(usagePointData);
                        sendNotification(result);
                        sendReloadNotification();
                    }
                });
    }

    public void assignCustomer(final UsagePointWorkspaceView usagePointWorkspaceView, final UsagePointData usagePointData, final Long newCustomerId) {
        
        //Get UsagePointData
        clientFactory.getSearchRpc().getUsagePointByCustId(newCustomerId.toString(), new ClientCallback<UsagePointData>() {
            
            @Override
            public void onSuccess(UsagePointData result) {
                // check if there is a usage point already - if so, error as we can't assign to a customer already assigned
                // but if multiUp is enabled then it is allowed
                if (!clientFactory.isEnableMultiUp() && result.getId() != null) {
                    Dialogs.displayErrorMessage(MessagesUtil.getInstance().getMessage("usagepointworkspace.customer.assign.error.already.assigned", new String[]{result.getName()}), MediaResourceUtil.getInstance().getErrorIcon(), MessagesUtil.getInstance().getMessage("button.close"));
                } else {
                    final CustomerAgreementData caData = result.getCustomerAgreementData();
                    clientFactory.getUsagePointRpc().assignCustomerToUsagePoint(usagePointData.getId(), result.getCustomerAgreementId(), new ClientCallback<UsagePoint>() {
                        @Override
                        public void onSuccess(UsagePoint result) {
                            logger.info("Successfully assigned customer to usage point: " + result);
                            // close workspace for the new customer id if it is open
                            final CustomerPlace newCustomerPlace = new CustomerPlace(newCustomerId.toString());
                            if (!clientFactory.isEnableMultiUp() && clientFactory.getWorkspaceContainer().isWorkspaceOpen(newCustomerPlace)) {
                                logger.info("Customer place open, closing it");
                                clientFactory.getWorkspaceContainer().closeWorkspaceNow(newCustomerPlace);
                            }

                            usagePointData.setCustomerAgreementData(caData);
                            // Should currently be a MeterPlace with no unassigned customer
                            // so no need to check and refresh place.

                            usagePointWorkspaceView.setUsagePointData(usagePointData);

                            if (caData.getRecordStatus() != RecordStatus.ACT) {
                                usagePointData.setRecordStatus(RecordStatus.DAC);
                                clientFactory.getUsagePointRpc().updateUsagePointComponent(usagePointData, usagePointData.getServiceLocation(), usagePointData.getUsagepointLocation(),
                                    new ClientCallback<UsagePointData>() {
                                        @Override
                                        public void onFailure(Throwable caught) {
                                            Dialogs.displayErrorMessage(MessagesUtil.getInstance().getMessage("usagepointworkspace.customer.assign.error.already.assigned", new String[]{caData.getCustomerData().getName(), usagePointData.getName()}), MediaResourceUtil.getInstance().getErrorIcon(), MessagesUtil.getInstance().getMessage("button.close"));
                                        }

                                        @Override
                                        public void onSuccess(UsagePointData result) {
                                            Dialogs.displayInformationMessage(MessagesUtil.getInstance().getMessage("usagepointworkspace.customer.assigned.usagepoint.deactivated", new String[]{caData.getCustomerData().getName(), usagePointData.getName()}), MediaResourceUtil.getInstance().getInformationIcon());
                                            sendNotification(result);
                                            sendReloadNotification();
                                        }
                                    });
                            } else {
                                if (usagePointData.getRecordStatus() != RecordStatus.ACT) {
                                    if (usagePointData.getMeterId() != null) {
                                        queryActivateAndSaveUsagePoint(usagePointData, usagePointData.getServiceLocation(), usagePointData.getUsagepointLocation(), usagePointWorkspaceView,
                                            new String[]{MessagesUtil.getInstance().getMessage("usagepointworkspace.customer.assigned", new String[]{caData.getCustomerData().getName(), usagePointData.getName()}),
                                                MessagesUtil.getInstance().getMessage("usagepointworkspace.assign.usage.point.activated")},
                                            new String[]{MessagesUtil.getInstance().getMessage("usagepointworkspace.customer.assigned.usagepoint.deactivated", new String[]{caData.getCustomerData().getName(), usagePointData.getName()})
                                            });
                                    } else {
                                        showMessageNotActivateUP("usagepointworkspace.customer.assigned.usagepoint.deactivated", caData.getCustomerData().getName(), usagePointData.getName());
                                        sendNotification(usagePointData);
                                        sendReloadNotification();
                                    }
                                } else {
                                    Dialogs.displayInformationMessage(MessagesUtil.getInstance().getMessage("usagepointworkspace.customer.assigned", new String[]{caData.getCustomerData().getName(), usagePointData.getName()}), MediaResourceUtil.getInstance().getInformationIcon());
                                    sendNotification(usagePointData);
                                    sendReloadNotification();
                                }
                            }
                        }
                    });

                }
            }
            @Override
            public void onFailure(Throwable caught) {
                caught.printStackTrace();
            }
        });
    }

    public void assignMeter(final UsagePointWorkspaceView usagePointWorkspaceView, final UsagePointData usagePointData, final String newMeterNumber, final List<MdcChannelReadingsDto> channelReadingsList) {
        logger.info("assignMeter step 2: getUsagePointDataByMeterNum");
        clientFactory.getSearchRpc().getUsagePointDataByMeterNum(newMeterNumber, new ClientCallback<UsagePointData>() {                    
            @Override
            public void onSuccess(UsagePointData result) {
                // check if there is a usage point already - if so, error as we can't assign to a customer already assigned
                if (result == null) {
                    Dialogs.displayErrorMessage(MessagesUtil.getInstance().getMessage("usagepointworkspace.error.meter.not.found"), MediaResourceUtil.getInstance().getErrorIcon(), MessagesUtil.getInstance().getMessage("button.close"));
                } else if (result.getId() != null) {
                    Dialogs.displayErrorMessage(MessagesUtil.getInstance().getMessage("usagepointworkspace.error.meter.already.assigned", new String[]{newMeterNumber, result.getName()}), MediaResourceUtil.getInstance().getErrorIcon(), MessagesUtil.getInstance().getMessage("button.close"));
                } else {
                    final MeterData newMeterData = result.getMeterData();
                    newMeterData.setEndDeviceStoreId(null);
                    final UpPricingStructure currentUPPS = usagePointData.getUpPricingStructureData().getUpPricingStructure();
                    if (currentUPPS != null) { //check meter is the correct model by the usage point pricing structure 
                        clientFactory.getMeterModelRpc().getMeterModelIds(currentUPPS.getPricingStructureId(), new ClientCallback<ArrayList<Long>>() {
                            boolean proceed = true;
                            @Override
                            public void onSuccess(ArrayList<Long> meterModelResult) {
                                if (!(meterModelResult.contains(newMeterData.getMeterModelId()))) {
                                    Dialogs.displayErrorMessage(MessagesUtil.getInstance().getMessage("usagepointworkspace.error.meter.unsupported.model.current", new String[]{newMeterNumber}), MediaResourceUtil.getInstance().getErrorIcon(), MessagesUtil.getInstance().getMessage("button.close"));
                                    proceed = false;
                                }
                                if (proceed) {
                                    assignMeterToUsagePoint(usagePointWorkspaceView, usagePointData, newMeterData, channelReadingsList);
                                }
                            }
                        });                    
                    } else {
                        assignMeterToUsagePoint(usagePointWorkspaceView, usagePointData, newMeterData, channelReadingsList);
                    }
                }       
            }   
        });
    }

    private void assignMeterToUsagePoint(final UsagePointWorkspaceView usagePointWorkspaceView, final UsagePointData usagePointData, final MeterData newMeterData, final List<MdcChannelReadingsDto> channelReadingsList) {
        logger.info("assignMeterToUsagePoint: reAssignUP");
        ClientCallback<UsagePoint> reAssignUsagepointSvcAsyncCallback = new ClientCallback<UsagePoint>() {
            @Override
            public void onFailure(final Throwable caught) {
                logger.info("Re-assign Failure!! caught.getMessage= " + caught.getMessage() + " caught=" + caught.toString() + "    getLocalizedMessage()= " + caught.getLocalizedMessage());
                clientFactory.getSearchRpc().getUsagePointDataByUsagePointName(usagePointData.getName(), new ClientCallback<UsagePointData>() {
                    @Override
                    public void onSuccess(UsagePointData result) {
                        hideWaitingDialog();
                        usagePointWorkspaceView.setUsagePointData(result);
                        onFailureDisplayMessage(caught);
                    }
                });
            }

            @Override
            public void onSuccess(UsagePoint result) {
                logger.info("Re-assign Success!!");
                // close workspace for the new meter if it is open
                final MeterPlace newMeterPlace = new MeterPlace(newMeterData.getMeterNum());
                if(clientFactory.getWorkspaceContainer().isWorkspaceOpen(newMeterPlace)) {
                    clientFactory.getWorkspaceContainer().closeWorkspaceNow(newMeterPlace);
                }
                usagePointWorkspaceView.setAddNewMeterToExistingUsagePoint(false);

                clientFactory.getSearchRpc().getUsagePointDataByMeterNum(newMeterData.getMeterNum(), new ClientCallback<UsagePointData>() {

                    @Override
                    public void onSuccess(UsagePointData result) {
                        usagePointData.setMeterData(result.getMeterData());
                        usagePointData.setMeterId(result.getMeterId());
                        if (result.getId() != null) {
                            usagePointData.getUpMeterInstall().setId(result.getUpMeterInstall().getId());
                            usagePointData.setInstallationDate(result.getUpMeterInstall().getInstallDate());
                            usagePointData.setActivationDate(result.getActivationDate());    
                        }
                        
                        if(usagePointWorkspaceView.getPlaceString().toLowerCase().contains("new") 
                                || usagePointWorkspaceView.getPlace() instanceof MeterPlace) {
                            usagePointWorkspaceView.setPlace(newMeterPlace);
                            usagePointWorkspaceView.setUsagePointData(usagePointData);
                            clientFactory.getWorkspaceContainer().refreshWorkspace(newMeterPlace);
                        } else {
                            usagePointWorkspaceView.setUsagePointData(usagePointData);
                            clientFactory.getWorkspaceContainer().refreshWorkspace(usagePointWorkspaceView.getPlace());
                        }

                        final EndDeviceStoreUpdatedEvent eventful = new EndDeviceStoreUpdatedEvent();
                        eventful.setEndDeviceStoreId(result.getMeterData().getEndDeviceStoreId());
                        clientFactory.getEventBus().fireEvent(eventful);

                        if (result.getId() != null) {
                            if (usagePointData.getRecordStatus() != RecordStatus.ACT) {
                                if (usagePointData.getCustomerAgreementData() != null && usagePointData.getCustomerAgreementData().getRecordStatus().equals(RecordStatus.ACT)){
                                    queryActivateAndSaveUsagePoint(usagePointData, usagePointData.getServiceLocation(), usagePointData.getUsagepointLocation(), usagePointWorkspaceView,
                                            new String[] {MessagesUtil.getInstance().getMessage("usagepointworkspace.meter.assigned", new String[]{newMeterData.getMeterNum(),result.getName()}),MessagesUtil.getInstance().getMessage("usagepointworkspace.assign.usage.point.activated")},
                                            new String[] {MessagesUtil.getInstance().getMessage("usagepointworkspace.meter.assigned.usagepoint.deactivated", new String[]{newMeterData.getMeterNum(),usagePointData.getName()})}
                                    );
                                } else {
                                    showMessageNotActivateUP("usagepointworkspace.meter.assigned.usagepoint.deactivated", newMeterData.getMeterNum(), result.getName());
                                    sendNotification(usagePointData);
                                    sendReloadNotification();
                                }
                            } else {
                                Dialogs.displayInformationMessage(MessagesUtil.getInstance().getMessage("usagepointworkspace.meter.assigned", new String[]{newMeterData.getMeterNum(),result.getName()}), MediaResourceUtil.getInstance().getInformationIcon());
                                sendNotification(usagePointData);
                                sendReloadNotification();
                            }
                            
                            //refresh installation date in txtbox on usagepointcomponent
                            usagePointWorkspaceView.getUsagePointComponent().refreshDates(result);
                        } else if (result.getMeterId() != null) {
                            if (result.getCustomerAgreementId() != null) {
                                Dialogs.displayInformationMessage(MessagesUtil.getInstance().getMessage("usagepointworkspace.meter.add.usagepoint.to.join.customer", new String[]{newMeterData.getMeterNum(), usagePointData.getCustomerAgreementData().getCustomerData().getName()}), MediaResourceUtil.getInstance().getInformationIcon());
                            } else {
                                Dialogs.displayInformationMessage(MessagesUtil.getInstance().getMessage("usagepointworkspace.meter.add.usagepoint.to.join"), MediaResourceUtil.getInstance().getInformationIcon());
                            }
                            sendReloadNotification();
                            setHistoryIds(usagePointData);
                        }
                    }
                });
            }
        };
        clientFactory.getUsagePointRpc().reAssignMeterToUsagePoint(usagePointData, newMeterData, channelReadingsList, reAssignUsagepointSvcAsyncCallback);
    }
    
    private void showMessageNotActivateUP(String message, String param1, String param2) {
        Dialogs.displayInformationMessages(new String[] {MessagesUtil.getInstance().getMessage(message, new String[]{param1,param2}),MessagesUtil.getInstance().getMessage("usagepoint.error.meterandcustomer.required")}, MediaResourceUtil.getInstance().getInformationIcon());
    }
    
    public void removeMeter(final UsagePointWorkspaceView usagePointWorkspaceView, final UsagePointData usagePointData) {
        ClientCallback<Void> removeUsagepointSvcAsyncCallback = new ClientCallback<Void>() {
            @Override
            public void onFailure(Throwable caught) {
                logger.info("Remove Failure!! caught.getMessage= " + caught.getMessage() + " caught=" + caught.toString());
                Dialogs.displayErrorMessage(MessagesUtil.getInstance().getMessage(caught.getMessage()), MediaResourceUtil.getInstance().getErrorIcon(), MessagesUtil.getInstance().getMessage("button.close"));
                usagePointData.setUpMeterInstallAndPrevious(usagePointData.getPreviousUpMeterInstall());
                usagePointData.clearPreviousUpMeterInstall();
            }

            @Override
            public void onSuccess(Void result) {
                logger.info("Remove Meter Success!!");
                
                final String removedMeterNumber = usagePointData.getMeterData().getMeterNum();
                clientFactory.getSearchRpc().getUsagePointDataByUsagePointName(
                        usagePointData.getName(),
                        new ClientCallback<UsagePointData>() { 
                            @Override
                            public void onSuccess(UsagePointData result) {
                                if (!(usagePointWorkspaceView.getPlace() instanceof UsagePointPlace)) {
                                    usagePointWorkspaceView.setPlace(new UsagePointPlace(usagePointData.getName()));
                                } 
                                usagePointWorkspaceView.setUsagePointData(result);
                                clientFactory.getWorkspaceContainer().refreshWorkspace(usagePointWorkspaceView.getPlace());
                                Dialogs.displayInformationMessage(MessagesUtil.getInstance().getMessage("usagepointworkspace.meter.removed", new String[]{removedMeterNumber, usagePointData.getName()}), MediaResourceUtil.getInstance().getInformationIcon());
                                sendNotification(removedMeterNumber);
                                sendNotification();
                                sendReloadNotification();
                            }
                });
            }
        };
        clientFactory.getUsagePointRpc().removeMeterFromUsagePoint(usagePointData, removeUsagepointSvcAsyncCallback);
    }
    
    private void sendNotification(UsagePointData usagePointData) {
        String meterNum = null;
        if (usagePointData.getMeterData() != null) {
            meterNum = usagePointData.getMeterData().getMeterNum();
        }
        sendNotification(meterNum);
    }    
        
    private void sendNotification(String meterNum) {    
        clientFactory.getWorkspaceContainer().notifyWorkspaces(
                new WorkspaceNotification(NotificationType.DATA_UPDATED, 
                                                    MeterMngStatics.METER_CUST_UP_MODIFIED, 
                                                    meterNum));
    }
    
    private void sendNotification() {
        clientFactory.getWorkspaceContainer().notifyWorkspaces(
                new WorkspaceNotification(NotificationType.DATA_UPDATED, MeterMngStatics.USAGE_POINT_METER_REMOVED));
    }
    
    private void sendReloadNotification() {
        clientFactory.getWorkspaceContainer().notifyWorkspaces(
                new WorkspaceNotification(NotificationType.DATA_UPDATED, MeterMngStatics.RELOAD_USAGE_POINT_WORKSPACEVIEW));
    }

    private SpecialActionReasonsLog createSpecialActionsReasonLog(Long specialActionsId) {
        SpecialActionReasonsLog specialActionReasonsLog = new SpecialActionReasonsLog();
        specialActionReasonsLog.setActionDate(new Date());
        specialActionReasonsLog.setSpecialActionsId(specialActionsId);
        specialActionReasonsLog.setReasonText(specialActionsId==specialActionsIdForDeactivateUsagePoint?
                MessagesUtil.getInstance().getMessage("specialaction.auto.deactivate.usagepoint"):
                MessagesUtil.getInstance().getMessage("specialaction.auto.activate.usagepoint"));
        specialActionReasonsLog.setUsername(clientFactory.getUser().getUserName());
        return specialActionReasonsLog;
    }

    private void queryActivateAndSaveUsagePoint(final UsagePointData usagePointData, final LocationData serviceLocation,
                                                final LocationData usagePointLocation, final UsagePointWorkspaceView usagePointWorkspaceView,
                                                final String[] confirmFeedbackMessages, final String[] cancelFeedbackMessages) {
        final UsagePointWorkspaceFactory parent = this;
        Dialogs.confirm (
                ResourcesFactoryUtil.getInstance().getMessages().getMessage("usagepointworkspace.assign.activate.usage.point.question"),
                ResourcesFactoryUtil.getInstance().getMessages().getMessage("option.positive"),
                ResourcesFactoryUtil.getInstance().getMessages().getMessage("option.negative"),
                ResourcesFactoryUtil.getInstance().getQuestionIcon(),
                new ConfirmHandler() {
                    @Override
                    public void confirmed(boolean confirm) {
                        if (confirm) {
                            //establish if current Tariff on Pricing structure is regRead
                            Long currentPsId = usagePointData.getUpPricingStructureData().getPricingStructure().getId();
                            UpPricingStructureData futurePsData = usagePointData.getUpPricingStructureData().getFutureUpPricingStructureData();
                            
                            List<PSDto> pSDtos = new ArrayList<>();
                            //for currentPS sending in new date and not installDate because on takeOn of data from other systems, 
                            //sometimes bring over meters with very old installDates but don't bother to pull in full PS history.
                            pSDtos.add(new PSDto(currentPsId, new Date()));
                            if (futurePsData != null) {
                                pSDtos.add(new PSDto(futurePsData.getUpPricingStructure().getPricingStructureId(), futurePsData.getUpPricingStructure().getStartDate()));
                            }
                            
                            
                            new UpWorkSpaceFactoryValidatePStoMM(parent).isregReadPsSameBillingDetsAsMeterModel(clientFactory, pSDtos,
                                    usagePointData.getMeterData().getMeterModelData().getMdcId(), clientFactory.getUser().getUserName(), logger,
                                    usagePointData, serviceLocation,
                                    usagePointLocation, usagePointWorkspaceView,
                                    confirmFeedbackMessages, cancelFeedbackMessages);

                        } else {
                            Dialogs.displayInformationMessages(cancelFeedbackMessages, MediaResourceUtil.getInstance().getInformationIcon());
                            sendNotification(usagePointData);
                            setHistoryIds(usagePointData);
                        }
                    }
                });
    }

    public void queryActivateAndSaveUsagePointCheckChannels(final UsagePointData usagePointData, final LocationData serviceLocation,
            final LocationData usagePointLocation, final UsagePointWorkspaceView usagePointWorkspaceView,
            final String[] confirmFeedbackMessages, final String[] cancelFeedbackMessages) {

        final UsagePointWorkspaceFactory parent = this;
        //if activating UP check for regread init reads here
        MeterUpMdcChannelInfo meterUpMdcChannelInfo = new MeterUpMdcChannelInfo(usagePointData.getMeterData().getId(), 
                usagePointData.getMeterData().getMeterModelId(), 
                usagePointData.getId(), 
                usagePointData.getUpPricingStructureData().getUpPricingStructure().getPricingStructureId(),
                usagePointData.getUpMeterInstall().getId(), 
                usagePointData.getInstallationDate());
        upWorkspaceFactoryData = new UsagePointWorkspaceFactoryDataForUpdate(usagePointData, serviceLocation, usagePointLocation, usagePointWorkspaceView, confirmFeedbackMessages, cancelFeedbackMessages);

        clientFactory.getLookupRpc().getMeterUpMdcChannelInfo(meterUpMdcChannelInfo, new ClientCallback<MeterUpMdcChannelInfo>() {
            @Override
            public void onSuccess(MeterUpMdcChannelInfo result) {
                if (result == null || result.getChannelList() == null || result.getChannelList().isEmpty()) {
                    logger.info("getMeterUpMdcChannelInfo: result is null");
                    fireUpdateEvent(null);
                } else {
                    logger.info("getMeterUpMdcChannelInfo: result.getChannelList() size=" + result.getChannelList().size());

                    //get initial readings for the channels
                    final AssignChannelReadingsDialogueBox assignChannelReadings = 
                            new AssignChannelReadingsDialogueBox(parent, result, usagePointData.getMeterData().getMeterNum(),
                                    usagePointData.getName(),
                                    usagePointData.getMeterData().getMeterModelData().getName(), 
                                    usagePointData.getMeterData().getMeterModelData().getMdcName(),
                                    usagePointData.getUpPricingStructureData().getPricingStructure().getName(),
                                    usagePointData.getMeterData().getMeterModelData().getServiceResourceId());
                            Scheduler.get().scheduleDeferred(new ScheduledCommand() {
                                @Override
                                public void execute() {
                                    assignChannelReadings.center();
                                    assignChannelReadings.show();
                                }
                            });
                }
            }

            @Override
            public void onFailure(Throwable caught) {
                super.onFailure(caught);
            }
        });

    }
    

    @Override
    public void fireUpdateEvent(MeterUpMdcChannelInfo meterUpMdcChannelInfo) {
        List<MdcChannelReadingsDto> channelReadingsList = null;
        if (meterUpMdcChannelInfo != null && !meterUpMdcChannelInfo.getChannelList().isEmpty()) {
            channelReadingsList = meterUpMdcChannelInfo.getChannelList();
        }
        queryActivateAndSaveUsagePointFinal(upWorkspaceFactoryData.getUsagePointData(), 
                upWorkspaceFactoryData.getServiceLocation(),
                upWorkspaceFactoryData.getUsagePointLocation(),
                upWorkspaceFactoryData.getUsagePointWorkspaceView(),
                upWorkspaceFactoryData.getConfirmFeedbackMessages(),
                upWorkspaceFactoryData.getCancelFeedbackMessages(), 
                channelReadingsList);
    }
    
    private void queryActivateAndSaveUsagePointFinal(final UsagePointData usagePointData, final LocationData serviceLocation,
            final LocationData usagePointLocation, final UsagePointWorkspaceView usagePointWorkspaceView,
            final String[] confirmFeedbackMessages, final String[] cancelFeedbackMessages, 
            final List<MdcChannelReadingsDto> channelReadingsList) {
        
        usagePointData.setRecordStatus(RecordStatus.ACT);
        
        if (specialActionsIdForActivateUsagePoint != null) {
            usagePointData.setActiveStatusUsagePointReasonsLog(createSpecialActionsReasonLog(specialActionsIdForActivateUsagePoint));
            updateUsagePointForAutoActivation(usagePointData, serviceLocation, usagePointLocation, usagePointWorkspaceView, confirmFeedbackMessages, channelReadingsList);
        } else {
            clientFactory.getSpecialActionsRpc().getSpecialActionsByValue(SpecialActionsData.ACTIVATE_USAGE_POINT, new ClientCallback<SpecialActions>() {
                @Override
                public void onSuccess(SpecialActions result) {
                    specialActionsIdForActivateUsagePoint = result.getId();
                    usagePointData.setActiveStatusUsagePointReasonsLog(createSpecialActionsReasonLog(specialActionsIdForActivateUsagePoint));
                    updateUsagePointForAutoActivation(usagePointData, serviceLocation, usagePointLocation, usagePointWorkspaceView, confirmFeedbackMessages, channelReadingsList);
                }
            });
        }
    }

    private void updateUsagePointForAutoActivation(UsagePointData usagePointData, LocationData serviceLocation, LocationData usagePointLocation,
                                                   final UsagePointWorkspaceView usagePointWorkspaceView, final String[] confirmFeedbackMessages, 
                                                   final List<MdcChannelReadingsDto> channelReadingsList) {
        usagePointData.setActiveStatusUsagePointReasonsLog(createSpecialActionsReasonLog(specialActionsIdForActivateUsagePoint));
        clientFactory.getUsagePointRpc().updateUsagePointComponent(usagePointData, serviceLocation, usagePointLocation, channelReadingsList, 
                new ClientCallback<UsagePointData>() {
                    @Override
                    public void onSuccess(UsagePointData result) {
                        Dialogs.displayInformationMessages(confirmFeedbackMessages, MediaResourceUtil.getInstance().getInformationIcon());
                        usagePointWorkspaceView.setUsagePointData(result);
                        clientFactory.getWorkspaceContainer().refreshWorkspace(usagePointWorkspaceView.getPlace());
                        sendNotification(result);
                        sendReloadNotification();
                    }
                    @Override
                    public void onFailureClient() {
                    	sendReloadNotification();
                    }
                });
    }
    
    private void setHistoryIds(final UsagePointData usagePointData) {
        clientFactory.getSearchRpc().setHistoryIds(usagePointData, new ClientCallback<HistoryData>() {
            @Override
            public void onSuccess(HistoryData result) {
                usagePointData.setHistoryData(result);
            }
        });
    }

    private void setHistoryIds(final UsagePointData usagePointData, final UsagePointWorkspaceView usagePointWorkspaceView) {
        clientFactory.getSearchRpc().setHistoryIds(usagePointData, new ClientCallback<HistoryData>() {
            @Override
            public void onSuccess(HistoryData result) {
                usagePointData.setHistoryData(result);
                usagePointWorkspaceView.toggleSaveBtns(true);
            }
        });
    }
    
    //****************************************************************************************************
    private class UsagePointWorkspaceFactoryDataForUpdate  {

        private UsagePointData usagePointData;
        private LocationData serviceLocation;
        private LocationData usagePointLocation;
        private UsagePointWorkspaceView usagePointWorkspaceView;
        private String[] confirmFeedbackMessages;
        private String[] cancelFeedbackMessages;
        
        public UsagePointWorkspaceFactoryDataForUpdate(
                UsagePointData usagePointData, LocationData serviceLocation,
                LocationData usagePointLocation,
                UsagePointWorkspaceView usagePointWorkspaceView,
                String[] confirmFeedbackMessages, String[] cancelFeedbackMessages) {
            super();
            this.usagePointData = usagePointData;
            this.serviceLocation = serviceLocation;
            this.usagePointLocation = usagePointLocation;
            this.usagePointWorkspaceView = usagePointWorkspaceView;
            this.confirmFeedbackMessages = confirmFeedbackMessages;
            this.cancelFeedbackMessages = cancelFeedbackMessages;
        }

        public UsagePointData getUsagePointData() {
            return usagePointData;
        }

        public LocationData getServiceLocation() {
            return serviceLocation;
        }

        public LocationData getUsagePointLocation() {
            return usagePointLocation;
        }

        public UsagePointWorkspaceView getUsagePointWorkspaceView() {
            return usagePointWorkspaceView;
        }

        public String[] getConfirmFeedbackMessages() {
            return confirmFeedbackMessages;
        }

        public String[] getCancelFeedbackMessages() {
            return cancelFeedbackMessages;
        }
    }
}
