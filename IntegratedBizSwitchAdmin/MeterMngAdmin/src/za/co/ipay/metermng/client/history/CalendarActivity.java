package za.co.ipay.metermng.client.history;

import java.util.logging.Logger;

import za.co.ipay.metermng.client.event.OpenCalendarEvent;
import za.co.ipay.metermng.client.factory.ClientFactory;

import com.google.gwt.activity.shared.AbstractActivity;
import com.google.gwt.event.shared.EventBus;
import com.google.gwt.user.client.ui.AcceptsOneWidget;

public class CalendarActivity extends AbstractActivity {

    private ClientFactory clientFactory;
    private CalendarPlace place;
    private static Logger logger = Logger.getLogger(CalendarActivity.class.getName());
    
    public CalendarActivity(CalendarPlace calendarPlace, ClientFactory clientFactory) {
        super();
        this.clientFactory = clientFactory;
        this.place = calendarPlace;
    }
    
    @Override
    public void start(AcceptsOneWidget panel, EventBus eventBus) {
        logger.info("Opening place: " + place.getCalendarPlaceType());
        if (place.getCalendarPlaceType() != null && !place.getCalendarPlaceType().isEmpty()) {
            clientFactory.getEventBus().fireEvent(new OpenCalendarEvent(place.getCalendarPlaceType()));
        }
    }
}


