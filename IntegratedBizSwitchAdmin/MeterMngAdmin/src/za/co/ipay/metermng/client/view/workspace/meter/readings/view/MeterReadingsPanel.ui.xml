<!DOCTYPE ui:UiBinder SYSTEM "http://dl.google.com/gwt/DTD/xhtml.ent">
<ui:UiBinder xmlns:ui="urn:ui:com.google.gwt.uibinder" 
             xmlns:g="urn:import:com.google.gwt.user.client.ui"
	         xmlns:p1="urn:import:za.co.ipay.gwt.common.client.form" 
	         xmlns:g2="urn:import:com.google.gwt.user.datepicker.client"
	         xmlns:w="urn:import:za.co.ipay.gwt.common.client.widgets">
	<ui:style>
	
	</ui:style>

	<ui:with field="msg" type="za.co.ipay.metermng.client.i18n.UiMessages" />

	<g:FlowPanel>
		<p1:FormRowPanel>

			<p1:FormElement ui:field="graphTypeElement" labelText="{msg.getGraphType}:" required="true">
				<g:ListBox ui:field="graphTypeBox" />
			</p1:FormElement>

			<p1:FormElement ui:field="meterNumberElement" labelText="{msg.getMeterNumber}:" required="true">
				<g:SuggestBox ui:field="meterBox" styleName="gwt-TextBox" />
			</p1:FormElement>

			<p1:FormElement ui:field="balancingMeterElement" labelText="{msg.getBalancingMeter}:" required="true"
				visible="false">
				<g:ListBox ui:field="balancingMeterBox" />
			</p1:FormElement>

			<p1:FormElement ui:field="readingTypeElement" labelText="{msg.getReadingType}:" required="true">
				<g:ListBox ui:field="readingTypeBox" />
			</p1:FormElement>

			<p1:FormElement ui:field="startElement" labelText="{msg.getMeterReadingsStart}:" required="true">
				<g2:DateBox ui:field="startBox" styleName="gwt-TextBox" />
			</p1:FormElement>

			<p1:FormElement ui:field="endElement" labelText="{msg.getMeterReadingsEnd}:" required="true">
				<g2:DateBox ui:field="endBox" styleName="gwt-TextBox" />
			</p1:FormElement>

		</p1:FormRowPanel>
	</g:FlowPanel>

</ui:UiBinder> 