package za.co.ipay.metermng.client.view.workspace.group.type;

import java.util.logging.Logger;

import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.gwt.common.client.workspace.SessionCheckCallback;
import za.co.ipay.gwt.common.client.workspace.SimpleTableView;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.history.GroupPlace;
import za.co.ipay.metermng.client.view.component.group.GroupHierarchyView;
import za.co.ipay.metermng.client.view.component.group.GroupTypeView;
import za.co.ipay.metermng.client.view.workspace.BaseWorkspace;
import za.co.ipay.metermng.mybatis.generated.model.GroupType;
import za.co.ipay.metermng.shared.GroupHierarchyData;
import za.co.ipay.metermng.shared.group.GroupTypeData;

import com.google.gwt.core.client.GWT;
import com.google.gwt.place.shared.Place;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.user.client.ui.DeckLayoutPanel;
import com.google.gwt.user.client.ui.Widget;

public class GroupTypeWorkspaceView extends BaseWorkspace {

    @UiField DeckLayoutPanel mainLayoutPanel;
    @UiField SimpleTableView<GroupTypeData> view;
    private GroupTypeView groupTypeView;

    @UiField SimpleTableView<GroupHierarchyData> view2;
    private GroupHierarchyView groupHierarchyView;

    private static Logger logger = Logger.getLogger(GroupTypeWorkspaceView.class.getName());

    private static GroupTypeWorkspaceViewUiBinder uiBinder = GWT.create(GroupTypeWorkspaceViewUiBinder.class);

    interface GroupTypeWorkspaceViewUiBinder extends UiBinder<Widget, GroupTypeWorkspaceView> {
    }

    public GroupTypeWorkspaceView(ClientFactory clientFactory, GroupPlace place) {
        this.clientFactory = clientFactory;
        initWidget(uiBinder.createAndBindUi(this));
        setPlaceString(GroupPlace.getPlaceAsString(place));
        setHeaderText(MessagesUtil.getInstance().getMessage("grouptype.title"));
        initUi();
    }

    private void initUi() {
        groupTypeView = new GroupTypeView(clientFactory, this, view);
        groupHierarchyView = new GroupHierarchyView(clientFactory, this, view2);
        mainLayoutPanel.showWidget(view);
    }

    @Override
    public void onArrival(Place place) {
        logger.info("Arrived at Group Types...");
        groupTypeView.refreshTable();
    }

    public void showGroupTypeHierarchies(final GroupType groupType) {
        SessionCheckCallback sessionCheckCallback = new SessionCheckCallback() {
            @Override
            public void callback(SessionCheckResolution resolution) {
                groupHierarchyView.setGroupType(groupType);
                mainLayoutPanel.showWidget(view2);
                mainLayoutPanel.animate(getAnimationTime());
            }
        };
        clientFactory.handleSessionCheckCallback(sessionCheckCallback);
    }

    public void showGroupTypes() {
        mainLayoutPanel.showWidget(view);
        mainLayoutPanel.animate(getAnimationTime());
    }

    @Override
    public void onLeaving() {

    }

    @Override
    public void onSelect() {

    }

    @Override
    public void onClose() {

    }

    @Override
    public boolean handles(Place place) {
        if (place instanceof GroupPlace && (GroupPlace.GROUP_TYPE_PLACE.getGroupType().equals(((GroupPlace) place).getGroupType()))) {
            return true;
        } else {
            return false;
        }
    }
}
