package za.co.ipay.metermng.client.event;

import com.google.gwt.event.shared.GwtEvent;

public class UpdateUsagePointGroupsListEvent extends GwtEvent<UpdateUsagePointGroupsListHandler> {

	public static Type<UpdateUsagePointGroupsListHandler> TYPE = new Type<UpdateUsagePointGroupsListHandler>();
	
	private Long groupTypeId;
	private Long selected;
	
	public UpdateUsagePointGroupsListEvent() {
    }
	
	public UpdateUsagePointGroupsListEvent(Long groupTypeId) {
	    this.groupTypeId = groupTypeId;
	}
	
	public UpdateUsagePointGroupsListEvent(Long groupTypeId, Long selectedId) {
        this.groupTypeId = groupTypeId;
        this.selected = selectedId;
    }
	
	@Override
	public Type<UpdateUsagePointGroupsListHandler> getAssociatedType() {
		return TYPE;
	}

	@Override
	protected void dispatch(UpdateUsagePointGroupsListHandler handler) {
		handler.updateUsagePointGroupList(this);
	}

    public Long getGroupTypeId() {
        return groupTypeId;
    }

    public void setGroupTypeId(Long groupTypeId) {
        this.groupTypeId = groupTypeId;
    }

    public Long getSelected() {
        return selected;
    }

    public void setSelected(Long selected) {
        this.selected = selected;
    }

    @Override
    public String toString() {
        return "UpdateUsagePointGroupsListEvent [groupTypeId=" + groupTypeId + ", selected=" + selected + "]";
    }
	
	

}
