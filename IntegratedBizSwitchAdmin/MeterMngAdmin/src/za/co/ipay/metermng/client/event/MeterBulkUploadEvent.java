package za.co.ipay.metermng.client.event;

import com.google.gwt.event.shared.GwtEvent;

public class MeterBulkUploadEvent extends GwtEvent<MeterBulkUploadEventHandler> {
	
	public static Type<MeterBulkUploadEventHandler> TYPE = new Type<MeterBulkUploadEventHandler>();
    
    private String name;
    
    public MeterBulkUploadEvent(String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }

    @Override
    public Type<MeterBulkUploadEventHandler> getAssociatedType() {
        return TYPE;
    }

    @Override
    protected void dispatch(MeterBulkUploadEventHandler handler) {
        handler.handleEvent(this);
    }
    
}