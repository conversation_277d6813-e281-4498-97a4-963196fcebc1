package za.co.ipay.metermng.client.view.workspace.bulkupload.metercustupbulkupload;

import com.google.gwt.place.shared.Place;
import za.co.ipay.gwt.common.client.Dialogs;
import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.gwt.common.client.workspace.WorkspaceCreateCallback;
import za.co.ipay.gwt.common.client.workspace.WorkspaceFactory;
import za.co.ipay.gwt.common.shared.exception.AccessControlException;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.history.MeterCustUPUploadPlace;
import za.co.ipay.metermng.client.resource.image.MediaResource.MediaResourceUtil;
import za.co.ipay.metermng.shared.MeterMngStatics;

import java.util.logging.Logger;

public class MeterCustUPUploadWorkspaceFactory implements WorkspaceFactory {
    
    private ClientFactory clientFactory;
    private static Logger logger = Logger.getLogger(MeterCustUPUploadWorkspaceFactory.class.getName());

    public MeterCustUPUploadWorkspaceFactory(ClientFactory clientFactory) {
        this.clientFactory = clientFactory;
        clientFactory.getWorkspaceContainer().register(this);
    }

	@Override
	public void createWorkspace(final Place place, final WorkspaceCreateCallback workspaceCreateCallback) {

		if (!clientFactory.getUser().hasPermission(MeterMngStatics.ACCESS_PERMISSION_MM_GENERATE_METERCUSTUP_UPLOAD_TEMPLATE)) {
			Dialogs.displayErrorMessage(MessagesUtil.getInstance().getMessage("error.accessdenied"),
					MediaResourceUtil.getInstance().getLockedIcon(),
					MessagesUtil.getInstance().getMessage("button.close"));
			workspaceCreateCallback.onWorkspaceCreationFailed(new AccessControlException("Access is Denied"));
			return;
        }
		try {
			MeterCustUPUploadWorkspaceView view = new MeterCustUPUploadWorkspaceView(clientFactory, (MeterCustUPUploadPlace) place);
			workspaceCreateCallback.onWorkspaceCreated(view);
		} catch (Exception e) {
			logger.info("MeterCustUPUploadWorkspaceView workspacecreation failed, exception= " + e);
			workspaceCreateCallback.onWorkspaceCreationFailed(e);
		}
    }
    
    @Override
    public boolean handles(Place place) {
        if (place instanceof MeterCustUPUploadPlace) {
            return true;
        } else {
            return false;
        }
    }
}
