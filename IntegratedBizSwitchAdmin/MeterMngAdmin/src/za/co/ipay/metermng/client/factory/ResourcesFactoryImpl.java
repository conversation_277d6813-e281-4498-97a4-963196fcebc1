package za.co.ipay.metermng.client.factory;

import za.co.ipay.gwt.common.client.factory.ResourcesFactory;
import za.co.ipay.gwt.common.client.resource.Messages;
import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.metermng.client.resource.image.MediaResource.MediaResourceUtil;
import za.co.ipay.metermng.client.resource.pager.CustomPagerResources;
import za.co.ipay.metermng.client.resource.table.CustomCellTableResources;

import com.google.gwt.core.client.GWT;
import com.google.gwt.resources.client.ImageResource;
import com.google.gwt.user.cellview.client.CellTable;
import com.google.gwt.user.cellview.client.CellTable.Resources;
import com.google.gwt.user.cellview.client.SimplePager;

public class ResourcesFactoryImpl implements ResourcesFactory {
    
    private static CellTable.Resources cellTableResources = (CellTable.Resources) GWT.create(CustomCellTableResources.class);
    
    private static SimplePager.Resources pagerResources = (SimplePager.Resources) GWT.create(CustomPagerResources.class);
    
    @Override
    public Resources getCellTableResources() {
        return cellTableResources;
    }
    
    @Override
    public SimplePager.Resources getPagerResources() {
        return pagerResources;
    }

    @Override
    public Messages getMessages() {
        return MessagesUtil.getInstance();
    }

    @Override
    public ImageResource getQuestionIcon() {
        return MediaResourceUtil.getInstance().getQuestionIcon();
    }
}