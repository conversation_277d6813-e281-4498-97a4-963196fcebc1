package za.co.ipay.metermng.client.event;

import za.co.ipay.metermng.client.history.UsagePointPlace;
import com.google.gwt.event.shared.GwtEvent;

public class UsagePointSearchEvent extends GwtEvent<UsagePointSearchEventHandler> {

    public static Type<UsagePointSearchEventHandler> TYPE = new Type<UsagePointSearchEventHandler>();
    private final UsagePointPlace usagePointPlace;

    public UsagePointSearchEvent(UsagePointPlace usagePointPlace) {
        this.usagePointPlace = usagePointPlace;
    }

    public UsagePointPlace getUsagePointPlace() {
        return usagePointPlace;
    }

    public boolean isSearchFromURL() {
        return usagePointPlace.isFromURL();
    }

    @Override
    public Type<UsagePointSearchEventHandler> getAssociatedType() {
        return TYPE;
    }

    @Override
    protected void dispatch(UsagePointSearchEventHandler handler) {
        handler.searchByUsagePoint(this);
    }

}
