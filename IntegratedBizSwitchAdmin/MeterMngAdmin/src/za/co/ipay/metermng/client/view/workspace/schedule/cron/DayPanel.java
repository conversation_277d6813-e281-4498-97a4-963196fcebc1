package za.co.ipay.metermng.client.view.workspace.schedule.cron;

import com.google.gwt.core.client.GWT;
import com.google.gwt.event.dom.client.ChangeEvent;
import com.google.gwt.event.shared.HandlerRegistration;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.user.client.ui.TextBox;
import com.google.gwt.user.client.ui.Widget;
import za.co.ipay.gwt.common.client.form.FormElement;
import za.co.ipay.gwt.common.client.form.SimpleForm;
import za.co.ipay.gwt.common.client.handler.FormDataChangeHandler;
import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.metermng.client.form.SimpleFormPanel;

public class DayPanel extends SimpleFormPanel {


    @UiField FormElement timeElement;
    @UiField TextBox timeBox;
    HandlerRegistration timeReg;

    private static DayPanelUiBinder uiBinder = GWT.create(DayPanelUiBinder.class);

    interface DayPanelUiBinder extends UiBinder<Widget, DayPanel> {

    }

    public DayPanel(SimpleForm form) {
        super(form);
        initWidget(uiBinder.createAndBindUi(this));
        initUi();
        addFieldHandlers();
    }

    private void initUi() {
        timeBox.getElement().setAttribute("type", "time");
    }

    @Override
    public void addFieldHandlers() {
        timeReg = timeBox.addHandler(new FormDataChangeHandler(form), ChangeEvent.getType());
    }

    @Override
    public void clearFields() {
        timeBox.setText("");
    }

    @Override
    public void clearErrors() {
        timeElement.setErrorMsg(null);
    }

    public boolean isValidInput() {
        clearErrors();
        boolean valid = true;
        if (!isValidTimeString()) {
            valid = false;
            timeElement.setErrorMsg(MessagesUtil.getInstance().getMessage("taskschedule.error.time"));
        }
        return valid;
    }

    private boolean isValidTimeString() {
        boolean result = false;
        try {
            Integer minute = Integer.valueOf(getMinute());
            Integer hour = Integer.valueOf(getHour());

            if (hour >= 0 && hour < 24 && minute >= 0 && minute < 60) {
                result = true;
            }

        } catch (NumberFormatException e) {
            result = false;
        }

        return result;
    }

    public void setTime(int hour, int min) {
        String hourString = (String.valueOf(hour).length() == 1) ? "0" + hour : String.valueOf(hour);
        String minuteString = (String.valueOf(min).length() == 1) ? "0" + min : String.valueOf(min);
        timeBox.setValue(hourString + ":" + minuteString);
    }

    public String getMinute() {
        String[] hoursAndMinutes = splitHoursAndMinutes();
        return hoursAndMinutes[1];
    }

    public String getHour() {

        String[] hoursAndMinutes = splitHoursAndMinutes();
        return hoursAndMinutes[0];
    }

    private String[] splitHoursAndMinutes() {
        String timeString = timeBox.getValue();
        return timeString.split(":");
    }

    public void removeFieldHandlers() {
        timeReg.removeHandler();
    }
}