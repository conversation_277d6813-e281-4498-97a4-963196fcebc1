package za.co.ipay.metermng.client.history;

import com.google.gwt.place.shared.Place;
import com.google.gwt.place.shared.PlaceTokenizer;
import com.google.gwt.place.shared.Prefix;

public class TaskSchedulePlace extends Place {

    public static TaskSchedulePlace ALL = new TaskSchedulePlace("all");
    
    private String name;

    public TaskSchedulePlace() {
    }
    
    public TaskSchedulePlace(String name) {
        this.name = name;
    }
    
    public String getName() {
        return name;
    }

    public static String toPlaceString(TaskSchedulePlace p) {
        if (p != null) {
            return "taskschedule:"+p.getName();
        } else {
            return "taskschedule:";
        }
    }

    @Prefix(value = "taskschedule")
    public static class Tokenizer implements PlaceTokenizer<TaskSchedulePlace> {
        @Override
        public String getToken(TaskSchedulePlace place) {
            return place.getName();
        }

        @Override
        public TaskSchedulePlace getPlace(String token) {
            return new TaskSchedulePlace(token);
        }
    }
}
