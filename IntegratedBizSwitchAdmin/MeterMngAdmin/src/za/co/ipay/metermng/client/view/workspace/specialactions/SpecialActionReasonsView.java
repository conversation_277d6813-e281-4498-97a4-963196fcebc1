package za.co.ipay.metermng.client.view.workspace.specialactions;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.logging.Logger;

import com.google.gwt.event.dom.client.ClickEvent;
import com.google.gwt.event.dom.client.ClickHandler;
import com.google.gwt.user.cellview.client.CellTable;
import com.google.gwt.user.cellview.client.ColumnSortEvent;
import com.google.gwt.user.cellview.client.ColumnSortEvent.ListHandler;
import com.google.gwt.user.cellview.client.TextColumn;
import com.google.gwt.user.client.ui.Anchor;
import com.google.gwt.view.client.ListDataProvider;

import za.co.ipay.gwt.common.client.Dialogs;
import za.co.ipay.gwt.common.client.form.FormManager;
import za.co.ipay.gwt.common.client.handler.ConfirmHandler;
import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.gwt.common.client.validation.ClientValidatorUtil;
import za.co.ipay.gwt.common.client.workspace.SessionCheckCallback;
import za.co.ipay.gwt.common.client.workspace.SimpleTableView;
import za.co.ipay.gwt.common.shared.exception.AccessControlException;
import za.co.ipay.metermng.client.event.SpecialActionReasonsUpdatedEvent;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.history.SpecialActionsPlace;
import za.co.ipay.metermng.client.resource.image.MediaResource.MediaResourceUtil;
import za.co.ipay.metermng.client.rpc.callback.ClientCallback;
import za.co.ipay.metermng.client.view.component.BaseComponent;
import za.co.ipay.metermng.client.widget.StatusTableColumn;
import za.co.ipay.metermng.datatypes.RecordStatus;
import za.co.ipay.metermng.mybatis.generated.model.SpecialActionReasons;
import za.co.ipay.metermng.mybatis.generated.model.SpecialActions;

public class SpecialActionReasonsView extends BaseComponent implements FormManager<SpecialActionReasons> {

	private static Logger logger = Logger.getLogger(SpecialActionReasonsView.class.getName());
	private SpecialActionsWorkspaceView parentWorkspace;

	private SpecialActionReasons specialActionReasons;

	private ArrayList<SpecialActionReasons> specialActionReasonsList;
	private SimpleTableView<SpecialActionReasons> view;
	private SpecialActionReasonsPanel panel;

	private ListDataProvider<SpecialActionReasons> dataProvider = new ListDataProvider<SpecialActionReasons>();

	private ListHandler<SpecialActionReasons> columnSortHandler;
	private TextColumn<SpecialActionReasons> nameColumn;

	private SpecialActions specialActions;

    public SpecialActionReasonsView( SpecialActionsWorkspaceView parentWorkspace,
                                ClientFactory clientFactory,
                                SimpleTableView<SpecialActionReasons> view) {

        this.parentWorkspace = parentWorkspace;
        this.clientFactory = clientFactory;
        this.view = view;
        initUi();
    }

    private void initUi() {
        initView();
        initForm();
        createTable();
    }

    private void initView() {
        view.setFormManager(this);

        Anchor anchor = new Anchor(MessagesUtil.getInstance().getMessage("special.actions.header"));
        anchor.addClickHandler(new ClickHandler() {
            @Override
            public void onClick(ClickEvent event) {
                goBack();
            }
        });
        view.getPageHeader().addPageHeaderLink(anchor);
    }

    private void initForm() {
        panel = new SpecialActionReasonsPanel(view.getForm());
        view.getForm().setHasDirtyDataManager(parentWorkspace);
        view.getForm().getFormFields().add(panel);

        view.getForm().getFormPanel().setHeadingText(MessagesUtil.getInstance().getMessage("special.action.reasons.title.add"));

        view.getForm().getSaveBtn().setText(MessagesUtil.getInstance().getMessage("button.create"));
        view.getForm().getSaveBtn().addClickHandler(new ClickHandler() {
            @Override
            public void onClick(ClickEvent event) {
                SessionCheckCallback sessionCheckCallback = new SessionCheckCallback() {
                    @Override
                    public void callback(SessionCheckResolution resolution) {
                        onSaveButtonClick();
                    }
                };
                clientFactory.handleSessionCheckCallback(sessionCheckCallback);
            }
        });
        view.getForm().getSaveBtn().ensureDebugId("saveButton");

        view.getForm().getOtherBtn().setText(MessagesUtil.getInstance().getMessage("button.cancel"));
        view.getForm().getOtherBtn().addClickHandler(new ClickHandler() {
            @Override
            public void onClick(ClickEvent event) {
                view.getForm().checkDirtyData(new ConfirmHandler() {
                    @Override
                    public void confirmed(boolean confirm) {
                        if (confirm) {
                            view.getForm().setDirtyData(false);
                            displaySelected(null);
                        }
                    }
                });
            }
        });
        view.getForm().getOtherBtn().ensureDebugId("cancelButton");

        view.getForm().getBackBtn().setVisible(true);
        view.getForm().getBackBtn().setText(MessagesUtil.getInstance().getMessage("button.back"));
        view.getForm().getBackBtn().addClickHandler(new ClickHandler() {
            @Override
            public void onClick(ClickEvent event) {
                goBack();
            }
        });
        view.getForm().getBackBtn().ensureDebugId("backBtn");
    }



    private void createTable() {
        nameColumn = new TextColumn<SpecialActionReasons>() {
            @Override
            public String getValue(SpecialActionReasons obj) {
                return obj.getReasonName();
            }
        };
        nameColumn.setSortable(true);

        TextColumn<SpecialActionReasons> descriptionColumn = new TextColumn<SpecialActionReasons>() {
            @Override
            public String getValue(SpecialActionReasons obj) {
                return obj.getReasonText();
            }
        };

        StatusTableColumn<SpecialActionReasons> recordStatusColumn = new StatusTableColumn<SpecialActionReasons>();

        view.getTable().addColumn(nameColumn, MessagesUtil.getInstance().getMessage("special.action.reasons.field.name"));
        view.getTable().addColumn(descriptionColumn, MessagesUtil.getInstance().getMessage("special.action.reasons.field.description"));
        view.getTable().addColumn(recordStatusColumn, MessagesUtil.getInstance().getMessage("special.action.reasons.field.recordstatus"));

        view.getTable().ensureDebugId("specialActionReasonsTable");

        dataProvider.addDataDisplay(view.getTable());
        view.getPager().setDisplay(view.getTable());
        view.getTable().setPageSize(getPageSize());
    }

    private void setSpecialActionReason(SpecialActionReasons spaReason) {
        panel.clearErrors();
        panel.clearFields();
        this.specialActionReasons = spaReason;
        if (specialActionReasons == null || specialActionReasons.getId()==null) {
            specialActionReasons = new SpecialActionReasons();
            specialActionReasons.setRecordStatus(RecordStatus.DAC);
            view.getForm().getSaveBtn().setText(MessagesUtil.getInstance().getMessage("button.create"));
            view.getForm().getFormPanel().setHeadingText(MessagesUtil.getInstance().getMessage("special.action.reasons.title.add"));
            view.clearTableSelection();
            panel.mridComponent.initMrid(clientFactory);
        } else {
            view.getForm().getSaveBtn().setText(MessagesUtil.getInstance().getMessage("button.update"));
            view.getForm().getFormPanel().setHeadingText(MessagesUtil.getInstance().getMessage("special.action.reasons.title.update"));
            panel.mridComponent.setMrid(specialActionReasons.getMrid());
            panel.mridComponent.setIsExternal(specialActionReasons.isMridExternal());
        }

        panel.nameTextBox.setText(specialActionReasons.getReasonName());
        panel.descriptionTextBox.setText(specialActionReasons.getReasonText());
        panel.activeBox.setValue(RecordStatus.ACT.equals(specialActionReasons.getRecordStatus()));

    }

    public void refreshTable() {
        clientFactory.getSpecialActionsRpc().getSpecialActionReasons(specialActions.getId(),
                new ClientCallback<ArrayList<SpecialActionReasons>>() {
                    @Override
                    public void onSuccess(ArrayList<SpecialActionReasons> result) {
                        specialActionReasonsList = result;
                        dataProvider.setList(specialActionReasonsList);
                        dataProvider.refresh();
                        CellTable<SpecialActionReasons> table = view.getTable();
                        if (specialActionReasons != null) {
                            table.getSelectionModel().setSelected(specialActionReasons, true);
                        }
                        if (columnSortHandler == null || columnSortHandler.getList() == null) {
                            columnSortHandler = new ListHandler<SpecialActionReasons>(dataProvider.getList());
                            columnSortHandler.setComparator(nameColumn, new Comparator<SpecialActionReasons>() {
                                public int compare(SpecialActionReasons o1, SpecialActionReasons o2) {
                                    if (o1 == o2) {
                                        return 0;
                                    }
                                    if (o1 != null && o1.getReasonName() != null) {
                                        return (o2 != null && o2.getReasonName() != null)
                                                ? o1.getReasonName().compareTo(o2.getReasonName())
                                                : 1;
                                    }
                                    return -1;
                                }
                            });
                            table.addColumnSortHandler(columnSortHandler);
                            table.getColumnSortList().push(nameColumn);
                        } else {
                            columnSortHandler.setList(dataProvider.getList());
                        }
                        ColumnSortEvent.fire(table, table.getColumnSortList());
                        table.setPageStart(0);
                    }
                });
    }

    private void onSaveButtonClick() {
        if (view.getForm().getSaveBtn().getText().equals(MessagesUtil.getInstance().getMessage("button.create"))){
            addSpecialActionReason();
        } else {
            updateSpecialActionReason();
        }
    }

    private void update() {
        if (specialActionReasons == null) {
            this.specialActionReasons = new SpecialActionReasons();
        }
        specialActionReasons.setSpecialActionsId(specialActions.getId());
        specialActionReasons.setReasonName(panel.nameTextBox.getText());
        specialActionReasons.setReasonText(panel.descriptionTextBox.getText());
        specialActionReasons.setRecordStatus((panel.activeBox.getValue()?RecordStatus.ACT:RecordStatus.DAC));
        specialActionReasons.setMrid(panel.mridComponent.getMrid());
        specialActionReasons.setMridExternal(panel.mridComponent.isExternal());
    }

    private boolean isValidInput() {
        boolean valid = true;

        panel.clearErrors();

        if (!ClientValidatorUtil.getInstance().validateField(specialActionReasons, "reasonName", panel.nameElement)) {
            valid = false;
            logger.info("Invalid reason name: "+specialActionReasons.getReasonName());
        }

        if (!ClientValidatorUtil.getInstance().validateField(specialActionReasons, "reasonText", panel.descriptionElement)) {
            valid = false;
        }

        if (!ClientValidatorUtil.getInstance().validateField(specialActionReasons, "mrid", panel.mridComponent.getTxtbxMridElement())) {
            valid = false;
        }

        if (!panel.mridComponent.validate()) {
            valid = false;
        }

        return valid;
    }

    public void addSpecialActionReason() {
        update();
        if (isValidInput()) {
            clientFactory.getSpecialActionsRpc().addSpecialActionReason(specialActionReasons, new ClientCallback<SpecialActionReasons>(view.getForm().getSaveBtn().getAbsoluteLeft(), view.getForm().getSaveBtn().getAbsoluteTop()) {
                @Override
                public void onSuccess(SpecialActionReasons result) {
                    if (result != null) {
                        Dialogs.displayInformationMessage(MessagesUtil.getInstance().getSavedMessage(new String[] { MessagesUtil.getInstance().getMessage("special.action.reason") }),
                        MediaResourceUtil.getInstance().getInformationIcon(),
                        view.getForm().getSaveBtn().getAbsoluteLeft(), view.getForm().getSaveBtn().getAbsoluteTop(),
                        MessagesUtil.getInstance().getMessage("button.close"));
                        specialActionReasonsList.add((SpecialActionReasons) result);
                        dataProvider.setList(specialActionReasonsList);
                        dataProvider.refresh();
                        setSpecialActionReason(null);
                        clientFactory.getEventBus().fireEvent(new SpecialActionReasonsUpdatedEvent());
                    }
                }

                @Override
                public void onFailure(Throwable caught) {
                    if (caught instanceof AccessControlException) {
                        clientFactory.getWorkspaceContainer().closeWorkspaceNow(SpecialActionsPlace.ALL);
                    }
                    super.onFailure(caught);
                };
            });
        }
    }

    public void updateSpecialActionReason() {
        update();
        if (isValidInput()) {
            clientFactory.getSpecialActionsRpc().updateSpecialActionReason(specialActionReasons,new ClientCallback<SpecialActionReasons>(view.getForm().getSaveBtn().getAbsoluteLeft(), view.getForm().getSaveBtn().getAbsoluteTop()) {
                    @Override
                    public void onSuccess(SpecialActionReasons result) {
                         if (result != null) {
                            int index = -1;
                            for (int i = 0; i < specialActionReasonsList.size(); i++) {
                                if (specialActionReasonsList.get(i).getId().equals(result.getId())){
                                    index = i;
                                    break;
                                }
                            }
                            if (index > -1) {
                                setSpecialActionReason(null);
                                Dialogs.displayInformationMessage(MessagesUtil.getInstance().getSavedMessage(new String[] { MessagesUtil.getInstance().getMessage("special.action.reason") }),
                                    MediaResourceUtil.getInstance().getInformationIcon(),
                                    view.getForm().getSaveBtn().getAbsoluteLeft(), view.getForm().getSaveBtn().getAbsoluteTop(),
                                    MessagesUtil.getInstance().getMessage("button.close"));
                                specialActionReasonsList.set(index, result);
                                dataProvider.setList(specialActionReasonsList);
                                dataProvider.refresh();
                                clientFactory.getEventBus().fireEvent(new SpecialActionReasonsUpdatedEvent());
                            }
                         }
                    }

                    @Override
                    public void onFailure(Throwable caught) {
                        if (caught instanceof AccessControlException) {
                            clientFactory.getWorkspaceContainer().closeWorkspaceNow(SpecialActionsPlace.ALL);
                        }
                        super.onFailure(caught);
                    };
                });

            }
    }

    public void goBack() {
        view.getForm().getHasDirtyDataManager().checkAnyDirtyData(new ConfirmHandler() {
            @Override
            public void confirmed(boolean confirm) {
                if (confirm) {
                    view.getForm().setDirtyData(false);
                    parentWorkspace.goToSpecialActions(specialActions.getId());
                }
            }
        });
    }

    @Override
    public void displaySelected(SpecialActionReasons selected) {
        setSpecialActionReason(selected);
    }

    public void initSpecialActionReasons(SpecialActions specialActions) {
        this.specialActions = specialActions;
        if (specialActions != null) {
            view.setDataDetails(specialActions.getSpecialActionsName(), specialActions.getSpecialActionsDescription());
            refreshTable();
            setSpecialActionReason(null);
        }
    }

}
