<!DOCTYPE ui:UiBinder SYSTEM "http://dl.google.com/gwt/DTD/xhtml.ent">
<ui:UiBinder xmlns:ui="urn:ui:com.google.gwt.uibinder" 
             xmlns:g="urn:import:com.google.gwt.user.client.ui"
             xmlns:p1="urn:import:za.co.ipay.gwt.common.client.form" 
             xmlns:g2="urn:import:com.google.gwt.user.datepicker.client"
             xmlns:w="urn:import:za.co.ipay.gwt.common.client.widgets">
    
    <ui:with field="msg" type="za.co.ipay.metermng.client.i18n.UiMessages" />

    <g:FlowPanel>
    
        <p1:FormRowPanel>
            <p1:FormElement ui:field="superMeterNumberElement" labelText="{msg.getSuperMeter}:" required="true">
                <g:ListBox ui:field="superMeterNumberBox" styleName="gwt-TextBox" />
            </p1:FormElement>
        </p1:FormRowPanel>
        
        <p1:FormRowPanel>
            <p1:FormElement ui:field="startElement" labelText="{msg.getMeterReadingsStart}:" required="true">
                <g2:DateBox ui:field="startBox" styleName="gwt-TextBox" />
            </p1:FormElement>

            <p1:FormElement ui:field="endElement" labelText="{msg.getMeterReadingsEnd}:" required="true">
                <g2:DateBox ui:field="endBox" styleName="gwt-TextBox" />
            </p1:FormElement>
        </p1:FormRowPanel>
        
        <p1:FormRowPanel>
            <p1:FormElement ui:field="readingIntervalElement" labelText="{msg.getMeterReadingInterval}:" required="true">
                <g:ListBox ui:field="readingIntervalBox" />
            </p1:FormElement>
        </p1:FormRowPanel>
        
        <p1:FormRowPanel>
            <p1:FormElement ui:field="readingTypeElement" labelText="{msg.getMeterReadingType}:" required="true">
                <g:ListBox ui:field="readingTypeBox" />
            </p1:FormElement>
        </p1:FormRowPanel>
        
        <p1:FormRowPanel>
            <p1:FormElement ui:field="deleteElement" labelText="{msg.getDeleteExistingSuperMeterReadings}:">
                <g:CheckBox ui:field="deleteBox" text="{msg.getDeleteExistingSuperMeterReadings}" value="true" />
            </p1:FormElement>
        </p1:FormRowPanel>
        
        <p1:FormRowPanel>
            <p1:FormElement ui:field="regenerateSubMetersElement" labelText="{msg.getRegenerateSubMeterReadings}:">
                <g:CheckBox ui:field="regenerateSubMetersBox" text="{msg.getRegenerateSubMeterReadings}" value="true" />
            </p1:FormElement>
        </p1:FormRowPanel>
        
        <p1:FormRowPanel>
            <p1:FormElement ui:field="variationsElement" labelText="{msg.getMeterReadingVariations}:" required="true" helpMsg="{msg.getMeterReadingVariationsHelp}" >
	            <p1:FormElement ui:field="hourOfDayElement" labelText="{msg.getHour}:" required="true" >
	                <g:ListBox ui:field="hourOfDayBox" styleName="gwt-TextBox" />
	            </p1:FormElement>
	            
	            <p1:FormElement ui:field="percentageElement" labelText="{msg.getPercent}:" required="true" >
                    <g:ListBox ui:field="percentageBox" styleName="gwt-TextBox" />
                </p1:FormElement>
	            
	            <p1:FormElement ui:field="addElement" labelText="" required="false">
	                <g:Button ui:field="addButton" text="&gt;" />
	            </p1:FormElement>
	            <p1:FormElement ui:field="addedVariationsElement" labelText="{msg.getMeterReadingVariations}:">
	                <g:VerticalPanel>
	                    <g:ListBox ui:field="addedVariationsBox" styleName="gwt-TextBox veryWideSelect"  multipleSelect="true" visibleItemCount="5" />
	                    <g:Button ui:field="removeButton" text="{msg.getRemoveButton}" styleName="gwt-Button verticalSpace" />
	                </g:VerticalPanel>
	            </p1:FormElement>
            </p1:FormElement>
        </p1:FormRowPanel>
        
    </g:FlowPanel>
    
</ui:UiBinder> 