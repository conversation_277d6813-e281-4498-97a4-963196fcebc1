package za.co.ipay.metermng.client.view.workspace.billingdet;

import java.util.List;

import com.google.gwt.core.client.GWT;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.user.client.ui.CheckBox;
import com.google.gwt.user.client.ui.Label;
import com.google.gwt.user.client.ui.ListBox;
import com.google.gwt.user.client.ui.RadioButton;
import com.google.gwt.user.client.ui.TextBox;
import com.google.gwt.user.client.ui.Widget;

import za.co.ipay.gwt.common.client.form.FormElement;
import za.co.ipay.gwt.common.client.form.SimpleForm;
import za.co.ipay.gwt.common.client.handler.FormDataChangeHandler;
import za.co.ipay.gwt.common.client.handler.FormDataClickHandler;
import za.co.ipay.metermng.client.form.SimpleFormPanel;
import za.co.ipay.metermng.shared.dto.BillingDetAppliesDto;
import za.co.ipay.metermng.shared.dto.IdNameDto;

public class BillingDetPanel extends SimpleFormPanel {
    
    @UiField TextBox nameTextBox;
    @UiField TextBox descriptionTextBox;
    @UiField CheckBox activeBox;
    @UiField CheckBox discountBox;
    @UiField RadioButton radioNone;
    @UiField RadioButton radioPercentage;
    @UiField RadioButton radioFlatRate;
    @UiField ListBox appliesToListBox;
    @UiField Label appliesToError;
    @UiField CheckBox taxableBox;
    
    @UiField FormElement activeElement;
    @UiField FormElement nameElement;
    @UiField FormElement descriptionElement;
    @UiField FormElement discountElement;
    @UiField FormElement unitChargeType;
    @UiField FormElement appliesToElement;
    @UiField FormElement taxableElement;
    
    private static BillingDetPanelUiBinder uiBinder = GWT.create(BillingDetPanelUiBinder.class);

    interface BillingDetPanelUiBinder extends UiBinder<Widget, BillingDetPanel> {
    }

    public BillingDetPanel(SimpleForm form) {
        super(form);
        initWidget(uiBinder.createAndBindUi(this));
        addFieldHandlers();
    }
    
    public void clearFields() {
        form.setDirtyData(false);
        nameTextBox.setText("");
        descriptionTextBox.setText("");
        activeBox.setValue(true);
        discountBox.setValue(false);
        radioNone.setValue(true);
        appliesToListBox.setSelectedIndex(0);
        appliesToError.setText("");
        appliesToError.setVisible(false);
        taxableBox.setValue(true);
    }

    public void clearErrors() {
        activeElement.clearErrorMsg();
        nameElement.clearErrorMsg();
        descriptionElement.clearErrorMsg();
        discountElement.clearErrorMsg();
        unitChargeType.clearErrorMsg();
        appliesToElement.clearErrorMsg();
        appliesToError.setText("");
        appliesToError.setVisible(false);
        taxableElement.clearErrorMsg();
    }

    @Override
    public void addFieldHandlers() {
        nameTextBox.addChangeHandler(new FormDataChangeHandler(form));
        descriptionTextBox.addChangeHandler(new FormDataChangeHandler(form));
        activeBox.addClickHandler(new FormDataClickHandler(form));
        discountBox.addClickHandler(new FormDataClickHandler(form));
        appliesToListBox.addChangeHandler(new FormDataChangeHandler(form));
        taxableBox.addClickHandler(new FormDataClickHandler(form));
    }
    
    public void populateAppliesToDropDown(List<BillingDetAppliesDto> billingDetDtoList) {
        appliesToListBox.clear();
        appliesToListBox.addItem("");
        for (BillingDetAppliesDto bd : billingDetDtoList) {
            if (bd.getAppliesToBillingDets() == null || bd.getAppliesToBillingDets().isEmpty()) {
                appliesToListBox.addItem(bd.getName(), bd.getId().toString());
            }
        }
    }
    
    public void setSelectedAppliesTo(BillingDetAppliesDto billingDetDto) {
        for (IdNameDto parentBd : billingDetDto.getAppliesToBillingDets()) {
            for(int i=0;i<appliesToListBox.getItemCount();i++) {
                if (parentBd.getId().toString().equals(appliesToListBox.getValue(i))) {
                    appliesToListBox.setItemSelected(i, true);
                    break;
                }    
            }
        }
    }
}