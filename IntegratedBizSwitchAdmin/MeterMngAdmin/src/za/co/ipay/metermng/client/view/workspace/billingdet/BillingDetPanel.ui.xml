<!DOCTYPE ui:UiBinder SYSTEM "http://dl.google.com/gwt/DTD/xhtml.ent">
<ui:UiBinder xmlns:ui="urn:ui:com.google.gwt.uibinder" 
             xmlns:g="urn:import:com.google.gwt.user.client.ui"
             xmlns:p1="urn:import:za.co.ipay.gwt.common.client.form">
	<ui:style>	
        .redWarning {
            color: Red;
        }
	</ui:style>
	
	  <ui:with field="msg" type="za.co.ipay.metermng.client.i18n.UiMessages" />

	  <g:FlowPanel>
	    <p1:FormRowPanel>
	      <p1:FormElement debugId="nameElement" ui:field="nameElement" labelText="{msg.getBillingDetName}:" helpMsg="{msg.getBillingDetNameHelp}" required="true">
	        <g:TextBox debugId="nameBox" text="" ui:field="nameTextBox" title="{msg.getBillingDetName}" width="50" visibleLength="30"/>
	      </p1:FormElement>
          <p1:FormElement ui:field="activeElement" labelText="{msg.getBillingDetActive}:"  helpMsg="{msg.getBillingDetActiveHelp}">
            <g:CheckBox ui:field="activeBox" debugId="activeBox" checked="true"/>
          </p1:FormElement>
        </p1:FormRowPanel>
	    <p1:FormRowPanel>
          <p1:FormElement debugId="descriptionElement" ui:field="descriptionElement" labelText="{msg.getBillingDetDescription}:" helpMsg="{msg.getBillingDetDescriptionHelp}">
            <g:TextBox debugId="descriptionBox" ui:field="descriptionTextBox" title="{msg.getBillingDetDescription}" visibleLength="50" styleName="gwt-TextBox"/>
          </p1:FormElement>
	    </p1:FormRowPanel>
        <p1:FormGroupPanel labelText="{msg.getBillingDetGroupLabel}" debugId="appliesToBillingDetFieldsPanel">
            <p1:FormRowPanel>
              <p1:FormElement debugId="discountElement" ui:field="discountElement" labelText="{msg.getBillingDetDiscount}:" helpMsg="{msg.getBillingDetDiscountHelp}">
                <g:CheckBox ui:field="discountBox" debugId="discountBox" checked="false"/>
              </p1:FormElement>
            </p1:FormRowPanel>
            <p1:FormRowPanel>
    
              <p1:FormElement debugId="unitChargeType" ui:field="unitChargeType" labelText="{msg.getChargeType}:" helpMsg="{msg.getBillingDetChargeTypeHelp}" required="true">
                  <g:RadioButton debugId="radioNone" ui:field="radioNone" name="groupUnitChargeType" value="true" text="{msg.getChargeTypeNone}" checked="true"/>
                  <g:RadioButton debugId="radioPercentage" ui:field="radioPercentage" name="groupUnitChargeType" text="{msg.getChargeTypePercentage}"/>
                  <g:RadioButton debugId="radioFlatRate" ui:field="radioFlatRate" name="groupUnitChargeType" text="{msg.getChargeTypeFlatRate}"/>
              </p1:FormElement>
              
              <p1:FormElement debugId="appliesToElement" ui:field="appliesToElement" labelText="{msg.getBillingDetAppliesToLabel}:" helpMsg="{msg.getBillingDetAppliesToHelp}">
                <g:ListBox debugId="appliesToListBox" ui:field="appliesToListBox" title="{msg.getBillingDetAppliesToLabel}" styleName="gwt-TextBox" multipleSelect="true" visibleItemCount="3"/>
              </p1:FormElement>
            </p1:FormRowPanel>
            
            <p1:FormRowPanel>
              <p1:FormElement debugId="taxableElement" ui:field="taxableElement" labelText="{msg.getBillingDetTaxable}:"  helpMsg="{msg.getBillingDetTaxableHelp}">
                <g:CheckBox ui:field="taxableBox" debugId="taxableBox" checked="true"/>
              </p1:FormElement>
            </p1:FormRowPanel>
            
            <p1:FormRowPanel>
                <g:Label debugId="appliesToError" ui:field="appliesToError" text="" styleName="{style.redWarning}" visible="false"/>
            </p1:FormRowPanel>
        </p1:FormGroupPanel>
	  </g:FlowPanel>
	
</ui:UiBinder> 