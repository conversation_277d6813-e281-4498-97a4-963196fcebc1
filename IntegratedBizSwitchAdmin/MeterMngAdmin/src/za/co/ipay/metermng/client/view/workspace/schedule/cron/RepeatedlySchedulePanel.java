package za.co.ipay.metermng.client.view.workspace.schedule.cron;


import java.util.logging.Logger;

import za.co.ipay.gwt.common.client.form.FormElement;
import za.co.ipay.gwt.common.client.form.IntegerValueBox;
import za.co.ipay.gwt.common.client.form.SimpleForm;
import za.co.ipay.gwt.common.client.handler.FormDataChangeHandler;
import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.metermng.client.form.SimpleFormPanel;
import za.co.ipay.metermng.shared.MeterMngStatics;

import com.google.gwt.core.client.GWT;
import com.google.gwt.event.shared.HandlerRegistration;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.user.client.ui.ListBox;
import com.google.gwt.user.client.ui.Widget;

public class RepeatedlySchedulePanel extends SimpleFormPanel implements ScheduleUi {
    
    //0 0/15 * * * ?
    //0/15 in the Minutes field, it means 'every 15th minute of the hour, starting at minute zero'
    //'3/20' in the Minutes field, it would mean 'every 20th minute of the hour, starting at minute three' - ie: '3,23,43' in the Minutes field. 
    
    //0 0 0/1 * * ? *

    @UiField FormElement numberElement;
    @UiField FormElement unitsElement;
    @UiField IntegerValueBox numberBox;
    @UiField ListBox unitsBox;
    
    HandlerRegistration numberReg;
    HandlerRegistration unitsReg;
    
    private static Logger logger = Logger.getLogger(RepeatedlySchedulePanel.class.getName());
    
    private static HourlySchedulePanelUiBinder uiBinder = GWT.create(HourlySchedulePanelUiBinder.class);

    interface HourlySchedulePanelUiBinder extends UiBinder<Widget, RepeatedlySchedulePanel> {
    }

    public RepeatedlySchedulePanel(SimpleForm form) {
        super(form);
        initWidget(uiBinder.createAndBindUi(this));
        addFieldHandlers();initUi();
    }
    
    private void initUi() {
        unitsBox.addItem("");
        unitsBox.addItem(MessagesUtil.getInstance().getMessage("taskschedule.minutes"), MeterMngStatics.MINUTES_VALUE);
        unitsBox.addItem(MessagesUtil.getInstance().getMessage("taskschedule.hours"), MeterMngStatics.HOURS_VALUE);
    }
    
    @Override
    public void setCronExpression(String cronExpression) {
        logger.info(cronExpression);
        CronExpressionParser parser = new CronExpressionParser(cronExpression);
        String hourly = parser.getHours();
        logger.info("hourly: "+hourly);
        if (hourly != null) {            
            String[] fields = hourly.split("\\/");
            if (fields != null && fields.length == 2) {
                numberBox.setValue(Integer.valueOf(fields[1]));
                unitsBox.setSelectedIndex(2);
                return;
            }            
        } 
        
        String minutes = parser.getMinutes();
        logger.info("Minutes: "+minutes);
        if (minutes != null) {                
            String[] fields = minutes.split("\\/");
            if (fields != null && fields.length == 2) {
                numberBox.setValue(Integer.valueOf(fields[1]));
                unitsBox.setSelectedIndex(1);
            }            
        }
    }
    
    @Override
    public boolean isValidInput() {
        clearErrors();
        boolean valid = true;
        Integer number = numberBox.getValue();
        if (number == null || number.intValue() <= 0) {
            valid = false;
            numberElement.setErrorMsg(MessagesUtil.getInstance().getMessage("taskschedule.repeatedly.error.number"));
        }
        if (unitsBox.getSelectedIndex() < 1) {
            valid = false;
            unitsElement.setErrorMsg(MessagesUtil.getInstance().getMessage("taskschedule.repeatedly.error.units"));
        }
        return valid;
    }
    
    @Override
    public String getCronExpression() {      
        if (MeterMngStatics.MINUTES_VALUE.equals(unitsBox.getValue(unitsBox.getSelectedIndex()))) {
            return CronExpressionParser.getCronExpression(
                    "0",
                    "0/"+numberBox.getValue(),
                    CronExpressionParser.ALL,
                    CronExpressionParser.ALL,
                    CronExpressionParser.ALL,
                    CronExpressionParser.ANY);   
        } else if (MeterMngStatics.HOURS_VALUE.equals(unitsBox.getValue(unitsBox.getSelectedIndex()))) {
            return CronExpressionParser.getCronExpression(
                    "0",
                    "0",
                    "0/"+numberBox.getValue(),
                    CronExpressionParser.ALL,
                    CronExpressionParser.ALL,
                    CronExpressionParser.ANY);   
        } else {
            return "";
        }        
    }

    @Override
    public void addFieldHandlers() {
        numberReg = numberBox.addChangeHandler(new FormDataChangeHandler(form));
        unitsReg = unitsBox.addChangeHandler(new FormDataChangeHandler(form));
    }
    
    @Override
    public void removeFieldHandlers() {
        numberReg.removeHandler();
        unitsReg.removeHandler();
    }

    @Override
    public void clearFields() {
        numberBox.setValue(null);
        unitsBox.setSelectedIndex(0);
    }

    @Override
    public void clearErrors() {
        numberElement.setErrorMsg(null);
        unitsElement.setErrorMsg(null);
    }
}
