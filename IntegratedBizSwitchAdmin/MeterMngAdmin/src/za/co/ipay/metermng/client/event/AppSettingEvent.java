package za.co.ipay.metermng.client.event;

import com.google.gwt.event.shared.GwtEvent;
import za.co.ipay.metermng.mybatis.generated.model.AppSetting;

public class AppSettingEvent extends GwtEvent<AppSettingEventHandler> {

    public static Type<AppSettingEventHandler> TYPE = new Type<AppSettingEventHandler>();

    private String name;
    private AppSetting updatedSetting;

    public AppSettingEvent(String name) {
        this.name = name;
    }

    public AppSettingEvent(AppSetting updatedSetting) {
        this.updatedSetting = updatedSetting;
    }

    public String getName() {
        return name;
    }

    public AppSetting getUpdatedSetting() {
        return updatedSetting;
    }

    @Override
    public Type<AppSettingEventHandler> getAssociatedType() {
        return TYPE;
    }

    @Override
    protected void dispatch(AppSettingEventHandler handler) {
        handler.handleEvent(this);
    }
}
