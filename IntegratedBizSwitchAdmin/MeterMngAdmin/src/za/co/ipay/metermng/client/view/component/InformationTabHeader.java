package za.co.ipay.metermng.client.view.component;

import com.google.gwt.core.client.GWT;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.user.client.ui.Composite;
import com.google.gwt.user.client.ui.HTML;
import com.google.gwt.user.client.ui.HasWordWrap;
import com.google.gwt.user.client.ui.Image;
import com.google.gwt.user.client.ui.Widget;

public class InformationTabHeader extends Composite implements HasWordWrap {

    @UiField Image tabImage;
    @UiField HTML tabText;

    private static InformationTabHeaderUiBinder uiBinder = GWT.create(InformationTabHeaderUiBinder.class);

    interface InformationTabHeaderUiBinder extends UiBinder<Widget, InformationTabHeader> {
    }

    public InformationTabHeader() {
        initWidget(uiBinder.createAndBindUi(this));
    }

    public InformationTabHeader(String imageUrl, String text) {
        initWidget(uiBinder.createAndBindUi(this));
        tabImage.setUrl(imageUrl);
        tabText.setText(text);
    }

    public void setImageUrl(String imageUrl) {
        tabImage.setUrl(imageUrl);
    }

    public void setText(String text) {
        tabText.setText(text);
    }

    @Override
    public boolean getWordWrap() {
        return tabText.getWordWrap();
    }

    @Override
    public void setWordWrap(boolean arg0) {
        tabText.setWordWrap(arg0);
    }

    public void setStyleNameForText(String style) {
        tabText.setStyleName(style);
    }
}
