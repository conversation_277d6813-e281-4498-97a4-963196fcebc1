@def selectionBorderWidth 1px;

.cellTableWidget {
    border-collapse: collapse;
    border: selectionBorderWidth solid #bababa;
}

.cellTableFirstColumn {
 
}

.cellTableLastColumn {
 
}

.cellTableFooter {
}

.cellTableHeader {
	vertical-align: top;   
    background: #D3D3D3;    
    border: selectionBorderWidth solid #bababa;
    border-bottom: 2px solid #000000;
}

.cellTableCell {
  padding: 3px 15px;
  overflow: hidden;  
  border: selectionBorderWidth solid #bababa;
}

.cellTableFirstColumnFooter {
 
}

.cellTableFirstColumnHeader {
 
}

.cellTableLastColumnFooter {
 
}

.cellTableLastColumnHeader {
 
}

.cellTableSortableHeader {
  cursor: pointer;
  cursor: hand;
}

.cellTableSortableHeader:hover {
  color: #6c6b6b;
}

.cellTableSortedHeaderAscending {

}

.cellTableSortedHeaderDescending {

}

.cellTableEvenRow {
  background: #ffffff;
}

.cellTableEvenRowCell {
  border: selectionBorderWidth solid #bababa;
}

.cellTableOddRow {
  background: #f3f7fb;
}

.cellTableOddRowCell {
  border: selectionBorderWidth solid #bababa;
}

.cellTableHoveredRow {
  background: #eee;
  border: selectionBorderWidth solid #bababa;
}

.cellTableHoveredRowCell {
  border: selectionBorderWidth solid #bababa;
}

.cellTableKeyboardSelectedRow {
  background: lightgray;
}

.cellTableKeyboardSelectedRowCell {
  border: selectionBorderWidth solid lightgray;
}

.cellTableSelectedRow {
    background: lightgray;
    color: black;
}

.cellTableSelectedRowCell {
  border: selectionBorderWidth solid lightgray;
}

/**
 * The keyboard selected cell is visible over selection.
 */
.cellTableKeyboardSelectedCell {
  border: selectionBorderWidth solid #bababa;
}

@sprite .cellTableLoading {
  gwt-image: 'cellTableLoading';
  margin: 30px;
}
