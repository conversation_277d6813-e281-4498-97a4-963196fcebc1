package za.co.ipay.metermng.client.view.workspace.uploads;

import com.google.gwt.core.client.GWT;
import com.google.gwt.place.shared.Place;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.user.client.ui.Widget;
import za.co.ipay.gwt.common.client.resource.Messages;
import za.co.ipay.gwt.common.shared.exception.AccessControlException;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.history.GroupPlace;
import za.co.ipay.metermng.client.history.MetadataUploadPlace;
import za.co.ipay.metermng.client.rpc.callback.ClientCallback;
import za.co.ipay.metermng.client.view.component.group.MetadataUploadPanel;
import za.co.ipay.metermng.client.view.workspace.BaseWorkspace;
import za.co.ipay.metermng.shared.group.GroupTypeData;

import java.util.ArrayList;
import java.util.logging.Logger;

public class MetadataUploadWorkspaceView extends BaseWorkspace {

    @UiField(provided = true)
    MetadataUploadPanel uploadPanel;

    private static Logger logger = Logger.getLogger(MetadataUploadWorkspaceView.class.getName());

    private static MetadataUploadWorkspaceView.MetadataUploadWorkspaceViewUiBinder uiBinder = GWT.create(MetadataUploadWorkspaceView.MetadataUploadWorkspaceViewUiBinder.class);

    interface MetadataUploadWorkspaceViewUiBinder extends UiBinder<Widget, MetadataUploadWorkspaceView> {
    }

    MetadataUploadWorkspaceView(ClientFactory clientFactory, MetadataUploadPlace place) {
        this.clientFactory = clientFactory;
        uploadPanel = new MetadataUploadPanel(clientFactory);
        initWidget(uiBinder.createAndBindUi(this));
        setPlaceString(MetadataUploadPlace.getPlaceAsString(place));
        setHeaderText(Messages.MessagesUtil.getInstance().getMessage("metadata.upload.heading"));
    }

    @Override
    public boolean handles(Place place) {
        return (place instanceof MetadataUploadPlace);
    }
}
