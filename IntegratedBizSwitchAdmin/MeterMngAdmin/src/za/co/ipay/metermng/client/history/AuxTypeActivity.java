package za.co.ipay.metermng.client.history;

import za.co.ipay.metermng.client.event.OpenAuxTypeEvent;
import za.co.ipay.metermng.client.factory.ClientFactory;

import com.google.gwt.activity.shared.AbstractActivity;
import com.google.gwt.event.shared.EventBus;
import com.google.gwt.user.client.ui.AcceptsOneWidget;

public class AuxTypeActivity extends AbstractActivity {

private ClientFactory clientFactory;
    
    public AuxTypeActivity(ClientFactory clientFactory) {
        super();
        this.clientFactory = clientFactory;
    }
    
    @Override
    public void start(AcceptsOneWidget panel, EventBus eventBus) {
        clientFactory.getEventBus().fireEvent(new OpenAuxTypeEvent());
    }
}
