package za.co.ipay.metermng.client.resource.tree;

import com.google.gwt.resources.client.ImageResource;
import com.google.gwt.resources.client.ImageResource.ImageOptions;
import com.google.gwt.resources.client.ImageResource.RepeatStyle;
import com.google.gwt.user.cellview.client.CellTree.Resources;
import com.google.gwt.user.cellview.client.CellTree.Style;

/**
 * CustomCellTreeResources is a custom resources implementation that can be used instead of the default styles for a
 * CellTree. This resources instance uses a custom style sheet that modifies the default styles used for the tree.
 * You can also change the images used for the tree nodes too.
 * <AUTHOR>
 */
public interface CustomCellTreeResources extends Resources {

    @Override
    @Source("CustomCellTree.css")
    Style cellTreeStyle(); 
    
    @Override
    @Source("cellTreeClosedItem.gif")
    ImageResource cellTreeClosedItem();

    @Override
    @Source("cellTreeOpenItem.gif")
    ImageResource cellTreeOpenItem(); 
    
    @Override
    @Source("customCellTreeSelectedBackground.png")
    @ImageOptions(repeatStyle = RepeatStyle.Horizontal, flipRtl = true)
    ImageResource cellTreeSelectedBackground();

}
