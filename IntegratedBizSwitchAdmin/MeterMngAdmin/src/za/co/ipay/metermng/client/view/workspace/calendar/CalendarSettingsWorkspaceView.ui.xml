<!DOCTYPE ui:UiBinder SYSTEM "http://dl.google.com/gwt/DTD/xhtml.ent">
<ui:UiBinder xmlns:ui="urn:ui:com.google.gwt.uibinder"
	xmlns:g="urn:import:com.google.gwt.user.client.ui" xmlns:g2="urn:import:com.google.gwt.user.cellview.client"
	xmlns:form="urn:import:za.co.ipay.gwt.common.client.form" xmlns:widget="urn:import:za.co.ipay.gwt.common.client.widgets"
	xmlns:tc="urn:import:za.co.ipay.metermng.client.view.component.tariff.calendar">
	<ui:style>
	
	</ui:style>
	<ui:with field="msg" type="za.co.ipay.metermng.client.i18n.UiMessages" />
	<g:DockLayoutPanel ui:field="dockLayoutPanel" styleName="mainPanel">
	
		<g:north size="30">
			<form:PageHeader heading="{msg.getCalendarSettingsHeader}"
				ui:field="pageHeader" />
		</g:north>

		<g:center>
			<g:ScrollPanel>
				<g:FlowPanel>
				    
					<g:DisclosurePanel open="false" styleName="gwt-DisclosurePanel-insidecomponent" ui:field="seasonsDisclosurePanel" debugId="seasonsDisclosurePanel">
						<g:customHeader>
							<g:FlowPanel styleName="gwt-DisclosurePanel-component .header" width="100%">
								<g:HorizontalPanel width="100%" borderWidth="0">
									<g:Cell width="16px" height="16px" verticalAlignment="ALIGN_MIDDLE">
										<g:Image styleName="horizontalFlow" ui:field="seasonsdisclosurearrow" width="16" height="16" />
									</g:Cell>
									<g:Cell verticalAlignment="ALIGN_MIDDLE">
										<g:Label styleName="gwt-DisclosurePanel-component .header" width="" ui:field="seasonsHeaderLabel" text="{msg.getCalendarSeasonTitle}" horizontalAlignment="ALIGN_LEFT" />
									</g:Cell>
								</g:HorizontalPanel>
							</g:FlowPanel>
						</g:customHeader>
						<g:FlowPanel styleName="simple-center" width="99%" height="100%">
							<tc:SeasonsPanel ui:field="seasonsPanel" />
						</g:FlowPanel>
					</g:DisclosurePanel>

					<g:DisclosurePanel open="false"
						styleName="gwt-DisclosurePanel-insidecomponent" ui:field="periodsDisclosurePanel" debugId="periodsDisclosurePanel">
						<g:customHeader>
							<g:FlowPanel styleName="gwt-DisclosurePanel-component .header" width="100%">
								<g:HorizontalPanel width="100%" borderWidth="0">
									<g:Cell width="16px" height="16px" verticalAlignment="ALIGN_MIDDLE">
										<g:Image styleName="horizontalFlow" ui:field="periodsdisclosurearrow" width="16" height="16" />
									</g:Cell>
									<g:Cell verticalAlignment="ALIGN_MIDDLE">
										<g:Label styleName="gwt-DisclosurePanel-component .header" width="" ui:field="periodsHeaderLabel" text="{msg.getCalendarPeriodTitle}" horizontalAlignment="ALIGN_LEFT" />
									</g:Cell>
								</g:HorizontalPanel>
							</g:FlowPanel>
						</g:customHeader>
						<g:FlowPanel styleName="simple-center" width="99%" height="100%">
							<tc:PeriodsPanel ui:field="periodsPanel" />
						</g:FlowPanel>
					</g:DisclosurePanel>
				</g:FlowPanel>


			</g:ScrollPanel>
		</g:center>
	</g:DockLayoutPanel>

</ui:UiBinder> 
