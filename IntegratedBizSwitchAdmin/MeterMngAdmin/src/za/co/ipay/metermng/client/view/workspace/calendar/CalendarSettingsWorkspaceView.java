package za.co.ipay.metermng.client.view.workspace.calendar;

import java.util.logging.Logger;

import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.history.CalendarPlace;
import za.co.ipay.metermng.client.resource.image.MediaResource.MediaResourceUtil;
import za.co.ipay.metermng.client.view.component.tariff.calendar.PeriodsPanel;
import za.co.ipay.metermng.client.view.component.tariff.calendar.SeasonsPanel;
import za.co.ipay.metermng.client.view.workspace.BaseWorkspace;

import com.google.gwt.core.client.GWT;
import com.google.gwt.event.logical.shared.CloseEvent;
import com.google.gwt.event.logical.shared.CloseHandler;
import com.google.gwt.event.logical.shared.OpenEvent;
import com.google.gwt.event.logical.shared.OpenHandler;
import com.google.gwt.place.shared.Place;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.user.client.Timer;
import com.google.gwt.user.client.ui.DisclosurePanel;
import com.google.gwt.user.client.ui.Image;
import com.google.gwt.user.client.ui.ProvidesResize;
import com.google.gwt.user.client.ui.RequiresResize;
import com.google.gwt.user.client.ui.Widget;

public class CalendarSettingsWorkspaceView extends BaseWorkspace implements ProvidesResize, RequiresResize {
    
    @UiField DisclosurePanel seasonsDisclosurePanel;
    @UiField Image seasonsdisclosurearrow;
    @UiField DisclosurePanel periodsDisclosurePanel;
    @UiField Image periodsdisclosurearrow;
    @UiField(provided=true) SeasonsPanel seasonsPanel;
    @UiField(provided=true) PeriodsPanel periodsPanel;
    
    private Logger logger = Logger.getLogger("CalendarSettingsWorkspaceView");
    
    interface CalendarSettingsUiBinder extends UiBinder<Widget, CalendarSettingsWorkspaceView> {
    }
    
    private static CalendarSettingsUiBinder uiBinder = GWT.create(CalendarSettingsUiBinder.class);

    public CalendarSettingsWorkspaceView(ClientFactory clientFactory, CalendarPlace place) {
        this.clientFactory = clientFactory;
        seasonsPanel = new SeasonsPanel(clientFactory, createAndRegisterHasDirtyData());
        periodsPanel = new PeriodsPanel(clientFactory, createAndRegisterHasDirtyData());
        initWidget(uiBinder.createAndBindUi(this));
        setImages();
        setPlaceString(CalendarPlace.getPlaceAsString(place));
        setHeaderText(MessagesUtil.getInstance().getMessage("calendar.settings.title"));
        addHandlers();
    }
    
    private void setImages() {
        seasonsdisclosurearrow.setResource(MediaResourceUtil.getInstance().getClosedArrowImage());
        periodsdisclosurearrow.setResource(MediaResourceUtil.getInstance().getClosedArrowImage());
    }

    private void addHandlers() {
        this.seasonsDisclosurePanel.addOpenHandler(new OpenHandler<DisclosurePanel>() {
            
            @Override
            public void onOpen(OpenEvent<DisclosurePanel> event) {
                seasonsdisclosurearrow.setResource(MediaResourceUtil.getInstance().getOpenedArrowImage());
            }
        });
        
        this.seasonsDisclosurePanel.addCloseHandler(new CloseHandler<DisclosurePanel>() {
            
            @Override
            public void onClose(CloseEvent<DisclosurePanel> event) {
                seasonsdisclosurearrow.setResource(MediaResourceUtil.getInstance().getClosedArrowImage());
            }
        });
        
        this.periodsDisclosurePanel.addOpenHandler(new OpenHandler<DisclosurePanel>() {
            
            @Override
            public void onOpen(OpenEvent<DisclosurePanel> event) {
                periodsdisclosurearrow.setResource(MediaResourceUtil.getInstance().getOpenedArrowImage());
            }
        });
        
        this.periodsDisclosurePanel.addCloseHandler(new CloseHandler<DisclosurePanel>() {
            
            @Override
            public void onClose(CloseEvent<DisclosurePanel> event) {
                periodsdisclosurearrow.setResource(MediaResourceUtil.getInstance().getClosedArrowImage());
            }
        });
    }
    @Override
    public void onArrival(Place place) {
        logger.info("Arrived at Calendar Settings...");
        seasonsPanel.refreshTable();
        periodsPanel.refreshTable();
    }
    
    @Override
    public void onLeaving() {
        // TODO Auto-generated method stub
        
    }

    @Override
    public void onSelect() {
        // TODO Auto-generated method stub
        
    }

    

    @Override
    public void onClose() {
        // TODO Auto-generated method stub
        
    }

    @Override
    public boolean handles(Place place) {
        if (place instanceof CalendarPlace 
                && (CalendarPlace.CALENDARS_PLACE_SETTINGS.getCalendarPlaceType().equals(((CalendarPlace) place).getCalendarPlaceType()))) {
            return true;
        } else {
            return false;
        }
    }
    
    @Override
    public void onResize() {
        new Timer() {
            @Override
            public void run() {
                seasonsDisclosurePanel.setWidth("100%");
                periodsDisclosurePanel.setWidth("100%");
            }
        }.schedule(100);
    }
}
