package za.co.ipay.metermng.client.history;

import java.util.logging.Logger;

import za.co.ipay.metermng.client.event.OpenUserGroupEvent;
import za.co.ipay.metermng.client.factory.ClientFactory;

import com.google.gwt.activity.shared.AbstractActivity;
import com.google.gwt.event.shared.EventBus;
import com.google.gwt.user.client.ui.AcceptsOneWidget;

public class UserGroupActivity extends AbstractActivity {

    private ClientFactory clientFactory;
    private UserGroupPlace thePlace;
    private static Logger logger = Logger.getLogger(UserGroupActivity.class.getName());

    public UserGroupActivity(UserGroupPlace place, ClientFactory clientFactory) {
        super();
        this.clientFactory = clientFactory;
        thePlace = place;
    }

    @Override
    public void start(AcceptsOneWidget panel, EventBus eventBus) {
        logger.info("Opening place: " + thePlace);
        clientFactory.getEventBus().fireEvent(new OpenUserGroupEvent());
    }
}

