package za.co.ipay.metermng.client.view.workspace.supplygroup;

import java.util.Date;
import java.util.ArrayList;
import java.util.List;
import java.util.logging.Logger;

import com.google.gwt.core.client.GWT;
import com.google.gwt.event.dom.client.ClickEvent;
import com.google.gwt.event.dom.client.ClickHandler;
import com.google.gwt.place.shared.Place;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.user.cellview.client.Column;
import com.google.gwt.user.cellview.client.ColumnSortEvent;
import com.google.gwt.user.cellview.client.ColumnSortEvent.AsyncHandler;
import com.google.gwt.user.cellview.client.ColumnSortList;
import com.google.gwt.user.cellview.client.TextColumn;
import com.google.gwt.user.client.ui.Widget;
import com.google.gwt.view.client.AsyncDataProvider;
import com.google.gwt.view.client.HasData;
import com.google.gwt.view.client.Range;

import za.co.ipay.gwt.common.client.Dialogs;
import za.co.ipay.gwt.common.client.form.FormManager;
import za.co.ipay.gwt.common.client.form.Format;
import za.co.ipay.gwt.common.client.form.Format.FormatUtil;
import za.co.ipay.gwt.common.client.handler.ConfirmHandler;
import za.co.ipay.gwt.common.client.resource.Messages;
import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.gwt.common.client.validation.ClientValidatorUtil;
import za.co.ipay.gwt.common.client.workspace.SessionCheckCallback;
import za.co.ipay.gwt.common.client.workspace.SimpleTableView;
import za.co.ipay.gwt.common.shared.LookupListItem;
import za.co.ipay.gwt.common.shared.validation.ValidateUtil;
import za.co.ipay.metermng.client.event.SupplyGroupAddedEvent;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.history.SupplyGroupPlace;
import za.co.ipay.metermng.client.resource.image.MediaResource.MediaResourceUtil;
import za.co.ipay.metermng.client.rpc.callback.ClientCallback;
import za.co.ipay.metermng.client.view.workspace.BaseWorkspace;
import za.co.ipay.metermng.client.widget.StatusTableColumn;
import za.co.ipay.metermng.datatypes.RecordStatus;
import za.co.ipay.metermng.mybatis.generated.model.StsSupplyGroup;
import za.co.ipay.metermng.shared.MeterMngStatics;
import za.co.ipay.metermng.shared.dto.ScreenData;
import za.co.ipay.metermng.shared.dto.StsSupplyGroupDto;

public class SupplyGroupWorkspaceView extends BaseWorkspace implements FormManager<StsSupplyGroupDto> {

    private AsyncDataProvider<StsSupplyGroupDto> dataProvider;

    @UiField SimpleTableView<StsSupplyGroupDto> view;
    private SupplyGroupPanel panel;

    private StsSupplyGroupDto supplyGroupDto;
    private final int STS_SUPPLY_GROUP_CODE_LENGTH = 6;

    private Format format = FormatUtil.getInstance();

    private static Logger logger = Logger.getLogger(SupplyGroupWorkspaceView.class.getName());

    private static SupplyGroupWorkspaceViewUiBinder uiBinder = GWT.create(SupplyGroupWorkspaceViewUiBinder.class);

    interface SupplyGroupWorkspaceViewUiBinder extends UiBinder<Widget, SupplyGroupWorkspaceView> {
    }

    public SupplyGroupWorkspaceView(ClientFactory clientFactory, SupplyGroupPlace place) {
        this.clientFactory = clientFactory;
        initWidget(uiBinder.createAndBindUi(this));
        setPlaceString("supplyGroup:all");
        setHeaderText(MessagesUtil.getInstance().getMessage("supplygroup.title"));
        initUi();
    }

    private void initUi() {
        initView();
        initForm();
        createTable();
        loadInitData();
        actionPermissions();
    }

    private void initView() {
        view.setFormManager(this);
    }

    private void initForm() {
        panel = new SupplyGroupPanel(view.getForm());
        view.getForm().setHasDirtyDataManager(this);
        view.getForm().getFormFields().add(panel);

        view.getForm().getFormPanel().setHeadingText(MessagesUtil.getInstance().getMessage("supplygroup.title.add"));

        view.getForm().getSaveBtn().setText(MessagesUtil.getInstance().getMessage("button.create"));
        view.getForm().getSaveBtn().addClickHandler(new ClickHandler() {
            @Override
            public void onClick(ClickEvent arg0) {
                onSave();
            }
        });
        view.getForm().getSaveBtn().ensureDebugId("saveButton");

        view.getForm().getOtherBtn().setText(MessagesUtil.getInstance().getMessage("button.cancel"));
        view.getForm().getOtherBtn().addClickHandler(new ClickHandler() {
            @Override
            public void onClick(ClickEvent event) {
                view.getForm().checkDirtyData(new ConfirmHandler() {
                    @Override
                    public void confirmed(boolean confirm) {
                        if (confirm) {
                            refreshTable(false);
                            view.getForm().setDirtyData(false);
                            displaySelected(null);
                        }
                    }
                });
            }
        });
        view.getForm().getOtherBtn().ensureDebugId("cancelButton");

        panel.activeBox.setValue(true);
    }

    private void onSave() {
        //if deactivating a supply group, if it is also been assigned as a target for another SGC, ask for confirmation to continue
        final RecordStatus newRecStatus = panel.activeBox.getValue() ? RecordStatus.ACT : RecordStatus.DAC;
        SessionCheckCallback sessionCheckCallback = new SessionCheckCallback() {
            @Override
            public void callback(SessionCheckResolution resolution) {
                if (supplyGroupDto != null && supplyGroupDto.getId() != null && supplyGroupDto.getRecordStatus().equals(RecordStatus.ACT)
                        && newRecStatus.equals(RecordStatus.DAC)) {
                    clientFactory.getSupplyGroupRpc().getSupplyGroupByTargetId(supplyGroupDto.getId(), new ClientCallback<StsSupplyGroup>() {
                        @Override
                        public void onSuccess(StsSupplyGroup result) {
                            if (result != null) {
                                Dialogs.confirm(MessagesUtil.getInstance().getMessage("supplygroup.target.deactivate.question"),
                                        MessagesUtil.getInstance().getMessage("option.positive"),
                                        MessagesUtil.getInstance().getMessage("option.negative"),
                                        MediaResourceUtil.getInstance().getQuestionIcon(),
                                        new ConfirmHandler() {
                                            @Override
                                            public void confirmed(boolean confirm) {
                                                if(confirm) {
                                                    onSaveContinue();
                                                } else {
                                                    return;
                                                }
                                            }
                                        },
                                        view.getForm().getSaveBtn().getAbsoluteLeft(),
                                        view.getForm().getSaveBtn().getAbsoluteTop());
                            } else {
                                onSaveContinue();
                            }
                        }
                    });
                } else {
                    onSaveContinue();
                }
            }
        };
        clientFactory.handleSessionCheckCallback(sessionCheckCallback);
    }

    private void onSaveContinue() {
        if (isValidInput()) {
            update();
            final Long id = supplyGroupDto.getId(); // keep track whether the table's total row count is increasing or not
            clientFactory.getSupplyGroupRpc().updateSupplyGroup(supplyGroupDto, new ClientCallback<StsSupplyGroupDto>(view.getForm().getSaveBtn().getAbsoluteLeft(), view.getForm().getSaveBtn().getAbsoluteTop()) {
                @Override
                public void onSuccess(StsSupplyGroupDto supplyGroupDto) {
                    populateTargetSgcKrn();
                    clientFactory.getEventBus().fireEvent(new SupplyGroupAddedEvent());
                    view.getForm().setDirtyData(false);
                    refreshTable(id == null);
                    Messages messages = MessagesUtil.getInstance();
                    String savedMsg = messages.getSavedMessage(new String[] { MessagesUtil.getInstance().getMessage("supplygroup.name") });
                    if (supplyGroupDto.isNullsInValidation()) {
                        savedMsg += messages.getMessage("supply.group.target.validation.nulls");
                    }
                    Dialogs.displayInformationMessage(savedMsg,
                            MediaResourceUtil.getInstance().getInformationIcon(),
                            view.getForm().getSaveBtn().getAbsoluteLeft(),
                            view.getForm().getSaveBtn().getAbsoluteTop(),
                            MessagesUtil.getInstance().getMessage("button.close"));
                    clear();
                }
            });
        }
    }

    private void createTable() {
        if (dataProvider == null) {
            Column<StsSupplyGroupDto, ?> nameColumn = createNameColumn();
            view.getTable().addColumn(nameColumn, MessagesUtil.getInstance().getMessage("supplygroup.field.name"));
            view.getTable().addColumn(createCodeColumn(), MessagesUtil.getInstance().getMessage("supplygroup.field.code"));
            view.getTable().addColumn(createKeyRevisionNumberColumn(), MessagesUtil.getInstance().getMessage("supplygroup.field.keyrevisionnumber"));
            view.getTable().addColumn(createBaseDateColumn(), MessagesUtil.getInstance().getMessage("supplygroup.base.date.label"));
            view.getTable().addColumn(createIssuedUntilDateColumn(), MessagesUtil.getInstance().getMessage("supplygroup.field.issued.until.date.label"));
            view.getTable().addColumn(createExpiryDateColumn(), MessagesUtil.getInstance().getMessage("supplygroup.field.expiry.date.label"));
            view.getTable().addColumn(createKmcExpiryColumn(), MessagesUtil.getInstance().getMessage("supplygroup.field.kmc.expirydate"));
            view.getTable().addColumn(new StatusTableColumn<StsSupplyGroupDto>(), MessagesUtil.getInstance().getMessage("supplygroup.field.status"));
            view.getTable().addColumn(createTargetColumn(), MessagesUtil.getInstance().getMessage("supplygroup.field.target"));

            view.getTable().ensureDebugId("supplyGroupTable");

            // Set the range to display
            view.getTable().setVisibleRange(0, getPageSize());

            // Set the data provider for the table
            dataProvider = new AsyncDataProvider<StsSupplyGroupDto>() {
                @Override
                protected void onRangeChanged(HasData<StsSupplyGroupDto> display) {
                    final int start = display.getVisibleRange().getStart();
                    String sortColumn = null;
                    boolean isAscending = true;
                    ColumnSortList sortList = view.getTable().getColumnSortList();
                    if (sortList != null && sortList.size() != 0) {
                        @SuppressWarnings("unchecked")
                        Column<StsSupplyGroupDto, ?> sColumn = (Column<StsSupplyGroupDto, ?>) sortList.get(0).getColumn();
                        Integer columnIndex = view.getTable().getColumnIndex(sColumn);
                        sortColumn = getColumnName(columnIndex);
                        isAscending = sortList.get(0).isAscending();
                        logger.info("Sorting by column: " + sortColumn + " isAscending: " + isAscending);
                    }
                    clientFactory.getSupplyGroupRpc().getSupplyGroups(start, getPageSize(), sortColumn, isAscending, new ClientCallback<List<StsSupplyGroupDto>>() {
                        @Override
                        public void onSuccess(List<StsSupplyGroupDto> result) {
                            dataProvider.updateRowData(start, result);
                        }
                    });
                }
            };
            dataProvider.addDataDisplay(view.getTable());

            // Create the table's pager
            view.getPager().setDisplay(view.getTable());

            // Set the table's column sorter handler
            AsyncHandler columnSortHandler = new AsyncHandler(view.getTable()) {
                @Override
                public void onColumnSort(ColumnSortEvent event) {
                    final int start = view.getTable().getVisibleRange().getStart();
                    @SuppressWarnings("unchecked")
                    int sortIndex = view.getTable().getColumnIndex((Column<StsSupplyGroupDto, ?>) event.getColumn());
                    String sortColumn = getColumnName(sortIndex);
                    boolean isAscending = event.isSortAscending();
                    clientFactory.getSupplyGroupRpc().getSupplyGroups(start, getPageSize(), sortColumn, isAscending, new ClientCallback<List<StsSupplyGroupDto>>() {
                        public void onSuccess(List<StsSupplyGroupDto> result) {
                            dataProvider.updateRowData(start, result);
                        }
                    });
                }
            };
            view.getTable().addColumnSortHandler(columnSortHandler);
            view.getTable().getColumnSortList().push(nameColumn);
        }
    }

    private String getColumnName(int index) {
        switch (index) {
            case 0:
                return "name";
            case 1:
                return "code";
            case 2:
                return "revisionNumber";
            case 3:
                return "baseDate";
            default:
                return null;
        }
    }

    private Column<StsSupplyGroupDto, ?> createNameColumn() {
        TextColumn<StsSupplyGroupDto> nameColumn = new TextColumn<StsSupplyGroupDto>() {
            @Override
            public String getValue(StsSupplyGroupDto supplyGroupDto) {
                return supplyGroupDto.getName();
            }
        };
        nameColumn.setSortable(true);
        return nameColumn;
    }

    private Column<StsSupplyGroupDto, ?> createCodeColumn() {
        TextColumn<StsSupplyGroupDto> column = new TextColumn<StsSupplyGroupDto>() {
            @Override
            public String getValue(StsSupplyGroupDto supplyGroupDto) {
                return supplyGroupDto.getSupplyGroupCode();
            }
        };
        column.setSortable(true);
        return column;
    }

    private Column<StsSupplyGroupDto, ?> createKeyRevisionNumberColumn() {
        TextColumn<StsSupplyGroupDto> column = new TextColumn<StsSupplyGroupDto>() {
            @Override
            public String getValue(StsSupplyGroupDto supplyGroupDto) {
                if (supplyGroupDto.getKeyRevisionNum() != null) {
                    return supplyGroupDto.getKeyRevisionNum().toString();
                } else {
                    return "";
                }
            }
        };
        column.setSortable(true);
        return column;
    }

    private Column<StsSupplyGroupDto, ?> createBaseDateColumn() {
        TextColumn<StsSupplyGroupDto> column = new TextColumn<StsSupplyGroupDto>() {
            @Override
            public String getValue(StsSupplyGroupDto supplyGroupDto) {
                if (supplyGroupDto.getBaseDate() != null) {
                    return supplyGroupDto.getBaseDate().toString();
                } else {
                    return "";
                }
            }
        };
        column.setSortable(true);
        return column;
    }

    private Column<StsSupplyGroupDto, ?> createKmcExpiryColumn() {
        TextColumn<StsSupplyGroupDto> kmcColumn = new TextColumn<StsSupplyGroupDto>() {
            @Override
            public String getValue(StsSupplyGroupDto supplyGroupDto) {
                Date kmcExpiryDate = supplyGroupDto.getKmcExpiryDate();
                if (kmcExpiryDate != null) {
                    return format.formatDateTime(kmcExpiryDate);
                } else {
                    return "";
                }
            }
        };
        return kmcColumn;
    }

    private Column<StsSupplyGroupDto, ?> createTargetColumn() {
        TextColumn<StsSupplyGroupDto> targetColumn = new TextColumn<StsSupplyGroupDto>() {
            @Override
            public String getValue(StsSupplyGroupDto supplyGroupDto) {
                String target = supplyGroupDto.getTargetSupGrKrnString();
                if (target != null) {
                    return target;
                } else {
                    return "";
                }
            }
        };
        return targetColumn;
    }

    private Column<StsSupplyGroupDto, ?> createIssuedUntilDateColumn() {
        TextColumn<StsSupplyGroupDto> targetColumn = new TextColumn<StsSupplyGroupDto>() {
            @Override
            public String getValue(StsSupplyGroupDto supplyGroupDto) {
                Date issuedUntilDt = supplyGroupDto.getIssuedUntilDate();
                if (issuedUntilDt != null) {
                    return format.formatDateTime(issuedUntilDt);
                } else {
                    return "";
                }
            }
        };
        return targetColumn;
    }

    private Column<StsSupplyGroupDto, ?> createExpiryDateColumn() {
        TextColumn<StsSupplyGroupDto> targetColumn = new TextColumn<StsSupplyGroupDto>() {
            @Override
            public String getValue(StsSupplyGroupDto supplyGroupDto) {
                Date expiryDt = supplyGroupDto.getExpiryDate();
                if (expiryDt != null) {
                    return format.formatDateTime(expiryDt);
                } else {
                    return "";
                }
            }
        };
        return targetColumn;
    }

    private void loadInitData() {
        populateKeyRevisionNumber();
        populateTargetSgcKrn();
    }

    private void populateKeyRevisionNumber() {
        clientFactory.getSupplyGroupRpc().getKeyRevisionNumbers(new ClientCallback<ScreenData>() {
            @SuppressWarnings("unchecked")
            @Override
            public void onSuccess(ScreenData screenData) {
                panel.keyRevisionNumberBox.clear();
                panel.keyRevisionNumberBox.addItem("");
                List<String> revisionsNumbers = (List<String>) screenData.getData("revisionNumbers");
                for (String number : revisionsNumbers) {
                    panel.keyRevisionNumberBox.addItem(number);
                }
            }
        });
    }
    private void populateTargetSgcKrn() {
        clientFactory.getLookupRpc().getSgKrnLookupList(new ClientCallback<ArrayList<LookupListItem>>() {
            @Override
            public void onSuccess(ArrayList<LookupListItem> result) {
                panel.lstbxTargetSupGrCde.clearAll();
                panel.lstbxTargetSupGrCde.setLookupItems(result);   //do it this way round so that the map goes with

                if (supplyGroupDto != null && supplyGroupDto.getId() != null) {
                    //remove current item from the target dropdown, can't select itself as target
                    panel.lstbxTargetSupGrCde.removeItemFromListAndDropdownByValue(String.valueOf(supplyGroupDto.getId()));
                    if (supplyGroupDto.getTargetStsSupplyGroupId() != null) {
                        panel.lstbxTargetSupGrCde.selectItemByValue(String.valueOf(supplyGroupDto.getTargetStsSupplyGroupId()));
                    }
                }
            }
        });
    }

    @Override
    public void onArrival(Place place) {
        clientFactory.getSupplyGroupRpc().getSupplyGroupCount(new ClientCallback<Integer>() {
            @Override
            public void onFailureClient() {
                view.getTable().setRowCount(0, true);
                dataProvider.updateRowCount(0, true);
            }

            @Override
            public void onSuccess(Integer result) {
                logger.fine("Got count: " + result + " for dataProvider: " + dataProvider);
                view.getTable().setRowCount(result, true);
                dataProvider.updateRowCount(result, true);
            }
        });
    }

    @Override
    public void displaySelected(StsSupplyGroupDto selected) {
        setSupplyGroup(selected);
    }

    private void refreshTable(boolean insertedNew) {
        // need to also update the pager re: number of available
        if (insertedNew) {
            view.getTable().setRowCount(view.getTable().getRowCount() + 1, true);
        }
        Range range = view.getTable().getVisibleRange();
        view.getTable().setVisibleRangeAndClearData(range, true);
    }

    private void setSupplyGroup(StsSupplyGroupDto group) {
        clear();
        this.supplyGroupDto = group;
        if (supplyGroupDto == null) {
            supplyGroupDto = new StsSupplyGroupDto();
            supplyGroupDto.setRecordStatus(RecordStatus.ACT);
            view.getForm().getSaveBtn().setText(MessagesUtil.getInstance().getMessage("button.create"));
            view.getForm().getFormPanel().setHeadingText(MessagesUtil.getInstance().getMessage("supplygroup.title.add"));
            view.clearTableSelection();
        } else {
            view.getForm().getSaveBtn().setText(MessagesUtil.getInstance().getMessage("button.update"));
            view.getForm().getFormPanel().setHeadingText(MessagesUtil.getInstance().getMessage("supplygroup.title.update"));
        }

        populateTargetSgcKrn();

        // display the supply group
        if (supplyGroupDto.getName() != null) {
            panel.nameBox.setText(supplyGroupDto.getName().trim());
        }
        if (supplyGroupDto.getSupplyGroupCode() != null) {
            panel.codeBox.setText(supplyGroupDto.getSupplyGroupCode().trim());
        }

        panel.keyRevisionNumberBox.setSelectedIndex(0);
        if (supplyGroupDto.getKeyRevisionNum() != null) {
            String number = supplyGroupDto.getKeyRevisionNum().toString();
            for (int i = 0; i < panel.keyRevisionNumberBox.getItemCount(); i++) {
                if (number.equals(panel.keyRevisionNumberBox.getItemText(i))) {
                    panel.keyRevisionNumberBox.setSelectedIndex(i);
                }
            }
        }

        panel.kmcExpiryDateBox.setValue(supplyGroupDto.getKmcExpiryDate());
        panel.isDefaultBox.setValue(supplyGroupDto.isDefaultSupplyGroup());
        panel.activeBox.setValue(RecordStatus.ACT.equals(supplyGroupDto.getRecordStatus()));
        if (supplyGroupDto.getBaseDate() != null) {
            panel.baseDateLabel.setText(supplyGroupDto.getBaseDate().toString());
        }
        panel.isDefaultBox.setValue(supplyGroupDto.isDefaultSupplyGroup());
        panel.isInUseByMeter(supplyGroupDto.isInUseByMeter());
    }

    private void clear() {
        supplyGroupDto = null;
        view.getForm().getFormPanel().setHeadingText(MessagesUtil.getInstance().getMessage("supplygroup.title.add"));
        view.getForm().getSaveBtn().setText(MessagesUtil.getInstance().getMessage("button.create"));
        panel.clearFields();
        panel.clearErrors();
    }

    private void update() {
        if (supplyGroupDto == null) {
            supplyGroupDto = new StsSupplyGroupDto();
            supplyGroupDto.setRecordStatus(RecordStatus.ACT);
        }
        //GUI should be disabled if a meter is attached - if GUI is NOT disabled and supply group is in use by a meter
        //we do NOT want to update these fields
        if(!supplyGroupDto.isInUseByMeter()) {
            supplyGroupDto.setName(panel.nameBox.getText());
            supplyGroupDto.setSupplyGroupCode(panel.codeBox.getText());
            supplyGroupDto.setKeyRevisionNum(getSelectedRevisionNumber());
            supplyGroupDto.setKmcExpiryDate(panel.kmcExpiryDateBox.getValue());
            supplyGroupDto.setRecordStatus(panel.activeBox.getValue() ? RecordStatus.ACT : RecordStatus.DAC);
        }
        //these fields can always be changed since #23118
        supplyGroupDto.setTargetStsSupplyGroupId(panel.getSelectedTargetSgcId());
        supplyGroupDto.setDefaultSupplyGroup(panel.isDefaultBox.getValue());
    }

    private boolean isValidInput() {
        boolean valid = true;
        panel.clearErrors();

        StsSupplyGroupDto group = new StsSupplyGroupDto();
        if (this.supplyGroupDto != null) {
            group.setId(this.supplyGroupDto.getId());
        }
        group.setName(panel.nameBox.getText());
        group.setSupplyGroupCode(panel.codeBox.getText());
        group.setKeyRevisionNum(getSelectedRevisionNumber());
        group.setRecordStatus(panel.activeBox.getValue() ? RecordStatus.ACT : RecordStatus.DAC);
        group.setKmcExpiryDate(panel.kmcExpiryDateBox.getValue());
        group.setTargetStsSupplyGroupId(panel.getSelectedTargetSgcId());
        group.setDefaultSupplyGroup(panel.isDefaultBox.getValue());

        if (!ClientValidatorUtil.getInstance().validateField(group, "name", panel.nameElement)) {
            valid = false;
        }
        if (!ClientValidatorUtil.getInstance().validateField(group, "supplyGroupCode", panel.codeElement)) {
            valid = false;
        }
        if (group.getSupplyGroupCode() != null && group.getSupplyGroupCode().trim().length() != STS_SUPPLY_GROUP_CODE_LENGTH) {
            panel.codeElement.showErrorMsg(MessagesUtil.getInstance().getMessage("error.field.supplygroupcode.size",
                new String[]{String.valueOf(STS_SUPPLY_GROUP_CODE_LENGTH)}));
            valid = false;
        }
        if (!ClientValidatorUtil.getInstance().validateField(group, "keyRevisionNum", panel.keyRevisionNumberElement)) {
            valid = false;
        }

        return valid;
    }

    private Integer getSelectedRevisionNumber() {
        String selected = panel.keyRevisionNumberBox.getItemText(panel.keyRevisionNumberBox.getSelectedIndex());
        if (ValidateUtil.isValidInteger(selected)) {
            return Integer.valueOf(selected);
        } else {
            return null;
        }
    }

    @Override
    public boolean handles(Place place) {
        return place instanceof SupplyGroupPlace;
    }

    private void actionPermissions() {
        if (!clientFactory.getUser().hasPermission(MeterMngStatics.EDIT_PERMISSION_MM_SUPPLY_GROUP)) {
            view.getForm().getButtons().removeFromParent();
        }
    }
}
