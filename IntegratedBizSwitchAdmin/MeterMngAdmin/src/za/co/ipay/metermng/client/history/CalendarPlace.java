package za.co.ipay.metermng.client.history;

import com.google.gwt.place.shared.Place;
import com.google.gwt.place.shared.PlaceTokenizer;
import com.google.gwt.place.shared.Prefix;

public class CalendarPlace extends Place {

    public static CalendarPlace CALENDARS_PLACE = new CalendarPlace("all");
    public static CalendarPlace CALENDARS_PLACE_SETTINGS = new CalendarPlace("settings");
    
    private String calendarPlaceType;

    public CalendarPlace(String calendarTab) {
        this.calendarPlaceType = calendarTab;
    }

    public String getCalendarPlaceType() {
        return calendarPlaceType;
    }
    
    public static String getPlaceAsString(CalendarPlace place) {
        if (place != null) {
            return "calendar:"+place.getCalendarPlaceType();
        } else {
            return "";
        }
    }
    
    @Prefix(value = "calendar")
    public static class Tokenizer implements PlaceTokenizer<CalendarPlace> {
        @Override
        public String getToken(CalendarPlace place) {
            return place.getCalendarPlaceType();
        }

        @Override
        public CalendarPlace getPlace(String token) {
            return new CalendarPlace(token);
        }
    }
}
