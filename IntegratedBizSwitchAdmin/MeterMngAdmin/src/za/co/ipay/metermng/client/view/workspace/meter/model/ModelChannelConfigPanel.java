package za.co.ipay.metermng.client.view.workspace.meter.model;

import java.util.List;
import java.util.logging.Logger;

import com.google.gwt.core.client.GWT;
import com.google.gwt.event.dom.client.ChangeEvent;
import com.google.gwt.event.dom.client.ChangeHandler;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.user.client.ui.CheckBox;
import com.google.gwt.user.client.ui.ListBox;
import com.google.gwt.user.client.ui.TextArea;
import com.google.gwt.user.client.ui.TextBox;
import com.google.gwt.user.client.ui.Widget;

import za.co.ipay.gwt.common.client.form.BigDecimalValueBox;
import za.co.ipay.gwt.common.client.form.FormElement;
import za.co.ipay.gwt.common.client.form.SimpleForm;
import za.co.ipay.gwt.common.client.handler.FormDataChangeHandler;
import za.co.ipay.gwt.common.shared.dto.IdNameDto;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.form.SimpleFormPanel;
import za.co.ipay.metermng.client.rpc.callback.ClientCallback;
import za.co.ipay.metermng.datatypes.RecordStatus;
import za.co.ipay.metermng.mybatis.generated.model.MdcChannel;
import za.co.ipay.metermng.mybatis.generated.model.TimeInterval;
import za.co.ipay.metermng.shared.dto.meter.MdcChannelDto;

public class ModelChannelConfigPanel extends SimpleFormPanel {

    @UiField FormElement mdcChannelElement;
    @UiField FormElement mdcTimeIntervalElement;
    @UiField FormElement mdcMaxSizeElement;
    @UiField FormElement mdcReadingMultiplierElement;
    @UiField FormElement timeIntervalElement;
    @UiField FormElement maxSizeElement;
    @UiField FormElement readingMultiplierElement;

    @UiField TextBox mdcName;
    @UiField ListBox mdcChannelListBox;
    @UiField TextBox channelName;
    @UiField TextBox channelValue;
    @UiField CheckBox activeBox;
    @UiField TextBox channelDescrip;
    @UiField TextArea billingDetName;
    @UiField TextBox meterReadingTypeName;

    @UiField TextBox mdcTimeIntervalTextBox;
    @UiField BigDecimalValueBox mdcMaxSizeBox;
    @UiField BigDecimalValueBox mdcReadingMultiplierBox;
    @UiField ListBox timeIntervalListBox;
    @UiField BigDecimalValueBox maxSizeBox;
    @UiField BigDecimalValueBox readingMultiplierBox;
    
    private ClientFactory clientFactory;
    private ModelChannelConfigView parentView;
    
    private static Logger logger = Logger.getLogger(ModelChannelConfigPanel.class.getName());

    private static MdcChannelPanelUiBinder uiBinder = GWT.create(MdcChannelPanelUiBinder.class);

    interface MdcChannelPanelUiBinder extends UiBinder<Widget, ModelChannelConfigPanel> {
    }

    public ModelChannelConfigPanel(ClientFactory clientFactory, SimpleForm form, ModelChannelConfigView parentView) {
        super(form);
        this.clientFactory = clientFactory;
        this.parentView = parentView;
        initWidget(uiBinder.createAndBindUi(this));       
        
        populateTimeIntervals();
        activeBox.setValue(false);
        activeBox.setEnabled(false);
        
        addFieldHandlers();
    }
    
    public void clearFields() {
        form.setDirtyData(false);
        
        mdcChannelElement.setVisible(true);
        mdcChannelListBox.setEnabled(true);
        mdcChannelListBox.setSelectedIndex(0);
        channelName.setText("");
        channelValue.setText("");
        activeBox.setValue(false);
        channelDescrip.setText("");
        billingDetName.setText("");
        meterReadingTypeName.setText("");
        
        timeIntervalListBox.setSelectedIndex(0);
        maxSizeBox.setText("");
        readingMultiplierBox.setText("");
        
    }

    public void clearErrors() {
        mdcChannelElement.clearErrorMsg();
        timeIntervalElement.clearErrorMsg();
        maxSizeElement.clearErrorMsg();
        readingMultiplierElement.clearErrorMsg();
    }
    
    protected void setMdcName(String name) {
        mdcName.setText(name);
    }
    
    protected void setChannelDetails(MdcChannelDto mdcChannelDto) {
        MdcChannel mdcChannel = mdcChannelDto.getMdcChannel();
        channelName.setText(mdcChannel.getName());
        channelValue.setText(mdcChannel.getValue());
        activeBox.setValue(mdcChannel.getRecordStatus().equals(RecordStatus.ACT));
        activeBox.setEnabled(false);
        channelDescrip.setText(mdcChannel.getDescription());
        setBillingDetName(mdcChannelDto.getBillingDetList());
        meterReadingTypeName.setText(mdcChannelDto.getMeterReadingTypeName());
        mdcMaxSizeBox.setValue(mdcChannelDto.getMdcChannel().getMaxSize());
        mdcReadingMultiplierBox.setValue(mdcChannelDto.getMdcChannel().getReadingMultiplier());
        mdcTimeIntervalTextBox.setValue(mdcChannelDto.getTimeIntervalName());
    }
    
    protected void setBillingDetName(List<IdNameDto> billingDetIdNameDtoList) {
        StringBuilder bldr = new StringBuilder();
        for (IdNameDto nd: billingDetIdNameDtoList) {
            bldr.append(nd.getName()).append(", ");
        }
        String billingDetNames = bldr.toString();
        if (!billingDetNames.isEmpty()) {
            billingDetNames = billingDetNames.substring(0, billingDetNames.length() - 2);
        }
        billingDetName.setText(billingDetNames);
    }
    
    protected void setTimeIntervalSelection(Long id) {
        if (id != null) {
            for(int i=0;i<timeIntervalListBox.getItemCount();i++) {
                if (timeIntervalListBox.getValue(i).equals(id.toString())) {
                    timeIntervalListBox.setSelectedIndex(i);
                    break;
                }
            }
        }
    }
    
    protected void populateMdcChannels(Long mdcId, Long meterModelId) {
        
        clientFactory.getMdcChannelRpc().getMdcChannelsNotOverriddenByMeter(mdcId, meterModelId, new ClientCallback<List<MdcChannel>>() {
            @Override
            public void onSuccess(List<MdcChannel> result) {
                initMdcChannels(result);
            }
        });
    }
    
    private void initMdcChannels(List<MdcChannel> mdcChannelList){
        mdcChannelListBox.clear();
        mdcChannelListBox.addItem("", "");
        
        if (!mdcChannelList.isEmpty()) {
            parentView.clear();
            parentView.enableButtons();

            for (MdcChannel mc : mdcChannelList) {
                mdcChannelListBox.addItem(mc.getName(), mc.getId().toString());
            }
        } else {
            mdcChannelElement.setVisible(false);
        }
    }    
    
    protected void populateTimeIntervals() {
        clientFactory.getMdcChannelRpc().getTimeIntervals(new ClientCallback<List<TimeInterval>>() {
            @Override
            public void onSuccess(List<TimeInterval> result) {
                initTimeInterval(result);
            }
        });
    }
    
    private void initTimeInterval(List<TimeInterval> timeIntervalList){
        timeIntervalListBox.clear();
        timeIntervalListBox.addItem("", "");
        
        for (TimeInterval ti : timeIntervalList) {
            timeIntervalListBox.addItem(ti.getTimeIntervalName(), ti.getId().toString());
        }
    }    

    @Override
    public void addFieldHandlers() {
        timeIntervalListBox.addChangeHandler(new FormDataChangeHandler(form));
        maxSizeBox.addChangeHandler(new FormDataChangeHandler(form));
        readingMultiplierBox.addChangeHandler(new FormDataChangeHandler(form));
        mdcChannelListBox.addChangeHandler(new ChangeHandler() {
            @Override
            public void onChange(ChangeEvent event) {
                getMdcChannelDefaults();
            }
        });
    }
    
    
    private void getMdcChannelDefaults() {
        if (mdcChannelListBox.getSelectedIndex() > 0) {                  // because when change the dropdown contents after save / delete also hits changeHandler
            Long mdcChannelId = Long.valueOf(mdcChannelListBox.getValue(mdcChannelListBox.getSelectedIndex()));
            //if (!parentView.isChannelOverriden(mdcChannelId)) {
                clientFactory.getMdcChannelRpc().getMdcChannelDto(mdcChannelId, new ClientCallback<MdcChannelDto>() {
                    @Override
                    public void onSuccess(MdcChannelDto result) {
                        setChannelDetails(result);
                        parentView.setCreateButton();
                    }
                });
            //}
        }
    }
}
