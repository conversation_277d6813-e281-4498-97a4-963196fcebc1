package za.co.ipay.metermng.client.view.workspace.group.user;

import java.util.List;
import java.util.logging.Logger;

import za.co.ipay.gwt.common.client.Dialogs;
import za.co.ipay.gwt.common.client.factory.ResourcesFactoryUtil;
import za.co.ipay.gwt.common.client.form.FormManager;
import za.co.ipay.gwt.common.client.handler.ConfirmHandler;
import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.gwt.common.client.workspace.SessionCheckCallback;
import za.co.ipay.gwt.common.client.workspace.SimpleTableView;
import za.co.ipay.gwt.common.client.workspace.WorkspaceNotification;
import za.co.ipay.gwt.common.client.workspace.WorkspaceNotification.NotificationType;
import za.co.ipay.gwt.common.shared.exception.AccessControlException;
import za.co.ipay.metermng.client.event.SelectAccessGroupEvent;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.history.UserGroupPlace;
import za.co.ipay.metermng.client.resource.image.MediaResource.MediaResourceUtil;
import za.co.ipay.metermng.client.rpc.callback.ClientCallback;
import za.co.ipay.metermng.client.view.workspace.BaseWorkspace;
import za.co.ipay.metermng.mybatis.generated.model.GenGroup;
import za.co.ipay.metermng.mybatis.generated.model.GroupType;
import za.co.ipay.metermng.mybatis.generated.model.UserGroup;
import za.co.ipay.metermng.shared.MeterMngStatics;
import za.co.ipay.metermng.shared.dto.SelectionDataItem;
import za.co.ipay.metermng.shared.dto.user.MeterMngUser;
import za.co.ipay.metermng.shared.dto.user.MeterMngUserGroup;
import za.co.ipay.metermng.shared.dto.user.UserData;

import com.google.gwt.core.client.GWT;
import com.google.gwt.event.dom.client.ClickEvent;
import com.google.gwt.event.dom.client.ClickHandler;
import com.google.gwt.place.shared.Place;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.user.cellview.client.TextColumn;
import com.google.gwt.user.client.ui.Widget;
import com.google.gwt.view.client.ListDataProvider;

public class UserGroupWorkspaceView extends BaseWorkspace implements FormManager<MeterMngUserGroup> {

    @UiField SimpleTableView<MeterMngUserGroup> view;
    private ListDataProvider<MeterMngUserGroup> dataProvider;
    private GroupType accessGroupType;
    private MeterMngUserPanel panel;
    private MeterMngUserGroup user;
    private MeterMngUser loggedInUser;

    private static UserGroupWorkspaceViewUiBinder uiBinder = GWT.create(UserGroupWorkspaceViewUiBinder.class);

    private static Logger logger = Logger.getLogger(UserGroupWorkspaceView.class.getName());

    interface UserGroupWorkspaceViewUiBinder extends UiBinder<Widget, UserGroupWorkspaceView> {
    }

    public UserGroupWorkspaceView(ClientFactory clientFactory, UserGroupPlace place) {
        this.clientFactory = clientFactory;
        initWidget(uiBinder.createAndBindUi(this));
        setPlaceString(UserGroupPlace.getPlaceAsString());
        setHeaderText(MessagesUtil.getInstance().getMessage("usergroup.title"));
        initUi();
    }

    private void initUi() {
        view.setDataDetails(null, MessagesUtil.getInstance().getMessage("usergroup.instructions"));
        initForm();
        createTable();
        loadData();
    }

    private void initForm() {
        view.setFormManager(this);
        panel = new MeterMngUserPanel(clientFactory, view.getForm());
        view.getForm().setHasDirtyDataManager(this);
        view.getForm().getFormFields().add(panel);

        view.getForm().getSaveBtn().setText(MessagesUtil.getInstance().getMessage("button.create"));
        view.getForm().getSaveBtn().addClickHandler(new ClickHandler() {
            @Override
            public void onClick(ClickEvent arg0) {
                onSave();
            }
        });

        view.getForm().getOtherBtn().setText(MessagesUtil.getInstance().getMessage("button.cancel"));
        view.getForm().getOtherBtn().addClickHandler(new ClickHandler() {
            @Override
            public void onClick(ClickEvent event) {
                view.getForm().checkDirtyData(new ConfirmHandler() {
                    @Override
                    public void confirmed(boolean confirm) {
                        if (confirm) {
                            view.getForm().setDirtyData(false);
                            displaySelected(null);
                        }
                    }
                });
            }
        });

        view.getForm().getBackBtn().setText(MessagesUtil.getInstance().getMessage("button.clear.group"));
        view.getForm().getBackBtn().addClickHandler(new ClickHandler() {
            @Override
            public void onClick(ClickEvent event) {
                onDelete();
            }
        });

        view.getForm().getFormPanel().setHeadingText(MessagesUtil.getInstance().getMessage("usergroup.title.add"));
    }

    private void createTable() {
        if (dataProvider != null) {
            return;
        }

        TextColumn<MeterMngUserGroup> nameColumn = new TextColumn<MeterMngUserGroup>() {
          @Override
          public String getValue(MeterMngUserGroup user) {
              return user.getUserName();
          }
        };
        view.getTable().addColumn(nameColumn, MessagesUtil.getInstance().getMessage("usergroup.field.user"));

        TextColumn<MeterMngUserGroup> userGroupColumn = new TextColumn<MeterMngUserGroup>() {
            @Override
            public String getValue(MeterMngUserGroup user) {
              if (user.getAssignedGroup() != null) {
                  return user.getAssignedGroup().getName();
              } else {
                  return "";
              }
            }
          };
        view.getTable().addColumn(userGroupColumn, MessagesUtil.getInstance().getMessage("usergroup.field.usergroup"));

        TextColumn<MeterMngUserGroup> groupHierarchyColumn = new TextColumn<MeterMngUserGroup>() {
            @Override
            public String getValue(MeterMngUserGroup user) {
              if (user.getAssignedGroupHierarchy() != null) {
                  return user.getAssignedGroupHierarchy().getName();
              } else {
                  return "";
              }
            }
          };
        view.getTable().addColumn(groupHierarchyColumn, MessagesUtil.getInstance().getMessage("usergroup.field.grouphierarchy"));

        dataProvider = new ListDataProvider<MeterMngUserGroup>();
        dataProvider.addDataDisplay(view.getTable());
        view.getPager().setDisplay(view.getTable());
    }

    private void loadData() {
        clientFactory.getGroupRpc().getAccessGroupType(new ClientCallback<GroupType>() {
            @Override
            public void onSuccess(GroupType result) {
                accessGroupType = result;
                if (result != null) {
                    panel.setUserAccessGroupType(accessGroupType);
                } else {
                    panel.clearFields();
                    Dialogs.centreErrorMessage(MessagesUtil.getInstance().getMessage("usergroup.no.accessgroup"), MediaResourceUtil.getInstance().getErrorIcon(), MessagesUtil.getInstance().getMessage("button.close"));
                }
            }
        });
    }

    @Override
    public void onArrival(Place place) {
        logger.info("Arrived at UserGroup: "+place.toString());
        loadUserGroups();
    }

    private void loadUserGroups() {
        clientFactory.getUserRpc().getUsersWithGroup(
                new ClientCallback<List<MeterMngUserGroup>>() {
                    @Override
                    public void onSuccess(List<MeterMngUserGroup> result) {
                        List<MeterMngUserGroup> list = dataProvider.getList();
                        list.clear();
                        list.addAll(result);
                    }

                    @Override
                    public void onFailure(Throwable caught) {
                        if (caught instanceof AccessControlException) {
                            clientFactory.getWorkspaceContainer().closeWorkspaceNow(UserGroupPlace.ALL_USER_GROUP_PLACE);
                        }
                        super.onFailure(caught);
                    }
                });
       clientFactory.getUserRpc().getCurrentUser(new ClientCallback<MeterMngUser>() {
                                    @Override
                                    public void onSuccess(MeterMngUser result) {
                                        loggedInUser = result;
                                        logger.info("Set the logged in user: "+loggedInUser.getUserName());
                                    }
       });
    }

    private void onClear() {
        panel.clearFields();
        panel.clearErrors();
        view.getForm().getFormPanel().setHeadingText(MessagesUtil.getInstance().getMessage("usergroup.title.add"));
        view.getForm().getSaveBtn().setText(MessagesUtil.getInstance().getMessage("button.create"));
        view.getForm().getBackBtn().setVisible(false);
    }

    private void setUserGroup(MeterMngUserGroup u) {
        panel.clearErrors();
        panel.clearFields();
        this.user = u;
        if (user != null && user.getUserName() != null) {
            panel.userBox.setText(user.getUserName());
            panel.selectedUser = new UserData(user.getId(), user.getUserName(), "");
            view.getForm().getFormPanel().setHeadingText(MessagesUtil.getInstance().getMessage("usergroup.title.update"));
            view.getForm().getSaveBtn().setText(MessagesUtil.getInstance().getMessage("button.update"));
            view.getForm().getBackBtn().setVisible(true);
        } else {
            view.getForm().getFormPanel().setHeadingText(MessagesUtil.getInstance().getMessage("usergroup.title.add"));
            view.getForm().getSaveBtn().setText(MessagesUtil.getInstance().getMessage("button.create"));
            view.getForm().getBackBtn().setVisible(false);
        }
        if (user != null && user.getGroupPath() != null && user.getGroupPath().size() > 0) {
            //set the user's current group path
            panel.userGroupBox.setSelectedGroup(user.getGroupPath());
        }
    }

    private void onSave() {
        final Long userId = getSelectedUserId();
        final Long groupId = getSelectedUserGroupId();
        if (userId != null && groupId != null) {
            SessionCheckCallback sessionCheckCallback = new SessionCheckCallback() {
                @Override
                public void callback(SessionCheckResolution resolution) {
                    final String groupName = getSelectedUserGroupName();
                    clientFactory.getUserRpc().saveUserGroup(userId, groupId, new ClientCallback<UserGroup>() {
                        @Override
                        public void onSuccess(UserGroup userGroup) {
                            Dialogs.displayInformationMessage(MessagesUtil.getInstance().getMessage("message.saved", new String[] { MessagesUtil.getInstance().getMessage("usergroup.title") }),
                                    MediaResourceUtil.getInstance().getInformationIcon(),
                                    panel.userBox.getAbsoluteLeft(),
                                    panel.userBox.getAbsoluteTop() + 75,
                                    MessagesUtil.getInstance().getMessage("button.close"));
                            onClear();
                            loadUserGroups();
                            checkLoggedInUser(userId, groupId, groupName);
                        }

                        @Override
                        public void onFailure(Throwable caught) {
                            if (caught instanceof AccessControlException) {
                                clientFactory.getWorkspaceContainer().closeWorkspaceNow(UserGroupPlace.ALL_USER_GROUP_PLACE);
                            }
                            super.onFailure(caught);
                        }
                    });
                }
            };
            clientFactory.handleSessionCheckCallback(sessionCheckCallback);
        }
    }

    //Method to check if the updated user is the logged in user - updates their current group to be their newly assigned
    //group.
    private void checkLoggedInUser(Long userId, Long groupId, final String groupName) {
        if (loggedInUser != null && loggedInUser.getId() == userId.longValue()) {
            logger.info("Updating the logged in user's group userId:"+userId+" groupId:"+groupId);

            clientFactory.getUserRpc().updateUserCurrentGroup(Boolean.TRUE, groupId, new ClientCallback<GenGroup>() {
                @Override
                public void onSuccess(GenGroup result) {
                    if (result == null) {
                        clientFactory.getEventBus().fireEvent(new SelectAccessGroupEvent());
                        return;
                    }
                    //Update the header
                    clientFactory.displayUserGroup(groupName);
                    //Notify any affected tabs
                    clientFactory.getWorkspaceContainer().notifyWorkspaces(new WorkspaceNotification(NotificationType.DATA_UPDATED, MeterMngStatics.USER_CURRENT_GROUP));
                }
            });
        }
    }

    private void onDelete() {
        if (user != null && user.getAssignedGroup() != null) {
            SessionCheckCallback sessionCheckCallback = new SessionCheckCallback() {
                @Override
                public void callback(SessionCheckResolution resolution) {
                    if (loggedInUser != null && loggedInUser.getId() == user.getId()) {
                        //cant clear my group when I am logged on
                        Dialogs.displayErrorMessage(MessagesUtil.getInstance().getMessage("usergroup.clear.yourself"),
                                MediaResourceUtil.getInstance().getErrorIcon(),
                                MessagesUtil.getInstance().getMessage("button.close"));
                    } else {
                        Dialogs.confirm(MessagesUtil.getInstance().getMessage("usergroup.confirm.clear"),
                                ResourcesFactoryUtil.getInstance().getMessages().getMessage("option.positive"),
                                ResourcesFactoryUtil.getInstance().getMessages().getMessage("option.negative"),
                                ResourcesFactoryUtil.getInstance().getQuestionIcon(),
                                new ConfirmHandler() {
                                    @Override
                                    public void confirmed(boolean confirm) {
                                        if (confirm) {
                                            logger.info("Clearing user's group: "+user.getId());
                                            clientFactory.getUserRpc().deleteUserGroup(user.getId(), new ClientCallback<Void>() {
                                                @Override
                                                public void onSuccess(Void result) {
                                                    Dialogs.displayInformationMessage(
                                                            MessagesUtil.getInstance().getMessage("usergroup.cleared"), MediaResourceUtil.getInstance().getInformationIcon(),
                                                            panel.userBox.getAbsoluteLeft(),
                                                            panel.userBox.getAbsoluteTop() + 50,
                                                            MessagesUtil.getInstance().getMessage("button.close"));
                                                    onClear();
                                                    loadUserGroups();
                                                    checkLoggedInUser(user.getId(), null, "");
                                                }
                                            });
                                        }
                                    }},
                                view.getForm().getSaveBtn().getAbsoluteLeft(),
                                view.getForm().getSaveBtn().getAbsoluteTop());
                    }
                }
            };
            clientFactory.handleSessionCheckCallback(sessionCheckCallback);
        } else {
            Dialogs.centreErrorMessage(MessagesUtil.getInstance().getMessage("usergroup.error.nocurrent"), MediaResourceUtil.getInstance().getErrorIcon(), MessagesUtil.getInstance().getMessage("button.close"));
        }
    }

    private Long getSelectedUserId() {
        panel.usernameElement.showErrorMsg(null);
        if (panel.selectedUser != null && panel.selectedUser.getId() != null) {
            return panel.selectedUser.getId();
        } else {
            panel.usernameElement.showErrorMsg(MessagesUtil.getInstance().getMessage("usergroup.error.user"));
            return null;
        }
    }

    private Long getSelectedUserGroupId() {
        panel.userGroupElement.showErrorMsg(null);
        if (panel.userGroupBox != null) {
            Long groupId = panel.userGroupBox.getSelectedGroup();
            logger.info("Selected user's group:"+groupId);
            if (SelectionDataItem.isActualGroupId(groupId)) {
                return groupId;
            }
        }
        panel.userGroupElement.showErrorMsg(MessagesUtil.getInstance().getMessage("usergroup.error.group"));
        return null;
    }

    private String getSelectedUserGroupName() {
        if (panel.userGroupBox != null) {
            return panel.userGroupBox.getSelectedGroupName();
        }
        return null;
    }

    @Override
    public void onSelect() {

    }

    @Override
    public void onLeaving() {

    }

    @Override
    public void onClose() {

    }

    @Override
    public boolean handles(Place place) {
        if (place instanceof UserGroupPlace) {
            return true;
        } else {
            return false;
        }
    }

    @Override
    public void displaySelected(MeterMngUserGroup selected) {
        onClear();
        setUserGroup(selected);
    }

    @Override
    public void handleNotification(final WorkspaceNotification notification) {
        logger.info("Received notification: " + notification);
        if ((MeterMngStatics.ACCESS_CONTROL_GROUP_TYPE.equals(notification.getDataType())
                || MeterMngStatics.ACCESS_GROUP_TYPE.equals(notification.getDataType()))
                && NotificationType.DATA_UPDATED == notification.getNotificationType()) {
            logger.info("The accessGroupType or accessGroups has been updated - reloading this workspace...");
            SessionCheckCallback sessionCheckCallback = new SessionCheckCallback() {
                @Override
                public void callback(SessionCheckResolution resolution) {
                    onClear();
                    loadData();
                    loadUserGroups();
                }
            };
            clientFactory.handleSessionCheckCallback(sessionCheckCallback);
        }
    }
}
