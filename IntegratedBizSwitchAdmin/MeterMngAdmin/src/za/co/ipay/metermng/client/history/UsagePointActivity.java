package za.co.ipay.metermng.client.history;

import za.co.ipay.metermng.client.event.UsagePointSearchEvent;
import za.co.ipay.metermng.client.factory.ClientFactory;

import com.google.gwt.activity.shared.AbstractActivity;
import com.google.gwt.event.shared.EventBus;
import com.google.gwt.user.client.ui.AcceptsOneWidget;

public class UsagePointActivity extends AbstractActivity {

    private ClientFactory clientFactory;
    private UsagePointPlace usagePointPlace;
    
    public UsagePointActivity(UsagePointPlace usagePointPlace, ClientFactory clientFactory) {
        super();
        this.clientFactory = clientFactory;
        this.usagePointPlace = usagePointPlace;
    }
    
    @Override
    public void start(AcceptsOneWidget panel, EventBus eventBus) {
        if (usagePointPlace.getUsagePointName() != null && !usagePointPlace.getUsagePointName().isEmpty()) {
            clientFactory.getEventBus().fireEvent(new UsagePointSearchEvent(usagePointPlace));
        }
    }

}
