<!DOCTYPE ui:UiBinder SYSTEM "http://dl.google.com/gwt/DTD/xhtml.ent">
<ui:UiBinder xmlns:ui="urn:ui:com.google.gwt.uibinder"
             xmlns:g="urn:import:com.google.gwt.user.client.ui"
             xmlns:ipay="urn:import:za.co.ipay.gwt.common.client.widgets"
             xmlns:p2="urn:import:za.co.ipay.metermng.client.view.component"
	xmlns:p3="urn:import:za.co.ipay.gwt.common.client.form"
>
	<ui:style field="RemoveMeterStyle">
		.selectStyle select {
			width: 189px;
			margin-right: 16px;
		}
		.txtbxStyle {
	    	padding: 2px 2px 2px 2px;
		    border: 1px solid #ccc;
		    border-top: 1px solid #999;
		    font-size: 1em;
		    font-family: Arial Unicode MS, Arial, sans-serif;
		    color: #38291E;
			margin-right: 12px;
		}
    </ui:style>

    <ui:with field="msg" type="za.co.ipay.metermng.client.i18n.UiMessages" />

    <g:HTMLPanel>
        <table>
            <tr>
                <td>
                    <g:VerticalPanel horizontalAlignment="ALIGN_CENTER" spacing="10">
                        <g:HTML ui:field="clearMeterBalanceMsg" debugId="clearMeterBalanceMsg" visible="false"/>
                        <g:Label text="{msg.getMeterSelectStoreMove}" styleName="gwt-Label-bold" ui:field="lblSelectMeterStore" />
						<ipay:IpayListBox visibleItemCount="1" ui:field="lstbxStores" styleName="{RemoveMeterStyle.selectStyle}" multipleSelect="false" />
						<g:FlowPanel ui:field="deviceMoveRefPanel" visible="false">
			                <p3:FormElement ui:field="deviceMoveRefElement" debugId="deviceMoveRefElement" required="true" labelText="{msg.getUsagePointDeviceMoveRefLbl}:">
			                    <g:TextBox debugId="deviceMoveRefTxtbx" ui:field="deviceMoveRefTxtbx" styleName="{RemoveMeterStyle.txtbxStyle}" visibleLength="28"/>
			                </p3:FormElement>
			            </g:FlowPanel>
                        <p2:SpecialActionsReasonComponent ui:field="specialactionreasons"></p2:SpecialActionsReasonComponent>
                        <g:FlowPanel>
                            <g:Button debugId="removeMeterButton" text="{msg.getRemoveMeterButton}" styleName="gwt-Button-ipay" ui:field="btnRemoveMeter"/>
                            <g:Button debugId="btnCancel" styleName="gwt-Button-ipay" ui:field="btnCancel"
                                      visible="true" text="{msg.getCancelButton}"/>
                        </g:FlowPanel>
                    </g:VerticalPanel>
                </td>
            </tr>
        </table>
    </g:HTMLPanel>
</ui:UiBinder> 
