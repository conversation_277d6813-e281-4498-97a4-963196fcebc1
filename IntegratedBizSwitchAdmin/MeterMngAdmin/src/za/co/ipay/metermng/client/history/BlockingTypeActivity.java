package za.co.ipay.metermng.client.history;

import com.google.gwt.activity.shared.AbstractActivity;
import com.google.gwt.event.shared.EventBus;
import com.google.gwt.user.client.ui.AcceptsOneWidget;

import za.co.ipay.metermng.client.event.OpenBlockingTypeEvent;
import za.co.ipay.metermng.client.factory.ClientFactory;

public class BlockingTypeActivity extends AbstractActivity {

	private ClientFactory clientFactory;
	private BlockingTypePlace place;

	public BlockingTypeActivity(ClientFactory clientFactory, BlockingTypePlace place) {
		super();
		this.clientFactory = clientFactory;
		this.place = place;
	}

	@Override
	public void start(AcceptsOneWidget panel, EventBus eventBus) {
		clientFactory.getEventBus().fireEvent(new OpenBlockingTypeEvent(place.getBlockingTypeId()));
	}
}
