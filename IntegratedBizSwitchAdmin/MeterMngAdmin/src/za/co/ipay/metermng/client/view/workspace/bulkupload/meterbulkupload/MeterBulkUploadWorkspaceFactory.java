package za.co.ipay.metermng.client.view.workspace.bulkupload.meterbulkupload;

import java.util.logging.Logger;

import com.google.gwt.place.shared.Place;

import za.co.ipay.gwt.common.client.Dialogs;
import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.gwt.common.client.workspace.WorkspaceCreateCallback;
import za.co.ipay.gwt.common.client.workspace.WorkspaceFactory;
import za.co.ipay.gwt.common.shared.exception.AccessControlException;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.history.MeterBulkUploadPlace;
import za.co.ipay.metermng.client.resource.image.MediaResource.MediaResourceUtil;
import za.co.ipay.metermng.shared.MeterMngStatics;

public class MeterBulkUploadWorkspaceFactory implements WorkspaceFactory {
	
	private ClientFactory clientFactory;
    private static Logger logger = Logger.getLogger(MeterBulkUploadWorkspaceFactory.class.getName());

	public MeterBulkUploadWorkspaceFactory(ClientFactory clientFactory) {
		this.clientFactory = clientFactory;
		clientFactory.getWorkspaceContainer().register(this);
	}

	@Override
	public void createWorkspace(Place place, WorkspaceCreateCallback workspaceCreateCallback) {
        if (!clientFactory.getUser().hasPermission(MeterMngStatics.ACCESS_PERMISSION_MM_TRANS_UPLOAD)) {
            Dialogs.displayErrorMessage(MessagesUtil.getInstance().getMessage("error.accessdenied"), 
                    MediaResourceUtil.getInstance().getLockedIcon(), 
                    MessagesUtil.getInstance().getMessage("button.close"));
            workspaceCreateCallback.onWorkspaceCreationFailed(new AccessControlException("Access is Denied"));
            return;               
        }
        try {
            MeterBulkUploadWorkspaceView view = new MeterBulkUploadWorkspaceView(clientFactory, (MeterBulkUploadPlace) place);
            workspaceCreateCallback.onWorkspaceCreated(view);
        } catch (Exception e) {
            logger.info("ERROR: workspacecreation failed, exception= " + e.getMessage());
            workspaceCreateCallback.onWorkspaceCreationFailed(e);
        }
	}

	@Override
	public boolean handles(Place place) {
		return (place instanceof MeterBulkUploadPlace);
	}

}
