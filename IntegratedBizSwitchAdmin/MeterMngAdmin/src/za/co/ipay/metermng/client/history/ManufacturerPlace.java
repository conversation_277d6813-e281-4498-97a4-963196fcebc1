package za.co.ipay.metermng.client.history;

import com.google.gwt.place.shared.Place;
import com.google.gwt.place.shared.PlaceTokenizer;
import com.google.gwt.place.shared.Prefix;

public class ManufacturerPlace extends Place {

    public static ManufacturerPlace ALL_PLACE = new ManufacturerPlace("all");

    private String name;
    
    public ManufacturerPlace(String name) {
        this.name = name;
    }
    
    public String getName() {
        return name;
    }
    
    public static String getPlaceAsString(ManufacturerPlace p) {
        return "manufacturer:"+p.getName();
    }

    @Prefix(value = "manufacturer")
    public static class Tokenizer implements PlaceTokenizer<ManufacturerPlace> {
        
        @Override
        public String getToken(ManufacturerPlace place) {
            return "all";
        }

        @Override
        public ManufacturerPlace getPlace(String token) {
            return new ManufacturerPlace(token);
        }
    }
}
