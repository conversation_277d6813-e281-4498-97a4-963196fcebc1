package za.co.ipay.metermng.client.view.workspace.meter.balancing.alert;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.logging.Logger;

import za.co.ipay.gwt.common.client.form.FormElement;
import za.co.ipay.gwt.common.client.form.Format;
import za.co.ipay.gwt.common.client.form.Format.FormatUtil;
import za.co.ipay.gwt.common.client.form.SimpleForm;
import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.gwt.common.client.widgets.PercentageTextBox;
import za.co.ipay.gwt.common.client.widgets.StrictDateFormat;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.form.SimpleFormPanel;
import za.co.ipay.metermng.mybatis.generated.model.MeterReadingType;
import za.co.ipay.metermng.shared.MeterMngStatics;

import com.google.gwt.core.client.GWT;
import com.google.gwt.i18n.client.DateTimeFormat;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.user.client.ui.ListBox;
import com.google.gwt.user.client.ui.Widget;
import com.google.gwt.user.datepicker.client.DateBox;

public class EnergyBalancingAlertPanel extends SimpleFormPanel {
    
    ClientFactory clientFactory;
    
    @UiField FormElement startElement;
    @UiField FormElement endElement;
    @UiField FormElement percentElement;
    @UiField FormElement readingTypeElement;
    
    @UiField DateBox startBox;
    @UiField DateBox endBox;
    @UiField PercentageTextBox percentBox;
    @UiField ListBox readingTypeBox;
    
    private int energyForwardReadingIndex = 0;
    
    private static Logger logger = Logger.getLogger(EnergyBalancingAlertPanel.class.getName());
    
    private static EnergyBalancingPanelUiBinder uiBinder = GWT.create(EnergyBalancingPanelUiBinder.class);

    interface EnergyBalancingPanelUiBinder extends UiBinder<Widget, EnergyBalancingAlertPanel> {
    }

    public EnergyBalancingAlertPanel(ClientFactory clientFactory, SimpleForm form) {
        super(form);
        this.clientFactory = clientFactory;
        initWidget(uiBinder.createAndBindUi(this));
        initUi();
    }
    
    private void initUi() {
        Format format = FormatUtil.getInstance();
        StrictDateFormat strictDateFormat = new StrictDateFormat(
                DateTimeFormat.getFormat(format.getDateFormat() + " " + format.getTimeFormat()));
        startBox.setFormat(strictDateFormat);
        endBox.setFormat(strictDateFormat);
        clearFields();
    }
    
    protected void setMeterReadingTypes(ArrayList<MeterReadingType> types) {
        readingTypeBox.clear();
        readingTypeBox.addItem("");        
        for(int i=0;i<types.size();i++) {
            MeterReadingType type = types.get(i);
            readingTypeBox.addItem(type.getName(), type.getId().toString());
            if (type.getValue().equals(MeterMngStatics.ENERGY_FORWARD_METER_READING_TYPE)) {
                energyForwardReadingIndex = i+1;
                readingTypeBox.setSelectedIndex(i+1);
            }
        }
    }
    
    public Long getMeterReadingTypeId() {
        int index = readingTypeBox.getSelectedIndex();
        if (index > 0) {
            return Long.valueOf(readingTypeBox.getValue(index));
        } else {
            return null;
        }
    }

    @Override
    public void addFieldHandlers() {
        
    }

    @Override
    public void clearFields() {
        readingTypeBox.setSelectedIndex(energyForwardReadingIndex);
        startBox.setValue(getInitStartDate());
        endBox.setValue(getInitEndDate());
        percentBox.setAmount(MeterMngStatics.DEFAULT_ENERGY_BALANCE_VARIATION);
    }
    
    private Date getInitStartDate() {
        String start = DateTimeFormat.getFormat("MM/yyyy").format(new Date());
        return DateTimeFormat.getFormat("dd/MM/yyyy HH:mm").parse("01/"+start+" 00:00");
    }
    
    private Date getInitEndDate() {
        return new Date(); //now
    }

    @Override
    public void clearErrors() {
        readingTypeElement.setErrorMsg(null);
        startElement.setErrorMsg(null);
        endElement.setErrorMsg(null);
        percentElement.setErrorMsg(null); 
    }
    
    protected boolean isValidInput() {
        clearErrors();
        boolean valid = true;
        
        Long meterReadingTypeId = null;
        int index = readingTypeBox.getSelectedIndex();
        if (index > 0) {
            meterReadingTypeId = Long.valueOf(readingTypeBox.getValue(index));
        }
        if (meterReadingTypeId == null) {
            valid = false;
            readingTypeElement.showErrorMsg(MessagesUtil.getInstance().getMessage("energybalancing.error.readingtype"));
        }
        
        Date start = getStartDate();
        String startInput = startBox.getTextBox().getText();
        Date end = getEndDate();
        String endInput = endBox.getTextBox().getText();
        String format = FormatUtil.getInstance().getDateFormat() + " " +FormatUtil.getInstance().getTimeFormat();
        logger.info("StartDate: "+start+" EndDate: "+end);
        if (start == null && startInput == null) {
            valid = false;
            startElement.showErrorMsg(MessagesUtil.getInstance().getMessage("meterreadings.error.start"));
        } else if (start == null && startInput != null) {
            valid = false;
            startElement.showErrorMsg(MessagesUtil.getInstance().getMessage("meterreadings.error.start.format", new String[]{format}));
        }
        
        if (end == null && endInput == null) {
            valid = false;
            endElement.showErrorMsg(MessagesUtil.getInstance().getMessage("meterreadings.error.end"));
        } else if (end == null && endInput != null) {
            valid = false;
            endElement.showErrorMsg(MessagesUtil.getInstance().getMessage("meterreadings.error.end.format", new String[]{format}));
        }        
        
        if (start != null && end != null && !start.before(end)) {
            valid = false;
            startElement.showErrorMsg(MessagesUtil.getInstance().getMessage("meterreadings.error.dates"));
        }
        
        BigDecimal percent = getPercent();
        if (percent == null || percent.doubleValue() < 0) {
            valid = false;
            percentElement.showErrorMsg(MessagesUtil.getInstance().getMessage("energybalancing.error.percent"));
        }
        
        return valid;
    }
    
    public Date getStartDate() {
        return startBox.getValue();
    }
    
    public Date getEndDate() {
        return endBox.getValue();
    }
    
    public BigDecimal getPercent() {
        return percentBox.getAmount();
    }
}
