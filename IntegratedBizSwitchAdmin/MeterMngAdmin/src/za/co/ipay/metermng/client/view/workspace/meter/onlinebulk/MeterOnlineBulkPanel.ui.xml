<!DOCTYPE ui:UiBinder SYSTEM "http://dl.google.com/gwt/DTD/xhtml.ent">
<ui:UiBinder xmlns:ui="urn:ui:com.google.gwt.uibinder"
             xmlns:g="urn:import:com.google.gwt.user.client.ui"
             xmlns:form="urn:import:za.co.ipay.gwt.common.client.form"
             xmlns:p1="urn:import:za.co.ipay.gwt.common.client.widgets"
             xmlns:p2="urn:import:com.google.gwt.user.datepicker.client"
             xmlns:p3="urn:import:za.co.ipay.metermng.client.view.component"
             xmlns:p4="urn:import:za.co.ipay.metermng.client.view.component.meter"
             xmlns:pricingstructure="urn:import:za.co.ipay.metermng.client.widget.pricingstructure">

      <ui:style>    
          .spaceLeft {
            margin-left: 5em;
        }
    </ui:style>
    
      <ui:with field="msg" type="za.co.ipay.metermng.client.i18n.UiMessages" />

      <g:FlowPanel ui:field="mobPanel" debugId="meterOnlineBulkPanel" width="100%">

            <form:FormRowPanel>

                <form:FormElement helpMsg="{msg.getOnlineBulkSelectStoreHelp}" labelText="{msg.getMeterSelectStoreAdd}: " >         
                    <p1:IpayListBox  ui:field="lstbxSelectStore" visibleItemCount="1" styleName="gwt-ListBox-ipay" multipleSelect="false" debugId="lstbxSelectStore"  />
                </form:FormElement>

                <form:FormElement ui:field="suggestBoxMeterElement" labelText="{msg.getMeterNumberInstructions}:" required="true" helpMsg="{msg.getOnlineBulkMeterNumberHelp}">
                    <g:SuggestBox ui:field="suggestBoxMeterNumber" styleName="gwt-TextBox" tabIndex="1" debugId="suggestBoxMeterNumber"/>
                </form:FormElement>

                <form:FormElement ui:field="meterModelElement" helpMsg="{msg.getSelectMeterModelHelp}" labelText="{msg.getSelectMeterModel}:" required="true">
                    <p1:IpayListBox ui:field="lstbxMeterModel" visibleItemCount="1" styleName="gwt-ListBox-ipay" multipleSelect="false" debugId="lstbxMeterModel" />
                </form:FormElement>

            </form:FormRowPanel>

          <form:FormRowPanel ui:field="encryptionKeyRow" visible="false">
              <form:FormElement ui:field="encryptionKeyElement" labelText="Encryption Key:" helpMsg="{msg.getEncryptionKeyHelp}" required="true">
                  <g:TextBox ui:field="txtbxEncryptionKey" styleName="gwt-TextBox" visibleLength="20" debugId="txtbxEncryptionKey"/>
              </form:FormElement>
          </form:FormRowPanel>
            
            <form:FormRowPanel ui:field="breakerIdPanel" visible="false">
                <form:FormElement ui:field="breakerIdElement" debugId="breakerIdElement" required="true" helpMsg="{msg.getMeterBreakerIdHelp}" labelText="{msg.getMeterBreakerId}:">
                    <g:TextBox debugId="txtbxBreakerId" ui:field="txtbxBreakerId" styleName="gwt-TextBox" visibleLength="20"/>
                </form:FormElement>  
            </form:FormRowPanel>

            <form:FormGroupPanel ui:field="stsContainer" labelText="{msg.getMeterStsInfo}" >
                  <form:FormRowPanel>
                      <form:FormElement ui:field="supplyGrpCdeElement" required="true" helpMsg="{msg.getOnlineBulkSupplyGroupCodeHelp}" labelText="{msg.getMeterSupplyGroupCode}">
                          <p1:IpayListBox ui:field="lstbxSupplyGrpCde" visibleItemCount="1" styleName="gwt-ListBox-ipay" multipleSelect="false" debugId="lstbxSupplyGrpCde"/>
                      </form:FormElement>
 
                      <form:FormElement ui:field="algCodeElement" required="true" helpMsg="{msg.getMeterAlgorithmCodeHelp}" labelText="{msg.getMeterAlgorithmCode}">
                          <p1:IpayListBox  ui:field="lstbxAlgCode" visibleItemCount="1" styleName="gwt-ListBox-ipay" multipleSelect="false"  debugId="lstbxAlgCode"/>
                      </form:FormElement>

                  </form:FormRowPanel>

                  <form:FormRowPanel >
                      <form:FormElement ui:field="tokTecCodeElement" required="true" helpMsg="{msg.getMeterTokenTechCodeHelp}" labelText="{msg.getMeterTokenTechCode}">
                          <p1:IpayListBox  ui:field="lstbxTokTecCode" visibleItemCount="1" styleName="gwt-ListBox-ipay" multipleSelect="false" debugId="lstbxTokTecCode"/>
                      </form:FormElement>
 
                      <form:FormElement ui:field="currTariffIndxElement" required="true" helpMsg="{msg.getOnlineBulkTariffIndxHelpMeter}" labelText="{msg.getMeterTariffIndex}">
                          <g:TextBox  ui:field="txtbxCurrTariffIndx" styleName="gwt-TextBox" visibleLength="3" maxLength="2" debugId="txtbxCurrTariffIndx"/>
                      </form:FormElement>


                  </form:FormRowPanel>
            </form:FormGroupPanel>

           <form:FormGroupPanel ui:field="uriPanel" debugId="uriPanel" labelText="{msg.getMeterUriFields}" visible="false">
                <form:FormRowPanel ui:field="uriPanelRow1">
                  <form:FormElement ui:field="meterUriAddressElement" debugId="meterUriAddressElement" helpMsg="{msg.getMeterUriAddressHelp}" labelText="{msg.getMeterUriAddress}:">
                      <g:TextBox debugId="txtbxMeterUriAddress" ui:field="txtbxMeterUriAddress" styleName="gwt-TextBox" visibleLength="31"/>
                  </form:FormElement>
                  <form:FormElement ui:field="meterUriPortElement" debugId="meterUriPortElement" helpMsg="{msg.getMeterUriPortHelp}" labelText="{msg.getMeterUriPort}:">
                      <form:BigDecimalValueBox debugId="txtbxMeterUriPort" ui:field="txtbxMeterUriPort" styleName="gwt-TextBox" visibleLength="7"/>
                  </form:FormElement>
                </form:FormRowPanel>
                <form:FormRowPanel ui:field="uriPanelRow2">
                  <form:FormElement ui:field="meterUriProtocolElement" debugId="meterUriProtocolElement" helpMsg="{msg.getMeterUriProtocolHelp}" labelText="{msg.getMeterUriProtocol}:">
                      <g:TextBox debugId="txtbxMeterUriProtocol" ui:field="txtbxMeterUriProtocol" styleName="gwt-TextBox" visibleLength="18"/>
                  </form:FormElement>
                  <form:FormElement ui:field="meterUriParamsElement" debugId="meterUriParamsElement" helpMsg="{msg.getMeterUriParamsHelp}" labelText="{msg.getMeterUriParams}:">
                      <g:TextArea debugId="txtbxMeterUriParams" ui:field="txtbxMeterUriParams" styleName="gwt-TextBox" visibleLines="3"/>
                  </form:FormElement>
                </form:FormRowPanel>
          </form:FormGroupPanel>


          <form:FormGroupPanel labelText="{msg.getUsagePointGroupPanelTitle}">
                <form:FormRowPanel>
                    <form:FormElement ui:field="installationDateElement" labelText="{msg.getUsagePointMeterInstallationDate}" helpMsg="{msg.getUsagePointMeterInstallationDateHelp}" required="true">
                        <p2:DateBox  ui:field="dtbxMeterInstallationDate" styleName="gwt-TextBox" debugId="dtbxMeterInstallationDate"/>
                    </form:FormElement>
                </form:FormRowPanel>

				<form:FormGroupPanel labelText="Pricing Structure">
					<form:FormRowPanel>
						<form:FormElement ui:field="currentPSElement" helpMsg="{msg.getUsagePointPricingStructureHelp}" labelText="{msg.getUsagePointPricingStructure}: " required="true" debugId="currentPSElement">
                            <pricingstructure:PricingStructureLookup debugId="currentPricingStructureLookup" ui:field="currentPricingStructureLookup"/>
                        </form:FormElement>
						<form:FormElement ui:field="currentPSStartDateElement" debugId="currentPSStartDateElement" labelText="{msg.getUsagePointPSStartDateLbl}:" helpMsg="{msg.getUsagePointPSStartDateHelp}">
							<p2:DateBox ui:field="currentPricingStructureStartDate" debugId="currentPricingStructureStartDate" styleName="gwt-TextBox" enabled="false"/>
						</form:FormElement>
						<form:FormElement>
							<g:PushButton ui:field="showCurrTariffBtn" />
						</form:FormElement>
					</form:FormRowPanel>
			
					<form:FormRowPanel ui:field="futurePSRowPanel" debugId="futurePSRowPanel" visible="false">
						<form:FormElement ui:field="futurePSElement" helpMsg="{msg.getUsagePointFuturePSListHelp}" labelText="{msg.getUsagePointPricingStructure}: " debugId="futurePSElement">
                            <pricingstructure:PricingStructureLookup debugId="futurePricingStructureLookup" ui:field="futurePricingStructureLookup"/>
                        </form:FormElement>
						<form:FormElement ui:field="futurePSStartDateElement" debugId="futurePSStartDateElement" labelText="{msg.getUsagePointPSStartDateLbl}:" helpMsg="{msg.getUsagePointFuturePSDateHelp}">
							<p2:DateBox ui:field="futurePricingStructureStartDate" debugId="futurePricingStructureStartDate" styleName="gwt-TextBox" enabled="false"/>
						</form:FormElement>
					</form:FormRowPanel>
			
					<form:FormRowPanel ui:field="pricingChangeReasonPanel" visible="false" />
				</form:FormGroupPanel>
             
                <form:FormRowPanel>
                    <g:HorizontalPanel>
                    <g:FlowPanel ui:field="locationGroupPanel"/>
                    
                    <form:FormElement ui:field="suiteNumberElement" helpMsg="{msg.getLocationSuiteNumberHelp}" labelText="{msg.getLocationSuiteNumberName}:"  styleName="{style.spaceLeft}">
                        <g:HorizontalPanel>
                            <g:cell horizontalAlignment="ALIGN_CENTER" verticalAlignment="ALIGN_MIDDLE">
                                <g:Label text="{msg.getOnlineBulkSuiteNoText}" styleName="gwt-Label-bold"></g:Label>
                            </g:cell>
                            <g:TextBox  ui:field="txtbxSuiteNumber" styleName="gwt-TextBox" visibleLength="5" debugId="txtbxSuiteNumber"/>
                        </g:HorizontalPanel>
                    </form:FormElement>
                    </g:HorizontalPanel>
                </form:FormRowPanel>
            </form:FormGroupPanel>


            <form:FormGroupPanel labelText="{msg.getCustomerInformation}">
                <form:FormRowPanel>
                    <form:FormElement ui:field="surnameElement" helpMsg="{msg.getOnlineBulkSurnameHelp}" labelText="{msg.getCustomerSurname}:" required="true">
                        <g:HorizontalPanel>
                            <g:cell horizontalAlignment="ALIGN_CENTER" verticalAlignment="ALIGN_MIDDLE">
                                <g:Label text="{msg.getOnlineBulkTenantText}" styleName="gwt-Label-bold"></g:Label>
                            </g:cell>    
                            <g:TextBox  ui:field="txtbxSurname" styleName="gwt-TextBox" visibleLength="15" debugId="txtbxSurname"/>
                        </g:HorizontalPanel>    
                    </form:FormElement>
                    
                    <form:FormElement ui:field="phoneNumberElement" helpMsg="{msg.getOnlineBulkCustomerPhoneHelp}" labelText="{msg.getCustomerPhone}:">
                          <g:TextBox ui:field="txtbxPhone" styleName="gwt-TextBox gap" visibleLength="15" tabIndex="2"  debugId="txtbxPhone"/>
                     </form:FormElement>
                </form:FormRowPanel>    
            </form:FormGroupPanel>
            
            <form:FormGroupPanel ui:field="freeIssueContainer" labelText="{msg.getOnlineBulkFreeIssueTitle}">
                <form:FormRowPanel>
                    <form:FormElement ui:field="genFreeIssueTokenElement">
                        <g:CheckBox ui:field="chkBxGenFreeIssueToken" debugId="chkBxGenFreeIssueToken" text="{msg.getOnlineBulkFreeIssueGenerate}" checked="false" styleName="gwt-Label-bold-left"/>
                    </form:FormElement>

                    <form:FormElement ui:field="unitsElement" visible="false">
                        <g:HorizontalPanel verticalAlignment="ALIGN_MIDDLE">
                            <form:BigDecimalValueBox  ui:field="txtbxFreeIssueUnits" alignment="LEFT" styleName="gwt-TextBox-ipay" visibleLength="10" debugId="txtbxFreeIssueUnits"/>
                            <g:Label ui:field="lblUnitSymbol"/>        
                        </g:HorizontalPanel>
                    </form:FormElement>

                    <form:FormElement ui:field="smsTokenElement" helpMsg="{msg.getOnlineBulkFreeIssueSmsTokenHelp}" visible="false">
                         <g:CheckBox  ui:field="chkBxSmsToken" text="{msg.getOnlineBulkFreeIssueSmsToken}" checked="false" styleName="gwt-Label-bold-left" debugId="chkBxSmsToken"/>
                    </form:FormElement>
                </form:FormRowPanel>
                        
                <p3:SpecialActionsReasonComponent ui:field="freeIssueTokenReasonsComponent" visible="false"></p3:SpecialActionsReasonComponent>

                <form:FormRowPanel>
                    <p4:EngineeringTokenUserRefPanel ui:field="engineeringTokenUserRefPanel"/>
                </form:FormRowPanel>
            </form:FormGroupPanel>
      </g:FlowPanel>
    
</ui:UiBinder> 
