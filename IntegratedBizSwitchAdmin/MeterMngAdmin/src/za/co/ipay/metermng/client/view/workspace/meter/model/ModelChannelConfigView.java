package za.co.ipay.metermng.client.view.workspace.meter.model;

import java.math.BigDecimal;
import java.util.Comparator;
import java.util.List;
import java.util.logging.Logger;

import com.google.gwt.event.dom.client.ClickEvent;
import com.google.gwt.event.dom.client.ClickHandler;
import com.google.gwt.user.cellview.client.ColumnSortEvent;
import com.google.gwt.user.cellview.client.ColumnSortEvent.ListHandler;
import com.google.gwt.user.cellview.client.TextColumn;
import com.google.gwt.user.client.ui.Anchor;
import com.google.gwt.user.client.ui.Button;
import com.google.gwt.view.client.ListDataProvider;

import za.co.ipay.gwt.common.client.Dialogs;
import za.co.ipay.gwt.common.client.form.FormManager;
import za.co.ipay.gwt.common.client.handler.ConfirmHandler;
import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.gwt.common.client.validation.ClientValidatorUtil;
import za.co.ipay.gwt.common.client.workspace.SessionCheckCallback;
import za.co.ipay.gwt.common.client.workspace.SimpleTableView;
import za.co.ipay.gwt.common.client.workspace.WorkspaceNotification;
import za.co.ipay.gwt.common.client.workspace.WorkspaceNotification.NotificationType;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.resource.image.MediaResource.MediaResourceUtil;
import za.co.ipay.metermng.client.rpc.callback.ClientCallback;
import za.co.ipay.metermng.client.view.component.BaseComponent;
import za.co.ipay.metermng.client.widget.StatusTableColumn;
import za.co.ipay.metermng.mybatis.generated.model.ModelChannelConfig;
import za.co.ipay.metermng.shared.MeterMngStatics;
import za.co.ipay.metermng.shared.dto.meter.MeterModelDto;
import za.co.ipay.metermng.shared.dto.meter.ModelChannelConfigDto;

public class ModelChannelConfigView extends BaseComponent implements FormManager<ModelChannelConfigDto> {

    private MeterModelWorkspaceView parentWorkspace;

    private SimpleTableView<ModelChannelConfigDto> view;
    ModelChannelConfigPanel panel;
    private Button deleteBtn;

    private ListDataProvider<ModelChannelConfigDto> dataProvider;
    private ListHandler<ModelChannelConfigDto> columnSortHandler;
    private TextColumn<ModelChannelConfigDto> nameColumn;
    private TextColumn<ModelChannelConfigDto> valueColumn;
    private TextColumn<ModelChannelConfigDto> timeIntervalColumn;
    private TextColumn<ModelChannelConfigDto> maxSizeColumn;
    private TextColumn<ModelChannelConfigDto> readingMultiplierColumn;
    private StatusTableColumn<ModelChannelConfigDto> statusColumn;

    private MeterModelDto meterModelDto;
    private ModelChannelConfig modelChannelConfig;

    private static Logger logger = Logger.getLogger(ModelChannelConfigView.class.getName());

    public ModelChannelConfigView(ClientFactory clientFactory, MeterModelWorkspaceView parentWorkspace, SimpleTableView<ModelChannelConfigDto> view) {
        this.clientFactory = clientFactory;
        this.parentWorkspace = parentWorkspace;
        this.view = view;
        initView();
        initForm();
        createTable();
    }

    private void initView() {
        view.setFormManager(this);
    }

    private void initForm() {
        Anchor back = new Anchor(MessagesUtil.getInstance().getMessage("meter.models"));
        back.addClickHandler(new ClickHandler() {
            @Override
            public void onClick(ClickEvent event) {
                goBack();
            }
        });
        view.getPageHeader().addPageHeaderLink(back);

        panel = new ModelChannelConfigPanel(clientFactory, view.getForm(), this);

        view.getForm().setHasDirtyDataManager(parentWorkspace);
        view.getForm().getFormFields().add(panel);

        view.getForm().getSaveBtn().setText(MessagesUtil.getInstance().getMessage("button.create"));
        view.getForm().getSaveBtn().addClickHandler(new ClickHandler() {
            @Override
            public void onClick(ClickEvent event) {
                onSave();
            }
        });

        view.getForm().getOtherBtn().setText(MessagesUtil.getInstance().getMessage("button.cancel"));
        view.getForm().getOtherBtn().addClickHandler(new ClickHandler() {
            @Override
            public void onClick(ClickEvent event) {
                view.getForm().checkDirtyData(new ConfirmHandler() {
                    @Override
                    public void confirmed(boolean confirm) {
                        if (confirm) {
                            view.getForm().setDirtyData(false);
                            displaySelected(null);
                        }
                    }
                });
            }
        });

        view.getForm().getBackBtn().setText(MessagesUtil.getInstance().getMessage("button.back"));
        view.getForm().getBackBtn().addClickHandler(new ClickHandler() {
            @Override
            public void onClick(ClickEvent event) {
                goBack();
            }
        });
        view.getForm().getBackBtn().setVisible(true);

        deleteBtn = new Button(MessagesUtil.getInstance().getMessage("button.delete"));
        deleteBtn.addClickHandler(new ClickHandler() {
            @Override
            public void onClick(ClickEvent event) {
                deleteModelChannelConfig();
            }
        });
        deleteBtn.setVisible(false);
        view.getForm().getSecondaryButtons().add(deleteBtn);
        view.getForm().getSecondaryButtons().setVisible(true);

        view.getForm().getFormPanel().setHeadingText(MessagesUtil.getInstance().getMessage("channel.config.title.add"));
    }

    private void goBack() {
        view.getForm().checkDirtyData(new ConfirmHandler() {
            @Override
            public void confirmed(boolean confirm) {
                if (confirm) {
                    view.getForm().setDirtyData(false);
                    parentWorkspace.showMeterModel();
                }
            }});
    }

    private void createTable() {
        if (dataProvider == null) {
            nameColumn = new TextColumn<ModelChannelConfigDto>() {
                @Override
                public String getValue(ModelChannelConfigDto object) {
                    return object.getMdcChannelDto().getMdcChannel().getName();
                }
            };
            nameColumn.setSortable(true);

            valueColumn = new TextColumn<ModelChannelConfigDto>() {
                @Override
                public String getValue(ModelChannelConfigDto object) {
                    return object.getMdcChannelDto().getMdcChannel().getValue();
                }
            };
            valueColumn.setSortable(true);

            TextColumn<ModelChannelConfigDto> descripColumn = new TextColumn<ModelChannelConfigDto>() {
                @Override
                public String getValue(ModelChannelConfigDto object) {
                    return object.getMdcChannelDto().getMdcChannel().getDescription();
                }
            };

            statusColumn = new StatusTableColumn<ModelChannelConfigDto>();
            statusColumn.setSortable(true);

            timeIntervalColumn = new TextColumn<ModelChannelConfigDto>() {
                @Override
                public String getValue(ModelChannelConfigDto object) {
                    return object.getTimeIntervalName();
                }
            };
            timeIntervalColumn.setSortable(true);

            maxSizeColumn  = new TextColumn<ModelChannelConfigDto>() {
                @Override
                public String getValue(ModelChannelConfigDto object) {
                    return object.getModelChannelConfig().getMaxSize().toPlainString();
                }
            };
            maxSizeColumn.setSortable(true);

            readingMultiplierColumn = new TextColumn<ModelChannelConfigDto>() {
                @Override
                public String getValue(ModelChannelConfigDto object) {
                    BigDecimal readingMultiplier = object.getModelChannelConfig().getReadingMultiplier();
                    if (readingMultiplier != null) {
                        return readingMultiplier.toPlainString();
                    } else {
                        return "";
                    }
                }
            };
            timeIntervalColumn.setSortable(true);

            view.getTable().addColumn(nameColumn, MessagesUtil.getInstance().getMessage("channel.field.name"));
            view.getTable().addColumn(valueColumn, MessagesUtil.getInstance().getMessage("channel.field.value"));
            view.getTable().addColumn(descripColumn, MessagesUtil.getInstance().getMessage("channel.field.descrip"));
            view.getTable().addColumn(statusColumn, MessagesUtil.getInstance().getMessage("channel.field.status"));
            view.getTable().addColumn(timeIntervalColumn, MessagesUtil.getInstance().getMessage("channel.field.time.interval"));
            view.getTable().addColumn(maxSizeColumn, MessagesUtil.getInstance().getMessage("channel.field.maxsize"));
            view.getTable().addColumn(readingMultiplierColumn, MessagesUtil.getInstance().getMessage("channel.field.reading_multiplier"));

            // Set the data provider for the table
            dataProvider = new ListDataProvider<ModelChannelConfigDto>();
            dataProvider.addDataDisplay(view.getTable());

            // Create the table's pager
            view.getPager().setDisplay(view.getTable());

        }
    }

    public void setMeterModel(MeterModelDto meterModelDto) {
        clear();
        this.meterModelDto = meterModelDto;
        view.setDataDetails(meterModelDto.getMeterModel().getName(), meterModelDto.getMeterModel().getDescription());
        panel.setMdcName(meterModelDto.getMdc().getName());
        loadChannelConfigs();
    }

    protected void loadChannelConfigs() {
        clientFactory.getMdcChannelRpc().getModelChannelConfigs(meterModelDto.getMeterModel().getId(), new ClientCallback<List<ModelChannelConfigDto>>() {
            @Override
            public void onSuccess(List<ModelChannelConfigDto> data) {
                displayData(data);
                view.getTable().setPageStart(0);
            }
        });
    }

    private void displayData(List<ModelChannelConfigDto> data) {
        logger.info("Displaying retrieved Mdc Channels: "+data.size());
        if (dataProvider != null && dataProvider.getList() != null) {
            dataProvider.getList().clear();
            dataProvider.getList().addAll(data);
            panel.populateMdcChannels(meterModelDto.getMdc().getId(), meterModelDto.getMeterModel().getId());
        }
        if (columnSortHandler == null || columnSortHandler.getList() == null) {
            columnSortHandler = new ListHandler<ModelChannelConfigDto>(dataProvider.getList());
            columnSortHandler.setComparator(valueColumn, new Comparator<ModelChannelConfigDto>() {
                public int compare(ModelChannelConfigDto o1, ModelChannelConfigDto o2) {
                    if (o1 == o2) {
                        return 0;
                    }
                    if (o1 != null) {
                        return (o2 != null) ? o1.getMdcChannelDto().getMdcChannel().getValue().compareTo(o2.getMdcChannelDto().getMdcChannel().getValue()) : 1;
                    }
                    return -1;
                }
            });
            view.getTable().addColumnSortHandler(columnSortHandler);

            columnSortHandler.setComparator(nameColumn, new Comparator<ModelChannelConfigDto>() {
                public int compare(ModelChannelConfigDto o1, ModelChannelConfigDto o2) {
                    if (o1 == o2) {
                        return 0;
                    }

                    // Compare the name columns.
                    if (o1 != null) {
                        return (o2 != null) ? o1.getMdcChannelDto().getMdcChannel().getName().compareTo(o2.getMdcChannelDto().getMdcChannel().getName()) : 1;
                    }
                    return -1;
                }
            });
            view.getTable().addColumnSortHandler(columnSortHandler);

            columnSortHandler.setComparator(timeIntervalColumn, new Comparator<ModelChannelConfigDto>() {
                public int compare(ModelChannelConfigDto o1, ModelChannelConfigDto o2) {
                    if (o1 == o2) {
                        return 0;
                    }

                    // Compare the name columns.
                    if (o1 != null) {
                        return (o2 != null) ? o1.getTimeIntervalName().compareTo(o2.getTimeIntervalName()) : 1;
                    }
                    return -1;
                }
            });
            view.getTable().addColumnSortHandler(columnSortHandler);

            // We know that the data is sorted by value by default.
            view.getTable().getColumnSortList().push(valueColumn);
            ColumnSortEvent.fire(view.getTable(), view.getTable().getColumnSortList());
        } else {
            columnSortHandler.setList(dataProvider.getList());
            ColumnSortEvent.fire(view.getTable(), view.getTable().getColumnSortList());
        }
    }

    @Override
    public void displaySelected(ModelChannelConfigDto selected) {
        displaySelectedModelChannelConfig(selected);
    }

    private void displaySelectedModelChannelConfig(ModelChannelConfigDto selected) {
        clear();
        if (selected == null) {
            setCreateButton();
            deleteBtn.setVisible(false);
            view.clearTableSelection();
        } else {
            view.getForm().getSaveBtn().setText(MessagesUtil.getInstance().getMessage("button.update"));
            view.getForm().getFormPanel().setHeadingText(MessagesUtil.getInstance().getMessage("channel.config.title.update"));
            deleteBtn.setVisible(true);

            this.modelChannelConfig = selected.getModelChannelConfig();
            panel.setChannelDetails(selected.getMdcChannelDto());
            panel.mdcChannelElement.setVisible(false);
            panel.setTimeIntervalSelection(modelChannelConfig.getTimeIntervalId());
            panel.maxSizeBox.setText(modelChannelConfig.getMaxSize().toPlainString());
            if (modelChannelConfig.getReadingMultiplier() != null) {
                panel.readingMultiplierBox.setText(modelChannelConfig.getReadingMultiplier().toPlainString());
            }
        }
    }

    protected void setCreateButton() {
        view.getForm().getSaveBtn().setText(MessagesUtil.getInstance().getMessage("button.create"));
        view.getForm().getFormPanel().setHeadingText(MessagesUtil.getInstance().getMessage("channel.config.title.add"));
    }

    protected void clear() {
        modelChannelConfig = null;
        setCreateButton();
        deleteBtn.setVisible(false);
        panel.clearFields();
        panel.clearErrors();
        view.clearTableSelection();
    }

    private boolean isValidInput() {
        boolean valid = true;
        panel.clearErrors();

        if (panel.maxSizeBox.getText() == null || panel.maxSizeBox.getText().isEmpty()){
            panel.maxSizeElement.setErrorMsg(MessagesUtil.getInstance().getMessage("error.field.value.null"));
            valid = false;
        }

        if ((modelChannelConfig == null || modelChannelConfig.getId() == null) && panel.mdcChannelListBox.getSelectedIndex() < 1) {
            panel.mdcChannelElement.setErrorMsg(MessagesUtil.getInstance().getMessage("error.field.value.null"));
            valid = false;
        }

        if (!valid) {
            return valid;
        }

        ModelChannelConfigDto dto = updateModelChannelConfig();
        ModelChannelConfig channelConfig = dto.getModelChannelConfig();

        if (!ClientValidatorUtil.getInstance().validateField(channelConfig, "maxSize", panel.maxSizeElement)) {
            valid = false;
        }

        return valid;
    }

    private ModelChannelConfigDto updateModelChannelConfig() {
        ModelChannelConfigDto dto = new ModelChannelConfigDto();
        updateModelChannelConfig(dto);
        return dto;
    }

    private void updateModelChannelConfig(ModelChannelConfigDto dto) {
        ModelChannelConfig modelChannelConfigToUpdate = new ModelChannelConfig();
        if (modelChannelConfig != null) {
            modelChannelConfigToUpdate.setId(modelChannelConfig.getId());
            modelChannelConfigToUpdate.setMdcChannelId(modelChannelConfig.getMdcChannelId());
        } else {
            modelChannelConfigToUpdate.setMdcChannelId(Long.valueOf(panel.mdcChannelListBox.getValue(panel.mdcChannelListBox.getSelectedIndex())));
        }
        modelChannelConfigToUpdate.setMeterModelId(meterModelDto.getMeterModel().getId());
        if (panel.timeIntervalListBox.getSelectedIndex() !=0) {
            modelChannelConfigToUpdate.setTimeIntervalId(Long.valueOf(panel.timeIntervalListBox.getValue(panel.timeIntervalListBox.getSelectedIndex())));
        } else {
            modelChannelConfigToUpdate.setTimeIntervalId(null);
        }
        modelChannelConfigToUpdate.setMaxSize(new BigDecimal(panel.maxSizeBox.getText()));
        if (panel.readingMultiplierBox.getValue() != null) {
            modelChannelConfigToUpdate.setReadingMultiplier(panel.readingMultiplierBox.getValue());
        }
        dto.setModelChannelConfig(modelChannelConfigToUpdate);
    }

    private void onSave() {
        disableButtons();
        if (isValidInput()) {
            saveNow();
        } else {
            enableButtons();
        }
    }

    private void saveNow() {
        SessionCheckCallback sessionCheckCallback = new SessionCheckCallback() {
            @Override
            public void callback(SessionCheckResolution resolution) {
                ModelChannelConfigDto m = updateModelChannelConfig();
                clientFactory.getMdcChannelRpc().saveModelChannelConfig(
                        m,
                        new ClientCallback<ModelChannelConfigDto>(view.getForm().getSaveBtn().getAbsoluteLeft(), view.getForm().getSaveBtn().getAbsoluteTop()) {
                            @Override
                            public void onSuccess(ModelChannelConfigDto result) {
                                view.getForm().setDirtyData(false);
                                loadChannelConfigs();
                                Dialogs.displayInformationMessage(
                                        MessagesUtil.getInstance()
                                                .getSavedMessage(
                                                        new String[] { MessagesUtil.getInstance().getMessage("channel.config.field.titlename") }), MediaResourceUtil.getInstance()
                                                .getInformationIcon(), view.getForm().getSaveBtn().getAbsoluteLeft(), view
                                                .getForm().getSaveBtn().getAbsoluteTop(), MessagesUtil.getInstance()
                                                .getMessage("button.close"));
                                clear();
                                enableButtons();
                                sendNotification();
                            }
                            @Override
                            public void onFailureClient() {
                                enableButtons();
                            }
                        });
            }
        };
        clientFactory.handleSessionCheckCallback(sessionCheckCallback);
    }

    private void deleteModelChannelConfig() {
        disableButtons();
        ConfirmHandler handler = new ConfirmHandler() {
            @Override
            public void confirmed(boolean confirm) {
                if (confirm) {
                    SessionCheckCallback sessionCheckCallback = new SessionCheckCallback() {
                        @Override
                        public void callback(SessionCheckResolution resolution) {
                            clientFactory.getMdcChannelRpc().deleteModelChannelConfig(
                                    modelChannelConfig,
                                    new ClientCallback<Void>() {
                                        @Override
                                        public void onSuccess(Void result) {
                                            clear();
                                            Dialogs.displayInformationMessage(MessagesUtil.getInstance().getMessage("channel.config.deleted"),
                                                    MediaResourceUtil.getInstance().getInformationIcon(),
                                                    MessagesUtil.getInstance().getMessage("button.close"), null);
                                            clear();
                                            loadChannelConfigs();
                                            enableButtons();
                                            sendNotification();
                                        }
                                        @Override
                                        public void onFailureClient() {
                                            enableButtons();
                                        }
                                    });
                        }
                    };
                    clientFactory.handleSessionCheckCallback(sessionCheckCallback);
                }
            }
        };
        Dialogs.confirm(MessagesUtil.getInstance().getMessage("channel.config.delete.confirm"),
                MessagesUtil.getInstance().getMessage("button.yes"),
                MessagesUtil.getInstance().getMessage("button.no"),
                MediaResourceUtil.getInstance().getQuestionIcon(),
                handler,
                deleteBtn.getAbsoluteLeft(), deleteBtn.getAbsoluteTop());
    }

    protected void disableButtons() {
        view.getForm().getSaveBtn().setEnabled(false);
        view.getForm().getOtherBtn().setEnabled(false);
        view.getForm().getBackBtn().setEnabled(false);
        deleteBtn.setEnabled(false);
    }


    protected void enableButtons() {
        view.getForm().getSaveBtn().setEnabled(true);
        view.getForm().getOtherBtn().setEnabled(true);
        view.getForm().getBackBtn().setEnabled(true);
        deleteBtn.setEnabled(true);
    }

    private void sendNotification() {
        //Notify any affected tabs
        clientFactory.getWorkspaceContainer().notifyWorkspaces(new WorkspaceNotification(NotificationType.DATA_UPDATED, MeterMngStatics.MODEL_CHANNEL_CONFIG_MODIFIED));
    }

    protected void repopulateMdcChannels() {
        if (meterModelDto != null && meterModelDto.getMdc() != null) {
            panel.populateMdcChannels(meterModelDto.getMdc().getId(), meterModelDto.getMeterModel().getId());
            loadChannelConfigs();
        }
    }
}
