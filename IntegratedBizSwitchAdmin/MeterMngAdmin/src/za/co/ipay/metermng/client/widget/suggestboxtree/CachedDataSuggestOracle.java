package za.co.ipay.metermng.client.widget.suggestboxtree;

import java.util.ArrayList;
import java.util.List;

import com.google.gwt.user.client.ui.SuggestOracle.Suggestion;

/**
 * A suggestion oracle that keeps a local cache of suggestions.
 * 
 * <AUTHOR>
 * @param <R> the type of id of the suggestion eg. long or a string 
 * @param <T> a sub type of Suggestion
 */
public abstract class CachedDataSuggestOracle<R, T extends Suggestion> extends IdentifiableSuggestOracle<R> {
    protected ArrayList<T> workingList = new ArrayList<T>();
    protected List<T> sourceData;
    
    public CachedDataSuggestOracle(List<T> sourceData) {
        super();
        this.sourceData = sourceData;
    }
    
    @Override
    public void requestDefaultSuggestions(Request request, Callback callback) {
        this.requestSuggestions(request, callback);
    }

    @Override
    public void requestSuggestions(final Request request, final Callback callback) {
        String q = request.getQuery() == null ? "" : request.getQuery().trim();
        workingList.clear();
        findData(sourceData, q, request.getLimit());
        Response response = new Response(workingList);
        callback.onSuggestionsReady(request, response);
    }

    private void findData(List<T> listToSearch, String query, int limit) {
        for (T item : listToSearch) {
            if (query == null || query.isEmpty() || item.getDisplayString().toLowerCase().startsWith(query.toLowerCase())) {
                workingList.add(item);
                if(workingList.size() >= limit) {
                    break;
                }
            }
        }
    }
    
}
