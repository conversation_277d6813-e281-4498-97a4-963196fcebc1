package za.co.ipay.metermng.client.resource.image;

import com.google.gwt.core.client.GWT;
import com.google.gwt.resources.client.ClientBundle;
import com.google.gwt.resources.client.ImageResource;

public interface MediaResource extends ClientBundle {
    
    public static class MediaResourceUtil {
        private static MediaResource instance;
        public static MediaResource getInstance() {
            if (instance == null) {
                instance = GWT.create(MediaResource.class);
            }
            return instance;
        }
        public static void setInstance(MediaResource imageResource) {
            instance = imageResource;
        }
    }

    @Source("web.png")
    public ImageResource getDashboardIcon();
    
    @Source("information-icon.png")
    public ImageResource getInformationIcon();
    
    @Source("error-icon.png")
    public ImageResource getErrorIcon();
    
    @Source("warning-icon.png")
    public ImageResource getWarningIcon();
    
    @Source("locked-icon.png")
    public ImageResource getLockedIcon();
    
    @Source("question-icon.png")
    public ImageResource getQuestionIcon();
    
    @Source("waiting.gif")
    public ImageResource getWaitIcon();
    
    @Source("navigation-right.png")
    public ImageResource getLogoutIcon();
    
    @Source("question.png")
    public ImageResource getMenuHelpIcon();
    
    @Source("empty.png")
    public ImageResource getEmptyIcon();
    
    @Source("user.png")
    public ImageResource getUserIcon();
    @Source("user-small.png")
    public ImageResource getUserSmallIcon();
    
    @Source("users-small.png")
    public ImageResource getUsersSmallIcon();
    @Source("users.png")
    public ImageResource getUsersIcon();
    
    @Source("users-cropped.png")
    public ImageResource getUsersCroppedIcon();
    
    @Source("key.png")
    public ImageResource getKeyIcon();
    
    @Source("navigation-right-button.png")
    public ImageResource getNavRightArrow();
    
    @Source("compass.png")
    public ImageResource getLocationIcon();
    
    @Source("gear.png")
    public ImageResource getGearImage();
    
    @Source("closedArrow.png")
    public ImageResource getClosedArrowImage();    
    
    @Source("openedArrow.png")
    public ImageResource getOpenedArrowImage();    
    
    @Source("dashboard-small.png")
    public ImageResource getDashboardSmallImage();
    
    @Source("dashboard.png")
    public ImageResource getDashboardImage();
    
    @Source("home.png")
    public ImageResource getHomeImage();  
    @Source("home-small.png")
    public ImageResource getHomeSmallImage(); 
    
    @Source("search.png")
    public ImageResource getSearchImage();
    
    @Source("tick.png")
    public ImageResource getTickImage();
    
    @Source("trans.png")
    public ImageResource getTransparentImage();
    
    @Source("pricing.png")
    public ImageResource getPricingImage();
    
    @Source("analytics.png")
    public ImageResource getAnalyticsImage();
    
    @Source("add.png")
    public ImageResource getAddImage();
    
    @Source("delete.png")    
    public ImageResource getDeleteImage();
    
    @Source("info.png")
    public ImageResource getInfoImage();
    
    @Source("synchronize.png")
    public ImageResource getRefreshImage();
    
    @Source("outlet.png")
    public ImageResource getOutletImage();
    
    @Source("store.png")
    public ImageResource getStoreImage();
    
    @Source("clock.png")
    public ImageResource getClockImage();
    
    @Source("ipay-logo.png")
    public ImageResource getIPayLogo();
}
