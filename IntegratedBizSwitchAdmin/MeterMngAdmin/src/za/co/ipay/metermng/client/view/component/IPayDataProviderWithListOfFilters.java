package za.co.ipay.metermng.client.view.component;

import java.util.ArrayList;
import java.util.List;

import com.google.gwt.view.client.HasData;
import com.google.gwt.view.client.ListDataProvider;

public class IPayDataProviderWithListOfFilters<T> extends ListDataProvider<T> {
    
    private List<String> listFilterStrings;  
    
    public final IpayDataProviderFilter<T> historyFilter;  
  
    public IPayDataProviderWithListOfFilters(IpayDataProviderFilter<T> filter) {  
        this.historyFilter = filter;  
    }  
  
    public void setFilter(List<String> listFilterStrings) {  
        this.listFilterStrings = listFilterStrings;  
        refresh();  
    }  
  
    public void resetFilter() {  
        listFilterStrings = null;  
        refresh();  
    }  
  
    public boolean hasFilter() {  
        return (listFilterStrings != null && !listFilterStrings.isEmpty());
    }  
  
    @Override  
    protected void updateRowData(HasData<T> display, int start, List<T> values) {
        int size = values.size();
        if (!hasFilter() || historyFilter == null) { // we don't need to filter, so call base class  
            display.setRowCount(size);
            super.updateRowData(display, start, values);  
        } else {  
            List<T> resulted = new ArrayList<T>();  
            
            for (int i = start; i < size; i++) {
                for (String filterString : listFilterStrings) {

                    if (historyFilter.isValid(values.get(i), filterString)) { 
                        if (!resulted.contains(values.get(i))) {
                            resulted.add(values.get(i));  
                        }
                    }  
                    
                }
            }  
            
            display.setRowData(start, resulted);  
            display.setRowCount(resulted.size());  
            
        }  
    }  
}  