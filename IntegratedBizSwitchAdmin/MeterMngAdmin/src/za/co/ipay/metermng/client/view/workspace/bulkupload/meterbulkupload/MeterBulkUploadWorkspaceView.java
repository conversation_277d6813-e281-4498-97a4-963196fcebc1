package za.co.ipay.metermng.client.view.workspace.bulkupload.meterbulkupload;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.logging.Logger;

import com.google.gwt.cell.client.AbstractCell;
import com.google.gwt.core.client.GWT;
import com.google.gwt.place.shared.Place;
import com.google.gwt.safehtml.shared.SafeHtmlBuilder;
import com.google.gwt.safehtml.shared.SafeHtmlUtils;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.user.cellview.client.CellTable;
import com.google.gwt.user.cellview.client.Column;
import com.google.gwt.user.cellview.client.TextColumn;
import com.google.gwt.user.client.Window;
import com.google.gwt.user.client.ui.DialogBox;
import com.google.gwt.user.client.ui.ScrollPanel;
import com.google.gwt.user.client.ui.Widget;
import com.google.gwt.view.client.ListDataProvider;

import za.co.ipay.gwt.common.client.Dialogs;
import za.co.ipay.gwt.common.client.factory.ResourcesFactoryUtil;
import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.history.MeterBulkUploadPlace;
import za.co.ipay.metermng.client.resource.image.MediaResource.MediaResourceUtil;
import za.co.ipay.metermng.client.rpc.callback.ClientCallback;
import za.co.ipay.metermng.client.view.component.bulkupload.FileUploadPanel;
import za.co.ipay.metermng.client.view.workspace.BaseWorkspace;
import za.co.ipay.metermng.client.view.workspace.bulkupload.ParentUpload;
import za.co.ipay.metermng.mybatis.generated.model.AppSetting;
import za.co.ipay.metermng.shared.MeterMngStatics;
import za.co.ipay.metermng.shared.bulkupload.dto.metercustupuploaddata.MeterBulkCsvData;
import za.co.ipay.metermng.shared.dto.uploaddata.metercustupuploaddata.MeterBulkCsvMapToData;

public class MeterBulkUploadWorkspaceView extends BaseWorkspace implements ParentUpload<MeterBulkCsvData> {
	
	@UiField(provided=true) FileUploadPanel<MeterBulkCsvData> fileUploadPanel;
    private List<AppSetting> customFieldsAppSettings = new ArrayList<>();
    
    private CellTable<MeterBulkCsvData> clltblTransactions;
    private Column<MeterBulkCsvData, String> errorColumn;
    private boolean isCsvDataValid;
    
    private CellTable<MeterBulkCsvData> selectedTable;
    private ListDataProvider<MeterBulkCsvData> selectedTableDataProvider;
    
    private static final int DEFAULT_PAGE_SIZE = 15;
    
    private static Logger logger = Logger.getLogger(MeterBulkUploadWorkspaceView.class.getName());

    private static MeterBulkUploadWorkspaceViewUiBinder uiBinder = GWT.create(MeterBulkUploadWorkspaceViewUiBinder.class);

    interface MeterBulkUploadWorkspaceViewUiBinder extends UiBinder<Widget, MeterBulkUploadWorkspaceView> {
    }

	public MeterBulkUploadWorkspaceView(ClientFactory clientFactory, MeterBulkUploadPlace place) {
        this.clientFactory = clientFactory;
        clientFactory.getAppSettingRpc().getAppSettingsForUPAndMeterAndCustomerCustomFields(new ClientCallback<ArrayList<AppSetting>>() {
            @Override
            public void onSuccess(ArrayList<AppSetting> result) {
                customFieldsAppSettings = result;
            }
        });
        initTable();
        createTable();
        fileUploadPanel = new FileUploadPanel<MeterBulkCsvData>(clientFactory, MeterMngStatics.METER_UPLOAD, this);
        initWidget(uiBinder.createAndBindUi(this));
        setPlaceString(MeterBulkUploadPlace.getPlaceAsString(place));
        setHeaderText(MessagesUtil.getInstance().getMessage("bulk.upload.meterupload.heading"));
        createSelectedTable();
    }
	
	private void initTable() {
        clltblTransactions = new CellTable<MeterBulkCsvData>(DEFAULT_PAGE_SIZE, ResourcesFactoryUtil.getInstance().getCellTableResources());
    }
	
	private void createTable() {
		
        AbstractCell<String> errorCell = new AbstractCell<String>() { 
            @Override
            public void render(Context context, String value, SafeHtmlBuilder sb) {
                if (value == null) { 
                    return; 
                } 
                sb.appendHtmlConstant("<span class=\"errorInlineNotBold\">" + value + "</span>");
            } 
        };
        errorColumn = new Column<MeterBulkCsvData, String>(errorCell) {
            @Override
            public String getValue(MeterBulkCsvData object) {
                return object.getErrors();
            }
        };
        TextColumn<MeterBulkCsvData> meterNumColumn = new TextColumn<MeterBulkCsvData>() {
            @Override
            public String getValue(MeterBulkCsvData object) {
                return object.getMeterNum();
            }
        };
        TextColumn<MeterBulkCsvData> meterModelNameColumn = new TextColumn<MeterBulkCsvData>() {
            @Override
            public String getValue(MeterBulkCsvData object) {
                return object.getMeterModelName();
            }
        };
        
        clltblTransactions.addColumn(errorColumn, SafeHtmlUtils.fromSafeConstant("<span class=\"error\">" + MessagesUtil.getInstance().getMessage("bulk.upload.errors") + "</span>"));
        clltblTransactions.addColumn(meterNumColumn, MessagesUtil.getInstance().getMessage("bulk.upload.meternum"));
        clltblTransactions.addColumn(meterModelNameColumn, MessagesUtil.getInstance().getMessage("bulk.upload.metermodelname"));
        
    }
	
	private void createSelectedTable() {
		
        TextColumn<MeterBulkCsvData> meterTypeCol = new TextColumn<MeterBulkCsvData>() {
            @Override
            public String getValue(MeterBulkCsvData object) {
                return object.getMeterType();
            }
        };
        TextColumn<MeterBulkCsvData> meterNumCol = new TextColumn<MeterBulkCsvData>() {
            @Override
            public String getValue(MeterBulkCsvData object) {
                return object.getMeterNum();
            }
        };
        TextColumn<MeterBulkCsvData> serialNumCol = new TextColumn<MeterBulkCsvData>() {
            @Override
            public String getValue(MeterBulkCsvData object) {
                return object.getSerialNum();
            }
        };
        TextColumn<MeterBulkCsvData> mridCol = new TextColumn<MeterBulkCsvData>() {
            @Override
            public String getValue(MeterBulkCsvData object) {
                return object.getMeterMrid();
            }
        };
        TextColumn<MeterBulkCsvData> meterModelNameCol = new TextColumn<MeterBulkCsvData>() {
            @Override
            public String getValue(MeterBulkCsvData object) {
                return object.getMeterModelName();
            }
        };
        TextColumn<MeterBulkCsvData> breakerIdCol = new TextColumn<MeterBulkCsvData>() {
            @Override
            public String getValue(MeterBulkCsvData object) {
                return object.getBreakerId();
            }
        };
        TextColumn<MeterBulkCsvData> endDeviceStoreNameCol = new TextColumn<MeterBulkCsvData>() {
            @Override
            public String getValue(MeterBulkCsvData object) {
                return object.getEndDeviceStoreName();
            }
        };
        TextColumn<MeterBulkCsvData> encKeyCol = new TextColumn<MeterBulkCsvData>() {
            @Override
            public String getValue(MeterBulkCsvData object) {
                return object.getEncKey();
            }
        };
        TextColumn<MeterBulkCsvData> stsTokenTechCodeCol = new TextColumn<MeterBulkCsvData>() {
            @Override  
            public String getValue(MeterBulkCsvData object) { 
                return object.getStsTokenTechCode();
            }
        };
        TextColumn<MeterBulkCsvData> stsAlgorithmCodeCol = new TextColumn<MeterBulkCsvData>() {
            @Override  
            public String getValue(MeterBulkCsvData object) { 
                return object.getStsAlgorithmCode();
            }
        };
        TextColumn<MeterBulkCsvData> stsSupplyGroupCodeCol = new TextColumn<MeterBulkCsvData>() {
            @Override  
            public String getValue(MeterBulkCsvData object) { 
                return object.getStsSupplyGroupCode();
            }
        };
        TextColumn<MeterBulkCsvData> stsKeyRevisionNumCol = new TextColumn<MeterBulkCsvData>() {
            @Override  
            public String getValue(MeterBulkCsvData object) { 
                return object.getStsKeyRevisionNum();
            }
        };
        TextColumn<MeterBulkCsvData> stsTariffIndexCol = new TextColumn<MeterBulkCsvData>() {
            @Override  
            public String getValue(MeterBulkCsvData object) { 
                return object.getStsTariffIndex();
            }
        };
        
        selectedTable = new CellTable<MeterBulkCsvData>();
        selectedTable.addColumn(meterTypeCol, MessagesUtil.getInstance().getMessage("bulk.upload.metertype"));
        selectedTable.addColumn(meterNumCol, MessagesUtil.getInstance().getMessage("bulk.upload.meternum"));
        selectedTable.addColumn(serialNumCol, MessagesUtil.getInstance().getMessage("bulk.upload.serialnum"));
        selectedTable.addColumn(mridCol, MessagesUtil.getInstance().getMessage("bulk.upload.mrid"));
        selectedTable.addColumn(meterModelNameCol, MessagesUtil.getInstance().getMessage("bulk.upload.metermodelname"));
        selectedTable.addColumn(breakerIdCol, MessagesUtil.getInstance().getMessage("bulk.upload.breakerid"));
        selectedTable.addColumn(endDeviceStoreNameCol, MessagesUtil.getInstance().getMessage("bulk.upload.meterupload.enddevicestorename"));

        selectedTable.addColumn(encKeyCol, MessagesUtil.getInstance().getMessage("bulk.upload.enc.key"));
        selectedTable.addColumn(stsTokenTechCodeCol, MessagesUtil.getInstance().getMessage("bulk.upload.ststokentechcode"));
        selectedTable.addColumn(stsAlgorithmCodeCol, MessagesUtil.getInstance().getMessage("bulk.upload.stsalgorithmcode"));
        selectedTable.addColumn(stsSupplyGroupCodeCol, MessagesUtil.getInstance().getMessage("bulk.upload.stssupplygroupcode"));
        selectedTable.addColumn(stsKeyRevisionNumCol, MessagesUtil.getInstance().getMessage("bulk.upload.stskeyrevisionnum"));
        selectedTable.addColumn(stsTariffIndexCol, MessagesUtil.getInstance().getMessage("bulk.upload.ststariffindex"));
       
        selectedTableDataProvider = new ListDataProvider<MeterBulkCsvData>() ; 
        selectedTableDataProvider.addDataDisplay(selectedTable);
    }

	// START : ParentUpload methods
	@Override
	public String getPageHeaderKey() {
		return "bulk.upload.meterupload.heading";
	}

	@Override
	public String getDataTitleKey() {
		return "bulk.upload.meterupload.data.title";
	}

	@Override
	public String getUploadDescriptionKey() {
		return "bulk.upload.meterupload.description";
	}

	@Override
	public String getUrlHandlerMapping() {
		return "secure/meterbulkupload.do";
	}

	@Override
	public CellTable<MeterBulkCsvData> getTable() {
		return clltblTransactions;
	}
	
	@Override
	public Column<MeterBulkCsvData, String> getErrorColumn() {
		return errorColumn;
	}
	
	@Override
	public boolean isCsvDataValid() {
		return isCsvDataValid;
	}

	@Override
	public void displaySelected(MeterBulkCsvData selected, int left, int top) {

        selectedTableDataProvider.getList().clear();
        selectedTableDataProvider.getList().add(selected);
        selectedTableDataProvider.refresh();
        
        //Throw out a popup with a limited width and horizontal scrollbar
        DialogBox simplePopup = new DialogBox(true);
        simplePopup.setText(MessagesUtil.getInstance().getMessage(""));
        simplePopup.setAnimationEnabled(true);
        ScrollPanel scrollPanel = new ScrollPanel(selectedTable);
        int popupwidth = Window.getClientWidth() - clientFactory.getPrimaryLayoutView().getSidePanelStackWidth() - 100;
        scrollPanel.setWidth(popupwidth + "px");
        simplePopup.setWidget(scrollPanel);
        simplePopup.setPopupPosition(left, top);
        simplePopup.show();
	}

	@Override
	public List<MeterBulkCsvData> getTransCsvList(String result) {
        String customFieldStatusUnavailable = MessagesUtil.getInstance().getMessage("user.custom.field.status.unavailable");
		MeterBulkCsvMapToData csvMapData = new MeterBulkCsvMapToData(customFieldsAppSettings, customFieldStatusUnavailable !=null ? customFieldStatusUnavailable: "");
        HashMap<Integer, String> csvFieldMap = new HashMap<Integer, String>();
        
        isCsvDataValid = true;
        
        List<MeterBulkCsvData> transCsvDataList = new ArrayList<MeterBulkCsvData>();
        String[] transStringArray = result.split("\r\n|[\r\n]");   //System.lineSeparator());
        for (String trans : transStringArray) {
            if (trans != null && !trans.isEmpty()){
                if (trans.contains("Info: Required")) {
                    continue; 
                }
                if (trans.contains("Meter Num") && csvFieldMap.isEmpty()) {
                    //for headerLine construct the index to DataName Map
                    try {
                        csvFieldMap = csvMapData.constructCsvFieldMap(trans);
                        continue;
                    } catch (Exception e) {
                        String[] paramArr = new String[] {e.getMessage().substring(e.getMessage().indexOf(':') +1)}; //Strips out "Unknown Column Heading:", leaves Heading Name
                        Dialogs.centreErrorMessage(
                                MessagesUtil.getInstance().getMessage("bulk.upload.file.unrecognized.heading.error", paramArr), 
                                MediaResourceUtil.getInstance().getErrorIcon(), 
                                MessagesUtil.getInstance().getMessage("button.close"));
                    }
                }
                
                MeterBulkCsvData custTrans = null;
                try {
                    custTrans = new MeterBulkCsvData(csvFieldMap, trans, true);
                } catch (Exception e) {
                    logger.info("METERUPLOAD ERROR CREATING MeterBulkCsvData!! Exception= " + e.getMessage());
                    Dialogs.centreErrorMessage(MessagesUtil.getInstance().getMessage("bulk.upload.object.creation.error", new String[] {"Meter"}), MediaResourceUtil.getInstance().getErrorIcon(), MessagesUtil.getInstance().getMessage("button.close"));
                }
                if (custTrans == null || !custTrans.getErrors().isEmpty()) {
                    isCsvDataValid = false;
                }
                transCsvDataList.add(custTrans);
            }
        }
        return transCsvDataList;
	}
	// END : ParentUpload methods
	
	// START : Workspace methods
	@Override
	public void onLeaving() {
	}

	@Override
	public void onSelect() {
	}

	@Override
	public void onArrival(Place place) {
	}

	@Override
	public void onClose() {
	}

	@Override
	public boolean handles(Place place) {
		return (place instanceof MeterBulkUploadPlace);
	}
	// END : Workspace methods
}
