package za.co.ipay.metermng.client.view.workspace.meter.mdc;

import java.util.ArrayList;
import java.util.List;
import java.util.logging.Logger;

import za.co.ipay.gwt.common.client.Dialogs;
import za.co.ipay.gwt.common.client.factory.ResourcesFactoryUtil;
import za.co.ipay.gwt.common.client.form.BigDecimalValueBox;
import za.co.ipay.gwt.common.client.form.FormElement;
import za.co.ipay.gwt.common.client.form.SimpleForm;
import za.co.ipay.gwt.common.client.handler.ConfirmHandler;
import za.co.ipay.gwt.common.client.handler.FormDataChangeHandler;
import za.co.ipay.gwt.common.client.handler.FormDataClickHandler;
import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.form.SimpleFormPanel;
import za.co.ipay.metermng.client.rpc.callback.ClientCallback;
import za.co.ipay.metermng.mybatis.generated.model.BillingDet;
import za.co.ipay.metermng.mybatis.generated.model.MeterReadingType;
import za.co.ipay.metermng.mybatis.generated.model.TimeInterval;

import com.google.gwt.core.client.GWT;
import com.google.gwt.event.dom.client.ChangeEvent;
import com.google.gwt.event.dom.client.ChangeHandler;
import com.google.gwt.event.dom.client.ClickEvent;
import com.google.gwt.event.dom.client.ClickHandler;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.user.client.ui.CheckBox;
import com.google.gwt.user.client.ui.ListBox;
import com.google.gwt.user.client.ui.TextArea;
import com.google.gwt.user.client.ui.TextBox;
import com.google.gwt.user.client.ui.Widget;

public class MdcChannelPanel extends SimpleFormPanel {

    @UiField FormElement channelValueElement;
    @UiField FormElement nameElement;
    @UiField FormElement descripElement;
    @UiField FormElement billingDetElement;
    @UiField FormElement meterReadingTypeElement;
    @UiField FormElement timeIntervalElement;
    @UiField FormElement maxSizeElement;
    @UiField FormElement readingMultiplierElement;
    @UiField FormElement activeElement;

    @UiField TextBox channelValueBox;
    @UiField TextBox nameBox;
    @UiField TextBox descripBox;
    @UiField ListBox billingDetListBox;
    @UiField ListBox meterReadingTypeListBox;
    @UiField ListBox timeIntervalListBox;
    @UiField BigDecimalValueBox maxSizeBox;
    @UiField BigDecimalValueBox readingMultiplierBox;
    @UiField CheckBox activeBox;
    @UiField TextArea overrideMeterModels;
    
    private ClientFactory clientFactory;
    
    private static Logger logger = Logger.getLogger(MdcChannelPanel.class.getName());

    private static MdcChannelPanelUiBinder uiBinder = GWT.create(MdcChannelPanelUiBinder.class);

    interface MdcChannelPanelUiBinder extends UiBinder<Widget, MdcChannelPanel> {
    }

    public MdcChannelPanel(ClientFactory clientFactory, SimpleForm form) {
        super(form);
        this.clientFactory = clientFactory;
        initWidget(uiBinder.createAndBindUi(this));       
        
        populateBillingDetListBox();
        populateMeterReadingTypes();
        populateTimeIntervals();
        
        addFieldHandlers();
    }
    
    public void clearFields() {
        form.setDirtyData(false);
        channelValueBox.setText("");
        nameBox.setText("");
        descripBox.setText("");
        billingDetListBox.setSelectedIndex(0);
        meterReadingTypeListBox.setSelectedIndex(0);
        timeIntervalListBox.setSelectedIndex(0);
        maxSizeBox.setText("");
        readingMultiplierBox.setText("");
        activeBox.setValue(true);
        overrideMeterModels.setText("");
    }

    public void clearErrors() {
        channelValueElement.clearErrorMsg();
        nameElement.clearErrorMsg();
        descripElement.clearErrorMsg();
        billingDetElement.clearErrorMsg();
        meterReadingTypeElement.clearErrorMsg();
        timeIntervalElement.clearErrorMsg();
        maxSizeElement.clearErrorMsg();
        readingMultiplierElement.clearErrorMsg();
        activeElement.clearErrorMsg();
    }
    
    protected void populateBillingDetListBox() {
        clientFactory.getBillingDetRpc().getActiveBillingDets(new ClientCallback<List<BillingDet>>() {
            @Override
            public void onSuccess(List<BillingDet> result) {
                initBillingDet(result);
            }
        });
    }
    
    private void initBillingDet(List<BillingDet> billingDetList){
        billingDetListBox.clear();
        billingDetListBox.addItem("", "");
        
        for (BillingDet bd : billingDetList) {
            billingDetListBox.addItem(bd.getName(), bd.getId().toString());
        }
        
        if (billingDetList.size() < 4) { 
            billingDetListBox.setVisibleItemCount(billingDetList.size() + 1);
        }
    }
    
    protected void populateMeterReadingTypes() {
        clientFactory.getMeterRpc().getMeterReadingTypes(new ClientCallback<ArrayList<MeterReadingType>>() {
            @Override
            public void onSuccess(ArrayList<MeterReadingType> result) {
                initMeterReadingTypes(result);
            }
        });
    } 
    
    private void initMeterReadingTypes(ArrayList<MeterReadingType> readingTypeList){
        meterReadingTypeListBox.clear();
        meterReadingTypeListBox.addItem("", "");
        
        for (MeterReadingType mrt : readingTypeList) {
            meterReadingTypeListBox.addItem(mrt.getName(), mrt.getId().toString());
        }
    }
    
    protected void populateTimeIntervals() {
        clientFactory.getMdcChannelRpc().getTimeIntervals(new ClientCallback<List<TimeInterval>>() {
            @Override
            public void onSuccess(List<TimeInterval> result) {
                initTimeInterval(result);
            }
        });
    }
    
    private void initTimeInterval(List<TimeInterval> timeIntervalList){
        timeIntervalListBox.clear();
        timeIntervalListBox.addItem("", "");
        
        for (TimeInterval ti : timeIntervalList) {
            timeIntervalListBox.addItem(ti.getTimeIntervalName(), ti.getId().toString());
        }
    }    

    @Override
    public void addFieldHandlers() {
        channelValueBox.addChangeHandler(new FormDataChangeHandler(form));
        nameBox.addChangeHandler(new FormDataChangeHandler(form));
        descripBox.addChangeHandler(new FormDataChangeHandler(form));
        billingDetListBox.addChangeHandler(new FormDataChangeHandler(form));
        meterReadingTypeListBox.addChangeHandler(new FormDataChangeHandler(form));
        timeIntervalListBox.addChangeHandler(new FormDataChangeHandler(form));
        maxSizeBox.addChangeHandler(new FormDataChangeHandler(form));
        readingMultiplierBox.addChangeHandler(new FormDataChangeHandler(form));
        //activeBox.addClickHandler(new FormDataClickHandler(form));
        activeBox.addClickHandler(new ClickHandler() {
            @Override
            public void onClick(ClickEvent arg0) {
                form.setDirtyData(true);
                
                //store what it has been clicked to, in case want to reset
                final boolean isActiveNow = activeBox.getValue();
                if (!overrideMeterModels.getText().isEmpty() 
                        && !overrideMeterModels.getText().equals(MessagesUtil.getInstance().getMessage("mdc.channel.override.metermodels.none"))) {
                    Dialogs.confirm(
                            ResourcesFactoryUtil.getInstance().getMessages().getMessage("mdc.channel.status.change.warn.overrides"),
                            ResourcesFactoryUtil.getInstance().getMessages().getMessage("option.positive"),
                            ResourcesFactoryUtil.getInstance().getMessages().getMessage("option.negative"),
                            ResourcesFactoryUtil.getInstance().getQuestionIcon(), 
                            new ConfirmHandler() {
                                @Override
                                public void confirmed(boolean confirm) {
                                    if (!confirm) {
                                        activeBox.setValue(!isActiveNow);
                                        return;
                                    }
                                }
                            }
                    ); 
                }
            }
        });
    }
}
