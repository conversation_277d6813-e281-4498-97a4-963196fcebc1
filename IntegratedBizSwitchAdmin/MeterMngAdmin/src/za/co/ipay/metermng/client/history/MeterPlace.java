package za.co.ipay.metermng.client.history;

import za.co.ipay.metermng.shared.MeterMngStatics;

import com.google.gwt.place.shared.PlaceTokenizer;
import com.google.gwt.place.shared.Prefix;

public class MeterPlace extends AbstractUsagePointPlace {

    public static final MeterPlace NEW_METER_PLACE = new MeterPlace("new");
    
    private String meterNumber;
    private boolean isFromURL = true;

    public MeterPlace(String meterNumber) {
        this.meterNumber = meterNumber;
    }
    
    public MeterPlace(String meterNumber, String previousHistoryToken) {
        this.meterNumber = meterNumber;
        this.previousHistoryToken = previousHistoryToken;
    }

    public MeterPlace(String meterNumber, boolean isFromURL) {
        super();
        this.meterNumber = meterNumber;
        this.isFromURL = isFromURL;
    }

    @Override
    public boolean isFromURL() {
        return isFromURL;
    }

    public String getMeterNumber() {
        return meterNumber;
    }

    @Override
    public boolean isNew() {
        return meterNumber.equalsIgnoreCase("new");
    }
    
    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((meterNumber == null) ? 0 : meterNumber.hashCode());
        return result;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        MeterPlace other = (MeterPlace) obj;
        if (meterNumber == null) {
            if (other.meterNumber != null)
                return false;
        } else if (!meterNumber.equals(other.meterNumber))
            return false;
        return true;
    }

    @Prefix(value = "meter")
    public static class Tokenizer implements PlaceTokenizer<MeterPlace> {
        @Override
        public String getToken(MeterPlace place) {
            if (place.getPreviousHistoryToken() != null && !place.getPreviousHistoryToken().equals("")) {
                return place.getMeterNumber() + MeterMngStatics.PLACE_TOKEN_SEPARATOR + place.getPreviousHistoryToken();
            } else {
                return place.getMeterNumber();
            }
        }

        @Override
        public MeterPlace getPlace(String token) {
            if (token != null && token.length() > 0) {
                int index = token.indexOf(MeterMngStatics.PLACE_TOKEN_SEPARATOR);
                if (index > -1) {
                    return new MeterPlace(token.substring(0, index), token.substring(index+1));
                }
            }
            return new MeterPlace(token);
        }
    }
}
