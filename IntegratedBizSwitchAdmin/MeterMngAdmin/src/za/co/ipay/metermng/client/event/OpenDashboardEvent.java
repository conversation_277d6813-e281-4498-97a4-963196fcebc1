package za.co.ipay.metermng.client.event;

import com.google.gwt.event.shared.GwtEvent;

public class OpenDashboardEvent extends GwtEvent<OpenDashboardEventHandler> {

    public static Type<OpenDashboardEventHandler> TYPE = new Type<OpenDashboardEventHandler>();
    
    @Override
    public Type<OpenDashboardEventHandler> getAssociatedType() {
        return TYPE;
    }

    @Override
    protected void dispatch(OpenDashboardEventHandler handler) {
        handler.openDashboard(this);
    }

}