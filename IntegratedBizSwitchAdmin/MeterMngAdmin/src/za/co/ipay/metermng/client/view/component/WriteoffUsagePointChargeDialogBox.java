package za.co.ipay.metermng.client.view.component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;

import com.google.gwt.core.client.GWT;
import com.google.gwt.core.client.Scheduler;
import com.google.gwt.core.client.Scheduler.ScheduledCommand;
import com.google.gwt.event.dom.client.ClickEvent;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiFactory;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.uibinder.client.UiHandler;
import com.google.gwt.user.cellview.client.CellTable;
import com.google.gwt.user.cellview.client.TextColumn;
import com.google.gwt.user.client.Window;
import com.google.gwt.user.client.ui.Button;
import com.google.gwt.user.client.ui.DialogBox;
import com.google.gwt.user.client.ui.HTML;
import com.google.gwt.user.client.ui.HorizontalPanel;
import com.google.gwt.user.client.ui.Label;
import com.google.gwt.user.client.ui.ScrollPanel;
import com.google.gwt.user.client.ui.VerticalPanel;
import com.google.gwt.user.client.ui.Widget;
import com.google.gwt.view.client.ListDataProvider;

import za.co.ipay.gwt.common.client.form.Format.FormatUtil;
import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.gwt.common.client.workspace.SessionCheckCallback;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.i18n.UiMessages;
import za.co.ipay.metermng.client.i18n.UiMessagesUtil;
import za.co.ipay.metermng.client.rpc.callback.ClientCallback;
import za.co.ipay.metermng.client.view.workspace.UsagePointWorkspaceView;
import za.co.ipay.metermng.mybatis.generated.model.SpecialActionReasonsLog;
import za.co.ipay.metermng.shared.CustomerTransItemData;
import za.co.ipay.metermng.shared.CustomerTransItemOutstandCharges;
import za.co.ipay.metermng.shared.MeterMngStatics;
import za.co.ipay.metermng.shared.SpecialActionsData;

public class WriteoffUsagePointChargeDialogBox extends DialogBox {

    @UiField VerticalPanel vendTimeChargesPanel;
    @UiField HTML lastVendCyclicChargeDate;
    @UiField ScrollPanel scrollPanel;
    @UiField(provided = true)  CellTable<CustomerTransItemData> clltbltransitems;
    @UiField Label vendTimeTotalLbl; // default wording for vend only charges, change if have both
    @UiField HTML vendTotalAmount;

    @UiField VerticalPanel billingTimeChargesPanel; //default not visible
    @UiField HTML lastBillingCyclicChargeDate;
    @UiField ScrollPanel scrollPanelBilling;
    @UiField(provided = true)  CellTable<CustomerTransItemData> clltbltransitemsBilling;
    @UiField HTML billingTotalAmount;

    @UiField HorizontalPanel totalChargesPanel; //default not visible, only displayed when have both
    @UiField HTML totalChargesAmount;

    @UiField HorizontalPanel completedMessage;
    @UiField Button writeoffButton;
    @UiField Button cancelButton;
    @UiField Button closeButton;

    @UiField HorizontalPanel writeoffChargeReasonPanel;

    private SpecialActionsReasonComponent writeoffChargeReasonComponent;

    private ListDataProvider<CustomerTransItemData> dataProviderVendTbl;
    private ListDataProvider<CustomerTransItemData> dataProviderBillingTbl;
    private BigDecimal vendTot = BigDecimal.ZERO;
    private BigDecimal billingTot = BigDecimal.ZERO;
    boolean haveVendCharges = true;
    boolean haveBillingCharges = false;

    private ClientFactory clientFactory;
    private Long usagePointId;
    private CustomerTransItemOutstandCharges outstandCharges;
    private ArrayList<CustomerTransItemData> vendOutstCustomerTransItemList;
    private ArrayList<CustomerTransItemData> billingOutstCustomerTransItemList;
    private Date upNewCyclicChargeDate;
    private Date upLastCyclicChargeDate;
    private Date upLastBillingCyclicChargeDate;
    private UsagePointWorkspaceView usagePointWorkspaceView;

    private static WriteoffUsagePointChargeDialogBoxUiBinder uiBinder = GWT.create(WriteoffUsagePointChargeDialogBoxUiBinder.class);

    interface WriteoffUsagePointChargeDialogBoxUiBinder extends UiBinder<Widget, WriteoffUsagePointChargeDialogBox> {
    }

    public WriteoffUsagePointChargeDialogBox(ClientFactory clientFactory, Long usagePointId,
            Date upLastCyclicChargeDate, Date upLastBillingCyclicChargeDate, Date upNewCyclicChargeDate,
            CustomerTransItemOutstandCharges outstandCharges,
            UsagePointWorkspaceView usagePointWorkspaceView) {
        super();
        this.setGlassEnabled(true);
        this.setModal(true);
        this.setAutoHideEnabled(false);

        this.usagePointId = usagePointId;
        this.clientFactory = clientFactory;

        this.outstandCharges = outstandCharges;
        this.vendOutstCustomerTransItemList = (ArrayList<CustomerTransItemData>) outstandCharges.getVendCustTransItemOutStand();
        this.billingOutstCustomerTransItemList = (ArrayList<CustomerTransItemData>) outstandCharges.getBillingCustTransItemOutStand();
        this.upNewCyclicChargeDate = upNewCyclicChargeDate;
        this.upLastCyclicChargeDate = upLastCyclicChargeDate;
        this.upLastBillingCyclicChargeDate = upLastBillingCyclicChargeDate;
        this.usagePointWorkspaceView = usagePointWorkspaceView;

        clltbltransitems = new CellTable<CustomerTransItemData>();
        clltbltransitemsBilling = new CellTable<CustomerTransItemData>();
        setWidget(uiBinder.createAndBindUi(this));
        this.ensureDebugId("writeoffUsagePointChargeDialogBox");

        verifyPermissions();

        if (!vendOutstCustomerTransItemList.isEmpty()) {
            setupVendChargesTable();
        } else {
            vendTimeChargesPanel.removeFromParent();
            haveVendCharges = false;
        }

        if (!billingOutstCustomerTransItemList.isEmpty()) {
            setupBillingChargesTable(haveVendCharges);
            haveBillingCharges = true;
        } else {
            billingTimeChargesPanel.removeFromParent();
            totalChargesPanel.removeFromParent();
        }

        if (usagePointWorkspaceView != null) {
            writeoffButton.setText(
                    MessagesUtil.getInstance().getMessage("usagepoint.charge.button.writeoff.and.unassign.customer"));
            closeButton.setText(MessagesUtil.getInstance().getMessage("usagepoint.charge.button.close.writeoff.and.unassign.customer"));
        }

        //for initial layout must checkHeight as well - but only makes sense after Dom created, so defer
        Scheduler.get().scheduleDeferred(new ScheduledCommand() {
            @Override
            public void execute() {
              checkHeight();
            }
          });

        setupSpecialReasons();
    }

    private void setupVendChargesTable() {
        lastVendCyclicChargeDate.setText(FormatUtil.getInstance().formatDateTime(upLastCyclicChargeDate));
        vendTot = calculateTotal(vendOutstCustomerTransItemList);
        vendTotalAmount.setText(FormatUtil.getInstance().formatCurrency(vendTot, true));
        initdataProviderVendTbl();
        setupVendTable();
    }

    private void setupBillingChargesTable(boolean haveVendCharges) {
        billingTimeChargesPanel.setVisible(true);
        lastBillingCyclicChargeDate.setText(FormatUtil.getInstance().formatDateTime(upLastBillingCyclicChargeDate));
        billingTot = calculateTotal(billingOutstCustomerTransItemList);
        billingTotalAmount.setText(FormatUtil.getInstance().formatCurrency(billingTot, true));
        initdataProviderBillingTbl();
        setupBillingTable();

        if (haveVendCharges) {
            //then have both charges, so display grand total
            totalChargesPanel.setVisible(true);
            BigDecimal total = vendTot.add(billingTot);
            totalChargesAmount.setText(FormatUtil.getInstance().formatCurrency(total, true));
        }
    }

    private void setupSpecialReasons() {
        writeoffChargeReasonComponent = new SpecialActionsReasonComponent(clientFactory, null,
                SpecialActionsData.CHARGE_WRITEOFF,
                MessagesUtil.getInstance().getMessage("usagepoint.charge.writeoff.enter.reason"),
                MessagesUtil.getInstance().getMessage("usagepoint.charge.writeoff.select.reason"));
        writeoffChargeReasonPanel.add(writeoffChargeReasonComponent);
    }

    private void verifyPermissions() {
        //User must have proper perms to do a writeoff
        if (!clientFactory.getUser().hasPermission(MeterMngStatics.ACCESS_PERMISSION_MM_OUTSTANDING_CHARGE_WRITEOFF)) {
            writeoffButton.removeFromParent();
        }
    }

    private BigDecimal calculateTotal(ArrayList<CustomerTransItemData> customerTransItemList) {
        BigDecimal totalAmount = new BigDecimal(0);
        for (CustomerTransItemData ctid : customerTransItemList) {
            totalAmount = totalAmount.add(ctid.getAmtInclTax());
        }
        return totalAmount;
    }

    private TextColumn<CustomerTransItemData> makeTranstypeCol() {
        TextColumn<CustomerTransItemData> transtype = new TextColumn<CustomerTransItemData>() {
            @Override
            public String getValue(CustomerTransItemData data) {
                return (data.getTransItemTypeDetails() != null ? data.getTransItemTypeDetails().getName() : null);
            }
        };
        return transtype;
    }

    private TextColumn<CustomerTransItemData> makeDescripCol() {
        TextColumn<CustomerTransItemData> description = new TextColumn<CustomerTransItemData>() {
            @Override
            public String getValue(CustomerTransItemData data) {
                return data.getDescription();
            }
        };
        return description;
    }

    private TextColumn<CustomerTransItemData> makeTokenCol() {
        TextColumn<CustomerTransItemData> tokenCol = new TextColumn<CustomerTransItemData>() {
            @Override
            public String getValue(CustomerTransItemData data) {
                return data.getToken();
            }
        };
        return tokenCol;
    }

    private TextColumn<CustomerTransItemData> makeAmtCol() {
        TextColumn<CustomerTransItemData> amtCol = new TextColumn<CustomerTransItemData>() {
            @Override
            public String getValue(CustomerTransItemData data) {
                return FormatUtil.getInstance().formatCurrency(data.getAmtInclTax(), true);
            }
        };
        return amtCol;
    }

    private TextColumn<CustomerTransItemData> makeTaxCol() {
        TextColumn<CustomerTransItemData> taxCol = new TextColumn<CustomerTransItemData>() {
            @Override
            public String getValue(CustomerTransItemData data) {
                return FormatUtil.getInstance().formatCurrency(data.getAmtTax(), true);
            }
        };
        return taxCol;
    }

    private TextColumn<CustomerTransItemData> makeTariffCol() {
        TextColumn<CustomerTransItemData> tariff = new TextColumn<CustomerTransItemData>() {
            @Override
            public String getValue(CustomerTransItemData data) {
                return data.getTariff();
            }
        };
        return tariff;
    }

    protected void setupVendTable() {
        clltbltransitems.addColumn(makeTranstypeCol(), MessagesUtil.getInstance().getMessage("meter.txn.type"));
        clltbltransitems.addColumn(makeDescripCol(), MessagesUtil.getInstance().getMessage("meter.txn.description"));
        clltbltransitems.addColumn(makeTokenCol(), MessagesUtil.getInstance().getMessage("meter.txn.token"));
        clltbltransitems.addColumn(makeAmtCol(), MessagesUtil.getInstance().getMessage("meter.txn.amount"));
        clltbltransitems.addColumn(makeTaxCol(), MessagesUtil.getInstance().getMessage("meter.txn.tax"));
        clltbltransitems.addColumn(makeTariffCol(), MessagesUtil.getInstance().getMessage("meter.txn.tariff"));

        dataProviderVendTbl.addDataDisplay(clltbltransitems);
    }

    protected void setupBillingTable() {
        clltbltransitemsBilling.addColumn(makeTranstypeCol(), MessagesUtil.getInstance().getMessage("meter.txn.type"));
        clltbltransitemsBilling.addColumn(makeDescripCol(), MessagesUtil.getInstance().getMessage("meter.txn.description"));
        clltbltransitemsBilling.addColumn(makeTokenCol(), MessagesUtil.getInstance().getMessage("meter.txn.token"));
        clltbltransitemsBilling.addColumn(makeAmtCol(), MessagesUtil.getInstance().getMessage("meter.txn.amount"));
        clltbltransitemsBilling.addColumn(makeTaxCol(), MessagesUtil.getInstance().getMessage("meter.txn.tax"));
        clltbltransitemsBilling.addColumn(makeTariffCol(), MessagesUtil.getInstance().getMessage("meter.txn.tariff"));

        dataProviderBillingTbl.addDataDisplay(clltbltransitemsBilling);
    }

    private void initdataProviderVendTbl() {
        dataProviderVendTbl = new ListDataProvider<CustomerTransItemData>();
        dataProviderVendTbl.getList().clear();
        dataProviderVendTbl.setList(vendOutstCustomerTransItemList);
        clltbltransitems.setRowCount(dataProviderVendTbl.getList().size(), true);
        dataProviderVendTbl.refresh();
    }

    private void initdataProviderBillingTbl() {
        dataProviderBillingTbl = new ListDataProvider<CustomerTransItemData>();
        dataProviderBillingTbl.getList().clear();
        dataProviderBillingTbl.setList(billingOutstCustomerTransItemList);
        clltbltransitemsBilling.setRowCount(dataProviderBillingTbl.getList().size(), true);
        dataProviderBillingTbl.refresh();
    }

    private void checkHeight() {
        //calc pagesize for scrollpanel/s
        int pageHeight = Window.getClientHeight();
        int heightAvail = pageHeight - 320;
        if (haveVendCharges && haveBillingCharges) {
            heightAvail = heightAvail / 2;
        }
        if (haveVendCharges) {
            if (clltbltransitems.getOffsetHeight() >  heightAvail) {
                scrollPanel.setHeight(heightAvail + "px");
            }
            scrollPanel.scrollToBottom();
            scrollPanel.setVerticalScrollPosition(clltbltransitems.getOffsetHeight());
        }

        if (haveBillingCharges) {
            if (clltbltransitemsBilling.getOffsetHeight() >  heightAvail) {
                scrollPanelBilling.setHeight(heightAvail + "px");
            }
            scrollPanelBilling.scrollToBottom();
            scrollPanelBilling.setVerticalScrollPosition(clltbltransitems.getOffsetHeight());
        }
    }

    @UiHandler("writeoffButton")
    void handleWriteoffButton(ClickEvent event) {
        writeoffButton.setEnabled(false);
        cancelButton.setEnabled(false);
        final String currentUser = clientFactory.getUser().getUserName();
        SpecialActionReasonsLog logEntry = null;
        if (writeoffChargeReasonComponent.isAttached() && writeoffChargeReasonComponent.isVisible()) {
            if(!writeoffChargeReasonComponent.validate()) { // 'Required' is set to true but no input given
                writeoffButton.setEnabled(true);
                cancelButton.setEnabled(true);
                return;
            }
            // Be careful changing getLogEntry() to null or false - no check for null in serviceImpl
            logEntry = writeoffChargeReasonComponent.getLogEntry(true);
        }
        final SpecialActionReasonsLog finalLogEntry = logEntry;
        SessionCheckCallback sessionCheckCallback = new SessionCheckCallback() {
            @Override
            public void callback(SessionCheckResolution sessionCheckResolution) {
                clientFactory.getUsagePointRpc().writeoffOutstandingCyclicCharges(usagePointId, upLastCyclicChargeDate,
                        upLastBillingCyclicChargeDate, upNewCyclicChargeDate, currentUser, outstandCharges, finalLogEntry,
                        new ClientCallback<Void>() {
                            @Override
                            public void onSuccess(Void result) {
                                completedMessage.setVisible(true);
                                closeButton.setVisible(true);
                                writeoffButton.removeFromParent();
                                cancelButton.removeFromParent();
                            }
                            @Override
                            public void onFailure(Throwable caught) {
                                writeoffButton.setEnabled(true);
                                cancelButton.setEnabled(true);
                                super.onFailure(caught);
                            }
                        });
            }
        };
        clientFactory.handleSessionCheckCallback(sessionCheckCallback);
    }

    @UiHandler("cancelButton")
    void handleCancelButton(ClickEvent event) {
        this.hide();
        if (usagePointWorkspaceView != null) {
            usagePointWorkspaceView.getCustomerComponent().handleAssignLink(null);
        }
    }

    @UiHandler("closeButton")
    void handleCloseButton(ClickEvent event) {
        if (usagePointWorkspaceView != null) {
            SessionCheckCallback sessionCheckCallback = new SessionCheckCallback() {
                @Override
                public void callback(SessionCheckResolution sessionCheckResolution) {
                    usagePointWorkspaceView.unassignCustomerFromUsagePoint();
                }
            };
            clientFactory.handleSessionCheckCallback(sessionCheckCallback);
        }
        this.hide();
    }

    @UiFactory
    public UiMessages getUiMessages() {
        return UiMessagesUtil.getInstance();
    }

}
