package za.co.ipay.metermng.client.view.workspace.bulkupload;

import java.util.List;

import com.google.gwt.user.cellview.client.CellTable;
import com.google.gwt.user.cellview.client.Column;

public interface ParentUpload<T> {

    String getPageHeaderKey();
    String getDataTitleKey();
    
    String getUploadDescriptionKey();
    
    
    String getUrlHandlerMapping();
    CellTable<T> getTable();

    /*
     * If many fields that don't fit in the width, like Meter_Customer-UsagePoint upload --> can here create a popup with scrollpanel showing the whole row
     */
    void displaySelected(T selected, int left, int top); 
    
    Column<T, String> getErrorColumn();
    
    List<T> getTransCsvList(String result);
    
    boolean isCsvDataValid();
}
