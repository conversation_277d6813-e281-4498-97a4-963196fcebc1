package za.co.ipay.metermng.client.view.workspace;

import com.google.gwt.core.client.GWT;
import com.google.gwt.event.dom.client.ClickEvent;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiFactory;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.uibinder.client.UiHandler;
import com.google.gwt.user.client.ui.Button;
import com.google.gwt.user.client.ui.DialogBox;
import com.google.gwt.user.client.ui.Label;
import com.google.gwt.user.client.ui.Widget;

import za.co.ipay.gwt.common.client.resource.Messages;
import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.gwt.common.client.widgets.Message;
import za.co.ipay.metermng.client.i18n.UiMessages;
import za.co.ipay.metermng.client.i18n.UiMessagesUtil;

public class UpdateAccessGroupDialogueBox extends DialogBox {

    @UiField Label accessGroupLbl;
    @UiField Label accessGroupNameLbl;
    @UiField Message errorMsg;
    @UiField Message clearFuturePricingstructures;
    @UiField Message clearUPAndCustomerLocationGroups;
    @UiField Message confirm;
    @UiField Button btnCancel;
    @UiField Button btnUpdate;

    private Long accessGroupId;
    private UsagePointWorkspaceView parent;
    private Messages messages = MessagesUtil.getInstance();

    // private static Logger logger = Logger.getLogger(UpdateAccessGroupDialogueBox.class.getName());
    private static UpdateAccessGroupDialogueBoxUiBinder uiBinder = GWT.create(UpdateAccessGroupDialogueBoxUiBinder.class);
    interface UpdateAccessGroupDialogueBoxUiBinder extends UiBinder<Widget, UpdateAccessGroupDialogueBox> {
    }

    public UpdateAccessGroupDialogueBox(UsagePointWorkspaceView parent, Long accessGroupId, String accessGroupName, Boolean isValidPs, Boolean isValidGroup) {
        this.ensureDebugId("updateAccessGroupDialogueBox");
        this.parent = parent;
        this.accessGroupId = accessGroupId;
        setWidget(uiBinder.createAndBindUi(this));
        cleanUp();
        
        accessGroupNameLbl.setText(accessGroupName);
        clearFuturePricingstructures.setText(messages.getMessage("access_group.update.future.pricing.clear"));
        clearUPAndCustomerLocationGroups.setText(messages.getMessage("access_group.update.location.groups.clear"));
        confirm.setText(messages.getMessage("access_group.update.confirm.lbl"));
        if (!isValidGroup) {
            btnUpdate.setEnabled(false);
            errorMsg.setText(messages.getMessage("access_group.update.group.error"));
            errorMsg.setVisible(true);
        } else if (!isValidPs) {
            btnUpdate.setEnabled(false);
            errorMsg.setText(messages.getMessage("access_group.update.pricing.error"));
            errorMsg.setVisible(true);
        }
    }

    @UiFactory
    public UiMessages getUiMessages() {
        return UiMessagesUtil.getInstance();
    }

    @UiHandler("btnCancel")
    void handleCancelAttachMeterToUsagePoint(ClickEvent event) {
        parent.resetAccessGroup();
        cleanUpAndHide();
    }

    @UiHandler("btnUpdate")
    void handleSaveButton(ClickEvent event) {
        parent.updateAccessGroup(accessGroupId);
        cleanUpAndHide();
    }

    private void cleanUpAndHide() {
        cleanUp();
        hide();
    }

    private void cleanUp() {
        btnUpdate.setEnabled(true);
        errorMsg.setText("");
        errorMsg.setVisible(false);
    }

}
