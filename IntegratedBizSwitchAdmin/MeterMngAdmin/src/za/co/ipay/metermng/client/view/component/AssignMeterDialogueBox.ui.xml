<!DOCTYPE ui:UiBinder SYSTEM "http://dl.google.com/gwt/DTD/xhtml.ent">
<ui:UiBinder xmlns:ui="urn:ui:com.google.gwt.uibinder"
			 xmlns:g="urn:import:com.google.gwt.user.client.ui"
			 xmlns:p1="urn:import:com.google.gwt.user.datepicker.client"
			 xmlns:ipay="urn:import:za.co.ipay.gwt.common.client.widgets"
			 xmlns:p2="urn:import:za.co.ipay.metermng.client.view.component"
			 xmlns:p3="urn:import:za.co.ipay.gwt.common.client.form"
			 xmlns:pricingstructure="urn:import:za.co.ipay.metermng.client.widget.pricingstructure">
	<ui:style field="AssignMeterStyle">
		.device_move_ref_label {
	    	display: inline;
			margin-left: 14px;
		}
		.txtbxStyle {
	    	padding: 2px 2px 2px 2px;
		    border: 1px solid #ccc;
		    border-top: 1px solid #999;
		    font-size: 1em;
		    font-family: Arial Unicode MS, Arial, sans-serif;
		    color: #38291E;
			margin-left: 4px;
		}
	</ui:style>

	<ui:with field="msg" type="za.co.ipay.metermng.client.i18n.UiMessages" />

	<g:HTMLPanel>
		<table>
			<tr>
				<td>
					<g:VerticalPanel horizontalAlignment="ALIGN_CENTER" spacing="10">
						<g:HTML ui:field="clearMeterBalanceMsg" debugId="clearMeterBalanceMsg" visible="false"/>
                        <g:FlowPanel ui:field="preamble" debugId="preamble">
					       <g:Label text="{msg.getMeterSelectStoreMove}" styleName="gwt-Label-bold" ui:field="lblSelectMeterStore" />
						  <ipay:IpayListBox visibleItemCount="1" ui:field="lstbxStores" styleName="gwt-ListBox-ipay" multipleSelect="false" />
                        </g:FlowPanel>
                        <ipay:Message ui:field="feedBack" debugId="feedback"/>
						<g:Label ui:field="lblEnterMeterNumber" text="{msg.getMeterEnterNumber}:" styleName="gwt-Label-bold" horizontalAlignment="ALIGN_CENTER" debugId="lblEnterMeterNumber"/>
						<g:SuggestBox  ui:field="lstbxAssignMeter" styleName="gwt-TextBox-ipay" debugId="lstbxAssignMeter"/>
                        <ipay:Message ui:field="meterfeedBack" debugId="meterfeedback" visible="false"/>
						<g:FlowPanel ui:field="deviceMoveRefPanel" visible="false">
			                <p3:FormElement ui:field="deviceMoveRefElement" debugId="deviceMoveRefElement" required="true" labelText="{msg.getUsagePointDeviceMoveRefLbl}:" 
			                	styleName="{AssignMeterStyle.device_move_ref_label}">
			                    <g:TextBox debugId="deviceMoveRefTxtbx" styleName="{AssignMeterStyle.txtbxStyle}" ui:field="deviceMoveRefTxtbx" visibleLength="20"/>
			                </p3:FormElement>
			            </g:FlowPanel>
						<g:FlowPanel ui:field="installDatePanel" visible="false" debugId="installDatePanel">
						    <g:Label text="{msg.getMeterSpecifyInstallDate}" styleName="gwt-Label-bold" horizontalAlignment="ALIGN_CENTER"/>
						    <g:Label horizontalAlignment="ALIGN_CENTER" ui:field="installdateRequired"/>
	                        <p1:DateBox debugId="assignInstallDate" styleName="gwt-TextBox" ui:field="dtbxInstallDate" />
                            <ipay:Message ui:field="datefeedBack" debugId="datefeedback" visible="false"/>
                        </g:FlowPanel>
                        <g:FlowPanel ui:field="changeActivationDatePanel" visible="false" debugId="changeActivationDatePanel">
                              <g:Label text="{msg.getMeterChangeActivationDate}" styleName="gwt-Label-bold" horizontalAlignment="ALIGN_CENTER"/>
                                <g:RadioButton ui:field="yesChangeActivationDate" name="changeActivationDate" value="false" enabled="true" text="{msg.getOptionPositive}" />
                                <g:RadioButton ui:field="noChangeActivationDate" name="changeActivationDate" value="false" enabled="true" text="{msg.getOptionNegative}" />
                            <ipay:Message ui:field="changeActivationFeedBack" debugId="changeActivationFeedBack" visible="false"/>
                        </g:FlowPanel>
                        <g:FlowPanel ui:field="currentPricingStructureFlowPanel" visible="false" debugId="currentPricingStructure">
                            <g:Label text="{msg.getMeterNewCurrentPSRequired}:" horizontalAlignment="ALIGN_CENTER" ui:field="newCurrentPSRequired" debugId="newCurrentPSRequired" styleName="errorStatus"/>
                            <g:Label  styleName="gwt-Label-bold" text="{msg.getMeterNewCurrentSelectPS}"/>
							<pricingstructure:PricingStructureLookup ui:field="currentPricingStructureLookup" debugId="currentPricingStructureLookup"/>
                        </g:FlowPanel>
                        <p2:SpecialActionsReasonComponent ui:field="specialactionreasons"/>
                        <g:FlowPanel>
							<g:Button debugId="replaceMeterButton" text="{msg.getReplaceMeterButton}" styleName="gwt-Button-ipay" ui:field="btnAssignReplaceMeter" />
                        	<g:Button debugId="cancel" styleName="gwt-Button-ipay" ui:field="btnCancel" visible="false"/>
                            <g:Button debugId="cancelDefault" styleName="gwt-Button-ipay" ui:field="btnCancelDefault" visible="true" text="{msg.getCancelButton}"/>
                        </g:FlowPanel>

					</g:VerticalPanel>
				</td>
			</tr>
		</table>
	</g:HTMLPanel>
</ui:UiBinder> 
