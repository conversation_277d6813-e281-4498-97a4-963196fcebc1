package za.co.ipay.metermng.client.view.workspace.meter.readings.add.singlemeter;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import za.co.ipay.gwt.common.client.form.FormElement;
import za.co.ipay.gwt.common.client.form.FormGroupPanel;
import za.co.ipay.gwt.common.client.form.FormRowPanel;
import za.co.ipay.gwt.common.client.form.Format.FormatUtil;
import za.co.ipay.gwt.common.client.handler.FormDataChangeHandler;
import za.co.ipay.gwt.common.client.handler.FormDataClickHandler;
import za.co.ipay.gwt.common.client.handler.FormDataValueChangeHandler;
import za.co.ipay.gwt.common.client.form.SimpleForm;
import za.co.ipay.gwt.common.client.resource.Messages;
import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.gwt.common.client.widgets.StrictDateFormat;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.form.SimpleFormPanel;
import za.co.ipay.metermng.client.rpc.callback.ClientCallback;
import za.co.ipay.metermng.mybatis.custom.model.MeterDto;
import za.co.ipay.metermng.mybatis.generated.model.MdcChannel;
import za.co.ipay.metermng.shared.MeterSuggestOracle;
import za.co.ipay.metermng.shared.MeterSuggestion;
import za.co.ipay.metermng.shared.dto.meter.MeterReadingDto;

import com.google.gwt.core.client.GWT;
import com.google.gwt.event.dom.client.ChangeEvent;
import com.google.gwt.event.dom.client.FocusEvent;
import com.google.gwt.event.dom.client.FocusHandler;
import com.google.gwt.event.logical.shared.SelectionEvent;
import com.google.gwt.event.logical.shared.SelectionHandler;
import com.google.gwt.event.logical.shared.ValueChangeEvent;
import com.google.gwt.event.logical.shared.ValueChangeHandler;
import com.google.gwt.i18n.client.DateTimeFormat;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.uibinder.client.UiHandler;
import com.google.gwt.user.client.ui.CheckBox;
import com.google.gwt.user.client.ui.ListBox;
import com.google.gwt.user.client.ui.RadioButton;
import com.google.gwt.user.client.ui.SuggestBox;
import com.google.gwt.user.client.ui.SuggestBox.DefaultSuggestionDisplay;
import com.google.gwt.user.client.ui.SuggestOracle.Suggestion;
import com.google.gwt.user.client.ui.TextBox;
import com.google.gwt.user.client.ui.Widget;
import com.google.gwt.user.datepicker.client.CalendarUtil;
import com.google.gwt.user.datepicker.client.DateBox;
import com.google.gwt.user.datepicker.client.DateBox.Format;

public class AddMeterReadingsPanel extends SimpleFormPanel {
    
    @UiField FormElement meterNumberElement;
    @UiField FormElement startElement;
    @UiField FormElement endElement;
    @UiField FormElement readingTypesElement;
    @UiField FormElement readingIntervalElement;
    @UiField FormElement tariffCalcElement;
    @UiField(provided=true) SuggestBox meterNumberBox;
    MeterSuggestOracle suggest;
    MeterDto selectedMeter;    
    @UiField DateBox startBox;
    @UiField DateBox endBox;
    @UiField ListBox readingTypesBox;
    @UiField ListBox readingIntervalBox;
    @UiField CheckBox tariffCalcBox;
    @UiField DateBox firstBox;
    @UiField DateBox lastBox;
    @UiField CheckBox checkZero;
    @UiField FormGroupPanel formZero;
    @UiField FormElement zeroStartElement;
    @UiField DateBox zeroStartBox;
    @UiField FormElement zeroEndElement;
    @UiField DateBox zeroEndBox;
    @UiField RadioButton radioZeroConsecutive;
    @UiField RadioButton radioZeroRandom;
    @UiField TextBox textZeroRandom;
    @UiField FormElement formZeroRandom;
    @UiField CheckBox checkMissing;
    @UiField FormGroupPanel formMissing;
    @UiField FormElement missingStartElement;
    @UiField DateBox missingStartBox;
    @UiField FormElement missingEndElement;
    @UiField DateBox missingEndBox;
    @UiField RadioButton radioMissingConsecutive;
    @UiField RadioButton radioMissingRandom;
    @UiField TextBox textMissingRandom;
    @UiField FormElement formMissingRandom;
    @UiField RadioButton radioDeleteAll;
    @UiField RadioButton radioDeleteSelected;
    @UiField RadioButton radioAppend;
    @UiField FormElement formDeleteReadings;
    @UiField RadioButton radioIntervalReadings;
    @UiField RadioButton radioRegisterReadings;
    @UiField FormRowPanel formMdcChannels;
    @UiField FormElement mdcChannelsElement;
    @UiField ListBox mdcChannelsBox;
    
    private ClientFactory clientFactory;
    private AddMeterReadingsWorkspaceView addMeterReadingsWorkspaceView;

    private static AddMeterReadingsPanelUiBinder uiBinder = GWT.create(AddMeterReadingsPanelUiBinder.class);

    interface AddMeterReadingsPanelUiBinder extends UiBinder<Widget, AddMeterReadingsPanel> {
    }

    public AddMeterReadingsPanel(ClientFactory clientFactory, SimpleForm form,
            AddMeterReadingsWorkspaceView addMeterReadingsWorkspaceView) {
        super(form);
        this.clientFactory = clientFactory;
        this.addMeterReadingsWorkspaceView = addMeterReadingsWorkspaceView;
        createMeterBox(clientFactory);
        initWidget(uiBinder.createAndBindUi(this));
        initUi();
    }

    private void createMeterBox(ClientFactory clientFactory) {
        suggest = new MeterSuggestOracle(clientFactory);
        this.meterNumberBox = new SuggestBox(suggest);
        meterNumberBox.addSelectionHandler(new SelectionHandler<MeterSuggestOracle.Suggestion>() {
            @Override
            public void onSelection(SelectionEvent<Suggestion> event) {
                if (event.getSelectedItem() instanceof MeterSuggestion) {
                    selectedMeter = ((MeterSuggestion) event.getSelectedItem()).getMeter();
                    populateExistingDateRange();
                    getMdcChannelsForReadingTypesAndMeter();
                } else {
                    meterNumberElement.setErrorMsg(
                            MessagesUtil.getInstance().getMessage("demo.addmeterreadings.error.meter.select"));
                }
            }
        });
        meterNumberBox.getValueBox().addFocusHandler(new FocusHandler() {
            @Override
            public void onFocus(FocusEvent event) {
                if (!((DefaultSuggestionDisplay) meterNumberBox.getSuggestionDisplay()).isSuggestionListShowing()) {
                    meterNumberBox.showSuggestionList();
                }
            }
        });
    }

    public void populateExistingDateRange() {
        if (selectedMeter != null) {
            clientFactory.getMeterRpc().getReadingsDateRangeForMeter(selectedMeter, getReadingTypeIds(),
                    radioIntervalReadings.getValue(), new ClientCallback<MeterReadingDto>() {
                        @Override
                        public void onSuccess(MeterReadingDto result) {
                            firstBox.setValue(result.getStart());
                            lastBox.setValue(result.getEnd());
                        }
                    });
        }
    }

    private void initUi() {
        za.co.ipay.gwt.common.client.form.Format formatInstance = FormatUtil.getInstance();
        Format format = new StrictDateFormat(
                DateTimeFormat.getFormat(formatInstance.getDateFormat() + " " + formatInstance.getTimeFormat()));
        startBox.setFormat(format);
        endBox.setFormat(format);
        firstBox.setFormat(format);
        lastBox.setFormat(format);
        zeroStartBox.setFormat(format);
        zeroEndBox.setFormat(format);
        missingStartBox.setFormat(format);
        missingEndBox.setFormat(format);
        startBox.setValue(getInitStartDate());
        endBox.setValue(getStartPreviousDay());

        addFieldHandlers();

        ValueChangeHandler<Boolean> radioZeroChangeHandler = new ValueChangeHandler<Boolean>() {
            @Override
            public void onValueChange(ValueChangeEvent<Boolean> event) {
                handleGroupZeroChange();
            }
        };
        radioZeroConsecutive.addValueChangeHandler(radioZeroChangeHandler);
        radioZeroRandom.addValueChangeHandler(radioZeroChangeHandler);
        ValueChangeHandler<Boolean> radioMissingChangeHandler = new ValueChangeHandler<Boolean>() {
            @Override
            public void onValueChange(ValueChangeEvent<Boolean> event) {
                handleGroupMissingChange();
            }
        };
        radioMissingConsecutive.addValueChangeHandler(radioMissingChangeHandler);
        radioMissingRandom.addValueChangeHandler(radioMissingChangeHandler);

        updateReadingVariant();

        ValueChangeHandler<Boolean> radioReadingsChangeHandler = new ValueChangeHandler<Boolean>() {
            @Override
            public void onValueChange(ValueChangeEvent<Boolean> event) {
                updateReadingVariant();
            }
        };
        radioIntervalReadings.addValueChangeHandler(radioReadingsChangeHandler);
        radioRegisterReadings.addValueChangeHandler(radioReadingsChangeHandler);

        getMdcChannelsForReadingTypesAndMeter();
    }

    @UiHandler("checkZero")
    void handleCheckZeroValueChange(ValueChangeEvent<Boolean> event) {
        boolean checked = checkZero.getValue();
        formZero.setVisible(checked);
        if (!checked) {
            zeroStartBox.setValue(null);
            zeroEndBox.setValue(null);
            radioZeroConsecutive.setValue(true);
            handleGroupZeroChange();
        }
    }

    @UiHandler("checkMissing")
    void handleCheckMissingValueChange(ValueChangeEvent<Boolean> event) {
        boolean checked = checkMissing.getValue();
        formMissing.setVisible(checked);
        if (!checked) {
            missingStartBox.setValue(null);
            missingEndBox.setValue(null);
            radioMissingConsecutive.setValue(true);
            handleGroupMissingChange();
        }
    }

    private void handleGroupZeroChange() {
        boolean selected = radioZeroConsecutive.getValue();
        formZeroRandom.setVisible(!selected);
        if (selected) {
            textZeroRandom.setValue(null);
        }
    }

    private void handleGroupMissingChange() {
        boolean selected = radioMissingConsecutive.getValue();
        formMissingRandom.setVisible(!selected);
        if (selected) {
            textMissingRandom.setValue(null);
        }
    }

    @Override
    public void addFieldHandlers() {
        meterNumberBox.addValueChangeHandler(new FormDataValueChangeHandler<String>(form));
        startBox.addValueChangeHandler(new FormDataValueChangeHandler<Date>(form));
        endBox.addValueChangeHandler(new FormDataValueChangeHandler<Date>(form));
        readingIntervalBox.addChangeHandler(new FormDataChangeHandler(form));
        readingTypesBox.addChangeHandler(new FormDataChangeHandler(form));
        tariffCalcBox.addClickHandler(new FormDataClickHandler(form));
        mdcChannelsBox.addChangeHandler(new FormDataChangeHandler(form));
    }

    @Override
    public void clearFields() {
        meterNumberBox.setText("");
        selectedMeter = null;
        startBox.setValue(getInitStartDate());
        endBox.setValue(getStartPreviousDay());
        readingTypesBox.setSelectedIndex(addMeterReadingsWorkspaceView.defaultReadingTypeIndex);
        tariffCalcBox.setEnabled(true);
        tariffCalcBox.setValue(true);
        readingIntervalBox.setSelectedIndex(0);
        mdcChannelsBox.setSelectedIndex(0);
        firstBox.setValue(null);
        lastBox.setValue(null);
        checkZero.setValue(false);
        handleCheckZeroValueChange(null);
        checkMissing.setValue(false);
        handleCheckMissingValueChange(null);
        radioDeleteAll.setValue(true);
    }

    public void disableTariffCalc() {
        tariffCalcBox.setValue(false);
        tariffCalcBox.setEnabled(false);
    }

    private Date getInitStartDate() {
        Date startDate = getStartPreviousDay();
        CalendarUtil.addDaysToDate(startDate, -2);
        String start = DateTimeFormat.getFormat("dd/MM/yyyy").format(startDate);
        return DateTimeFormat.getFormat("dd/MM/yyyy HH:mm").parse(start + " 00:00");
    }

    @SuppressWarnings("deprecation")
    protected Date getStartPreviousDay() {
        Date now = new Date();
        now.setHours(now.getHours() - 1);
        DateTimeFormat format = DateTimeFormat.getFormat("dd/MM/yyyy HH:mm");
        return format.parse(format.format(now));
    }

    @Override
    public void clearErrors() {
        meterNumberElement.clearErrorMsg();
        startElement.clearErrorMsg();
        endElement.clearErrorMsg();
        readingTypesElement.clearErrorMsg();
        readingIntervalElement.setErrorMsg(null);
        tariffCalcElement.clearErrorMsg();
        zeroStartElement.clearErrorMsg();
        zeroEndElement.clearErrorMsg();
        formZeroRandom.clearErrorMsg();
        missingStartElement.clearErrorMsg();
        missingEndElement.clearErrorMsg();
        formMissingRandom.clearErrorMsg();
        mdcChannelsElement.clearErrorMsg();
    }

    protected SuggestBox getMeterBox() {
        return meterNumberBox;
    }

    protected void setSelected(MeterDto meterDto) {
        selectedMeter = meterDto;
    }

    private void updateReadingVariant() {
        boolean registerReadings = radioRegisterReadings.getValue();
        String formMessage = null;
        String labelTextMessage = null;
        if (registerReadings) {
            formMessage = "demo.addmeterreadings.title.criteria.register";
            labelTextMessage = "demo.addmeterreadings.delete.register";
        } else {
            formMessage = "demo.addmeterreadings.title.criteria";
            labelTextMessage = "demo.addmeterreadings.delete";
        }
        Messages messagesInstance = MessagesUtil.getInstance();
        addMeterReadingsWorkspaceView.addMeterReadingsForm.getFormPanel()
                .setHeadingText(messagesInstance.getMessage(formMessage), "pageSectionTitle");
        formMdcChannels.setVisible(registerReadings);
        formDeleteReadings.setLabelText(messagesInstance.getMessage(labelTextMessage));

        populateExistingDateRange();
        getMdcChannelsForReadingTypesAndMeter();
    }

    private void getMdcChannelsForReadingTypesAndMeter() {
        if (selectedMeter != null) {
            ArrayList<Long> readingTypeIds = getReadingTypeIds();
            mdcChannelsBox.clear();
            if (!readingTypeIds.isEmpty()) {
                clientFactory.getMdcChannelRpc().getMdcChannelsForReadingTypesAndMeter(readingTypeIds,
                        selectedMeter.getId(), new ClientCallback<List<MdcChannel>>() {
                            @Override
                            public void onSuccess(List<MdcChannel> result) {
                                populateMdcChannels(result);
                            }
                        });
            }
        }
    }

    private void populateMdcChannels(List<MdcChannel> mdcChannels) {
        for (MdcChannel mdcChannel : mdcChannels) {
            mdcChannelsBox.addItem(mdcChannel.getName(), mdcChannel.getId().toString());
        }
        mdcChannelsBox.setVisibleItemCount(mdcChannels.size());
    }

    public ArrayList<Long> getReadingTypeIds() {
        ArrayList<Long> ids = new ArrayList<Long>();
        for (int i = 0; i < readingTypesBox.getItemCount(); i++) {
            if (readingTypesBox.isItemSelected(i)) {
                ids.add(Long.valueOf(readingTypesBox.getValue(i)));
            }
        }
        return ids;
    }

    @UiHandler("readingTypesBox")
    void handleReadingTypesBoxChange(ChangeEvent event) {
        populateExistingDateRange();
        getMdcChannelsForReadingTypesAndMeter();
    }
}