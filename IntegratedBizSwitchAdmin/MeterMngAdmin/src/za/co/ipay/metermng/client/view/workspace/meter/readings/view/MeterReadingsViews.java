package za.co.ipay.metermng.client.view.workspace.meter.readings.view;

import java.util.ArrayList;
import java.util.logging.Logger;

import com.google.gwt.core.client.GWT;
import com.google.gwt.event.dom.client.ClickEvent;
import com.google.gwt.event.dom.client.ClickHandler;
import com.google.gwt.event.logical.shared.BeforeSelectionEvent;
import com.google.gwt.event.logical.shared.BeforeSelectionHandler;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.user.client.Window;
import com.google.gwt.user.client.ui.Label;
import com.google.gwt.user.client.ui.ScrollPanel;
import com.google.gwt.user.client.ui.Widget;

import za.co.ipay.gwt.common.client.Dialogs;
import za.co.ipay.gwt.common.client.dataexport.GetRequestBuilder;
import za.co.ipay.gwt.common.client.form.SimpleForm;
import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.gwt.common.client.workspace.MultipleViewsFormPanel;
import za.co.ipay.gwt.common.client.workspace.SessionCheckCallback;
import za.co.ipay.gwt.common.shared.dto.IdNameDto;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.history.MeterReadingsPlace;
import za.co.ipay.metermng.client.resource.image.MediaResource.MediaResourceUtil;
import za.co.ipay.metermng.client.rpc.callback.ClientCallback;
import za.co.ipay.metermng.client.view.component.BaseComponent;
import za.co.ipay.metermng.client.view.workspace.UsagePointWorkspaceView;
import za.co.ipay.metermng.mybatis.custom.model.MeterDto;
import za.co.ipay.metermng.mybatis.generated.model.MeterReadingType;
import za.co.ipay.metermng.shared.MeterMngStatics;
import za.co.ipay.metermng.shared.dto.UsagePointData;
import za.co.ipay.metermng.shared.dto.meter.MeterModelScreenDataDto;
import za.co.ipay.metermng.shared.dto.meter.MeterReadingsDto;

public class MeterReadingsViews extends BaseComponent {

    @UiField MultipleViewsFormPanel multipleViewsForm;
    private SimpleForm form;
    private MeterReadingsPanel panel;
    private MeterReadingsGraphPanel graphPanel;
    private MeterReadingsTablePanel tablePanel;

    private String singleMeterGraphType;
    private String balancingMeterGraphType;
    private String energyUsageReadingType;
    protected ArrayList<MeterReadingType> meterReadingTypes;

    private boolean viewConstructed = false;

    private static Logger logger = Logger.getLogger(MeterReadingsViews.class.getName());

    private static MeterReadingsViewsUiBinder uiBinder = GWT.create(MeterReadingsViewsUiBinder.class);

    protected ArrayList<IdNameDto> serviceResources = null;

    private UsagePointWorkspaceView usagePointWorkspaceView;
    private boolean usagePointTabSelected = false;

    interface MeterReadingsViewsUiBinder extends UiBinder<Widget, MeterReadingsViews> {
    }

    public MeterReadingsViews(ClientFactory clientFactory, MeterReadingsPlace meterReadingsPlace, UsagePointWorkspaceView usagePointWorkspaceView) {
        this.clientFactory = clientFactory;
        this.usagePointWorkspaceView = usagePointWorkspaceView;
        initWidget(uiBinder.createAndBindUi(this));

        if (meterReadingsPlace == null || MeterReadingsPlace.USAGE_POINT_METER.equals(meterReadingsPlace.getSuperMeter())) {
            multipleViewsForm.removeHeader();
        } else {
            multipleViewsForm.setHeader(MessagesUtil.getInstance().getMessage("meterreadings.title"));
        }
        this.form = multipleViewsForm.getForm();
        setUsagePointTabSelected();
        initViews(meterReadingsPlace);

        int width = Window.getClientWidth() - (Window.getClientWidth() / 5);
        int height = 470;
        multipleViewsForm.getTabLayoutPanel().setWidth(width+"px");
        multipleViewsForm.getTabLayoutPanel().setHeight(height+"px");

        logger.info("Created MeterReadingsViews");
    }

    private void initViews(MeterReadingsPlace meterReadingsPlace) {
        logger.info("Init views...");
        initForm(meterReadingsPlace);
        initGraphUi();
        initReportUi();
        logger.info("Init views");

        viewConstructed = true;
    }

    private void setUsagePointTabSelected() {
        if(usagePointWorkspaceView != null){
            usagePointTabSelected = usagePointWorkspaceView.getSelectedTab() == usagePointWorkspaceView.getUptabinx();
        }
    }

    public boolean isViewConstructed() {
        return viewConstructed;
    }

    private void initForm(MeterReadingsPlace place) {
        ArrayList<String> graphTypes = getGraphTypes();
        if (place != null && place.getGraphType() != null && place.getReadingType() != null) {
            logger.info("Checking graphType and readingType");
            String gType = "";
            String rType = "";
            if (MeterMngStatics.SINGLE_METER_GRAPH_TYPE.equals(place.getGraphType())) {
                gType = singleMeterGraphType;
            } else if (MeterMngStatics.ENERGY_BALANCING_GRAPH_TYPE.equals(place.getGraphType())) {
                gType = balancingMeterGraphType;
            }
            if (MeterMngStatics.ENERGY_FORWARD_METER_READING_TYPE.equals(place.getReadingType())) {
                rType = energyUsageReadingType;
            }
            logger.info("Got graphType: "+gType+" readingType: "+rType);
            panel = new MeterReadingsPanel(clientFactory, form, graphTypes, place.getSuperMeter(), gType, rType, place.getStartDate(), place.getEndDate(), this);
        } else if (place != null && MeterReadingsPlace.USAGE_POINT_METER.equals(place.getSuperMeter())) {
            logger.info("No graphType and readingType: ");
            panel = new MeterReadingsPanel(clientFactory, form, graphTypes, this, true);
            logger.info("Created panel");
        } else {
            logger.info("No graphType and readingType: ");
            panel = new MeterReadingsPanel(clientFactory, form, graphTypes, this, false);
            logger.info("Created panel");
        }

        form.getFormFields().add(panel);
        loadMeterReadingTypes();

        form.getSaveBtn().setText(MessagesUtil.getInstance().getMessage("button.view"));
        form.getSaveBtn().addClickHandler(new ClickHandler() {
            @Override
            public void onClick(ClickEvent arg0) {
                SessionCheckCallback sessionCheckCallback = new SessionCheckCallback() {
                    @Override
                    public void callback(SessionCheckResolution resolution) {
                        onView();
                    }
                };
                clientFactory.handleSessionCheckCallback(sessionCheckCallback);
            }
        });

        form.getOtherBtn().setText(MessagesUtil.getInstance().getMessage("button.export"));
        form.getOtherBtn().addClickHandler(new ClickHandler() {
            @Override
            public void onClick(ClickEvent event) {
                onReportExport();
            }
        });


        form.getBackBtn().setText(MessagesUtil.getInstance().getMessage("button.clear"));
        form.getBackBtn().addClickHandler(new ClickHandler() {
            @Override
            public void onClick(ClickEvent event) {
                onClear();
            }
        });
        form.getBackBtn().setVisible(true);

        form.getFormPanel().setHeadingText("", "pageSectionTitle");

        clientFactory.getMeterModelRpc().getMeterModelsScreenData(new ClientCallback<MeterModelScreenDataDto>() {
            @Override
            public void onSuccess(MeterModelScreenDataDto screenData) {
                serviceResources = screenData.getServiceResources();
            }
        });
    }

    private void loadMeterReadingTypes() {
        clientFactory.getMeterRpc().getMeterReadingTypes(new ClientCallback<ArrayList<MeterReadingType>>() {
            @Override
            public void onSuccess(ArrayList<MeterReadingType> result) {
                meterReadingTypes = result;
                panel.setMeterReadingTypes(result);
            }
        });
    }

    private Long getMeterReadingTypeId(String meterReadingTypeValue) {
        if (meterReadingTypes != null) {
            for(MeterReadingType type : meterReadingTypes) {
                if (type.getValue().equals(meterReadingTypeValue)) {
                    return type.getId();
                }
            }
        }
        return null;
    }

    private ArrayList<String> getGraphTypes() {
        singleMeterGraphType = MessagesUtil.getInstance().getMessage("meterreadings.type.graph.single");
        balancingMeterGraphType = MessagesUtil.getInstance().getMessage("meterreadings.type.graph.balancing");
        ArrayList<String> types = new ArrayList<String>();
        types.add(singleMeterGraphType);
        types.add(balancingMeterGraphType);
        return types;
    }

    private void initGraphUi() {
        Label tab = new Label(MessagesUtil.getInstance().getMessage("meterreadings.header.graph"));
        graphPanel = new MeterReadingsGraphPanel(singleMeterGraphType);
        ScrollPanel scroll = new ScrollPanel(graphPanel);
        scroll.addStyleName("multipleView");
        multipleViewsForm.getTabLayoutPanel().add(scroll, tab);
    }

    private void initReportUi() {
        Label tab = new Label(MessagesUtil.getInstance().getMessage("meterreadings.header.table"));
        tablePanel = new MeterReadingsTablePanel();
        multipleViewsForm.getTabLayoutPanel().add(new ScrollPanel(tablePanel), tab);
        multipleViewsForm.getTabLayoutPanel().addBeforeSelectionHandler(new BeforeSelectionHandler<Integer>() {
            @Override
            public void onBeforeSelection(BeforeSelectionEvent<Integer> event) {
                if (event.getItem().equals(0)) {
                    graphPanel.resizeChart();
                }
            }
        });
    }

    public void setSelectedMeter(MeterDto selectedMeter, Long serviceResourceId) {
        panel.setSelectedMeter(selectedMeter, serviceResourceId);
    }

    public void setUsagePointData(UsagePointData usagePointData, Long serviceResourceId){
        panel.setUsagePointData(usagePointData, serviceResourceId);
    }

    protected void onView() {
        panel.clearErrors();
        if (panel.isValidInput()) {
            Long meterReadingTypeId = getMeterReadingTypeId(panel.getMeterReadingType());
            if (meterReadingTypeId != null) {
                final String graphType = panel.getGraphType();
                if (singleMeterGraphType.equals(graphType)) {
                    final MeterDto selectedMeter = panel.getSelectedMeter();
                    clientFactory.getMeterRpc().getMeterReadings(selectedMeter.getId(),
                                                                 meterReadingTypeId,
                                                                 panel.getStartDate(),
                                                                 panel.getEndDate(),
                                                                 new ClientCallback<MeterReadingsDto>() {
                                                                     @Override
                                                                     public void onSuccess(MeterReadingsDto result) {
                                                                         graphPanel.displayChartData(panel.getStartDate(), graphType, selectedMeter.getNumber(), result);
                                                                         tablePanel.displayMeterReadings(graphType, selectedMeter.getNumber(), result, true);
                                                                     }
                                                                 });
                } else if (MessagesUtil.getInstance().getMessage("meterreadings.type.graph.balancing").equals(graphType)) {
                    clientFactory.getMeterRpc().getMeterBalancingReadings(panel.getBalancingMeterId(),
                                                                          meterReadingTypeId,
                                                                          panel.getStartDate(),
                                                                          panel.getEndDate(),
                                                                          new ClientCallback<MeterReadingsDto>() {
                                                                             @Override
                                                                             public void onSuccess(MeterReadingsDto result) {
                                                                                 graphPanel.displayChartData(panel.getStartDate(), graphType, null, result);
                                                                                 tablePanel.displayMeterReadings(graphType, null, result, false);
                                                                             }
                                                                          });
                } else {
                    Dialogs.displayErrorMessage(MessagesUtil.getInstance().getMessage("meterreadings.error.type.graph"),
                                                MediaResourceUtil.getInstance().getErrorIcon());
                }
            } else {
                Dialogs.displayErrorMessage(MessagesUtil.getInstance().getMessage("meterreadings.error.type.reading.unknown"),
                                            MediaResourceUtil.getInstance().getErrorIcon());
            }
        }
    }

    private void onClear() {
        panel.clearErrors();
        panel.clearFields();
        graphPanel.clear();
        tablePanel.clear();
    }

    protected void onReportExport() {
        panel.clearErrors();
        if (panel.isValidInput()) {
            Long meterReadingTypeId = getMeterReadingTypeId(panel.getMeterReadingType());
            if (meterReadingTypeId != null) {
                final String graphType = panel.getGraphType();
                String encodedUrl = null;
                if (singleMeterGraphType.equals(graphType)) {
                    final MeterDto selectedMeter = panel.getSelectedMeter();
                    encodedUrl = new GetRequestBuilder()
                                    .withBaseUrl(GWT.getHostPageBaseURL())
                                    .withTargetUrl("export")
                                    .addParam("data", "meterreadings")
                                    .addParam("type", "singleMeter")
                                    .addParam("meter", selectedMeter.getId().toString())
                                    .addParam("readingtype", meterReadingTypeId.toString())
                                    .addParam("start", Long.toString(panel.getStartDate().getTime()))
                                    .addParam("end", Long.toString(panel.getEndDate().getTime()))
                                    .toEncodedUrl();
                } else if (MessagesUtil.getInstance().getMessage("meterreadings.type.graph.balancing").equals(graphType)) {
                    encodedUrl = new GetRequestBuilder()
                                    .withBaseUrl(GWT.getHostPageBaseURL())
                                    .withTargetUrl("export")
                                    .addParam("data", "meterreadings")
                                    .addParam("type", "energyBalancing")
                                    .addParam("meter", panel.getBalancingMeterId().toString())
                                    .addParam("readingtype", meterReadingTypeId.toString())
                                    .addParam("start", Long.toString(panel.getStartDate().getTime()))
                                    .addParam("end", Long.toString(panel.getEndDate().getTime()))
                                    .toEncodedUrl();
                } else {
                    Dialogs.displayErrorMessage(MessagesUtil.getInstance().getMessage("meterreadings.error.type.graph"),
                                                MediaResourceUtil.getInstance().getErrorIcon());
                }

                //Request the export file via the export URL
                if (encodedUrl != null) {
                    logger.info("Export url: "+encodedUrl);
                    int top = Window.getClientHeight() / 2 - 50;
                    int left = Window.getClientWidth() / 2 - 50;
                    Window.open(encodedUrl, "_blank", "width=100, height=100, top="+top+", left="+left);
                }
            } else {
                Dialogs.displayErrorMessage(MessagesUtil.getInstance().getMessage("meterreadings.error.type.reading.unknown"),
                                            MediaResourceUtil.getInstance().getErrorIcon());
            }
        }
    }

    public boolean isUsagePointTabSelected(){
        return usagePointTabSelected;
    }
}
