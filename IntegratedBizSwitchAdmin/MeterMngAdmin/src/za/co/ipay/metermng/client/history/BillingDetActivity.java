package za.co.ipay.metermng.client.history;

import za.co.ipay.metermng.client.event.BillingDetEvent;
import za.co.ipay.metermng.client.factory.ClientFactory;

import com.google.gwt.activity.shared.AbstractActivity;
import com.google.gwt.event.shared.EventBus;
import com.google.gwt.user.client.ui.AcceptsOneWidget;

public class BillingDetActivity extends AbstractActivity {

    private ClientFactory clientFactory;
    private BillingDetPlace place;

    public BillingDetActivity(BillingDetPlace place, ClientFactory clientFactory) {
        super();
        this.clientFactory = clientFactory;
        this.place = place;
    }

    @Override
    public void start(AcceptsOneWidget panel, EventBus eventBus) {
        clientFactory.getEventBus().fireEvent(new BillingDetEvent(place.getName()));
    }
}
