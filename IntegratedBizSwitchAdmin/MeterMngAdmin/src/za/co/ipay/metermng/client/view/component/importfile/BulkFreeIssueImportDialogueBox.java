package za.co.ipay.metermng.client.view.component.importfile;

import java.util.List;

import za.co.ipay.gwt.common.client.Dialogs;
import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.resource.image.MediaResource.MediaResourceUtil;
import za.co.ipay.metermng.shared.dto.importfile.ImportFileItemDto;
import za.co.ipay.metermng.shared.integration.bulkfreeissue.BulkFreeIssueImportRecord;

public class BulkFreeIssueImportDialogueBox extends ImportFileItemBaseDialogueBox {

    private BulkFreeIssueImportRecord recordIn;

    public BulkFreeIssueImportDialogueBox(ClientFactory clientFactory, ImportFileItemView importFileItemView) {
        super(clientFactory, importFileItemView);
    }

    @Override
    protected List<ImportRecordField> createDataList(ImportFileItemDto itemDto) {
        BulkFreeIssueImportRecord record = recordIn = itemDto.getBulkFreeIssueImportRecord();
        dataList.clear();
        dataList.add(new ImportRecordField("Meter Number", record.getMeterNum()));
        dataList.add(new ImportRecordField("Units", record.getUnits() != null ? record.getUnits().toString() : ""));
        dataList.add(new ImportRecordField("Description", record.getDescription() != null ? record.getDescription() : ""));
        dataList.add(new ImportRecordField("User Reference", record.getUserReference() != null ? record.getUserReference() : ""));
        dataList.add(new ImportRecordField("Reason", record.getReason() != null ? record.getReason() : ""));
        dataList.add(new ImportRecordField("Custom Reason", record.getCustomReason() != null ? record.getCustomReason() : ""));
        return dataList;
    }

    @Override
    protected void checkDirtyData() {
        isDirtyData = false;
        BulkFreeIssueImportRecord chgRec = createRecordFromList();

        if (!safeEquals(recordIn.getMeterNum(), chgRec.getMeterNum())) {
            isDirtyData = true;
            return;
        }
        if (!safeEquals(recordIn.getUnits(), chgRec.getUnits())) {
            isDirtyData = true;
            return;
        }
        if (!safeEquals(recordIn.getDescription(), chgRec.getDescription())) {
            isDirtyData = true;
            return;
        }
        if (!safeEquals(recordIn.getUserReference(), chgRec.getUserReference())) {
            isDirtyData = true;
            return;
        }
        if (!safeEquals(recordIn.getReason(), chgRec.getReason())) {
            isDirtyData = true;
            return;
        }
        if (!safeEquals(recordIn.getCustomReason(), chgRec.getCustomReason())) {
            isDirtyData = true;
            return;
        }
    }

    private BulkFreeIssueImportRecord createRecordFromList() {
        BulkFreeIssueImportRecord chgRec = new BulkFreeIssueImportRecord();

        for (ImportRecordField field : dataProvider.getList()) {
            if (field.getFieldname().equals("Meter Number")) {
                chgRec.setMeterNum(field.getFieldValue());
            }
            if (field.getFieldname().equals("Units")) {
                try {
                    if (field.getFieldValue() != null && !field.getFieldValue().trim().isEmpty()) {
                        chgRec.setUnits(Integer.parseInt(field.getFieldValue().trim()));
                    }
                } catch (NumberFormatException e) {
                    // Invalid number format - will be handled in validation
                }
            }
            if (field.getFieldname().equals("Description")) {
                chgRec.setDescription(field.getFieldValue());
            }
            if (field.getFieldname().equals("User Reference")) {
                chgRec.setUserReference(field.getFieldValue());
            }
            if (field.getFieldname().equals("Reason")) {
                chgRec.setReason(field.getFieldValue());
            }
            if (field.getFieldname().equals("Custom Reason")) {
                chgRec.setCustomReason(field.getFieldValue());
            }
        }

        return chgRec;
    }

    @Override
    protected void updateParentRow() {
        BulkFreeIssueImportRecord record = createRecordFromList();
        importFileItemDto.setGenericImportRecord(record);
        importFileItemDto.getImportFileItem().setComment("");
        importFileItemDto.setImportFileItemImportComment("");
    }

    @Override
    protected void displayUpdateMessage() {
        BulkFreeIssueImportRecord record = createRecordFromList();
        Dialogs.displayInformationMessage(MessagesUtil.getInstance()
                .getMessage("import.edit.bulk.free.issue.item.update.success",
                        new String[] { record.getMeterNum() }),
                MediaResourceUtil.getInstance().getInformationIcon());
    }

    @Override
    protected void prepareImportFileItem(ImportFileItemDto importFileItemDto) {
        BulkFreeIssueImportRecord record = createRecordFromList();
        importFileItemDto.setGenericImportRecord(record);
        importFileItemDto.getImportFileItem().setComment("");
        importFileItemDto.setImportFileItemImportComment("");
    }

    private boolean safeEquals(Object obj1, Object obj2) {
        if (obj1 == null && obj2 == null) return true;
        if (obj1 == null || obj2 == null) return false;
        return obj1.equals(obj2);
    }
}
