package za.co.ipay.metermng.client.view.component;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.logging.Logger;

import com.google.gwt.core.client.GWT;
import com.google.gwt.core.client.Scheduler;
import com.google.gwt.core.client.Scheduler.ScheduledCommand;
import com.google.gwt.event.dom.client.ClickEvent;
import com.google.gwt.event.logical.shared.SelectionEvent;
import com.google.gwt.event.logical.shared.ValueChangeEvent;
import com.google.gwt.i18n.client.DateTimeFormat;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiFactory;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.uibinder.client.UiHandler;
import com.google.gwt.user.client.ui.Button;
import com.google.gwt.user.client.ui.DialogBox;
import com.google.gwt.user.client.ui.FlowPanel;
import com.google.gwt.user.client.ui.Label;
import com.google.gwt.user.client.ui.SuggestBox;
import com.google.gwt.user.client.ui.Widget;
import com.google.gwt.user.datepicker.client.DateBox;
import za.co.ipay.gwt.common.client.Dialogs;
import za.co.ipay.gwt.common.client.factory.ResourcesFactoryUtil;
import za.co.ipay.gwt.common.client.form.Format.FormatUtil;
import za.co.ipay.gwt.common.client.form.HasDirtyData;
import za.co.ipay.gwt.common.client.form.LocalOnlyHasDirtyData;
import za.co.ipay.gwt.common.client.handler.ConfirmHandler;
import za.co.ipay.gwt.common.client.handler.FormDataValueChangeHandler;
import za.co.ipay.gwt.common.client.resource.Messages;
import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.gwt.common.client.widgets.Message;
import za.co.ipay.gwt.common.client.widgets.StrictDateFormat;
import za.co.ipay.gwt.common.client.workspace.SessionCheckCallback;
import za.co.ipay.gwt.common.shared.LookupListItem;
import za.co.ipay.metermng.client.event.UsagePointUpdatedEvent;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.i18n.UiMessages;
import za.co.ipay.metermng.client.i18n.UiMessagesUtil;
import za.co.ipay.metermng.client.resource.image.MediaResource.MediaResourceUtil;
import za.co.ipay.metermng.client.rpc.callback.ClientCallback;
import za.co.ipay.metermng.client.view.component.pricingstructure.AssignUsagePointDialogueValidatePStoMM;
import za.co.ipay.metermng.client.view.workspace.UsagePointWorkspaceView;
import za.co.ipay.metermng.client.widget.pricingstructure.PricingStructureLookup;
import za.co.ipay.metermng.mybatis.generated.model.UpPricingStructure;
import za.co.ipay.metermng.shared.MeterMngStatics;
import za.co.ipay.metermng.shared.UsagePointSuggestion;
import za.co.ipay.metermng.shared.UsagePointSuggestionsOracle;
import za.co.ipay.metermng.shared.dto.MdcChannelReadingsDto;
import za.co.ipay.metermng.shared.dto.MeterData;
import za.co.ipay.metermng.shared.dto.PSDto;
import za.co.ipay.metermng.shared.dto.UpPricingStructureData;
import za.co.ipay.metermng.shared.dto.UsagePointData;
import za.co.ipay.metermng.shared.dto.usagepoint.MeterUpMdcChannelInfo;
import za.co.ipay.metermng.shared.dto.usagepoint.UsagePointFetchDto;

public class AssignUsagePointDialogueBox extends DialogBox implements AssignChannelReadingsComponent{

    private static final Logger logger = Logger.getLogger(AssignUsagePointDialogueBox.class.getName());
    private static final AssignUsagePointDialogueBoxUiBinder uiBinder = GWT.create(AssignUsagePointDialogueBoxUiBinder.class);

    @UiField Message feedBack;
    @UiField(provided = true) SuggestBox sggstBxAssign;
    @UiField Button btnAssign;

    @UiField FlowPanel installDatePanel;
    @UiField Label installdateRequired;
    @UiField DateBox dtbxInstallDate;

    @UiField Label newCurrentPSRequired;
    @UiField FlowPanel currentPricingStructureFlowPanel;
    @UiField FlowPanel futurePricingStructureFlowPanel;
    @UiField(provided = true) PricingStructureLookup currentPricingStructureLookup;
    @UiField(provided = true) PricingStructureLookup futurePricingStructureLookup;


    private UsagePointSuggestion selectedUsagePoint;
    private final ClientFactory clientFactory;
    private UsagePointData usagePointData;
    protected UsagePointWorkspaceView usagePointWorkspaceView;
    private final HasDirtyData hasDirtyData = new LocalOnlyHasDirtyData();
    private UsagePointFetchDto usagePointFetchDto;

    interface AssignUsagePointDialogueBoxUiBinder extends UiBinder<Widget, AssignUsagePointDialogueBox> {
    }

    public AssignUsagePointDialogueBox(ClientFactory clientFactory, UsagePointWorkspaceView usagePointWorkspaceView) {
        this.clientFactory = clientFactory;
        this.usagePointWorkspaceView = usagePointWorkspaceView;
        currentPricingStructureLookup = new PricingStructureLookup(true, hasDirtyData, clientFactory);
        futurePricingStructureLookup = new PricingStructureLookup(false, hasDirtyData, clientFactory);
        sggstBxAssign = new SuggestBox(new UsagePointSuggestionsOracle(clientFactory, true));
        setWidget(uiBinder.createAndBindUi(this));
        dtbxInstallDate.setFormat(
                new StrictDateFormat(DateTimeFormat.getFormat(FormatUtil.getInstance().getDateTimeFormat())));
        feedBack.setVisible(false);
        this.ensureDebugId("assignUsagePointDialogBox");
        addFieldHandlers();
    }

    private void addFieldHandlers() {
        dtbxInstallDate.addValueChangeHandler(new FormDataValueChangeHandler<Date>(hasDirtyData));
        sggstBxAssign.addValueChangeHandler(new FormDataValueChangeHandler<String>(hasDirtyData) {
            @Override
            public void onValueChange(ValueChangeEvent<String> event) {
                super.onValueChange(event);
                if (selectedUsagePoint != null
                        && !sggstBxAssign.getText().equals(selectedUsagePoint.getUsagePointName())) {
                    selectedUsagePoint = null;
                }
            }
        });
    }

    @UiHandler("currentPricingStructureLookup")
    void handleCurrentPricingStructureChange(ValueChangeEvent<String> event) {
        hasDirtyData.setDirtyData(true);
        LookupListItem lookupListItem =  currentPricingStructureLookup.getSelectedPricingStructureItem();
        if (lookupListItem != null) {
            usagePointData.setFirstTariffStartDate((Date)lookupListItem.getExtraInfoMap().get("firstTariffStartDate"));
        }
    }

    public void setUsagePointData(UsagePointData usagePointData) {
        this.usagePointData = usagePointData;
        if (usagePointData != null && usagePointData.getMeterData() != null) {
            showPanels();
        } else {
            removeDateAndPricingStructurePanels();
        }
    }

    public void showPanels(){
        installDatePanel.setVisible(true);
        dtbxInstallDate.setValue(new Date());
        currentPricingStructureFlowPanel.setVisible(true);
        populatePricingStructureListBox(usagePointData.getMeterData());
        currentPricingStructureLookup.clearSelection();
        futurePricingStructureLookup.clearSelection();
        futurePricingStructureLookup.setEnabled(false);
    }

    public void removeDateAndPricingStructurePanels() {
        installDatePanel.removeFromParent();
        currentPricingStructureFlowPanel.removeFromParent();
        futurePricingStructureFlowPanel.removeFromParent();
    }

    private void populatePricingStructureListBox(MeterData meterData) {
        currentPricingStructureLookup.updateLookupList(meterData.getMeterModelData().getServiceResourceId(),
                meterData.getMeterTypeId(), meterData.getMeterModelData().getPaymentModeIds());
        futurePricingStructureLookup.updateLookupList(meterData.getMeterModelData().getServiceResourceId(),
                meterData.getMeterTypeId(), meterData.getMeterModelData().getPaymentModeIds());
    }

    @UiFactory
    public UiMessages getUiMessages() {
        return UiMessagesUtil.getInstance();
    }

    @UiHandler("sggstBxAssign")
    void handleSuggestBox(SelectionEvent<com.google.gwt.user.client.ui.SuggestOracle.Suggestion> se) {
        makeSelection((UsagePointSuggestion) se.getSelectedItem());
    }

    @UiHandler("btnCancel")
    void handleCancelBtn(ClickEvent event) {
        if (hasDirtyData.isDirtyData()) {
            Dialogs.confirmDiscardChanges(new ConfirmHandler() {
                @Override
                public void confirmed(boolean confirm) {
                    if (confirm) {
                        hasDirtyData.setDirtyData(false);
                        hide();
                    }
                }
            });
        } else {
            this.hide();

        }
    }

    private void makeSelection(UsagePointSuggestion selectedUsagePoint) {
        feedBack.setText("");
        feedBack.setVisible(false);
        newCurrentPSRequired.setText("");
        this.selectedUsagePoint = selectedUsagePoint;
        sggstBxAssign.setText(selectedUsagePoint.getUsagePointName());

        boolean isAssigned = true;
        if (selectedUsagePoint.getMeterId() == null && selectedUsagePoint.getCustomerAgreementId() == null) {
            isAssigned = false;
        }
        btnAssign.setEnabled(!isAssigned);
        if (isAssigned) {
            feedBack.setText(MessagesUtil.getInstance().getMessage("usagepoint.assigned"));
            feedBack.setType(Message.MESSAGE_TYPE_ERROR);
            feedBack.setVisible(true);
        } else {
            UpPricingStructureData uppsData = selectedUsagePoint.getUpPricingStructureData();
            currentPricingStructureLookup.setSavedPricingStructureId(uppsData.getPricingStructure().getId());
            if (currentPricingStructureLookup.getSelectedPricingStructureItem() == null
                    && currentPricingStructureLookup.isAttached()) {
                String meterNum = usagePointData.getMeterData().getMeterNum();
                newCurrentPSRequired.setText(MessagesUtil.getInstance().getMessage(
                        "usagepoint.new.pricingstructure.required", new String[] { meterNum,
                                selectedUsagePoint.getUpPricingStructureData().getPricingStructure().getName() }));
            }

            if (uppsData.getFutureUpPricingStructureData() != null) {
                UpPricingStructure futureUPPS = uppsData.getFutureUpPricingStructureData().getUpPricingStructure();
                if (futureUPPS != null && futureUPPS.getPricingStructureId() != null) {
                    futurePricingStructureLookup.setSavedPricingStructureId(futureUPPS.getPricingStructureId());
                    futurePricingStructureLookup.setEnabled(false);
                    futurePricingStructureFlowPanel.setVisible(true);
                }
            }
        }
    }


    @UiHandler("btnAssign")
    void handleAssign(ClickEvent event) {
        clearErrorMessages();
        if (selectedUsagePoint != null) {
            handleOpenWorkspace();
        } else {
            Dialogs.centreErrorMessage(MessagesUtil.getInstance().getMessage("error.no.selection"),
                MediaResourceUtil.getInstance().getErrorIcon(), null);
        }
    }

    private void handleOpenWorkspace() {
        UsagePointData temp = new UsagePointData();
        temp.setId(selectedUsagePoint.getUsagePointId());
        temp.setMeterId(selectedUsagePoint.getMeterId());
        temp.setCustomerAgreementId(selectedUsagePoint.getCustomerAgreementId());
        if(usagePointWorkspaceView.checkDuplicateDataOnOtherTabs(temp)){
            return;
        }
        assign();
    }

    private void assign(){
        if (usagePointData != null && usagePointData.getId() != null
            && usagePointData.getId().equals(selectedUsagePoint.getUsagePointId())) {
            Dialogs.displayInformationMessage(MessagesUtil.getInstance().getMessage("usagepoint.fetch.duplicate", new String[]{selectedUsagePoint.getUsagePointName()}), MediaResourceUtil.getInstance().getInformationIcon(), btnAssign.getAbsoluteLeft(), btnAssign.getAbsoluteTop()+btnAssign.getOffsetHeight(), null);
            return;
        }

        usagePointFetchDto = new UsagePointFetchDto();
        usagePointFetchDto.setUsagePointId(selectedUsagePoint.getUsagePointId());
        usagePointFetchDto.setUsagePointName(selectedUsagePoint.getUsagePointName());
        usagePointFetchDto.setLastCyclicChargeDate(selectedUsagePoint.getLastCyclicChargeDate());

        if (usagePointData != null && usagePointData.getMeterData() != null) {
            usagePointFetchDto.setMeterId(usagePointData.getMeterData().getId());
            usagePointFetchDto.setMeterNum(usagePointData.getMeterData().getMeterNum());
            usagePointFetchDto.setMeterModelId(usagePointData.getMeterData().getMeterModelData().getId());
            usagePointFetchDto.setServiceResourceId(usagePointData.getMeterData().getMeterModelData().getServiceResourceId());
            usagePointFetchDto.setMeterModelName(usagePointData.getMeterData().getMeterModelData().getName());
            usagePointFetchDto.setMdcId(usagePointData.getMeterData().getMeterModelData().getMdcId());
            usagePointFetchDto.setMdcName(usagePointData.getMeterData().getMeterModelData().getMdcName());
            //check pricing structure
            if (selectedUsagePoint.getUpPricingStructureData().getFutureUpPricingStructureData() != null) {
                if (futurePricingStructureLookup.getSelectedPricingStructureItem() == null) {
                    usagePointFetchDto.setFuturePricingStructureId(null);
                } else {
                    usagePointFetchDto.setFuturePricingStructureId(selectedUsagePoint.getUpPricingStructureData()
                            .getFutureUpPricingStructureData().getUpPricingStructure().getPricingStructureId());
                }
            }

            if (currentPricingStructureLookup.getSelectedPricingStructureItem() !=  null) {
                //i.e. have already selected a new pricing structure
                setCurrentSelectedPricingStructureInDto();
            } else {
                //check pricing structure
                currentPricingStructureLookup.setSavedPricingStructureId(selectedUsagePoint
                        .getUpPricingStructureData()
                        .getPricingStructure()
                        .getId());
            }
            checkInstallDate();
        } else {
            // no meter data - panels not even showing
            usagePointFetchDto.setCurrentPricingStructureId(selectedUsagePoint.getUpPricingStructureData().getUpPricingStructure().getPricingStructureId());
            if (selectedUsagePoint.getUpPricingStructureData().getFutureUpPricingStructureData() != null) {
                usagePointFetchDto.setFuturePricingStructureId(selectedUsagePoint.getUpPricingStructureData().getFutureUpPricingStructureData().getUpPricingStructure().getPricingStructureId());
            }
            checkCustomerLink();
        }
    }

    private void setCurrentSelectedPricingStructureInDto() {
        LookupListItem selectedPricingStructureItem  = currentPricingStructureLookup.getSelectedPricingStructureItem();
        Long selectedPricingStructureID = Long.valueOf(selectedPricingStructureItem.getValue());
        if (selectedPricingStructureID > 0) {
            usagePointFetchDto.setFirstTariffStartDate((Date)selectedPricingStructureItem.getExtraInfoMap()
                    .get("firstTariffStartDate"));
        }
        if (!selectedPricingStructureID.equals(selectedUsagePoint.getUpPricingStructureData().getUpPricingStructure()
                .getPricingStructureId())) {
            futurePricingStructureFlowPanel.setVisible(false);
            usagePointFetchDto.setCurrentPricingStructureId(selectedPricingStructureID);
            usagePointFetchDto.setFuturePricingStructureId(null);
            usagePointFetchDto.setPricingStructureName(selectedPricingStructureItem.getDisplayString());
        } else {
            usagePointFetchDto.setCurrentPricingStructureId(null);
            usagePointFetchDto.setPricingStructureName(null);
        }
    }

    private void checkInstallDate() {
        final Date installDate = dtbxInstallDate.getValue();
        if (installDate == null) {
            displayErrorMsg("meter.date.install.missing");
            return;
        }
        boolean valid = true;
        String errorMsg = "";
        if (currentPricingStructureFlowPanel.isVisible()) {
            if (!clientFactory.getUser().hasPermission(MeterMngStatics.ACCESS_PERMISSION_UP_PRICING_STRUCT)) {
                Dialogs.displayErrorMessage(MessagesUtil.getInstance().getMessage("error.pricing.structure.accessdenied"),
                        MediaResourceUtil.getInstance().getErrorIcon(), null);
                return;
            }
            if (currentPricingStructureLookup.getSelectedPricingStructureItem() == null) {
                newCurrentPSRequired.setText(MessagesUtil.getInstance().getMessage("usagepoint.ps.required"));
                valid = false;
            }
        }

        if (valid) {
            processValid(installDate);
        } else {
            displayErrorMsg(errorMsg);
        }
    }

    private void processValid(final Date installDate) {
        Dialogs.confirm (
                new String[]{
                        ResourcesFactoryUtil.getInstance().getMessages().getMessage("question.confirm.installation.date.1",new String[]{usagePointData.getMeterData().getMeterNum(),FormatUtil.getInstance().formatDate(installDate)
                                ,FormatUtil.getInstance().formatTime(installDate)}),
                                ResourcesFactoryUtil.getInstance().getMessages().getMessage("question.confirm.installation.date.2")},
                                ResourcesFactoryUtil.getInstance().getMessages().getMessage("option.confirm"),
                                ResourcesFactoryUtil.getInstance().getMessages().getMessage("option.no"),
                                ResourcesFactoryUtil.getInstance().getQuestionIcon(),
                                new ConfirmHandler() {
                    @Override
                    public void confirmed(boolean confirm) {
                        if (confirm) {
                            checkInstallDateToFirstTariffStart(installDate);
                        } else {
                            dtbxInstallDate.setFocus(true);
                        }
                    }
        });
    }

    private void checkInstallDateToFirstTariffStart(final Date installDate) {
        //check that new installation date is not before the PS startdate
        Date firstTariffStartDate = usagePointFetchDto.getFirstTariffStartDate();
        if (installDate.before(firstTariffStartDate)) {
            Date lastCyclicChargeDate = usagePointFetchDto.getLastCyclicChargeDate();
            String message = UsagePointWorkspaceView.constructMessageInstallDtVsTariffStart(firstTariffStartDate, lastCyclicChargeDate);
            Messages messages = MessagesUtil.getInstance();
            Dialogs.confirm(message,
                    messages.getMessage("button.yes"),
                    messages.getMessage("button.no"),
                    MediaResourceUtil.getInstance().getQuestionIcon(),
                    new ConfirmHandler() {
                @Override
                public void confirmed(boolean confirm) {
                    if(confirm) {
                        usagePointFetchDto.setInstallDate(installDate);
                        checkCustomerLink();
                    } else {
                        dtbxInstallDate.setFocus(true);
                    }
                }
            });
        } else {
            usagePointFetchDto.setInstallDate(installDate);
            checkCustomerLink();
        }
    }

    private void checkCustomerLink() {
        if (usagePointData != null && usagePointData.getCustomerAgreementData() != null) {
            Dialogs.confirm (
                    ResourcesFactoryUtil.getInstance().getMessages().getMessage("question.confirm.link.to.customer",new String[]{usagePointData.getCustomerAgreementData().getCustomerData().getName()}),
                    ResourcesFactoryUtil.getInstance().getMessages().getMessage("option.confirm"),
                    ResourcesFactoryUtil.getInstance().getMessages().getMessage("option.no"),
                    ResourcesFactoryUtil.getInstance().getQuestionIcon(),
                    new ConfirmHandler() {
                        @Override
                        public void confirmed(boolean confirm) {
                            if (confirm) {
                                usagePointFetchDto.setCustomerAgreementId(usagePointData.getCustomerAgreementData().getId());
                                checkPotentialRegReadAlignment();
                            }
                        }
                    });
        } else {
            checkPotentialRegReadAlignment();
        }
    }

    private void checkPotentialRegReadAlignment() {
        if (usagePointFetchDto.getMeterId() == null) {
            fireUpdateEvent(null);
        } else {
            // for register reading PS or Model with MDC with mdcChannels: check correlation between billingDets
            //for currentPS sending in new date and not installDate because on takeOn of data from other systems,
            //sometimes bring over meters with very old installDates but don't bother to pull in full PS history.
            List<PSDto> pSDtos = new ArrayList<>();
            if (usagePointFetchDto.getCurrentPricingStructureId() != null) {
                pSDtos.add(new PSDto(usagePointFetchDto.getCurrentPricingStructureId(), new Date()));
            } else {
                UpPricingStructureData uppsData = selectedUsagePoint.getUpPricingStructureData();
                pSDtos.add(new PSDto(uppsData.getUpPricingStructure().getPricingStructureId(), new Date()));
                UpPricingStructureData futurePsData = uppsData.getFutureUpPricingStructureData();
                if (futurePsData != null) {
                    pSDtos.add(new PSDto(futurePsData.getUpPricingStructure().getPricingStructureId(), futurePsData.getUpPricingStructure().getStartDate()));
                }
            }
            new AssignUsagePointDialogueValidatePStoMM(this).isregReadPsSameBillingDetsAsMeterModel(clientFactory, pSDtos,
                    usagePointFetchDto.getMdcId(), clientFactory.getUser().getUserName(), logger);

        }
    }

    public void checkChannelInfoB4Fetch() {
            final AssignUsagePointDialogueBox parent = this;
            logger.info("AssignUsagePointDialogueBox: getChannels: pricingstructure id= " +  usagePointFetchDto.getCurrentPricingStructureId() + " metermodelID= " + usagePointFetchDto.getMeterModelId() + "  installationDate= " + usagePointFetchDto.getInstallDate().toString());
            MeterUpMdcChannelInfo meterUpMdcChannelInfo = new MeterUpMdcChannelInfo(usagePointFetchDto.getMeterId(),
                    usagePointFetchDto.getMeterModelId(),
                    usagePointFetchDto.getUsagePointId(),
                    usagePointFetchDto.getCurrentPricingStructureId(),
                    null,
                    usagePointFetchDto.getInstallDate());
            clientFactory.getLookupRpc().getMeterUpMdcChannelInfo(meterUpMdcChannelInfo, new ClientCallback<MeterUpMdcChannelInfo>() {
                @Override
                public void onSuccess(MeterUpMdcChannelInfo result) {
                    if (result == null || result.getChannelList() == null || result.getChannelList().isEmpty()) {
                        logger.info("getMeterUpMdcChannelInfo: result is null");
                        fireUpdateEvent(null);
                        return;
                    } else {
                        logger.info("getMeterUpMdcChannelInfo: result.getChannelList() size=" + result.getChannelList().size());

                        //get initial readings for the channels
                        final AssignChannelReadingsDialogueBox assignChannelReadings =
                                new AssignChannelReadingsDialogueBox(parent, result, usagePointFetchDto.getMeterNum(),
                                        usagePointFetchDto.getUsagePointName(),
                                        usagePointFetchDto.getMeterModelName(),
                                        usagePointFetchDto.getMdcName(),
                                        usagePointFetchDto.getPricingStructureName(),
                                        usagePointFetchDto.getServiceResourceId());
                            Scheduler.get().scheduleDeferred(new ScheduledCommand() {
                                @Override
                                public void execute() {
                                    assignChannelReadings.center();
                                    assignChannelReadings.show();
                                }
                            });
                    }
                }

                @Override
                public void onFailure(Throwable caught) {
                    super.onFailure(caught);
                }
            });
    }

    @Override
    public void fireUpdateEvent(MeterUpMdcChannelInfo meterUpMdcChannelInfo) {
        //fireUsagePointUpdatedEvent
        final int left = this.getAbsoluteLeft() + (this.getOffsetWidth() / 2);
        final int top = this.getAbsoluteTop();
        List<MdcChannelReadingsDto> channelList = null;
        if (meterUpMdcChannelInfo != null) {
            channelList = meterUpMdcChannelInfo.getChannelList();
        }
        final List<MdcChannelReadingsDto> finalChannelList = channelList;
        SessionCheckCallback sessionCheckCallback = new SessionCheckCallback() {
            @Override
            public void callback(SessionCheckResolution sessionCheckResolution) {
                usagePointFetchDto.setAccessGroupId(clientFactory.getUser().getSessionGroupId());
                // Even though this is a get..() it also does the UP update. usagePointService.updateUsagePointFromNewFetch
                clientFactory.getSearchRpc().getUsagePointDataforFetchUsagePoint(usagePointFetchDto, finalChannelList, new ClientCallback<UsagePointData>() {
                    @Override
                    public void onSuccess(UsagePointData result) {
                        int updateType = UsagePointUpdatedEvent.FETCH_USAGE_POINT;
                        UsagePointUpdatedEvent updateevent = new UsagePointUpdatedEvent(usagePointWorkspaceView, result, updateType);
                        updateevent.setLeft(left);
                        updateevent.setTop(top);
                        clientFactory.getEventBus().fireEvent(updateevent);
                        AssignUsagePointDialogueBox.this.hide();
                    }
                });
            }
        };
        clientFactory.handleSessionCheckCallback(sessionCheckCallback);
    }

    private void displayErrorMsg(String code) {
        if (code == null || code.isEmpty()) {
            return;
        }
        feedBack.setText(MessagesUtil.getInstance().getMessage(code));
        feedBack.setType(Message.MESSAGE_TYPE_ERROR);
        feedBack.setVisible(true);
    }

    public void clearFields() {
        clearErrorMessages();
        selectedUsagePoint = null;
        sggstBxAssign.getValueBox().setText("");
    }

    public void clearErrorMessages() {
        feedBack.setText("");
        feedBack.setVisible(false);
        newCurrentPSRequired.setText("");
    }
}
