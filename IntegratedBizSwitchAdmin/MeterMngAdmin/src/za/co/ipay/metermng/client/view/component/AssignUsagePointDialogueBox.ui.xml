<!DOCTYPE ui:UiBinder SYSTEM "http://dl.google.com/gwt/DTD/xhtml.ent">
<ui:UiBinder xmlns:ui="urn:ui:com.google.gwt.uibinder"
     xmlns:g="urn:import:com.google.gwt.user.client.ui"
     xmlns:p2="urn:import:com.google.gwt.user.datepicker.client"
     xmlns:p1="urn:import:za.co.ipay.gwt.common.client.widgets"
     xmlns:pricingstructure="urn:import:za.co.ipay.metermng.client.widget.pricingstructure">
    <ui:style>
        
    </ui:style>
  
  <ui:with field="msg" type="za.co.ipay.metermng.client.i18n.UiMessages" />
  
    <g:HTMLPanel>        
       <table>
           <tr>
               <td>
                   <g:VerticalPanel horizontalAlignment="ALIGN_CENTER" spacing="10">
                       <p1:Message ui:field="feedBack" debugId="feedback"/>
                       <g:Label text="{msg.getUsagePointNameFetch}:" styleName="gwt-Label-bold" horizontalAlignment="ALIGN_CENTER"></g:Label>
                       <g:FlowPanel>
                            <g:SuggestBox debugId="assignUsagePointBox" ui:field="sggstBxAssign" animationEnabled="true" autoSelectEnabled="false" styleName="gwt-TextBox-ipay" />
                       </g:FlowPanel>
                       <g:FlowPanel ui:field="installDatePanel" visible="false">
                            <g:Label text="{msg.getMeterSpecifyInstallDate}" styleName="gwt-Label-bold" horizontalAlignment="ALIGN_CENTER"></g:Label>
                            <g:Label horizontalAlignment="ALIGN_CENTER" ui:field="installdateRequired"/>
                            <p2:DateBox debugId="assignInstallDate" styleName="gwt-TextBox" ui:field="dtbxInstallDate" />
                       </g:FlowPanel>
                       <g:FlowPanel ui:field="currentPricingStructureFlowPanel" visible="false">
                            <g:Label horizontalAlignment="ALIGN_CENTER" ui:field="newCurrentPSRequired" debugId="newCurrentPSRequired" styleName="errorStatus"/>
                            <g:Label  styleName="gwt-Label-bold" text="{msg.getMeterNewCurrentSelectPS}"/>
                           <pricingstructure:PricingStructureLookup ui:field="currentPricingStructureLookup" debugId="currentPricingStructureLookup"/>
                       </g:FlowPanel>
                       <g:FlowPanel ui:field="futurePricingStructureFlowPanel" visible="false">
                            <g:Label horizontalAlignment="ALIGN_CENTER" ui:field="newFuturePSRequired" debugId="newFuturePSRequired" styleName="errorStatus"/>
                            <g:Label  styleName="gwt-Label-bold" text="{msg.getMeterNewFutureSelectPS}"/>
                            <pricingstructure:PricingStructureLookup ui:field="futurePricingStructureLookup" debugId="futurPricingStructureLookup"/>
                       </g:FlowPanel>
                       <g:FlowPanel>
                           <g:Button debugId="assignUsagePointButton" text="{msg.getUsagepointFetch}" styleName="gwt-Button-ipay" ui:field="btnAssign"/>
                           <g:Button debugId="btnCancel" styleName="gwt-Button-ipay" ui:field="btnCancel" visible="true" text="{msg.getCancelButton}"/>
                       </g:FlowPanel>

                   </g:VerticalPanel>
               </td>
           </tr>
       </table>
    </g:HTMLPanel>
</ui:UiBinder> 