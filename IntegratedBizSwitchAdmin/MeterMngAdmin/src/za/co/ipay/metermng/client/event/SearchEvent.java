package za.co.ipay.metermng.client.event;

import za.co.ipay.metermng.shared.dto.search.SearchResultType;

import com.google.gwt.event.shared.GwtEvent;

public class SearchEvent extends GwtEvent<SearchEventHandler> {

    public static Type<SearchEventHandler> TYPE = new Type<SearchEventHandler>();
    
    private String searchType;
    private SearchResultType dataType;
    private String searchText;
    
    public SearchEvent(String type) {
        this.searchType = type;
    }
    
    public SearchEvent(String type, SearchResultType dataType, String searchText) {
        this.searchType = type;
        this.dataType = dataType;
        this.searchText = searchText;
    }

    public String getSearchType() {
        return searchType;
    }    

    public SearchResultType getDataType() {
        return dataType;
    }    

    public String getSearchText() {
        return searchText;
    }

    @Override
    public Type<SearchEventHandler> getAssociatedType() {
        return TYPE;
    }

    @Override
    protected void dispatch(SearchEventHandler handler) {
        handler.processSearchEvent(this);
    }
}
