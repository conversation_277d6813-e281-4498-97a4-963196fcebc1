package za.co.ipay.metermng.client.view.component.importfile.actionparamimpl.bulkfreeissue;

import com.google.gwt.core.client.GWT;
import com.google.gwt.event.dom.client.ClickHandler;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.user.client.Timer;
import com.google.gwt.user.client.ui.Label;
import com.google.gwt.user.client.ui.ProvidesResize;
import com.google.gwt.user.client.ui.RequiresResize;
import com.google.gwt.user.client.ui.VerticalPanel;
import com.google.gwt.user.client.ui.Widget;

import za.co.ipay.gwt.common.client.form.FormElement;
import za.co.ipay.gwt.common.client.form.HasDirtyData;
import za.co.ipay.gwt.common.client.form.HasDirtyDataManager;
import za.co.ipay.gwt.common.client.form.LocalOnlyHasDirtyDataManager;
import za.co.ipay.gwt.common.client.form.FormDataClickHandler;
import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.gwt.common.client.widgets.IpayIntegerBox;
import za.co.ipay.gwt.common.client.widgets.IpayTextBox;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.view.component.SpecialActionsReasonComponent;
import za.co.ipay.metermng.client.view.component.importfile.actionparamimpl.ActionParamsComponent;
import za.co.ipay.metermng.shared.dto.BulkParamRecord;
import za.co.ipay.metermng.shared.integration.bulkfreeissue.BulkFreeIssueParamRecord;

public class BulkFreeIssueParamsPanel extends ActionParamsComponent implements ProvidesResize, RequiresResize {

    @UiField VerticalPanel bulkFreeIssueParamsPanel;
    @UiField Label bulkFreeIssueParamHeading;
    @UiField FormElement unitsElement;
    @UiField IpayIntegerBox unitsBox;
    @UiField FormElement descriptionElement;
    @UiField IpayTextBox descriptionBox;
    @UiField FormElement userReferenceElement;
    @UiField IpayTextBox userReferenceBox;

    private BulkFreeIssueParamRecord bulkFreeIssueParamRecord;
    private HasDirtyData hasDirtyData;
    HasDirtyDataManager hasDirtyDataManager;
    private SpecialActionsReasonComponent specialActionsReasonComponent;

    private static BulkFreeIssueParamsPanelUiBinder uiBinder = GWT.create(BulkFreeIssueParamsPanelUiBinder.class);

    interface BulkFreeIssueParamsPanelUiBinder extends UiBinder<Widget, BulkFreeIssueParamsPanel> {
    }

    public BulkFreeIssueParamsPanel(ClientFactory clientFactory, String fileName,
            BulkFreeIssueParamRecord bulkFreeIssueParamRecord) {
        super();
        this.clientFactory = clientFactory;
        this.bulkFreeIssueParamRecord = bulkFreeIssueParamRecord;
        hasDirtyDataManager = new LocalOnlyHasDirtyDataManager();
        hasDirtyData = hasDirtyDataManager.createAndRegisterHasDirtyData();
        initWidget(uiBinder.createAndBindUi(this));
        bulkFreeIssueParamHeading
                .setText(MessagesUtil.getInstance().getMessage("bulk.free.issue.header", new String[] { fileName }));
        
        // Initialize special actions reason component
        specialActionsReasonComponent = new SpecialActionsReasonComponent(clientFactory, hasDirtyData);
        bulkFreeIssueParamsPanel.add(specialActionsReasonComponent);
        
        addFieldHandlers();
        mapDataToForm();
    }

    private void addFieldHandlers() {
        ClickHandler formDataClickHandler = new FormDataClickHandler(hasDirtyData);
        
        unitsBox.addValueChangeHandler(event -> {
            hasDirtyData.setDirtyData(true);
            unitsElement.clearErrorMsg();
        });
        
        descriptionBox.addValueChangeHandler(event -> {
            hasDirtyData.setDirtyData(true);
            descriptionElement.clearErrorMsg();
        });
        
        userReferenceBox.addValueChangeHandler(event -> {
            hasDirtyData.setDirtyData(true);
            userReferenceElement.clearErrorMsg();
        });
    }

    @Override
    public void mapDataToForm() {
        clearErrorMessages();
        if (bulkFreeIssueParamRecord != null) {
            unitsBox.setValue(bulkFreeIssueParamRecord.getUnits());
            descriptionBox.setValue(bulkFreeIssueParamRecord.getDescription());
            userReferenceBox.setValue(bulkFreeIssueParamRecord.getUserReference());
            
            if (bulkFreeIssueParamRecord.getSpecialActionReasonsId() != null) {
                specialActionsReasonComponent.setSpecialActionReasonsId(bulkFreeIssueParamRecord.getSpecialActionReasonsId());
            }
            if (bulkFreeIssueParamRecord.getReasonText() != null) {
                specialActionsReasonComponent.setReasonText(bulkFreeIssueParamRecord.getReasonText());
            }
        }
    }

    private void clearErrorMessages() {
        unitsElement.clearErrorMsg();
        descriptionElement.clearErrorMsg();
        userReferenceElement.clearErrorMsg();
    }

    @Override
    public boolean validateForm() {
        boolean isValid = true;
        clearErrorMessages();

        // Validate units (optional but if provided must be positive)
        if (unitsBox.getValue() != null && unitsBox.getValue() <= 0) {
            unitsElement.showErrorMsg(MessagesUtil.getInstance().getMessage("bulk.free.issue.units.validation.positive"));
            isValid = false;
        }

        return isValid;
    }

    @Override
    public BulkParamRecord mapFormToData() {
        bulkFreeIssueParamRecord = new BulkFreeIssueParamRecord();
        
        bulkFreeIssueParamRecord.setUnits(unitsBox.getValue());
        bulkFreeIssueParamRecord.setDescription(descriptionBox.getValue());
        bulkFreeIssueParamRecord.setUserReference(userReferenceBox.getValue());
        bulkFreeIssueParamRecord.setSpecialActionReasonsId(specialActionsReasonComponent.getSpecialActionReasonsId());
        bulkFreeIssueParamRecord.setReasonText(specialActionsReasonComponent.getReasonText());
        
        return bulkFreeIssueParamRecord;
    }

    @Override
    public boolean checkDirtyData() {
        return hasDirtyData.isDirtyData();
    }

    @Override
    public void onResize() {
        new Timer() {
            @Override
            public void run() {
                bulkFreeIssueParamsPanel.setWidth("100%");
            }
        }.schedule(100);
    }
}
