<!DOCTYPE ui:UiBinder SYSTEM "http://dl.google.com/gwt/DTD/xhtml.ent">
<ui:UiBinder xmlns:ui="urn:ui:com.google.gwt.uibinder" 
             xmlns:g="urn:import:com.google.gwt.user.client.ui"
             xmlns:p3="urn:import:za.co.ipay.gwt.common.client.form"
             xmlns:p2="urn:import:za.co.ipay.gwt.common.client.widgets"
             xmlns:p1="urn:import:za.co.ipay.metermng.client.view.component.group.entity.ndp">
    <ui:style>  
       .buttonBottomSpacing {
            margin-bottom: 2px;
        }
    </ui:style>

    <ui:with field="msg" type="za.co.ipay.metermng.client.i18n.UiMessages" />
    
       <g:VerticalPanel>
            <g:HTML text="{msg.getNdpActiveInstruction}" styleName="dataDescription"/>
            
            <g:FlowPanel styleName="formElementsPanel" debugId="scheduleInfoPanel">
                <p3:FormElement ui:field="activeElement" labelText="{msg.getNdpScheduleActive}:"  helpMsg="{msg.getNdpScheduleActiveHelp}">
                    <g:CheckBox ui:field="scheduleActiveBox" checked="false"  debugId="scheduleActiveBox"/>
                </p3:FormElement>
            </g:FlowPanel>
            
            
            <g:HTMLPanel styleName="mainButtons {style.buttonBottomSpacing}">
                <g:Button ui:field="btnSave" text="{msg.getUpdateButton}" debugId="btnSave"/>
                <g:Button ui:field="btnCancel" text="{msg.getCancelButton}" debugId="btnCancel"/>
            </g:HTMLPanel>
            
                

            <p1:SchedulePanel ui:field="schedulePanel" visible="true" debugId="schedulePanel"></p1:SchedulePanel>
                    
    </g:VerticalPanel>

</ui:UiBinder> 