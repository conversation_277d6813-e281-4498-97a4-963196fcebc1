package za.co.ipay.metermng.client.view;

import java.util.logging.Logger;

import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.history.SelectAccessGroupPlace;
import za.co.ipay.metermng.client.view.component.BaseComponent;
import za.co.ipay.metermng.client.view.menu.Header;
import za.co.ipay.metermng.client.view.menu.SidePanelStack;
import za.co.ipay.metermng.shared.dto.user.MeterMngUser;

import com.google.gwt.core.client.GWT;
import com.google.gwt.event.dom.client.ClickEvent;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.uibinder.client.UiHandler;
import com.google.gwt.user.client.Window;
import com.google.gwt.user.client.ui.Composite;
import com.google.gwt.user.client.ui.DockLayoutPanel;
import com.google.gwt.user.client.ui.FocusPanel;
import com.google.gwt.user.client.ui.RequiresResize;
import com.google.gwt.user.client.ui.SimpleLayoutPanel;
import com.google.gwt.user.client.ui.Widget;

public class PrimaryLayoutView extends BaseComponent implements RequiresResize {

    private Logger logger = Logger.getLogger("PrimaryLayoutView");

    interface PrimaryLayoutViewUiBinder extends UiBinder<Widget, PrimaryLayoutView> {
    }

    private static PrimaryLayoutViewUiBinder uiBinder = GWT.create(PrimaryLayoutViewUiBinder.class);

    @UiField DockLayoutPanel primaryLayoutPanel;
    @UiField(provided=true) Header header;
    @UiField SimpleLayoutPanel sidePanel;
    @UiField FocusPanel opcl;
    @UiField(provided = true) Composite workspaceContainerComposite;
    
    private SidePanelStack sidePanelStack;
    boolean open = true;
    
    public PrimaryLayoutView(ClientFactory clientFactory, MeterMngUser user) {
        logger.info("Creating PrimaryLayoutView: "+clientFactory);
        header = new Header(clientFactory);
        header.setUser(user);
        sidePanelStack = new SidePanelStack(clientFactory, user);
        workspaceContainerComposite = clientFactory.getWorkspaceContainer().asComposite();
        initWidget(uiBinder.createAndBindUi(this));
        int windowhight = Window.getClientHeight();
        header.setParentView(this);
        showStackStyleSidePanel();
        opcl.setHeight(windowhight + "px");
    }

    @UiHandler("opcl")
    void handleOpenClose(ClickEvent ce) {
        logger.info("handleOpenClose: "+open);
        if (open) {
            sidePanel.clear();
            open = false;
            opcl.setStylePrimaryName("close-border-closed");
            primaryLayoutPanel.setWidgetSize(sidePanel, 0.1);
            primaryLayoutPanel.forceLayout();            
        } else {
            sidePanel.add(sidePanelStack);
            open = true;
            opcl.setStylePrimaryName("close-border");
            primaryLayoutPanel.setWidgetSize(sidePanel, 16);
            primaryLayoutPanel.forceLayout();
        }
    }

    @Override
    public void onResize() {
        primaryLayoutPanel.forceLayout();
    }
    
    public void setCurrentGroup(String groupName) {
        header.setCurrentGroup(groupName);
    }
    
    public void showStackStyleSidePanel() {
        sidePanel.clear();
        sidePanel.add(sidePanelStack);
    }
    
    public int getSidePanelStackWidth() {
        return sidePanelStack.getOffsetWidth();
    }
    
    public void displayChangeGroupScreen(SelectAccessGroupPlace p) {
        header.displayChangeGroupScreen(p);
    }
    
    public void displayChangePasswordScreen(MeterMngUser user) {
        header.displayChangePasswordScreen(user);
    }
    
    public void displayChangePasswordScreen() {
        header.displayChangePasswordScreen();
    }
    
    public void setLogo(String logo) {
        header.setLogo(logo);
    }
}
