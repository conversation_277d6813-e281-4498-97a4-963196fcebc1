package za.co.ipay.metermng.client.event;

import com.google.gwt.event.shared.GwtEvent;

public class AuxTypesUpdatedEvent extends GwtEvent<AuxTypesUpdatedEventHandler> {

    public static Type<AuxTypesUpdatedEventHandler> TYPE = new Type<AuxTypesUpdatedEventHandler>();
    
    public AuxTypesUpdatedEvent() {
        
    }
    
	@Override
    public Type<AuxTypesUpdatedEventHandler> getAssociatedType() {
        return TYPE;
    }

    @Override
    protected void dispatch(AuxTypesUpdatedEventHandler handler) {
        handler.processAuxTypesUpdatedEvent(this);
    }


}
