<!DOCTYPE ui:UiBinder SYSTEM "http://dl.google.com/gwt/DTD/xhtml.ent">
<ui:UiBinder xmlns:ui="urn:ui:com.google.gwt.uibinder" xmlns:g="urn:import:com.google.gwt.user.client.ui"
             xmlns:p1="urn:import:za.co.ipay.gwt.common.client.form" xmlns:p2="urn:import:com.google.gwt.user.datepicker.client"
             xmlns:p3="urn:import:za.co.ipay.gwt.common.client.widgets" xmlns:ipaycomp="urn:import:za.co.ipay.metermng.client.view.component"
             xmlns:ipayusercustomfields="urn:import:za.co.ipay.metermng.client.view.component.usercustomfields"
             xmlns:pricingstructure="urn:import:za.co.ipay.metermng.client.widget.pricingstructure">

    <ui:style>
        .rightalign {
          text-align: right;
        }       
        .unitsymbol {
          padding-left: 2px;
          padding-top: 3px;
        }       
    </ui:style>
    
	<ui:with field="msg" type="za.co.ipay.metermng.client.i18n.UiMessages" />

	<g:DisclosurePanel open="true" styleName="gwt-DisclosurePanel-component" ui:field="usagePointDisclosurePanel"
		debugId="usagePointDisclosurePanel">
		<g:customHeader>
			<g:FlowPanel debugId="usagePointDisclosurePanel" styleName="gwt-DisclosurePanel-component .header">
				<g:HorizontalPanel verticalAlignment="ALIGN_MIDDLE">
					<g:Image styleName="horizontalFlow" ui:field="openorclosearrow" width="12px" />
					<g:Image ui:field="upImage" />
					<g:Label styleName="gwt-DisclosurePanel-component .header" width="" ui:field="headerLabel" text="{msg.getUsagePointTitle}:"
						horizontalAlignment="ALIGN_LEFT" />
				</g:HorizontalPanel>
			</g:FlowPanel>
		</g:customHeader>
        
		<g:FlowPanel>
		    <g:FlowPanel styleName="disclosureContent" ui:field="contentpanel">
		        <g:FlowPanel styleName="formElementsPanel">
                    <g:FlowPanel ui:field="assignLinkRow" visible="false">
                        <p1:FormRowPanel width="100%"  styleName="{style.rightalign}" >
                            <p1:FormElement ui:field="assignLinkElement" helpMsg="{msg.getUsagepointFetchHelp}">         
                            <g:Label debugId="fetchUsagePointLabel" text="{msg.getUsagepointFetch}" ui:field="lblFetch" styleName="gwt-Label-iPayLink" horizontalAlignment="ALIGN_RIGHT"/>
                            </p1:FormElement> 
                        </p1:FormRowPanel>
                    </g:FlowPanel>
		            <g:FlowPanel>
		                <p1:FormRowPanel>
		                    <p1:FormElement debugId="activeElement" ui:field="activeElement" helpMsg="{msg.getUsagePointActiveHelp}">
		                        <p3:IpayActiveCheckBox debugId="activeBox" text="{msg.getUsagePointActive}" checked="false"
		                            ui:field="chckbxActive" styleName="error" />
                            </p1:FormElement>
                            <p1:FormElement ui:field="installationDateElement" labelText="{msg.getUsagePointMeterInstallationDate}" helpMsg="{msg.getUsagePointMeterInstallationDateHelp}" 
                            	debugId="installationDateElement">
                                <p2:DateBox  styleName="gwt-TextBox" ui:field="dtbxMeterInstallationDate" enabled="false" debugId="dtbxMeterInstallationDate"/>
                            </p1:FormElement>                  
                        </p1:FormRowPanel>
		                <p1:FormRowPanel ui:field="activationDatePanel" visible="false">
                            <p1:FormElement labelText="{msg.getUsagePointFieldMeterActivationDate}:">
                                <p2:DateBox debugId="dtbxMeterActivationDate" styleName="gwt-TextBox" ui:field="dtbxMeterActivationDate" enabled="false"/>
                            </p1:FormElement>                  
                        </p1:FormRowPanel>
					<p1:FormRowPanel ui:field="deactivateReasonPanel" visible="false"/>
                    <p1:FormRowPanel ui:field="activateReasonPanel" visible="false"/>
                    </g:FlowPanel>
                    <g:FlowPanel ui:field="usagePointTopContainer" width="100%" height="100%">
                        <p1:FormRowPanel>
                            <p1:FormElement debugId="usagePointNameElement" ui:field="usagePointNameElement" helpMsg="{msg.getUsagePointNameHelp}" labelText="{msg.getUsagePointName}: "
                                required="true">
                                <g:TextBox debugId="usagePointNameBox" ui:field="txtbxUsagepointname" styleName="gwt-TextBox" />
                            </p1:FormElement>
                        </p1:FormRowPanel>
                    </g:FlowPanel>
                    
                    <ipaycomp:MridComponent ui:field="mridComponent"/>
                    
                    <p1:FormGroupPanel labelText="Pricing Structure">
                    	<p1:FormRowPanel>
                    		<p1:FormElement ui:field="currentPSElement" helpMsg="{msg.getUsagePointPricingStructureHelp}"
                                labelText="{msg.getUsagePointPricingStructure}: " required="true" debugId="currentPSElement">
                                <pricingstructure:PricingStructureLookup ui:field="currentPricingStructureLookup" debugId="currentPricingStructureLookup"/>
                            </p1:FormElement>
                            <p1:FormElement ui:field="currentPSStartDateElement" debugId="currentPSStartDateElement" labelText="{msg.getUsagePointPSStartDateLbl}:" helpMsg="{msg.getUsagePointPSStartDateHelp}">
				          		<p2:DateBox ui:field="currentPricingStructureStartDate" debugId="currentPricingStructureStartDate" styleName="gwt-TextBox" enabled="false"/>
				        	</p1:FormElement>
                        </p1:FormRowPanel>
                        
                        <p1:FormRowPanel ui:field="currentPricingChangeReasonPanel" visible="false"/>
                        
                        <p1:FormRowPanel>
                    		<p1:FormElement ui:field="futurePSElement" helpMsg="{msg.getUsagePointFuturePSListHelp}"
                                            labelText="{msg.getUsagePointFuturePricingStructure}: " debugId="futurePSElement">
                                <pricingstructure:PricingStructureLookup ui:field="futurePricingStructureLookup" debugId="futurePricingStructureLookup"/>
                            </p1:FormElement>
                            <p1:FormElement ui:field="futurePSStartDateElement" debugId="futurePSStartDateElement" labelText="{msg.getUsagePointPSStartDateLbl}:" helpMsg="{msg.getUsagePointFuturePSDateHelp}">
				          		<p2:DateBox ui:field="futurePricingStructureStartDate" debugId="futurePricingStructureStartDate" styleName="gwt-TextBox" />
				        	</p1:FormElement>
				        	<p1:FormElement>
				        	 	<g:Button debugId="pricingStructureDeleteButton" ui:field="pricingStructureDeleteButton" text="{msg.getUsagePointPSDeleteBtn}" />
				        	 </p1:FormElement>
                        </p1:FormRowPanel>
                        
						<p1:FormRowPanel ui:field="futurePricingChangeReasonPanel" visible="false"/>
                              
                        <p1:FormRowPanel ui:field="pricingStructureViewAllPanel">   
                            <g:Button debugId="pricingStructureViewAllButton" ui:field="pricingStructureViewAllButton" text="{msg.getUsagePointPSViewAllBtn}" />
                        </p1:FormRowPanel>
      
				    </p1:FormGroupPanel>
                    
                    <g:FlowPanel>
                        <p1:FormRowPanel ui:field="usagePointpChargeViewPanel" visible="false">   
                        	<g:Button debugId="usagePointpChargeViewButton" ui:field="usagePointpChargeViewButton" text="{msg.getUsagePointChargeViewButton}" />
                        </p1:FormRowPanel>
                        <p1:FormRowPanel ui:field="usagePointpMeterInspectionPanel" visible="false">   
                            <g:Button debugId="usagePointpMeterInspectionButton" ui:field="usagePointpMeterInspectionButton" text="{msg.getUsagePointMeterInspectionButton}" />
                        </p1:FormRowPanel>
                        <p1:FormRowPanel>
                            <g:HorizontalPanel spacing="5">
                                <g:HTML text="{msg.getLastMdcConnectControl}: " styleName="gwt-Label" />
                                <g:HTML debugId="lastMdcConnectControlText" ui:field="lastMdcConnectControlText" styleName="gwt-Label-bold" />
                            </g:HorizontalPanel>    
                        </p1:FormRowPanel>
                    </g:FlowPanel>
				    
                    <g:FlowPanel ui:field="upBlockingConfigContainer" width="100%" height="100%">
                    	<p1:FormRowPanel>
                            <p1:FormElement ui:field="upBlockingTypeElement" helpMsg="{msg.getUsagePointBlockingHelp}"
                                labelText="{msg.getUsagePointBlockingLabel}: " debugId="upBlockingTypeElement">
                                <g:ListBox debugId="lstbxUpBlockingType" visibleItemCount="1" ui:field="lstbxUpBlockingType"
                                    styleName="gwt-ListBox-ipay" multipleSelect="false" />
                            </p1:FormElement>
                        </p1:FormRowPanel>
                        <p1:FormRowPanel ui:field="upBlockingReasonPanel" visible="false"/>
                        <p1:FormRowPanel ui:field="upBlockingReasonMsgPanel" visible="false"/>
                    </g:FlowPanel>
                    
                    <ipayusercustomfields:UserCustomFieldsComponent ui:field="userCustomFieldsComponent" width="100%" visible="false"></ipayusercustomfields:UserCustomFieldsComponent>
                    
		            <p1:FormGroupPanel labelText="{msg.getUnitsAccountTitle}" ui:field="unitsAccountPanel" visible="false">
	                   <p1:FormRowPanel > 
	                       <p1:FormElement debugId="accountNameElement" ui:field="accountNameElement" helpMsg="{msg.getUnitsAccountNameHelp}" labelText="{msg.getUnitsAccountName}:" required="true">
	                           <g:TextBox debugId="accountNameBox" ui:field="txtbxAccountName" styleName="gwt-TextBox" visibleLength="15"/>
	                       </p1:FormElement>                   
	                      <p1:FormElement ui:field="accountBalanceElement" helpMsg="{msg.getUnitsAccountBalanceHelp}" labelText="{msg.getUnitsAccountBalance}">
	                            <g:Label debugId="accountBalanceBox" text="0" ui:field="lblAccountBalance" styleName="accountBalance"/>
	                       </p1:FormElement>
	                       <p1:FormElement ui:field="btnSyncElement" helpMsg="{msg.getUnitsAccountSyncHelp}">
	                            <g:Button debugId="btnSync" ui:field="btnSync" text="{msg.getUnitsAccountSyncButton}"/>
	                       </p1:FormElement>    
		               </p1:FormRowPanel> 
	                   <p1:FormRowPanel ui:field="lowBalanceThresholdPanel">
	                       <p1:FormElement ui:field="lowBalanceThresholdElement" helpMsg="{msg.getUnitsAccountLowBalanceThresholdHelp}" labelText="{msg.getUnitsAccountLowBalanceThreshold}:" >
                       			<g:HorizontalPanel>
	                           		<p1:BigDecimalValueBox debugId="lowBalanceThresholdBox" ui:field="txtbxlowThreshold" styleName="gwt-TextBox largeNumericInput" />
	                           		<g:Label ui:field="lowBalanceThresholdSymbol" styleName="{style.unitsymbol}"/>
	                           	</g:HorizontalPanel>
	                       </p1:FormElement>
	                   </p1:FormRowPanel> 
	                   <p1:FormRowPanel ui:field="notificationPanel">
	                       <p1:FormElement debugId="notificationEmailElement" ui:field="notificationEmailElement" helpMsg="{msg.getUnitsAccountNotificationEmailHelp}"
	                       		labelText="{msg.getUnitsAccountNotificationEmail}:" >
	                           <g:TextBox debugId="notificationEmailBox" ui:field="txtbxNotificationEmail" styleName="gwt-TextBox gap" visibleLength="25"/>
	                       </p1:FormElement>
	                       <p1:FormElement debugId="notificationPhoneElement" ui:field="notificationPhoneElement" helpMsg="{msg.getUnitsAccountNotificationPhoneHelp}" 
	                       		labelText="{msg.getUnitsAccountNotificationPhone}:" >
	                           <g:TextBox debugId="notificationPhoneBox" ui:field="txtbxNotificationPhone" styleName="gwt-TextBox gap" visibleLength="25"/>
	                       </p1:FormElement>
	                   </p1:FormRowPanel> 
	                   <g:Label text="{msg.getUnitsAccountNote}"/>          
		            </p1:FormGroupPanel>
                    
                    <g:DisclosurePanel debugId="usagePointPhysicalAddressPanel" ui:field="physicalAddressPanel" width="100%" styleName="gwt-DisclosurePanel-insidecomponent">
                        <g:header>
                            <ui:text from="{msg.getCustomerPhysicalAddress}" />
                        </g:header>
                        <ipaycomp:LocationComponent debugId="usagePointLocationComponent" ui:field="serviceLocationComponent" ></ipaycomp:LocationComponent>
                    </g:DisclosurePanel>
                    <g:DisclosurePanel ui:field="upgroupsPanel" width="100%" styleName="gwt-DisclosurePanel-insidecomponent" open="true" debugId="upgroupsPanel">
                        <g:header>
                            <ui:text from="{msg.getUsagePointAllGroupsTitle}" />
                        </g:header>
                        <g:ScrollPanel>
                            <g:FlowPanel>
                                <g:HorizontalPanel debugId="groupTypesPanel" ui:field="groupTypesPanel"></g:HorizontalPanel>
                                <p3:Message ui:field="groupError" visible="false"></p3:Message>
                            </g:FlowPanel>
                        </g:ScrollPanel>
                    </g:DisclosurePanel>
                </g:FlowPanel>

                <g:VerticalPanel ui:field="requiredKeys">
                    <g:Label horizontalAlignment="ALIGN_CENTER" styleName="requiredLabel" text="{msg.getUsagePointRequiredText}" />
                </g:VerticalPanel>
                <g:HorizontalPanel spacing="5">
                    <g:Button debugId="usagePointSaveButton" ui:field="btnSave" text="{msg.getSaveButton}" />
                    <g:Button debugId="usagePointCancelButton" ui:field="btnCancel" text="{msg.getCancelButton}" />
                    <g:HTML>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</g:HTML>
                    <g:Button debugId ="btnCalc" ui:field="btnCalc" text="{msg.getCalcTariffButton}" visible="false" />
                </g:HorizontalPanel>
            </g:FlowPanel>
		</g:FlowPanel>
	</g:DisclosurePanel>
</ui:UiBinder> 
