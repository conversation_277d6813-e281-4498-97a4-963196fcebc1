package za.co.ipay.metermng.client.view.workspace.group.all;

import za.co.ipay.gwt.common.client.form.SimpleForm;
import za.co.ipay.metermng.client.form.SimpleFormPanel;
import za.co.ipay.metermng.shared.group.GroupTypeData;

/**
 * GroupTypeSelection is the base class for any panel that involves selecting a group type.
 * 
 * <AUTHOR>
 */
public abstract class GroupTypeSelection extends SimpleFormPanel {

    public GroupTypeSelection(SimpleForm form) {
        super(form);
    }
    
    public void setForm(SimpleForm form) {
        this.form = form;
    }
    
    public abstract GroupTypeData getGroupTypeData();
    
    public abstract Long getGroupTypeId();
    
    public abstract String getGroupTypeName();
    
    public abstract void setFocus();
    
    public abstract void selectGroupType(Long groupTypeId);
}
