package za.co.ipay.metermng.client.view.workspace.meter.readings.view;

import java.util.Comparator;

import za.co.ipay.metermng.shared.dto.meter.MeterReadingDto;

public class StartDateComparator implements Comparator<MeterReadingDto> {

    public int compare(MeterReadingDto o1, MeterReadingDto o2) {
        if (o1 != null) {
            return (o2 != null) ? o1.getStart().compareTo(o2.getStart()) : 1;
        }
        return -1;
    }
}
