package za.co.ipay.metermng.client.view.workspace.specialactions;

import java.util.logging.Logger;

import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.gwt.common.client.workspace.SessionCheckCallback;
import za.co.ipay.gwt.common.client.workspace.SimpleTableView;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.history.SpecialActionsPlace;
import za.co.ipay.metermng.client.view.workspace.BaseWorkspace;
import za.co.ipay.metermng.mybatis.generated.model.SpecialActionReasons;
import za.co.ipay.metermng.mybatis.generated.model.SpecialActions;

import com.google.gwt.core.client.GWT;
import com.google.gwt.place.shared.Place;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.user.client.ui.DeckLayoutPanel;
import com.google.gwt.user.client.ui.Widget;

public class SpecialActionsWorkspaceView extends BaseWorkspace {

	@UiField DeckLayoutPanel deckPanel;
    @UiField SimpleTableView<SpecialActions> actionsView;
    SpecialActionsView specialActionsView;

    @UiField SimpleTableView<SpecialActionReasons> reasonsView;
    SpecialActionReasonsView specialActionReasonsView;

    private static Logger logger = Logger.getLogger(SpecialActionsWorkspaceView.class.getName());

    private static SpecialActionsWorkspaceViewUiBinder uiBinder = GWT.create(SpecialActionsWorkspaceViewUiBinder.class);

    interface SpecialActionsWorkspaceViewUiBinder extends UiBinder<Widget, SpecialActionsWorkspaceView> {
    }

    public SpecialActionsWorkspaceView(ClientFactory clientFactory, SpecialActionsPlace place) {
        this.clientFactory = clientFactory;
        initWidget(uiBinder.createAndBindUi(this));
        setPlaceString(SpecialActionsPlace.getPlaceAsString(place));
        setHeaderText(MessagesUtil.getInstance().getMessage("special.actions.header"));
        initUi();
    }

    private void initUi() {
        specialActionsView = new SpecialActionsView(this, clientFactory, actionsView);
        specialActionReasonsView = new SpecialActionReasonsView(this, clientFactory, reasonsView);
        deckPanel.showWidget(actionsView);
    }

    public void goToSpecialActions(Long specialActionsId) {
        deckPanel.showWidget(0);
        deckPanel.animate(getAnimationTime());
        specialActionsView.refreshTable();
    }

    public void goToSpecialActionReasons(final SpecialActions specialActions) {
        SessionCheckCallback sessionCheckCallback = new SessionCheckCallback() {
            @Override
            public void callback(SessionCheckResolution resolution) {
                specialActionReasonsView.initSpecialActionReasons(specialActions);
                deckPanel.showWidget(1);
                deckPanel.animate(getAnimationTime());
            }
        };
        clientFactory.handleSessionCheckCallback(sessionCheckCallback);
    }

    @Override
    public void onArrival(Place place) {
        logger.info("Arrived at Special Actions: "+place);
        specialActionsView.onWorkspaceArrival();
    }

    @Override
    public void onLeaving() {

    }

    @Override
    public void onSelect() {

    }

    @Override
    public void onClose() {
        onLeaving();
    }

    @Override
    public boolean handles(Place place) {
        return (place instanceof SpecialActionsPlace);
    }

    public SpecialActionReasonsView getSpecialActionReasonsView() {
        return specialActionReasonsView;
    }
}
