package za.co.ipay.metermng.client.widget.pricingstructure;

import com.google.gwt.event.dom.client.ChangeEvent;
import com.google.gwt.event.dom.client.ChangeHandler;
import com.google.gwt.event.dom.client.FocusHandler;
import com.google.gwt.event.dom.client.KeyDownHandler;
import com.google.gwt.event.logical.shared.HasValueChangeHandlers;
import com.google.gwt.event.logical.shared.SelectionEvent;
import com.google.gwt.event.logical.shared.SelectionHandler;
import com.google.gwt.event.logical.shared.ValueChangeEvent;
import com.google.gwt.event.logical.shared.ValueChangeHandler;
import com.google.gwt.event.shared.HandlerRegistration;
import com.google.gwt.user.client.ui.Composite;
import com.google.gwt.user.client.ui.HasEnabled;
import com.google.gwt.user.client.ui.SuggestBox.DefaultSuggestionDisplay;
import com.google.gwt.user.client.ui.SuggestOracle.Suggestion;
import com.google.gwt.user.client.ui.VerticalPanel;
import com.google.gwt.user.client.ui.Widget;
import za.co.ipay.gwt.common.client.form.HasDirtyData;
import za.co.ipay.gwt.common.client.resource.Messages;
import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.gwt.common.client.widgets.IpayListBox;
import za.co.ipay.gwt.common.shared.LookupListItem;
import za.co.ipay.metermng.client.event.PricingStructureUpdateEvent;
import za.co.ipay.metermng.client.event.PricingStructureUpdateEventHandler;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.rpc.callback.ClientCallback;
import za.co.ipay.metermng.client.widget.suggestboxtree.IdentifiableSuggestOracle.FindSuggestionCallback;
import za.co.ipay.metermng.client.widget.suggestboxtree.SettableSuggestBox;
import za.co.ipay.metermng.mybatis.generated.model.AppSetting;
import za.co.ipay.metermng.shared.LookupListCachedDataSuggestOracle;
import za.co.ipay.metermng.shared.MeterMngStatics;

import java.util.ArrayList;
import java.util.Objects;
import java.util.logging.Logger;

public class PricingStructureLookup extends Composite implements HasEnabled, HasValueChangeHandlers<String> {

    private final static Logger logger = Logger.getLogger("PricingStructureLookup");

    private SettableSuggestBox<String> suggestBox;
    private IpayListBox listBox;
    private final VerticalPanel container;
    private final String label = "";
    public boolean useSuggestBox;
    private final ClientFactory clientFactory;
    private final ClientCallback<ArrayList<LookupListItem>> lookupPricingStructuresCallback;

    //private boolean dataHasChanged;
    private final HasDirtyData hasDirtyData;
    private final ArrayList<LookupListItem> items = new ArrayList<>();

    private Long savedPricingStructureId;
    private LookupListItem selectedPricingStructureItem = new LookupListItem();

    private Long serviceResourceId = null; 
    private Long meterTypeId = null; 
    private ArrayList<Long> paymentModeIds = null;
    private final boolean hasTariffCurrentlyRunning;
    private Long meterModelId = null;
    private final Messages messages = MessagesUtil.getInstance();
    
    public PricingStructureLookup(boolean hasTariffCurrentlyRunning, HasDirtyData hasDirtyData,
                                  ClientFactory clientFactory) {
        this.hasDirtyData = hasDirtyData;
        this.clientFactory = clientFactory;
        this.container = new VerticalPanel();
        this.hasTariffCurrentlyRunning = hasTariffCurrentlyRunning;
        clientFactory.getEventBus().addHandler(PricingStructureUpdateEvent.TYPE,
                new PricingStructureUpdateEventHandler() {
                    @Override
                    public void processPricingStructureUpdatedEvent(PricingStructureUpdateEvent event) {
                        if (event.getAppSettingUpdate() != null) {
                            AppSetting appSetting = event.getAppSettingUpdate();
                            if (Objects.equals(appSetting.getKey(),
                                    MeterMngStatics.APP_SETTING_PRICING_STRUCTURE_DISPLAY_SUGGESTION_BOXES)) {
                                switchDisplay(Objects.equals(appSetting.getValue().toLowerCase(), "true"));
                            }
                        } else if (event.isListUpdated()) {
                            internalUpdateLookupList();
                        }
                    }
                });
        lookupPricingStructuresCallback = new ClientCallback<ArrayList<LookupListItem>>() {
            @Override
            public void onSuccess(final ArrayList<LookupListItem> result) {
                items.clear();
                items.addAll(result);
                refresh();
            }
        };
        clientFactory.getAppSettingRpc()
                .getAppSettingByKey(MeterMngStatics.APP_SETTING_PRICING_STRUCTURE_DISPLAY_SUGGESTION_BOXES,
                        new ClientCallback<AppSetting>() {
                            @Override
                            public void onSuccess(AppSetting appSetting) {
                                boolean switchDisplay = Objects.equals(appSetting.getValue()
                                        .toLowerCase(), "true");
                                switchDisplay(switchDisplay);
                            }
                        });
        initWidget(container);
    }

    public void updateLookupList() {
        this.serviceResourceId = null;
        this.meterTypeId = null;
        this.paymentModeIds = null;
        this.meterModelId = null;
        internalUpdateLookupList();
    }

    public void updateLookupList(Long meterModelId) {
        this.meterModelId = meterModelId;
        this.serviceResourceId = null;
        this.meterTypeId = null;
        this.paymentModeIds = null;
        internalUpdateLookupList();
    }

    public void updateLookupList(Long serviceResourceId, Long meterTypeId, ArrayList<Long> paymentModeIds) {
        this.serviceResourceId = serviceResourceId;
        this.meterTypeId = meterTypeId;
        this.paymentModeIds = paymentModeIds;
        this.meterModelId = null;
        internalUpdateLookupList();
    }

    public void updateForDisplayOnly(LookupListItem lookupListItem) {
        items.clear();
        items.add(lookupListItem);
        savedPricingStructureId = Long.valueOf(lookupListItem.getValue());
        this.serviceResourceId = null;
        this.meterTypeId = null;
        this.paymentModeIds = null;
        this.meterModelId = null;
        refresh();
        if (useSuggestBox && suggestBox != null) {
            suggestBox.setEnabled(false);
        } else if (listBox != null) {
            listBox.setEnabled(false);
        }
    }
    
    private void internalUpdateLookupList() {
        if (this.meterModelId != null) {
            clientFactory.getLookupRpc().getPricingStructureLookupListFromMeterModel(meterModelId, hasTariffCurrentlyRunning,
                    lookupPricingStructuresCallback);
        } else if (this.serviceResourceId != null) {
            clientFactory.getLookupRpc().getPricingStructureLookupList(serviceResourceId, meterTypeId, paymentModeIds,
                    hasTariffCurrentlyRunning, lookupPricingStructuresCallback);

        } else {
            clientFactory.getLookupRpc().getPricingStructureLookupList(hasTariffCurrentlyRunning,
                    lookupPricingStructuresCallback);
        }
    }

    private void refresh() {
        if (useSuggestBox) {
            final LookupListCachedDataSuggestOracle lookupListCachedDataSuggestOracle =
                    new LookupListCachedDataSuggestOracle(items, label);
            listBox = null;
            suggestBox = new SettableSuggestBox<>(lookupListCachedDataSuggestOracle);
            setStyleName("gwt-TextBox topSpaced");
            setAnimationEnabled(true);
            setAutoSelectEnabled(false);
            suggestBox.getValueBox().getElement().setPropertyString("placeholder",
                        messages.getMessage("pricingstructure.suggestbox.placeholder"));

            suggestBox.addSelectionHandler(new SelectionHandler<Suggestion>() {
                @Override
                public void onSelection(SelectionEvent<Suggestion> selectionEvent) {
                    selectedPricingStructureItem = (LookupListItem) selectionEvent.getSelectedItem();
                    if (isDataDirty()) {
                        if (hasDirtyData != null) {
                            hasDirtyData.setDirtyData(true);
                        }
                        suggestBox.addStyleName("changed");
                    } else {
                        suggestBox.removeStyleName("changed");
                    }
                    ValueChangeEvent.fire(PricingStructureLookup.this, selectedPricingStructureItem.getValue());
                }
            });
            suggestBox.addValueChangeHandler(new ValueChangeHandler<String>() {
                @Override
                public void onValueChange(ValueChangeEvent<String> valueChangeEvent) {
                    String suggestBoxValue = valueChangeEvent.getValue();
                    if (suggestBoxValue == null || suggestBoxValue.trim().isEmpty()) {
                        clearSelection();
                        selectedPricingStructureItem = null;
                        if (isDataDirty()) {
                            if (hasDirtyData != null) {
                                hasDirtyData.setDirtyData(true);
                            }
                            suggestBox.addStyleName("changed");
                        } else {
                            suggestBox.removeStyleName("changed");
                        }
                        ValueChangeEvent.fire(PricingStructureLookup.this, null);
                    } else {
                        for (LookupListItem lookupListItem : items) {
                            if (suggestBoxValue.equalsIgnoreCase(lookupListItem.getText())) {
                                selectedPricingStructureItem = lookupListItem;
                                if (isDataDirty()) {
                                    if (hasDirtyData != null) {
                                        hasDirtyData.setDirtyData(true);
                                    }
                                    suggestBox.addStyleName("changed");
                                } else {
                                    suggestBox.removeStyleName("changed");
                                }
                                ValueChangeEvent.fire(PricingStructureLookup.this, lookupListItem.getValue());
                                break;
                            }
                        }
                    }
                }
            });
            container.clear();
            container.add(suggestBox);
        } else {
            suggestBox = null;
            listBox = new IpayListBox();
            listBox.setVisibleItemCount(1);
            listBox.setStyleName("gwt-ListBox-ipay");
            listBox.addChangeHandler(new ChangeHandler() {
                @Override
                public void onChange(ChangeEvent changeEvent) {
                    selectedPricingStructureItem = listBox.getItem(listBox.getSelectedIndex());
                    if (isDataDirty()) {
                        if (hasDirtyData != null) {
                            hasDirtyData.setDirtyData(true);
                        }
                        listBox.setStyleName("changed");
                        setSelectedPricingStructureItem(selectedPricingStructureItem.getValue());
                    } else {
                        listBox.removeStyleName("changed");
                    }
                    ValueChangeEvent.fire(PricingStructureLookup.this, listBox.getListBox()
                            .getSelectedValue());
                }
            });

            container.clear();
            container.add(listBox);
            listBox.clearAll();
            listBox.setLookupItemsWithEmptyFirst(items);
        }
        if (savedPricingStructureId != null) {
            setSelectedPricingStructureItem(String.valueOf(savedPricingStructureId));
        }
    }

    public String getLabel() {
        return label;
    }

    public LookupListItem getSelectedPricingStructureItem() {
        return selectedPricingStructureItem == null ? null
                : selectedPricingStructureItem.getValue()==null ? null
                : selectedPricingStructureItem;
    }

    public void setSavedPricingStructureId(Long savedPricingStructureId) {
        this.savedPricingStructureId = savedPricingStructureId;
        setSelectedPricingStructureItem(String.valueOf(savedPricingStructureId));
    }

    private void setSelectedPricingStructureItem(String value) {
        if (value == null) {
            clearSelection();
        } else if (getPricingStructureWidget() != null ) {
            if (Objects.equals(getPricingStructureWidget(),suggestBox)) {
                suggestBox.setById(value, true);
                LookupListCachedDataSuggestOracle lookupListCachedDataSuggestOracle = (LookupListCachedDataSuggestOracle)
                        suggestBox.getSuggestOracle();
                lookupListCachedDataSuggestOracle.findById(value, new FindSuggestionCallback() {
                    @Override
                    public void found(Suggestion suggestion) {
                        selectedPricingStructureItem = (LookupListItem) suggestion;
                    }
                });
            } else if (Objects.equals(getPricingStructureWidget(),listBox)) {
                listBox.selectItemByValue(value);
                selectedPricingStructureItem = listBox.getItem(listBox.getSelectedIndex());
                if (selectedPricingStructureItem.getText().trim().isEmpty()) {
                    selectedPricingStructureItem.setValue(null);
                }
            }
        }
    }

    public void clearSelection() {
        if (getPricingStructureWidget() != null) {
            if (getPricingStructureWidget().equals(listBox)) {
                // Reset the ListBox selection
                listBox.setSelectedIndex(-1); // No selection
            } else if (getPricingStructureWidget().equals(suggestBox)) {
                // Clear the SuggestBox value
                suggestBox.setValue("", true);
            }
        }
        savedPricingStructureId = null;
        selectedPricingStructureItem = new LookupListItem();
    }

    public void switchDisplay(boolean showSuggestBox) {
        if (showSuggestBox != useSuggestBox) {
            useSuggestBox = showSuggestBox;
            refresh();
            if (selectedPricingStructureItem != null) {
                setSelectedPricingStructureItem(selectedPricingStructureItem.getValue());
            }
        }
    }

    private Widget getPricingStructureWidget() {
        return container.getWidgetCount() > 0 ? container.getWidget(0) : null;
    }

    @Override
    public void setStyleName(String style) {
        if (useSuggestBox) {
            suggestBox.setStyleName(style);
        } else if (listBox != null) {
            listBox.setStyleName(style);
        }
    }

    public void setAnimationEnabled(boolean enable) {
        if (useSuggestBox) {
            ((DefaultSuggestionDisplay)suggestBox.getSuggestionDisplay()).setAnimationEnabled(enable);
        }
    }

    public void setAutoSelectEnabled(boolean selectsFirstItem) {
        if (useSuggestBox) {
            suggestBox.setAutoSelectEnabled(selectsFirstItem);
        }
    }

    public boolean isDataDirty() {
        String selectedValue = selectedPricingStructureItem == null ? null : selectedPricingStructureItem.getValue();
        return !Objects.equals(savedPricingStructureId, selectedValue == null ? null : Long.valueOf(selectedValue));
    }

    @Override
    public boolean isEnabled() {
        if (useSuggestBox && suggestBox != null) {
            return suggestBox.isEnabled();
        } else if (listBox != null){
            return listBox.isEnabled();
        } else {
            return false;
        }
    }

    public void setEnabled(boolean enabled) {
        if (useSuggestBox) {
            suggestBox.setEnabled(enabled);
        } else if (listBox != null){
            listBox.setEnabled(enabled);
        }
    }

    public void addFocusHandler(FocusHandler focusHandler) {
        if (useSuggestBox) {
            suggestBox.getValueBox().addFocusHandler(focusHandler);
        } else if (listBox != null) {
            listBox.addFocusHandler(focusHandler);
        }
    }

    public void showSuggestionList() {
        if (useSuggestBox) {
            suggestBox.showSuggestionList();
        }
    }

    public void addKeyDownHandler(KeyDownHandler handler) {
        if (useSuggestBox) {
            suggestBox.addKeyDownHandler(handler);
        } else if (listBox != null){
            listBox.addKeyDownHandler(handler);
        }
    }

    @Override
    public HandlerRegistration addValueChangeHandler(ValueChangeHandler valueChangeHandler) {
        return addHandler(valueChangeHandler, ValueChangeEvent.getType());
    }
}
