package za.co.ipay.metermng.client.event;

import com.google.gwt.event.shared.GwtEvent;

public class ManufacturerEvent extends GwtEvent<ManufacturerEventHandler> {
    
    public static Type<ManufacturerEventHandler> TYPE = new Type<ManufacturerEventHandler>();
    
    private String name;
    
    public ManufacturerEvent(String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }

    @Override
    public Type<ManufacturerEventHandler> getAssociatedType() {
        return TYPE;
    }

    @Override
    protected void dispatch(ManufacturerEventHandler handler) {
        handler.handleEvent(this);
    }
}
