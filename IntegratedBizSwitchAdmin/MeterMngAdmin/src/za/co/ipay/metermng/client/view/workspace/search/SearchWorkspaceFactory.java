package za.co.ipay.metermng.client.view.workspace.search;

import za.co.ipay.gwt.common.client.workspace.WorkspaceCreateCallback;
import za.co.ipay.gwt.common.client.workspace.WorkspaceFactory;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.history.SearchPlace;

import com.google.gwt.place.shared.Place;

public class SearchWorkspaceFactory implements WorkspaceFactory {

    private ClientFactory clientFactory;

    public SearchWorkspaceFactory(ClientFactory clientFactory) {
        this.clientFactory = clientFactory;
        clientFactory.getWorkspaceContainer().register(this);
    }

    @Override
    public void createWorkspace(Place place, WorkspaceCreateCallback workspaceCreateCallback) {
        SearchPlace searchPlace = null;
        if (place instanceof SearchPlace) {
            searchPlace = (SearchPlace) place;
            try {
                if (SearchPlace.ADVANCED_SEARCH_TYPE.equals(searchPlace.getSearchType())) {
                    AdvancedSearchWorkspaceView view = new AdvancedSearchWorkspaceView(clientFactory, searchPlace);
                    workspaceCreateCallback.onWorkspaceCreated(view);
//                } else if (SearchPlace.VIEWED_SEARCH_TYPE.equals(searchPlace.getSearchType())
//                        || SearchPlace.MODIFIED_SEARCH_TYPE.equals(searchPlace.getSearchType())) {
//                    ListingSearchWorkspaceView view = new ListingSearchWorkspaceView(clientFactory, searchPlace);
//                    workspaceCreateCallback.onWorkspaceCreated(view);
                } else {
                    workspaceCreateCallback.onWorkspaceCreationFailed(new Exception("Unknown workspace for searchPlace: "+searchPlace));
                }
            } catch (Exception e) {
                workspaceCreateCallback.onWorkspaceCreationFailed(e);
            }
        } else {
            workspaceCreateCallback.onWorkspaceCreationFailed(new Exception("Invalid place for factory: "+place));
        }
    }

    @Override
    public boolean handles(Place place) {
        if (place instanceof SearchPlace) {
            return true;
        } else {
            return false;
        }
    }
}
