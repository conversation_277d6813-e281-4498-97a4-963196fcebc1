package za.co.ipay.metermng.client.view.component;

import java.util.Date;
import java.util.List;
import java.util.logging.Logger;

import com.google.gwt.cell.client.DateCell;
import com.google.gwt.core.client.GWT;
import com.google.gwt.i18n.client.DateTimeFormat;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.user.cellview.client.CellTable;
import com.google.gwt.user.cellview.client.Column;
import com.google.gwt.user.cellview.client.TextColumn;
import com.google.gwt.user.client.ui.DialogBox;
import com.google.gwt.user.client.ui.Widget;
import com.google.gwt.view.client.ListDataProvider;

import za.co.ipay.gwt.common.client.form.Format;
import za.co.ipay.gwt.common.client.form.Format.FormatUtil;
import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.metermng.shared.dto.UpPricingStructureData;

public class ViewPricingStructuresDialogueBox extends DialogBox {

    private static Logger logger = Logger.getLogger(ViewPricingStructuresDialogueBox.class.getName());

    private List<UpPricingStructureData> usagepointPSList;
    private ListDataProvider<UpPricingStructureData> dataProvider;

    private static ViewPricingStructuresDialogueBoxUiBinder uiBinder = GWT.create(ViewPricingStructuresDialogueBoxUiBinder.class);
    interface ViewPricingStructuresDialogueBoxUiBinder extends UiBinder<Widget, ViewPricingStructuresDialogueBox> {
    }

    @UiField(provided = true) CellTable<UpPricingStructureData> clltblViewPS;

    public ViewPricingStructuresDialogueBox(List<UpPricingStructureData> usagepointPSList) {
        super();
        this.usagepointPSList = usagepointPSList;
        createTable();
        setWidget(uiBinder.createAndBindUi(this));
        this.ensureDebugId("ViewPricingStructuresDialogueBox");
        initDataProvider();
        initTable();
        logger.info("ViewPricingStructuresDialogueBox complete");
    }

    protected void createTable() {
        clltblViewPS = new CellTable<UpPricingStructureData>();
    }

    private void initDataProvider() {
        dataProvider = new ListDataProvider<UpPricingStructureData>();
        dataProvider.getList().clear();
        dataProvider.setList(usagepointPSList);
        clltblViewPS.setRowCount(dataProvider.getList().size(), true);
        dataProvider.refresh();
    }

    private void initTable() {
        TextColumn<UpPricingStructureData> psNameColumn = new TextColumn<UpPricingStructureData>() {
            @Override
            public String getValue(UpPricingStructureData data) {
                return data.getPricingStructure().getName();
            }
        };

        DateCell dateCell = new DateCell(DateTimeFormat.getFormat(FormatUtil.getInstance().getDateTimeFormat()));
        Column<UpPricingStructureData, Date> psStartDateColumn = new Column<UpPricingStructureData, Date>(dateCell) {
            @Override
            public Date getValue(UpPricingStructureData data) {
                Format format = FormatUtil.getInstance();
                return format.parseDateTime(format.formatDateTime(data.getUpPricingStructure().getStartDate()));
            }
        };
        psStartDateColumn.setSortable(true);
        psStartDateColumn.setDefaultSortAscending(false);
        
        TextColumn<UpPricingStructureData> psChangeReasonColumn = new TextColumn<UpPricingStructureData>() {
            @Override
            public String getValue(UpPricingStructureData data) {
                if (data.getChangeReasonText() != null) {
                    return data.getChangeReasonText();
                } else { 
                    return "";
                }
                
            }
        };
        
        Column<UpPricingStructureData, Date> psDateModifiedColumn = new Column<UpPricingStructureData, Date>(dateCell) {
            @Override
            public Date getValue(UpPricingStructureData data) {
                Format format = FormatUtil.getInstance();
                return format.parseDateTime(format.formatDateTime(data.getDateRecModified()));
            }
        };
        
        TextColumn<UpPricingStructureData> psUserModifiedColumn = new TextColumn<UpPricingStructureData>() {
            @Override
            public String getValue(UpPricingStructureData data) {
                return data.getUserRecEntered();
            }
        };

        clltblViewPS.addColumn(psNameColumn, MessagesUtil.getInstance().getMessage("usagepoint.ps.name.lbl"));
        clltblViewPS.addColumn(psStartDateColumn, MessagesUtil.getInstance().getMessage("usagepoint.ps.start.date.lbl"));
        clltblViewPS.addColumn(psDateModifiedColumn, MessagesUtil.getInstance().getMessage("usagepoint.ps.date.modified.hd"));
        clltblViewPS.addColumn(psUserModifiedColumn, MessagesUtil.getInstance().getMessage("usagepoint.ps.user.modified.hd"));
        clltblViewPS.addColumn(psChangeReasonColumn, MessagesUtil.getInstance().getMessage("usagepoint.ps.change.reason.hd"));
        dataProvider.addDataDisplay(clltblViewPS);
    }

}
