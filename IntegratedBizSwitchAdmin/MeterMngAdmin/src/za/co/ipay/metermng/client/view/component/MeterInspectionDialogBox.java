package za.co.ipay.metermng.client.view.component;

import java.util.logging.Logger;

import com.google.gwt.core.client.GWT;
import com.google.gwt.event.dom.client.ClickEvent;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiFactory;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.uibinder.client.UiHandler;
import com.google.gwt.user.client.ui.Button;
import com.google.gwt.user.client.ui.DialogBox;
import com.google.gwt.user.client.ui.TextBox;
import com.google.gwt.user.client.ui.Widget;
import za.co.ipay.gwt.common.client.Dialogs;
import za.co.ipay.gwt.common.client.form.HasDirtyData;
import za.co.ipay.gwt.common.client.form.LocalOnlyHasDirtyData;
import za.co.ipay.gwt.common.client.handler.ConfirmHandler;
import za.co.ipay.gwt.common.client.handler.FormDataChangeHandler;
import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.gwt.common.client.widgets.Message;
import za.co.ipay.gwt.common.client.workspace.SessionCheckCallback;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.i18n.UiMessages;
import za.co.ipay.metermng.client.i18n.UiMessagesUtil;
import za.co.ipay.metermng.client.rpc.callback.ClientCallback;
import za.co.ipay.metermng.shared.IpayResponseData;
import za.co.ipay.metermng.shared.SpecialActionsData;
import za.co.ipay.metermng.shared.dto.UsagePointData;

public class MeterInspectionDialogBox extends DialogBox {

    @UiField Message feedBack;
    @UiField TextBox txtbxComment;
    @UiField Button sendBtn;
    @UiField Button cancelBtn;
    @UiField Button closeBtn;
    @UiField(provided=true) SpecialActionsReasonComponent specialactionreasons;

    int left, top;
    private UsagePointData usagePoint;
    private ClientFactory clientFactory;
    private HasDirtyData hasDirtyData = new LocalOnlyHasDirtyData();

    private static Logger logger = Logger.getLogger(ViewUsagePointChargeDialogBox.class.getName());

    private static MeterInspectionDialogBoxUiBinder uiBinder = GWT.create(MeterInspectionDialogBoxUiBinder.class);

    interface MeterInspectionDialogBoxUiBinder extends UiBinder<Widget, MeterInspectionDialogBox> {
    }

    public MeterInspectionDialogBox(ClientFactory clientFactory, UsagePointData usagePoint, final int left, final int top) {
        super();
        this.clientFactory = clientFactory;
        this.usagePoint = usagePoint;
        this.left = left;
        this.top = top;
        specialactionreasons = new SpecialActionsReasonComponent(clientFactory, hasDirtyData, SpecialActionsData.INSPECT_METER);

        setWidget(uiBinder.createAndBindUi(this));
        feedBack.setVisible(false);
        closeBtn.setVisible(false);
        addFieldHandlers();
    }

    private void addFieldHandlers() {
        txtbxComment.addChangeHandler(new FormDataChangeHandler(hasDirtyData));
    }

    @UiHandler("sendBtn")
    void handleViewOustandingChargeButton(ClickEvent event) {
        sendBtn.setEnabled(false);
        showFeedbackMessage("", null, Message.MESSAGE_TYPE_SUCCESS);
        if (isValid()) {
            usagePoint.setInspectMeterReasonsLog(specialactionreasons.getLogEntry());
            final String comment = txtbxComment.getValue();
            final ClientCallback<IpayResponseData> meterInspectionAsyncCallback = new ClientCallback<IpayResponseData>() {
                @Override
                public void onSuccess(IpayResponseData result) {
                    if (result == null) {
                        showFeedbackMessage("usagepoint.meter.inspection.request.setup.error", null, Message.MESSAGE_TYPE_ERROR);
                        sendBtn.setEnabled(true);
                    } else {
                        String mesg = "usagepoint.meter.inspection.request.meterMng000";
                        if (result.getResCode().equals("meterMng001")) {
                            mesg = "usagepoint.meter.inspection.request.meterMng001";
                        } else if (result.getResCode().equals("meterMng011")) {
                            mesg = "usagepoint.meter.inspection.request.meterMng011";
                        }
                        showFeedbackMessage(mesg, new String[]{result.getResRef()}, Message.MESSAGE_TYPE_SUCCESS);
                        closeBtn.setVisible(true);
                        sendBtn.setEnabled(false);
                        cancelBtn.setEnabled(false);
                    }
                }

                @Override
                public void onFailure(Throwable caught) {
                    showFeedbackMessage("usagepoint.meter.inspection.request.processing.error", null, Message.MESSAGE_TYPE_ERROR);
                    sendBtn.setEnabled(true);
                }
            };
            SessionCheckCallback sessionCheckCallback = new SessionCheckCallback() {
                @Override
                public void callback(SessionCheckResolution sessionCheckResolution) {
                    clientFactory.getUsagePointRpc().sendMeterInspectionRequestMsg(usagePoint, comment, meterInspectionAsyncCallback);
                }
            };
            clientFactory.handleSessionCheckCallback(sessionCheckCallback);
        }
    }

    private void showFeedbackMessage(String key, String[] args, int msgType) {
        if (args != null && args.length > 0) {
            feedBack.setText(MessagesUtil.getInstance().getMessage(key, args));
        } else {
            feedBack.setText(MessagesUtil.getInstance().getMessage(key));
        }
        feedBack.setType(msgType);
        feedBack.setVisible(!key.isEmpty());
    }

    private boolean isValid() {
        specialactionreasons.clearErrorMessages();
        boolean valid = true;
        if (specialactionreasons.isAttached() && specialactionreasons.isVisible() && !specialactionreasons.validate()) {
            valid = false;
        }
        return valid;
    }

    @UiHandler("cancelBtn")
    void handleCancelButton(ClickEvent event) {
        if (hasDirtyData.isDirtyData()) {
            Dialogs.confirmDiscardChanges(new ConfirmHandler() {
                @Override
                public void confirmed(boolean confirm) {
                    if (confirm) {
                        hasDirtyData.setDirtyData(false);
                        hide();
                    }
                }
            });
        } else {
            this.hide();
        }
    }

    @UiHandler("closeBtn")
    void handleCloseButton(ClickEvent event) {
        this.hide();
    }

    @UiFactory
    public UiMessages getUiMessages() {
        return UiMessagesUtil.getInstance();
    }
}
