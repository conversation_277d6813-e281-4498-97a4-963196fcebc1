package za.co.ipay.metermng.client.view.workspace.schedule.cron;

import java.util.logging.Logger;

/**
 * A cron expression is a string comprised of 6 or 7 fields separated by white space.
 * The fields are: Seconds Minutes Hours DayOfMonth Month DayOfWeek Year 
 * Examples:
 *   0 0 13 ? * *      (daily)
 *   0 30 13 ? * WED   (weekly)
 *   0 30 8 20 * ?     (monthly)
 * 
 * <AUTHOR>
 */
public class CronExpressionParser {
    
    public static final String ANY = "?";
    public static final String ALL = "*";
    private static final String SPLIT_REG_EXP = "\\s"; 

    private String cronExpression;
    private String[] fields;
    
    private static Logger logger = Logger.getLogger(CronExpressionParser.class.getName());
    
    @SuppressWarnings("unused")
    private CronExpressionParser() {
        //GWT default constructor
        cronExpression = "";
    }
    
    public CronExpressionParser(String cronExpression) {
        this.cronExpression = cronExpression;
        parse();
    }    
    
    private void parse() {
        //eg: 0 15 10 ? * * 
        fields = cronExpression.split(SPLIT_REG_EXP);
    }
    
    public String getSeconds() {
        return getField(0);
    }
    
    public String getMinutes() {
        return getField(1);
    }
    
    public String getHours() {
        return getField(2);
    }
    
    public String getDayOfMonth() {
        return getField(3);
    }
    
    public String getMonth() {
        return getField(4);
    }
    
    public String getDayOfWeek() {
        return getField(5);
    }
    
    public String getYear() {
        return getField(6);
    }
    
    private String getField(int i) {
        if (fields != null && fields.length >= (i+1)) {
            return fields[i];
        } else {
            return "";
        }
    }
    
    public static String getCronExpression(String... fields) {
        if (fields != null) {         
            StringBuilder exp = new StringBuilder();
            for(int i=0;i<fields.length;i++) {
                if (i == 0) {
                    exp.append(fields[i]);
                } else {
                    exp.append(" ").append(fields[i]);
                }
            }
            
            //Add any extra missing fields - //eg: 0 15 10 ? * * *
            int count = fields.length;
            for(int j=count;j<7;j++) {
                if (j == 0) {
                    exp.append(ALL);
                } else {
                    exp.append(" ").append(ALL);
                }
            }
            
            return exp.toString();
        } else {
            return "";
        }
    }
    
    public static boolean isDaily(String cronExp) {
        if (cronExp != null && !cronExp.trim().equals("")) {
           String[] fields = cronExp.split(SPLIT_REG_EXP);
           if (fields != null && fields.length == 6) {
               if (fields[3].equals(ANY) && fields[4].equals(ALL) && fields[5].equals(ALL)) {
                   return true;
               }
           } else if (fields != null && fields.length == 7) {
               if (fields[3].equals(ANY) && fields[4].equals(ALL) && fields[5].equals(ALL) && fields[6].equals(ALL)) {
                   return true;
               }
           } else {
               if (fields != null) {
                   logger.info("Expected 6 or 7 fields in cron exp: "+cronExp+" fields:"+fields.length);
               } else {
                   logger.info("Invalid cronExp to parse: "+cronExp);
               }
           }
        } 
        return false;
    }
    
    public static boolean isWeekly(String cronExp) {
        if (cronExp != null && !cronExp.trim().equals("")) {
           String[] fields = cronExp.split(SPLIT_REG_EXP);
           if (fields != null && (fields.length == 6 || fields.length == 7)) {
               if (!fields[5].equals(ALL) && !fields[5].equals(ANY)) {
                   return true;
               }
           } else {
               logger.info("Expected 6 or 7 fields in cron exp: "+cronExp+" fields:"+fields.length);
           }
        } 
        return false;
    }
    
    //0 32 15 24 * ? *
    public static boolean isMonthly(String cronExp) {
        if (cronExp != null && !cronExp.trim().equals("")) {
           String[] fields = cronExp.split(SPLIT_REG_EXP);
           if (fields != null && (fields.length == 6 || fields.length == 7)) {
               if (!fields[3].equals(ANY) && !fields[3].equals(ALL)) {
                   return true;
               }
           } else {
               logger.info("Expected 6 or 7 fields in cron exp: "+cronExp+" fields:"+fields.length);
           }
        } 
        return false;
    }
    
    //0 0 0/1 * * ? *
    public static boolean isRepeatedly(String cronExp) {
        if (cronExp != null && !cronExp.trim().equals("")) {
           String[] fields = cronExp.split(SPLIT_REG_EXP);
           if (fields != null && (fields.length == 6 || fields.length == 7)) {
               if (fields[1].indexOf('/') > -1 || fields[2].indexOf('/') > -1) {
                   return true;
               }
           } else {
               logger.info("Expected 6 or 7 fields in cron exp: "+cronExp+" fields:"+fields.length);
           }
        } 
        return false;
    }
}
