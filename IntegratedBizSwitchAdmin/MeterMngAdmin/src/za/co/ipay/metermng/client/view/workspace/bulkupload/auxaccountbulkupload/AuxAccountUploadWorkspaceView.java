package za.co.ipay.metermng.client.view.workspace.bulkupload.auxaccountbulkupload;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.logging.Logger;

import com.google.gwt.cell.client.AbstractCell;
import com.google.gwt.core.client.GWT;
import com.google.gwt.place.shared.Place;
import com.google.gwt.safehtml.shared.SafeHtmlBuilder;
import com.google.gwt.safehtml.shared.SafeHtmlUtils;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.user.cellview.client.CellTable;
import com.google.gwt.user.cellview.client.Column;
import com.google.gwt.user.cellview.client.TextColumn;
import com.google.gwt.user.client.Window;
import com.google.gwt.user.client.ui.DialogBox;
import com.google.gwt.user.client.ui.ScrollPanel;
import com.google.gwt.user.client.ui.Widget;
import com.google.gwt.view.client.ListDataProvider;

import za.co.ipay.gwt.common.client.Dialogs;
import za.co.ipay.gwt.common.client.factory.ResourcesFactoryUtil;
import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.history.AuxAccountUploadPlace;
import za.co.ipay.metermng.client.resource.image.MediaResource.MediaResourceUtil;
import za.co.ipay.metermng.client.view.component.bulkupload.FileUploadPanel;
import za.co.ipay.metermng.client.view.workspace.BaseWorkspace;
import za.co.ipay.metermng.client.view.workspace.bulkupload.ParentUpload;
import za.co.ipay.metermng.shared.MeterMngStatics;
import za.co.ipay.metermng.shared.dto.uploaddata.auxaccountupload.AuxAccountCsvData;
import za.co.ipay.metermng.shared.dto.uploaddata.auxaccountupload.AuxAccountCsvMapToData;

public class AuxAccountUploadWorkspaceView extends BaseWorkspace implements ParentUpload<AuxAccountCsvData> {

	@UiField(provided = true)
	FileUploadPanel<AuxAccountCsvData> fileUploadPanel;

	private CellTable<AuxAccountCsvData> auxAccountsClltbl;
	private Column<AuxAccountCsvData, String> errorColumn;
	private boolean isCsvDataValid;

	private CellTable<AuxAccountCsvData> selectedTable;
	private ListDataProvider<AuxAccountCsvData> selectedTableDataProvider;

	private static final int DEFAULT_PAGE_SIZE = 15;

	private static Logger logger = Logger.getLogger(AuxAccountUploadWorkspaceView.class.getName());

	private static AuxAccountUploadWorkspaceViewUiBinder uiBinder = GWT
			.create(AuxAccountUploadWorkspaceViewUiBinder.class);

	interface AuxAccountUploadWorkspaceViewUiBinder extends UiBinder<Widget, AuxAccountUploadWorkspaceView> {
	}

	public AuxAccountUploadWorkspaceView(ClientFactory clientFactory, AuxAccountUploadPlace place) {
		this.clientFactory = clientFactory;
		initTable();
		createTable();
		fileUploadPanel = new FileUploadPanel<AuxAccountCsvData>(clientFactory, MeterMngStatics.AUX_ACCOUNT_UPLOAD, this);
		initWidget(uiBinder.createAndBindUi(this));
		setPlaceString(AuxAccountUploadPlace.getPlaceAsString(place));
		setHeaderText(MessagesUtil.getInstance().getMessage("auxaccount.upload.heading"));
		createSelectedTable();
	}

	private void initTable() {
		auxAccountsClltbl = new CellTable<AuxAccountCsvData>(DEFAULT_PAGE_SIZE, ResourcesFactoryUtil.getInstance().getCellTableResources());
	}

	private void createTable() {
		AbstractCell<String> errorCell = new AbstractCell<String>() {
			@Override
			public void render(Context context, String value, SafeHtmlBuilder sb) {
				if (value == null)
					return;
				sb.appendHtmlConstant("<span class=\"errorInlineNotBold\">" + value + "</span>");
			}
		};
		errorColumn = new Column<AuxAccountCsvData, String>(errorCell) {
			@Override
			public String getValue(AuxAccountCsvData object) {
				return object.getErrors();
			}
		};

		auxAccountsClltbl.addColumn(errorColumn, SafeHtmlUtils.fromSafeConstant("<span class=\"error\">" + MessagesUtil.getInstance().getMessage("auxaccount.upload.errors") + "</span>"));
		createTableCommons(auxAccountsClltbl, false);
	}

	private void createSelectedTable() {
		selectedTable = new CellTable<AuxAccountCsvData>();
		createTableCommons(selectedTable, true);
		selectedTableDataProvider = new ListDataProvider<AuxAccountCsvData>();
		selectedTableDataProvider.addDataDisplay(selectedTable);
	}

	private void createTableCommons(CellTable<AuxAccountCsvData> currentTable, boolean isSelectedTable) {

		TextColumn<AuxAccountCsvData> identifierTypeColumn = new TextColumn<AuxAccountCsvData>() {
			@Override
			public String getValue(AuxAccountCsvData object) {
				return object.getIdentifierType();
			}
		};

		TextColumn<AuxAccountCsvData> identifierColumn = new TextColumn<AuxAccountCsvData>() {
			@Override
			public String getValue(AuxAccountCsvData object) {
				return object.getIdentifier();
			}
		};

		TextColumn<AuxAccountCsvData> auxAccountNameColumn = new TextColumn<AuxAccountCsvData>() {
			@Override
			public String getValue(AuxAccountCsvData object) {
				return object.getAuxAccountName();
			}
		};

		TextColumn<AuxAccountCsvData> auxTypeNameColumn = new TextColumn<AuxAccountCsvData>() {
			@Override
			public String getValue(AuxAccountCsvData object) {
				return object.getAuxTypeName();
			}
		};

		TextColumn<AuxAccountCsvData> auxAccountPriorityColumn = new TextColumn<AuxAccountCsvData>() {
			@Override
			public String getValue(AuxAccountCsvData object) {
				return object.getAuxAccountPriority();
			}
		};

		TextColumn<AuxAccountCsvData> chargeScheduleNameColumn = null;
		TextColumn<AuxAccountCsvData> principleAmountColumn = null;
		TextColumn<AuxAccountCsvData> balanceColumn = null;
		TextColumn<AuxAccountCsvData> balanceTypeColumn = null;
		TextColumn<AuxAccountCsvData> suspendUntilColumn = null;
		TextColumn<AuxAccountCsvData> startDateColumn = null;
		if (isSelectedTable) {
			chargeScheduleNameColumn = new TextColumn<AuxAccountCsvData>() {
				@Override
				public String getValue(AuxAccountCsvData object) {
					return object.getChargeScheduleName();
				}
			};

			principleAmountColumn = new TextColumn<AuxAccountCsvData>() {
				@Override
				public String getValue(AuxAccountCsvData object) {
					return object.getPrincipleAmount();
				}
			};

			balanceColumn = new TextColumn<AuxAccountCsvData>() {
				@Override
				public String getValue(AuxAccountCsvData object) {
					return object.getBalance();
				}
			};

			balanceTypeColumn = new TextColumn<AuxAccountCsvData>() {
				@Override
				public String getValue(AuxAccountCsvData object) {
					return object.getBalanceType();
				}
			};

			suspendUntilColumn = new TextColumn<AuxAccountCsvData>() {
				@Override
				public String getValue(AuxAccountCsvData object) {
					return object.getSuspendUntil();
				}
			};
			startDateColumn = new TextColumn<AuxAccountCsvData>() {
                @Override
                public String getValue(AuxAccountCsvData object) {
                    return object.getStartDate();
                }
            };
		}

		currentTable.addColumn(identifierTypeColumn, MessagesUtil.getInstance().getMessage("auxaccount.upload.identifierType"));
		currentTable.addColumn(identifierColumn, MessagesUtil.getInstance().getMessage("auxaccount.upload.identifier"));
		currentTable.addColumn(auxAccountNameColumn, MessagesUtil.getInstance().getMessage("auxaccount.upload.auxaccountname"));
		currentTable.addColumn(auxTypeNameColumn, MessagesUtil.getInstance().getMessage("auxaccount.upload.auxtype"));
		currentTable.addColumn(auxAccountPriorityColumn, MessagesUtil.getInstance().getMessage("auxaccount.upload.accountpriority"));
		if (isSelectedTable) {
			currentTable.addColumn(chargeScheduleNameColumn, MessagesUtil.getInstance().getMessage("auxaccount.upload.chrgschdlname"));
			currentTable.addColumn(principleAmountColumn, MessagesUtil.getInstance().getMessage("auxaccount.upload.principleamaount"));
			currentTable.addColumn(balanceColumn, MessagesUtil.getInstance().getMessage("auxaccount.upload.balance"));
			currentTable.addColumn(balanceTypeColumn, MessagesUtil.getInstance().getMessage("auxaccount.upload.balanceType"));
			currentTable.addColumn(suspendUntilColumn, MessagesUtil.getInstance().getMessage("auxaccount.upload.suspendUntil"));
			currentTable.addColumn(startDateColumn, MessagesUtil.getInstance().getMessage("auxaccount.upload.startDate"));
		}

	}

	// START : ParentUpload methods
	@Override
	public String getPageHeaderKey() {
		return "auxaccount.upload.heading";
	}

	@Override
	public String getDataTitleKey() {
		return "auxaccount.upload.data.title";
	}

	@Override
	public String getUploadDescriptionKey() {
		return "auxaccount.upload.data.description";
	}

	@Override
	public String getUrlHandlerMapping() {
		return "secure/auxaccountbulkupload.do";
	}

	@Override
	public CellTable<AuxAccountCsvData> getTable() {
		return auxAccountsClltbl;
	}

	@Override
	public void displaySelected(AuxAccountCsvData selected, int left, int top) {
		selectedTableDataProvider.getList().clear();
		selectedTableDataProvider.getList().add(selected);
		selectedTableDataProvider.refresh();

		// Throw out a popup with a limited width and horizontal scrollbar
		DialogBox simplePopup = new DialogBox(true);
		simplePopup.setText(MessagesUtil.getInstance().getMessage(""));
		simplePopup.setAnimationEnabled(true);
		ScrollPanel scrollPanel = new ScrollPanel(selectedTable);
		int popupWidth = Window.getClientWidth() - clientFactory.getPrimaryLayoutView().getSidePanelStackWidth() - 100;
		scrollPanel.setWidth(popupWidth + "px");
		simplePopup.setWidget(scrollPanel);
		simplePopup.setPopupPosition(left, top);
		simplePopup.show();

	}

	@Override
	public Column<AuxAccountCsvData, String> getErrorColumn() {
		return errorColumn;
	}

	@Override
	public List<AuxAccountCsvData> getTransCsvList(String result) {
		AuxAccountCsvMapToData csvMapData = new AuxAccountCsvMapToData();
		HashMap<Integer, String> csvFieldMap = new HashMap<Integer, String>();

		isCsvDataValid = true;

		List<AuxAccountCsvData> transCsvDataList = new ArrayList<AuxAccountCsvData>();
		String[] transStringArray = result.split("\r\n|[\r\n]");// System.lineSeparator());
		for (String trans : transStringArray) {
			if (trans != null && !trans.isEmpty()) {
				if (trans.contains("Info: Required"))
					continue;

				if (trans.contains("Aux Account Name") && csvFieldMap.isEmpty()) {
					try {
						csvFieldMap = csvMapData.constructCsvFieldMap(trans);
						continue;
					} catch (Exception e) {
						String[] paramArr = new String[] { e.getMessage().substring(e.getMessage().indexOf(':') + 1) }; // Strips out "Unknown Column Heading:", leaves Heading Name
						Dialogs.centreErrorMessage(
								MessagesUtil.getInstance().getMessage("bulk.upload.file.unrecognized.heading.error", paramArr),
								MediaResourceUtil.getInstance().getErrorIcon(),
								MessagesUtil.getInstance().getMessage("button.close"));
					}
				}

				AuxAccountCsvData auxData = null;
				try {
					auxData = new AuxAccountCsvData(csvFieldMap, trans, true);
				} catch (Exception e) {
					logger.info("AUXACCOUNTUPLOAD ERROR CREATING AuxAccountCsvData!! Exception= " + e.getMessage());
					Dialogs.centreErrorMessage(
							MessagesUtil.getInstance().getMessage("bulk.upload.object.creation.error", new String[] { "AuxAccount" }),
							MediaResourceUtil.getInstance().getErrorIcon(),
							MessagesUtil.getInstance().getMessage("button.close"));
				}
				if (auxData == null || !auxData.getErrors().isEmpty()) {
					isCsvDataValid = false;
				}
				transCsvDataList.add(auxData);
			}
		}
		return transCsvDataList;
	}

	@Override
	public boolean isCsvDataValid() {
		return isCsvDataValid;
	}
	// END : ParentUpload methods

	// START : Workspace methods
	@Override
	public void onArrival(Place place) {
	}

	@Override
	public void onLeaving() {
	}

	@Override
	public void onSelect() {
	}

	@Override
	public void onClose() {
	}

	@Override
	public boolean handles(Place place) {
		return (place instanceof AuxAccountUploadPlace);
	}
	// END : Workspace methods
}
