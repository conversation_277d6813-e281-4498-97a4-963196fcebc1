package za.co.ipay.metermng.client.widget.tree;

import static com.google.gwt.dom.client.BrowserEvents.BLUR;
import static com.google.gwt.dom.client.BrowserEvents.DBLCLICK;
import static com.google.gwt.dom.client.BrowserEvents.KEYDOWN;
import static com.google.gwt.dom.client.BrowserEvents.KEYUP;

import java.util.logging.Level;
import java.util.logging.Logger;

import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.datatypes.RecordStatus;
import za.co.ipay.metermng.shared.GenGroupData;

import com.google.gwt.cell.client.AbstractEditableCell;
import com.google.gwt.cell.client.ValueUpdater;
import com.google.gwt.core.client.GWT;
import com.google.gwt.dom.client.Element;
import com.google.gwt.dom.client.EventTarget;
import com.google.gwt.dom.client.InputElement;
import com.google.gwt.dom.client.NativeEvent;
import com.google.gwt.event.dom.client.KeyCodes;
import com.google.gwt.safehtml.client.SafeHtmlTemplates;
import com.google.gwt.safehtml.shared.SafeHtml;
import com.google.gwt.safehtml.shared.SafeHtmlBuilder;
import com.google.gwt.text.shared.SafeHtmlRenderer;
import com.google.gwt.text.shared.SimpleSafeHtmlRenderer;

/**
 * GroupReadOrEditableCell displays a group's name and either allows the name to be edited in a text box or just
 * displayed in a read only text box. 
 * 
 * <AUTHOR>
 */
public class GroupReadOrEditableCell extends AbstractEditableCell<GenGroupData, CurrentViewData> {
    
    private static Logger logger = Logger.getLogger(GroupReadOrEditableCell.class.getName());

    interface Template extends SafeHtmlTemplates {
        @Template("<input type=\"text\" value=\"{0}\" tabindex=\"-1\" size='10'></input>")
        SafeHtml input(String value);
    }

    interface ReadOnlyTemplate extends SafeHtmlTemplates {
        @Template("<input type=\"text\" value=\"{0}\" tabindex=\"-1\" size='10' readonly=\"true\"></input>")
        SafeHtml input(String value);
    }
    
    private GroupDataTreeHandler treeHandler;
    private static Template template;
    private static ReadOnlyTemplate readOnlyTemplate;
    private final SafeHtmlRenderer<String> renderer;
    private ClientFactory clientFactory;

    public GroupReadOrEditableCell(GroupDataTreeHandler treeHandler) {
        this(treeHandler, SimpleSafeHtmlRenderer.getInstance());
    }
    
    public GroupReadOrEditableCell(ClientFactory clientFactory, GroupDataTreeHandler treeHandler) {
        this(treeHandler, SimpleSafeHtmlRenderer.getInstance());
        this.clientFactory = clientFactory;
    }

    public GroupReadOrEditableCell(GroupDataTreeHandler treeHandler, SafeHtmlRenderer<String> renderer) {
        super(DBLCLICK, KEYUP, KEYDOWN, BLUR);
        try {
            this.treeHandler = treeHandler;
            if (template == null) {
                template = GWT.create(Template.class);
            }
            if (readOnlyTemplate == null) {
                readOnlyTemplate = GWT.create(ReadOnlyTemplate.class);
            }
            if (renderer == null) {
                throw new IllegalArgumentException("renderer is null");
            }        
        } catch (Exception e) {
            logger.log(Level.SEVERE, "Error with cell:", e);
        }
        this.renderer = renderer;
    }

    @Override
    public boolean isEditing(Context context, Element parent, GenGroupData value) {
        CurrentViewData viewData = getViewData(context.getKey());
        return viewData == null ? false : viewData.isEditing();
    }

    @Override
    public void onBrowserEvent(Context context, Element parent, GenGroupData value, NativeEvent event, ValueUpdater<GenGroupData> valueUpdater) {
        Object key = context.getKey();
        CurrentViewData viewData = getViewData(key);
        if (viewData != null && viewData.isEditing()) {
            editEvent(context, parent, value, viewData, event, valueUpdater);
        } else {
            String type = event.getType();
            int keyCode = event.getKeyCode();
            boolean enterPressed = KEYUP.equals(type) && keyCode == KeyCodes.KEY_ENTER;
            // Disable double click editing if this group has an org_group
            // Changing org groups can also be a bulk action, because it has 
            // affects row level security group access.
            if (value.getAccessGroupId() == null && (DBLCLICK.equals(type) || enterPressed)) {
                if (viewData == null) {
                    viewData = new CurrentViewData(value);
                    setViewData(key, viewData);
                } else {
                    viewData.setEditing(true);
                }
                edit(context, parent, value);
            }
        }
    }

    @Override
    public void render(Context context, GenGroupData value, SafeHtmlBuilder sb) {
        // Get the view data.
        Object key = context.getKey();
        CurrentViewData viewData = getViewData(key);
        if (viewData != null && !viewData.isEditing() && value != null && value.getName().equals(viewData.getText())) {
            clearViewData(key);
            viewData = null;
        }
        
        String toRender = "";
        if (value != null) {
            toRender = value.getName();
        }
        if (viewData != null) {
            String text = viewData.getText();
            if (viewData.isEditing()) {
                //Do not use the renderer in edit mode because the value of a text input element is always treated as text. 
                //SafeHtml isn't valid in the context of the value attribute.
                if (treeHandler.isGroupViewOnly(value) || value.getAccessGroupId() != null) {
                    sb.append(readOnlyTemplate.input(text));
                } else {
                    sb.append(template.input(text));
                }
                return;
            } else {
                // The user pressed enter, but view data still exists.
                toRender = text;
            }
        }

        if (toRender != null && toRender.trim().length() > 0) {
            if (RecordStatus.ACT.equals(value.getRecordStatus())) {
                sb.appendHtmlConstant("<span class='active'>");
            } else {
                sb.appendHtmlConstant("<span class='inactive'>");
            }
            sb.append(renderer.render(toRender));
            sb.appendHtmlConstant("</span>");
            sb.appendHtmlConstant("<input type='hidden' id='"+value.getId()+"'/>");
        } else {
            //Render a blank space to force the rendered element to have a height. Otherwise it is not clickable.
            sb.appendHtmlConstant("\u00A0");
        }
        
    }

    @Override
    public boolean resetFocus(Context context, Element parent, GenGroupData value) {
        if (isEditing(context, parent, value)) {
            getInputElement(parent).focus();
            return true;
        }
        return false;
    }
    
    protected void edit(Context context, Element parent, GenGroupData value) {
        setValue(context, parent, value);
        treeHandler.setPosition(parent);
        treeHandler.openGenGroupEditPanel(value);
    }

    private void cancel(Context context, Element parent, GenGroupData value) {
        clearInput(getInputElement(parent));
        setValue(context, parent, value);
    }

    private native void clearInput(Element input) /*-{
		if (input.selectionEnd)
			input.selectionEnd = input.selectionStart;
		else if ($doc.selection)
			$doc.selection.clear();
    }-*/;

    private void commit(Context context, Element parent, CurrentViewData viewData, ValueUpdater<GenGroupData> valueUpdater) {
        GenGroupData value = updateViewData(parent, viewData, false);
        clearInput(getInputElement(parent));
        setValue(context, parent, viewData.getOriginal());
        logger.info("EditableCell commit: "+viewData.getOriginal()+" valueUpdater:"+valueUpdater);
        if (valueUpdater != null) {
            valueUpdater.update(value);
        } else {
            treeHandler.setPosition(parent);
            //FIXME Going to force a save here cos the valueUpdater shouldn't be null :(
            treeHandler.saveGenGroup(value);
        }
    }
    
    private void editEvent(Context context, Element parent, GenGroupData value, CurrentViewData viewData, NativeEvent event, ValueUpdater<GenGroupData> valueUpdater) {
        String type = event.getType();
        boolean keyUp = KEYUP.equals(type);
        boolean keyDown = KEYDOWN.equals(type);
        if (keyUp || keyDown) {
            int keyCode = event.getKeyCode();
            if (keyUp && keyCode == KeyCodes.KEY_ENTER) {
                // Commit the change.
                if (viewData.getText().trim().isEmpty()) {
                 // Cancel edit mode.
                    String originalText = viewData.getOriginal().getName();
                    if (viewData.isEditingAgain()) {
                        viewData.setText(originalText);
                        viewData.setEditing(false);
                    } else {
                        setViewData(context.getKey(), null);
                    }
                    cancel(context, parent, value);
                } else {
                    commit(context, parent, viewData, valueUpdater);
                }
                
            } else if (keyUp && keyCode == KeyCodes.KEY_ESCAPE) {
                // Cancel edit mode.
                String originalText = viewData.getOriginal().getName();
                if (viewData.isEditingAgain()) {
                    viewData.setText(originalText);
                    viewData.setEditing(false);
                } else {
                    setViewData(context.getKey(), null);
                }
                cancel(context, parent, value);
            } else {
                // Update the text in the view data on each key.
                updateViewData(parent, viewData, true);
            }
        } else if (BLUR.equals(type)) {
            // Commit the change. Ensure that we are blurring the input element and
            // not the parent element itself.
            EventTarget eventTarget = event.getEventTarget();
            if (Element.is(eventTarget)) {
                Element target = Element.as(eventTarget);
                if ("input".equals(target.getTagName().toLowerCase())) {
                    if (viewData.getText().trim().isEmpty()) {
                     // Cancel edit mode.
                        String originalText = viewData.getOriginal().getName();
                        if (viewData.isEditingAgain()) {
                            viewData.setText(originalText);
                            viewData.setEditing(false);
                        } else {
                            setViewData(context.getKey(), null);
                        }
                        cancel(context, parent, value);
                    } else {
                        commit(context, parent, viewData, valueUpdater);
                    }
                }
            }
        }
    }

    private InputElement getInputElement(Element parent) {
        return parent.getFirstChild().<InputElement> cast();
    }

    private GenGroupData updateViewData(Element parent, CurrentViewData viewData, boolean isEditing) {
        InputElement input = (InputElement) parent.getFirstChild();
        String value = input.getValue();
        viewData.setText(value);
        viewData.setEditing(isEditing);
        if (!value.trim().isEmpty()) {
            viewData.getOriginal().setName(value);
        }
        return viewData.getOriginal();
    }     
}
