package za.co.ipay.metermng.client.view.workspace.group.user;

import za.co.ipay.gwt.common.client.Dialogs;
import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.gwt.common.client.workspace.WorkspaceCreateCallback;
import za.co.ipay.gwt.common.client.workspace.WorkspaceFactory;
import za.co.ipay.gwt.common.shared.exception.AccessControlException;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.history.UserGroupPlace;
import za.co.ipay.metermng.client.resource.image.MediaResource.MediaResourceUtil;
import za.co.ipay.metermng.client.rpc.callback.ClientCallback;
import za.co.ipay.metermng.shared.MeterMngStatics;
import za.co.ipay.metermng.shared.dto.user.MeterMngUser;

import com.google.gwt.place.shared.Place;

public class UserGroupWorkspaceFactory implements WorkspaceFactory {
    
    private ClientFactory clientFactory;

    public UserGroupWorkspaceFactory(ClientFactory clientFactory) {
        this.clientFactory = clientFactory;
        clientFactory.getWorkspaceContainer().register(this);
    }

    @Override
    public void createWorkspace(final Place place, final WorkspaceCreateCallback workspaceCreateCallback) {
        //user must be at root level AND have permission to edit user access groups
        clientFactory.getUserRpc().getCurrentUser(new ClientCallback<MeterMngUser>() {
            @Override
            public void onSuccess(MeterMngUser result) {
                if (result.getAssignedGroup().getParentId() != null ||
                        !clientFactory.getUser().hasPermission(MeterMngStatics.ACCESS_PERMISSION_MM_USER_GROUP_ADMIN)) {
                    Dialogs.displayErrorMessage(MessagesUtil.getInstance().getMessage("error.accessdenied"), 
                            MediaResourceUtil.getInstance().getLockedIcon(), 
                            MessagesUtil.getInstance().getMessage("button.close"));
                    workspaceCreateCallback.onWorkspaceCreationFailed(new AccessControlException("Access is Denied"));
                    return;               
                }
                try {
                    UserGroupWorkspaceView view = new UserGroupWorkspaceView(clientFactory, (UserGroupPlace) place);
                    workspaceCreateCallback.onWorkspaceCreated(view);
                } catch (Exception e) {
                    workspaceCreateCallback.onWorkspaceCreationFailed(e);
                }
            }
        }); 
    }

    @Override
    public boolean handles(Place place) {
        if (place instanceof UserGroupPlace) {
            return true;
        } else {
            return false;
        }
    }
}
