package za.co.ipay.metermng.client.event;

import com.google.gwt.event.shared.GwtEvent;

public class OpenAdminDashboardEvent extends GwtEvent<OpenAdminDashboardEventHandler> {

    public static Type<OpenAdminDashboardEventHandler> TYPE = new Type<OpenAdminDashboardEventHandler>();
    
    @Override
    public Type<OpenAdminDashboardEventHandler> getAssociatedType() {
        return TYPE;
    }

    @Override
    protected void dispatch(OpenAdminDashboardEventHandler handler) {
        handler.openAdminDashboard(this);
    }

}