package za.co.ipay.metermng.client.view.workspace.bulkupload.auxtransupload;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.logging.Logger;

import com.google.gwt.cell.client.AbstractCell;
import com.google.gwt.core.client.GWT;
import com.google.gwt.place.shared.Place;
import com.google.gwt.safehtml.shared.SafeHtmlBuilder;
import com.google.gwt.safehtml.shared.SafeHtmlUtils;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.user.cellview.client.CellTable;
import com.google.gwt.user.cellview.client.Column;
import com.google.gwt.user.cellview.client.TextColumn;
import com.google.gwt.user.client.Window;
import com.google.gwt.user.client.ui.DialogBox;
import com.google.gwt.user.client.ui.ScrollPanel;
import com.google.gwt.user.client.ui.Widget;
import com.google.gwt.view.client.ListDataProvider;

import za.co.ipay.gwt.common.client.Dialogs;
import za.co.ipay.gwt.common.client.factory.ResourcesFactoryUtil;
import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.history.AuxTransUploadPlace;
import za.co.ipay.metermng.client.resource.image.MediaResource.MediaResourceUtil;
import za.co.ipay.metermng.client.view.component.bulkupload.FileUploadPanel;
import za.co.ipay.metermng.client.view.workspace.BaseWorkspace;
import za.co.ipay.metermng.client.view.workspace.bulkupload.ParentUpload;
import za.co.ipay.metermng.shared.MeterMngStatics;
import za.co.ipay.metermng.shared.dto.uploaddata.transuploaddata.AuxTransCsvData;
import za.co.ipay.metermng.shared.dto.uploaddata.transuploaddata.TransCsvMapToData;

public class AuxTransUploadWorkspaceView extends BaseWorkspace implements ParentUpload<AuxTransCsvData> {

	@UiField(provided = true) FileUploadPanel<AuxTransCsvData> fileUploadPanel;

	private CellTable<AuxTransCsvData> clltblTransactions;
	private Column<AuxTransCsvData, String> errorColumn;
	private boolean isCsvDataValid;

	private CellTable<AuxTransCsvData> selectedTable;
	private ListDataProvider<AuxTransCsvData> selectedTableDataProvider;

	private static final int DEFAULT_PAGE_SIZE = 15;
	private static final boolean IS_CUSTOMER_TRANS_UPLOAD = false;
	private static final String ROW_INFO_IDENTIFIER = "Info: Required";
	private static final String ROW_HEADER_IDENTIFIER = "Aux Account Name";

	private static Logger logger = Logger.getLogger(AuxTransUploadWorkspaceView.class.getName());

	private static AuxTransUploadWorkspaceViewUiBinder uiBinder = GWT.create(AuxTransUploadWorkspaceViewUiBinder.class);

	interface AuxTransUploadWorkspaceViewUiBinder extends UiBinder<Widget, AuxTransUploadWorkspaceView> {
	}

	public AuxTransUploadWorkspaceView(ClientFactory clientFactory, AuxTransUploadPlace place) {
		this.clientFactory = clientFactory;
		initTable();
		createTable();
		fileUploadPanel = new FileUploadPanel<AuxTransCsvData>(clientFactory, MeterMngStatics.AUX_TRANSACTION_UPLOAD, this);
		initWidget(uiBinder.createAndBindUi(this));
		setPlaceString(AuxTransUploadPlace.getPlaceAsString(place));
		setHeaderText(MessagesUtil.getInstance().getMessage("auxaccount.trans.upload.heading"));
		createSelectedTable();
	}

	private void initTable() {
		clltblTransactions = new CellTable<AuxTransCsvData>(DEFAULT_PAGE_SIZE, ResourcesFactoryUtil.getInstance().getCellTableResources());
	}

	private void createTable() {
		AbstractCell<String> errorCell = new AbstractCell<String>() {
			@Override
			public void render(Context context, String value, SafeHtmlBuilder sb) {
				if (value == null) {
					return;
				}
				sb.appendHtmlConstant("<span class=\"errorInlineNotBold\">" + value + "</span>");
			}
		};
		errorColumn = new Column<AuxTransCsvData, String>(errorCell) {
			@Override
			public String getValue(AuxTransCsvData object) {
				return object.getErrors();
			}
		};

		clltblTransactions.addColumn(errorColumn, SafeHtmlUtils.fromSafeConstant("<span class=\"error\">" + MessagesUtil.getInstance().getMessage("bulk.upload.errors") + "</span>"));
		createTableCommons(clltblTransactions, false);
	}

	private void createSelectedTable() {
		selectedTable = new CellTable<AuxTransCsvData>();
		createTableCommons(selectedTable, true);
		selectedTableDataProvider = new ListDataProvider<AuxTransCsvData>();
		selectedTableDataProvider.addDataDisplay(selectedTable);
	}

	private void createTableCommons(CellTable<AuxTransCsvData> currentTable, boolean isSelectedTable) {

		TextColumn<AuxTransCsvData> auxAccountNameColumn = new TextColumn<AuxTransCsvData>() {
			@Override
			public String getValue(AuxTransCsvData object) {
				return object.getAuxAccountName();
			}
		};

		TextColumn<AuxTransCsvData> agreementRefColumn = new TextColumn<AuxTransCsvData>() {
			@Override
			public String getValue(AuxTransCsvData object) {
				return object.getAgreementRef();
			}
		};

		TextColumn<AuxTransCsvData> amtInclTaxColumn = null;
		TextColumn<AuxTransCsvData> amtTaxColumn = null;
		TextColumn<AuxTransCsvData> transDateColumn = null;
		TextColumn<AuxTransCsvData> accountRefColumn = null;
		TextColumn<AuxTransCsvData> commentColumn = null;

		if (isSelectedTable) {
			amtInclTaxColumn = new TextColumn<AuxTransCsvData>() {
				@Override
				public String getValue(AuxTransCsvData object) {
					return object.getAmtInclTax();
				}
			};

			amtTaxColumn = new TextColumn<AuxTransCsvData>() {
				@Override
				public String getValue(AuxTransCsvData object) {
					return object.getAmtTax();
				}
			};

			transDateColumn = new TextColumn<AuxTransCsvData>() {
				@Override
				public String getValue(AuxTransCsvData object) {
					return object.getTransDate();
				}
			};

			accountRefColumn = new TextColumn<AuxTransCsvData>() {
				@Override
				public String getValue(AuxTransCsvData object) {
					return object.getAccountRef();
				}
			};

			commentColumn = new TextColumn<AuxTransCsvData>() {
				@Override
				public String getValue(AuxTransCsvData object) {
					return object.getComment();
				}
			};
		}

		// Add the columns.
		currentTable.addColumn(auxAccountNameColumn, MessagesUtil.getInstance().getMessage("auxaccount.trans.upload.auxaccountname"));
		currentTable.addColumn(agreementRefColumn, MessagesUtil.getInstance().getMessage("auxaccount.trans.upload.agreementref"));
		if (isSelectedTable) {
			currentTable.addColumn(amtInclTaxColumn, MessagesUtil.getInstance().getMessage("trans.bulk.upload.amt.incl.tax"));
			currentTable.addColumn(amtTaxColumn, MessagesUtil.getInstance().getMessage("trans.bulk.upload.amt.tax"));
			currentTable.addColumn(transDateColumn, MessagesUtil.getInstance().getMessage("trans.bulk.upload.trans.date"));
			currentTable.addColumn(accountRefColumn, MessagesUtil.getInstance().getMessage("trans.bulk.upload.account.ref"));
			currentTable.addColumn(commentColumn, MessagesUtil.getInstance().getMessage("trans.bulk.upload.comment"));
		}
	}

	// START : ParentUpload methods
	@Override
	public String getPageHeaderKey() {
		return "auxaccount.trans.upload.heading";
	}

	@Override
	public String getDataTitleKey() {
		return "auxaccount.trans.upload.data.title";
	}

	@Override
	public String getUploadDescriptionKey() {
		return "auxaccount.trans.upload.data.description";
	}

	@Override
	public String getUrlHandlerMapping() {
		return "secure/auxtransbulkupload.do";
	}

	@Override
	public CellTable<AuxTransCsvData> getTable() {
		return clltblTransactions;
	}

	@Override
	public void displaySelected(AuxTransCsvData selected, int left, int top) {
		selectedTableDataProvider.getList().clear();
		selectedTableDataProvider.getList().add(selected);
		selectedTableDataProvider.refresh();

		// Throw out a popup with a limited width and horizontal scrollbar
		DialogBox simplePopup = new DialogBox(true);
		simplePopup.setText(MessagesUtil.getInstance().getMessage(""));
		simplePopup.setAnimationEnabled(true);
		ScrollPanel scrollPanel = new ScrollPanel(selectedTable);
		int popupwidth = Window.getClientWidth() - clientFactory.getPrimaryLayoutView().getSidePanelStackWidth() - 100;
		scrollPanel.setWidth(popupwidth + "px");
		simplePopup.setWidget(scrollPanel);
		simplePopup.setPopupPosition(left, top);
		simplePopup.show();
	}

	@Override
	public Column<AuxTransCsvData, String> getErrorColumn() {
		return errorColumn;
	}

	@Override
	public List<AuxTransCsvData> getTransCsvList(String result) {
		TransCsvMapToData csvMapData = new TransCsvMapToData(IS_CUSTOMER_TRANS_UPLOAD);
		HashMap<Integer, String> csvFieldMap = new HashMap<Integer, String>();

		isCsvDataValid = true;

		List<AuxTransCsvData> transCsvDataList = new ArrayList<AuxTransCsvData>();
		String[] transStringArray = result.split("\r\n|[\r\n]"); // System.lineSeparator());
		for (String trans : transStringArray) {
			if (trans != null && !trans.isEmpty()) {
				if (trans.contains(ROW_INFO_IDENTIFIER)) {
					continue;
				}
				if (trans.contains(ROW_HEADER_IDENTIFIER) && csvFieldMap.isEmpty()) {
					// for headerLine construct the index to DataName Map
					try {
						csvFieldMap = csvMapData.constructCsvFieldMap(trans);
						continue;
					} catch (Exception e) {
						// Strips out "Unknown Column Heading:", leaves Heading Name
						String[] paramArr = new String[] { e.getMessage().substring(e.getMessage().indexOf(':') + 1) };
						Dialogs.centreErrorMessage(
								MessagesUtil.getInstance().getMessage("bulk.upload.file.unrecognized.heading.error", paramArr),
								MediaResourceUtil.getInstance().getErrorIcon(),
								MessagesUtil.getInstance().getMessage("button.close"));
					}
				}

				AuxTransCsvData custTrans = null;
				try {
					custTrans = new AuxTransCsvData(csvFieldMap, trans, true);
				} catch (Exception e) {
					logger.info("AUXTRANSUPLOAD ERROR CREATING AuxTransCsvData!! Exception= " + e.getMessage());
					Dialogs.centreErrorMessage(
							MessagesUtil.getInstance().getMessage("bulk.upload.object.creation.error", new String[] { "CustomerTrans" }),
							MediaResourceUtil.getInstance().getErrorIcon(),
							MessagesUtil.getInstance().getMessage("button.close"));
				}
				if (custTrans == null || !custTrans.getErrors().isEmpty()) {
					isCsvDataValid = false;
				}
				transCsvDataList.add(custTrans);
			}
		}
		return transCsvDataList;
	}

	@Override
	public boolean isCsvDataValid() {
		return isCsvDataValid;
	}
	// END : ParentUpload methods

	// START : Workspace methods
	@Override
	public void onArrival(Place place) {
	}

	@Override
	public void onLeaving() {
	}

	@Override
	public void onSelect() {
	}

	@Override
	public void onClose() {
	}

	@Override
	public boolean handles(Place place) {
		return (place instanceof AuxTransUploadPlace);
	}
	// END : Workspace methods
}
