package za.co.ipay.metermng.client.validation;

import javax.validation.Validator;
import javax.validation.groups.Default;

import com.google.gwt.core.client.GWT;
import com.google.gwt.validation.client.AbstractGwtValidatorFactory;
import com.google.gwt.validation.client.GwtValidation;
import com.google.gwt.validation.client.impl.AbstractGwtValidator;

import za.co.ipay.gwt.common.shared.validation.group.ClientGroup;
import za.co.ipay.metermng.mybatis.generated.model.AccountTrans;
import za.co.ipay.metermng.mybatis.generated.model.AppSetting;
import za.co.ipay.metermng.mybatis.generated.model.AuxChargeSchedule;
import za.co.ipay.metermng.mybatis.generated.model.AuxType;
import za.co.ipay.metermng.mybatis.generated.model.BillingDet;
import za.co.ipay.metermng.mybatis.generated.model.Customer;
import za.co.ipay.metermng.mybatis.generated.model.CustomerAccThresholds;
import za.co.ipay.metermng.mybatis.generated.model.CustomerAccount;
import za.co.ipay.metermng.mybatis.generated.model.CustomerAgreement;
import za.co.ipay.metermng.mybatis.generated.model.EndDeviceStore;
import za.co.ipay.metermng.mybatis.generated.model.GroupEntity;
import za.co.ipay.metermng.mybatis.generated.model.GroupType;
import za.co.ipay.metermng.mybatis.generated.model.Location;
import za.co.ipay.metermng.mybatis.generated.model.Manufacturer;
import za.co.ipay.metermng.mybatis.generated.model.Mdc;
import za.co.ipay.metermng.mybatis.generated.model.MdcChannel;
import za.co.ipay.metermng.mybatis.generated.model.Meter;
import za.co.ipay.metermng.mybatis.generated.model.MeterModel;
import za.co.ipay.metermng.mybatis.generated.model.ModelChannelConfig;
import za.co.ipay.metermng.mybatis.generated.model.PricingStructure;
import za.co.ipay.metermng.mybatis.generated.model.ScheduledTask;
import za.co.ipay.metermng.mybatis.generated.model.SpecialActionReasons;
import za.co.ipay.metermng.mybatis.generated.model.SpecialActions;
import za.co.ipay.metermng.mybatis.generated.model.StsMeter;
import za.co.ipay.metermng.mybatis.generated.model.StsSupplyGroup;
import za.co.ipay.metermng.mybatis.generated.model.Tariff;
import za.co.ipay.metermng.mybatis.generated.model.TaskSchedule;
import za.co.ipay.metermng.mybatis.generated.model.TouCalendar;
import za.co.ipay.metermng.mybatis.generated.model.TouCalendarSeason;
import za.co.ipay.metermng.mybatis.generated.model.TouDayProfile;
import za.co.ipay.metermng.mybatis.generated.model.TouPeriod;
import za.co.ipay.metermng.mybatis.generated.model.TouSeason;
import za.co.ipay.metermng.mybatis.generated.model.TouSpecialDay;
import za.co.ipay.metermng.mybatis.generated.model.UpMeterInstall;
import za.co.ipay.metermng.mybatis.generated.model.UsagePoint;
import za.co.ipay.metermng.shared.AuxAccountData;
import za.co.ipay.metermng.shared.GroupHierarchyData;
import za.co.ipay.metermng.shared.tariff.basic.BasicPrivateUtilityTariffCalcContents;

/**
 *  ValidatorFactory is used to bootstrap the validation framework for use on the client-side. 
 *  The actual validator is generated by GWT using our specified classes and their validation annotations as well as
 *  the appropriate groups.
 */
public class ValidatorFactory extends AbstractGwtValidatorFactory {

    /** 
     * GwtClientValidator is used to define which classes can be validated by the generated GWT Validator. 
     * Add the classes you want validated to the value below.  
     * The groups specify which group's rules are going to be applied by the validator.
     */
    @GwtValidation(groups = { Default.class, ClientGroup.class }, 
                   value = { GroupType.class, GroupEntity.class, GroupHierarchyData.class,
                             StsSupplyGroup.class, 
                             AuxChargeSchedule.class,  AuxType.class, AuxAccountData.class,
                             PricingStructure.class, Tariff.class,
                             Meter.class,
                             StsMeter.class,
                             Location.class,
                             Customer.class,  CustomerAgreement.class, CustomerAccount.class,                             
                             EndDeviceStore.class,
                             TouSeason.class, TouPeriod.class, TouCalendar.class, TouCalendarSeason.class,
                             TouDayProfile.class, TouSpecialDay.class,
                             Manufacturer.class, MeterModel.class,
                             Mdc.class, MdcChannel.class, ModelChannelConfig.class,
                             TaskSchedule.class, ScheduledTask.class, BasicPrivateUtilityTariffCalcContents.class, UsagePoint.class,
                             AppSetting.class, AccountTrans.class, CustomerAccThresholds.class, BillingDet.class, 
                             SpecialActions.class, SpecialActionReasons.class, UpMeterInstall.class
                            }) 
    public interface GwtClientValidator extends Validator {
    }

    @Override
    public AbstractGwtValidator createValidator() {
        return GWT.create(GwtClientValidator.class);
    }
}
