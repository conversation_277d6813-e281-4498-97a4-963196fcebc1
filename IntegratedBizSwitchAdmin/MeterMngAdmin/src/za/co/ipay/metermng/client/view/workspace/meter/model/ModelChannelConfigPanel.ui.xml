<!DOCTYPE ui:UiBinder SYSTEM "http://dl.google.com/gwt/DTD/xhtml.ent">
<ui:UiBinder xmlns:ui="urn:ui:com.google.gwt.uibinder" 
             xmlns:g="urn:import:com.google.gwt.user.client.ui"
             xmlns:p1="urn:import:za.co.ipay.gwt.common.client.form" 
             xmlns:g2="urn:import:com.google.gwt.user.datepicker.client"
             xmlns:w="urn:import:za.co.ipay.gwt.common.client.widgets">
    
    <ui:with field="msg" type="za.co.ipay.metermng.client.i18n.UiMessages" />

    <ui:style>
        .panel_spacing {
            display: inline;
            margin_top: 0.1em;
            margin-right: 1em; 
            margin-left: 0.1em;
            vertical-align: top;
        }
        .label_border {
            display: inline;
            border:1px solid #99a6b2;
            padding_right:0.2em;
            padding_left:0.2em;
            padding-bottom: 0.2em;
        }
        .panel_checkbox_spacing {
            display: inline;
            margin-right: 0.1em; 
            margin-left: 1em;
        }
    </ui:style>  
    
    <g:FlowPanel>
    
        <p1:FormRowPanel>
            <g:Label text="{msg.getChannelConfigMdcTitleName}:" styleName="{style.panel_spacing}"/>
            <g:TextBox ui:field="mdcName" text="" width="50" enabled="false" />
        </p1:FormRowPanel>    
    
        <p1:FormRowPanel>
            <p1:FormElement ui:field="mdcChannelElement" labelText="{msg.getChannelFieldTitlename}:" helpMsg="{msg.getChannelFieldTitlenameHelp}">
                <g:ListBox ui:field="mdcChannelListBox"/>
            </p1:FormElement>
        </p1:FormRowPanel>    
    
        <p1:FormRowPanel>
            <g:Label text="{msg.getChannelNameLabel}:" styleName="{style.panel_spacing}"/>
            <g:TextBox ui:field="channelName" text="" width="50" enabled="false"/>
        </p1:FormRowPanel>
 
        <p1:FormRowPanel>
            <g:Label text="{msg.getChannelValueLabel}:" styleName="{style.panel_spacing}"/>
            <g:TextBox ui:field="channelValue" text="" width="20" enabled="false" />
            
            <g:CheckBox ui:field="activeBox" text="{msg.getChannelActive}" styleName="{style.panel_checkbox_spacing}"/>
        </p1:FormRowPanel>
        
        <p1:FormRowPanel>
            <g:Label text="{msg.getChannelDescripLabel}:" styleName="{style.panel_spacing}"/>
            <g:TextBox ui:field="channelDescrip" text="" width="50" enabled="false" />
        </p1:FormRowPanel>
        
        <p1:FormRowPanel>
            <g:Label text="{msg.getBillingDetNamesLabel}:" styleName="{style.panel_spacing}"/>
            <g:TextArea ui:field="billingDetName" text="" width="50" enabled="false"/>
        </p1:FormRowPanel>
        
        <p1:FormRowPanel>
            <g:Label text="{msg.getChannelMeterReadingTypeLabel}:" styleName="{style.panel_spacing}"/>
            <g:TextBox ui:field="meterReadingTypeName" text="" width="50" enabled="false"/>
        </p1:FormRowPanel>                
        
        <g:HorizontalPanel>
            <p1:FormRowPanel>
                <p1:FormElement ui:field="mdcTimeIntervalElement" labelText="{msg.getMdcChannelTimeIntervalLabel}:">
                    <g:TextBox ui:field="mdcTimeIntervalTextBox" enabled="false"/>
                </p1:FormElement>
            </p1:FormRowPanel>
            <p1:FormRowPanel>
                <p1:FormElement ui:field="timeIntervalElement" labelText="{msg.getChannelOverrideTimeIntervalLabel}:" helpMsg="{msg.getChannelTimeIntervalHelp}">
                    <g:ListBox ui:field="timeIntervalListBox" />
                </p1:FormElement>
            </p1:FormRowPanel>
        </g:HorizontalPanel>
        
        <g:HorizontalPanel>
            <p1:FormRowPanel>
                <p1:FormElement ui:field="mdcMaxSizeElement" labelText="{msg.getMdcChannelMaxSizeLabel}:" >
                    <p1:BigDecimalValueBox ui:field="mdcMaxSizeBox" styleName="gwt-TextBox-ipay" visibleLength="15" enabled="false"/>
                </p1:FormElement>
            </p1:FormRowPanel>
            <p1:FormRowPanel>
                <p1:FormElement ui:field="maxSizeElement" labelText="{msg.getChannelOverrideMaxSizeLabel}:" required="true" helpMsg="{msg.getChannelMaxSizeHelp}">
                    <p1:BigDecimalValueBox ui:field="maxSizeBox" styleName="gwt-TextBox-ipay" visibleLength="15"/>
                </p1:FormElement>
            </p1:FormRowPanel>
        </g:HorizontalPanel>

        <g:HorizontalPanel>
            <p1:FormRowPanel>
                <p1:FormElement ui:field="mdcReadingMultiplierElement" labelText="{msg.getMdcChannelReadingMultiplierLabel}:">
                    <p1:BigDecimalValueBox ui:field="mdcReadingMultiplierBox" styleName="gwt-TextBox-ipay" visibleLength="15" enabled="false"/>
                </p1:FormElement>
            </p1:FormRowPanel>
            <p1:FormRowPanel>
                <p1:FormElement ui:field="readingMultiplierElement" labelText="{msg.getChannelOverrideReadingMultiplierLabel}:" required="false" helpMsg="{msg.getChannelReadingMultiplierHelp}">
                    <p1:BigDecimalValueBox ui:field="readingMultiplierBox" styleName="gwt-TextBox-ipay" visibleLength="15"/>
                </p1:FormElement>
            </p1:FormRowPanel>
        </g:HorizontalPanel>
        
    </g:FlowPanel>
    
</ui:UiBinder> 