package za.co.ipay.metermng.client.history;

import za.co.ipay.metermng.client.event.TaskScheduleEvent;
import za.co.ipay.metermng.client.factory.ClientFactory;

import com.google.gwt.activity.shared.AbstractActivity;
import com.google.gwt.event.shared.EventBus;
import com.google.gwt.user.client.ui.AcceptsOneWidget;

public class TaskScheduleActivity extends AbstractActivity {

    private ClientFactory clientFactory;
    private TaskSchedulePlace place;
    
    public TaskScheduleActivity(TaskSchedulePlace place, ClientFactory clientFactory) {
        super();
        this.place = place;
        this.clientFactory = clientFactory;
    }
    
    @Override
    public void start(AcceptsOneWidget panel, EventBus eventBus) {
        clientFactory.getEventBus().fireEvent(new TaskScheduleEvent(place.getName()));
    }
}
