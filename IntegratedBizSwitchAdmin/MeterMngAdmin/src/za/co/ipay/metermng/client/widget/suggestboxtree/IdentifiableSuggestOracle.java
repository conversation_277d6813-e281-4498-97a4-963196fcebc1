package za.co.ipay.metermng.client.widget.suggestboxtree;

import com.google.gwt.user.client.ui.SuggestOracle;

/**
 * This Oracle allows finding items by id and identifying placeholder 
 * items that representing empty data.
 * 
 * When used with a SettableSuggestBox the suggest box gains the power 
 * of prepopulating the suggest box with a specific value based on id.
 * 
 * <AUTHOR>
 * @param <T> the type of id of the suggestion eg. long or a string
 */
public abstract class IdentifiableSuggestOracle<T> extends SuggestOracle {
    public abstract String getLabel();
    
    public abstract void findById(T id, FindSuggestionCallback callback);
    
    public boolean isEmptyItem(Suggestion suggestion) {
        return false;
    }
    
    public interface FindSuggestionCallback {
        void found(Suggestion suggestion);
    }
}
