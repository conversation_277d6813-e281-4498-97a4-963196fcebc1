package za.co.ipay.metermng.client.history;

import com.google.gwt.place.shared.Place;
import com.google.gwt.place.shared.PlaceTokenizer;
import com.google.gwt.place.shared.Prefix;

public class UserGroupPlace extends Place {

    public static UserGroupPlace ALL_USER_GROUP_PLACE = new UserGroupPlace();

    public UserGroupPlace() {
    }
    
    public static String getPlaceAsString() {
        return "userGroup:all";
    }

    @Prefix(value = "userGroup")
    public static class Tokenizer implements PlaceTokenizer<UserGroupPlace> {
        
        @Override
        public String getToken(UserGroupPlace place) {
            return "all";
        }

        @Override
        public UserGroupPlace getPlace(String token) {
            return new UserGroupPlace();
        }
    }
    
}
