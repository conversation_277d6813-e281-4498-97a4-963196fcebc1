package za.co.ipay.metermng.client.view.workspace.group.user;

import java.util.ArrayList;
import java.util.logging.Logger;

import za.co.ipay.gwt.common.client.Dialogs;
import za.co.ipay.gwt.common.client.form.FormElement;
import za.co.ipay.gwt.common.client.form.SimpleForm;
import za.co.ipay.gwt.common.client.handler.FormDataValueChangeHandler;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.form.SimpleFormPanel;
import za.co.ipay.metermng.client.i18n.UiMessagesUtil;
import za.co.ipay.metermng.client.resource.image.MediaResource.MediaResourceUtil;
import za.co.ipay.metermng.client.rpc.callback.ClientCallback;
import za.co.ipay.metermng.client.view.component.selection.SelectionDataWidget;
import za.co.ipay.metermng.mybatis.generated.model.GroupType;
import za.co.ipay.metermng.shared.dto.SelectionDataItem;
import za.co.ipay.metermng.shared.dto.user.UserData;

import com.google.gwt.core.client.GWT;
import com.google.gwt.event.dom.client.FocusEvent;
import com.google.gwt.event.dom.client.FocusHandler;
import com.google.gwt.event.logical.shared.SelectionEvent;
import com.google.gwt.event.logical.shared.SelectionHandler;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.user.client.ui.HorizontalPanel;
import com.google.gwt.user.client.ui.SuggestBox;
import com.google.gwt.user.client.ui.SuggestOracle;
import com.google.gwt.user.client.ui.SuggestOracle.Suggestion;
import com.google.gwt.user.client.ui.Widget;

/**
 * MeterMngUserPanel is a simple panel used to display a user and their current group.
 * <AUTHOR>
 */
public class MeterMngUserPanel extends SimpleFormPanel {
    
    private boolean horizontalLayout = false;
    private boolean mustSelectLastlevel = false;
    private boolean autoSelect = false;
    
    ClientFactory clientFactory;
    @UiField FormElement usernameElement;
    @UiField FormElement userGroupElement;    
    @UiField(provided=true) SuggestBox userBox;
    @UiField HorizontalPanel userGroupPanel;
    SelectionDataWidget userGroupBox;
    UserData selectedUser;

    private static MeterMngUserPanelUiBinder uiBinder = GWT.create(MeterMngUserPanelUiBinder.class);
    
    private static Logger logger = Logger.getLogger(MeterMngUserPanel.class.getName());

    interface MeterMngUserPanelUiBinder extends UiBinder<Widget, MeterMngUserPanel> {
    }

    public MeterMngUserPanel(ClientFactory clientFactory, SimpleForm form) {
        super(form);
        this.clientFactory = clientFactory;
        createUserBox(clientFactory);
        initWidget(uiBinder.createAndBindUi(this));
        addFieldHandlers();
    }
    
    private void createUserBox(ClientFactory clientFactory) {
        this.userBox = new SuggestBox(new UserSuggestOracle(clientFactory));
        userBox.addSelectionHandler(new SelectionHandler<SuggestOracle.Suggestion>() {            
            @Override
            public void onSelection(SelectionEvent<Suggestion> event) {
                if (event.getSelectedItem() instanceof UserSuggestion) {
                    selectedUser = ((UserSuggestion) event.getSelectedItem()).getUser();
                }                
            }
        });
        userBox.getValueBox().addFocusHandler(new FocusHandler() {            
            @Override
            public void onFocus(FocusEvent event) {
                userBox.showSuggestionList();
            }
        });
    }
    
    public void setUserAccessGroupType(GroupType groupType) {
        for(int i=userGroupPanel.getWidgetCount()-1;i>=0;i--) {
            userGroupPanel.remove(i);
        }
        
        clientFactory.getGroupRpc().getGroupType(groupType.getId(), new ClientCallback<ArrayList<SelectionDataItem>>() {
            @Override
            public void onSuccess(ArrayList<SelectionDataItem> result) {
                if (result.size() > 0) {
                    displayAccessGroups(result);
                } else {
                    Dialogs.displayErrorMessage(UiMessagesUtil.getInstance().getNoAccessGroups(), MediaResourceUtil.getInstance().getErrorIcon());
                }
            }            
        });
    }
    
    private void displayAccessGroups(ArrayList<SelectionDataItem> result) {
        SelectionDataItem item = null;
        for(int i=0;i<result.size();i++) {                    
            item = result.get(i);            
            logger.info("Got access control group: "+item);
            userGroupBox = new SelectionDataWidget(clientFactory, clientFactory.getGroupRpc(), item, horizontalLayout, mustSelectLastlevel, null, autoSelect, form);                    
            userGroupPanel.add(userGroupBox);
        }
    }

    @Override
    public void addFieldHandlers() {
        userBox.addValueChangeHandler(new FormDataValueChangeHandler<String>(form));
    }

    @Override
    public void clearFields() {
        selectedUser = null;
        userBox.setText("");
        if (userGroupBox != null) {
            userGroupBox.setSelectedGroup(new ArrayList<Long>());
            userGroupBox.clear();
        }
    }

    @Override
    public void clearErrors() {
        usernameElement.clearErrorMsg();
        userGroupElement.clearErrorMsg();        
    }
}
