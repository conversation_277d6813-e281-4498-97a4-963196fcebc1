package za.co.ipay.metermng.client.event;

import com.google.gwt.event.shared.GwtEvent;

public class OpenAuxChargeStructureEvent extends GwtEvent<OpenAuxChargeStructureEventHandler> {

    public static Type<OpenAuxChargeStructureEventHandler> TYPE = new Type<OpenAuxChargeStructureEventHandler>();
    
    private String auxChargeStructureId;
    
    public OpenAuxChargeStructureEvent(String auxChargeStructureId) {
        this.auxChargeStructureId = auxChargeStructureId;
    }
    
    public String getAuxChargeStructureId() {
        return auxChargeStructureId;
    }
    
    @Override
    public Type<OpenAuxChargeStructureEventHandler> getAssociatedType() {
        return TYPE;
    }

    @Override
    protected void dispatch(OpenAuxChargeStructureEventHandler handler) {
        handler.openAuxChargeStructure(this);
    }
}
