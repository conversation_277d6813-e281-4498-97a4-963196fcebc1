package za.co.ipay.metermng.client.view.workspace;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.logging.Logger;

import com.google.gwt.core.client.GWT;
import com.google.gwt.event.dom.client.ClickEvent;
import com.google.gwt.event.dom.client.ClickHandler;
import com.google.gwt.event.logical.shared.SelectionEvent;
import com.google.gwt.event.logical.shared.SelectionHandler;
import com.google.gwt.i18n.client.DateTimeFormat;
import com.google.gwt.place.shared.Place;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.uibinder.client.UiHandler;
import com.google.gwt.user.client.Window;
import com.google.gwt.user.client.ui.Button;
import com.google.gwt.user.client.ui.FlowPanel;
import com.google.gwt.user.client.ui.HTML;
import com.google.gwt.user.client.ui.HorizontalPanel;
import com.google.gwt.user.client.ui.Image;
import com.google.gwt.user.client.ui.ProvidesResize;
import com.google.gwt.user.client.ui.RequiresResize;
import com.google.gwt.user.client.ui.TabPanel;
import com.google.gwt.user.client.ui.UIObject;
import com.google.gwt.user.client.ui.Widget;
import za.co.ipay.accesscontrol.domain.Group;
import za.co.ipay.gwt.common.client.Dialogs;
import za.co.ipay.gwt.common.client.dataexport.GetRequestBuilder;
import za.co.ipay.gwt.common.client.form.FormGroupPanel;
import za.co.ipay.gwt.common.client.form.Format.FormatUtil;
import za.co.ipay.gwt.common.client.form.PageHeader;
import za.co.ipay.gwt.common.client.resource.Messages;
import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.gwt.common.client.widgets.IpayListBox;
import za.co.ipay.gwt.common.client.widgets.ScrollableTabLayoutPanel;
import za.co.ipay.gwt.common.client.workspace.SessionCheckCallback;
import za.co.ipay.gwt.common.client.workspace.TabLayoutWorkspaceContainer;
import za.co.ipay.gwt.common.client.workspace.Workspace;
import za.co.ipay.gwt.common.client.workspace.WorkspaceNotification;
import za.co.ipay.gwt.common.client.workspace.WorkspaceNotification.NotificationType;
import za.co.ipay.gwt.common.shared.LookupListItem;
import za.co.ipay.metermng.client.event.EngineeringTokenIssuedEvent;
import za.co.ipay.metermng.client.event.EngineeringTokenIssuedEventHandler;
import za.co.ipay.metermng.client.event.LinkToEvent;
import za.co.ipay.metermng.client.event.OpenUsagePointEvent;
import za.co.ipay.metermng.client.event.UsagePointUpdatedEvent;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.history.AbstractUsagePointPlace;
import za.co.ipay.metermng.client.history.CustomerPlace;
import za.co.ipay.metermng.client.history.MeterPlace;
import za.co.ipay.metermng.client.history.UsagePointPlace;
import za.co.ipay.metermng.client.resource.image.MediaResource.MediaResourceUtil;
import za.co.ipay.metermng.client.rpc.callback.ClientCallback;
import za.co.ipay.metermng.client.util.MeterMngClientUtils;
import za.co.ipay.metermng.client.view.component.AssignMeterDialogueBox;
import za.co.ipay.metermng.client.view.component.CustomerComponent;
import za.co.ipay.metermng.client.view.component.InformationTabHeader;
import za.co.ipay.metermng.client.view.component.MeterComponent;
import za.co.ipay.metermng.client.view.component.RemoveMeterDialogueBox;
import za.co.ipay.metermng.client.view.component.UsagePointComponent;
import za.co.ipay.metermng.client.view.component.ViewUsagePointChargeDialogBox;
import za.co.ipay.metermng.client.view.component.customer.CustomerInformation;
import za.co.ipay.metermng.client.view.component.meter.ContainsMeterComponent;
import za.co.ipay.metermng.client.view.component.meter.MeterInformation;
import za.co.ipay.metermng.client.view.component.usagepoint.UsagePointInformation;
import za.co.ipay.metermng.datatypes.PaymentModeE;
import za.co.ipay.metermng.datatypes.RecordStatus;
import za.co.ipay.metermng.mybatis.generated.model.AppSetting;
import za.co.ipay.metermng.mybatis.generated.model.FormFields;
import za.co.ipay.metermng.mybatis.generated.model.StsMeter;
import za.co.ipay.metermng.mybatis.generated.model.UsagePoint;
import za.co.ipay.metermng.shared.MeterMngStatics;
import za.co.ipay.metermng.shared.TokenData;
import za.co.ipay.metermng.shared.appsettings.AppSettings;
import za.co.ipay.metermng.shared.dto.CustomerAgreementData;
import za.co.ipay.metermng.shared.dto.HistoryData;
import za.co.ipay.metermng.shared.dto.LocationData;
import za.co.ipay.metermng.shared.dto.MeterData;
import za.co.ipay.metermng.shared.dto.UpGenGroupLinkData;
import za.co.ipay.metermng.shared.dto.UsagePointData;
import za.co.ipay.metermng.shared.dto.uploaddata.metadata.GisMetadata;

public class UsagePointWorkspaceView extends BaseWorkspace implements ProvidesResize, RequiresResize, ContainsMeterComponent {

    public static final int SHOW_INFORMATION_METER = 0;
    public static final int SHOW_INFORMATION_CUSTOMER = 1;
    public static final int SHOW_INFORMATION_USAGEPOINT = 2;

    public static final String CUSTOMER_NEW = "customer:new";
    public static final String METER_NEW = "meter:new";
    public static final String USAGEPOINT_NEW = "usagepoint:new";

    private static Logger logger = Logger.getLogger("UsagePointWorkspaceView");

    private UsagePointData usagepointData = null;
    private AbstractUsagePointPlace place;

    public static final DateTimeFormat gwtDateFormat = DateTimeFormat.getFormat("yyyy-MM-dd HH:mm:ss");

    interface MeterViewUiBinder extends UiBinder<Widget, UsagePointWorkspaceView> {
    }

    private static MeterViewUiBinder uiBinder = GWT.create(MeterViewUiBinder.class);

    @UiField(provided=true) UsagePointComponent usagepointcomponent;
    @UiField(provided=true) CustomerComponent customercomponent;
    @UiField(provided=true) MeterComponent metercomponent;
    @UiField FlowPanel usagepointpanel;
    @UiField FlowPanel customerpanel;
    @UiField FlowPanel meterpanel;
    @UiField TabPanel informationTabs;
    @UiField HTML infoPanelHeading;
    @UiField Image reload;
    @UiField IpayListBox accessGroupLstbx;
    @UiField Button accessGroupUpdateButton;
    @UiField Button printContractButton;
    @UiField FormGroupPanel accessGroupUpdate;
    @UiField HorizontalPanel accessGroupInner;
    @UiField FlowPanel overViewPanel;
    @UiField PageHeader pageHeader;

    private MeterInformation meterInfo = null;
    private UsagePointInformation upInfo = null;
    private CustomerInformation customerInfo = null;

    private int meterinfotabinx;
    private int custtabinx;
    private int uptabinx;
    private Long accessGroupId;
    private boolean isAddNewMeterToExistingUsagePoint = false;
    private ClientCallback<Map<String, FormFields>> userInterfaceComponentSettingsUpdateCallback;
    private ArrayList<AppSetting> customFieldList;
    private ArrayList<LookupListItem> accessGroupList;
    private UsagePointWorkspaceFactory usagePointWorkspaceFactory;

    public UsagePointWorkspaceView(final ClientFactory clientFactory, final AbstractUsagePointPlace theplace,
                                   ArrayList<AppSetting> customFieldList) {
        this.place = theplace;
        this.clientFactory = clientFactory;
        this.customFieldList = customFieldList;

        if(place instanceof MeterPlace) {
            setPlaceString(METER_NEW);
            setHeaderText(MessagesUtil.getInstance().getMessage("meter.add.new"));
        } else if(place instanceof CustomerPlace) {
            setPlaceString(CUSTOMER_NEW);
            setHeaderText(MessagesUtil.getInstance().getMessage("customer.add.new"));
        } else if(place instanceof UsagePointPlace) {
            setPlaceString(USAGEPOINT_NEW);
            setHeaderText(MessagesUtil.getInstance().getMessage("usagepoint.add.new"));
        }

    	usagepointcomponent = new UsagePointComponent(clientFactory,this, customFieldList);
    	customercomponent = new CustomerComponent(clientFactory,this, customFieldList);
        metercomponent = new MeterComponent(clientFactory, this, customFieldList);

        userInterfaceComponentSettingsUpdateCallback = new ClientCallback<Map<String, FormFields>>() {
            @Override
            public void onSuccess(Map<String, FormFields> result) {
                usagepointcomponent.updateUserInterfaceComponentSettings(result);
                customercomponent.updateUserInterfaceComponentSettings(result);
                metercomponent.updateUserInterfaceComponentSettings(result);
            }
        };
        updateUserInterfaceComponentSettings();

    	initWidget(uiBinder.createAndBindUi(this));

    	reload.setResource(MediaResourceUtil.getInstance().getRefreshImage());

    	generateInformationTabs();
        informationTabs.addSelectionHandler(new SelectionHandler<Integer>() {
            @Override
            public void onSelection(SelectionEvent<Integer> event) {
                showInformation(event.getSelectedItem().intValue());
            }
        });

        clientFactory.getEventBus().addHandler(EngineeringTokenIssuedEvent.TYPE, new EngineeringTokenIssuedEventHandler() {
            @Override
            public void handleEngineeringTokenIssuedEvent(EngineeringTokenIssuedEvent event) {
                int eventType = event.getEngineeringTokenType();
                if ((eventType == TokenData.TOKEN_TYPE_KEY_CHANGE
                        || eventType == TokenData.TOKEN_TYPE_POWER_LIMIT)
                        && event.getMeter().getId().equals(usagepointData.getMeterId())) {
                    SessionCheckCallback sessionCheckCallback = new SessionCheckCallback() {
                        @Override
                        public void callback(SessionCheckResolution resolution) {
                            clientFactory.getMeterRpc().getMeter(usagepointData.getMeterId(), new ClientCallback<MeterData>() {
                                @Override
                                public void onSuccess(MeterData result) {
                                    usagepointData.setMeterData(result);
                                    customercomponent.setUsagePointData(usagepointData);
                                    metercomponent.setMeterData(usagepointData.getMeterData());
                                    usagepointcomponent.setUsagePointData(usagepointData);
                                    setHistoryIds(usagepointData);
                                }
                            });
                        }
                    };
                    clientFactory.handleSessionCheckCallback(sessionCheckCallback);
                }
            }
        });
    }

    private void setHistoryIds(final UsagePointData usagePointData) {
        clientFactory.getSearchRpc().setHistoryIds(usagePointData, new ClientCallback<HistoryData>() {
            @Override
            public void onSuccess(HistoryData result) {
                usagePointData.setHistoryData(result);
            }
        });
    }

    private void generateInformationTabs() {
        InformationTabHeader tabH;
        int count=0;
        if (clientFactory.getUser().hasPermission("mm_meter_info")) {
            meterInfo = new MeterInformation(clientFactory, this);
            tabH = new InformationTabHeader(MediaResourceUtil.getInstance().getDashboardSmallImage().getSafeUri().asString(),
                                            MessagesUtil.getInstance().getMessage("meter.info.title"));
            tabH.ensureDebugId("meterInformationTabHeader");
            informationTabs.add(meterInfo, tabH);
            meterinfotabinx = count;
            meterInfo.setVisible(false);
            informationTabs.getTabBar().getTab(meterinfotabinx).setWordWrap(false);
            informationTabs.getTabBar().setTabEnabled(meterinfotabinx, false);
            count++;
        }

        if (clientFactory.getUser().hasPermission("mm_customer_info")) {
            customerInfo = new CustomerInformation(this, clientFactory, customFieldList);
            tabH = new InformationTabHeader(MediaResourceUtil.getInstance().getUserSmallIcon().getSafeUri().asString(),
                                            MessagesUtil.getInstance().getMessage("customer.info.title"));
            tabH.ensureDebugId("customerInformationTabHeader");
            informationTabs.add(customerInfo, tabH);
            custtabinx = count;
            customerInfo.setVisible(false);
            informationTabs.getTabBar().getTab(custtabinx).setWordWrap(false);
            ((UIObject) informationTabs.getTabBar().getTab(custtabinx)).ensureDebugId("customerInformationTab");
            informationTabs.getTabBar().setTabEnabled(custtabinx, false);
            count++;
        }

        if (clientFactory.getUser().hasPermission("mm_usage_point_info")) {
            upInfo = new UsagePointInformation(clientFactory, customFieldList, this);
            tabH = new InformationTabHeader(MediaResourceUtil.getInstance().getHomeSmallImage().getSafeUri().asString(),
                                            MessagesUtil.getInstance().getMessage("usagepoint.info.title"));
            tabH.ensureDebugId("usagePointInformationTabHeader");
            informationTabs.add(upInfo, tabH);
            uptabinx = count;
            upInfo.setVisible(false);
            informationTabs.getTabBar().getTab(uptabinx).setWordWrap(false);
            informationTabs.getTabBar().setTabEnabled(uptabinx, false);
        }

        if (upInfo == null && customerInfo == null && meterInfo == null) {
            infoPanelHeading.setVisible(false);
        }
    }

    private void updateUserInterfaceComponentSettings() {
        clientFactory.getUserInterfaceRpcAsync().getFormFields(userInterfaceComponentSettingsUpdateCallback);
    }

    public UsagePointComponent getUsagePointComponent() {
        return usagepointcomponent;
    }

    public MeterComponent getMeterComponent() {
        return metercomponent;
    }

    public CustomerComponent getCustomerComponent() {
        return customercomponent;
    }

    @UiHandler("reload")
    void handleReload(ClickEvent e) {
        reload();
    }

    /**
     * Used when there is a usagepoint.
     * Meter + Customer do not have a direct link.
     */
    public void reload() {
    	if (usagepointData != null && usagepointData.getId() != null) {
    	    reloadUI();
        }
    }

    /**
     * Used to reload the UI.
     * Meter + Customer do not have a direct link.
     * So on reload you might lose one of this. Use reload() instead.
     */
    public void reloadUI() {
        // remove information tabs & recreate so that when open them again, information is fresh
        informationTabs.clear();
        generateInformationTabs();
        if (this.place instanceof MeterPlace && usagepointData.getMeterData() != null) {
            clientFactory.getSearchRpc().getUsagePointDataByMeterNum(usagepointData.getMeterData().getMeterNum(), new ClientCallback<UsagePointData>() {
                @Override
                public void onSuccess(UsagePointData result) {
                    setUsagePointData(result);
                }

            });
        } else if (this.place instanceof CustomerPlace && usagepointData.getCustomerAgreementData() != null) {
            if(clientFactory.isEnableMultiUp() && usagepointData.getName()!=null) {
                // For multi UP, the customer can have more than one UP, and by default the first is selected.
                clientFactory.getSearchRpc().getUsagePointDataByUsagePointName(usagepointData.getName(), new ClientCallback<UsagePointData>() {
                    @Override
                    public void onSuccess(UsagePointData result) {
                        setUsagePointData(result);
                    }
                });
            } else {
                clientFactory.getSearchRpc().getUsagePointByAgreementRef(usagepointData.getCustomerAgreementData().getAgreementRef(), new ClientCallback<UsagePointData>() {
                    @Override
                    public void onSuccess(UsagePointData result) {
                        setUsagePointData(result);
                    }
                });
            }
        } else if (this.place instanceof UsagePointPlace && usagepointData.getId() != null) {
            clientFactory.getSearchRpc().getUsagePointDataByUsagePointName(usagepointData.getName(), new ClientCallback<UsagePointData>() {
                @Override
                public void onSuccess(UsagePointData result) {
                    setUsagePointData(result);
                }
            });
        }
    }

    public void hideCustomerAccountPanel(boolean isHidePanel) {
        customercomponent.hideCustomerAccountPanel(isHidePanel);
    }

    public void populate() {
        if (usagepointData != null) {
            if (usagepointData.getId() == null) {
                UsagePoint newusagepoint = new UsagePoint();
                if (usagepointData.getMeterData() != null) {
                    newusagepoint.setMeterId(usagepointData.getMeterData().getId());
                }

                if (usagepointData.getCustomerAgreementData() != null) {
                    newusagepoint.setCustomerAgreementId(usagepointData.getCustomerAgreementData().getId());
                }
                usagepointData.setUsagePoint(newusagepoint);
                if (upInfo != null) {
                    upInfo.setVisible(false);
                    informationTabs.getTabBar().setTabEnabled(uptabinx, false);
                }
            } else {
                if (place instanceof UsagePointPlace) {
                    setPlaceString("usagepoint:" + usagepointData.getName());
                    setHeaderText(usagepointData.getName());
                }
                if (upInfo != null) {
                    upInfo.setVisible(true);
                    informationTabs.getTabBar().setTabEnabled(uptabinx, true);
                }
            }

            customercomponent.setUsagePointData(usagepointData);
            metercomponent.setMeterData(usagepointData.getMeterData());
            usagepointcomponent.setUsagePointData(usagepointData);

            if (usagepointData.getServiceLocation() != null) {
                usagepointcomponent.setServiceLocation(usagepointData.getServiceLocation());
            }

            if (usagepointData.getMeterData() != null) {
                if (place instanceof MeterPlace) {
                    setPlaceString("meter:" + usagepointData.getMeterData().getMeterNum());
                    setHeaderText(usagepointData.getMeterData().getMeterNum());
                }
                if (meterInfo != null) {
                    meterInfo.setVisible(true);
                    informationTabs.getTabBar().setTabEnabled(meterinfotabinx, true);
                }
            } else {
                if (meterInfo != null) {
                    meterInfo.setVisible(false);
                    informationTabs.getTabBar().setTabEnabled(meterinfotabinx, false);
                }
            }

            if (usagepointData.getCustomerAgreementData() != null) {
                if (place instanceof CustomerPlace) {
                    setPlaceString(CustomerPlace.createPlaceString((CustomerPlace) place,
                        String.valueOf(usagepointData.getCustomerAgreementData().getCustomerData().getId())));
                    setHeaderText(usagepointData.getCustomerAgreementData().getCustomerData().getName());
                }
                if (usagepointData.getMeterData() == null && usagepointData.getId() == null) {
                    setHeaderText(usagepointData.getCustomerAgreementData().getCustomerData().getName());
                }
                if (meterInfo != null) {
                    meterInfo.setFreeIssueAuxAccountId(usagepointData.getCustomerAgreementData().getFreeIssueAuxAccId());
                    meterInfo.setCustomerAgreementId(usagepointData.getCustomerAgreementData().getId());
                }
                if (customerInfo != null) {
                    customerInfo.setVisible(true);
                    informationTabs.getTabBar().setTabEnabled(custtabinx, true);
                }
            } else {
                if (customerInfo != null) {
                    customerInfo.setVisible(false);
                    informationTabs.getTabBar().setTabEnabled(custtabinx, false);
                }
            }
            printContractButton.setVisible(usagepointData.getId() != null && usagepointData.getCustomerAgreementData() != null && usagepointData.getMeterData() != null);
        } else {
            usagepointcomponent.setUsagePointData(null);
            metercomponent.setMeterData(null);
        }
        if (!clientFactory.isEnableAccessGroups()) {
            overViewPanel.removeFromParent();
            pageHeader.removeFromParent();
        } else {
            accessGroupUpdate.setVisible(true);
            processOverviewData();
        }
    }

    private void processOverviewData() {
        if (usagepointData != null) {
            boolean isCustOrUp = usagepointData.getId() != null || usagepointData.getCustomerAgreementData() != null;
            if (clientFactory.isEnableAccessGroups()) {
                accessGroupId = usagepointData.getAccessGroupId();
                if (accessGroupId == null && usagepointData.getCustomerAgreementData() != null) {
                    accessGroupId = usagepointData.getCustomerAgreementData().getCustomerData().getAccessGroupId();
                }
                if (isCustOrUp) {
                    accessGroupUpdate.setVisible(true);
                    accessGroupInner.setVisible(true);
                    buildAccessGroupData();
                }
            }
        } else if (clientFactory.isEnableAccessGroups()) {
            accessGroupUpdate.setVisible(true);
            accessGroupInner.setVisible(false);
        }
    }

    private void buildAccessGroupData() {
        if (clientFactory.isEnableAccessGroups() && clientFactory.getUser().getOrganisation() != null) {
            clientFactory.getUserRpc().getAccessGroupsByOrgId(clientFactory.getUser().getOrganisation(), new ClientCallback<List<Group>>() {
                @Override
                public void onSuccess(List<Group> result) {
                    accessGroupLstbx.clear();
                    accessGroupList = new ArrayList<LookupListItem>();
                    if (clientFactory.getUser().hasPermission("mm_update_group")) {
                        accessGroupLstbx.setEnabled(true);
                        accessGroupUpdateButton.setEnabled(true);
                        /*
                         *  mm_update_other_group Only Global can use this permission due to RLS.
                         */
                        if (clientFactory.isGroupGlobalUser() && clientFactory.getUser().hasPermission("mm_update_to_global") &&
                                clientFactory.getUser().hasPermission("mm_update_other_group")) {
                            for (Group group : result) {
                                accessGroupList.add(new LookupListItem(group.getId().toString(), group.getName()));
                            }
                            accessGroupLstbx.setLookupItemsWithEmptyFirst(accessGroupList);
                        } else if (clientFactory.isGroupGlobalUser() && clientFactory.getUser().hasPermission("mm_update_other_group")) {
                            for (Group group : result) {
                                accessGroupList.add(new LookupListItem(group.getId().toString(), group.getName()));
                            }
                            accessGroupLstbx.setLookupItems(accessGroupList);
                        } else if (clientFactory.getUser().hasPermission("mm_update_to_global")) {
                            for (Group group : result) {
                                if ((clientFactory.getUser().getSessionGroupId() != null && group.getId().equals(clientFactory.getUser().getSessionGroupId()))
                                        || group.getId().equals(accessGroupId)) {
                                    accessGroupList.add(new LookupListItem(group.getId().toString(), group.getName()));
                                    break;
                                }
                            }
                            accessGroupLstbx.setLookupItemsWithEmptyFirst(accessGroupList);
                        }
                    } else {
                        for (Group group : result) {
                            if (group.getId().equals(accessGroupId)) {
                                accessGroupList.add(new LookupListItem(group.getId().toString(), group.getName()));
                                break;
                            }
                        }
                        accessGroupLstbx.setLookupItems(accessGroupList);
                        accessGroupUpdateButton.setEnabled(false);
                    }
                    if (accessGroupId != null) {
                        accessGroupLstbx.selectItemByValue(accessGroupId.toString());
                    }
                }
            });
        }
    }

    public List<GisMetadata> loadGisMetadata(UsagePointData usagepointData) {
        List<UpGenGroupLinkData> data = usagepointData.getSelectedGroups();
        List<GisMetadata> gisMetadata = new ArrayList<>();
        if(data != null) {
            for (UpGenGroupLinkData d : data) {
                logger.info("Selected Group: " + d.getDepthList() + ":" + d.getGenGroupId() + ":" + d.getMetadata());
                for (Map.Entry<String, String> entry : d.getMetadata().entrySet()) {
                    GisMetadata gisInfo = GisMetadata.parse(entry.getValue());
                    if (gisInfo != null) {
                        gisInfo.setDesc(entry.getKey());
                        gisMetadata.add(gisInfo);
                    }
                }
            }
        }
        return gisMetadata;
    }

    public void setPlace(AbstractUsagePointPlace place) {
        this.place = place;
    }

    public AbstractUsagePointPlace getPlace() {
        return place;
    }

    public void setUsagePointData(UsagePointData usagePointData) {
        this.usagepointData = usagePointData;
        if (usagePointData == null) {
            logger.info("UP is null");
            if(place instanceof MeterPlace) {
                setHeaderText(MessagesUtil.getInstance().getMessage("meter.add.new"));
                setPlaceString(METER_NEW);
            } else if(place instanceof CustomerPlace) {
                setHeaderText(MessagesUtil.getInstance().getMessage("customer.add.new"));
                setPlaceString(CUSTOMER_NEW);
            } else if(place instanceof UsagePointPlace) {
                setHeaderText(MessagesUtil.getInstance().getMessage("usagepoint.add.new"));
                setPlaceString(USAGEPOINT_NEW);
            }
        } else {
            checkDuplicateDataOnOtherTabs();
        }
        usagepointcomponent.setOpen(place instanceof UsagePointPlace);
    	metercomponent.setOpen(place instanceof MeterPlace);
    	metercomponent.initDialogs();
    	customercomponent.setOpen(place instanceof CustomerPlace);
    	populate();
    }

    public UsagePointData getUsagePointData() {
        return usagepointData;
    }

    public MeterData getMeterData() {
        if (usagepointData != null) {
            return usagepointData.getMeterData();
        }
        return null;
    }

    public StsMeter getStsMeter() {
        if (getMeterData() != null) {
            return getMeterData().getStsMeter();
        }
        return null;
    }

    public CustomerAgreementData getCustomerAgreementData() {
        if (usagepointData != null) {
            return usagepointData.getCustomerAgreementData();
        }
        return null;
    }

	@Override
	public void onResize() {
		Iterator<Widget> widgets = usagepointpanel.iterator();
		Widget w;
		while (widgets.hasNext()){
			w = widgets.next();
			if (w instanceof RequiresResize) {
				((RequiresResize) w).onResize();
			}
		}
		widgets = customerpanel.iterator();
		while (widgets.hasNext()){
			w = widgets.next();
			if (w instanceof RequiresResize) {
				((RequiresResize) w).onResize();
			}
		}
		widgets = meterpanel.iterator();
		while (widgets.hasNext()){
			w = widgets.next();
			if (w instanceof RequiresResize) {
				((RequiresResize) w).onResize();
			}
		}

		if (upInfo != null && upInfo.getUsagePointTransactions() != null) {
		    upInfo.getUsagePointTransactions().resizeTabPanel();
		}
		if (meterInfo != null && meterInfo.getMeterTransactions() != null) {
		    meterInfo.getMeterTransactions().resizeTabPanel();
		}
		if (customerInfo != null && customerInfo.getCustomerAccTransactions() != null) {
		    customerInfo.getCustomerAccTransactions().resizeTabPanel();
		}

	}

	@Override
	public void onLeaving() {
		logger.info("Place: "+getPlaceString() + " is leaving you, sorry");
	}

    @Override
    public void onSelect() {
        logger.info("onSelect(), " + getPlaceString());
    }

	@Override
	public void onArrival(Place place) {
		logger.info("Hello from UsagePointWorkspace! It's me, place: " + getPlaceString());
		if (place.equals(CustomerPlace.NEW_CUSTOMER_PLACE)) {
			usagepointcomponent.setOpen(false);
			metercomponent.setOpen(false);
			customercomponent.setOpen(true);
		} else if (place.equals(MeterPlace.NEW_METER_PLACE)) {
			usagepointcomponent.setOpen(false);
			metercomponent.setOpen(true);
			customercomponent.setOpen(false);
		} else if (place.equals(UsagePointPlace.NEW_USAGEPOINT_PLACE)) {
            usagepointcomponent.setOpen(true);
            metercomponent.setOpen(false);
            customercomponent.setOpen(false);
        }
	}

	public void showInformation(int tabIndex) {

        switch (tabIndex) {
            case SHOW_INFORMATION_CUSTOMER:
                if (customerInfo != null) {
                    if (usagepointData != null && usagepointData.getCustomerAgreementData() != null) {
                        customerInfo.setCustomerAgreementInfo(usagepointData.getCustomerAgreementData());
                    }
                }
                break;
            case SHOW_INFORMATION_USAGEPOINT:
                if (upInfo != null) {
                    if (usagepointData != null && usagepointData.getId() != null) {
                        upInfo.setUsagePointInfo(usagepointData);
                        LocationData locationData = usagepointData.getServiceLocation();
                        locationData = locationData != null ? locationData : usagepointData.getUsagepointLocation();
                        upInfo.upInfoRechargeSetMapCoords(locationData);
                        upInfo.upInfoRechargeSetGisMetadata(loadGisMetadata(usagepointData));
                        upInfo.reloadMap();
                    }
			    }
				break;
			default:
                if (meterInfo != null) {
                    if (usagepointData != null) {
                        if (usagepointData.getMeterData() != null) {
                            meterInfo.setMeterInfo(usagepointData.getMeterData(), usagepointData.getId());
                            meterInfo.setUsagePointActive(usagepointData.getRecordStatus() != null && usagepointData.getRecordStatus().equals(RecordStatus.ACT));
                        }
                        if (usagepointData.getCustomerAgreementData() != null) {
                            meterInfo.setFreeIssueAuxAccountId(usagepointData.getCustomerAgreementData().getFreeIssueAuxAccId());
                            meterInfo.setCustomerAgreementId(usagepointData.getCustomerAgreementData().getId());
                        }
                        LocationData locationData = usagepointData.getServiceLocation();
                        locationData = locationData != null ? locationData : usagepointData.getUsagepointLocation();
                        meterInfo.meterInfoRechargeSetMapCoords(locationData);
                        meterInfo.meterInfoRechargeSetGisMetadata(loadGisMetadata(usagepointData));
                        meterInfo.reloadMap();
                    }
                }
		}
	}

    @Override
    public void onClose() {
    }

    @Override
    public boolean handles(Place place) {
        logger.info("handles test place=" + place);
        logger.info("this.place=" + this.place);
        if(place.equals(this.place)) {
            logger.info("place equals");
            // This would be the case when both are CustomerPlace or both are MeterPlace
            // It also handles the "new" case
            return true;
        }

        if(place instanceof CustomerPlace) {
            CustomerPlace customerPlace = (CustomerPlace) place;
            if (customerPlace.isNew()) {
            	return false;
            }
            if (!clientFactory.isEnableMultiUp()) {
                if (customerPlace.getSearchType() == CustomerPlace.SEARCH_BY_ID) {
                    Long customerId = Long.parseLong(customerPlace.getCustomerId());
                    if (usagepointData != null && usagepointData.getCustomerAgreementData() != null
                        && usagepointData.getCustomerAgreementData().getCustomerId() != null
                        && customerId.equals(usagepointData.getCustomerAgreementData().getCustomerId())) {
                        return true;
                    }
                }
                if (customerPlace.getSearchType() == CustomerPlace.SEARCH_BY_AGREEMENT_REF) {
                    if (usagepointData != null && usagepointData.getCustomerAgreementData() != null
                        && usagepointData.getCustomerAgreementData().getAgreementRef() != null
                        && customerPlace.getCustomerId().equals(usagepointData.getCustomerAgreementData().getAgreementRef())) {
                        return true;
                    }
                }
                if (customerPlace.getSearchType() == CustomerPlace.SEARCH_BY_ACCOUNT_NAME) {
                    if (usagepointData != null && usagepointData.getCustomerAgreementData() != null
                        && usagepointData.getCustomerAgreementData().getAgreementRef() != null
                        && customerPlace.getCustomerId().equals(usagepointData.getCustomerAgreementData().getCustomerAccount().getAccountName())) {
                        return true;
                    }
                }
            }
        } else if(place instanceof MeterPlace && !isAddNewMeterToExistingUsagePoint) {
            MeterPlace meterPlace = (MeterPlace) place;
            // meterNum as "new" is handled earlier implicitly
            if(usagepointData != null && usagepointData.getMeterData() != null && meterPlace.getMeterNumber().equals(usagepointData.getMeterData().getMeterNum())) {
                return true;
            }
        } else if(place instanceof UsagePointPlace) {
            UsagePointPlace usagePointPlace = (UsagePointPlace) place;
            if (usagepointData != null && usagepointData.getId() != null && usagepointData.getName().equals(usagePointPlace.getUsagePointName())) {
                return true;
            }
        }
        return false;
    }

    @Override
    public Widget asWidget() {
        return this;
    }

    @Override
    public void handleNotification(final WorkspaceNotification notification) {
        logger.info("Received notification: " + notification);
        final String dataType = notification.getDataType();
        if (NotificationType.DATA_UPDATED == notification.getNotificationType()) {
            SessionCheckCallback sessionCheckCallback = new SessionCheckCallback() {
                @Override
                public void callback(SessionCheckResolution resolution) {
                    if (MeterMngStatics.USER_CURRENT_GROUP.equals(dataType)) {
                        logger.info("The user's current group has changed - closing this usage point workspace...");
                        clientFactory.getWorkspaceContainer().closeWorkspaceNow(UsagePointWorkspaceView.this);
                    } else if (MeterMngStatics.ONLINE_BULK_METER_ADDED.equals(dataType)
                               || MeterMngStatics.ONLINE_BULK_MODIFIED.equals(dataType)) {
                        String meterNum = (String) notification.getObject();
                        if (usagepointData != null && usagepointData.getMeterData() != null
                            && usagepointData.getMeterData().getMeterNum().equals(meterNum)) {
                            reload();
                        }
                    } else if (dataType.equals(MeterMngStatics.RELOAD_USAGE_POINT_WORKSPACEVIEW)) {
                        reload();
                    } else if (dataType.equals(MeterMngStatics.METER_MODEL_MODIFIED)) {
                        // Similar notification in DeviceStoreWorkspaceView. Changes should potentially be made to both.
                        metercomponent.populateMeterModelListBox();
                        if (usagepointData != null) {
                            meterInfo.setMeterInfo(usagepointData.getMeterData(), usagepointData.getId());
                        }
                        meterInfo.populateMdcTrans();
                    } else if (MeterMngStatics.CUSTOMER_ACCOUNT_BALANCE_ADJUSTED.equals(dataType)) {
                        BigDecimal customerBalance = (BigDecimal) notification.getObject();
                        // after input an account balance adjustment simply refresh the account balance
                        // - not reload entire page!!
                        customercomponent.refreshAccountBalance(customerBalance);
                        usagepointData.getCustomerAgreementData().getCustomerAccount().setAccountBalance(customerBalance);
                        usagepointcomponent.refreshCustomerAccountBalanceInUsagePoint(customerBalance);
                    } else if (MeterMngStatics.APPSETTINGS_MODIFIED.equals(dataType)) {
                        String appSettingKey = ((String) notification.getObject()).toLowerCase();
                        if (appSettingKey.contains(".custom_")) {
                            usagepointcomponent.getUserCustomFieldsComponent().refreshCustomAppSettings();
                            upInfo.refreshUsagePointHistoryViewAppSettings();
                            customercomponent.getUserCustomFieldsComponent().refreshCustomAppSettings();
                            customerInfo.refreshCustomerHistoryAppSettings();
                            metercomponent.getUserCustomFieldsComponent().refreshCustomAppSettings();
                            meterInfo.refreshMeterHistoryAppSettings();
                        } else if (appSettingKey.equals(AppSettings.USAGEPOINT_DEVICE_MOVEMENT_REFERENCE)) {
                            metercomponent.initDialogs();
                            upInfo.refreshUsagePointHistoryViewDeviceMoveRef(true);
                        } else if (appSettingKey.contains("engineering.token.issue.user.reference.status")) {
                            meterInfo.updateEngineeringTokenUserRefStatus();
                        } else if (appSettingKey.equals(AppSettings.LOCATION_REQUIRED_TO_ALL_LEVELS)) {
                            usagepointcomponent.getLocationComponent().remapLocation();
                            customercomponent.getLocationComponent().remapLocation();
                        } else if (appSettingKey.contains("transaction.history.table.totals.enabled")) {
                            meterInfo.getMeterTransactions().getTablePanel().toggleTotalsColumnsByAppSetting();
                            upInfo.getUsagePointTransactions().getTablePanel().toggleTotalsColumnsByAppSetting();
                        } else if (appSettingKey.equals(AppSettings.POWER_LIMIT_SETTINGS)) {
                            metercomponent.updatePowerLimits();
                            meterInfo.refreshPowerLimitAppSettings();
                        } else if (appSettingKey.equals(AppSettings.UP_ALLOW_FUTURE_INSTALLATION_DATES)) {
                            usagepointcomponent.setAppSettingValues();
                        } else if (appSettingKey.equals(AppSettings.AUTO_POPULATE_AGREEMENT_REF)) {
                            customercomponent.setAutoPopulateAgreementRef();
                            customercomponent.mapDataToForm();
                        } else if (appSettingKey.equals(AppSettings.AUTO_POPULATE_ACCOUNT_NAME)) {
                            customercomponent.setAutoPopulateAccountName();
                            customercomponent.mapDataToForm();
                        }
                    } else if (MeterMngStatics.AUX_ACCOUNT_BALANCE_ADJUSTED.equals(dataType)) {
                        customerInfo.auxAccountViewRefreshTransactionTable();
                        customerInfo.auxAccountHistoryViewRefreshTransactionTable();
                    } else if (MeterMngStatics.AUX_ACCOUNT_UPLOAD.equals(dataType)) {
                        customercomponent.populateAuxAccountLookupListBox();
                        customerInfo.auxAccountViewRefreshTransactionTable();
                        customerInfo.auxAccountHistoryViewRefreshTransactionTable();
                    } else if (MeterMngStatics.AUX_ACCOUNT_HISTORY_UPDATE.equals(dataType)) {
                        customercomponent.populateAuxAccountLookupListBox();
                        customerInfo.auxAccountHistoryViewRefreshTransactionTable();
                    } else if (MeterMngStatics.CUST_TRANSACTION_UPLOAD.equals(dataType)) {
                        customerInfo.refreshCustomerAccountTransactionViewTable();
                        meterInfo.populateMdcTrans();
                        upInfo.populateMdcTrans();
                    } else if (MeterMngStatics.GROUP_TYPE.equals(dataType)) {
                        usagepointcomponent.reloadUsagePointGroupTypes();
                    } else if (MeterMngStatics.MAP_READY.equals(dataType)) {
                        if (usagepointData != null) {
                            LocationData locationData = usagepointData.getServiceLocation();
                            locationData = locationData != null ? locationData : usagepointData.getUsagepointLocation();
                            List<GisMetadata> gisMetadata = loadGisMetadata(usagepointData);
                            if (meterInfo != null) {
                                meterInfo.meterInfoRechargeSetMapCoords(locationData);
                                meterInfo.meterInfoRechargeSetGisMetadata(gisMetadata);
                            }
                            if (upInfo != null) {
                                upInfo.upInfoRechargeSetMapCoords(locationData);
                                upInfo.upInfoRechargeSetGisMetadata(gisMetadata);
                            }
                        }
                    } else if (MeterMngStatics.BLOCKINGTYPES_MODIFIED.equals(dataType)) {
                        usagepointcomponent.populateBlockingTypeLookupListBox();
                    } else if (MeterMngStatics.USER_INTERFACE_CONFIGURATION_MODIFIED.equals(dataType)) {
                        updateUserInterfaceComponentSettings();
                    } else if (MeterMngStatics.UNITS_ACCOUNT_BALANCE_ADJUSTED.equals(dataType)) {
                        upInfo.refreshUsagePointUnitsAccountTransactionViewTable();
                        usagepointcomponent.updateUnitsBalance((BigDecimal) notification.getObject());
                    }
                }
            };
            clientFactory.handleSessionCheckCallback(sessionCheckCallback);
        }
    }

    @Override
    public void onSaveMeter(MeterData meterData, Integer buttonTop, Integer buttonLeft) {
        if (usagepointData == null) {
            usagepointData = new UsagePointData();
        }
        usagepointData.setMeterData(meterData);
        UsagePointUpdatedEvent usagePointUpdatedEvent = new UsagePointUpdatedEvent(this, usagepointData, UsagePointUpdatedEvent.SAVE_METER,  buttonTop, buttonLeft);
        //for unassigned Usage point installation date is still null, but meterId is already set by Metercomponent handleSave()...setMeterData()
        if (usagepointData != null && usagepointData.getId() != null && usagepointData.getInstallationDate() == null) {
            setAddNewMeterToExistingUsagePoint(true);
            usagePointUpdatedEvent.setAddNewMeterToExistingUsagePoint(true);
        }
        clientFactory.getEventBus().fireEvent(usagePointUpdatedEvent);
    }

    public void setAddNewMeterToExistingUsagePoint(boolean isAddNewMeterToExistingUsagePoint) {
        //this switch is because when adding a new meter to an unattached UsagePOint, the check for Handles(place) which will close the meterplace if its open somewhere seperately, finds the meternum already on this page because in the process of adding...
        //It is unset after that check in UsagePointWorkspaceFactory.assignMeterToUsagePoint(..) & also by the cancel button on the AssignMeterDialogueBox
        logger.info("UsagePOintWorkspaceView: setting isAddNewMeterToExistingUsagePoint to " + isAddNewMeterToExistingUsagePoint);
        this.isAddNewMeterToExistingUsagePoint = isAddNewMeterToExistingUsagePoint;
    }

    @Override
    public void onCancelMeter() {
        if (usagepointData == null) {
            usagepointData = new UsagePointData();
        }
        usagepointData.cancelChanges();
    }

    @Override
    public void onAssignMeter(AssignMeterDialogueBox assignMeterDialogueBox) {
        if (usagepointData == null) {
            usagepointData = new UsagePointData();
        }
        assignMeterDialogueBox.setUsagePointData(usagepointData);
        assignMeterDialogueBox.hide();
    }

    @Override
    public void onRemoveMeter(RemoveMeterDialogueBox removeMeterDialogueBox) {
        if (usagepointData == null) {
            usagepointData = new UsagePointData();
        }
        removeMeterDialogueBox.setUsagePointData(usagepointData);
        removeMeterDialogueBox.hide();
    }

    @Override
    public UsagePointData getUsagePoint() {
        return usagepointData;
    }


    @Override
    public void setMeter(MeterData meterData) {
        usagepointData.setMeterData(meterData);
    }

    public void openEngineeringTokenHistory() {
        if (meterInfo != null) {
            meterInfo.openEngineeringTokenHistory();
        } else {
            Dialogs.displayErrorMessage(MessagesUtil.getInstance().getMessage("error.accessdenied"),
                    MediaResourceUtil.getInstance().getLockedIcon(),
                    MessagesUtil.getInstance().getMessage("button.close"));
        }
    }

    public void openTransactionHistory(int paramType) {
        switch (paramType) {
        case LinkToEvent.METER_NUM:
            if (meterInfo != null) {
                meterInfo.openTransactionHistory();
            } else {
                Dialogs.displayErrorMessage(MessagesUtil.getInstance().getMessage("error.accessdenied"),
                        MediaResourceUtil.getInstance().getLockedIcon(),
                        MessagesUtil.getInstance().getMessage("button.close"));
            }
            break;
        case LinkToEvent.USAGE_POINT_NAME:
            if (upInfo != null) {
                upInfo.openTransactionHistory();
            } else {
                Dialogs.displayErrorMessage(MessagesUtil.getInstance().getMessage("error.accessdenied"),
                        MediaResourceUtil.getInstance().getLockedIcon(),
                        MessagesUtil.getInstance().getMessage("button.close"));
            }
            break;
        default:
            break;
        }
    }

    public void openRemoveMeterDialogue() {
        metercomponent.showRemoveMeterPopup();
    }

	public MeterInformation getMeterInfo() {
		return meterInfo;
	}

	public void setMeterInfo(MeterInformation meterInfo) {
		this.meterInfo = meterInfo;
	}

	public UsagePointInformation getUpInfo() {
		return upInfo;
	}

	public void setUpInfo(UsagePointInformation upInfo) {
		this.upInfo = upInfo;
	}

    @UiHandler("printContractButton")
    void handlePrintContract(ClickEvent e) {
        if (usagepointData != null && usagepointData.getId() != null && usagepointData.getMeterData() != null && usagepointData.getCustomerAgreementData() != null) {
            String logoUrl = clientFactory.getLogoUrl();
            if (logoUrl==null || logoUrl.isEmpty()) {
                logoUrl = MediaResourceUtil.getInstance().getIPayLogo().getSafeUri().asString();
            }
            String encodedUrl = new GetRequestBuilder().withBaseUrl(GWT.getHostPageBaseURL())
                    .withTargetUrl("customercontractpdf")
                    .addParam("usagepoint", usagepointData.getName())
                    .addParam("logo", logoUrl)
                    .addParam("locale", MessagesUtil.getInstance().getLocaleName())
                    .toEncodedUrl();

            if (encodedUrl != null) {
                Window.Location.assign(encodedUrl);
            }
        } else {
            Dialogs.displayErrorMessage(MessagesUtil.getInstance().getMessage("export.error.nodata"),
                    MediaResourceUtil.getInstance().getErrorIcon());
        }
    }

    @UiHandler("accessGroupUpdateButton")
    void handleClearOrgGroup(ClickEvent e) {
        UsagePointData data = getUsagePointData();

        Long accessGroupIdDb = data.getAccessGroupId();
        if (accessGroupIdDb == null && data.getCustomerAgreementData() != null) {
            accessGroupIdDb = data.getCustomerAgreementData().getCustomerData().getAccessGroupId();
        }
        Long accessGroupIdSlct = Long.parseLong(accessGroupLstbx.getValue(accessGroupLstbx.getSelectedIndex()));
        final Long accessGroupId = accessGroupIdSlct.longValue() > 0l ? accessGroupIdSlct : null;
        final String groupName;
        if (accessGroupId != null) {
            groupName = accessGroupLstbx.getItemText(accessGroupLstbx.getSelectedIndex());
        } else {
            groupName = clientFactory.getGlobalGroupName();
        }
        final Boolean isValidGroup;
        if ((accessGroupIdDb == null && accessGroupId == null)
                || (accessGroupIdDb != null && accessGroupIdDb.equals(accessGroupId))) {
            isValidGroup = Boolean.FALSE;
        } else {
            isValidGroup = Boolean.TRUE;
        }
        final UsagePointWorkspaceView view = this;
        final Long customerAgreementId = data.getCustomerAgreementId();
        final Long usagePointId = data.getId();
        SessionCheckCallback sessionCheckCallback = new SessionCheckCallback() {
            @Override
            public void callback(SessionCheckResolution resolution) {
                clientFactory.getUsagePointRpc().isValidAccessGroupUpdate(usagePointId, customerAgreementId, accessGroupId,
                        new ClientCallback<Boolean>() {
                            @Override
                            public void onSuccess(Boolean isValidPs) {
                                UpdateAccessGroupDialogueBox updateAccessGroupDialogueBox = new UpdateAccessGroupDialogueBox(view, accessGroupId, groupName, isValidPs, isValidGroup);
                                updateAccessGroupDialogueBox.setPopupPosition(accessGroupUpdateButton.getAbsoluteLeft()+accessGroupUpdateButton.getOffsetWidth()-250, accessGroupUpdateButton.getAbsoluteTop() + accessGroupUpdateButton.getOffsetHeight());
                                updateAccessGroupDialogueBox.setGlassEnabled(true);
                                updateAccessGroupDialogueBox.setAutoHideEnabled(false);
                                updateAccessGroupDialogueBox.setAutoHideOnHistoryEventsEnabled(true);
                                updateAccessGroupDialogueBox.setAnimationEnabled(true);
                                updateAccessGroupDialogueBox.setText(MessagesUtil.getInstance().getMessage("access_group.update.header")+":");
                                updateAccessGroupDialogueBox.show();
                            }
                        });
            }
        };
        clientFactory.handleSessionCheckCallback(sessionCheckCallback);
    }

    public void updateAccessGroup(final Long newAccessGroupId) {
        UsagePointData usagePointData = this.getUsagePointData();
        if (newAccessGroupId == null) {
            // Clear
            clientFactory.getCustomerRpc().clearAccessGroup(usagePointData, new ClientCallback<Void>(
                    accessGroupUpdateButton.getAbsoluteLeft(), accessGroupUpdateButton.getAbsoluteTop()) {
                @Override
                public void onSuccess(Void result) {
                    accessGroupId = null;
                    accessGroupLstbx.setSelectedIndex(0);
                    reloadUI();
                    Dialogs.displayInformationMessage(
                            MessagesUtil.getInstance().getMessage("access_group.success.updated_group"),
                            MediaResourceUtil.getInstance().getInformationIcon());
                }
            });
        } else if (newAccessGroupId != null) {
            // Claim
            clientFactory.getCustomerRpc().claimAccessGroup(newAccessGroupId, usagePointData, new ClientCallback<Void>(
                    accessGroupUpdateButton.getAbsoluteLeft(), accessGroupUpdateButton.getAbsoluteTop()) {
                @Override
                public void onSuccess(Void result) {
                    accessGroupId = newAccessGroupId;
                    accessGroupLstbx.selectItemByValue(accessGroupId.toString());
                    reloadUI();
                    Dialogs.displayInformationMessage(
                            MessagesUtil.getInstance().getMessage("access_group.success.updated_group"),
                            MediaResourceUtil.getInstance().getInformationIcon());
                }
            });
        }
    }

    public void resetAccessGroup() {
        accessGroupLstbx.selectItemByValue(accessGroupId == null ? " " : accessGroupId.toString());
    }

    @Override
    public Workspace getWorkspace() {
        return this;
    }

    public void showViewUsagePointChargeDialogBox(final UsagePointData usagePointData, final int left, final int top,
            final UsagePointWorkspaceFactory usagePointWorkspaceFactory) {

          //if current PS is not thin / thin_units continue showing the viewCharges, will not have billing charges because
          //on tariffRetrieval, running backwards, the list is truncated if the next oldest tariff is a change from STS to AMI
          //or vice versa, so no charges to collect for previous tariff in that timeline
        Long currentPaymentModeId = usagePointData.getUpPricingStructureData().getPricingStructure().getPaymentModeId();
        if (!currentPaymentModeId.equals(PaymentModeE.THIN.getId()) && !currentPaymentModeId.equals(PaymentModeE.THIN_UNITS.getId())) {
            showViewUsagePointChargeDialogBoxContinue(usagePointData, left, top, usagePointWorkspaceFactory);

        } else {
            //else establish whether any history since last charge date DID have a billingCyclic Charge that might need writing off as well
            clientFactory.getSearchRpc().isBillingCyclicChargesInTariffHistSinceLastChargeDate(usagePointData, new ClientCallback<Boolean>() {
                @Override
                public void onSuccess(Boolean result) {
                    usagePointData.setHasBillingCyclicCharges(result);
                    showViewUsagePointChargeDialogBoxContinue(usagePointData, left, top, usagePointWorkspaceFactory);
                }
            });
        }
    }

    public void showViewUsagePointChargeDialogBoxContinue(final UsagePointData usagePointData, final int left, final int top,
            final UsagePointWorkspaceFactory usagePointWorkspaceFactory) {
        this.usagePointWorkspaceFactory = usagePointWorkspaceFactory;
        Messages messages = MessagesUtil.getInstance();

        //if activation date is in future - then just display a message, no outstanding charges!
        if (usagePointData.getActivationDate().after(new Date())) {
            Dialogs.displayInformationMessage(
                    messages.getMessage("usagepoint.charge.view.activation.in.future"),
                    MediaResourceUtil.getInstance().getInformationIcon());
            return;
        }

        UsagePointWorkspaceView usagePointWorkspaceView = null;
        if (usagePointWorkspaceFactory != null) {  //is not null when it comes from the Customer unassign & WRITEOFF event
            usagePointWorkspaceView = UsagePointWorkspaceView.this;
        }

        ViewUsagePointChargeDialogBox viewUsagePointChargeDialogBox = new ViewUsagePointChargeDialogBox(
                clientFactory, usagePointData, usagePointWorkspaceView);
        viewUsagePointChargeDialogBox.setAutoHideOnHistoryEventsEnabled(true);
        viewUsagePointChargeDialogBox.setAnimationEnabled(true);
        viewUsagePointChargeDialogBox.setGlassEnabled(true);
        viewUsagePointChargeDialogBox.setText(messages.getMessage("usagepoint.charge.view.dialog.heading") + ":");
        viewUsagePointChargeDialogBox.setPopupPosition(left, top);
        viewUsagePointChargeDialogBox.show();
        MeterMngClientUtils.ensurePopupIsOnScreen(viewUsagePointChargeDialogBox, left, top, 0);
    }

    public void unassignCustomerFromUsagePoint() {
        usagePointWorkspaceFactory.unassignCustomerFromUsagePoint(this, usagepointData);
    }

    private ArrayList<UsagePointWorkspaceView> getOtherUsagePointWorkspaceViews() {
        TabLayoutWorkspaceContainer container = (TabLayoutWorkspaceContainer) clientFactory.getWorkspaceContainer();
        int tabCount = container.getWorkspacesCount();
        ScrollableTabLayoutPanel tabLayoutPanel = container.getTabLayoutPanel();
        ArrayList<UsagePointWorkspaceView> otherWorkspaces = new ArrayList<>();
        for (int i = tabCount - 1; i >= 0; i--) {
            Workspace workspace = (Workspace) tabLayoutPanel.getWidget(i);
            if (workspace instanceof UsagePointWorkspaceView) {
                UsagePointWorkspaceView view = (UsagePointWorkspaceView) workspace;
                if (!this.equals(view)) {
                    otherWorkspaces.add(view);
                }
            }
        }
        return otherWorkspaces;
    }

    private void checkDuplicateDataOnOtherTabs() {
        UsagePointData usagePointData = this.getUsagePointData();
        Long meterIdBeingOpened = usagePointData.getMeterId();
        Long upIdBeingOpened = usagePointData.getId();
        Long custAgreementRefIdBeingOpened = usagePointData.getCustomerAgreementId();
        Long auxAccountsBeingOpened = usagePointData.getAuxAccountHistData();
        Long custAccTransHistIdBeingOpened = usagePointData.getHistoryData().getLatestCustomerAccountTransHistId();
        ArrayList<UsagePointWorkspaceView> otherUpWorkspaceViews = getOtherUsagePointWorkspaceViews();

        for (UsagePointWorkspaceView otherUpWorkspaceView : otherUpWorkspaceViews) {
            UsagePointData otherUsagePointData = otherUpWorkspaceView.getUsagePointData();
            if (otherUsagePointData != null) {
                Long otherMeterId = otherUsagePointData.getMeterId();
                Long otherUpId = otherUsagePointData.getId();
                Long otherCustAgreementRefId = otherUsagePointData.getCustomerAgreementId();
                Long otherAuxAccounts = otherUsagePointData.getAuxAccountHistData();
                Long otherCustAccTransHistId = otherUsagePointData.getHistoryData().getLatestCustomerAccountTransHistId();

                if (meterIdBeingOpened != null && meterIdBeingOpened.equals(otherMeterId)) {
                    logger.info("Found workspace open with duplicate meter");
                    clientFactory.getWorkspaceContainer().closeWorkspaceNow(otherUpWorkspaceView);
                    break;
                }
                if (upIdBeingOpened != null && upIdBeingOpened.equals(otherUpId)) {
                    logger.info("Found workspace open with duplicate usagepoint");
                    clientFactory.getWorkspaceContainer().closeWorkspaceNow(otherUpWorkspaceView);
                    break;
                }
                if (custAgreementRefIdBeingOpened != null && custAgreementRefIdBeingOpened.equals(otherCustAgreementRefId)) {
                    if (!clientFactory.isEnableMultiUp()) {
                        logger.info("Found workspace open with duplicate customer");
                        clientFactory.getWorkspaceContainer().closeWorkspaceNow(otherUpWorkspaceView);
                    }
                    break;
                }
                if (auxAccountsBeingOpened != null && auxAccountsBeingOpened.equals(otherAuxAccounts)) {
                    logger.info("Found workspace open with duplicate auxaccount");
                    clientFactory.getWorkspaceContainer().closeWorkspaceNow(otherUpWorkspaceView);
                    break;

                }
                if (custAccTransHistIdBeingOpened != null && custAccTransHistIdBeingOpened.equals(otherCustAccTransHistId)) {
                    logger.info("Found workspace open with duplicate customer account transaction");
                    clientFactory.getWorkspaceContainer().closeWorkspaceNow(otherUpWorkspaceView);
                    break;
                }
            }
        }
    }


    public boolean checkDuplicateDataOnOtherTabs(UsagePointData usagePointData) {
        ArrayList<UsagePointWorkspaceView> otherUpWorkspaceViews = getOtherUsagePointWorkspaceViews();
        if (otherUpWorkspaceViews.isEmpty()) {
            return false;
        }

        Long meterIdBeingOpened = usagePointData.getMeterId();
        Long upIdBeingOpened = usagePointData.getId();
        Long custAgreementRefIdBeingOpened = usagePointData.getCustomerAgreementId();
        Long auxAccountsBeingOpened = usagePointData.getAuxAccountHistData();
        Long custAccTransHistIdBeingOpened = usagePointData.getHistoryData().getLatestCustomerAccountTransHistId();
        Messages messages = MessagesUtil.getInstance();

        for (UsagePointWorkspaceView view : otherUpWorkspaceViews) {
            UsagePointData otherUsagePointData = view.getUsagePointData();
            if (otherUsagePointData != null) {
                Long otherMeterId = otherUsagePointData.getMeterId();
                Long otherUpId = otherUsagePointData.getId();
                Long otherCustAgreementRefId = otherUsagePointData.getCustomerAgreementId();
                Long otherAuxAccounts = otherUsagePointData.getAuxAccountHistData();
                Long otherCustAccTransHistId = otherUsagePointData.getHistoryData().getLatestCustomerAccountTransHistId();
                String messageKey = null;

                if (meterIdBeingOpened != null && meterIdBeingOpened.equals(otherMeterId)) {
                    messageKey = "error.tab.duplicate.meter";
                } else if (upIdBeingOpened != null && upIdBeingOpened.equals(otherUpId)) {
                    messageKey = "error.tab.duplicate.usagepoint";
                } else if (custAgreementRefIdBeingOpened != null && custAgreementRefIdBeingOpened.equals(otherCustAgreementRefId)) {
                    if (!clientFactory.isEnableMultiUp() || (otherMeterId == null && meterIdBeingOpened == null && upIdBeingOpened == null && otherUpId == null)) {
                        messageKey = "error.tab.duplicate.customer";
                    }
                } else if (auxAccountsBeingOpened != null && auxAccountsBeingOpened.equals(otherAuxAccounts)) {
                    messageKey = "error.tab.duplicate.auxaccount";

                }  else if (custAccTransHistIdBeingOpened != null && custAccTransHistIdBeingOpened.equals(otherCustAccTransHistId)) {
                    messageKey = "error.tab.duplicate.custaccounttrans";
                }

                if (messageKey != null) {
                    String message = messages.getMessage(messageKey, new String[]{view.getHeaderText()});
                    Dialogs.displayErrorMessage(message, MediaResourceUtil.getInstance().getErrorIcon());
                    return true;
                }
            }
        }
        return false;
    }

    public static String constructMessageInstallDtVsTariffStart(Date firstTariffStartDate, Date lastCyclicChargeDate) {
        Messages messages = MessagesUtil.getInstance();
        StringBuilder sb = new StringBuilder(messages.getMessage("usagepoint.installation.date.before.tariff.start1",
                new String[]{FormatUtil.getInstance().formatDateTime(firstTariffStartDate)}));
        if (lastCyclicChargeDate == null) {
            sb.append(messages.getMessage("usagepoint.installation.date.before.tariff.start3"));
        } else {
            sb.append(messages.getMessage("usagepoint.installation.date.before.tariff.start2",
                    new String[]{FormatUtil.getInstance().formatDateTime(lastCyclicChargeDate)}));
        }
        sb.append(messages.getMessage("usagepoint.installation.date.before.tariff.start4"));
        return sb.toString();
    }

    public void processInvalidState(final Place place) {
        final UsagePointWorkspaceView thisUsagePointWorkspaceView = this;

        Dialogs.centreErrorMessage(MessagesUtil.getInstance().getMessage("error.usagepoint.outdated"),
                MediaResourceUtil.getInstance().getErrorIcon(),
                MessagesUtil.getInstance().getMessage("button.reload"),
                new ClickHandler() {
                    @Override
                    public void onClick(ClickEvent arg0) {
                        ArrayList<UsagePointWorkspaceView> upWorkspaceViews = getOtherUsagePointWorkspaceViews();
                        upWorkspaceViews.add(thisUsagePointWorkspaceView);
                        for (UsagePointWorkspaceView upwv: upWorkspaceViews) {
                            Place otherPlace = upwv.getPlace();
                            if(otherPlace != null) {
                                if(otherPlace instanceof MeterPlace) {
                                    upwv.reload();
                                    clientFactory.getWorkspaceContainer().closeWorkspaceNow(otherPlace);
                                    clientFactory.getEventBus().fireEvent(new OpenUsagePointEvent(new MeterPlace(((MeterPlace) otherPlace).getMeterNumber())));
                                } else if (otherPlace instanceof UsagePointPlace) {
                                    upwv.reload();
                                    clientFactory.getWorkspaceContainer().closeWorkspaceNow(otherPlace);
                                    clientFactory.getEventBus().fireEvent(new OpenUsagePointEvent(new UsagePointPlace(((UsagePointPlace) otherPlace).getUsagePointName())));
                                } else if(otherPlace instanceof CustomerPlace) {
                                    upwv.reload();
                                    clientFactory.getWorkspaceContainer().closeWorkspaceNow(otherPlace);
                                    clientFactory.getEventBus().fireEvent(new OpenUsagePointEvent(new CustomerPlace(((CustomerPlace) otherPlace).getCustomerId())));
                                }
                            }
                        }
                    }
                });
    }

    // A group user must 1st claim the global contract or contract element before editing.
    public static boolean hasGlobalContractElement(UsagePointData upData, boolean isGroupGroupUser) {
        if (isGroupGroupUser && upData != null) {
            Long upAccessGroupId = -1l;
            Long meterAccessGroupId = -1l;
            Long customerAccessGroupId = -1l;

            if (upData.getId() != null) {
                upAccessGroupId = upData.getAccessGroupId();
            }

            MeterData meter = upData.getMeterData();
            if (meter != null && meter.getId() != null) {
                meterAccessGroupId = meter.getAccessGroupId();
            }

            CustomerAgreementData caData = upData.getCustomerAgreementData();
            if (caData != null && caData.getId() != null) {
                customerAccessGroupId = caData.getCustomerData().getAccessGroupId();
            }

            return (upAccessGroupId == null || meterAccessGroupId == null || customerAccessGroupId == null);
        }
        return false;
    }

    public int getUptabinx() {
        return uptabinx;
    }

    public int getSelectedTab(){
        return informationTabs.getTabBar().getSelectedTab();
    }

    public void toggleSaveBtns(boolean enable) {
        metercomponent.getSaveBtn().setEnabled(enable);
        customercomponent.getBtnSave().setEnabled(enable);
        usagepointcomponent.getBtnSave().setEnabled(enable);
    }
}
