package za.co.ipay.metermng.client.event;

import com.google.gwt.event.shared.GwtEvent;

public class EndDeviceStoreUpdatedEvent extends GwtEvent<EndDeviceStoreUpdatedEventHandler> {

    public static Type<EndDeviceStoreUpdatedEventHandler> TYPE = new Type<EndDeviceStoreUpdatedEventHandler>();
    
    private Long endDeviceStoreId;
    
    public Long getEndDeviceStoreId() {
        return endDeviceStoreId;
    }

    public void setEndDeviceStoreId(Long endDeviceStoreId) {
        this.endDeviceStoreId = endDeviceStoreId;
    }

    public EndDeviceStoreUpdatedEvent() {
        
    }
    
	@Override
    public Type<EndDeviceStoreUpdatedEventHandler> getAssociatedType() {
        return TYPE;
    }

    @Override
    protected void dispatch(EndDeviceStoreUpdatedEventHandler handler) {
        handler.processEndDeviceStoreUpdatedEvent(this);
    }
}
