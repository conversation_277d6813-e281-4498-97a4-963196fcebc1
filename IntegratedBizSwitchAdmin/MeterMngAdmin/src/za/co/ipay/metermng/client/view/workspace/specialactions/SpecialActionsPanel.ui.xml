<!DOCTYPE ui:UiBinder SYSTEM "http://dl.google.com/gwt/DTD/xhtml.ent">
<ui:UiBinder xmlns:ui="urn:ui:com.google.gwt.uibinder" 
             xmlns:g="urn:import:com.google.gwt.user.client.ui"
             xmlns:p1="urn:import:za.co.ipay.gwt.common.client.form"
             xmlns:p2="urn:import:za.co.ipay.gwt.common.client.widgets">
	<ui:style>	
	</ui:style>
	
	  <ui:with field="msg" type="za.co.ipay.metermng.client.i18n.UiMessages" />
    
	<g:FlowPanel>
	    <p1:FormRowPanel>
	    	<p1:FormElement debugId="nameElement" ui:field="nameElement" labelText="{msg.getSpecialActionName}:" helpMsg="{msg.getSpecialActionNameHelp}" required="true">
	        	<g:TextBox debugId="nameBox" text="" ui:field="nameTextBox" title="{msg.getSpecialActionName}" width="50" visibleLength="30"/>
	      	</p1:FormElement>
	      	<p1:FormElement ui:field="reasonRequiredElement" labelText="{msg.getSpecialActionReasonRequired}:" helpMsg="{msg.getSpecialActionReasonRequiredHelp}">
				<g:CheckBox ui:field="reasonRequiredBox" debugId="reasonRequiredBox" />
			</p1:FormElement>
			<p1:FormElement debugId="inputTypeElement" ui:field="inputTypeElement" labelText="{msg.getSpecialActionReasonInputType}:" helpMsg="{msg.getSpecialActionReasonInputTypeHelp}" required="true">
                <g:ListBox debugId="inputTypeListBox" visibleItemCount="1" ui:field="inputTypeBox" styleName="gwt-ListBox-ipay" multipleSelect="false"/>
          </p1:FormElement>
        </p1:FormRowPanel>
        <p1:FormRowPanel>
          
	    </p1:FormRowPanel>
	    <p1:FormRowPanel>
          <p1:FormElement debugId="descriptionElement" ui:field="descriptionElement" labelText="{msg.getSpecialActionDescription}:" helpMsg="{msg.getSpecialActionDescriptionHelp}" required="false">
            <g:TextArea debugId="descriptionBox" ui:field="descriptionTextArea" title="{msg.getSpecialActionDescription}" visibleLines="5" characterWidth="30" styleName="gwt-TextBox"/>
          </p1:FormElement>
	    </p1:FormRowPanel>
	  </g:FlowPanel>
	
</ui:UiBinder> 