package za.co.ipay.metermng.client.view.component;

import java.util.Date;
import java.util.List;

import com.google.gwt.core.client.GWT;
import com.google.gwt.event.dom.client.ClickEvent;
import com.google.gwt.i18n.client.DateTimeFormat;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiFactory;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.uibinder.client.UiHandler;
import com.google.gwt.user.client.Window;
import com.google.gwt.user.client.ui.Button;
import com.google.gwt.user.client.ui.DialogBox;
import com.google.gwt.user.client.ui.FlowPanel;
import com.google.gwt.user.client.ui.HTML;
import com.google.gwt.user.client.ui.Label;
import com.google.gwt.user.client.ui.Widget;
import za.co.ipay.gwt.common.client.Dialogs;
import za.co.ipay.gwt.common.client.form.FormElement;
import za.co.ipay.gwt.common.client.form.Format.FormatUtil;
import za.co.ipay.gwt.common.client.handler.ConfirmHandler;
import za.co.ipay.gwt.common.client.resource.Messages;
import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.gwt.common.client.widgets.IpayDateBox;
import za.co.ipay.gwt.common.client.widgets.Message;
import za.co.ipay.gwt.common.client.widgets.StrictDateFormat;
import za.co.ipay.gwt.common.client.workspace.SessionCheckCallback;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.i18n.UiMessages;
import za.co.ipay.metermng.client.i18n.UiMessagesUtil;
import za.co.ipay.metermng.client.resource.image.MediaResource.MediaResourceUtil;
import za.co.ipay.metermng.client.rpc.callback.ClientCallback;
import za.co.ipay.metermng.client.util.MeterMngClientUtils;
import za.co.ipay.metermng.client.view.workspace.UsagePointWorkspaceView;
import za.co.ipay.metermng.shared.CustomerTransItemData;
import za.co.ipay.metermng.shared.CustomerTransItemOutstandCharges;
import za.co.ipay.metermng.shared.dto.UsagePointData;

public class ViewUsagePointChargeDialogBox extends DialogBox {

    @UiField Message feedBack;

    @UiField FormElement vendCyclicChargeDateElement;
    @UiField Label lastVendCyclicDateLabel;                //by default not visible (no billingCyclic, so don't need differentation
    @UiField HTML lastVendCyclicChargeDate;

    @UiField FlowPanel lastBillingCyclicChargeDateElement;   //by default not visible
    @UiField HTML lastBillingCyclicChargeDate;


    @UiField FormElement cyclicChargeDateElement;
    @UiField IpayDateBox cyclicChargefilterDate;
    @UiField Button viewOutstandingChargeBtn;
    @UiField Button cancelOutstandingChargeBtn;

    private ClientFactory clientFactory;
    private UsagePointData usagePointData;
    private Date filterDate;
    private Date lastCyclicDate;
    private Date lastBillingCyclicDate;
    private UsagePointWorkspaceView usagePointWorkspaceView;
    private DateTimeFormat df = DateTimeFormat.getFormat("dd-MM-yyyy HH:mm:ss");

    private static ViewUsagePointChargeDialogBoxUiBinder uiBinder = GWT.create(ViewUsagePointChargeDialogBoxUiBinder.class);

    interface ViewUsagePointChargeDialogBoxUiBinder extends UiBinder<Widget, ViewUsagePointChargeDialogBox> {
    }

    public ViewUsagePointChargeDialogBox(ClientFactory clientFactory, UsagePointData usagePointData,
            UsagePointWorkspaceView usagePointWorkspaceView) {
        super();
        this.clientFactory = clientFactory;
        this.usagePointData = usagePointData;
        this.usagePointWorkspaceView = usagePointWorkspaceView;
        establishDates();

        setWidget(uiBinder.createAndBindUi(this));
        init();
    }

    private void establishDates() {
        //May have two types of billing charges: Vend_time charges and billing-time charges
        //if Usage point has a lastVendCyclicChargeDate, calc outstanding charges for vend_time charges from that date
        //else if UP lastVendCyclicChargeDate == null, then use the activation date as starting point for that calc
        //Ditto for lastBillingCyclicDate

        lastCyclicDate = usagePointData.getLastCyclicChargeDate();
        if (lastCyclicDate == null) {
            lastCyclicDate = usagePointData.getActivationDate();
        }
        lastBillingCyclicDate = usagePointData.getLastBillingCyclicChargeDate();
        if (lastBillingCyclicDate == null) {
            lastBillingCyclicDate = usagePointData.getActivationDate();
        }
    }

    private void init() {
        this.ensureDebugId("viewUsagePointChargeDialogBox");
        feedBack.setVisible(false);
        feedBack.setType(Message.MESSAGE_TYPE_ERROR);
        setDialogDates();
    }

    private void setDialogDates() {
        cyclicChargefilterDate.setFormat(
                new StrictDateFormat(DateTimeFormat.getFormat(FormatUtil.getInstance().getDateTimeFormat())));
        lastVendCyclicChargeDate.setText(FormatUtil.getInstance().formatDateTime(lastCyclicDate));
        if (usagePointData.isHasBillingCyclicCharges()) {
            vendCyclicChargeDateElement.setLabelText(MessagesUtil.getInstance().getMessage("usagepoint.last.cyclic.dates.info"));  //plural
            lastVendCyclicDateLabel.setVisible(true);
            lastBillingCyclicChargeDate.setText(FormatUtil.getInstance().formatDateTime(lastBillingCyclicDate));
            lastBillingCyclicChargeDateElement.setVisible(true);
        } else {
            lastVendCyclicChargeDate.setStyleName("gwt-Label-bold");
        }
    }

    @UiHandler("viewOutstandingChargeBtn")
    void handleViewOustandingChargeButton(ClickEvent event) {
        showFeedbackMessage("");
        filterDate = cyclicChargefilterDate.getValue();

        if (!isValidFilterDate()) {
            return;
        }

        boolean valid = true;
        if (usagePointData.isHasBillingCyclicCharges()) {
            if (!checkBothCyclicDates()) {
                valid = false;
            }
        } else if (!lastCyclicDate.before(filterDate)) {
            showFeedbackMessage("usagepoint.charge.view.dialog.invalid.date");
            valid = false;
        }

        if (valid) {
            SessionCheckCallback sessionCheckCallback = new SessionCheckCallback() {
                @Override
                public void callback(SessionCheckResolution sessionCheckResolution) {
                    showOutstandingCharges();
                }
            };
            clientFactory.handleSessionCheckCallback(sessionCheckCallback);
        }
    }

    private boolean isValidFilterDate() {
        //filterDate already extracted from form
        cyclicChargeDateElement.clearErrorMsg();
        if (!MeterMngClientUtils.isDateBoxValueValid(cyclicChargefilterDate.getTextBox().getText())) {
            String[] dateFormatParam = {FormatUtil.getInstance().getDateTimeFormat()};
            cyclicChargeDateElement.setErrorMsg(MessagesUtil.getInstance().getMessage("error.field.datetime.invalid", dateFormatParam));
            return false;
        }

        boolean valid = true;
        if (filterDate == null) {
            showFeedbackMessage("usagepoint.charge.view.dialog.nodate.filter");
            valid = false;
        } else if (filterDate.after(new Date())) {
            showFeedbackMessage("usagepoint.charge.view.dialog.invalid.date2");
            valid = false;
        }
        return valid;
    }

    private boolean checkBothCyclicDates() {
        //filterDate already extracted from form
        //both dates set to activation date if were null at instantiation
        boolean valid = true;
        Date lowestLastCyclicDate = lastCyclicDate;
        if (lastBillingCyclicDate.before(lowestLastCyclicDate)) {
            lowestLastCyclicDate = lastBillingCyclicDate;
        }

        if (!lowestLastCyclicDate.before(filterDate)) {
            showFeedbackMessage("usagepoint.charge.view.dialog.invalid.date.both");
            valid = false;
        } else if (!lastCyclicDate.before(filterDate)) {
            confirmLowDate("usagepoint.charge.view.dialog.warning.last.vend.cyclic.date", lastCyclicDate);
            valid = false;
        } else if (!lastBillingCyclicDate.before(filterDate)) {
            confirmLowDate("usagepoint.charge.view.dialog.warning.last.vend.billing.date", lastBillingCyclicDate);
            valid = false;
        }

        return valid;
    }

    private void confirmLowDate(String confirmMessage, Date cyclicDate) {
        final Messages messages = MessagesUtil.getInstance();
        String displayDate = df.format (cyclicDate);
        Dialogs.confirm(messages.getMessage(confirmMessage, new String[] {displayDate}), messages.getMessage("button.yes"),
                messages.getMessage("button.no"), MediaResourceUtil.getInstance().getQuestionIcon(),
                new ConfirmHandler() {
                    @Override
                    public void confirmed(boolean confirm) {
                        if (confirm) {
                            filterDate = cyclicChargefilterDate.getValue();
                            if (!isValidFilterDate()) {
                                return;
                            } else {
                                showOutstandingCharges();
                            }
                        }
                    }
        });
    }

    private void showOutstandingCharges() {
        UsagePointData usagePointDataNew = new UsagePointData();
        usagePointDataNew.setUsagePoint(this.usagePointData);
        usagePointDataNew.setLastCyclicChargeDate(lastCyclicDate);
        usagePointDataNew.setLastBillingCyclicChargeDate(lastBillingCyclicDate);
        usagePointDataNew.setHasBillingCyclicCharges(this.usagePointData.isHasBillingCyclicCharges());
        clientFactory.getUsagePointRpc().getCustomerTransItemFromCyclicCharges(usagePointDataNew, filterDate,
                new ClientCallback<CustomerTransItemOutstandCharges>() {
                    @Override
                    public void onSuccess(CustomerTransItemOutstandCharges results) {
                        showWriteoffDialog(results);
                    }

                    @Override
                    public void onFailure(Throwable caught) {
                        // no charges found
                        showFeedbackMessage("usagepoint.charge.no.data");
                    }
                });
    }

    private void showFeedbackMessage(String key) {
        feedBack.setText(MessagesUtil.getInstance().getMessage(key));
        feedBack.setVisible(!key.isEmpty());
    }

    private void showWriteoffDialog(CustomerTransItemOutstandCharges results) {
        List<CustomerTransItemData> outstandVendCharges = results.getVendCustTransItemOutStand();
        List<CustomerTransItemData> outstandBillingCharges = results.getBillingCustTransItemOutStand();

        if ((outstandVendCharges == null || outstandVendCharges.isEmpty())
                && (outstandBillingCharges == null || outstandBillingCharges.isEmpty())) {
            showFeedbackMessage("usagepoint.charge.no.data");
        } else {
            WriteoffUsagePointChargeDialogBox writeoffUsagePointChargeDialogBox = new WriteoffUsagePointChargeDialogBox(
                    clientFactory, usagePointData.getId(), lastCyclicDate, lastBillingCyclicDate, filterDate, results, usagePointWorkspaceView);
            writeoffUsagePointChargeDialogBox.setAutoHideEnabled(false);
            writeoffUsagePointChargeDialogBox.setGlassEnabled(true);
            writeoffUsagePointChargeDialogBox.setAutoHideOnHistoryEventsEnabled(true);
            writeoffUsagePointChargeDialogBox.setAnimationEnabled(true);
            writeoffUsagePointChargeDialogBox.setText(MessagesUtil.getInstance()
                    .getMessage("usagepoint.charge.writeoff.dialog.heading", new String[] {(DateTimeFormat.getFormat(FormatUtil.getInstance().getDateTimeFormat())).format(filterDate)}) + ":");
            int left = (Window.getClientWidth() / 3);
            writeoffUsagePointChargeDialogBox.setPopupPosition(left, 70);
            writeoffUsagePointChargeDialogBox.show();
            this.hide();
        }
    }

    @UiHandler("cancelOutstandingChargeBtn")
    void handleCancelOustandingChargeButton(ClickEvent event) {
        this.hide();
        if (usagePointWorkspaceView != null) {
            usagePointWorkspaceView.getCustomerComponent().handleAssignLink(null);
        }
    }

    @UiFactory
    public UiMessages getUiMessages() {
        return UiMessagesUtil.getInstance();
    }

}
