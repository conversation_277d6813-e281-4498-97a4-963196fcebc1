package za.co.ipay.metermng.client.factory;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Logger;

import com.google.gwt.core.client.GWT;
import com.google.gwt.i18n.client.LocaleInfo;
import com.google.gwt.json.client.JSONArray;
import com.google.gwt.json.client.JSONObject;
import com.google.gwt.json.client.JSONString;
import com.google.gwt.maps.client.base.LatLng;
import com.google.gwt.place.shared.PlaceController;
import com.google.gwt.place.shared.PlaceHistoryMapper;
import com.google.gwt.user.client.rpc.AsyncCallback;
import com.google.gwt.user.client.ui.SuggestBox;
import com.google.web.bindery.event.shared.EventBus;
import com.google.web.bindery.event.shared.SimpleEventBus;
import za.co.ipay.gwt.common.client.accesscontrol.permission.PermissionsImpl;
import za.co.ipay.gwt.common.client.accesscontrol.permission.PermissionsUtil;
import za.co.ipay.gwt.common.client.factory.ResourcesFactoryUtil;
import za.co.ipay.gwt.common.client.form.Format.FormatUtil;
import za.co.ipay.gwt.common.client.resource.Messages;
import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.gwt.common.client.rpc.PingRpc;
import za.co.ipay.gwt.common.client.rpc.PingRpcAsync;
import za.co.ipay.gwt.common.client.workspace.SessionCheckCallback;
import za.co.ipay.gwt.common.client.workspace.SessionCheckCallback.SessionCheckResolution;
import za.co.ipay.gwt.common.client.workspace.SessionCheckHandler;
import za.co.ipay.gwt.common.client.workspace.TabLayoutWorkspaceContainer;
import za.co.ipay.gwt.common.client.workspace.WorkspaceContainer;
import za.co.ipay.gwt.common.shared.message.MessageBundleData;
import za.co.ipay.metermng.client.MeterMngAdmin;
import za.co.ipay.metermng.client.MeterMngAdmin.LoadedHandler;
import za.co.ipay.metermng.client.event.ScheduledEventDispatcher;
import za.co.ipay.metermng.client.history.mapper.WorkspacePlaceToHistoryMapper;
import za.co.ipay.metermng.client.i18n.UiMessages;
import za.co.ipay.metermng.client.i18n.UiMessagesUtil;
import za.co.ipay.metermng.client.rpc.AppSettingRpc;
import za.co.ipay.metermng.client.rpc.AppSettingRpcAsync;
import za.co.ipay.metermng.client.rpc.AuxAccountsRpc;
import za.co.ipay.metermng.client.rpc.AuxAccountsRpcAsync;
import za.co.ipay.metermng.client.rpc.AuxChargeScheduleRpc;
import za.co.ipay.metermng.client.rpc.AuxChargeScheduleRpcAsync;
import za.co.ipay.metermng.client.rpc.AuxTypeRpc;
import za.co.ipay.metermng.client.rpc.AuxTypeRpcAsync;
import za.co.ipay.metermng.client.rpc.BillingDetRpc;
import za.co.ipay.metermng.client.rpc.BillingDetRpcAsync;
import za.co.ipay.metermng.client.rpc.BlockingTypeRpc;
import za.co.ipay.metermng.client.rpc.BlockingTypeRpcAsync;
import za.co.ipay.metermng.client.rpc.CalendarRpc;
import za.co.ipay.metermng.client.rpc.CalendarRpcAsync;
import za.co.ipay.metermng.client.rpc.CustomerRpc;
import za.co.ipay.metermng.client.rpc.CustomerRpcAsync;
import za.co.ipay.metermng.client.rpc.DashBoardRpc;
import za.co.ipay.metermng.client.rpc.DashBoardRpcAsync;
import za.co.ipay.metermng.client.rpc.DeviceStoreRpc;
import za.co.ipay.metermng.client.rpc.DeviceStoreRpcAsync;
import za.co.ipay.metermng.client.rpc.GroupRpc;
import za.co.ipay.metermng.client.rpc.GroupRpcAsync;
import za.co.ipay.metermng.client.rpc.ImportFileDataRpc;
import za.co.ipay.metermng.client.rpc.ImportFileDataRpcAsync;
import za.co.ipay.metermng.client.rpc.KeyIndicatorRpc;
import za.co.ipay.metermng.client.rpc.KeyIndicatorRpcAsync;
import za.co.ipay.metermng.client.rpc.LocationRpc;
import za.co.ipay.metermng.client.rpc.LocationRpcAsync;
import za.co.ipay.metermng.client.rpc.LookupRpc;
import za.co.ipay.metermng.client.rpc.LookupRpcAsync;
import za.co.ipay.metermng.client.rpc.ManufacturerRpc;
import za.co.ipay.metermng.client.rpc.ManufacturerRpcAsync;
import za.co.ipay.metermng.client.rpc.MdcChannelRpc;
import za.co.ipay.metermng.client.rpc.MdcChannelRpcAsync;
import za.co.ipay.metermng.client.rpc.MdcRpc;
import za.co.ipay.metermng.client.rpc.MdcRpcAsync;
import za.co.ipay.metermng.client.rpc.MeterMngAppRpc;
import za.co.ipay.metermng.client.rpc.MeterMngAppRpcAsync;
import za.co.ipay.metermng.client.rpc.MeterModelRpc;
import za.co.ipay.metermng.client.rpc.MeterModelRpcAsync;
import za.co.ipay.metermng.client.rpc.MeterRpc;
import za.co.ipay.metermng.client.rpc.MeterRpcAsync;
import za.co.ipay.metermng.client.rpc.NdpRpc;
import za.co.ipay.metermng.client.rpc.NdpRpcAsync;
import za.co.ipay.metermng.client.rpc.NotificationRpc;
import za.co.ipay.metermng.client.rpc.NotificationRpcAsync;
import za.co.ipay.metermng.client.rpc.PricingStructureRpc;
import za.co.ipay.metermng.client.rpc.PricingStructureRpcAsync;
import za.co.ipay.metermng.client.rpc.SalesPerResourceRpc;
import za.co.ipay.metermng.client.rpc.SalesPerResourceRpcAsync;
import za.co.ipay.metermng.client.rpc.ScheduleRpc;
import za.co.ipay.metermng.client.rpc.ScheduleRpcAsync;
import za.co.ipay.metermng.client.rpc.SearchRpc;
import za.co.ipay.metermng.client.rpc.SearchRpcAsync;
import za.co.ipay.metermng.client.rpc.SpecialActionsRpc;
import za.co.ipay.metermng.client.rpc.SpecialActionsRpcAsync;
import za.co.ipay.metermng.client.rpc.SupplyGroupRpc;
import za.co.ipay.metermng.client.rpc.SupplyGroupRpcAsync;
import za.co.ipay.metermng.client.rpc.TokenGenerationRpc;
import za.co.ipay.metermng.client.rpc.TokenGenerationRpcAsync;
import za.co.ipay.metermng.client.rpc.UsagePointGroupsRpc;
import za.co.ipay.metermng.client.rpc.UsagePointGroupsRpcAsync;
import za.co.ipay.metermng.client.rpc.UsagePointRpc;
import za.co.ipay.metermng.client.rpc.UsagePointRpcAsync;
import za.co.ipay.metermng.client.rpc.UserInterfaceRpc;
import za.co.ipay.metermng.client.rpc.UserInterfaceRpcAsync;
import za.co.ipay.metermng.client.rpc.UserRpc;
import za.co.ipay.metermng.client.rpc.UserRpcAsync;
import za.co.ipay.metermng.client.rpc.callback.ClientCallback;
import za.co.ipay.metermng.client.view.PrimaryLayoutView;
import za.co.ipay.metermng.client.view.component.SearchWidget;
import za.co.ipay.metermng.client.view.menu.CustomerMenuView;
import za.co.ipay.metermng.client.view.menu.CustomerSearchView;
import za.co.ipay.metermng.client.view.menu.GroupsMenuView;
import za.co.ipay.metermng.client.view.menu.MeterMenuView;
import za.co.ipay.metermng.client.view.menu.MeterSearchView;
import za.co.ipay.metermng.client.view.menu.PricingStructureMenuView;
import za.co.ipay.metermng.client.view.menu.SearchMenuView;
import za.co.ipay.metermng.client.view.workspace.AuxChargeScheduleWorkspaceView.AuxChargeStructureWorkspaceFactory;
import za.co.ipay.metermng.client.view.workspace.AuxTypeWorkspaceView.AuxTypeWorkspaceFactory;
import za.co.ipay.metermng.client.view.workspace.DeviceStoreWorkspaceView.DeviceStoreWorkspaceFactory;
import za.co.ipay.metermng.client.view.workspace.UsagePointWorkspaceFactory;
import za.co.ipay.metermng.client.view.workspace.appsetting.AppSettingWorkspaceFactory;
import za.co.ipay.metermng.client.view.workspace.billingdet.BillingDetWorkspaceView.BillingDetWorkspaceFactory;
import za.co.ipay.metermng.client.view.workspace.blockingtype.BlockingTypeWorkspaceFactory;
import za.co.ipay.metermng.client.view.workspace.bulkupload.auxaccountbulkupload.AuxAccountUploadWorkspaceFactory;
import za.co.ipay.metermng.client.view.workspace.bulkupload.auxtransupload.AuxTransUploadWorkspaceFactory;
import za.co.ipay.metermng.client.view.workspace.bulkupload.custtransbulkupload.CustomerTransUploadWorkspaceFactory;
import za.co.ipay.metermng.client.view.workspace.bulkupload.meterbulkupload.MeterBulkUploadWorkspaceFactory;
import za.co.ipay.metermng.client.view.workspace.bulkupload.metercustupbulkupload.MeterCustUPUploadWorkspaceFactory;
import za.co.ipay.metermng.client.view.workspace.calendar.CalendarSettingsWorkspaceFactory;
import za.co.ipay.metermng.client.view.workspace.calendar.CalendarsWorkspaceFactory;
import za.co.ipay.metermng.client.view.workspace.dashboard.AdminDashboardWorkspaceView.AdminDashboardWorkspaceFactory;
import za.co.ipay.metermng.client.view.workspace.dashboard.DashboardWorkspaceView.DashboardWorkspaceFactory;
import za.co.ipay.metermng.client.view.workspace.displaytokens.DisplayTokensWorkspaceFactory;
import za.co.ipay.metermng.client.view.workspace.globalndp.GlobalNdpWorkspaceFactory;
import za.co.ipay.metermng.client.view.workspace.group.all.GenGroupWorkspaceFactory;
import za.co.ipay.metermng.client.view.workspace.group.type.GroupTypeWorkspaceFactory;
import za.co.ipay.metermng.client.view.workspace.group.user.UserGroupWorkspaceFactory;
import za.co.ipay.metermng.client.view.workspace.importfile.ImportFileWorkspaceFactory;
import za.co.ipay.metermng.client.view.workspace.meter.balancing.alert.EnergyBalancingAlertWorkspaceFactory;
import za.co.ipay.metermng.client.view.workspace.meter.balancing.meter.EnergyBalancingMeterWorkspaceFactory;
import za.co.ipay.metermng.client.view.workspace.meter.manufacturer.ManufacturerWorkspaceFactory;
import za.co.ipay.metermng.client.view.workspace.meter.mdc.MdcWorkspaceFactory;
import za.co.ipay.metermng.client.view.workspace.meter.model.MeterModelWorkspaceFactory;
import za.co.ipay.metermng.client.view.workspace.meter.onlinebulk.MeterOnlineBulkWorkspaceFactory;
import za.co.ipay.metermng.client.view.workspace.meter.readings.add.singlemeter.AddMeterReadingsWorkspaceFactory;
import za.co.ipay.metermng.client.view.workspace.meter.readings.add.supermeter.AddSuperMeterReadingsWorkspaceFactory;
import za.co.ipay.metermng.client.view.workspace.meter.readings.view.MeterReadingsWorkspaceFactory;
import za.co.ipay.metermng.client.view.workspace.pricing.PricingStructureWorkspaceFactory;
import za.co.ipay.metermng.client.view.workspace.schedule.TaskScheduleWorkspaceFactory;
import za.co.ipay.metermng.client.view.workspace.search.SearchWorkspaceFactory;
import za.co.ipay.metermng.client.view.workspace.specialactions.SpecialActionsWorkspaceFactory;
import za.co.ipay.metermng.client.view.workspace.supplygroup.SupplyGroupWorkspaceFactory;
import za.co.ipay.metermng.client.view.workspace.uploads.MetadataUploadWorkspaceFactory;
import za.co.ipay.metermng.client.view.workspace.userinterface.UserInterfaceWorkspaceFactory;
import za.co.ipay.metermng.shared.CustomerAccountSuggestionsOracle;
import za.co.ipay.metermng.shared.CustomerAgreementSuggestionsOracle;
import za.co.ipay.metermng.shared.CustomerSuggestionsOracle;
import za.co.ipay.metermng.shared.DayProfileSuggestionsOracle;
import za.co.ipay.metermng.shared.MeterSuggestOracle;
import za.co.ipay.metermng.shared.PeriodSuggestionsOracle;
import za.co.ipay.metermng.shared.SeasonSuggestionsOracle;
import za.co.ipay.metermng.shared.UsagePointSuggestionsOracle;
import za.co.ipay.metermng.shared.dto.MeterMngAppData;
import za.co.ipay.metermng.shared.dto.message.MessagesDto;
import za.co.ipay.metermng.shared.dto.user.MeterMngUser;

public class ClientFactoryImpl implements ClientFactory {

    private final ClientFactory CLIENT_FACTORY = this;

    private final EventBus eventBus = new SimpleEventBus();
    private final ScheduledEventDispatcher scheduledEventDispatcher = new ScheduledEventDispatcher(eventBus);
    private final SearchRpcAsync searchRpcService = GWT.create(SearchRpc.class);
    private final LookupRpcAsync lookupRpcService = GWT.create(LookupRpc.class);
    private final CustomerRpcAsync customerRpcService = GWT.create(CustomerRpc.class);
    private final MeterRpcAsync meterRpcService = GWT.create(MeterRpc.class);

    private final KeyIndicatorRpcAsync keyIndicatorRpcService = GWT.create(KeyIndicatorRpc.class);
    private final UsagePointGroupsRpcAsync usagePointGroupsRpcService= GWT.create(UsagePointGroupsRpc.class);
    private final SalesPerResourceRpcAsync salesPerResourceRpcService= GWT.create(SalesPerResourceRpc.class);
    private final DashBoardRpcAsync dashBoardRpcService= GWT.create(DashBoardRpc.class);

    private final UsagePointRpcAsync usagepointRpcService = GWT.create(UsagePointRpc.class);

    private final PlaceController placeController = new PlaceController(eventBus);
    private final PlaceHistoryMapper placeHistoryMapper = GWT.create(WorkspacePlaceToHistoryMapper.class);
    private final PingRpcAsync pingRpcService = GWT.create(PingRpc.class);
    private final WorkspaceContainer workspaceContainer = new TabLayoutWorkspaceContainer(placeController,
            "Meter Management: ", new SessionCheckHandler() {
        @Override
        public void checkSession(final SessionCheckCallback callback) {
            handleSessionCheckCallback(callback);
        }
    });

    private final MeterMngAppRpcAsync meterMngAppRpc = GWT.create(MeterMngAppRpc.class);
    private final PricingStructureRpcAsync pricingStructureRPC = GWT.create(PricingStructureRpc.class);
    private final CalendarRpcAsync calendarRPC = GWT.create(CalendarRpc.class);
    private final TokenGenerationRpcAsync tokenGenerationRPC = GWT.create(TokenGenerationRpc.class);
    private final AuxChargeScheduleRpcAsync auxChargeStructureRPC = GWT.create(AuxChargeScheduleRpc.class);
    private final AuxTypeRpcAsync auxTypeRPC = GWT.create(AuxTypeRpc.class);
    private final SupplyGroupRpcAsync supplyGroupRPC = GWT.create(SupplyGroupRpc.class);
    private final AuxAccountsRpcAsync auxAccountsRPC = GWT.create(AuxAccountsRpc.class);
    private final GroupRpcAsync groupRPC = GWT.create(GroupRpc.class);
    private final DeviceStoreRpcAsync deviceStoreRpcAsync = GWT.create(DeviceStoreRpc.class);
    private final UserRpcAsync userRpcAsync = GWT.create(UserRpc.class);
    private final SeasonSuggestionsOracle seasonSuggestionsOracle = new SeasonSuggestionsOracle(this);
    private final PeriodSuggestionsOracle periodSuggestionsOracle = new PeriodSuggestionsOracle(this);
    private final DayProfileSuggestionsOracle dayProfileSuggestionsOracle = new DayProfileSuggestionsOracle(this);
    private final ManufacturerRpcAsync manufacturerRpc = GWT.create(ManufacturerRpc.class);
    private final MdcRpcAsync mdcRpc = GWT.create(MdcRpc.class);
    private final MeterModelRpcAsync meterModelRpc = GWT.create(MeterModelRpc.class);
    private final ScheduleRpcAsync scheduleRpc = GWT.create(ScheduleRpc.class);
    private final LocationRpcAsync locationRpc = GWT.create(LocationRpc.class);
    private final AppSettingRpcAsync appSettingRpc = GWT.create(AppSettingRpc.class);
    private final NdpRpcAsync ndpRpc = GWT.create(NdpRpc.class);
    private final BillingDetRpcAsync billingDetRpc = GWT.create(BillingDetRpc.class);
    private final MdcChannelRpcAsync mdcChannelRpc = GWT.create(MdcChannelRpc.class);
    private final SpecialActionsRpcAsync specialActionsRpc = GWT.create(SpecialActionsRpc.class);
    private final NotificationRpcAsync notificationRpcAsync = GWT.create(NotificationRpc.class);
    private final BlockingTypeRpcAsync blockingTypeRpc = GWT.create(BlockingTypeRpc.class);
    private final ImportFileDataRpcAsync importFileDataRpc = GWT.create(ImportFileDataRpc.class);
    private final UserInterfaceRpcAsync userInterfaceRpc = GWT.create(UserInterfaceRpc.class);

    private SuggestBox suggestBoxMeterNumber;
    private MeterMenuView meterMenuView;
    private MeterSearchView meterSearchView;
    private PrimaryLayoutView primaryLayoutView;
    private SuggestBox suggestBoxCustomer;
    private SuggestBox suggestBoxCustomerIdNumber;
    private SuggestBox suggestBoxCustomerAgreement;
    private SuggestBox suggestBoxCustomerAccount;
    private CustomerMenuView customerMenuView;
    private CustomerSearchView customerSearchView;
    private SearchWidget searchWidget;
    private GroupsMenuView groupsMenuView;
    private PricingStructureMenuView pricingMenuView;
    private SearchMenuView searchMenuView;


    private final MeterSuggestOracle meterSuggestionsOracle = new MeterSuggestOracle(this);
    private final CustomerSuggestionsOracle customerSuggestionsOracle = new CustomerSuggestionsOracle(this, false, false, false);
    private final CustomerSuggestionsOracle customerIdNumberSuggestionsOracle = new CustomerSuggestionsOracle(this, false, true, false);
    private final CustomerAgreementSuggestionsOracle customerAgreementSuggestionsOracle = new CustomerAgreementSuggestionsOracle(this);
    private final CustomerAccountSuggestionsOracle customerAccountSuggestionsOracle = new CustomerAccountSuggestionsOracle(this);


    private SuggestBox suggestBoxUsagePoint;
    private final UsagePointSuggestionsOracle usagePointSuggestionsOracle = new UsagePointSuggestionsOracle(this, false);


    private boolean demoMode = false;
    private boolean enableSTS = true;
    private boolean useMancoLogo = false;
    private boolean enableMultiUp = false;
    private boolean enableMultiCustAgr = false;
    private String logoUrl;
    private boolean enableCentianSTS = false;
    private boolean allowReversalsLastTrans = false;
    private boolean allowReversalsOlderTrans = false;
    private int groupsTreeDisplaySize;
    private int groupsSuggestBoxDisplaySize;

    private boolean isGoogleMapsReady = false;

    private boolean enableNonBillable = false;
    private boolean enableAccessGroups = false;

    /** The locale's name which can be set to be a specific name to be used for loading the messages and formats. */
    private String localeName;
    /** The current user of the application. */
    private MeterMngUser user;

    private static Logger logger = Logger.getLogger(ClientFactoryImpl.class.getName());
    private List<LatLng> storeLocations;
    private Date appSettingsChangedDate = new Date();

    public ClientFactoryImpl() {
        // The workspace factories register themselves with the workspace container
        new UsagePointWorkspaceFactory(this);
        new PricingStructureWorkspaceFactory(this);
        new AuxChargeStructureWorkspaceFactory(this);
        new AuxTypeWorkspaceFactory(this);
        new SupplyGroupWorkspaceFactory(this);
        new GroupTypeWorkspaceFactory(this);
        new GenGroupWorkspaceFactory(this);
        new DisplayTokensWorkspaceFactory(this);
        new DeviceStoreWorkspaceFactory(this);
        new BlockingTypeWorkspaceFactory(this);
        new UserGroupWorkspaceFactory(this);
        new SearchWorkspaceFactory(this);
        new MeterReadingsWorkspaceFactory(this);
        new CalendarSettingsWorkspaceFactory(this);
        new CalendarsWorkspaceFactory(this);
        new EnergyBalancingAlertWorkspaceFactory(this);
        new ManufacturerWorkspaceFactory(this);
        new MdcWorkspaceFactory(this);
        new MeterModelWorkspaceFactory(this);
        new AddMeterReadingsWorkspaceFactory(this);
        new AddSuperMeterReadingsWorkspaceFactory(this);
        new EnergyBalancingMeterWorkspaceFactory(this);
        new TaskScheduleWorkspaceFactory(this);
        new DashboardWorkspaceFactory(this);
        new AdminDashboardWorkspaceFactory(this);
        new AppSettingWorkspaceFactory(this);
        new CustomerTransUploadWorkspaceFactory(this);
        new AuxTransUploadWorkspaceFactory(this);
        new GlobalNdpWorkspaceFactory(this);
        new BillingDetWorkspaceFactory(this);
        new MeterCustUPUploadWorkspaceFactory(this);
        new MeterBulkUploadWorkspaceFactory(this);
        new AuxAccountUploadWorkspaceFactory(this);
        new SpecialActionsWorkspaceFactory(this);
        new MeterOnlineBulkWorkspaceFactory(this);
        new ImportFileWorkspaceFactory(this);
        new MetadataUploadWorkspaceFactory(this);
        new UserInterfaceWorkspaceFactory(this);
        //Permissions for the application
        PermissionsUtil.setInstance(new PermissionsImpl("mtrmng_view","mtrmng_create","mtrmng_edit","mtrmng_delete"));
        //Resources
        ResourcesFactoryUtil.setInstance(new ResourcesFactoryImpl());
    }

    @Override
    public EventBus getEventBus() {
        return eventBus;
    }

    @Override
    public ScheduledEventDispatcher getScheduledEventDispatcher() {
        return scheduledEventDispatcher;
    }

    @Override
    public void loadConfigAndMessages(final LoadedHandler loadedHandler) {
        String currentLocale = getLocaleName();
        logger.info("Loading app config and messages/formats for current locale: "+currentLocale);
        meterMngAppRpc.getConfigAndMessages(currentLocale,
                                         new AsyncCallback<MeterMngAppData>() {
                                            @Override
                                            public void onFailure(Throwable t) {
                                                MessagesDto messagesDto = new MessagesDto();
                                                messagesDto.setMessages(new MessageBundleData("", new HashMap<String, String>()));
                                                messagesDto.setFormats(new MessageBundleData("", new HashMap<String, String>()));
                                                setMessages(messagesDto);
                                            }

                                            @Override
                                            public void onSuccess(MeterMngAppData result) {
                                                logger.info("Loaded config and messages successfully: "+result);
                                                // set release version
                                                MeterMngAdmin.RELEASE = result.getVersion();
                                                //Messages
                                                setMessages(result.getMessages());
                                                //Create the rest of the Ui
                                                loadedHandler.loaded(result.getMeterMngConfig());
                                            }

                                            private void setMessages(MessagesDto messagesDto) {
                                                //All the messages
                                                String localeName = messagesDto.getMessages().getLocaleName();
                                                Map<String, String> messages = messagesDto.getMessages().getMessages();
                                                MessagesUtil.setMessages(new Messages(localeName, messages));
                                                //The UI version of the messages
                                                UiMessages uiMessages = GWT.create(UiMessages.class);
                                                uiMessages.setMessages(messages);
                                                UiMessagesUtil.setUiMessages(uiMessages);
                                                logger.info("Created UiMessages: "+uiMessages.getClass().getName());

                                                //Format instance
                                                localeName = messagesDto.getFormats().getLocaleName();
                                                messages = messagesDto.getFormats().getMessages();
                                                boolean rightToLeft = false;
                                                if (messages.get("rightToLeft") != null && messages.get("rightToLeft").toLowerCase().contains("true")) {
                                                    rightToLeft = true;
                                                }
                                                FormatUtil.setFormat(rightToLeft,
                                                                     localeName,
                                                                     messages.get("currency.symbol"),
                                                                     messages.get("unit.symbol"),
                                                                     messages.get("currency.pattern"),
                                                                     messages.get("currency.separator.grouping"),
                                                                     messages.get("currency.separator.decimal"),
                                                                     messages.get("decimal.pattern"),
                                                                     messages.get("decimal.separator.grouping"),
                                                                     messages.get("decimal.separator.decimal"),
                                                                     messages.get("datetime.pattern"),
                                                                     messages.get("date.pattern"),
                                                                     messages.get("time.pattern"),
                                                                     messages.get("cellphone.pattern"),
                                                                     messages.get("cellphone.placeholder"),
                                                                     null, null); //by default the time zone is set to the local one

                                            }
                                        });
    }

    @Override
    public SearchRpcAsync getSearchRpc() {
        return searchRpcService;
    }

    @Override
    public LookupRpcAsync getLookupRpc() {
        return lookupRpcService;
    }

    @Override
    public UsagePointRpcAsync getUsagePointRpc() {
        return usagepointRpcService;
    }

    @Override
    public PrimaryLayoutView getPrimaryLayoutView() {
        return primaryLayoutView;
    }

    @Override
    public SearchMenuView getSearchMenuView() {
        return searchMenuView;
    }
    @Override
    public MeterMenuView getMeterMenuView() {
        return meterMenuView;
    }

    @Override
    public MeterSearchView getMeterSearchView() {
        return meterSearchView;
    }

    @Override
    public PlaceController getPlaceController() {
        return placeController;
    }

    @Override
    public CustomerMenuView getCustomerMenuView() {
        return customerMenuView;
    }

    @Override
    public CustomerSearchView getCustomerSearchView() {
        return customerSearchView;
    }

    @Override
    public CustomerSuggestionsOracle getCustomerSuggestionsOracle() {
        return customerSuggestionsOracle;
    }

    @Override
    public CustomerAgreementSuggestionsOracle getCustomerAgreementSuggestionsOracle() {
        return customerAgreementSuggestionsOracle;
    }

    @Override
    public CustomerAccountSuggestionsOracle getCustomerAccountSuggestionsOracle() {
        return customerAccountSuggestionsOracle;
    }

    @Override
    public SearchWidget getSearchWidget() {
        return searchWidget;
    }

    @Override
    public WorkspaceContainer getWorkspaceContainer() {
        return workspaceContainer;
    }

    @Override
    public GroupsMenuView getGroupsMenuView() {
        return groupsMenuView;
    }

    @Override
    public PricingStructureMenuView getPricingStructureMenuView() {
        return pricingMenuView;
    }

    @Override
    public CustomerRpcAsync getCustomerRpc() {
        return customerRpcService;
    }

    @Override
    public MeterRpcAsync getMeterRpc() {
        return meterRpcService;
    }

    @Override
    public PricingStructureRpcAsync getPricingStructureRpc() {
        return pricingStructureRPC;
    }

    @Override
    public CalendarRpcAsync getCalendarRpc() {
        return calendarRPC;
    }

    @Override
    public TokenGenerationRpcAsync getTokenGeneratorRpc() {
        return tokenGenerationRPC;
    }

    @Override
    public AuxChargeScheduleRpcAsync getAuxChargeStructureRpc() {
        return auxChargeStructureRPC;
    }

    @Override
    public AuxTypeRpcAsync getAuxTypeRpc() {
        return auxTypeRPC;
    }

    @Override
    public PlaceHistoryMapper getPlaceHistoryMapper() {
        return placeHistoryMapper;
    }

    @Override
    public SupplyGroupRpcAsync getSupplyGroupRpc() {
        return supplyGroupRPC;
    }

//    @Override
//    public TranslationRpcAsync getTranslationRpc() {
//        return translationRPC;
//    }

    @Override
    public AuxAccountsRpcAsync getAuxAccountsRpc() {
         return auxAccountsRPC;
    }

    @Override
    public GroupRpcAsync getGroupRpc() {
        return groupRPC;
    }

    @Override
    public DeviceStoreRpcAsync getDeviceStoreRpc() {
        return deviceStoreRpcAsync;
    }

    @Override
    public UserRpcAsync getUserRpc() {
        return userRpcAsync;
    }

    @Override
    public SpecialActionsRpcAsync getSpecialActionsRpc() {
        return specialActionsRpc;
    }

    @Override
    public NotificationRpcAsync getNotificationRpcAsync(){
        return notificationRpcAsync;
    }

    @Override
    public BlockingTypeRpcAsync getBlockingTypeRpc() {
        return blockingTypeRpc;
    }

    @Override
    public ImportFileDataRpcAsync getImportFileDataRpc() {
        return importFileDataRpc;
    }

    @Override
    public UserInterfaceRpcAsync getUserInterfaceRpcAsync() {
        return userInterfaceRpc;
    }

    @Override
    public SeasonSuggestionsOracle getSeasonSuggestionsOracle() {
        return seasonSuggestionsOracle;
    }

    @Override
    public PeriodSuggestionsOracle getPeriodSuggestionsOracle() {
        return periodSuggestionsOracle;
    }

    @Override
    public DayProfileSuggestionsOracle getDayProfileSuggestionsOracle() {
        return dayProfileSuggestionsOracle;
    }

    @Override
    public void createViews() {
        suggestBoxMeterNumber = new SuggestBox(meterSuggestionsOracle);
        suggestBoxUsagePoint = new SuggestBox(usagePointSuggestionsOracle);
        meterSearchView = new MeterSearchView(suggestBoxMeterNumber, suggestBoxUsagePoint, CLIENT_FACTORY);

        suggestBoxCustomer = new SuggestBox(customerSuggestionsOracle);
        suggestBoxCustomerIdNumber = new SuggestBox(customerIdNumberSuggestionsOracle);
        suggestBoxCustomerAgreement = new SuggestBox(customerAgreementSuggestionsOracle);
        suggestBoxCustomerAccount = new SuggestBox(customerAccountSuggestionsOracle);
        customerSearchView = new CustomerSearchView(suggestBoxCustomer, suggestBoxCustomerIdNumber, suggestBoxCustomerAgreement, suggestBoxCustomerAccount, CLIENT_FACTORY);

        searchMenuView = new SearchMenuView(CLIENT_FACTORY);
        meterMenuView = new MeterMenuView(CLIENT_FACTORY);
        customerMenuView = new CustomerMenuView(CLIENT_FACTORY);
        groupsMenuView = new GroupsMenuView(CLIENT_FACTORY);
        pricingMenuView = new PricingStructureMenuView(CLIENT_FACTORY);
        primaryLayoutView = new PrimaryLayoutView(CLIENT_FACTORY, user);
        logger.info("Created views");
        if (isEnableAccessGroups() && user.getSessionGroupName() != null) {
            displayUserGroup(user.getSessionGroupName());
        } else if (!isEnableAccessGroups() && user.getAssignedGroup() != null) {
            displayUserGroup(user.getAssignedGroup().getName());
        } else {
            displayUserGroup("");
        }
        logger.info("Updated views for user: "+user.getUserName());
    }

    @Override
    public void setUser(MeterMngUser user) {
        this.user = user;
    }

    @Override
    public void displayUserGroup(String groupName) {
        if (groupName == null) {
            getPrimaryLayoutView().setCurrentGroup("");
        } else {
            getPrimaryLayoutView().setCurrentGroup(groupName);
        }
    }

    @Override
    public MeterMngUser getUser() {
        return user;                //RC: Watchit! THis is not kept up to date when current userGroup changes!!
    }

    @Override
    public ManufacturerRpcAsync getManufacturerRpc() {
        return manufacturerRpc;
    }

    @Override
    public MdcRpcAsync getMdcRpc() {
        return mdcRpc;
    }

    @Override
    public MdcChannelRpcAsync getMdcChannelRpc() {
        return mdcChannelRpc;
    }

    @Override
    public MeterModelRpcAsync getMeterModelRpc() {
        return meterModelRpc;
    }

    @Override
    public ScheduleRpcAsync getScheduleRpc() {
        return scheduleRpc;
    }

    @Override
    public void setLocaleName(String localeName) {
        this.localeName = localeName;
    }

    @Override
    public MeterMngAppRpcAsync getMeterMngAppRpc() {
        return meterMngAppRpc;
    }

    @Override
    public String getLocaleName() {
        if (localeName != null && !localeName.trim().equals("")) {
            return localeName;
        } else {
            return LocaleInfo.getCurrentLocale().getLocaleName();
        }
    }

    @Override
    public LocationRpcAsync getLocationRpc() {
        return locationRpc;
    }

    @Override
    public AppSettingRpcAsync getAppSettingRpc() {
        return appSettingRpc;
    }

    @Override
    public NdpRpcAsync getNdpRpc(){
        return ndpRpc;
    }

    @Override
    public BillingDetRpcAsync getBillingDetRpc() {
        return billingDetRpc;
    }

    @Override
    public PingRpcAsync getPingRpc() {
        return pingRpcService;
    }

    @Override
    public void handleSessionCheckCallback(final SessionCheckCallback sessionCheckCallback) {
        getPingRpc().ping(new ClientCallback<Void>(sessionCheckCallback) {
            @Override
            public void onSuccess(Void result) {
                sessionCheckCallback.callback(SessionCheckResolution.CONTINUE);
            }
        });
    }

    //----------------- Parameters ---------------------------------------------------------------------------------------
    @Override
    public boolean isDemoMode() {
        return demoMode;
    }

    @Override
    public void setDemoMode(boolean demoMode) {
        this.demoMode = demoMode;
    }


    @Override
    public boolean isEnableSTS() {
        return enableSTS;
    }

    @Override
    public void setEnableSTS(boolean enableSTS) {
        this.enableSTS = enableSTS;
    }

    @Override
    public boolean isUseMancoLogo(){
        return useMancoLogo;
    }

    @Override
    public void setUseMancoLogo(boolean useMancoLogo){
        this.useMancoLogo = useMancoLogo;
    }

    @Override
    public String getLogoUrl(){
        return logoUrl;
    }

    @Override
    public void setLogoUrl(String logoUrl) {
        this.logoUrl = logoUrl;
    }

    /**
    * Gets the name of the current browser.
    */
    public static native String getBrowserName() /*-{
        return navigator.userAgent.toLowerCase();
    }-*/;

    /**
    * Returns true if the current browser is IE (Internet Explorer).
    * use contains("msie") || contains("trident") to match IE11
    */
    @Override
    public boolean isIEBrowser() {
        String browserName = getBrowserName().toLowerCase();
        return (browserName.contains("msie") || browserName.contains("trident"));
    }

    @Override
    public boolean isEnableMultiUp() {
        return enableMultiUp;
    }

    @Override
    public void setEnableMultiUp(boolean enableMultiUp) {
        this.enableMultiUp = enableMultiUp;
    }

    @Override
    public boolean isEnableMultiCustAgr() {
        return enableMultiCustAgr;
    }

    @Override
    public void setEnableMultiCustAgr(boolean enableMultiCustAgr) {
        this.enableMultiCustAgr = enableMultiCustAgr;
    }

    @Override
    public boolean isEnableCentianSTS() {
        return enableCentianSTS;
    }

    @Override
    public void setEnableCentianSTS(boolean enableCentianSTS) {
        this.enableCentianSTS = enableCentianSTS;
    }

    @Override
    public boolean isEnableNonBillable() {
        return enableNonBillable;
    }

    @Override
    public void setEnableNonBillable(boolean enableNonBillable) {
        this.enableNonBillable = enableNonBillable;
    }

    @Override
    public boolean isEnableAccessGroups() {
        return enableAccessGroups;
    }

    @Override
    public void setEnableAccessGroups(boolean enableAccessGroups) {
        this.enableAccessGroups = enableAccessGroups;
    }

    @Override
    public boolean isGroupGlobalUser() {
        // access groups are enabled and its a global user.
        // PERMISSIONS should be considered as an alternative.
        return (enableAccessGroups && user.getSessionGroupId() == null);
    }

    @Override
    public boolean isGroupGroupUser() {
        // access groups are enabled and its a regional user.
        // PERMISSIONS should be considered as an alternative.
        return (enableAccessGroups && user.getSessionGroupId() != null);
    }

    @Override
    public String getGlobalGroupName() {
        return user.getGlobalGroupName();
    }

    @Override
    public boolean hasAccessGroupsEditPermissions(Long objectGroupId) {
        if (enableAccessGroups) {
            if (user.getSessionGroupId() != null) {
                return (user.getSessionGroupId().equals(objectGroupId));
            } else {
                return (objectGroupId == null);
            }
        }
        return true;
    }

    @Override
    public KeyIndicatorRpcAsync getKeyIndicatorRpc() {
        return keyIndicatorRpcService;
    }

    @Override
    public UsagePointGroupsRpcAsync getUsagePointGroupsRpc() {
        return usagePointGroupsRpcService;
    }

    @Override
    public SalesPerResourceRpcAsync getSalesPerResourceRpc() {
        return salesPerResourceRpcService;
    }

    @Override
    public DashBoardRpcAsync getDashBoardRpc() {
        return dashBoardRpcService;
    }

	@Override
	public boolean isAllowReversalsLastTrans() {
		return allowReversalsLastTrans;
	}

	@Override
	public void setAllowReversalsLastTrans(boolean allowReversalsLastTrans) {
		this.allowReversalsLastTrans = allowReversalsLastTrans;
	}

	@Override
	public boolean isAllowReversalsOlderTrans() {
		return allowReversalsOlderTrans;
	}

	@Override
	public void setAllowReversalsOlderTrans(boolean allowReversalsOlderTrans) {
		this.allowReversalsOlderTrans = allowReversalsOlderTrans;
		if (!allowReversalsLastTrans && allowReversalsOlderTrans) {
			logger.warning("MMA: WARNING! ClientFactoryImpl trying to set allowReversalsOlderTrans without allowReversalsOlderTrans. default both to false now. Check the context.xml");
			this.allowReversalsOlderTrans = false;
		}
	}

    @Override
    public void setGoogleMapsReady(boolean isGoogleMapsReady){
        this.isGoogleMapsReady = isGoogleMapsReady;
    }

    @Override
    public boolean isGoogleMapsReady(){
        return isGoogleMapsReady;
    }

    @Override
    public void setStoreLocations(String storeLocations) {
        List<LatLng> tmpStoreLocations = new ArrayList<>();
        JSONString js = new JSONString(storeLocations);
        JSONArray locations = js.isArray();
        for(int i = 0, j = locations.size(); i < j; i++){
            JSONObject object = locations.get(i).isObject();
            LatLng storeLocationCoords = LatLng.newInstance(object.get("lat").isNumber().doubleValue(), object.get("lon").isNumber().doubleValue());
            tmpStoreLocations.add(storeLocationCoords);
        }
        this.storeLocations = tmpStoreLocations;
    }

    @Override
    public List<LatLng> getStoreLocations() {
        return storeLocations;
    }

    @Override
    public void updateAppSettingsChangedDate() {
        appSettingsChangedDate = new Date();
    }

    @Override
    public boolean hasAppSettingsChanged(Date appSettingsChangedDate) {
        return appSettingsChangedDate == null || appSettingsChangedDate.before(this.appSettingsChangedDate);
    }

    @Override
    public int getGroupsTreeDisplaySize() {
        return groupsTreeDisplaySize;
    }

    @Override
    public void setGroupsTreeDisplaySize(int groupsTreeDisplaySize) {
        this.groupsTreeDisplaySize = groupsTreeDisplaySize;
    }

    @Override
    public int getGroupsSuggestBoxDisplaySize() {
        return groupsSuggestBoxDisplaySize;
    }

    @Override
    public void setGroupsSuggestBoxDisplaySize(int groupsSuggestBoxDisplaySize) {
        this.groupsSuggestBoxDisplaySize = groupsSuggestBoxDisplaySize;
    }
}
