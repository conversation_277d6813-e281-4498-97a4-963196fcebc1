package za.co.ipay.metermng.client.event;

import com.google.gwt.event.shared.GwtEvent;

public class SeasonsUpdatedEvent extends GwtEvent<SeasonsUpdatedEventHandler> {

    public static Type<SeasonsUpdatedEventHandler> TYPE = new Type<SeasonsUpdatedEventHandler>();
    
    public SeasonsUpdatedEvent() {
        
    }
    
	@Override
    public Type<SeasonsUpdatedEventHandler> getAssociatedType() {
        return TYPE;
    }

    @Override
    protected void dispatch(SeasonsUpdatedEventHandler handler) {
        handler.processSeasonsUpdatedEvent(this);
    }


}
