package za.co.ipay.metermng.client.history;

import com.google.gwt.place.shared.Place;
import com.google.gwt.place.shared.PlaceTokenizer;
import com.google.gwt.place.shared.Prefix;

public class AppSettingPlace extends Place {
    
    public static final String ALL = "all";
    
    private String name;
    
    public AppSettingPlace(String name) {
        this.name = name;
    }
    
    public String getName() {
        return name;
    }

    public static String getPlaceAsString(AppSettingPlace place) {
        return "appSetting:"+place.getName();
    }
    
    @Prefix(value = "appSetting")
    public static class Tokenizer implements PlaceTokenizer<AppSettingPlace> {
        
        @Override
        public String getToken(AppSettingPlace place) {
            return place.getName();
        }

        @Override
        public AppSettingPlace getPlace(String token) {
            return new AppSettingPlace(token);
        }
    }
}
