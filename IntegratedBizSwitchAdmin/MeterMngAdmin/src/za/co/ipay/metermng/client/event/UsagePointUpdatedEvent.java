package za.co.ipay.metermng.client.event;

import java.util.List;

import com.google.gwt.event.shared.GwtEvent;

import za.co.ipay.metermng.client.view.workspace.UsagePointWorkspaceView;
import za.co.ipay.metermng.shared.dto.MdcChannelReadingsDto;
import za.co.ipay.metermng.shared.dto.UsagePointData;

public class UsagePointUpdatedEvent extends GwtEvent<UsagePointUpdatedEventHandler> {
	
	public static final int ASSIGN_CUSTOMER = 1;
	public static final int UNASSIGN_CUSTOMER = 2;
	public static final int SAVE_METER = 3;
	public static final int ASSIGN_METER = 4;
	public static final int SAVE_CUSTOMER = 5;
	public static final int SAVE_USAGE_POINT = 6;
	public static final int REMOVE_METER = 7;
	public static final int FETCH_CUSTOMER = 8;
	public static final int FETCH_USAGE_POINT = 9;
    public static final int WRITEOFF_CHARGES = 10;
    public static final int SAVE_NOTIFICATIONS = 11;
	
	public static Type<UsagePointUpdatedEventHandler> TYPE = new Type<UsagePointUpdatedEventHandler>();
    private final UsagePointData usagePointData;
    private int updateType;
    private Long assignToCustomerId;
    private String assignToMeterNumber;
    private UsagePointWorkspaceView usagePointWorkspaceView;
    private int left;
    private int top;
    private boolean isAddNewMeterToExistingUsagePoint = false;
    private List<MdcChannelReadingsDto> channelReadingsList = null;
    private boolean userDeactivatedUP = false;
    
	public UsagePointUpdatedEvent(UsagePointWorkspaceView usagePointWorkspaceView, UsagePointData usagePointData, int updateType) {
		super();
		this.usagePointWorkspaceView = usagePointWorkspaceView;
		this.usagePointData = usagePointData;
		this.updateType = updateType;
	}
	
	public UsagePointUpdatedEvent(UsagePointWorkspaceView usagePointWorkspaceView, UsagePointData usagePointData, int updateType, int top, int left) {
        super();
        this.usagePointWorkspaceView = usagePointWorkspaceView;
        this.usagePointData = usagePointData;
        this.updateType = updateType;
        this.top = top;
        this.left = left;
    }
	
	public UsagePointWorkspaceView getUsagePointWorkspaceView() {
        return usagePointWorkspaceView;
    }

	public UsagePointData getUsagePointData() {
		return usagePointData;
	}

	public int getUpdateType() {
		return updateType;
	}
	
	public void setAssignToCustomerId(Long assignToCustomerId) {
		this.assignToCustomerId = assignToCustomerId;
	}

	public Long getAssignToCustomerId() {
		return assignToCustomerId;
	}
	
	public String getAssignToMeterNumber() {
		return assignToMeterNumber;
	}

	public void setAssignToMeterNumber(String assignToMeterNumber) {
		this.assignToMeterNumber = assignToMeterNumber;
	}

	public List<MdcChannelReadingsDto> getChannelReadingsList() {
        return channelReadingsList;
    }

    public void setChannelReadingsList(List<MdcChannelReadingsDto> channelReadingsList) {
        this.channelReadingsList = channelReadingsList;
    }

    public boolean isUserDeactivatedUP() {
        return userDeactivatedUP;
    }

    public void setUserDeactivatedUP(boolean userDeactivatedUP) {
        this.userDeactivatedUP = userDeactivatedUP;
    }

    public int getLeft() {
        return left;
    }

    public int getTop() {
        return top;
    }

    public void setLeft(int left) {
        this.left = left;
    }

    public void setTop(int top) {
        this.top = top;
    }

    public boolean isAddNewMeterToExistingUsagePoint() {
        return isAddNewMeterToExistingUsagePoint;
    }

    public void setAddNewMeterToExistingUsagePoint(boolean isAddNewMeterToExistingUsagePoint) {
        this.isAddNewMeterToExistingUsagePoint = isAddNewMeterToExistingUsagePoint;
    }

    @Override
	public Type<UsagePointUpdatedEventHandler> getAssociatedType() {
		return TYPE;
	}

	@Override
	protected void dispatch(UsagePointUpdatedEventHandler handler) {
		handler.updateUsagePoint(this);
	}


}
