<!DOCTYPE ui:UiBinder SYSTEM "http://dl.google.com/gwt/DTD/xhtml.ent">
<ui:UiBinder xmlns:ui="urn:ui:com.google.gwt.uibinder" 
             xmlns:g="urn:import:com.google.gwt.user.client.ui"
	         xmlns:p1="urn:import:za.co.ipay.gwt.common.client.form"
	         xmlns:p2="urn:import:za.co.ipay.metermng.client.view.component"
	         xmlns:w="urn:import:za.co.ipay.gwt.common.client.widgets">
	<ui:style>
  	</ui:style>

	<ui:with field="msg" type="za.co.ipay.metermng.client.i18n.UiMessages" />

	<g:FlowPanel>

		<p1:FormRowPanel>
			<p1:FormElement ui:field="nameElement" debugId="nameElement" labelText="{msg.getMeterModelName}:" helpMsg="{msg.getMeterModelNameHelp}" required="true">
				<g:TextBox text="" ui:field="nameTextBox" debugId="nameTextBox" title="{msg.getMeterModelName}" />
			</p1:FormElement>
			<p1:FormElement ui:field="descriptionElement" debugId="descriptionElement" labelText="{msg.getMeterModelDescription}:" helpMsg="{msg.getMeterModelDescriptionHelp}">
				<g:TextBox ui:field="descriptionTextBox" debugId="descriptionTextBox" title="{msg.getMeterModelDescription}" />
			</p1:FormElement>
			<p1:FormElement ui:field="activeElement" labelText="{msg.getMeterModelActive}:" helpMsg="{msg.getMeterModelActiveHelp}">
				<g:CheckBox ui:field="activeBox" debugId="activeBox" />
			</p1:FormElement>
		</p1:FormRowPanel>
		
		<p1:FormRowPanel>
		    <p1:FormElement ui:field="manufacturerElement" debugId="manufacturerElement" labelText="{msg.getMeterModelsManufacturer}:" helpMsg="{msg.getMeterModelsManufacturerHelp}" required="true">
                <g:ListBox ui:field="manufacturerBox" debugId="manufacturerBox" title="{msg.getMeterModelsManufacturer}" styleName="gwt-TextBox" />
            </p1:FormElement>
            <p1:FormElement ui:field="serviceResourceElement" debugId="serviceResourceElement" labelText="{msg.getMeterModelsServiceResource}:" helpMsg="{msg.getMeterModelsServiceResourceHelp}" required="true">
                <g:ListBox ui:field="serviceResourceBox" debugId="serviceResourceBox" title="{msg.getMeterModelsServiceResource}" styleName="gwt-TextBox" />
            </p1:FormElement>
            <p1:FormElement ui:field="meterTypeElement" debugId="meterTypeElement" labelText="{msg.getMeterModelsMeterType}:" helpMsg="{msg.getMeterModelsMeterTypeHelp}" required="true">
                <g:ListBox ui:field="meterTypeBox" debugId="meterTypeBox" title="{msg.getMeterModelsMeterType}" styleName="gwt-TextBox" />
            </p1:FormElement>
            <p1:FormElement ui:field="meterPhaseElement" debugId="meterPhaseElement" labelText="{msg.getMeterSelectMeterPhase}:" helpMsg="{msg.getMeterSelectMeterPhaseHelp}">
                <g:ListBox ui:field="meterPhaseBox" debugId="meterPhaseBox" title="{msg.getMeterSelectMeterPhase}" styleName="gwt-TextBox" />
            </p1:FormElement>
        </p1:FormRowPanel>
        
        <p2:MridComponent ui:field="mridComponent"></p2:MridComponent>

        <p1:FormRowPanel>
            <p1:FormElement ui:field="paymentModesElement" debugId="paymentModesElement" labelText="{msg.getMeterModelsPaymentModes}:" helpMsg="{msg.getMeterModelsPaymentModesHelp}" required="true">
                <g:ListBox ui:field="paymentModesBox" debugId="paymentModesBox" title="{msg.getMeterModelsPaymentModes}" multipleSelect="true" visibleItemCount="5" />
            </p1:FormElement>
        </p1:FormRowPanel>

        <p1:FormRowPanel>
            <p1:FormElement ui:field="mdcElement" debugId="mdcElement" labelText="{msg.getMdc}:" helpMsg="{msg.getMdcHelp}" required="false">
                <g:ListBox ui:field="mdcBox" debugId="mdcBox" title="{msg.getMdc}" styleName="gwt-TextBox" />
            </p1:FormElement>
        </p1:FormRowPanel>

        <p1:FormRowPanel>
            <p1:FormElement>
                <p1:FormGroupPanel labelText="{msg.getMdc}" ui:field="mdcGroupPanel" debugId="mdcGroupPanel" visible="false">
                    <p1:FormRowPanel >
                    <p1:FormElement ui:field="balSyncElement" debugId="balSyncElement" labelText="{msg.getMeterModelBalanceSync}:" helpMsg="{msg.getMeterModelBalanceSyncHelp}">
                        <g:CheckBox ui:field="balSyncBox" debugId="balSyncBox" />
                    </p1:FormElement>

                    <p1:FormElement ui:field="needsBreakerIdElement" debugId="needsBreakerIdElement" labelText="{msg.getMeterModelNeedsBreakerId}:" helpMsg="{msg.getMeterModelNeedsBreakerIdHelp}">
                        <g:CheckBox ui:field="needsBreakerIdBox" debugId="needsBreakerIdBox" />
                    </p1:FormElement>
                    </p1:FormRowPanel>

                    <p1:FormRowPanel >
                    <p1:FormElement ui:field="messageDisplayElement" debugId="messageDisplayElement" labelText="{msg.getMeterModelMessageDisplay}:" helpMsg="{msg.getMeterModelMessageDisplayHelp}">
                        <g:CheckBox ui:field="messageDisplayBox" debugId="messageDisplayBox" />
                    </p1:FormElement>

                    <p1:FormElement ui:field="needsEncryptionKeyElement" debugId="needsEncryptionKeyElement" labelText="{msg.getMeterModelNeedsEncryptionKey}:" helpMsg="{msg.getMeterModelNeedsEncryptionKeyHelp}">
                        <g:CheckBox ui:field="needsEncryptionKeyBox" debugId="needsEncryptionKeyBox" />
                    </p1:FormElement>
                    </p1:FormRowPanel>

                    <p1:FormRowPanel >
                    <p1:FormElement ui:field="toaElement" debugId="toaElement" labelText="{msg.getMeterModelTOA}:" helpMsg="{msg.getMeterModelTOAHelp}">
                        <g:CheckBox ui:field="toaBox" debugId="toaBox" />
                    </p1:FormElement>

                    <p1:FormElement ui:field="uriPresentElement" debugId="uriPresentElement" labelText="{msg.getMeterModelUriPresent}:" helpMsg="{msg.getMeterModelUriPresentHelp}">
                        <g:CheckBox ui:field="uriPresentBox" debugId="uriPresentBox" />
                    </p1:FormElement>
                    </p1:FormRowPanel>

                    <p1:FormRowPanel>
                        <p1:FormElement>
                            <p1:FormGroupPanel labelText="{msg.getMeterModelBatteryEventLbl}" ui:field="batteryPanel" debugId="batteryPanel">
                                <p1:FormElement ui:field="batteryCapacityElement" debugId="batteryCapacityElement" labelText="{msg.getMeterModelBatteryCapacity}:" helpMsg="{msg.getMeterModelBatteryCapacityHelp}">
                                    <p1:BigDecimalValueBox ui:field="batteryCapacityTextBox" debugId="batteryCapacityTextBox" styleName="gwt-TextBox largeNumericInput" />
                                </p1:FormElement>
                                <p1:FormElement ui:field="lowThresholdPercentElement" debugId="lowThresholdPercentElement" labelText="Low Threshold:" helpMsg="{msg.getMeterModelBatteryThresholdHelp}">
                                    <w:PercentageTextBox ui:field="lowThresholdPercentTextBox" debugId="lowThresholdPercentTextBox" />
                                </p1:FormElement>
                            </p1:FormGroupPanel>
                        </p1:FormElement>
                    </p1:FormRowPanel>

                    <p1:FormRowPanel >
                    <p1:FormElement ui:field="dataDecoderElement" debugId="dataDecoderElement" labelText="{msg.getMeterModelsDataDecoder}:" helpMsg="{msg.getMeterModelsDataDecoderHelp}">
                        <g:ListBox ui:field="dataDecoderBox" debugId="dataDecoderBox" title="{msg.getMeterModelsDataDecoder}" styleName="gwt-TextBox"/>
                    </p1:FormElement>
                    </p1:FormRowPanel>
                </p1:FormGroupPanel>
            </p1:FormElement>
        </p1:FormRowPanel>

        <g:Label ui:field="meterModelInUseLabel" debugId="meterModelInUseLabel" text="{msg.getMeterModelInUse}" styleName="warn" visible="false" width="400px" />

	</g:FlowPanel>

</ui:UiBinder>
