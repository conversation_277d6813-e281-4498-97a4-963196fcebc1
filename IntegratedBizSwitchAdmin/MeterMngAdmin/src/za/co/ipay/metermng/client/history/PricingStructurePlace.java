package za.co.ipay.metermng.client.history;

import com.google.gwt.place.shared.Place;
import com.google.gwt.place.shared.PlaceTokenizer;
import com.google.gwt.place.shared.Prefix;

public class PricingStructurePlace extends Place {
    
    private String pricingStructureId;
    
    public static final PricingStructurePlace ALL_PRICING_STRUCTURES_PLACE = new PricingStructurePlace("all");
    
    public PricingStructurePlace(String pricingStructureId) {
        this.pricingStructureId = pricingStructureId;
    }
    
    public String getPricingStructureId() {
        return pricingStructureId;
    }
    
    public boolean isAllPricingStructures() {
        return pricingStructureId.equalsIgnoreCase("all");
    }
    
    public static String toPlaceString(PricingStructurePlace p) {
        return "pricingStructure:"+p.getPricingStructureId();
    }

    @Prefix(value="pricingStructure")
    public static class Tokenizer implements PlaceTokenizer<PricingStructurePlace> {
        @Override
        public String getToken(PricingStructurePlace place) {
            return place.getPricingStructureId();
        }

        @Override
        public PricingStructurePlace getPlace(String pricingStructureId) {
            return new PricingStructurePlace(pricingStructureId);
        }

    }

}
