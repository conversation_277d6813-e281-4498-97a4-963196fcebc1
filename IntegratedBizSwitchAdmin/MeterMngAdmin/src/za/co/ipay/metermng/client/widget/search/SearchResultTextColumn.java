package za.co.ipay.metermng.client.widget.search;

import java.util.logging.Logger;

import com.google.gwt.cell.client.Cell.Context;
import com.google.gwt.user.cellview.client.Column;

import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.shared.dto.search.SearchResultData;
import za.co.ipay.metermng.shared.dto.search.SearchResultType;

/**
 * SearchResultTextColumn displays a SearchResultData's detail as text which can be click-able to view the detail in
 * its own screen, if applicable.
 * <AUTHOR>
 */
public class SearchResultTextColumn extends Column<SearchResultData, SearchResultData> {

    /** What kind of data the column is displaying, eg: a meter, a customer, etc. */
    private SearchResultType columnType;
    /** The current token for the screen where we are now. */
    private String currentToken;
    private ClientFactory clientFactory;
    
    public SearchResultTextColumn(ClientFactory clientFactory, String currentToken, SearchResultType columnType, String detailKey) {
        super(new SearchResultClickableCell(clientFactory, currentToken, columnType, detailKey));
        this.columnType = columnType;
        this.clientFactory = clientFactory;
        this.currentToken = currentToken;
    }
    
    public SearchResultTextColumn(ClientFactory clientFactory, String currentToken, SearchResultType columnType, String[] detailKeys) {
        super(new SearchResultClickableCellComposite(clientFactory, currentToken, columnType, detailKeys));
        this.columnType = columnType;
        this.clientFactory = clientFactory;
        this.currentToken = currentToken;
    }
    
    @Override
    public SearchResultData getValue(SearchResultData data) {
        return data;
    }

    @Override
    public String getCellStyleNames(Context context, SearchResultData data) {
        String style = super.getCellStyleNames(context, data);
        if (style == null) {
            style = "";
        }
        //If we can link and show the data in its own screen, make the text look like a link for clicking, otherwise
        //just show plain text
        String link = SearchResultData.getHistoryToken(clientFactory, columnType, data, currentToken);

        if (link != null) {
            style += " searchResultLink";
        }
        return style;
    }
}
