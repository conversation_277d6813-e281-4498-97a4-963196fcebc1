package za.co.ipay.metermng.client.event;

import com.google.gwt.event.shared.GwtEvent;

public class OpenAuxTypeEvent extends GwtEvent<OpenAuxTypeEventHandler> {

    public static Type<OpenAuxTypeEventHandler> TYPE = new Type<OpenAuxTypeEventHandler>();
    
    @Override
    public Type<OpenAuxTypeEventHandler> getAssociatedType() {
        return TYPE;
    }

    @Override
    protected void dispatch(OpenAuxTypeEventHandler handler) {
        handler.openAuxType(this);
    }

}