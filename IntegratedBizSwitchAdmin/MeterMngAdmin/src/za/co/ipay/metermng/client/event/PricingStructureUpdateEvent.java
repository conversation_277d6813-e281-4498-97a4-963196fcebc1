package za.co.ipay.metermng.client.event;

import com.google.gwt.event.shared.GwtEvent;
import za.co.ipay.metermng.mybatis.generated.model.AppSetting;

public class PricingStructureUpdateEvent extends GwtEvent<PricingStructureUpdateEventHandler> {

    private final boolean listUpdated;
    private final AppSetting appSettingUpdate;

    public static Type<PricingStructureUpdateEventHandler> TYPE = new Type<PricingStructureUpdateEventHandler>();

    public PricingStructureUpdateEvent(boolean listUpdated) {
        this.listUpdated = listUpdated;
        this.appSettingUpdate = null;
    }

    public PricingStructureUpdateEvent(AppSetting appSettingUpdate) {
        this.appSettingUpdate = appSettingUpdate;
        this.listUpdated = false;
    }
    
	@Override
    public Type<PricingStructureUpdateEventHandler> getAssociatedType() {
        return TYPE;
    }

    @Override
    protected void dispatch(PricingStructureUpdateEventHandler handler) {
        handler.processPricingStructureUpdatedEvent(this);
    }

    public boolean isListUpdated() {
        return listUpdated;
    }

    public AppSetting getAppSettingUpdate() {
        return appSettingUpdate;
    }

}
