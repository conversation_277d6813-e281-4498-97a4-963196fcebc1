package za.co.ipay.metermng.client.view.workspace.group.all;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Iterator;
import java.util.List;
import java.util.logging.Logger;

import com.google.gwt.core.client.GWT;
import com.google.gwt.core.client.Scheduler;
import com.google.gwt.core.client.Scheduler.ScheduledCommand;
import com.google.gwt.dom.client.Element;
import com.google.gwt.event.dom.client.ClickEvent;
import com.google.gwt.event.dom.client.ClickHandler;
import com.google.gwt.event.dom.client.FocusEvent;
import com.google.gwt.event.dom.client.FocusHandler;
import com.google.gwt.event.logical.shared.SelectionEvent;
import com.google.gwt.event.logical.shared.SelectionHandler;
import com.google.gwt.place.shared.Place;
import com.google.gwt.regexp.shared.RegExp;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiFactory;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.user.cellview.client.CellTree;
import com.google.gwt.user.cellview.client.HasKeyboardSelectionPolicy.KeyboardSelectionPolicy;
import com.google.gwt.user.client.ui.Button;
import com.google.gwt.user.client.ui.DeckLayoutPanel;
import com.google.gwt.user.client.ui.HorizontalPanel;
import com.google.gwt.user.client.ui.Label;
import com.google.gwt.user.client.ui.PopupPanel;
import com.google.gwt.user.client.ui.SuggestBox;
import com.google.gwt.user.client.ui.SuggestOracle;
import com.google.gwt.user.client.ui.SuggestOracle.Suggestion;
import com.google.gwt.user.client.ui.Widget;
import com.google.gwt.view.client.SelectionChangeEvent;
import za.co.ipay.accesscontrol.domain.Group;
import za.co.ipay.gwt.common.client.Dialogs;
import za.co.ipay.gwt.common.client.form.FormElement;
import za.co.ipay.gwt.common.client.form.FormRowPanel;
import za.co.ipay.gwt.common.client.form.HasDirtyDataManager;
import za.co.ipay.gwt.common.client.handler.ConfirmHandler;
import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.gwt.common.client.workspace.SessionCheckCallback;
import za.co.ipay.gwt.common.client.workspace.SimpleFormView;
import za.co.ipay.gwt.common.client.workspace.SimpleTreeView;
import za.co.ipay.gwt.common.client.workspace.TreeFactory;
import za.co.ipay.gwt.common.client.workspace.WorkspaceNotification;
import za.co.ipay.gwt.common.client.workspace.WorkspaceNotification.NotificationType;
import za.co.ipay.gwt.common.shared.exception.AccessControlException;
import za.co.ipay.metermng.client.event.UpdateUsagePointGroupsListEvent;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.history.GroupPlace;
import za.co.ipay.metermng.client.i18n.UiMessagesUtil;
import za.co.ipay.metermng.client.resource.image.MediaResource.MediaResourceUtil;
import za.co.ipay.metermng.client.rpc.callback.ClientCallback;
import za.co.ipay.metermng.client.view.component.group.GenGroupParentComponent;
import za.co.ipay.metermng.client.view.component.group.entity.EntityView;
import za.co.ipay.metermng.client.view.workspace.BaseWorkspace;
import za.co.ipay.metermng.client.widget.tree.AddGroupNodePanel;
import za.co.ipay.metermng.client.widget.tree.GroupDataTreeHandler;
import za.co.ipay.metermng.client.widget.tree.GroupDataTreeModel;
import za.co.ipay.metermng.client.widget.tree.MeterMngTreeFactory;
import za.co.ipay.metermng.datatypes.RecordStatus;
import za.co.ipay.metermng.mybatis.generated.model.GroupType;
import za.co.ipay.metermng.shared.GenGroupData;
import za.co.ipay.metermng.shared.GroupHierarchyData;
import za.co.ipay.metermng.shared.MeterMngStatics;
import za.co.ipay.metermng.shared.dto.user.MeterMngUserGroup;
import za.co.ipay.metermng.shared.group.GroupTypeData;

/**
 * GenGroupWorkspaceView is used to display groups in a tree, using their corresponding group hierarchies, and allow
 * adding, updating and deleting of groups, with some restricting depending upon which group type or group types are
 * being displayed.
 *
 * NoTe: RC: this class has been duplicated as UpGenGroupTreePanel for use in popup scenarios.
 *           Any major functional work here - check that one as well.
 *
 * <AUTHOR>
 */

public class GenGroupWorkspaceView extends BaseWorkspace implements GenGroupParentComponent, GroupDataTreeHandler {

    /** Whether any group types can be selected or if the group type is set to a fixed single selection only. */
    private boolean singleSelection = false;
    /** The current selection group type which could be all, location, access, etc. */
    private String selectionType;

    @UiField DeckLayoutPanel mainLayoutPanel;
    @UiField SimpleTreeView<GenGroupData> view;
    private GroupDataTreeModel model;
    private GroupTypeSelection groupTypeSelection;
    @UiField SimpleFormView view2;
    private EntityView entityView;
    private SuggestBox filterBox;
    private HorizontalPanel searchPanel;

    /** The current group hierarchy for the selected group type. */
    private ArrayList<GroupHierarchyData> groupHierarchies = new ArrayList<GroupHierarchyData>();
    /** This stores the current last or deepest level in the current group hierarchy. It is used to know whether a leaf
     * in the tree needs to have add child menu item or not if they are the deepest in the hierarchy. */
    private int lastLevel = 0;
    private GroupHierarchyData currentHierarchy;
    private ArrayList<Long> lastGenGroupPath;
    /** The left co-ordinate for the add group pop up window. */
    private int mouseLeft;
    /** The top co-ordinate for the add group pop up window. */
    private int mouseTop;

    /** The current user with their access control group and group path. */
    private MeterMngUserGroup currentUser;

    private static Logger logger = Logger.getLogger(GenGroupWorkspaceView.class.getName());
    final RegExp pattern = RegExp.compile("^usagepointgroup\\.[a-zA-Z_]+(\\d{1,})\\.(status|label|datatype)$");

    private static UsagePointWorkspaceViewUiBinder uiBinder = GWT.create(UsagePointWorkspaceViewUiBinder.class);
    private FormElement searchElement;

    interface UsagePointWorkspaceViewUiBinder extends UiBinder<Widget, GenGroupWorkspaceView> {
    }

    public GenGroupWorkspaceView(final ClientFactory clientFactory, GroupPlace place) {
        this.clientFactory = clientFactory;
        if (MeterMngStatics.ACCESS_GROUP_TYPE.equals(place.getGroupType())) {
            this.singleSelection = true;
            this.selectionType = MeterMngStatics.ACCESS_GROUP_TYPE;
        } else {
            this.selectionType = place.getGroupType();
        }
        if (singleSelection) {
            //panel with only instructions as the single group type will be visible as the tree's root
            this.groupTypeSelection = new GroupTypeSingleSelectionPanel(this, selectionType);
        } else {
            //selection panel with drop-down of group types
            this.groupTypeSelection = new GroupTypeSelectionPanel(this, selectionType);
        }
        initWidget(uiBinder.createAndBindUi(this));
        initUi();
        setPlaceString(GroupPlace.getPlaceAsString(place));
        setHeaders();
    }

    @UiFactory
    public TreeFactory getTreeFactory() {
        model = new GroupDataTreeModel(clientFactory, this);
        return new MeterMngTreeFactory(model, clientFactory);
    }

    private void initUi() {
        this.lastGenGroupPath = new ArrayList<Long>();
        initForms();
        buildFilterBox();

        searchPanel = new HorizontalPanel();
        searchPanel.setSpacing(3);
        Label searchLabel = new Label(MessagesUtil.getInstance().getMessage("grouptree.search"));
        searchLabel.setStylePrimaryName("gwt-Label-bold");
        FormRowPanel formRowPanel = new FormRowPanel();
        searchElement = new FormElement();
        searchElement.add(filterBox);
        searchElement.setLabelText(MessagesUtil.getInstance().getMessage("grouptree.search"));
        searchElement.setHelpMsg(MessagesUtil.getInstance().getMessage("grouptree.search.help"));
        formRowPanel.add(searchElement);
        FormElement btnElement = new FormElement();
        Button clearSearchBtn = new Button(MessagesUtil.getInstance().getMessage("button.clear"));
        clearSearchBtn.addClickHandler(new ClickHandler() {
            @Override
            public void onClick(ClickEvent event) {
                // clear the search box
                filterBox.setValue("");
                lastGenGroupPath.clear();
                recreateTree();
            }

        });
        btnElement.add(clearSearchBtn);
        formRowPanel.add(btnElement);
        searchPanel.add(formRowPanel);
        view.getExtraPanel().add(searchPanel);
        if (groupTypeSelection.getGroupTypeId()==null) {
            searchPanel.setVisible(false);
        }
        addSelectionChangeHandlerToModel();

        entityView = new EntityView(clientFactory, this, view2);
        mainLayoutPanel.showWidget(view);
        Scheduler.get().scheduleDeferred(new ScheduledCommand() {

            @Override
            public void execute() {
                // This is a workaround for a weird timing issue
                // Everything works fine when opening from the menu, but when doing a browser refresh
                // while the page is open it doesn't show the group types: None label and shows a
                // ShowMore link, it then is not fixed when one selects a group type
                // I suspect there is a weird timing issue with the tree not attached maybe
                // but trying to find the cause gave me no joy
                recreateTree();
            }
        });
    }

    private void buildFilterBox() {
        filterBox = new SuggestBox(model.getGroupSuggestOracle());
        filterBox.addSelectionHandler(new SelectionHandler<SuggestOracle.Suggestion>() {
            @Override
            public void onSelection(SelectionEvent<Suggestion> event) {
                if (event.getSelectedItem() instanceof GenGroupData) {
                    GenGroupData selected = (GenGroupData) event.getSelectedItem();
                    saveLastGenGroupPath(selected);
                    filterGroup(selected);
                }
            }
        });
        filterBox.getValueBox().addFocusHandler(new FocusHandler() {
            @Override
            public void onFocus(FocusEvent event) {
                filterBox.showSuggestionList();
            }
        });
    }

    private void addSelectionChangeHandlerToModel() {
        // When clicking "show more" the screen jumps focus to the selected item
        // and for some reason the tree keeps selecting something even if I try
        // unselecting. I found that disabling the keyboard selection policy
        // makes the issue disappear. I also tried scrollIntoView on the last
        // element displayed when clicking showMore, but then it jumps up and
        // back down again creating a visual disturbance.
        view.getTree().setKeyboardSelectionPolicy(KeyboardSelectionPolicy.DISABLED);
        model.getSingleSelectionModel().addSelectionChangeHandler(new SelectionChangeEvent.Handler() {
            @Override
            public void onSelectionChange(SelectionChangeEvent event) {
                GenGroupData selectedGroup = model.getSingleSelectionModel().getSelectedObject();
                logger.info("onSelectionChange: selected: " + selectedGroup + ",filterBox.text: " + filterBox.getText());
                if(selectedGroup == null) {
                    return;
                }
                // This following line breaks addition of nodes for all but top level
                // Normally not good to leave commented code, but leaving this for the
                // sake of warning that small changes can break stuff here, and this can
                // be a basis for finding the root issue potentially.
                // model.nodeFlush(getTree().getRootTreeNode(), selectedGroup.getId());
            }
        });
    }

    private void recreateTree() {
        List<GenGroupData> sourceGroupdata = model.getSourceGroupdata();
        this.recreateTree(sourceGroupdata);
    }

    private void recreateTree(List<GenGroupData> sourceGroupdata) {
        // After adding "show more" searching and selecting an item
        // was no longer straightforward, so now filtering sets the
        // visible range to 1 for the searched element and the same
        // is applied when adding items cos they could be outside the
        // show more range.
        // There were numerous side effects on doing that like duplicate
        // items showing up which was only resolved by completely
        // recreating the tree (where previously only the data was
        // replaced for example when changing the group type
        // ideally this approach was more structurally visible on the
        // SimpleTreeView and filter box and also in the order of events
        // which is a little async in terms of when the factory/model
        // creation happens and all dependencies resolved. For example
        // when changing the model on the tree the filter box is out
        // out of date because it had an old model passed to it.
        // There is also too much duplication with the UpGenGroupTreePanel
        // but alas this is the best I could do in the time allowed.

        // tree factory recreates the model
        view.replaceTree(getTreeFactory());
        // set the original data on the model
        model.setData(sourceGroupdata);
        searchElement.remove(filterBox);
        buildFilterBox();
        searchElement.add(filterBox);
        addSelectionChangeHandlerToModel();
        // When saving new, for some reason this next call flushes pending UI stuff
        // without it when range is set in filterGroup shows the current selection
        // as the first element in the tree, but then a complete copy of the unfiltered
        // after it
        view.clearTreeSelection();
    }

    private void initForms() {
        groupTypeSelection.setForm(view.getTreeForm());
        if (singleSelection) {
            if (MeterMngStatics.ACCESS_GROUP_TYPE.equals(selectionType)) {
                view.getTreeForm().getFormPanel().setHeadingText(MessagesUtil.getInstance().getMessage("accessgroups.title.current"), "pageSectionTitle");
            }
        } else {
            if (MeterMngStatics.LOCATION_GROUP_TYPE.equals(selectionType)) {
                view.getTreeForm().getFormPanel().setHeadingText(MessagesUtil.getInstance().getMessage("locationgroups.title.current"), "pageSectionTitle");
            } else {
                view.getTreeForm().getFormPanel().setHeadingText(MessagesUtil.getInstance().getMessage("usagepointgroups.title"), "pageSectionTitle");
            }
        }
        view.getTreeForm().getFormFields().add(groupTypeSelection);
        view.getTreeForm().getButtons().setVisible(false);
        view.getForm().setVisible(false);
    }


    private void setHeaders() {
        if (singleSelection) {
            if (MeterMngStatics.ACCESS_GROUP_TYPE.equals(selectionType)) {
                setHeaderText(MessagesUtil.getInstance().getMessage("accessgroups.title"));
                view.setPageHeader(MessagesUtil.getInstance().getMessage("accessgroups.title"));
            }
        } else {
            if (MeterMngStatics.LOCATION_GROUP_TYPE.equals(selectionType)) {
                setHeaderText(MessagesUtil.getInstance().getMessage("locationgroups.title"));
                view.setPageHeader(MessagesUtil.getInstance().getMessage("locationgroups.title"));
            } else {
                setHeaderText(MessagesUtil.getInstance().getMessage("usagepointgroup.title"));
            }
        }
    }

    public void viewGroupEntity(final GenGroupData group) {
        final SessionCheckCallback callback = new SessionCheckCallback() {
            @Override
            public void callback(SessionCheckResolution resolution) {
                entityView.setGenGroup(group);
                mainLayoutPanel.showWidget(view2);
                mainLayoutPanel.animate(getAnimationTime());
            }
        };
        clientFactory.handleSessionCheckCallback(callback);
    }

    @Override
    public void setPosition(Element element) {
        if (element != null) {
            this.mouseLeft = element.getAbsoluteLeft();
            this.mouseTop = element.getAbsoluteTop();
        }
    }

    @Override
    public void onArrival(Place place) {
        if (groupTypeSelection.getGroupTypeId() == null) {
            loadRequiredGroupTypes();
        }
    }

    private void loadRequiredGroupTypes() {
        if (singleSelection) {
            if (MeterMngStatics.ACCESS_GROUP_TYPE.equals(selectionType)) {
                loadUserAssignedGroupPath();
            }
        } else {
            loadGroupTypes();
        }
    }

    private void loadUserAssignedGroupPath() {
        clientFactory.getUserRpc().getCurrentUserWithGroup(new ClientCallback<MeterMngUserGroup>() {
            @Override
            public void onSuccess(MeterMngUserGroup user) {
                currentUser = user;
                logger.info("Set currentUser: "+currentUser.getUserName());
                lastGenGroupPath = new ArrayList<Long>(user.getGroupPath());
                loadAccessGroupType();
            }
        });
    }

    private void loadAccessGroupType() {
        clientFactory.getGroupRpc().getAccessGroupType(new ClientCallback<GroupType>() {
            @Override
            public void onSuccess(GroupType groupType) {
                logger.fine("Got groupType: " + groupType);
                ((GroupTypeSingleSelectionPanel) groupTypeSelection).setGroupType(new GroupTypeData(groupType));
            }
        });
    }

    private void loadGroupTypes() {
        logger.info("Loading group types...");
        clientFactory.getGroupRpc().getGroupTypesWithHierarchy(new ClientCallback<ArrayList<GroupTypeData>>() {
            @Override
            public void onSuccess(ArrayList<GroupTypeData> groupTypes) {
                logger.fine("Got groupTypes: " + groupTypes.size());
                boolean displayAccessGroup = false;
                boolean displayLocationGroup = false;
                if (singleSelection) {
                    if (MeterMngStatics.ACCESS_GROUP_TYPE.equals(selectionType)) {
                        displayAccessGroup = true;
                    }
                } else if (MeterMngStatics.LOCATION_GROUP_TYPE.equals(selectionType)) {
                    displayLocationGroup = true;
                }
                ((GroupTypeSelectionPanel) groupTypeSelection).setGroupTypes(groupTypes, displayAccessGroup, displayLocationGroup);
            }

            @Override
            public void onFailure(Throwable caught) {
                if (caught instanceof AccessControlException) {
                    clientFactory.getWorkspaceContainer().closeWorkspaceNow(GroupPlace.USAGE_POINT_GROUPS_PLACE);
                }
                super.onFailure(caught);
            }
        });
    }

    @Override
    public void clearGroupHierarchies() {
        this.groupHierarchies = new ArrayList<GroupHierarchyData>();
        this.lastLevel = 0;
    }

    private void setGroupHierarchies(ArrayList<GroupHierarchyData> data) {
        this.groupHierarchies = data;
        if (groupHierarchies.size() > 0) {
            this.lastLevel = groupHierarchies.get(groupHierarchies.size() - 1).getLevel();
        } else {
            this.lastLevel = 0;
        }
        logger.info("Set group hierarchies: lastLevel:"+lastLevel);
    }

    @Override
    public void clearGroups() {
        searchPanel.setVisible(false);
        model.setData(new ArrayList<GenGroupData>());
    }

    private void clearWorkspace() {
        clearGroupHierarchies();
        clearGroups();
        updateRoot();
    }

    @Override
    public void loadGroupHierarchies(final Long groupTypeId) {
        if (groupTypeId != null) {
            SessionCheckCallback sessionCheckCallback = new SessionCheckCallback() {
                @Override
                public void callback(SessionCheckResolution resolution) {
                    clientFactory.getGroupRpc().getGroupHierarchies(groupTypeId, new ClientCallback<ArrayList<GroupHierarchyData>>() {
                        @Override
                        public void onSuccess(ArrayList<GroupHierarchyData> data) {
                            setGroupHierarchies(data);
                        }
                    });
                }
            };
            clientFactory.handleSessionCheckCallback(sessionCheckCallback);
        }
    }

    @Override
    public void updateRoot() {
        model.updateRoot();
    }

    @Override
    public void reLoadGroups(GenGroupData genGroup) {
        logger.info("RELOAD GROUPS");
        //store current path
        saveLastGenGroupPath(genGroup);

        loadGroups(groupTypeSelection.getGroupTypeId());
    }

    @Override
    public void loadGroups(final Long groupTypeId) {
        filterBox.setText("");
        searchPanel.setVisible(true);
        if (groupTypeId != null) {
            SessionCheckCallback sessionCheckCallback = new SessionCheckCallback() {
                @Override
                public void callback(SessionCheckResolution resolution) {
                    clientFactory.getGroupRpc().getGroups(groupTypeId, new ClientCallback<ArrayList<GenGroupData>>() {
                        @Override
                        public void onSuccess(ArrayList<GenGroupData> result) {
                            recreateTree(result);
                        }
                    });
                }
            };
            clientFactory.handleSessionCheckCallback(sessionCheckCallback);
        } else {
            Dialogs.displayErrorMessage(MessagesUtil.getInstance().getMessage("usagepointgroup.noselection.grouptype"),
                                        MediaResourceUtil.getInstance().getErrorIcon(),
                                        MessagesUtil.getInstance().getMessage("button.close"));
        }
    }

    @Override
    public void onLeaving() {

    }

    @Override
    public void onSelect() {

    }

    @Override
    public void onClose() {

    }

    @Override
    public boolean handles(Place place) {
        if (place instanceof GroupPlace) {
            GroupPlace groupPlace = (GroupPlace) place;
            logger.info("Checking groupPlace: "+groupPlace.getGroupType()+" singleSelection: "+singleSelection+" selectionType: "+selectionType);
            if (MeterMngStatics.USAGE_POINT_GROUP_TYPE.equals(((GroupPlace) place).getGroupType())
                    && MeterMngStatics.USAGE_POINT_GROUP_TYPE.equals(selectionType)
                    && !singleSelection) {
                return true;
            } else if (MeterMngStatics.LOCATION_GROUP_TYPE.equals(((GroupPlace) place).getGroupType())
                    && MeterMngStatics.LOCATION_GROUP_TYPE.equals(selectionType)
                    && !singleSelection) {
                return true;
            } else if (singleSelection
                    && MeterMngStatics.ACCESS_GROUP_TYPE.equals(((GroupPlace) place).getGroupType())
                    && MeterMngStatics.ACCESS_GROUP_TYPE.equals(selectionType)) {
                    return true;
            } else {
                return false;
            }
        } else {
            return false;
        }
    }

    @Override
    public void goBack() {
        checkAnyDirtyData(new ConfirmHandler() {

            @Override
            public void confirmed(boolean confirm) {
                if(confirm) {
                    clearAllHasDirtyData();
                    mainLayoutPanel.showWidget(view);
                    mainLayoutPanel.animate(getAnimationTime());
                }
            }
        });
    }

    @Override
    public GroupTypeData getSelectedGroupTypeData() {
        return groupTypeSelection.getGroupTypeData();
    }

    @Override
    public Long getSelectedGroupTypeId() {
        return groupTypeSelection.getGroupTypeId();
    }

    @Override
    public String getSelectedGroupTypeName() {
        return groupTypeSelection.getGroupTypeName();
    }

    @Override
    public void addNewGroup(final ClientFactory clientFactory, Long groupTypeId, GenGroupData parent, final GenGroupData current) {
        //Populate the new current node with its parent and groupHierarchy attributes
        logger.info("addNewGroup(): parent: " + parent);

        current.setName("New");
        current.setRecordStatus(RecordStatus.DAC);
        current.setNewEntry(true);

        if (parent != null) {
            current.setParentId(parent.getId());
            current.setParent(parent);
        }
        GroupHierarchyData parentHierarchy = getParentHierarchy(parent);
        if (parentHierarchy == null && current.getGroupHierarchyId() != null) {
            currentHierarchy = getCurrentHierarchy(current.getGroupHierarchyId());
        } else {
            currentHierarchy = getCurrentHierarchy(parentHierarchy);
        }
        if (currentHierarchy != null) {
            current.setGroupHierarchyId(currentHierarchy.getId());
        }

        logger.info("addNewGroup current:" + current);

        final StringBuilder theHeading = new StringBuilder();
        StringBuilder nameLabel = new StringBuilder();
        if (parent == null) {
            nameLabel.append(UiMessagesUtil.getInstance().getNewGroupInstructions()).append(" ").append(currentHierarchy.getName());
            GenGroupData root = model.getRoot();
            model.expandNode(view.getTree().getRootTreeNode(), root.getId());
        } else {
            nameLabel.append(UiMessagesUtil.getInstance().getNewGroupInstructions()).append(" ").append(currentHierarchy.getName())
                    .append(" ").append(UiMessagesUtil.getInstance().getNewGroupFor()).append(" ").append(parent.getName());
            model.expandNode(view.getTree().getRootTreeNode(), parent.getId());
        }
        SessionCheckCallback sessionCheckCallback = new SessionCheckCallback() {
            @Override
            public void callback(SessionCheckResolution resolution) {
                if (currentHierarchy.isAccessGroup()) {
                    clientFactory.getGroupRpc().getAvailableAccessGroupsByGroupHierarchy(currentHierarchy.getId(), new ClientCallback<List<Group>>() {

                        @Override
                        public void onSuccess(List<Group> result) {
                            showAddPopup(clientFactory, current, theHeading, result);
                        }
                    });
                } else {
                    showAddPopup(clientFactory, current, theHeading, null);
                }
            }
        };
        clientFactory.handleSessionCheckCallback(sessionCheckCallback);
    }

    private void showAddPopup(ClientFactory clientFactory, GenGroupData current, StringBuilder theHeading, List<Group> accessGroups) {
        PopupPanel simplePopup = new PopupPanel(true);
        AddGroupNodePanel addGroupNodePanel = new AddGroupNodePanel(clientFactory, current, theHeading.toString(), this, simplePopup, accessGroups);
        addGroupNodePanel.populateAddNewMrid();
        simplePopup.setGlassEnabled(true);
        simplePopup.setAutoHideEnabled(false);
        simplePopup.setAutoHideOnHistoryEventsEnabled(true);
        simplePopup.setAnimationEnabled(true);
        simplePopup.setWidget(addGroupNodePanel);
        simplePopup.setPopupPosition(mouseLeft, mouseTop);
        simplePopup.addStyleName(MeterMngStatics.MAIN_POPUP_STYLE);
        simplePopup.show();
        addGroupNodePanel.setFocusOnTextBox();
        if (isLowestLevel(current)) {
            current.setRecordStatus(RecordStatus.ACT);
        }
    }

    private GroupHierarchyData getParentHierarchy(GenGroupData parent) {
        if (parent != null) {
            logger.fine("Getting parent's hierarchy: " + parent.getGroupHierarchyId());
            for (GroupHierarchyData gh : groupHierarchies) {
                if (gh.getId().equals(parent.getGroupHierarchyId())) {
                    return gh;
                }
            }
        }
        return null;
    }

    private GroupHierarchyData getCurrentHierarchy(GroupHierarchyData parentHierarchy) {
        int level = 1;
        if (parentHierarchy != null) {
            level = parentHierarchy.getLevel() + 1;
        }
        for (GroupHierarchyData gh : groupHierarchies) {
            if (gh.getLevel().intValue() == level) {
                logger.fine("Found current hierarchy: " + gh);
                return gh;
            }
        }
        logger.severe("No matching groupHierarchy found for level: " + level);
        return null;
    }

    private GroupHierarchyData getCurrentHierarchy(Long id) {
        for (GroupHierarchyData gh : groupHierarchies) {
            if (gh.getId().equals(id)) {
                logger.fine("Found current hierarchy: " + gh);
                return gh;
            }
        }
        logger.severe("No matching groupHierarchy found for id: " + id);
        return null;
    }

    public static final class GroupNameComparator implements Comparator<GenGroupData> {
        @Override
        public int compare(GenGroupData o1, GenGroupData o2) {
            return o1.getName().compareTo(o2.getName());
        }
    }

    @Override
    public void saveGenGroup(final GenGroupData groupToSave) {
        if (groupToSave != null) {
            SessionCheckCallback sessionCheckCallback = new SessionCheckCallback() {
                @Override
                public void callback(SessionCheckResolution resolution) {
                    GroupHierarchyData currentHierarchy = getCurrentHierarchy(groupToSave.getGroupHierarchyId());
                    logger.info("Saving edited gengroup: " + groupToSave.getName() + " currentHierarchy: " + currentHierarchy);
                    boolean lastLevel = (GenGroupWorkspaceView.this.lastLevel == currentHierarchy.getLevel().intValue());
                    clientFactory.getGroupRpc().updateGenGroup(groupToSave, lastLevel, new ClientCallback<GenGroupData>() {
                        @Override
                        public void onSuccess(GenGroupData group) {
                            // Note this result is not fully populated and does not contain parent info
                            // therefore we set the id on the local object and continue working with it
                            boolean existingGroup = false;
                            if(groupToSave.getId() == null) {
                                groupToSave.setId(group.getId());
                            } else {
                                existingGroup = true;
                            }
                            // make sure both variables in sync to avoid errors further down
                            group = groupToSave;
                            logger.info("Successfully saved genGroup: " + group);

                            // Existing groups don't need to be added to the source data set
                            if(!existingGroup) {
                                // Need to add this as a child of the parent so that the selection
                                // code that displays children works
                                if(group.getParent() != null) {
                                    group.getParent().getChildren().add(group);
                                    Collections.sort(group.getParent().getChildren(), new GroupNameComparator());
                                } else {
                                    List<GenGroupData> sourceGroupdata = model.getSourceGroupdata();
                                    sourceGroupdata.add(group);
                                    Collections.sort(sourceGroupdata, new GroupNameComparator());
                                }
                                if(isLowestLevel(group)) {
                                    // We need to set the active status of the parents since
                                    // they would not have been active without a leaf node
                                    // This would have already been done on the
                                    // server, but to avoid a reload we copy the same change to
                                    // the locally cached data in the browser
                                    GenGroupData parent = group.getParent();
                                    while(parent != null) {
                                        parent.setRecordStatus(RecordStatus.ACT);
                                        parent = parent.getParent();
                                    }
                                }
                            }

                            saveLastGenGroupPath(group);
                            recreateTree();
                            filterGroup(group);
                            clientFactory.getEventBus().fireEvent(new UpdateUsagePointGroupsListEvent(groupTypeSelection.getGroupTypeId(),group.getId()));
                            notifyGroupsUpdated();
                        }
                    });
                }
            };
            clientFactory.handleSessionCheckCallback(sessionCheckCallback);
        }
    }

    private void notifyGroupsUpdated() {
        String dataType = MeterMngStatics.GROUPS;
        if (singleSelection) {
            if (MeterMngStatics.ACCESS_GROUP_TYPE.equals(selectionType)) {
                dataType = MeterMngStatics.ACCESS_GROUP_TYPE;
            }
        } else {
          if (MeterMngStatics.LOCATION_GROUP_TYPE.equals(selectionType)) {
              dataType = MeterMngStatics.LOCATION_GROUP_TYPE;
          }
        }
        clientFactory.getWorkspaceContainer().notifyWorkspaces(new WorkspaceNotification(NotificationType.DATA_UPDATED, dataType));
    }

    private void saveLastGenGroupPath(GenGroupData group) {
        lastGenGroupPath.clear();
        lastGenGroupPath.add(group.getId());
        GenGroupData current = group.getParent();
        while (current != null) {
            lastGenGroupPath.add(0, current.getId());
            current = current.getParent();
        }
        logger.info("Saved path: " + lastGenGroupPath);
    }

    private void saveParentLastGenGroupPath(GenGroupData parent) {
        lastGenGroupPath.clear();
        if (parent != null) {
            lastGenGroupPath.add(parent.getId());
            GenGroupData current = parent.getParent();
            while (current != null) {
                lastGenGroupPath.add(0, current.getId());
                current = current.getParent();
            }
            logger.info("Saved parent's path: " + lastGenGroupPath);
        } else {
            lastGenGroupPath.add(null);
        }
    }

    @Override
    public boolean isLowestLevel(GenGroupData data) {
        GroupHierarchyData hierarchy = null;
        for (GroupHierarchyData gh : groupHierarchies) {
            if (data != null && gh.getId().equals(data.getGroupHierarchyId())) {
                hierarchy = gh;
                break;
            }
        }
        if (hierarchy != null) {
            if (hierarchy.getLevel().intValue() == lastLevel) {
                return true;
            }
        }
        return false;
    }

    @Override
    public String getLevelName(GenGroupData data) {
        if (data == null) {
            return "";
        } else {
            for (GroupHierarchyData gh : groupHierarchies) {
                if (gh.getId().equals(data.getGroupHierarchyId())) {
                    return gh.getName();
                }
            }
        }
        logger.fine("No corresponding groupHierarchy found for node: " + data.getName() + " " + data.getGroupHierarchyId());
        return "";
    }

    @Override
    public void deleteGroup(final GenGroupData genGroupData) {
        logger.info("deleteGroup: " + genGroupData);
        SessionCheckCallback sessionCheckCallback = new SessionCheckCallback() {
            @Override
            public void callback(SessionCheckResolution resolution) {
                clientFactory.getGroupRpc().deleteGenGroup(genGroupData, new ClientCallback<Void>() {
                    @Override
                    public void onSuccess(Void result) {
                        logger.info("deleteGroup: success");
                        saveParentLastGenGroupPath(genGroupData.getParent());
                        List<GenGroupData> parentList;
                        if(genGroupData.getParent() == null) {
                            // is in the root node
                            logger.info("deleteGroup: is in root node");
                            parentList = model.getSourceGroupdata();
                        } else {
                            logger.info("deleteGroup: is in sub node");
                            parentList = genGroupData.getParent().getChildren();
                        }
                        if(parentList != null && ! parentList.isEmpty()) {
                            int i = 0;
                            for (Iterator<GenGroupData> iterator = parentList.iterator(); iterator.hasNext();) {
                                i++;
                                GenGroupData gg = (GenGroupData) iterator.next();
                                if(gg.getId().equals(genGroupData.getId())) {
                                    logger.info("deleteGroup: remove from source data: " + i);
                                    iterator.remove();
                                    break;
                                }
                            }
                        }

                        if(genGroupData.getRecordStatus() == RecordStatus.ACT) {
                            // We need to set the deactivate the parents that have no
                            // other children. This would have already been done on the
                            // server, but to avoid a reload we copy the same change to
                            // the locally cached data in the browser
                            GenGroupData parent = genGroupData.getParent();
                            while(parent != null) {
                                if(parent.getChildren() == null || parent.getChildren().isEmpty()) {
                                    // does not have children so it does not have leaf nodes
                                    parent.setRecordStatus(RecordStatus.DAC);
                                } else {
                                    // parent has other children but need to check if any go down to leaf node
                                    if(! hasLeafNodeChild(parent)) {
                                        parent.setRecordStatus(RecordStatus.DAC);
                                    }
                                }
                                parent = parent.getParent();
                            }
                        }

                        filterBox.setText(null);
                        recreateTree();
                        model.expandNode(getTree().getRootTreeNode(), genGroupData.getParentId());
                        view.setSelectedTreeData(genGroupData.getParent());

                        clientFactory.getEventBus().fireEvent(new UpdateUsagePointGroupsListEvent(groupTypeSelection.getGroupTypeId()));
                        notifyGroupsUpdated();
                    }
                });
            }
        };
        clientFactory.handleSessionCheckCallback(sessionCheckCallback);
    }

    private boolean hasLeafNodeChild(GenGroupData group) {
        // Not if this is a lead node itself it will return false
        if(group.getChildren() != null) {
            for (GenGroupData child : group.getChildren()) {
                if(isLowestLevel(child)) {
                    return true;
                } else {
                    if(hasLeafNodeChild(child)) {
                        return true;
                    }
                }
            }
        }
        return false;
    }

    @Override
    public CellTree getTree() {
        return view.getTree();
    }

    @Override
    public ArrayList<Long> getLastGenGroupPath() {
        return lastGenGroupPath;
    }

    @Override
    public void setPageFocus() {
        groupTypeSelection.setFocus();
    }

    @Override
    public boolean isSingleSelection() {
        return singleSelection;
    }

    @Override
    public Long getAssignedId() {
        if (currentUser != null && currentUser.getAssignedGroup() != null) {
            return currentUser.getAssignedGroup().getId();
        } else {
            return null;
        }
    }

    @Override
    public ArrayList<Long> getAssignedPath() {
        if (currentUser != null && currentUser.getGroupPath() != null) {
            return currentUser.getGroupPath();
        } else {
            return new ArrayList<Long>();
        }
    }

    @Override
    public boolean isGroupViewOnly(GenGroupData group) {
        return model.isGroupViewOnly(group);
    }


    public String getSelectionType() {
        return selectionType;
    }

    @Override
    public void handleNotification(WorkspaceNotification notification) {
        logger.info("Received notification: "+notification);
        if ((MeterMngStatics.GROUP_TYPE.equals(notification.getDataType()) || MeterMngStatics.GROUPSEDITEDXTREEPOPUP.equals(notification.getDataType()))
                && NotificationType.DATA_UPDATED == notification.getNotificationType()) {
            if (!singleSelection) {
                logger.info("The group types have changed - reloading this GenGroupWorkspace...");
                ((GroupTypeSelectionPanel) groupTypeSelection).clearFields();
                clearWorkspace();
                SessionCheckCallback sessionCheckCallback = new SessionCheckCallback() {
                    @Override
                    public void callback(SessionCheckResolution resolution) {
                        loadRequiredGroupTypes();
                    }
                };
                clientFactory.handleSessionCheckCallback(sessionCheckCallback);
            } else {
                logger.info("The group types have changed - closing the single selection GenGroupWorkspace...");
                clientFactory.getWorkspaceContainer().closeWorkspaceNow(this);
            }
        } else if (MeterMngStatics.APPSETTINGS_MODIFIED.equals(notification.getDataType())
                && NotificationType.DATA_UPDATED == notification.getNotificationType()) {
            if (!((String)notification.getObject()).toLowerCase().contains(".custom_")) {
                entityView.refreshGlobalThresholds();
            } else if(pattern.test((String)notification.getObject())) {
                entityView.refreshCustomComponents();
            }
        }
    }

    @Override
    public HasDirtyDataManager getHasDirtyDataManager() {
        return this;
    }

    @Override
    public void openGenGroupEditPanel(GenGroupData current) {
        String nameLabel = UiMessagesUtil.getInstance().getGroupEdit()+" "+ current.getDisplayString();
        PopupPanel simplePopup = new PopupPanel(true);
        AddGroupNodePanel addGroupNodePanel = new AddGroupNodePanel(clientFactory, current, nameLabel, this, simplePopup, null);
        addGroupNodePanel.populateEditInputs(current);
        simplePopup.setGlassEnabled(true);
        simplePopup.setAutoHideEnabled(false);
        simplePopup.setAutoHideOnHistoryEventsEnabled(true);
        simplePopup.setAnimationEnabled(true);
        simplePopup.setWidget(addGroupNodePanel);
        simplePopup.setPopupPosition(mouseLeft, mouseTop);
        simplePopup.addStyleName(MeterMngStatics.MAIN_POPUP_STYLE);
        simplePopup.show();
        addGroupNodePanel.setFocusOnTextBox();
    }

    private void filterGroup(GenGroupData selected) {
        logger.info("filterGroup: " + selected);
        filterBox.setText(selected.getName());
        ArrayList<GenGroupData> parentGroups = new ArrayList<GenGroupData>();
        ArrayList<Long> parentGroupIds = new ArrayList<Long>();
        GenGroupData current = selected.getParent();
        while (current != null) {
            parentGroups.add(0, current);
            parentGroupIds.add(0, current.getId());
            current = current.getParent();
        }
        model.expandNode(view.getTree().getRootTreeNode(), null);
        for (GenGroupData genGroupData : parentGroups) {
            model.setVisibleRangeForGroup(genGroupData, 1, true);
        }
        model.setVisibleRangeForGroup(selected, 1, false);
        model.getSingleSelectionModel().setSelected(selected, true);
    }

}
