<!DOCTYPE ui:UiBinder SYSTEM "http://dl.google.com/gwt/DTD/xhtml.ent">
<ui:UiBinder xmlns:ui="urn:ui:com.google.gwt.uibinder"
	         xmlns:g="urn:import:com.google.gwt.user.client.ui" 
             xmlns:p1="urn:import:za.co.ipay.metermng.client.view.component" 
             xmlns:p2="urn:import:com.google.gwt.user.cellview.client"
             xmlns:p3="urn:import:za.co.ipay.gwt.common.client.form" 
             xmlns:p4="urn:import:za.co.ipay.metermng.client.view.component.tariff"
             xmlns:p5="urn:import:za.co.ipay.gwt.common.client.widgets"
             xmlns:p6="urn:import:za.co.ipay.gwt.common.client.workspace">
	<ui:style>
	</ui:style>
  
    <ui:with field="msg" type="za.co.ipay.metermng.client.i18n.UiMessages" />
  
    <g:DeckLayoutPanel height="100%" width="100%" animationVertical="false" ui:field="deckPanel">
    
        <!-- 1st deck -->
        <p6:SimpleTableView ui:field="view" header="{msg.getPricingStructuresHeader}" title="{msg.getPricingStructuresTitle}" />
      
        <!-- 2nd deck -->
        <p6:SimpleTableView ui:field="view2" header="{msg.getTariffsHeader}" title="{msg.getTariffsTitle}" />
    
   </g:DeckLayoutPanel>
</ui:UiBinder> 