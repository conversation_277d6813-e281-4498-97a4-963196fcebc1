package za.co.ipay.metermng.client.view.workspace.blockingtype;

import java.math.BigDecimal;

import com.google.gwt.core.client.GWT;
import com.google.gwt.event.dom.client.ClickEvent;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.uibinder.client.UiHandler;
import com.google.gwt.user.client.ui.CheckBox;
import com.google.gwt.user.client.ui.IntegerBox;
import com.google.gwt.user.client.ui.Label;
import com.google.gwt.user.client.ui.TextArea;
import com.google.gwt.user.client.ui.TextBox;
import com.google.gwt.user.client.ui.Widget;

import za.co.ipay.gwt.common.client.form.BigDecimalValueBox;
import za.co.ipay.gwt.common.client.form.FormElement;
import za.co.ipay.gwt.common.client.form.SimpleForm;
import za.co.ipay.gwt.common.client.handler.FormDataChangeHandler;
import za.co.ipay.gwt.common.client.handler.FormDataClickHandler;
import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.gwt.common.client.widgets.CurrencyTextBox;
import za.co.ipay.metermng.client.form.SimpleFormPanel;

public class BlockingTypePanel extends SimpleFormPanel {

    @UiField FormElement typeNameElement;
    @UiField FormElement unitsPerDayElement;
    @UiField FormElement maxAmountPerDayElement;
    @UiField FormElement numVendsElement;
    @UiField FormElement maxAmountElement;
    @UiField FormElement completeElement;
    @UiField FormElement messageElement;

    @UiField TextBox typeNameTextBox;
    @UiField BigDecimalValueBox unitsPerDayBox;
    @UiField CurrencyTextBox maxAmountPerDayBox;
    @UiField IntegerBox numVendsBox;
    @UiField CurrencyTextBox maxAmountTextBox;
    @UiField CheckBox completeCheckBox;
    @UiField TextArea messageTextArea;
    @UiField Label error;

    private static final String REASON_FIXED_MSG_TEMPLATE = "{reason_fixed}";
    private static final String REASON_UNITS_MSG_TEMPLATE = "{max_units_per_day}";
    private static final String REASON_DAILY_AMT_MSG_TEMPLATE = "{max_amount_per_day}";
    private static final String REASON_VENDS_MSG_TEMPLATE = "{remaining_vends}";
    private static final String REASON_AMT_MSG_TEMPLATE = "{remaining_amount}";

    private static AuxChargeStructurePanelUiBinder uiBinder = GWT.create(AuxChargeStructurePanelUiBinder.class);

    interface AuxChargeStructurePanelUiBinder extends UiBinder<Widget, BlockingTypePanel> {
    }

    public BlockingTypePanel(SimpleForm form) {
        super(form);
        initWidget(uiBinder.createAndBindUi(this));
        addFieldHandlers();
    }

    public void clearErrors() {
        typeNameElement.clearErrorMsg();
        unitsPerDayElement.clearErrorMsg();
        numVendsElement.clearErrorMsg();
        maxAmountElement.clearErrorMsg();
        maxAmountPerDayElement.clearErrorMsg();
        completeElement.clearErrorMsg();
        messageElement.clearErrorMsg();
        error.setText("");
        error.setVisible(false);
    }

    public void clearFields() {
        form.setDirtyData(false);

        typeNameTextBox.setText("");
        unitsPerDayBox.setValue(null);
        maxAmountPerDayBox.setAmount(null);
        numVendsBox.setValue(null);
        maxAmountTextBox.setAmount(null);
        completeCheckBox.setValue(false);
        messageTextArea.setText("");
    }

    public boolean isValid() {
        boolean valid = true;
        String typeName = typeNameTextBox.getText();
        String message = messageTextArea.getText();
        BigDecimal unitsPerDay = unitsPerDayBox.getValue();
        Integer numVends = numVendsBox.getValue();
        BigDecimal maxAmountPerDay = maxAmountPerDayBox.getAmount();
        BigDecimal maxAmount = maxAmountTextBox.getAmount();

        if (typeName == null || typeName.trim().equals("")) {
            typeNameElement.setErrorMsg(MessagesUtil.getInstance().getMessage(MessagesUtil.getInstance()
                    .getMessage("error.field.is.required", new String[] { typeNameElement.getLabelText() })));
            valid = false;
        }
        if (message == null || message.trim().equals("")) {
            messageElement.setErrorMsg(MessagesUtil.getInstance().getMessage(MessagesUtil.getInstance()
                    .getMessage("error.field.is.required", new String[] { messageElement.getLabelText() })));
            valid = false;
        } else {
            StringBuilder errorMsg = new StringBuilder();
            boolean validMsg = validateMsg(message.trim());
            if (!validMsg) {
                errorMsg.append(MessagesUtil.getInstance().getMessage("blockingtype.msg.error.variables"));
                valid = false;
            } else {
                if (unitsPerDay != null && !message.contains(REASON_UNITS_MSG_TEMPLATE)) {
                    errorMsg.append(MessagesUtil.getInstance().getMessage("blockingtype.msg.error.units"));
                    valid = false;
                } else if (unitsPerDay == null && message.contains(REASON_UNITS_MSG_TEMPLATE)) {
                    errorMsg.append(MessagesUtil.getInstance().getMessage("blockingtype.msg.error.units.undefined",
                            new String[] { unitsPerDayElement.getLabelText() }));
                    valid = false;
                }
                if (numVends != null && !message.contains(REASON_VENDS_MSG_TEMPLATE)) {
                    errorMsg.append(MessagesUtil.getInstance().getMessage("blockingtype.msg.error.vends"));
                    valid = false;
                } else if (numVends == null && message.contains(REASON_VENDS_MSG_TEMPLATE)) {
                    errorMsg.append(MessagesUtil.getInstance().getMessage("blockingtype.msg.error.vends.undefined",
                            new String[] { numVendsElement.getLabelText() }));
                    valid = false;
                }
                if (maxAmount != null && !message.contains(REASON_AMT_MSG_TEMPLATE)) {
                    errorMsg.append(MessagesUtil.getInstance().getMessage("blockingtype.msg.error.amount"));
                    valid = false;
                } else if (maxAmount == null && message.contains(REASON_AMT_MSG_TEMPLATE)) {
                    errorMsg.append(MessagesUtil.getInstance().getMessage("blockingtype.msg.error.amount.undefined",
                            new String[] { maxAmountElement.getLabelText() }));
                    valid = false;
                }
                if (maxAmountPerDay != null && !message.contains(REASON_DAILY_AMT_MSG_TEMPLATE)) {
                    errorMsg.append(MessagesUtil.getInstance().getMessage("blockingtype.msg.error.dailyamount"));
                    valid = false;
                } else if (maxAmountPerDay == null && message.contains(REASON_DAILY_AMT_MSG_TEMPLATE)) {
                    errorMsg.append(MessagesUtil.getInstance().getMessage("blockingtype.msg.error.amount.undefined",
                            new String[] { maxAmountPerDayElement.getLabelText() }));
                    valid = false;
                }
            }

            if (!valid) {
                messageElement.setErrorMsg(errorMsg.toString());
            }
        }
        if (!completeCheckBox.getValue() && unitsPerDayBox.getValue() == null && numVendsBox.getValue() == null
                && maxAmountTextBox.getAmount() == null && maxAmountPerDayBox.getAmount() == null) {
            String errorMsg = MessagesUtil.getInstance().getMessage("blockingtype.panel.error.missingvalues",
                    new String[] { MessagesUtil.getInstance().getMessage("blockingtype.form.units"),
                            MessagesUtil.getInstance().getMessage("blockingtype.form.vends"),
                            MessagesUtil.getInstance().getMessage("blockingtype.form.amount"),
                            MessagesUtil.getInstance().getMessage("blockingtype.form.dailyamount") });
            error.setText(errorMsg);
            error.setVisible(true);
            valid = false;
        }

        if (unitsPerDay != null && unitsPerDay.compareTo(BigDecimal.ZERO) < 0) {
            valid = false;
            unitsPerDayElement.showErrorMsg(MessagesUtil.getInstance().getMessage("error.positive.value"));
        }
        if (maxAmountPerDay != null && maxAmountPerDay.compareTo(BigDecimal.ZERO) < 0) {
            valid = false;
            maxAmountPerDayElement.showErrorMsg(MessagesUtil.getInstance().getMessage("error.positive.value"));
        }
        if (maxAmount != null && maxAmount.compareTo(BigDecimal.ZERO) < 0) {
            valid = false;
            maxAmountElement.showErrorMsg(MessagesUtil.getInstance().getMessage("error.positive.value"));
        }
        if (numVends != null && numVends < 0) {
            valid = false;
            numVendsElement.showErrorMsg(MessagesUtil.getInstance().getMessage("error.positive.value"));
        }
        return valid;
    }

    // Msg only supports curly brackets for variables.
    public static boolean validateMsg(String msg) {
		String _a =  REASON_FIXED_MSG_TEMPLATE;
	    String _b = REASON_UNITS_MSG_TEMPLATE;
	    String _c = REASON_VENDS_MSG_TEMPLATE;
	    String _d = REASON_AMT_MSG_TEMPLATE;
	    String _e = REASON_DAILY_AMT_MSG_TEMPLATE;
		if (msg.endsWith("{") || (msg.endsWith("}") && !(msg.endsWith(_a) || msg.endsWith(_b) ||
				msg.endsWith(_c) || msg.endsWith(_d) || msg.endsWith(_e)))) {
			return false;
		}
		if (msg.startsWith("}") || (msg.startsWith("{") && !(msg.startsWith(_a) || msg.startsWith(_b) ||
				msg.startsWith(_c) || msg.startsWith(_d) || msg.startsWith(_e)))) {
			return false;
		}
		
		String[] closes = msg.split("}");
		if (closes.length > 1)
			for (int x = 0; x < closes.length; x++) {
				String section = closes[x];
				if (x == (closes.length - 1) && !section.contains("}") || section.trim().equals("")) {
					continue;
				}
				if (!(section.endsWith(endsSub(_a)) || section.endsWith(endsSub(_b)) ||
						section.endsWith(endsSub(_c)) || section.endsWith(endsSub(_d)) ||
                        section.endsWith(endsSub(_e)))) {
					return false;
				}
			}

		String[] opens = msg.split("\\{");
		if (opens.length > 1)
			for (int x = 0; x < opens.length; x++) {
				String section = opens[x];
				if ((x == 0 && !msg.startsWith("{")) || section.trim().equals("")) {
					continue;
				}
				if (!(section.startsWith(startsSub(_a)) || section.startsWith(startsSub(_b)) ||
						section.startsWith(startsSub(_c)) || section.startsWith(startsSub(_d)) ||
                        section.startsWith(startsSub(_e)))) {
					return false;
				}
			}
		return true;
	}

	public static String startsSub(String var) {
		return var.substring(1);
	}

	public static String endsSub(String var) {
		return var.substring(0, var.length() - 1);
	}

    @UiHandler("completeCheckBox")
    void handleCompleteCheckbox(ClickEvent event) {
        handleCompleteCheckbox();
    }

    public void handleCompleteCheckbox() {
        Boolean value = completeCheckBox.getValue();
        if (value != null && value) {
            unitsPerDayBox.setValue(null);
            numVendsBox.setValue(null);
            maxAmountTextBox.setAmount(null);
            maxAmountPerDayBox.setAmount(null);
            unitsPerDayBox.setEnabled(false);
            numVendsBox.setEnabled(false);
            maxAmountTextBox.setEnabled(false);
            maxAmountPerDayBox.setEnabled(false);
        } else {
            unitsPerDayBox.setEnabled(true);
            numVendsBox.setEnabled(true);
            maxAmountTextBox.setEnabled(true);
            maxAmountPerDayBox.setEnabled(true);
        }
    }

    @Override
    public void addFieldHandlers() {
        typeNameTextBox.addChangeHandler(new FormDataChangeHandler(form));
        unitsPerDayBox.addChangeHandler(new FormDataChangeHandler(form));
        numVendsBox.addChangeHandler(new FormDataChangeHandler(form));
        maxAmountTextBox.addChangeHandler(new FormDataChangeHandler(form));
        messageTextArea.addChangeHandler(new FormDataChangeHandler(form));
        completeCheckBox.addClickHandler(new FormDataClickHandler(form));
        maxAmountPerDayBox.addChangeHandler(new FormDataChangeHandler(form));
    }
}
