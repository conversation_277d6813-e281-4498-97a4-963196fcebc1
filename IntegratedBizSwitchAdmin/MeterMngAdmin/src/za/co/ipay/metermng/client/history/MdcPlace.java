package za.co.ipay.metermng.client.history;

import com.google.gwt.place.shared.Place;
import com.google.gwt.place.shared.PlaceTokenizer;
import com.google.gwt.place.shared.Prefix;

public class Mdc<PERSON>lace extends Place {
    
    public static final String ALL = "all";
    
    private String name;
    
    public MdcPlace(String name) {
        this.name = name;
    }
    
    public String getName() {
        return name;
    }

    public static String getPlaceAsString(MdcPlace place) {
        return "mdc:"+place.getName();
    }
    
    @Prefix(value = "mdc")
    public static class Tokenizer implements PlaceTokenizer<MdcPlace> {
        
        @Override
        public String getToken(Mdc<PERSON>lace place) {
            return place.getName();
        }

        @Override
        public MdcPlace getPlace(String token) {
            return new MdcPlace(token);
        }
    }
}
