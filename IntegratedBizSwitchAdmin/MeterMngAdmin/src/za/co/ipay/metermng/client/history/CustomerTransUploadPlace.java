package za.co.ipay.metermng.client.history;

import com.google.gwt.place.shared.Place;
import com.google.gwt.place.shared.PlaceTokenizer;
import com.google.gwt.place.shared.Prefix;

public class CustomerTransUploadPlace extends Place {

    public static CustomerTransUploadPlace ALL_PLACE = new CustomerTransUploadPlace("all");

    private String name;
    
    public CustomerTransUploadPlace(String name) {
        this.name = name;
    }
    
    public String getName() {
        return name;
    }
    
    public static String getPlaceAsString(CustomerTransUploadPlace p) {
        return "customertransupload:"+p.getName();
    }

    @Prefix(value = "customertransupload")
    public static class Tokenizer implements PlaceTokenizer<CustomerTransUploadPlace> {
        
        @Override
        public String getToken(CustomerTransUploadPlace place) {
            return "all";
        }

        @Override
        public CustomerTransUploadPlace getPlace(String token) {
            return new CustomerTransUploadPlace(token);
        }
    }
}
