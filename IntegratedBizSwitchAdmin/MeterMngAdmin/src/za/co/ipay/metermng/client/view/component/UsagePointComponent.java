package za.co.ipay.metermng.client.view.component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.logging.Logger;

import com.google.gwt.core.client.GWT;
import com.google.gwt.core.client.Scheduler;
import com.google.gwt.core.client.Scheduler.ScheduledCommand;
import com.google.gwt.event.dom.client.ChangeEvent;
import com.google.gwt.event.dom.client.ClickEvent;
import com.google.gwt.event.logical.shared.CloseEvent;
import com.google.gwt.event.logical.shared.CloseHandler;
import com.google.gwt.event.logical.shared.OpenEvent;
import com.google.gwt.event.logical.shared.OpenHandler;
import com.google.gwt.event.logical.shared.ValueChangeEvent;
import com.google.gwt.i18n.client.DateTimeFormat;
import com.google.gwt.place.shared.Place;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.uibinder.client.UiHandler;
import com.google.gwt.user.client.Timer;
import com.google.gwt.user.client.ui.Button;
import com.google.gwt.user.client.ui.DisclosurePanel;
import com.google.gwt.user.client.ui.FlowPanel;
import com.google.gwt.user.client.ui.HTML;
import com.google.gwt.user.client.ui.HTMLPanel;
import com.google.gwt.user.client.ui.HorizontalPanel;
import com.google.gwt.user.client.ui.Image;
import com.google.gwt.user.client.ui.Label;
import com.google.gwt.user.client.ui.ListBox;
import com.google.gwt.user.client.ui.ProvidesResize;
import com.google.gwt.user.client.ui.RequiresResize;
import com.google.gwt.user.client.ui.TextBox;
import com.google.gwt.user.client.ui.VerticalPanel;
import com.google.gwt.user.client.ui.Widget;
import com.google.gwt.user.datepicker.client.DateBox;
import za.co.ipay.gwt.common.client.Dialogs;
import za.co.ipay.gwt.common.client.factory.ResourcesFactoryUtil;
import za.co.ipay.gwt.common.client.form.BigDecimalValueBox;
import za.co.ipay.gwt.common.client.form.FormElement;
import za.co.ipay.gwt.common.client.form.FormGroupPanel;
import za.co.ipay.gwt.common.client.form.FormRowPanel;
import za.co.ipay.gwt.common.client.form.Format.FormatUtil;
import za.co.ipay.gwt.common.client.form.HasDirtyData;
import za.co.ipay.gwt.common.client.handler.ConfirmHandler;
import za.co.ipay.gwt.common.client.handler.FormDataChangeHandler;
import za.co.ipay.gwt.common.client.handler.FormDataValueChangeHandler;
import za.co.ipay.gwt.common.client.resource.Messages;
import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.gwt.common.client.resource.StyleNames;
import za.co.ipay.gwt.common.client.validation.ClientValidatorUtil;
import za.co.ipay.gwt.common.client.widgets.IpayActiveCheckBox;
import za.co.ipay.gwt.common.client.widgets.StrictDateFormat;
import za.co.ipay.gwt.common.client.workspace.SessionCheckCallback;
import za.co.ipay.gwt.common.shared.LookupListItem;
import za.co.ipay.gwt.common.shared.validation.ValidateUtil;
import za.co.ipay.metermng.client.event.MeterModelChangedEvent;
import za.co.ipay.metermng.client.event.MeterModelChangedEventHandler;
import za.co.ipay.metermng.client.event.UsagePointUpdatedEvent;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.resource.image.MediaResource;
import za.co.ipay.metermng.client.resource.image.MediaResource.MediaResourceUtil;
import za.co.ipay.metermng.client.rpc.AppSettingRpcAsync;
import za.co.ipay.metermng.client.rpc.callback.ClientCallback;
import za.co.ipay.metermng.client.util.MeterMngClientUtils;
import za.co.ipay.metermng.client.view.component.onlinebulk.GroupTypeTreeInfo;
import za.co.ipay.metermng.client.view.component.pricingstructure.UpComponentValidatePStoMM;
import za.co.ipay.metermng.client.view.component.selection.SelectionDataItemSuggestBoxTree;
import za.co.ipay.metermng.client.view.component.selection.SelectionDataItemSuggestBoxTreeFactory;
import za.co.ipay.metermng.client.view.component.usercustomfields.ContainsUserCustomFieldsComponent;
import za.co.ipay.metermng.client.view.component.usercustomfields.UserCustomFieldsComponent;
import za.co.ipay.metermng.client.view.workspace.UsagePointWorkspaceView;
import za.co.ipay.metermng.client.widget.pricingstructure.PricingStructureLookup;
import za.co.ipay.metermng.client.widget.suggestboxtree.SuggestBoxTree;
import za.co.ipay.metermng.datatypes.PaymentModeE;
import za.co.ipay.metermng.datatypes.RecordStatus;
import za.co.ipay.metermng.mybatis.generated.model.AppSetting;
import za.co.ipay.metermng.mybatis.generated.model.FormFields;
import za.co.ipay.metermng.mybatis.generated.model.PricingStructure;
import za.co.ipay.metermng.mybatis.generated.model.SpecialActionReasonsLog;
import za.co.ipay.metermng.mybatis.generated.model.UnitsAccount;
import za.co.ipay.metermng.mybatis.generated.model.UpMeterInstall;
import za.co.ipay.metermng.mybatis.generated.model.UpPricingStructure;
import za.co.ipay.metermng.mybatis.generated.model.UsagePoint;
import za.co.ipay.metermng.shared.IpayResponseData;
import za.co.ipay.metermng.shared.MeterMngStatics;
import za.co.ipay.metermng.shared.SpecialActionsData;
import za.co.ipay.metermng.shared.appsettings.AppSettings;
import za.co.ipay.metermng.shared.appsettings.CustomFieldDto;
import za.co.ipay.metermng.shared.dto.CustomerData;
import za.co.ipay.metermng.shared.dto.LocationData;
import za.co.ipay.metermng.shared.dto.MdcChannelReadingsDto;
import za.co.ipay.metermng.shared.dto.MeterData;
import za.co.ipay.metermng.shared.dto.MeterModelData;
import za.co.ipay.metermng.shared.dto.PSDto;
import za.co.ipay.metermng.shared.dto.SelectionDataItem;
import za.co.ipay.metermng.shared.dto.UpGenGroupLinkData;
import za.co.ipay.metermng.shared.dto.UpPricingStructureData;
import za.co.ipay.metermng.shared.dto.UsagePointData;
import za.co.ipay.metermng.shared.dto.usagepoint.MeterUpMdcChannelInfo;
import za.co.ipay.metermng.shared.userinterface.UserInterfaceFormFields;

public class UsagePointComponent extends BaseComponent
        implements ProvidesResize, RequiresResize, ContainsUserCustomFieldsComponent,
        AssignChannelReadingsComponent {

    private static final String UP_METER_INSPECTION_REQUEST = "usagepoint.meter_inspection_enabled";
    private static final UsagePointWidgetUiBinder uiBinder = GWT.create(UsagePointWidgetUiBinder.class);

    @UiField FlowPanel assignLinkRow;
    @UiField FormElement assignLinkElement;
    @UiField Label lblFetch;

    @UiField TextBox txtbxUsagepointname;
    @UiField ListBox lstbxUpBlockingType;
    @UiField HTML lastMdcConnectControlText;
    @UiField FlowPanel usagePointTopContainer;
    @UiField DisclosurePanel usagePointDisclosurePanel;
    @UiField FlowPanel contentpanel;
    @UiField(provided=true) UserCustomFieldsComponent userCustomFieldsComponent;

    @UiField HorizontalPanel groupTypesPanel;
    @UiField(provided=true) LocationComponent serviceLocationComponent;
    @UiField Button btnCancel;
    @UiField Button btnSave;
    @UiField Button btnCalc;
    @UiField Button usagePointpChargeViewButton;
    @UiField Button usagePointpMeterInspectionButton;
    @UiField IpayActiveCheckBox chckbxActive;
    @UiField Image openorclosearrow;
    @UiField Image upImage;
    @UiField Label headerLabel;
    @UiField FormElement activeElement;
    @UiField DateBox dtbxMeterInstallationDate;
    @UiField FormElement installationDateElement;
    @UiField FormElement usagePointNameElement;
    @UiField FormElement upBlockingTypeElement;
    @UiField FormRowPanel deactivateReasonPanel;
    @UiField FormRowPanel activateReasonPanel;
    @UiField FormRowPanel currentPricingChangeReasonPanel;
    @UiField FormRowPanel futurePricingChangeReasonPanel;
    @UiField FormRowPanel upBlockingReasonPanel;
    @UiField FormRowPanel upBlockingReasonMsgPanel;
    @UiField FormRowPanel usagePointpChargeViewPanel;
    @UiField FormRowPanel usagePointpMeterInspectionPanel;
    @UiField VerticalPanel requiredKeys;
    @UiField DisclosurePanel physicalAddressPanel;
    @UiField FormRowPanel activationDatePanel;
    @UiField DateBox dtbxMeterActivationDate;

    @UiField FormElement currentPSElement;
    @UiField FormElement futurePSElement;
    @UiField FormElement  currentPSStartDateElement;
    @UiField FormElement  futurePSStartDateElement;
    @UiField DateBox currentPricingStructureStartDate;
    @UiField DateBox futurePricingStructureStartDate;
    @UiField Button pricingStructureDeleteButton;
    @UiField Button pricingStructureViewAllButton;

    @UiField FormGroupPanel unitsAccountPanel;
    @UiField FormElement accountNameElement;
    @UiField TextBox txtbxAccountName;
    @UiField FormElement accountBalanceElement;
    @UiField Label lblAccountBalance;
    @UiField FormElement btnSyncElement;
    @UiField Button btnSync;
    @UiField FormRowPanel lowBalanceThresholdPanel;
    @UiField FormElement lowBalanceThresholdElement;
    @UiField BigDecimalValueBox txtbxlowThreshold;
    @UiField FormRowPanel notificationPanel;
    @UiField FormElement notificationEmailElement;
    @UiField TextBox txtbxNotificationEmail;
    @UiField FormElement notificationPhoneElement;
    @UiField TextBox txtbxNotificationPhone;
    @UiField Label lowBalanceThresholdSymbol;
    @UiField(provided = true) MridComponent mridComponent;
    @UiField(provided = true) PricingStructureLookup currentPricingStructureLookup;
    @UiField(provided = true) PricingStructureLookup futurePricingStructureLookup;

    private UsagePointData usagePoint = new UsagePointData();
    private LocationData serviceLocation;
    private final UsagePointWorkspaceView usagePointWorkspaceView;
    private Boolean disclosureOpen = null;
    private ClientFactory clientFactory;
    protected HasDirtyData hasDirtyData;
    private boolean userDeactivatedUP = false;

    private final ArrayList<AppSetting> customFieldList;
    private boolean userCustomFieldsComponentVisible = false;

    private SpecialActionsReasonComponent deactivateReasonsComponent;
    private SpecialActionsReasonComponent activateReasonsComponent;
    private SpecialActionsReasonComponent currentPricingChangeReasonsComponent;
    private SpecialActionsReasonComponent futurePricingChangeReasonsComponent;
    private SpecialActionsReasonComponent blockingReasonsComponent;
    private SpecialActionsReasonComponent unBlockingReasonsComponent;

    private static final String NOT_BLOCKED = "-1";
    private final Logger logger = Logger.getLogger("UsagePointComponent");
    private Map<String, FormFields> userInterfaceFields;
    private final boolean hasBlockPerm;
    private final boolean hasUnblockPerm;
    private boolean upAllowFutureInstallationDates;
    private boolean validateAllPsToMm = false;
    private boolean isUnitsPricingStructure = false;
    private String unitsSymbol;
    private UpComponentValidatePStoMM upComponentValidatePStoMM;
    private boolean changeInPaymentMode = false;
    private boolean validateUpNameRegex = false;

    interface UsagePointWidgetUiBinder extends UiBinder<Widget, UsagePointComponent> {
    }

    public UsagePointComponent(final ClientFactory clientFactory, UsagePointWorkspaceView usagePointWorkspaceView,
                               ArrayList<AppSetting> customFieldList) {

        this.clientFactory = clientFactory;
        this.usagePointWorkspaceView = usagePointWorkspaceView;
        this.hasDirtyData = usagePointWorkspaceView.createAndRegisterHasDirtyData();
        this.serviceLocationComponent = new LocationComponent(clientFactory, hasDirtyData);
        this.customFieldList = customFieldList;
        userCustomFieldsComponent = new UserCustomFieldsComponent(clientFactory, this, hasDirtyData);
        mridComponent = new MridComponent(clientFactory, hasDirtyData, null, false);
        currentPricingStructureLookup = new PricingStructureLookup(true, hasDirtyData, clientFactory);
        futurePricingStructureLookup = new PricingStructureLookup(false, hasDirtyData, clientFactory);

        initWidget(uiBinder.createAndBindUi(this));

        hasBlockPerm = clientFactory.getUser().hasPermission(MeterMngStatics.EDIT_PERMISSION_MM_USAGE_POINT_BLOCK);
        hasUnblockPerm = clientFactory.getUser().hasPermission(MeterMngStatics.EDIT_PERMISSION_MM_USAGE_POINT_UNBLOCK);
        createReasonsComponents();
        init();
        addFieldHandlers();
    }

    private void refreshPricingStructureLists() {
        if (usagePoint == null || usagePoint.getMeterData() == null) {
            currentPricingStructureLookup.updateLookupList();
            futurePricingStructureLookup.updateLookupList();
        } else {
            MeterData meterData = usagePoint.getMeterData();
            MeterModelData meterModelData = meterData.getMeterModelData();
            Long serviceResourceId = meterModelData.getServiceResourceId();
            Long meterTypeId = meterData.getMeterTypeId();
            ArrayList<Long> paymentModeIds = meterModelData.getPaymentModeIds();
            currentPricingStructureLookup.updateLookupList(serviceResourceId, meterTypeId, paymentModeIds);
            futurePricingStructureLookup.updateLookupList(serviceResourceId, meterTypeId, paymentModeIds);
        }

    }

    private void createReasonsComponents() {
        currentPricingChangeReasonsComponent = new SpecialActionsReasonComponent(clientFactory, hasDirtyData, SpecialActionsData.CHANGE_PRICING_STRUCTURE,
                MessagesUtil.getInstance().getMessage("usagepoint.current.pricing.change.enter.reason"), MessagesUtil.getInstance().getMessage("usagepoint.current.pricing.change.select.reason"));

        futurePricingChangeReasonsComponent = new SpecialActionsReasonComponent(clientFactory, hasDirtyData, SpecialActionsData.CHANGE_PRICING_STRUCTURE,
                MessagesUtil.getInstance().getMessage("usagepoint.future.pricing.change.enter.reason"), MessagesUtil.getInstance().getMessage("usagepoint.future.pricing.change.select.reason"));

        deactivateReasonsComponent = new SpecialActionsReasonComponent(clientFactory, hasDirtyData, SpecialActionsData.DEACTIVATE_USAGE_POINT,
                MessagesUtil.getInstance().getMessage("usagepoint.deactivate.enter.reason"), MessagesUtil.getInstance().getMessage("usagepoint.deactivate.select.reason"));

        activateReasonsComponent = new SpecialActionsReasonComponent(clientFactory, hasDirtyData, SpecialActionsData.ACTIVATE_USAGE_POINT,
                MessagesUtil.getInstance().getMessage("usagepoint.activate.enter.reason"), MessagesUtil.getInstance().getMessage("usagepoint.activate.select.reason"));

        blockingReasonsComponent = new SpecialActionsReasonComponent(clientFactory, hasDirtyData, SpecialActionsData.BLOCK_USAGE_POINT,
                MessagesUtil.getInstance().getMessage("usagepoint.blocking.enter.reason"), MessagesUtil.getInstance().getMessage("usagepoint.blocking.select.reason"));

        unBlockingReasonsComponent = new SpecialActionsReasonComponent(clientFactory, hasDirtyData, SpecialActionsData.UNBLOCK_USAGE_POINT,
                MessagesUtil.getInstance().getMessage("usagepoint.unblocking.enter.reason"), MessagesUtil.getInstance().getMessage("usagepoint.unblocking.select.reason"));

    }

    private void init() {
        configureCustomFields();
        openorclosearrow.setResource(MediaResourceUtil.getInstance().getOpenedArrowImage());
        upImage.setResource(MediaResourceUtil.getInstance().getHomeImage());
        usagePoint.setRecordStatus(RecordStatus.DAC);
        mridComponent.initMrid(clientFactory);
        clientFactory.getUsagePointRpc().getAutoGeneratedRef(new ClientCallback<String>() {
            @Override
            public void onSuccess(String result) {
                usagePoint.setAutoReference(result);
            }
        });
        AppSettingRpcAsync appSettingRpc = clientFactory.getAppSettingRpc();
        appSettingRpc.getAppSettingByKey(UP_METER_INSPECTION_REQUEST, new ClientCallback<AppSetting>() {
            @Override
            public void onSuccess(AppSetting result) {
                if (result.getValue().equals("true")) {
                    usagePointpMeterInspectionPanel.setVisible(true);
                }
            }
        });
        setAppSettingValues();
        addHandlers();
        actionPermissions();    // remove action buttons if no edit permission
        mapDataToForm();
        usagePointDisclosurePanel.setWidth("100%");
        StrictDateFormat format = new StrictDateFormat(
                DateTimeFormat.getFormat(FormatUtil.getInstance().getDateTimeFormat()));
        dtbxMeterInstallationDate.setFormat(format);
        dtbxMeterActivationDate.setFormat(format);
        currentPricingStructureStartDate.setFormat(format);
        futurePricingStructureStartDate.setFormat(format);
        disclosureOpen = null;
        if (serviceLocationComponent != null) {
            serviceLocationComponent.setContainerWorkspace(usagePointWorkspaceView);
        }

        assignLinkRow.setVisible(true);
    }

    public void setAppSettingValues() {

        clientFactory.getAppSettingRpc().getAppSettingsByKeys(
                Arrays.asList(AppSettings.UP_ALLOW_FUTURE_INSTALLATION_DATES,
                        AppSettings.VALIDATE_ALL_BILLING_DETERMINANTS_MODEL_VS_TARIFF),
                new ClientCallback<Map<String, AppSetting>>() {
                    @Override
                    public void onSuccess(Map<String, AppSetting> resultMap) {
                        for (AppSetting appSett : resultMap.values()) {
                            switch (appSett.getKey()) {
                            case AppSettings.UP_ALLOW_FUTURE_INSTALLATION_DATES:
                                upAllowFutureInstallationDates = Boolean.parseBoolean(appSett.getValue());
                                manageInstallationDateBox();
                                break;
                                case AppSettings.VALIDATE_ALL_BILLING_DETERMINANTS_MODEL_VS_TARIFF:
                                    validateAllPsToMm = Boolean.parseBoolean(appSett.getValue());
                                break;
                            }
                        }
                    }
                });
    }

    private void actionPermissions() {

        // We use setVisible and not removeFromParent since Fetch might be used.
        requiredKeys.setVisible(true);
        btnSave.setVisible(true);
        btnCancel.setVisible(true);
        pricingStructureDeleteButton.setVisible(true);

        assignLinkRow.setVisible(usagePoint == null || usagePoint.getId() == null ||
                                 (usagePoint.getMeterId() == null && usagePoint.getCustomerAgreementId() == null));

        if (usagePoint != null && usagePoint.getId() != null) {
            btnCalc.setVisible(true);
            usagePointpChargeViewButton.setVisible(true);
            btnSyncElement.setVisible(true);
        } else {
            btnCalc.setVisible(false);
            usagePointpChargeViewButton.setVisible(false);
            btnSyncElement.setVisible(false);
        }

        if (clientFactory.isEnableAccessGroups()) {
            boolean groupHasGlobal = UsagePointWorkspaceView.hasGlobalContractElement(usagePoint, clientFactory.isGroupGroupUser());
            if (groupHasGlobal) {
                requiredKeys.setVisible(false);
                btnSave.setVisible(false);
                btnCancel.setVisible(false);
                btnCalc.setVisible(false);
                usagePointpChargeViewButton.setVisible(false);
                btnSyncElement.setVisible(false);
                assignLinkRow.setVisible(false);
                pricingStructureDeleteButton.setVisible(false);
            }

            if (clientFactory.isGroupGlobalUser()) {
                requiredKeys.removeFromParent();
                btnSave.removeFromParent();
                btnCancel.removeFromParent();
                btnCalc.removeFromParent();
                usagePointpChargeViewButton.removeFromParent();
                btnSyncElement.removeFromParent();
                assignLinkRow.removeFromParent();
                pricingStructureDeleteButton.removeFromParent();
            }
        }

        if (!clientFactory.getUser().hasPermission(MeterMngStatics.ACCESS_PERMISSION_UP_PRICING_STRUCT)) {
            pricingStructureDeleteButton.removeFromParent();
        }
        if (!clientFactory.getUser().hasPermission(MeterMngStatics.ACTION_PERMISSION_MM_UNITS_ACC_SYNC_BALANCE)) {
            btnSyncElement.removeFromParent();
        }
        if (!clientFactory.getUser().hasPermission(MeterMngStatics.EDIT_PERMISSION_MM_USAGE_POINT_EDIT)) {
            requiredKeys.removeFromParent();
            btnSave.removeFromParent();
            btnCancel.removeFromParent();
            assignLinkRow.removeFromParent();
        }
        if (!clientFactory.getUser().hasPermission(MeterMngStatics.ACTION_PERMISSION_MM_UP_CALC_TARIFF)) {
            btnCalc.removeFromParent();
        }
        if (!clientFactory.getUser().hasPermission(MeterMngStatics.ACCESS_PERMISSION_MM_OUTSTANDING_CHARGE_VIEW)) {
            usagePointpChargeViewButton.removeFromParent();
        }
        if (!clientFactory.getUser().hasPermission(MeterMngStatics.ACTION_PERMISSION_MM_UNITS_ACC_SYNC_BALANCE)) {
            btnSyncElement.removeFromParent();
        }
    }

    private boolean checkActivatedUP(RecordStatus upRecordStatus) {
        return chckbxActive.isEnabled() && chckbxActive.getValue() && upRecordStatus.equals(RecordStatus.DAC);
    }

    private boolean checkBlockingTypeDirty() {
        boolean dirty = false;
        Long defaultValue = new Long(NOT_BLOCKED);
        Long selectedValue = new Long(lstbxUpBlockingType.getValue(lstbxUpBlockingType.getSelectedIndex()));
        Long existsingValue = usagePoint.getBlockingTypeId();

        if ((existsingValue != null && !selectedValue.equals(existsingValue))
                || (existsingValue == null && !selectedValue.equals(defaultValue))) {
            dirty = true;
            lstbxUpBlockingType.addStyleName("changed");
        } else {
            lstbxUpBlockingType.removeStyleName("changed");
        }
        return dirty;
    }

    @UiHandler("btnSave")
    void handleSaveButton(ClickEvent event) {
        usagePointWorkspaceView.toggleSaveBtns(false);
        SessionCheckCallback sessionCheckCallback = new SessionCheckCallback() {
            @Override
            public void callback(SessionCheckResolution sessionCheckResolution) {
                if(usagePoint != null) {
                    usagePoint.setMultiUsagePointEnabled(clientFactory.isEnableMultiUp());
                    clientFactory.getSearchRpc().checkUsagePointDataIntegrity(usagePoint, new ClientCallback<Boolean>() {
                        @Override
                        public void onSuccess(Boolean result) {
                            if(result) {
                                continueHandleSaveButton();
                            } else {
                                Place place = usagePointWorkspaceView.getPlace();
                                usagePointWorkspaceView.processInvalidState(place);
                                usagePointWorkspaceView.toggleSaveBtns(true);
                            }
                        }
                    });
                } else {
                    continueHandleSaveButton();
                }
            }
        };
        clientFactory.handleSessionCheckCallback(sessionCheckCallback);
    }

    private void continueHandleSaveButton() {
        // Map form fields to data object
        clearErrorMessages();

        //note: getPricingStructureItem() is repeated MANY times in this code, extracting the LookupListItem upfront here to
        //try and save some unnecessary execution
        LookupListItem currentPSItem = currentPricingStructureLookup.getSelectedPricingStructureItem();

        if (currentPSItem != null) {
            if (changeInPaymentMode) {
                Messages messages = MessagesUtil.getInstance();
                Dialogs.confirm(messages.getMessage("ps.paymentmode.change.warn"), messages.getMessage("button.yes"),
                        messages.getMessage("button.no"), MediaResourceUtil.getInstance().getQuestionIcon(),
                        new ConfirmHandler() {
                            @Override
                            public void confirmed(boolean confirm) {
                                if (confirm) {
                                    continueHandleSaveButton2();
                                } else {
                                    usagePointWorkspaceView.toggleSaveBtns(true);
                                }
                            }
                        });
            } else {
                continueHandleSaveButton2();
            }
        } else {
            handleSave2();
        }
    }

    private void continueHandleSaveButton2() {
        LookupListItem currentPSItem = currentPricingStructureLookup.getSelectedPricingStructureItem();

        Date installationDate = dtbxMeterInstallationDate.getValue();
        Date firstTariffStartDate = (Date) currentPSItem.getExtraInfoMap().get("firstTariffStartDate");
        if (installationDate != null && !dtbxMeterInstallationDate.getTextBox().getValue().isEmpty()
                && installationDate.before(firstTariffStartDate)) {
            Date lastCyclicChargeDate = null;
            if (usagePoint != null) {
                lastCyclicChargeDate = usagePoint.getLastCyclicChargeDate();
            }
            String message = UsagePointWorkspaceView.constructMessageInstallDtVsTariffStart(firstTariffStartDate,
                    lastCyclicChargeDate);
            Messages messages = MessagesUtil.getInstance();
            Dialogs.confirm(message, messages.getMessage("button.yes"), messages.getMessage("button.no"),
                    MediaResourceUtil.getInstance().getQuestionIcon(), new ConfirmHandler() {

                        @Override
                        public void confirmed(boolean confirm) {
                            if (confirm) {
                                handleSave2();
                            } else {
                                usagePointWorkspaceView.toggleSaveBtns(true);
                            }
                        }
                    });
        } else {
            handleSave2();
        }

    }

    private void handleSave2() {
        final LookupListItem currentPSItem = currentPricingStructureLookup.getSelectedPricingStructureItem();
        final LookupListItem futurePSItem = futurePricingStructureLookup.getSelectedPricingStructureItem();
        if (validate(currentPSItem, futurePSItem)) {
            if (futurePSItem != null) {
                Long currentPaymentMode = Long.valueOf(currentPSItem.getExtraInfoMap().get("paymentModeId").toString());
                boolean isCurrentUBA = PaymentModeE.THIN_UNITS.getId() == currentPaymentMode;
                Long futurePaymentMode = Long.valueOf(futurePSItem.getExtraInfoMap().get("paymentModeId").toString());
                boolean isFutureUBA = PaymentModeE.THIN_UNITS.getId() == futurePaymentMode;
                if (isCurrentUBA && !isFutureUBA) {
                    String message = MessagesUtil.getInstance().getMessage("usagepoint.ps.unitstocurrency");
                    Messages messages = MessagesUtil.getInstance();
                    Dialogs.confirm(message, messages.getMessage("button.yes"), messages.getMessage("button.no"),
                            MediaResourceUtil.getInstance().getQuestionIcon(), new ConfirmHandler() {
                                @Override
                                public void confirmed(boolean confirm) {
                                    if (confirm) {
                                        handleSave3(currentPSItem, futurePSItem);
                                    } else {
                                        usagePointWorkspaceView.toggleSaveBtns(true);
                                    }
                                }
                            });
                } else {
                    handleSave3(currentPSItem, futurePSItem);
                }
            } else {
                handleSave3(currentPSItem, futurePSItem);
            }
        } else {
            String errormsg = MessagesUtil.getInstance().getMessage("usagepoint.save.errors");
            if (usagePoint != null && usagePoint.getMeterData() != null) {
                if (usagePoint.getMeterData().getRecordStatus() != RecordStatus.ACT) {
                    errormsg = (MessagesUtil.getInstance().getMessage("usagepoint.save.errors") + " " + MessagesUtil.getInstance().getMessage("usagepoint.error.meterandcustomer.required"));
                }
            }
            if (usagePoint != null && usagePoint.getCustomerAgreementData() != null) {
                if (usagePoint.getCustomerAgreementData().getRecordStatus() != RecordStatus.ACT) {
                    errormsg = (MessagesUtil.getInstance().getMessage("usagepoint.save.errors") + " " + MessagesUtil.getInstance().getMessage("usagepoint.error.meterandcustomer.required"));
                }
            }

            Dialogs.centreErrorMessage(errormsg, MediaResourceUtil.getInstance().getErrorIcon(), null);
            usagePointWorkspaceView.toggleSaveBtns(true);
        }
    }

    private void handleSave3(LookupListItem currentPSItem, LookupListItem futurePSItem) {
        String currentPSIdStr = currentPSItem.getValue();
        String futurePSIdStr = (futurePSItem == null ? null : futurePSItem.getValue());

        // check correlation between choice of PS and MDC channels
        if (usagePoint != null && usagePoint.getMeterData() != null
            && (currentPricingStructureLookup.isDataDirty()
                || futurePricingStructureLookup.isDataDirty()
                        || checkActivatedUP(usagePoint.getRecordStatus()))) {
            boolean isMdcChannels = usagePoint.getMeterData().getMeterModelData().isMdcHasChannels();
            boolean isAnyRegReadPS = ("true".equals(currentPSItem.getExtraInfoMap().get("regReadPS")))
                                     || (futurePSItem != null && "true".equals(futurePSItem.getExtraInfoMap().get("regReadPS")));

            if (isAnyRegReadPS || (isMdcChannels && validateAllPsToMm)) {
                // confirm corresponding mdcChannels billingdets to tariff billingdets
                List<PSDto> pSDtos = new ArrayList<>();
                // for currentPS sending in new date and not installDate because on takeOn of
                // data from other systems,
                // sometimes bring over meters with very old installDates but don't bother to
                // pull in full PS history.
                pSDtos.add(new PSDto(Long.valueOf(currentPSIdStr), new Date()));
                if (futurePSIdStr != null) {
                    pSDtos.add(new PSDto(Long.valueOf(futurePSIdStr), futurePricingStructureStartDate.getValue()));
                }

                upComponentValidatePStoMM = new UpComponentValidatePStoMM(this);
                upComponentValidatePStoMM.isregReadPsSameBillingDetsAsMeterModel(clientFactory, pSDtos,
                        usagePoint.getMeterData().getMeterModelData().getMdcId(), clientFactory.getUser().getUserName(), logger);

            } else {
                saveContinue();
            }
        } else {
            saveContinue();
        }
    }

    public void saveContinue() {
        boolean installDateDirty = isInstallDateDirty();
        final boolean activatedNow = checkActivatedUP(usagePoint.getRecordStatus());
        UpPricingStructure futureUpPs = null;
        if (usagePoint.getUpPricingStructureData().getFutureUpPricingStructureData() != null) {
            futureUpPs = usagePoint.getUpPricingStructureData().getFutureUpPricingStructureData().getUpPricingStructure();
        }
        if (dtbxMeterInstallationDate.isVisible() && dtbxMeterInstallationDate.isEnabled()
                && (installDateDirty || activatedNow)) {
            Date installdate = dtbxMeterInstallationDate.getValue();
            String psMessage = "";
            if (installDateDirty && futureUpPs != null && installdate.after(futureUpPs.getStartDate())) {
                psMessage = ResourcesFactoryUtil.getInstance().getMessages().getMessage("question.confirm.installation.date.3");
            }
            Dialogs.confirm (
                    new String[]{
                            ResourcesFactoryUtil.getInstance().getMessages().getMessage("question.confirm.installation.date.1",new String[]{usagePoint.getMeterData().getMeterNum(),FormatUtil.getInstance().formatDate(installdate)
                                    , FormatUtil.getInstance().formatTime(installdate)}),
                                    ResourcesFactoryUtil.getInstance().getMessages().getMessage("question.confirm.installation.date.2"),
                                    psMessage},
                                    ResourcesFactoryUtil.getInstance().getMessages().getMessage("option.confirm"),
                                    ResourcesFactoryUtil.getInstance().getMessages().getMessage("option.no"),
                    ResourcesFactoryUtil.getInstance().getQuestionIcon(),
                                    new ConfirmHandler() {
                        @Override
                        public void confirmed(boolean confirm) {
                            if (confirm) {
                            	mapFormToData();
                                checkChannelInfoB4Save();
                            } else {
                                dtbxMeterInstallationDate.setEnabled(true);
                                dtbxMeterInstallationDate.setFocus(true);
                                usagePointWorkspaceView.toggleSaveBtns(true);
                            }
                        }
                    });
        } else {
        	mapFormToData();
            if (dtbxMeterInstallationDate.getValue() != null &&
                currentPricingStructureLookup.isDataDirty() || futurePricingStructureLookup.isDataDirty()
                || activatedNow) {
                checkChannelInfoB4Save();
            } else {
                fireUpdateEvent(null);
            }
        }
    }

    private void manageInstallationDateBox() {
        Date installationDate = usagePoint.getInstallationDate();
        if (installationDate == null) {
            enableInstallationDatebox(usagePoint.getMeterId() != null);
        } else {
            enableInstallationDatebox(
                    isUnitsPricingStructure && ((upAllowFutureInstallationDates && installationDate.after(new Date()))
                            || usagePoint.hasNoTransactions()));
        }
    }

    private void enableInstallationDatebox(boolean enabled) {
        dtbxMeterInstallationDate.setEnabled(enabled);
        if (!enabled) {
            dtbxMeterInstallationDate.setValue(usagePoint.getInstallationDate());
        }
        installationDateElement.setRequired(enabled || dtbxMeterInstallationDate.getValue() != null);
    }

    private boolean isInstallDateDirty() {
        boolean installChanged = false;
        Date currentInstallDate = usagePoint.getInstallationDate();
        Date newInstallDate = dtbxMeterInstallationDate.getValue();
        //note: current installDate not null and new one null isnot possible
        if ((currentInstallDate == null && newInstallDate != null) ||
                (currentInstallDate != null &&  newInstallDate != null && !currentInstallDate.equals(newInstallDate))) {
            installChanged = true;
        }
        return installChanged;
    }

    private void checkChannelInfoB4Save() {
        if (usagePoint.getRecordStatus().equals(RecordStatus.ACT)) {
            final UsagePointComponent parent = this;
            MeterUpMdcChannelInfo meterUpMdcChannelInfo = new MeterUpMdcChannelInfo(usagePoint.getMeterData().getId(),
                    usagePoint.getMeterData().getMeterModelId(),
                    usagePoint.getId(),
                    usagePoint.getUpPricingStructureData().getUpPricingStructure().getPricingStructureId(),
                    usagePoint.getUpMeterInstall().getId(),
                    usagePoint.getInstallationDate());
            clientFactory.getLookupRpc().getMeterUpMdcChannelInfo(meterUpMdcChannelInfo, new ClientCallback<MeterUpMdcChannelInfo>() {
                @Override
                public void onSuccess(MeterUpMdcChannelInfo result) {
                    if (result == null || result.getChannelList() == null || result.getChannelList().isEmpty()) {
                        logger.info("getChannelsForPricingStructureAndMeterModel: result is null");
                        fireUpdateEvent(null);
                    } else {
                        logger.info("getMeterUpMdcChannelInfo: result.getChannelList() size=" + result.getChannelList().size());
                        //get initial readings for the channels
                        final AssignChannelReadingsDialogueBox assignChannelReadings =
                                new AssignChannelReadingsDialogueBox(parent, result, usagePoint.getMeterData().getMeterNum(),
                                        usagePoint.getName(),
                                        usagePoint.getMeterData().getMeterModelData().getName(),
                                        usagePoint.getMeterData().getMeterModelData().getMdcName(),
                                        usagePoint.getUpPricingStructureData().getPricingStructure().getName(),
                                        usagePoint.getMeterData().getMeterModelData().getServiceResourceId());
                                Scheduler.get().scheduleDeferred(new ScheduledCommand() {
                                    @Override
                                    public void execute() {
                                        assignChannelReadings.center();
                                        assignChannelReadings.show();
                                    }
                                });
                    }
                }

            });
        } else {
            fireUpdateEvent(null);
        }
    }

    @Override
    public void fireUpdateEvent(MeterUpMdcChannelInfo meterUpMdcChannelInfo) {
        //fireUsagePointUpdatedEvent
        List<MdcChannelReadingsDto> channelReadingsList = null;
        if (meterUpMdcChannelInfo != null && !meterUpMdcChannelInfo.getChannelList().isEmpty()) {
            channelReadingsList = meterUpMdcChannelInfo.getChannelList();
        }
        hasDirtyData.setDirtyData(false);
        //clientFactory.getEventBus().fireEvent(new UsagePointUpdatedEvent(usagePointWorkspaceView, usagePoint, UsagePointUpdatedEvent.SAVE_USAGE_POINT));
        if ((dtbxMeterInstallationDate.isVisible() && dtbxMeterInstallationDate.isEnabled())) {
            UsagePointUpdatedEvent updateevent = new UsagePointUpdatedEvent(usagePointWorkspaceView, usagePoint, UsagePointUpdatedEvent.SAVE_USAGE_POINT);
            updateevent.setChannelReadingsList(channelReadingsList);
            updateevent.setUserDeactivatedUP(userDeactivatedUP);
            clientFactory.getEventBus().fireEvent(updateevent);
        } else {
            UsagePointUpdatedEvent updateevent = new UsagePointUpdatedEvent(usagePointWorkspaceView, usagePoint, UsagePointUpdatedEvent.SAVE_USAGE_POINT);
            updateevent.setUserDeactivatedUP(userDeactivatedUP);
            updateevent.setChannelReadingsList(channelReadingsList);
            deactivateReasonPanel.clear();
            deactivateReasonPanel.setVisible(false);
            activateReasonPanel.clear();
            activateReasonPanel.setVisible(false);
            currentPricingChangeReasonPanel.clear();
            currentPricingChangeReasonPanel.setVisible(false);
            futurePricingChangeReasonPanel.clear();
            futurePricingChangeReasonPanel.setVisible(false);
            clientFactory.getEventBus().fireEvent(updateevent);
        }
    }

    @UiHandler("btnCancel")
    void handleClearButton(ClickEvent event) {
        hasDirtyData.checkDirtyData(new ConfirmHandler() {

            @Override
            public void confirmed(boolean confirm) {
                if(confirm) {
                    hasDirtyData.setDirtyData(false);
                    changeInPaymentMode = false;
                    clearErrorMessages();
                    // clear custom fields
                    userCustomFieldsComponent.clearCustomFields();

                    //clear the usage point groups panel
                    clearUsagePointGroups();

                    // Map form fields to data object
                    if (clientFactory != null) {
                        SessionCheckCallback sessionCheckCallback = new SessionCheckCallback() {
                            @Override
                            public void callback(SessionCheckResolution sessionCheckResolution) {
                                if (usagePoint.getServiceLocation() != null) {
                                    setServiceLocation(usagePoint.getServiceLocation());
                                }
                                mapDataToForm();

                                Dialogs.centreMessage(MessagesUtil.getInstance().getMessage("usagepoint.changes.cleared"),
                                        new Image(MediaResourceUtil.getInstance().getInformationIcon()),
                                        StyleNames.POPUP_MESSAGE, null, null);

                                resetCurrentPricingStructureLookupSelection();
                                resetFuturePricingStructureLookupSelection();
                                resetPricingChangeReasonsPanel();
                                resetBlockingTypeLookupListBoxSelection();
                                clearBlockingReasonsComponent();
                                if (validateUpNameRegex) {
                                    txtbxUsagepointname.setText("");
                                }
                            }
                        };
                        clientFactory.handleSessionCheckCallback(sessionCheckCallback);
                    }
                }
            }
        });
    }


    @UiHandler("lblFetch")
    void handleAssignLink(ClickEvent event) {
        clearErrorMessages();
        AssignUsagePointDialogueBox assignUsagePointDialogueBox = new AssignUsagePointDialogueBox(clientFactory, usagePointWorkspaceView);
        assignUsagePointDialogueBox.clearFields();
        assignUsagePointDialogueBox.setUsagePointData(usagePoint);
        assignUsagePointDialogueBox.setGlassEnabled(true);
        assignUsagePointDialogueBox.setAutoHideEnabled(false);
        assignUsagePointDialogueBox.setAutoHideOnHistoryEventsEnabled(true);
        assignUsagePointDialogueBox.setAnimationEnabled(true);
        assignUsagePointDialogueBox.setText(MessagesUtil.getInstance().getMessage("usagepoint.find") + ":");
        assignUsagePointDialogueBox.setPopupPosition(lblFetch.getAbsoluteLeft() + lblFetch.getOffsetWidth() - 250, lblFetch.getAbsoluteTop() + lblFetch.getOffsetHeight());
        assignUsagePointDialogueBox.show();
        MeterMngClientUtils.ensurePopupIsOnScreen(assignUsagePointDialogueBox,
                lblFetch.getAbsoluteLeft() + lblFetch.getOffsetWidth() - 250,
                lblFetch.getAbsoluteTop() + lblFetch.getOffsetHeight(), 0);
    }

    @UiHandler("usagePointpChargeViewButton")
    void handleChargeViewButton(ClickEvent event) {
        SessionCheckCallback sessionCheckCallback = new SessionCheckCallback() {
            @Override
            public void callback(SessionCheckResolution sessionCheckResolution) {
                clientFactory.getSearchRpc().getUsagePointDataByUsagePointName(usagePoint.getName(), new ClientCallback<UsagePointData>() {
                    @Override
                    public void onSuccess(UsagePointData result) {
                        usagePointWorkspaceView.showViewUsagePointChargeDialogBox(result,
                                usagePointpChargeViewButton.getAbsoluteLeft() + 180,
                                usagePointpChargeViewButton.getAbsoluteTop(), null);
                    }
                });
            }
        };
        clientFactory.handleSessionCheckCallback(sessionCheckCallback);
    }

    @UiHandler("usagePointpMeterInspectionButton")
    void handleMeterInspectionButton(ClickEvent event) {
        LocationData usagepointLocation = usagePoint.getServiceLocation();
        MeterData meter = usagePoint.getMeterData();
        CustomerData customer = null;
        if (usagePoint.getCustomerAgreementData() != null) {
            customer = usagePoint.getCustomerAgreementData().getCustomerData();
        }
        if (clientFactory != null && usagePoint.getId() != null && usagepointLocation.getId() != null && meter.getId() != null &&
                customer != null && customer.getId() != null) {
            SessionCheckCallback sessionCheckCallback = new SessionCheckCallback() {
                @Override
                public void callback(SessionCheckResolution sessionCheckResolution) {
                    final int left = usagePointpMeterInspectionButton.getAbsoluteLeft() + usagePointpMeterInspectionButton.getOffsetWidth();
                    final int top = usagePointpMeterInspectionButton.getAbsoluteTop() - usagePointpMeterInspectionButton.getOffsetHeight();
                    MeterInspectionDialogBox meterInspectionDialogBox = new MeterInspectionDialogBox(clientFactory, usagePoint, left, top);
                    meterInspectionDialogBox.setGlassEnabled(true);
                    meterInspectionDialogBox.setAutoHideEnabled(false);
                    meterInspectionDialogBox.setAutoHideOnHistoryEventsEnabled(true);
                    meterInspectionDialogBox.setAnimationEnabled(true);
                    meterInspectionDialogBox.setText(MessagesUtil.getInstance().getMessage("usagepoint.meter.inspection.request.btn") + ":");
                    int left2 = usagePointpMeterInspectionButton.getAbsoluteLeft();
                    int top2 = usagePointpMeterInspectionButton.getAbsoluteTop();
                    meterInspectionDialogBox.setPopupPosition(left2 + 180, top2);
                    meterInspectionDialogBox.show();
                }
            };
            clientFactory.handleSessionCheckCallback(sessionCheckCallback);
        }
    }

    @UiHandler("pricingStructureViewAllButton")
    void handleViewAllPSButton(ClickEvent event) {
        if (usagePoint.getId() != null) {
            SessionCheckCallback sessionCheckCallback = new SessionCheckCallback() {
                @Override
                public void callback(SessionCheckResolution sessionCheckResolution) {
                    clientFactory.getUsagePointRpc().getAllUPPricingStructures(usagePoint.getId(), clientFactory.isEnableAccessGroups(),
                            new ClientCallback<List<UpPricingStructureData>>() {
                                @Override
                                public void onSuccess(List<UpPricingStructureData> result) {
                                    if (result != null) {
                                        ViewPricingStructuresDialogueBox dialogBox = new ViewPricingStructuresDialogueBox(result);
                                        dialogBox.setGlassEnabled(true);
                                        dialogBox.setAutoHideEnabled(true);
                                        dialogBox.setAnimationEnabled(true);
                                        dialogBox.setText(MessagesUtil.getInstance().getMessage("usagepoint.ps.view.all.btn") + ":");
                                        int left = pricingStructureViewAllButton.getAbsoluteLeft();
                                        int top = pricingStructureViewAllButton.getAbsoluteTop();
                                        dialogBox.setPopupPosition(left + 100, top - 100);
                                        dialogBox.show();
                                    }
                                }
                            });
                }
            };
            clientFactory.handleSessionCheckCallback(sessionCheckCallback);
        }
    }

    @UiHandler("pricingStructureDeleteButton")
    void handleDeletePSButton(ClickEvent event) {
        final UpPricingStructureData data = usagePoint.getUpPricingStructureData();
        UpPricingStructureData futureData = data.getFutureUpPricingStructureData();
        if (usagePoint.getId() != null && futureData != null && futureData.getUpPricingStructure().getId() != null) {
            final Long upPSID = futureData.getUpPricingStructure().getId();
            final Messages messages = MessagesUtil.getInstance();
            Dialogs.confirm(messages.getMessage("usagepoint.ps.delete.btn.confirm"), messages.getMessage("button.yes"),
                    messages.getMessage("button.no"), MediaResourceUtil.getInstance().getQuestionIcon(),
                    new ConfirmHandler() {
                        @Override
                        public void confirmed(boolean confirm) {
                            if (confirm) {
                                SessionCheckCallback sessionCheckCallback = new SessionCheckCallback() {
                                    @Override
                                    public void callback(SessionCheckResolution sessionCheckResolution) {
                                        clientFactory.getUsagePointRpc().deleteUpPricingStructure(upPSID, usagePoint.getId(), new ClientCallback<Boolean>() {
                                            @Override
                                            public void onSuccess(Boolean isValid) {
                                                data.setFutureUpPricingStructureData(null);
                                                futurePSElement.clearErrorMsg();
                                                futurePSStartDateElement.clearErrorMsg();
                                                resetFuturePricingStructureLookupSelection();
                                                Dialogs.centreMessage(MessagesUtil.getInstance().getMessage("usagepoint.ps.delete.success"),
                                                        new Image(MediaResourceUtil.getInstance().getInformationIcon()), StyleNames.POPUP_MESSAGE, null, null);
                                            }
                                        });
                                    }
                                };
                                clientFactory.handleSessionCheckCallback(sessionCheckCallback);
                            }
                        }
            });
        } else {
            data.setFutureUpPricingStructureData(null);
            futurePSElement.clearErrorMsg();
            futurePSStartDateElement.clearErrorMsg();
            resetFuturePricingStructureLookupSelection();
        }
    }

    @SuppressWarnings("unchecked")
    private void clearUsagePointGroups(){
        //clear the usage point groups panel
        SuggestBoxTree<Long, SelectionDataItem, GroupTypeTreeInfo> sbt;
        for (int j=0; j<groupTypesPanel.getWidgetCount(); j++) {
            if (groupTypesPanel.getWidget(j) instanceof SuggestBoxTree ) {
                sbt = (SuggestBoxTree<Long, SelectionDataItem, GroupTypeTreeInfo>) groupTypesPanel.getWidget(j);
                sbt.clear();
            }
        }
    }

    @UiHandler("chckbxActive")
    void handleActiveCheckbox(ClickEvent event) {
        final int left = chckbxActive.getAbsoluteLeft()+chckbxActive.getOffsetWidth();
        final int top = chckbxActive.getAbsoluteTop()-chckbxActive.getOffsetHeight();

        if (chckbxActive.getValue().equals(usagePoint.getRecordStatus().equals(RecordStatus.ACT))) { //already activated / deactivated
           showActivationReasons(false, false);
           hasDirtyData.setDirtyData(false);
        } else {
            if (chckbxActive.getValue()) { //being made active
                userDeactivatedUP = false;
                if (usagePoint.getMeterId() == null || usagePoint.getCustomerAgreementId() == null) {
                    Dialogs.displayErrorMessage(MessagesUtil.getInstance().getMessage("usagepoint.error.meterandcustomer.required"),
                            MediaResourceUtil.getInstance().getErrorIcon(), left, top,
                            MessagesUtil.getInstance().getMessage("button.close"));
                    chckbxActive.setValue(false);
                    chckbxActive.setTextAndStyle(chckbxActive.getValue());
                } else if (usagePoint.getId() != null) {
                    showActivationReasons(true, false);
                }
            } else { //deactivating
                userDeactivatedUP = true;
                showActivationReasons(false, true);
            }
        }
    }

    private void showActivationReasons(boolean activate, boolean deactivate) {
        if (activateReasonsComponent.getSpecialActions() != null) {
            if (activate) {
                hasDirtyData.setDirtyData(true);
                activateReasonPanel.add(activateReasonsComponent);
                activateReasonPanel.setVisible(true);
            } else {
                activateReasonsComponent.clearFields();
                activateReasonPanel.clear();
                activateReasonPanel.setVisible(false);
            }
        }
        if (deactivateReasonsComponent.getSpecialActions() != null) {
            if (deactivate) {
                hasDirtyData.setDirtyData(true);
                deactivateReasonPanel.add(deactivateReasonsComponent);
                deactivateReasonPanel.setVisible(true);
            } else {
                deactivateReasonsComponent.clearFields();
                deactivateReasonPanel.clear();
                deactivateReasonPanel.setVisible(false);
            }
        }
    }

    @UiHandler("btnCalc")
    void handleCalculateTariffButton(ClickEvent event) {
        clearErrorMessages();
        final int left = btnCalc.getAbsoluteLeft()+btnCalc.getOffsetWidth();
        final int top = btnCalc.getAbsoluteTop()-btnCalc.getOffsetHeight();
        // Map form fields to data object
        if (clientFactory != null) {
            final ClientCallback<IpayResponseData> calcTariffSvcAsyncCallback = new ClientCallback<IpayResponseData>() {
                @Override
                public void onSuccess(IpayResponseData result) {
                    if (result==null) {
                        Dialogs.displayErrorMessage(MessagesUtil.getInstance().getMessage("usagepoint.calculate.tariff.connection.error"),
                                MediaResourceUtil.getInstance().getErrorIcon(), left, top,
                                MessagesUtil.getInstance().getMessage("button.close"));
                    } else if (!result.getResCode().equals("meterMng000")) {
                        Dialogs.displayErrorMessage(MessagesUtil.getInstance().getMessage("usagepoint.calculate.tariff.error"),
                                                    MediaResourceUtil.getInstance().getErrorIcon(), left, top,
                                                    MessagesUtil.getInstance().getMessage("button.close"));
                    } else {
                        usagePointWorkspaceView.reload();
                        Dialogs.displayInformationMessage(MessagesUtil.getInstance().getMessage("usagepoint.calculate.tariff.ok"),
                                                    MediaResourceUtil.getInstance().getInformationIcon(), left, top, null);
                    }
                }
            };
            SessionCheckCallback sessionCheckCallback = new SessionCheckCallback() {
                @Override
                public void callback(SessionCheckResolution sessionCheckResolution) {
                    clientFactory.getPricingStructureRpc().sendTariffCalculationMsg(usagePoint.getMrid(), calcTariffSvcAsyncCallback);
                }
            };
            clientFactory.handleSessionCheckCallback(sessionCheckCallback);
        }
    }

    @UiHandler("currentPricingStructureLookup")
    void handleCurrentPricingStructureSelected(ValueChangeEvent<String> event) {
        boolean removeReasons = true;
        LookupListItem selectedPsItem = currentPricingStructureLookup.getSelectedPricingStructureItem();
        handlePricingStructureChange(selectedPsItem, null);
        // Check if adding a UP only. Hide currentPricingChangeReasonPanel if new UP.
        if (usagePoint != null && usagePoint.getId() != null) {
            if (currentPricingStructureLookup.isDataDirty()) {
                if (currentPricingChangeReasonsComponent.getSpecialActions() != null && currentPricingChangeReasonsComponent.getParent() == null) {
                    currentPricingChangeReasonPanel.add(currentPricingChangeReasonsComponent);
                    currentPricingChangeReasonPanel.setVisible(true);
                }
                removeReasons = false;
            }
        }

        if (removeReasons) {
            currentPricingChangeReasonPanel.clear();
            currentPricingChangeReasonPanel.setVisible(false);
        }
        manageThinUnitsAccount();
    }

    @UiHandler("futurePricingStructureLookup")
    void handleFuturePricingStructureChange(ValueChangeEvent<String> event) {
        boolean removeReasons = true;
        LookupListItem selectedPsItem = futurePricingStructureLookup.getSelectedPricingStructureItem();
        handlePricingStructureChange(currentPricingStructureLookup.getSelectedPricingStructureItem(), selectedPsItem);
        // Check if adding a UP only. Hide futurePricingChangeReasonPanel if new UP.
        if (usagePoint != null && usagePoint.getId() != null){
            if (futurePricingStructureLookup.isDataDirty()) {
                if (futurePricingChangeReasonsComponent.getSpecialActions() != null && futurePricingChangeReasonsComponent.getParent() == null) {
                    futurePricingChangeReasonPanel.add(futurePricingChangeReasonsComponent);
                    futurePricingChangeReasonPanel.setVisible(true);
                }
                removeReasons = false;
            }
        }

        if (removeReasons) {
            futurePricingChangeReasonPanel.clear();
            futurePricingChangeReasonPanel.setVisible(false);
        }
    }

    private void handlePricingStructureChange(LookupListItem currentPricingStructureItem,
                                              LookupListItem futurePricingStructureItem) {

        if (currentPricingStructureItem != null) {
            Long currentPSPaymentModeId = null;
            if (currentPricingStructureItem.getExtraInfoMap() != null) {
                currentPSPaymentModeId = Long.valueOf(currentPricingStructureItem.getExtraInfoMap().get("paymentModeId").toString());
            }

            if (futurePricingStructureItem == null) { // a change in current PS
                Long usagePointPaymentModeId;
                if (usagePoint.getUpPricingStructureData().getUpPricingStructure() != null) {
                    usagePointPaymentModeId = usagePoint.getUpPricingStructureData().getPricingStructure().getPaymentModeId();
                    if ((currentPSPaymentModeId == null && usagePointPaymentModeId != null)
                        || (currentPSPaymentModeId != null && usagePointPaymentModeId == null)) {
                        changeInPaymentMode = true;
                    } else if (currentPSPaymentModeId != null) {
                        changeInPaymentMode = !currentPSPaymentModeId.equals(usagePointPaymentModeId);
                    }
                }
            } else {
                if (futurePricingStructureItem.getExtraInfoMap() != null
                    && futurePricingStructureItem.getExtraInfoMap().get("paymentModeId") != null) {
                    Long futurePSPaymentModeId = Long.valueOf(futurePricingStructureItem.getExtraInfoMap()
                            .get("paymentModeId").toString());
                    changeInPaymentMode = !Objects.equals(currentPSPaymentModeId, futurePSPaymentModeId);
                }
            }
        }
    }

    private void manageThinUnitsAccount() {
        Long currentPaymentMode = null;
        Long serviceResourceId = null;
        LookupListItem currentPricingStructureItem = currentPricingStructureLookup.getSelectedPricingStructureItem();
        if (currentPricingStructureItem != null && currentPricingStructureItem.getExtraInfoMap() != null
            && currentPricingStructureItem.getExtraInfoMap().get("paymentModeId") != null) {
            serviceResourceId = Long.parseLong(currentPricingStructureItem.getExtraInfoMap().get("serviceResourceId").toString());
            currentPaymentMode = Long.parseLong(currentPricingStructureItem.getExtraInfoMap().get("paymentModeId").toString());
        }
        isUnitsPricingStructure = currentPaymentMode != null
                                  && PaymentModeE.UNITS == PaymentModeE.fromId(currentPaymentMode);
        if ((currentPaymentMode != null && PaymentModeE.THIN_UNITS == PaymentModeE.fromId(currentPaymentMode))) {
            unitsSymbol = " " + MeterMngClientUtils.getServiceResourceSymbol(serviceResourceId);
            lowBalanceThresholdSymbol.setText(unitsSymbol);

            updateUnitsBalance(usagePoint.getUnitsAccount().getAccountBalance());
            unitsAccountPanel.setVisible(true);
        } else {
            unitsAccountPanel.setVisible(false);
        }
        manageInstallationDateBox();
    }

    public void updateUnitsBalance(BigDecimal unitsBalance) {
        lblAccountBalance.setText(unitsBalance.setScale(1, RoundingMode.HALF_UP).toPlainString() + " " + unitsSymbol);
    }

    @UiHandler("lstbxUpBlockingType")
    void handleBlockingTypeChange(ChangeEvent event) {
        boolean blockingReasons = true;

        if (usagePoint != null) {
            Long selectedValue = new Long(lstbxUpBlockingType.getValue(lstbxUpBlockingType.getSelectedIndex()));
            if (checkBlockingTypeDirty()) {
                blockingReasons = false;
                if (selectedValue.equals(new Long(NOT_BLOCKED))) {
                        if (unBlockingReasonsComponent.getSpecialActions() != null && unBlockingReasonsComponent.getParent() == null) {
                                clearBlockingReasonsComponent();
                                upBlockingReasonPanel.add(unBlockingReasonsComponent);
                        upBlockingReasonPanel.setVisible(true);
                    }
                } else {
                        if (blockingReasonsComponent.getSpecialActions() != null && blockingReasonsComponent.getParent() == null) {
                                clearBlockingReasonsComponent();
                                upBlockingReasonPanel.add(blockingReasonsComponent);
                        upBlockingReasonPanel.setVisible(true);
                    }
                }
            }
        }

        if (blockingReasons) {
            clearBlockingReasonsComponent();
        }
    }

    @UiHandler("dtbxMeterInstallationDate")
    void handleDateBox(ValueChangeEvent<Date> event) {
        //Note RC: When FETCH a meter there IS an upMeterInstall; but when come from Search, there is not.... hence this addition
        if (usagePoint.getUpMeterInstall() != null) {
            usagePoint.getUpMeterInstall().setInstallDate(dtbxMeterInstallationDate.getValue());
        }
    }

    private void addHandlers() {
        if (clientFactory != null) {
            clientFactory.getEventBus().addHandler(MeterModelChangedEvent.TYPE, new MeterModelChangedEventHandler() {
                @Override
                public void processEvent(MeterModelChangedEvent event) {
                    SessionCheckCallback sessionCheckCallback = new SessionCheckCallback() {
                        @Override
                        public void callback(SessionCheckResolution sessionCheckResolution) {
                            refreshPricingStructureLists();
                        }
                    };
                    clientFactory.handleSessionCheckCallback(sessionCheckCallback);
                }
            });
        }

        this.usagePointDisclosurePanel.addOpenHandler(new OpenHandler<DisclosurePanel>() {

            @Override
            public void onOpen(OpenEvent<DisclosurePanel> event) {
                openorclosearrow.setUrl(MediaResourceUtil.getInstance().getOpenedArrowImage().getSafeUri().asString());
                disclosureOpen = Boolean.TRUE;
            }
        });

        this.usagePointDisclosurePanel.addCloseHandler(new CloseHandler<DisclosurePanel>() {

            @Override
            public void onClose(CloseEvent<DisclosurePanel> event) {
                openorclosearrow.setUrl(MediaResourceUtil.getInstance().getClosedArrowImage().getSafeUri().asString());
                disclosureOpen = Boolean.FALSE;
            }
        });


    }

    public void setUsagePointData(UsagePointData theUsagePointData) {
        this.usagePoint = theUsagePointData == null ? new UsagePointData() : theUsagePointData;
        refreshPricingStructureLists();

        if (usagePoint.getServiceLocation() != null) {
            setServiceLocation(usagePoint.getServiceLocation());
        }

        if (usagePoint.getUpMeterInstall() != null && usagePoint.getUpMeterInstall().getInstallDate() != null) {
            installationDateElement.setHelpMsg(MessagesUtil.getInstance().getMessage("usagepoint.field.meter.installation_date.help"));
        }
        actionPermissions();
        mapDataToForm();

    }

    public void refreshCustomerAccountBalanceInUsagePoint(BigDecimal customerBalance) {
        //after input an account balance adjustment simply refresh the account balance - not reload entire page!!
        usagePoint.getCustomerAgreementData().getCustomerAccount().setAccountBalance(customerBalance);
    }

    public void setServiceLocation(LocationData theServiceLocation) {
        this.serviceLocation = theServiceLocation;
    }

    public void setClientFactory(ClientFactory clientFactory) {
        this.clientFactory = clientFactory;
    }

    public void mapDataToForm() {
        populateBlockingTypeLookupListBox();
        usagePointpChargeViewPanel.setVisible(false);
        unitsSymbol = "";
        changeInPaymentMode = false;
        if (usagePoint != null && usagePoint.getId() != null) {
            txtbxUsagepointname.setText(usagePoint.getName());
            if (usagePoint.getMrid() != null && !usagePoint.getMrid().trim().isEmpty()) {
                mridComponent.setMrid(usagePoint.getMrid());
                mridComponent.setIsExternal(usagePoint.isMridExternal());
            }

            PricingStructure pricingStructure = usagePoint.getUpPricingStructureData().getPricingStructure();
            if (pricingStructure != null && usagePoint.getMeterId() != null) {
                boolean isThin = pricingStructure.getPaymentModeId().equals(PaymentModeE.THIN.getId())
                        || pricingStructure.getPaymentModeId().equals(PaymentModeE.THIN_UNITS.getId());
                btnCalc.setEnabled(isThin);
                btnCalc.setVisible(isThin);
                if (usagePoint.getCustomerAgreementId() != null) {
                        usagePointpChargeViewPanel.setVisible(true);
                }
            } else {
                btnCalc.setEnabled(false);
                btnCalc.setVisible(false);
            }
            // lstbxPricingstructure
            resetCurrentPricingStructureLookupSelection();
            resetFuturePricingStructureLookupSelection();

            // If user has no edit permissions
            currentPricingStructureLookup.setEnabled(clientFactory.getUser()
                    .hasPermission(MeterMngStatics.ACCESS_PERMISSION_UP_PRICING_STRUCT));
            futurePricingStructureLookup.setEnabled(clientFactory.getUser()
                    .hasPermission(MeterMngStatics.ACCESS_PERMISSION_UP_PRICING_STRUCT));

            // lstbxUpBlockingType
            resetBlockingTypeLookupListBoxSelection();
            activateBlockingTypeLookupListBox();

            if (serviceLocation != null) {
                serviceLocationComponent.setLocation(serviceLocation);
            }

            Date installationDate = usagePoint.getInstallationDate();
            dtbxMeterInstallationDate.setValue(installationDate);
            manageInstallationDateBox();
            Date activationDate = usagePoint.getActivationDate();
            dtbxMeterActivationDate.setValue(activationDate);
            activationDatePanel.setVisible(activationDate != null);

            headerLabel.setText(MessagesUtil.getInstance().getMessage("usagepoint.title") + ": " + usagePoint.getName());
            chckbxActive.setValue(usagePoint.getRecordStatus().equals(RecordStatus.ACT));
            chckbxActive.setTextAndStyle(chckbxActive.getValue());

            SpecialActionReasonsLog activeStatusUsagePointReasonsLog = usagePoint.getActiveStatusUsagePointReasonsLog();
            if (activeStatusUsagePointReasonsLog != null && !usagePoint.getRecordStatus().equals(RecordStatus.ACT)) {

                HTMLPanel htmlPanel = new HTMLPanel("<div>");

                StringBuilder strBldr = new StringBuilder(
                        MessagesUtil.getInstance().getMessage("usagepoint.deactivate.info.message",
                                new String[] { activeStatusUsagePointReasonsLog.getUsername(), FormatUtil.getInstance()
                                        .formatDate(activeStatusUsagePointReasonsLog.getActionDate()) }));
                String reasonText = activeStatusUsagePointReasonsLog.getReasonText();
                if (reasonText != null && !reasonText.isEmpty()) {
                    strBldr.append(MessagesUtil.getInstance().getMessage("usagepoint.deactivate.info.message.reason",
                                                                         new String[]{reasonText}));
                }
                HTML message = new HTML(strBldr.toString());

                message.addStyleName("warn");
                htmlPanel.add(message);

                deactivateReasonPanel.clear();
                deactivateReasonPanel.add(htmlPanel);
                deactivateReasonPanel.setVisible(true);
            } else {
                deactivateReasonPanel.clear();
                deactivateReasonPanel.setVisible(false);
            }

            if (usagePoint.getBlockingTypeId() != null && usagePoint.getBlockingUsagePointReasonsLog() != null) {
                                HTMLPanel htmlPanel = new HTMLPanel("<div>");
                                StringBuilder strBldr = new StringBuilder(MessagesUtil.getInstance().getMessage("usagepoint.blocking.info.message",
                                                new String[] { usagePoint.getBlockingUsagePointReasonsLog().getUsername(),
                                                                FormatUtil.getInstance().formatDate(usagePoint.getBlockingUsagePointReasonsLog().getActionDate()) }));
                                String reasonText = usagePoint.getBlockingUsagePointReasonsLog().getReasonText();
                                if (reasonText != null && !reasonText.trim().isEmpty()) {
                                        strBldr.append(MessagesUtil.getInstance().getMessage("usagepoint.deactivate.info.message.reason",
                                                        new String[] { reasonText.trim() }));
                                }
                                HTML message = new HTML(strBldr.toString());

                                message.addStyleName("warn");
                                htmlPanel.add(message);

                                upBlockingReasonMsgPanel.clear();
                                upBlockingReasonMsgPanel.add(htmlPanel);
                                upBlockingReasonMsgPanel.setVisible(true);
                        } else {
                                upBlockingReasonMsgPanel.clear();
                                upBlockingReasonMsgPanel.setVisible(false);
                        }

            if (usagePoint.getLastMdcConnectControl() != null) {
                lastMdcConnectControlText.setText(usagePoint.getLastMdcConnectControl().toString());
            } else {
                lastMdcConnectControlText.setText("None");
            }
            getGroupTypes(true);

            if (userCustomFieldsComponentVisible) {
                mapUserCustomFields();
            }

        } else {
            //RC: note code below is almost duplicated in clearUsagePointComponent() - used by remove meter process. Check if changes here are required there!waitDialog
            serviceLocationComponent.setLocation(null);

            if(upComponentValidatePStoMM != null) { // Validation failed so reset Pricing Structure selections
                resetCurrentPricingStructureLookupSelection();
                resetFuturePricingStructureLookupSelection();
            }

            if (usagePoint != null) {
                if (usagePoint.getMeterId() != null) {
                    dtbxMeterInstallationDate.setValue(new Date());
                    enableInstallationDatebox(true);
                }
                activateBlockingTypeLookupListBox();
            }
            headerLabel.setText(MessagesUtil.getInstance().getMessage("usagepoint.add.new"));
            lastMdcConnectControlText.setText("None");
            getGroupTypes(false);

            generateUsagePointName();

            if (userCustomFieldsComponentVisible) {
                userCustomFieldsComponent.clearCustomFields();
            }
            txtbxlowThreshold.setValue(null);
            txtbxNotificationEmail.setText("");
            txtbxNotificationPhone.setText("");
        }
        if (usagePoint != null) {
            UnitsAccount unitsAccount = usagePoint.getUnitsAccount();
            if (unitsAccount == null) {
                usagePoint.setUnitsAccount(new UnitsAccount());
                unitsAccount = usagePoint.getUnitsAccount();
            }
            String accountName = unitsAccount.getAccountName();
            if (accountName == null || accountName.trim().isEmpty()) {
                autoGenerateRefForAccount();
            } else {
                txtbxAccountName.setText(accountName);
            }

            BigDecimal lowBalThreshold = unitsAccount.getLowBalanceThreshold();
            if (lowBalThreshold != null) {
                txtbxlowThreshold.setValue(lowBalThreshold.setScale(1, RoundingMode.HALF_UP));
            } else {
                txtbxlowThreshold.setValue(null);
            }
            txtbxNotificationEmail.setText(unitsAccount.getNotificationEmail());
            txtbxNotificationPhone.setText(unitsAccount.getNotificationPhone());
            if (usagePoint.getInstallationDate() == null && usagePoint.getMeterId() != null) {
                enableInstallationDatebox(true);
            }
        }
    }

    private void autoGenerateRefForAccount() {
        clientFactory.getUsagePointRpc().getAutoGeneratedRef(new ClientCallback<String>() {
            @Override
            public void onSuccess(String result) {
                if (txtbxAccountName.getValue().isEmpty()) {
                    txtbxAccountName.setText("UBA" + result);
                }
            }
        });
    }

    private void activateBlockingTypeLookupListBox(){
        Long blockingTypeId = usagePoint.getBlockingTypeId();
        lstbxUpBlockingType.setEnabled((blockingTypeId == null || hasUnblockPerm) && (blockingTypeId != null || hasBlockPerm));
    }

    private void resetPricingChangeReasonsPanel() {
        boolean removeReasons = true;
        if (currentPricingStructureLookup.isDataDirty()) {
            if (currentPricingChangeReasonsComponent.getSpecialActions() != null && currentPricingChangeReasonsComponent.getParent() == null) {
                currentPricingChangeReasonPanel.add(currentPricingChangeReasonsComponent);
                currentPricingChangeReasonPanel.setVisible(true);
            }
            removeReasons = false;
        }
        if (removeReasons) {
            currentPricingChangeReasonsComponent.clearFields();
            currentPricingChangeReasonPanel.clear();
            currentPricingChangeReasonPanel.setVisible(false);
        }

        removeReasons = true;
        if (futurePricingStructureLookup.isDataDirty()) {
            if (futurePricingChangeReasonsComponent.getSpecialActions() != null && futurePricingChangeReasonsComponent.getParent() == null) {
                futurePricingChangeReasonPanel.add(futurePricingChangeReasonsComponent);
                futurePricingChangeReasonPanel.setVisible(true);
            }
            removeReasons = false;
        }
        if (removeReasons) {
            futurePricingChangeReasonsComponent.clearFields();
            futurePricingChangeReasonPanel.clear();
            futurePricingChangeReasonPanel.setVisible(false);
        }
    }

    private void mapUserCustomFields() {
        userCustomFieldsComponent.mapDataToForm(usagePoint.getCustomVarchar1(),
                usagePoint.getCustomVarchar2(),
                usagePoint.getCustomNumeric1(),
                usagePoint.getCustomNumeric2(),
                usagePoint.getCustomTimestamp1(),
                usagePoint.getCustomTimestamp2());
    }

    public void refreshDates(UsagePointData usagePointData) {
        this.usagePoint = usagePointData;
        dtbxMeterInstallationDate.setValue(usagePoint.getInstallationDate());
        Date activationDate = usagePoint.getActivationDate();
        dtbxMeterActivationDate.setValue(activationDate);
        activationDatePanel.setVisible(activationDate != null);
    }


    @SuppressWarnings({ "rawtypes", "unchecked" })
    private void mapFormToData() {
        if (usagePoint == null) {
            usagePoint = new UsagePointData();
        }

        if (txtbxUsagepointname.getText().isEmpty()) {
            usagePoint.setName(null);
        } else {
            usagePoint.setName(txtbxUsagepointname.getText());
        }

        if (clientFactory.getUser().hasPermission(MeterMngStatics.ACCESS_PERMISSION_UP_PRICING_STRUCT)) {
            LookupListItem currentItem = currentPricingStructureLookup.getSelectedPricingStructureItem();
            if (currentItem != null) {
                UpPricingStructure upPricingStructure = new UpPricingStructure();
                upPricingStructure.setPricingStructureId(Long.valueOf(currentItem.getValue()));
                upPricingStructure.setUsagePointId(usagePoint.getId());
                upPricingStructure.setStartDate((usagePoint.getId() == null ? new Date() : usagePoint.getActivationDate()));
                UpPricingStructureData currentData = usagePoint.getUpPricingStructureData();
                currentData.setUpPricingStructure(upPricingStructure);
                currentData.setPricingStructure(new PricingStructure());
                currentData.getPricingStructure().setId(Long.valueOf(currentItem.getValue()));
                currentData.getPricingStructure().setName(currentItem.getText());
            }
            LookupListItem fututeItem = futurePricingStructureLookup.getSelectedPricingStructureItem();
            if (fututeItem != null) {
                UpPricingStructure upPricingStructure = new UpPricingStructure();
                upPricingStructure.setPricingStructureId(Long.valueOf(fututeItem.getValue()));
                upPricingStructure.setUsagePointId(usagePoint.getId());
                upPricingStructure.setStartDate(futurePricingStructureStartDate.getValue());
                UpPricingStructureData futureData = usagePoint.getUpPricingStructureData().getFutureUpPricingStructureData();
                if (futureData == null) {
                    usagePoint.getUpPricingStructureData().setFutureUpPricingStructureData(new UpPricingStructureData());
                    futureData = usagePoint.getUpPricingStructureData().getFutureUpPricingStructureData();
                    futureData.setPricingStructure(new PricingStructure());
                }
                futureData.setUpPricingStructure(upPricingStructure);
                futureData.getPricingStructure().setId(Long.valueOf(fututeItem.getValue()));
                futureData.getPricingStructure().setName(fututeItem.getText());
            }
        }

        serviceLocationComponent.mapFormToData();
        serviceLocation = serviceLocationComponent.getLocation();

        if (chckbxActive.isEnabled() && chckbxActive.getValue()) {
            usagePoint.setRecordStatus(RecordStatus.ACT);
        } else {
            usagePoint.setRecordStatus(RecordStatus.DAC);
        }

        for (int j=0; j<groupTypesPanel.getWidgetCount(); j++) {
            if (groupTypesPanel.getWidget(j) instanceof SuggestBoxTree) {
                UpGenGroupLinkData upgdata = null;
                SuggestBoxTree<Long, SelectionDataItem, GroupTypeTreeInfo> sbt = (SuggestBoxTree)groupTypesPanel.getWidget(j);
                SelectionDataItemSuggestBoxTreeFactory factory = (SelectionDataItemSuggestBoxTreeFactory) sbt.getFactory();

                for (Long t : factory.getGroupTypeIds()) {
                    upgdata = usagePoint.getSelectedGroupByGroupType(t);
                    if (upgdata != null) {
                        break;
                    }
                }

                SelectionDataItem item = sbt.getCurrentSelectedItem();

                if (item == null || !SelectionDataItem.isActualGroupId(item.getActualId())) {
                    // If item is null or not an actual group ID, set genGroupId to null
                    if (upgdata != null) {
                        upgdata.setGenGroupId(null);
                        if (usagePoint.getUpgengroups() == null) {
                            usagePoint.setUpgengroups(new HashMap<Long, UpGenGroupLinkData>());
                        }
                        usagePoint.getUpgengroups().put(upgdata.getGroupTypeId(), upgdata);
                    }
                } else {
                    Long selectedGroup = item.getActualId();
                    if (upgdata == null) {
                        upgdata = new UpGenGroupLinkData();
                        upgdata.setGroupTypeId(sbt.getTreeInfo().getGroupTypeId());
                        upgdata.setUsagePointId(null);
                    }
                    upgdata.setGenGroupId(selectedGroup);
                    if (usagePoint.getUpgengroups() == null) {
                        usagePoint.setUpgengroups(new HashMap<Long, UpGenGroupLinkData>());
                    }
                    usagePoint.getUpgengroups().put(upgdata.getGroupTypeId(), upgdata);
                }
            }
        }

        UpMeterInstall upMeterInstall = usagePoint.getUpMeterInstall();
        boolean hasNewInstallDate = isInstallDateDirty();
        if (usagePoint.getMeterData() != null) {
            // only if attaching a usagepoint to a meter
            if (upMeterInstall == null || upMeterInstall.getInstallDate() == null) {
                if (upMeterInstall == null) {
                    upMeterInstall = new UpMeterInstall();
                    usagePoint.setUpMeterInstallAndPrevious(upMeterInstall);
                }
                upMeterInstall.setInstallDate(dtbxMeterInstallationDate.getValue());
            }
            Date installationDate = dtbxMeterInstallationDate.getValue();
            if (installationDate == null) {
                installationDate = upMeterInstall.getInstallDate();
            }
            usagePoint.setInstallationDate(installationDate);

            //shift inserting into activation date if still null to the actual save, to avoid user changing install date on the confirm message and leaving the activation date behind
            usagePoint.setChangeActivationDate(willUpdateActivationDate(hasNewInstallDate, usagePoint));
        }

        if (userCustomFieldsComponentVisible) {
            userCustomFieldsComponent.mapFormToData(usagePoint);
        }

        if (deactivateReasonsComponent.isAttached() && deactivateReasonPanel.isVisible()) {
            usagePoint.setActiveStatusUsagePointReasonsLog(deactivateReasonsComponent.getLogEntry(true));
        }

        if (activateReasonsComponent.isAttached() && activateReasonPanel.isVisible()) {
            usagePoint.setActiveStatusUsagePointReasonsLog(activateReasonsComponent.getLogEntry(true));
        }

        if (currentPricingChangeReasonsComponent.isAttached() && currentPricingChangeReasonPanel.isVisible()) {
            usagePoint.setChangePricingStructureReasonsLogCurrentPs(currentPricingChangeReasonsComponent.getLogEntry());
        }

        if (futurePricingChangeReasonsComponent.isAttached() && futurePricingChangeReasonPanel.isVisible()) {
            usagePoint.setChangePricingStructureReasonsLogFuturePs(futurePricingChangeReasonsComponent.getLogEntry());
        }

        // Blocking changes
        boolean hasUpdate = checkBlockingTypeDirty();

        if (hasUpdate) {
            Long defaultValue = new Long(NOT_BLOCKED);
            Long selectedValue = Long.valueOf(lstbxUpBlockingType.getValue(lstbxUpBlockingType.getSelectedIndex()));
            if (clientFactory.getUser().hasPermission(MeterMngStatics.EDIT_PERMISSION_MM_USAGE_POINT_BLOCK)
                    || clientFactory.getUser().hasPermission(MeterMngStatics.EDIT_PERMISSION_MM_USAGE_POINT_UNBLOCK)) {
                if (selectedValue.equals(defaultValue)) {
                    usagePoint.setBlockingTypeId(null);
                    usagePoint.setBlockReasonLogId(null);
                } else {
                    usagePoint.setBlockingTypeId(selectedValue);
                }

                if (blockingReasonsComponent.isAttached() && upBlockingReasonPanel.isVisible()) {
                    usagePoint.setBlockingUsagePointReasonsLog(blockingReasonsComponent.getLogEntry(true));
                } else if (unBlockingReasonsComponent.isAttached() && upBlockingReasonPanel.isVisible()) {
                    usagePoint.setBlockingUsagePointReasonsLog(unBlockingReasonsComponent.getLogEntry(true));
                } else {
                    usagePoint.setBlockingUsagePointReasonsLog(null);
                }
            }
        }

        boolean unitsAccountChanged = false;
        UnitsAccount unitsAccount = usagePoint.getUnitsAccount();
        if (unitsAccount == null) {
            unitsAccount = new UnitsAccount();
            unitsAccountChanged = true;
        }

        String accountName = getEmptyTextAsNull(txtbxAccountName.getText());
        if (!Objects.equals(accountName, getEmptyTextAsNull(unitsAccount.getAccountName()))) {
            unitsAccountChanged = true;
            unitsAccount.setAccountName(accountName);
        }

        BigDecimal lowBalanceThreshold = txtbxlowThreshold.getValue();
        if (!Objects.equals(lowBalanceThreshold, unitsAccount.getLowBalanceThreshold())) {
            unitsAccountChanged = true;
            unitsAccount.setLowBalanceThreshold(lowBalanceThreshold);
        }

        String notificationEmail = getEmptyTextAsNull(txtbxNotificationEmail.getText());
        if (!Objects.equals(notificationEmail, getEmptyTextAsNull(unitsAccount.getNotificationEmail()))) {
            unitsAccountChanged = true;
            unitsAccount.setNotificationEmail(notificationEmail);
        }

        String notificationPhone = getEmptyTextAsNull(txtbxNotificationPhone.getText());
        if (!Objects.equals(notificationPhone, getEmptyTextAsNull(unitsAccount.getNotificationPhone()))) {
            unitsAccountChanged = true;
            unitsAccount.setNotificationPhone(notificationPhone);
        }

        usagePoint.setUnitsAccountChanged(unitsAccountChanged);
        usagePoint.setUnitsAccount(unitsAccount);
        usagePoint.setMrid(mridComponent.getMrid());
        usagePoint.setMridExternal(mridComponent.isExternal());
        usagePoint.setServiceLocation(serviceLocation);
        usagePoint.setUsagepointLocation(null);
    }

    private String getEmptyTextAsNull(String text) {
        if (text == null || text.isEmpty()) {
            return null;
        }
        return text;
    }

    private boolean willUpdateActivationDate(boolean hasNewInstallDate, UsagePointData up) {
        return (hasNewInstallDate && isUnitsPricingStructure &&
                up.getRecordStatus() == RecordStatus.ACT &&
                up.getActivationDate() != null &&
                up.hasNoTransactions());
    }

    private boolean validate(LookupListItem currentPricingStructure, LookupListItem futurePricingStructure) {
        boolean validated = true;

        UsagePoint theusagepoint = new UsagePoint();
        if (txtbxUsagepointname.getText().trim().isEmpty()) {
            validated = false;
        } else {
            theusagepoint.setName(txtbxUsagepointname.getText());
        }
        if (!validateUpNameRegex
                && !ClientValidatorUtil.getInstance().validateField(theusagepoint, "name", usagePointNameElement)) {
            validated = false;
        } else if (validateUpNameRegex
                && !MeterMngClientUtils.validateUserInterfaceComponent(usagePointNameElement,
                        userInterfaceFields.get(UserInterfaceFormFields.UP_NAME), txtbxUsagepointname)) {
            validated = false;
        }
        theusagepoint.setMeterId(usagePoint.getMeterId());

        theusagepoint.setMrid(mridComponent.getMrid());
        if (!ClientValidatorUtil.getInstance().validateField(theusagepoint, "mrid", mridComponent.getTxtbxMridElement())) {
            validated = false;
        }
        if (!mridComponent.validate()) {
            validated = false;
        }

        String currentPricingStructureIdStr = (currentPricingStructure == null ? null : currentPricingStructure.getValue());
        Date installationDate = dtbxMeterInstallationDate.getValue();
        Date currentStartDate = (currentPricingStructureStartDate.getValue() == null ? installationDate : currentPricingStructureStartDate.getValue());
        if (currentStartDate == null && installationDate == null) {
            currentStartDate = new Date();
        }

        if (currentPricingStructureIdStr == null) {
            validated = false;
            currentPSElement.setErrorMsg(MessagesUtil.getInstance().getMessage("usagepoint.ps.required"));
        }

        String futurePSIdStr = (futurePricingStructure == null ? null : futurePricingStructure.getValue());
        Date futureStartDate = futurePricingStructureStartDate.getValue();
        if (futureStartDate != null && futurePSIdStr == null) {
            validated = false;
            futurePSElement.setErrorMsg(MessagesUtil.getInstance().getMessage("usagepoint.ps.required"));
        } else if (futureStartDate == null && futurePSIdStr != null) {
            validated = false;
            futurePSStartDateElement.setErrorMsg(MessagesUtil.getInstance().getMessage("usagepoint.ps.start.date.error"));
        }

        if (futurePSIdStr != null && futurePSIdStr.equals(currentPricingStructureIdStr)) {
            validated = false;
            futurePSElement.setErrorMsg(MessagesUtil.getInstance().getMessage("usagepoint.ps.future.required"));
        }

        if (futureStartDate != null && futurePSIdStr != null) {
            Date futurePSFirstTariffStartDate = (Date)futurePricingStructure.getExtraInfoMap().get("firstTariffStartDate");
            if (futureStartDate.before(new Date()) || futureStartDate.before(futurePSFirstTariffStartDate)) {
                validated = false;
                futurePSStartDateElement.setErrorMsg(MessagesUtil.getInstance().getMessage("usagepoint.ps.start.date.error"));
            }
        }
        if (currentStartDate != null && futureStartDate != null && (futureStartDate.before(currentStartDate) || futureStartDate.equals(currentStartDate))) {
            validated = false;
            futurePSStartDateElement.setErrorMsg(MessagesUtil.getInstance().getMessage("usagepoint.ps.start.date.error"));
        }

        if (clientFactory.getUser().hasPermission(MeterMngStatics.ACCESS_PERMISSION_UP_PRICING_STRUCT) &&
                (currentPricingStructureIdStr == null || currentPricingStructureIdStr.equals("-1l"))) {
            currentPSElement.showErrorMsg(MessagesUtil.getInstance().getMessage("usagepoint.pricingstructure.required"));
            validated = false;
        }

        if (!serviceLocationComponent.validate()) {
            validated = false;
        }
        if (physicalAddressPanel.isVisible()) {
            if (!serviceLocationComponent.validate()) {
                validated = false;
            }
            if (!MeterMngClientUtils.validateUserInterfaceComponent(serviceLocationComponent.erfnumberElement,
                    userInterfaceFields.get(UserInterfaceFormFields.UP_ERF_NUMBER),
                    serviceLocationComponent.txtbxErfNumber)) {
                validated = false;
            }
            if (!MeterMngClientUtils.validateUserInterfaceComponent(serviceLocationComponent.streetnumberElement,
                    userInterfaceFields.get(UserInterfaceFormFields.UP_STREET_NUM),
                    serviceLocationComponent.txtbxStreetNumber)) {
                validated = false;
            }
            if (!MeterMngClientUtils.validateUserInterfaceComponent(serviceLocationComponent.buildingNameElement,
                    userInterfaceFields.get(UserInterfaceFormFields.UP_BUILDING_NAME),
                    serviceLocationComponent.txtbxBuildingName)) {
                validated = false;
            }
            if (!MeterMngClientUtils.validateUserInterfaceComponent(serviceLocationComponent.suiteNumberElement,
                    userInterfaceFields.get(UserInterfaceFormFields.UP_SUITE_NUM),
                    serviceLocationComponent.txtbxSuiteNumber)) {
                validated = false;
            }
            if (!serviceLocationComponent.validateUserInterfaceComponent(serviceLocationComponent.address1Panel,
                    userInterfaceFields.get(UserInterfaceFormFields.UP_ADDRESS_1),
                    serviceLocationComponent.txtbxAddress1, serviceLocationComponent.address1ErrorLabel)) {
                validated = false;
            }
            if (!serviceLocationComponent.validateUserInterfaceComponent(serviceLocationComponent.address2Panel,
                    userInterfaceFields.get(UserInterfaceFormFields.UP_ADDRESS_2),
                    serviceLocationComponent.txtbxAddress2, serviceLocationComponent.address2ErrorLabel)) {
                validated = false;
            }
            if (!serviceLocationComponent.validateUserInterfaceComponent(serviceLocationComponent.address3Panel,
                    userInterfaceFields.get(UserInterfaceFormFields.UP_ADDRESS_3),
                    serviceLocationComponent.txtbxAddress3, serviceLocationComponent.address3ErrorLabel)) {
                validated = false;
            }
            if (!MeterMngClientUtils.validateUserInterfaceComponent(serviceLocationComponent.latitudeElement,
                    userInterfaceFields.get(UserInterfaceFormFields.UP_LATITUDE),
                    serviceLocationComponent.txtbxLatitude)) {
                validated = false;
            }
            if (!MeterMngClientUtils.validateUserInterfaceComponent(serviceLocationComponent.longitudeElement,
                    userInterfaceFields.get(UserInterfaceFormFields.UP_LONGITUDE),
                    serviceLocationComponent.txtbxLongitude)) {
                validated = false;
            }
        }

        if (validated && !areGroupsAllSelected()) {
            validated = false;
        }
        //allow save for when adding a new meter to an unattached UP but don't check the installation date if simply updating an unattached UP
        //checking parentworkspace getMeterData because have potentially not yet refreshed this components meterData....
        //if have usagePointData and have meterData, but no upMeterInstall --> this is a new installation : must give installation date
        if (usagePointWorkspaceView.getUsagePoint() != null && usagePoint != null  // have a usage point
                && (usagePointWorkspaceView.getUsagePoint().getMeterData() != null)   //have a meterData
                && (usagePoint.getUpMeterInstall() == null || usagePoint.getUpMeterInstall().getInstallDate() == null)  //up meter Install null or not yet an installation date so a new installation!
                ) {
            if (installationDate == null && dtbxMeterInstallationDate.getTextBox().getValue().isEmpty()) {
                installationDateElement.showErrorMsg(MessagesUtil.getInstance().getMessage("usagepoint.installation.date.required"));
                enableInstallationDatebox(true);
                validated = false;
            }
        }

        if (installationDate != null) {
            logger.info("UsagePOintComponent: validate dtbxMeterInstallationDate.getValue()= " + installationDate);
        } else {
            logger.info("UsagePOintComponent: validate dtbxMeterInstallationDate.getValue()= NULL");
        }

        if (dtbxMeterInstallationDate.isEnabled()) {
            String installationDateText = dtbxMeterInstallationDate.getTextBox().getValue();
            if (!MeterMngClientUtils.isValidDate(installationDateText)) {
                installationDateElement
                        .showErrorMsg(MessagesUtil.getInstance().getMessage("error.field.installdate.invalid",
                                new String[] { FormatUtil.getInstance().getDateTimeFormat() }));
                enableInstallationDatebox(true);
                validated = false;
            } else if (installationDate.after(new Date())
                    && (!upAllowFutureInstallationDates || !isUnitsPricingStructure)) {
                installationDateElement
                        .showErrorMsg(MessagesUtil.getInstance().getMessage("error.field.installdate.future"));
                enableInstallationDatebox(true);
                validated = false;
            }
        }

        if (userCustomFieldsComponentVisible) {
            if (!userCustomFieldsComponent.validateCustomFields()) {
                validated = false;
            }
        }

        if (deactivateReasonsComponent.isAttached()
                && deactivateReasonPanel.isVisible()) {
            boolean isvalid = deactivateReasonsComponent.validate();
            if (!isvalid) {
                validated = isvalid;
            }
        }
        if (activateReasonsComponent.isAttached()
                && activateReasonPanel.isVisible()) {
            boolean isvalid = activateReasonsComponent.validate();
            if (!isvalid) {
                validated = isvalid;
            }
        }

        if (currentPricingChangeReasonsComponent.isAttached()
                && currentPricingChangeReasonPanel.isVisible()) {
            boolean isvalid = currentPricingChangeReasonsComponent.validate();
            if (!isvalid) {
                validated = isvalid;
            }
        }
        if (futurePricingChangeReasonsComponent.isAttached()
                && futurePricingChangeReasonPanel.isVisible()) {
            boolean isvalid = futurePricingChangeReasonsComponent.validate();
            if (!isvalid) {
                validated = isvalid;
            }
        }

        if (blockingReasonsComponent.isAttached()
                && upBlockingReasonPanel.isVisible()) {
            boolean isvalid = blockingReasonsComponent.validate();
            if (!isvalid) {
                validated = isvalid;
            }
        }

        if (unBlockingReasonsComponent.isAttached()
                && upBlockingReasonPanel.isVisible()) {
            boolean isvalid = unBlockingReasonsComponent.validate();
            if (!isvalid) {
                validated = isvalid;
            }
        }

        UnitsAccount unitsAccount = new UnitsAccount();
        unitsAccount.setAccountName(txtbxAccountName.getText());
        unitsAccount.setLowBalanceThreshold(txtbxlowThreshold.getValue());
        unitsAccount.setNotificationEmail(txtbxNotificationEmail.getText());
        unitsAccount.setNotificationPhone(txtbxNotificationPhone.getText());

        if (lowBalanceThresholdElement.isVisible()
                && !MeterMngClientUtils.validateUserInterfaceComponent(lowBalanceThresholdElement,
                        userInterfaceFields.get(UserInterfaceFormFields.UNITS_ACC_LOW_BAL_THRESHOLD),
                        txtbxlowThreshold)) {
            validated = false;
        }

        if (notificationEmailElement.isVisible()) {
            FormFields formField = userInterfaceFields.get(UserInterfaceFormFields.UNITS_ACC_NOTIF_EMAIL);
            String text = txtbxNotificationEmail.getText();
            boolean tempValidated = true;
            if (!text.trim().isEmpty()) {
                if (formField.getValidationRegex() == null && !ValidateUtil.isValidEmailList(text)) {
                    notificationEmailElement.showErrorMsg(MessagesUtil.getInstance().getMessage("error.field.email1"));
                    validated = false;
                }
            }
            if (!MeterMngClientUtils.validateUserInterfaceComponent(notificationEmailElement,
                    formField, txtbxNotificationEmail)) {
                tempValidated = false;
            }
            if (!tempValidated) {
                validated = false;
            }
        }

        if (notificationPhoneElement.isVisible()) {
            FormFields formField = userInterfaceFields.get(UserInterfaceFormFields.UNITS_ACC_NOTIF_PHONE);
            String text = txtbxNotificationPhone.getText();
            boolean tempValidated = true;
            if (!text.trim().isEmpty()) {
                if (formField.getValidationRegex() == null && !ValidateUtil.isValidTelephoneNumberList(text)) {
                    notificationPhoneElement.showErrorMsg(MessagesUtil.getInstance().getMessage("error.field.phone"));
                    validated = false;
                }
            }
            if (!MeterMngClientUtils.validateUserInterfaceComponent(notificationPhoneElement,
                    formField, txtbxNotificationPhone)) {
                tempValidated = false;
            }
            if (!tempValidated) {
                validated = false;
            }
        }

        return validated;
    }

    @Override
    public void onResize() {
        new Timer() {
            @Override
            public void run() {
                usagePointDisclosurePanel.setWidth("100%");
            }
        }.schedule(100);

    }

    private void resetCurrentPricingStructureLookupSelection() {
        UpPricingStructure upPricingStructure = usagePoint.getUpPricingStructureData().getUpPricingStructure();
        if (upPricingStructure != null) {
            currentPricingStructureStartDate.setValue(upPricingStructure.getStartDate());
            currentPricingStructureLookup.setSavedPricingStructureId(upPricingStructure.getPricingStructureId());
        } else {
            currentPricingStructureLookup.clearSelection();
            currentPricingStructureStartDate.setValue(null);
        }
    }

    private void resetFuturePricingStructureLookupSelection() {
        UpPricingStructureData upPricingStructureData = usagePoint.getUpPricingStructureData().getFutureUpPricingStructureData();
        if (upPricingStructureData != null && upPricingStructureData.getUpPricingStructure() != null) {
            UpPricingStructure upPricingStructure = upPricingStructureData.getUpPricingStructure();
            futurePricingStructureStartDate.setValue(upPricingStructure.getStartDate());
            futurePricingStructureLookup.setSavedPricingStructureId(upPricingStructure.getPricingStructureId());
        } else {
            futurePricingStructureLookup.clearSelection();
            futurePricingStructureStartDate.setValue(null);
        }
    }

    public void resetBothPricingStructureLookupListBoxSelections() {
        resetCurrentPricingStructureLookupSelection();
        resetFuturePricingStructureLookupSelection();
    }

    public void populateBlockingTypeLookupListBox() {
        ClientCallback<ArrayList<LookupListItem>> lookupSvcAsyncCallback = new ClientCallback<ArrayList<LookupListItem>>() {
            @Override
            public void onSuccess(ArrayList<LookupListItem> result) {
                lstbxUpBlockingType.clear();
                if (hasUnblockPerm || usagePoint.getBlockingTypeId() == null) {
                    lstbxUpBlockingType.addItem(MessagesUtil.getInstance().getMessage("usagepoint.field.blocking.type.default"), "-1");
                }
                if (result != null) {
                    for (LookupListItem lookupListItem : result) {
                        if (hasUnblockPerm && !hasBlockPerm && usagePoint.getBlockingTypeId() != null &&
                                !Long.valueOf(lookupListItem.getValue()).equals(usagePoint.getBlockingTypeId())) {
                            continue;
                        }
                        lstbxUpBlockingType.addItem(lookupListItem.getText(), lookupListItem.getValue());
                    }
                    resetBlockingTypeLookupListBoxSelection();
                }
                clearBlockingReasonsComponent();
            }
        };
        if (hasBlockPerm || hasUnblockPerm || usagePoint.getBlockingType() == null) {
            clientFactory.getLookupRpc().getBlockingTypeLookupList(lookupSvcAsyncCallback);
        }
    }

    private void resetBlockingTypeLookupListBoxSelection() {
        if (usagePoint.getBlockingTypeId() != null) {
            for (int i = 0; i < lstbxUpBlockingType.getItemCount(); i++) {
                if (Long.valueOf(lstbxUpBlockingType.getValue(i)).equals(usagePoint.getBlockingTypeId())) {
                    lstbxUpBlockingType.setSelectedIndex(i);
                    break;
                }
            }
        } else {
            lstbxUpBlockingType.setSelectedIndex(0);
        }
    }

    public void reloadUsagePointGroupTypes(){
        getGroupTypes(false);
    }

    private void getGroupTypes(final boolean select) {
        clientFactory.getGroupRpc().getGroupTypes(false, false, new ClientCallback<ArrayList<SelectionDataItem>>() {
            @Override
            public void onSuccess(ArrayList<SelectionDataItem> result) {
                //Display each group type in a selection widget
                logger.info("Got selectionData: "+result.size());
                Collections.sort(result, new Comparator<SelectionDataItem>() {
                    public int compare(SelectionDataItem o1, SelectionDataItem o2) {
                        if(o1.getLayoutOrder() == null && o2.getLayoutOrder() == null) {
                            return o1.getName().compareTo(o2.getName());
                        } else if (o1.getLayoutOrder() == null && o2.getLayoutOrder() != null) {
                            return 1;
                        } else if (o2.getLayoutOrder() == null && o1.getLayoutOrder() != null) {
                            return -1;
                        }

                        return o1.getLayoutOrder().compareTo(o2.getLayoutOrder());
                    }
                });

                for (SelectionDataItem item : result) {
                    boolean addit = true;
                    for (int j = 0; j < groupTypesPanel.getWidgetCount(); j++) {
                        if (groupTypesPanel.getWidget(j) instanceof SuggestBoxTree) {
                            SuggestBoxTree<Long, SelectionDataItem, GroupTypeTreeInfo> sbt = (SuggestBoxTree) groupTypesPanel.getWidget(j);
                            if (item.getActualId().equals(sbt.getTreeInfo().getGroupTypeId())) {
                                addit = false;
                                logger.info("Selected group in widget: " + sbt.getCurrentSelectedItem());
                                break;
                            }
                        }
                    }
                    if (addit) {
                        boolean mustSelectLastLevel = item.isRequired();
                        SuggestBoxTree<Long, SelectionDataItem, GroupTypeTreeInfo> sbt = SelectionDataItemSuggestBoxTree.createSuggestBoxTree(item, mustSelectLastLevel, clientFactory, hasDirtyData, true);
                        groupTypesPanel.add(sbt);
                    }
                }

                if (select) {
                    setSelectedGroups();
                }
                logger.info("Groups complete");
            }
        });

    }

    @SuppressWarnings("unchecked")
    private void setSelectedGroups() {
        if (usagePoint.getSelectedGroups() != null && !usagePoint.getSelectedGroups().isEmpty()) {
            for (int j=0; j<groupTypesPanel.getWidgetCount(); j++) {
                if (groupTypesPanel.getWidget(j) instanceof SuggestBoxTree) {
                    UpGenGroupLinkData uData = null;
                    SuggestBoxTree<Long, SelectionDataItem, GroupTypeTreeInfo> sbt = (SuggestBoxTree<Long, SelectionDataItem, GroupTypeTreeInfo>)groupTypesPanel.getWidget(j);
                    SelectionDataItemSuggestBoxTreeFactory factory = (SelectionDataItemSuggestBoxTreeFactory) sbt.getFactory();
                    sbt.clear();

                    for (Long t : factory.getGroupTypeIds()) {
                        uData = usagePoint.getSelectedGroupByGroupType(t);
                        if (uData != null) {
                            ArrayList<Long> depthList = new ArrayList<Long>(uData.getDepthList());
                            if(t != sbt.getTreeInfo().getGroupTypeId()) {
                                // this is a sub group type node
                                depthList.add(0, t);
                            }
                            sbt.setValue(depthList);
                            break;
                        }
                    }
                }
            }
        } else {
            //ex mapDataToForm --> after a save, if the groups were incomplete, did NOT save the usage Point groups, clear screen here
            clearUsagePointGroups();
        }
    }

    @SuppressWarnings("unchecked")
    public boolean areGroupsAllSelected() {
        boolean validated = true;

        for (int j=0; j<groupTypesPanel.getWidgetCount(); j++) {
            if (groupTypesPanel.getWidget(j) instanceof SuggestBoxTree) {
                SuggestBoxTree<Long, SelectionDataItem, GroupTypeTreeInfo> sbt = (SuggestBoxTree<Long, SelectionDataItem, GroupTypeTreeInfo>) groupTypesPanel.getWidget(j);
                if (!sbt.getTreeInfo().isRequired()) {
                    continue;
                }
                SelectionDataItem selectedItem = sbt.getCurrentSelectedItem();
                if (selectedItem == null || ! SelectionDataItem.isActualGroupId(selectedItem.getActualId())) {
                    sbt.setError(MessagesUtil.getInstance().getMessage("usagepoint.group.required"));
                    validated = false;
                }
            }
        }
        return validated;
    }

    public void setOpen(boolean isOpen) {
        if (disclosureOpen == null) {
            usagePointDisclosurePanel.setOpen(isOpen);
        } else {
            usagePointDisclosurePanel.setOpen(disclosureOpen);
        }
    }

    @SuppressWarnings("rawtypes")
    private void clearErrorMessages() {
        usagePointNameElement.clearErrorMsg();
        currentPSElement.clearErrorMsg();
        futurePSElement.clearErrorMsg();
        currentPSStartDateElement.clearErrorMsg();
        futurePSStartDateElement.clearErrorMsg();
        serviceLocationComponent.clearErrors();

        deactivateReasonsComponent.clearErrorMessages();
        currentPricingChangeReasonsComponent.clearErrorMessages();
        futurePricingChangeReasonsComponent.clearErrorMessages();
        blockingReasonsComponent.clearErrorMessages();

        for (int j=0; j<groupTypesPanel.getWidgetCount(); j++) {
            if (groupTypesPanel.getWidget(j) instanceof SuggestBoxTree) {
                ((SuggestBoxTree)groupTypesPanel.getWidget(j)).clearError();
            }
        }
        installationDateElement.clearErrorMsg();

        if (userCustomFieldsComponentVisible) {
            userCustomFieldsComponent.clearErrorMessages();
        }
        lowBalanceThresholdElement.clearErrorMsg();
        notificationEmailElement.clearErrorMsg();
        notificationPhoneElement.clearErrorMsg();
        mridComponent.clearErrorMsg();
    }

    private void clearBlockingReasonsComponent() {
        upBlockingReasonPanel.clear();
        upBlockingReasonPanel.setVisible(false);
        blockingReasonsComponent.clearFields();
        blockingReasonsComponent.clearErrorMessages();
        unBlockingReasonsComponent.clearFields();
        unBlockingReasonsComponent.clearErrorMessages();
    }

    private void configureCustomFields() {
        boolean hasCustomFields = false;
        if (customFieldList != null && !customFieldList.isEmpty()) {
            hasCustomFields = userCustomFieldsComponent.configureCustomFields(customFieldList, "usagepoint");
        }

        if (customFieldList == null || customFieldList.isEmpty() || !hasCustomFields) {
            removeUserCustomFieldsComponent();
            return;
        }

        userCustomFieldsComponent.setVisible(true);
        userCustomFieldsComponentVisible = true;

    }

    public void removeUserCustomFieldsComponent() {
        logger.info("Custom AppSettings are NULL or empty!!" );
        userCustomFieldsComponent.setVisible(false);
        userCustomFieldsComponentVisible = false;
    }

    public void addUserCustomFieldsComponent() {
        userCustomFieldsComponent.setVisible(true);
        userCustomFieldsComponentVisible = true;
        mapUserCustomFields();
    }

    public UserCustomFieldsComponent getUserCustomFieldsComponent() {
        return userCustomFieldsComponent;
    }

    public LocationComponent getLocationComponent() {
        return serviceLocationComponent;
    }

    @Override
    public void setHistoryVisibilityMap(Map<String, CustomFieldDto> result) {
    }

    private void addFieldHandlers() {
        // Note chckbxActive specially handled in other section
        txtbxUsagepointname.addChangeHandler(new FormDataChangeHandler(hasDirtyData));
        lstbxUpBlockingType.addChangeHandler(new FormDataChangeHandler(hasDirtyData));
        dtbxMeterInstallationDate.addValueChangeHandler(new FormDataValueChangeHandler<Date>(hasDirtyData));
        currentPricingStructureStartDate.addValueChangeHandler(new FormDataValueChangeHandler<Date>(hasDirtyData));
        futurePricingStructureStartDate.addValueChangeHandler(new FormDataValueChangeHandler<Date>(hasDirtyData));
    }

    public UsagePointData getUsagePointData() {
        return usagePoint;
    }

    public void updateUserInterfaceComponentSettings(Map<String, FormFields> userInterfaceFields) {
        this.userInterfaceFields = userInterfaceFields;
        validateUpNameRegex = userInterfaceFields.get(UserInterfaceFormFields.UP_NAME).getValidationRegex() != null;
        // After the async callback for userInterfaceFields we can safely generate UPName knowing the value of the regex.
        generateUsagePointName();

        MeterMngClientUtils.setUserInterfaceComponentPreferences(
                userInterfaceFields.get(UserInterfaceFormFields.UNITS_ACC_LOW_BAL_THRESHOLD),
                serviceLocationComponent.erfnumberElement);
        MeterMngClientUtils.setUserInterfaceComponentPreferences(
                userInterfaceFields.get(UserInterfaceFormFields.UNITS_ACC_NOTIF_EMAIL),
                serviceLocationComponent.erfnumberElement);
        MeterMngClientUtils.setUserInterfaceComponentPreferences(
                userInterfaceFields.get(UserInterfaceFormFields.UNITS_ACC_NOTIF_PHONE),
                serviceLocationComponent.erfnumberElement);

        MeterMngClientUtils.setUserInterfaceComponentPreferences(
                userInterfaceFields.get(UserInterfaceFormFields.UP_ERF_NUMBER),
                serviceLocationComponent.erfnumberElement);
        MeterMngClientUtils.setUserInterfaceComponentPreferences(
                userInterfaceFields.get(UserInterfaceFormFields.UP_STREET_NUM),
                serviceLocationComponent.streetnumberElement);
        MeterMngClientUtils.setUserInterfaceComponentPreferences(
                userInterfaceFields.get(UserInterfaceFormFields.UP_BUILDING_NAME),
                serviceLocationComponent.buildingNameElement);
        MeterMngClientUtils.setUserInterfaceComponentPreferences(
                userInterfaceFields.get(UserInterfaceFormFields.UP_SUITE_NUM),
                serviceLocationComponent.suiteNumberElement);

        serviceLocationComponent.setUserInterfaceComponentPreferences(
                userInterfaceFields.get(UserInterfaceFormFields.UP_ADDRESS_1), serviceLocationComponent.address1Panel,
                serviceLocationComponent.address1RequiredLabel, serviceLocationComponent.address1ErrorLabel);
        serviceLocationComponent.setUserInterfaceComponentPreferences(
                userInterfaceFields.get(UserInterfaceFormFields.UP_ADDRESS_2), serviceLocationComponent.address2Panel,
                serviceLocationComponent.address2RequiredLabel, serviceLocationComponent.address2ErrorLabel);
        serviceLocationComponent.setUserInterfaceComponentPreferences(
                userInterfaceFields.get(UserInterfaceFormFields.UP_ADDRESS_3), serviceLocationComponent.address3Panel,
                serviceLocationComponent.address3RequiredLabel, serviceLocationComponent.address3ErrorLabel);

        MeterMngClientUtils.setUserInterfaceComponentPreferences(
                userInterfaceFields.get(UserInterfaceFormFields.UP_LATITUDE), serviceLocationComponent.latitudeElement);
        MeterMngClientUtils.setUserInterfaceComponentPreferences(
                userInterfaceFields.get(UserInterfaceFormFields.UP_LONGITUDE),
                serviceLocationComponent.longitudeElement);

        physicalAddressPanel.setVisible(serviceLocationComponent.handleUserInterfaceComponentGroups());
    }

    @UiHandler("btnSync")
    void handleSyncAccBalButton(ClickEvent event) {
        btnSync.setEnabled(false);
        clearErrorMessages();
        final int left = btnSync.getAbsoluteLeft() + btnSync.getOffsetWidth();
        final int top = btnSync.getAbsoluteTop() - btnSync.getOffsetHeight();
        if (clientFactory != null) {
            SessionCheckCallback sessionCheckCallback = new SessionCheckCallback() {
                @Override
                public void callback(SessionCheckResolution sessionCheckResolution) {
                    clientFactory.getUsagePointRpc().sendSyncUnitsAccountBalance(usagePoint.getMeterData(),
                            usagePoint.getUnitsAccountId(), new ClientCallback<IpayResponseData>() {
                                @Override
                                public void onSuccess(IpayResponseData result) {
                                    Messages messages = MessagesUtil.getInstance();
                                    MediaResource mediaResource = MediaResourceUtil.getInstance();
                                    if (result == null) {
                                        Dialogs.displayErrorMessage(
                                                messages.getMessage("customer.sync.accbal.connection.error"),
                                                mediaResource.getErrorIcon(), left, top, messages.getMessage("button.close"));
                                    } else if (!result.getResCode().equals("mdc000") && !result.getResCode().equals("mdc010")
                                            && !result.getResCode().equals("mdc011")) {
                                        Dialogs.displayErrorMessage(
                                                messages.getMessage("customer.sync.accbal.error",
                                                        new String[] { result.getResRef() }),
                                                mediaResource.getErrorIcon(), left, top, messages.getMessage("button.close"));
                                    } else {
                                        String mesg = "customer.sync.accbal.ok.mdc000";
                                        if (result.getResCode().equals("mdc010")) {
                                            mesg = "customer.sync.accbal.ok.mdc010";
                                        } else if (result.getResCode().equals("mdc011")) {
                                            mesg = "customer.sync.accbal.ok.mdc011";
                                        }
                                        Dialogs.displayInformationMessage(
                                                messages.getMessage(mesg, new String[] { result.getResRef() }),
                                                mediaResource.getInformationIcon(), left, top, null);
                                    }
                                    btnSync.setEnabled(true);
                                }
                            });
                }
            };
            clientFactory.handleSessionCheckCallback(sessionCheckCallback);
        }
    }

    public Button getBtnSave(){
        return btnSave;
    }

    public void toggleSaveBtns(boolean enable) {
        usagePointWorkspaceView.toggleSaveBtns(enable);
    }

    private void generateUsagePointName() {
        if (userInterfaceFields != null && !validateUpNameRegex) {
            clientFactory.getUsagePointRpc().getAutoGeneratedRef(new ClientCallback<String>() {
                @Override
                public void onSuccess(String result) {
                    if (txtbxUsagepointname.getValue().isEmpty()) {
                        txtbxUsagepointname.setText("UP"+result);
                    }
                }
            });
        }
    }
}
