<!DOCTYPE ui:UiBinder SYSTEM "http://dl.google.com/gwt/DTD/xhtml.ent">
<ui:UiBinder xmlns:ui="urn:ui:com.google.gwt.uibinder" 
             xmlns:g="urn:import:com.google.gwt.user.client.ui"
             xmlns:p1="urn:import:za.co.ipay.gwt.common.client.form">

    <ui:with field="msg" type="za.co.ipay.metermng.client.i18n.UiMessages" />

    <g:FlowPanel>

        <p1:FormRowPanel>
            <p1:FormElement ui:field="nameElement" labelText="{msg.getScheduledTaskName}:" helpMsg="{msg.getScheduledTaskNameHelp}" required="true">
                <g:TextBox text="" ui:field="nameTextBox" title="{msg.getScheduledTaskName}" width="200px" />
            </p1:FormElement>              
        </p1:FormRowPanel>
        
        <p1:FormRowPanel>
            <p1:FormElement ui:field="usernameElement" labelText="{msg.getUsername}:">
                <g:SuggestBox ui:field="userBox" styleName="gwt-TextBox" />
            </p1:FormElement>
            <p1:FormElement ui:field="addElement" labelText="" required="false">
                <g:Button ui:field="addButton" text="&gt;" />
            </p1:FormElement>
            <p1:FormElement ui:field="selectedUsersElement" labelText="{msg.getTaskScheduleUsers}:">
                <g:VerticalPanel>
                    <g:ListBox ui:field="selectedUsersBox" styleName="gwt-TextBox veryWideSelect"  multipleSelect="true" visibleItemCount="5" />
                    <g:Button ui:field="removeButton" text="{msg.getRemoveButton}" styleName="gwt-Button verticalSpace" />
                </g:VerticalPanel>
            </p1:FormElement>
        </p1:FormRowPanel>
        
        <p1:FormRowPanel>
            <p1:FormElement ui:field="customerElement" labelText="{msg.getCustomer}:" helpMsg="{msg.getCustomerHelp}">
                <g:SuggestBox ui:field="customerBox" styleName="gwt-TextBox" /><g:Button ui:field="customerRemoveButton" text="{msg.getClearButton}" styleName="gwt-Button leftSpace" />
            </p1:FormElement>
        </p1:FormRowPanel>        
        
        <p1:FormRowPanel>
            <p1:FormElement ui:field="taskClassElement" labelText="{msg.getScheduledTaskClass}:" helpMsg="{msg.getScheduledTaskClassHelp}" required="true">
                <g:ListBox ui:field="taskClassBox" title="{msg.getScheduledTaskClass}" />
            </p1:FormElement>
        </p1:FormRowPanel>
        
        <g:FlowPanel ui:field="taskClassPanel" />

    </g:FlowPanel>

</ui:UiBinder> 