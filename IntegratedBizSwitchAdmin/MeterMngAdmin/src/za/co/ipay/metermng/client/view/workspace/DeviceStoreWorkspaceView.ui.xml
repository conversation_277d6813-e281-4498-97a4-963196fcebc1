<!DOCTYPE ui:UiBinder SYSTEM "http://dl.google.com/gwt/DTD/xhtml.ent">
<ui:UiBinder 
  xmlns:ui="urn:ui:com.google.gwt.uibinder" 
  xmlns:g="urn:import:com.google.gwt.user.client.ui"
  xmlns:p1="urn:import:za.co.ipay.gwt.common.client.workspace"
  xmlns:p2="urn:import:za.co.ipay.metermng.client.view.component.devicestore">
  <ui:style>
  
  </ui:style>
  
    <ui:with field="msg" type="za.co.ipay.metermng.client.i18n.UiMessages" />
  
    <g:DeckLayoutPanel height="100%" width="100%" animationVertical="false" ui:field="deckPanel">
        <!-- 1st deck -->
 			<p1:SimpleTableView ui:field="view" header="{msg.getDeviceStoresHeader}" title="{msg.getDeviceStoresTitle}" />

        <!-- 2nd deck -->
            <p2:DeviceStoreMeters ui:field="deviceStoreMeters"  header="{msg.getDeviceStoreMetersHeader}" title="{msg.getDeviceStoreMetersTitle}"></p2:DeviceStoreMeters>
            
    </g:DeckLayoutPanel>
     
</ui:UiBinder>