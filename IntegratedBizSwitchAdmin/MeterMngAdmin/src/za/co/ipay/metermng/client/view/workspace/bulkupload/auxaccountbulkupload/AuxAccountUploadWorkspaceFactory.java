package za.co.ipay.metermng.client.view.workspace.bulkupload.auxaccountbulkupload;

import com.google.gwt.place.shared.Place;

import za.co.ipay.gwt.common.client.Dialogs;
import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.gwt.common.client.workspace.WorkspaceCreateCallback;
import za.co.ipay.gwt.common.client.workspace.WorkspaceFactory;
import za.co.ipay.gwt.common.shared.exception.AccessControlException;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.history.AuxAccountUploadPlace;
import za.co.ipay.metermng.client.resource.image.MediaResource.MediaResourceUtil;
import za.co.ipay.metermng.shared.MeterMngStatics;

public class AuxAccountUploadWorkspaceFactory implements WorkspaceFactory {

	private ClientFactory clientFactory;

	public AuxAccountUploadWorkspaceFactory(ClientFactory clientFactory) {
		this.clientFactory = clientFactory;
		clientFactory.getWorkspaceContainer().register(this);
	}

	@Override
	public void createWorkspace(Place place, WorkspaceCreateCallback workspaceCreateCallback) {
		if (!clientFactory.getUser().hasPermission(MeterMngStatics.ACCESS_PERMISSION_MM_AUX_ACCOUNT_UPLOAD)) {
			Dialogs.displayErrorMessage(MessagesUtil.getInstance().getMessage("error.accessdenied"),
					MediaResourceUtil.getInstance().getLockedIcon(),
					MessagesUtil.getInstance().getMessage("button.close"));
			workspaceCreateCallback.onWorkspaceCreationFailed(new AccessControlException("Access is Denied"));
			return;
		}
		try {
			AuxAccountUploadWorkspaceView view = new AuxAccountUploadWorkspaceView(clientFactory, (AuxAccountUploadPlace) place);
			workspaceCreateCallback.onWorkspaceCreated(view);
		} catch (Exception e) {
			workspaceCreateCallback.onWorkspaceCreationFailed(e);
		}
	}

	@Override
	public boolean handles(Place place) {
		return (place instanceof AuxAccountUploadPlace);
	}

}
