package za.co.ipay.metermng.client.view.workspace.group.all;

import java.util.HashMap;
import java.util.List;

import za.co.ipay.gwt.common.client.form.FormElement;
import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.metermng.client.view.component.group.GenGroupParentComponent;
import za.co.ipay.metermng.shared.group.GroupTypeData;

import com.google.gwt.core.client.GWT;
import com.google.gwt.event.dom.client.ChangeEvent;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.uibinder.client.UiHandler;
import com.google.gwt.user.client.ui.ListBox;
import com.google.gwt.user.client.ui.Widget;

public class GroupTypeSelectionPanel extends GroupTypeSelection {
    
    private GenGroupParentComponent parentWorkspace;
    String selectionType;
    private HashMap<Long, GroupTypeData> groupTypeDataMap = new HashMap<Long, GroupTypeData>(); 
    
    @UiField FormElement groupTypeElement;
    @UiField ListBox groupTypeBox;

    private static GroupTypeSelectionPanelUiBinder uiBinder = GWT.create(GroupTypeSelectionPanelUiBinder.class);

    interface GroupTypeSelectionPanelUiBinder extends UiBinder<Widget, GroupTypeSelectionPanel> {
    }

    public GroupTypeSelectionPanel(GenGroupParentComponent parentWorkspace, String selectionType) {
        super(null);        
        this.parentWorkspace = parentWorkspace;
        this.selectionType = selectionType;
        initWidget(uiBinder.createAndBindUi(this));        
    }

    @Override
    public void addFieldHandlers() {
        
    }

    @Override
    public void clearFields() {
        groupTypeBox.setSelectedIndex(0);
    }

    @Override
    public void clearErrors() {
        groupTypeElement.clearErrorMsg();
    }
    
    public void setGroupTypes(List<GroupTypeData> groupTypes, boolean displayAccessGroup, boolean displayLocationGroup) {
        groupTypeBox.clear();
        groupTypeBox.addItem("");
        for (GroupTypeData gt : groupTypes) {            
            if ( (displayAccessGroup && gt.isAccessGroup())
                 || (displayLocationGroup && gt.isLocationGroup())   
                 || ((!displayAccessGroup && !gt.isAccessGroup()) && (!displayLocationGroup && !gt.isLocationGroup())) ) {
                groupTypeBox.addItem(gt.getName(), gt.getId().toString());
                groupTypeDataMap.put(gt.getId(), gt);
            }
        }
    }
    
    @UiHandler("groupTypeBox")
    public void onGroupTypeSelection(ChangeEvent event) {
        fireGroupTypeSelection();
    }
    
    private void fireGroupTypeSelection() {
        int index = groupTypeBox.getSelectedIndex();
        if (index == 0) {
            parentWorkspace.clearGroupHierarchies();
            parentWorkspace.clearGroups();
        } else {
            Long groupTypeId = getGroupTypeId();
            parentWorkspace.loadGroupHierarchies(groupTypeId);
            parentWorkspace.loadGroups(groupTypeId);
        }
        parentWorkspace.updateRoot();
    }
    

    @Override
    public GroupTypeData getGroupTypeData() {
        int index = groupTypeBox.getSelectedIndex();
        if (index > 0) {
            return groupTypeDataMap.get(Long.valueOf(groupTypeBox.getValue(index)));
        } else {
            return null;
        }
    }
    
    public Long getGroupTypeId() {
        int index = groupTypeBox.getSelectedIndex();
        if (index > 0) {
            return Long.valueOf(groupTypeBox.getValue(index));
        } else {
            return null;
        }
    }

    public String getGroupTypeName() {
        if (groupTypeBox != null) {
            int index = groupTypeBox.getSelectedIndex();
            if (index > 0) {
                return groupTypeBox.getItemText(index);
            }
        }
        return MessagesUtil.getInstance().getMessage("grouptype.none");
    }
    
    public void setFocus() {
        groupTypeBox.setFocus(true);
    }
    
    public void selectGroupType(Long groupTypeId) {
        for(int i=1;i<groupTypeBox.getItemCount();i++) {
            if (groupTypeBox.getValue(i).equals(groupTypeId.toString())) {
                groupTypeBox.setSelectedIndex(i);
                fireGroupTypeSelection();
                return;
            }
        }
    }
}
