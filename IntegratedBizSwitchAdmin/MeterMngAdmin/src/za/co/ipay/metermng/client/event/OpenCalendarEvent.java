package za.co.ipay.metermng.client.event;

import com.google.gwt.event.shared.GwtEvent;

public class OpenCalendarEvent extends GwtEvent<OpenCalendarEventHandler> {

    public static Type<OpenCalendarEventHandler> TYPE = new Type<OpenCalendarEventHandler>();
    private final String calendarplacetype;
    
    public OpenCalendarEvent(String calendarPlaceType) {
        super();
        this.calendarplacetype = calendarPlaceType;
    }
    
    public String getCalendarPlaceType() {
        return calendarplacetype;
    }
    
    @Override
    public Type<OpenCalendarEventHandler> getAssociatedType() {
        return TYPE;
    }

    @Override
    protected void dispatch(OpenCalendarEventHandler handler) {
        handler.openCalendar(this);
    }

}

