package za.co.ipay.metermng.client.history;

import com.google.gwt.activity.shared.AbstractActivity;
import com.google.gwt.event.shared.EventBus;
import com.google.gwt.user.client.ui.AcceptsOneWidget;

import za.co.ipay.metermng.client.event.ImportFileEvent;
import za.co.ipay.metermng.client.factory.ClientFactory;

public class ImportFileActivity extends AbstractActivity {

    private ClientFactory clientFactory;
    private ImportFilePlace place;

    public ImportFileActivity(ImportFilePlace place, ClientFactory clientFactory) {
        super();
        this.clientFactory = clientFactory;
        this.place = place;
    }

    @Override
    public void start(AcceptsOneWidget panel, EventBus eventBus) {
        clientFactory.getEventBus().fireEvent(new ImportFileEvent(place.getName()));
    }
}
