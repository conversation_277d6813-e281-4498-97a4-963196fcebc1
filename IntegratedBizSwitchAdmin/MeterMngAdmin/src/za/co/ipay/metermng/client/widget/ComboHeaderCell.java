package za.co.ipay.metermng.client.widget;

import java.util.ArrayList;
import java.util.List;

import com.google.gwt.cell.client.CompositeCell;
import com.google.gwt.cell.client.HasCell;

/**
 * ComboHeaderCell is used to create a header with a main heading and then below it on the next line multiple sub-headings.
 * <AUTHOR>
 */
public class ComboHeader<PERSON>ell extends CompositeCell<ComboHeaderNames> {

    public ComboHeaderCell(ComboHeaderNames headers) {        
        super(createCells(headers));        
    }
    
    private static List<HasCell<ComboHeaderNames, ?>> createCells(ComboHeaderNames headers) {
        List<HasCell<ComboHeaderNames, ?>> cells = new ArrayList<HasCell<ComboHeaderNames,?>>();                
        cells.add(new ComboHeaderDisplayCell(headers.getMainHeader()));
        if (headers.getSubHeaders() != null) {
            int start = 0;
            int end = headers.getSubHeaders().length - 1;
            for(int i=0;i<headers.getSubHeaders().length;i++) {
                cells.add(new ComboHeaderDisplayCell(headers.getSubHeaders()[i], (i == start), (i == end)));
            }
        }
        return cells;
    }
}
