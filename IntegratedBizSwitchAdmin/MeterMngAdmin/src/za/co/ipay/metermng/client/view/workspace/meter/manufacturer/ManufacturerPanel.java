package za.co.ipay.metermng.client.view.workspace.meter.manufacturer;

import za.co.ipay.gwt.common.client.form.FormElement;
import za.co.ipay.gwt.common.client.form.SimpleForm;
import za.co.ipay.gwt.common.client.handler.FormDataChangeHandler;
import za.co.ipay.gwt.common.client.handler.FormDataClickHandler;
import za.co.ipay.metermng.client.form.SimpleFormPanel;

import com.google.gwt.core.client.GWT;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.user.client.ui.CheckBox;
import com.google.gwt.user.client.ui.TextBox;
import com.google.gwt.user.client.ui.Widget;

public class ManufacturerPanel extends SimpleFormPanel {

    @UiField TextBox nameTextBox;
    @UiField TextBox descriptionTextBox;
    @UiField CheckBox activeBox;
    
    @UiField FormElement activeElement;
    @UiField FormElement nameElement;
    @UiField FormElement descriptionElement;

    private static ManufacturerPanelUiBinder uiBinder = GWT.create(ManufacturerPanelUiBinder.class);

    interface ManufacturerPanelUiBinder extends UiBinder<Widget, ManufacturerPanel> {
    }

    public ManufacturerPanel(SimpleForm form) {
        super(form);
        initWidget(uiBinder.createAndBindUi(this));        
        addFieldHandlers();
    }
    
    public void clearFields() {
        form.setDirtyData(false);
        nameTextBox.setText("");
        descriptionTextBox.setText("");
        activeBox.setValue(true);
    }

    public void clearErrors() {
        activeElement.clearErrorMsg();
        nameElement.clearErrorMsg();
        descriptionElement.clearErrorMsg();
    }

    @Override
    public void addFieldHandlers() {
        nameTextBox.addChangeHandler(new FormDataChangeHandler(form));
        descriptionTextBox.addChangeHandler(new FormDataChangeHandler(form));
        activeBox.addClickHandler(new FormDataClickHandler(form));        
    }
}
