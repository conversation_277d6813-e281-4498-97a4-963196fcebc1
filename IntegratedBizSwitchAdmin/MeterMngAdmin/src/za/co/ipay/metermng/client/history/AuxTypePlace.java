package za.co.ipay.metermng.client.history;

import com.google.gwt.place.shared.Place;
import com.google.gwt.place.shared.PlaceTokenizer;
import com.google.gwt.place.shared.Prefix;

public class AuxTypePlace extends Place {

    public static AuxTypePlace ALL_AUX_TYPE_PLACE = new AuxTypePlace();

    public AuxTypePlace() {
    }

    @Prefix(value = "auxType")
    public static class Tokenizer implements PlaceTokenizer<AuxTypePlace> {
        @Override
        public String getToken(AuxTypePlace place) {
            return "all";
        }

        @Override
        public AuxTypePlace getPlace(String token) {
            return new AuxTypePlace();
        }
    }
}
