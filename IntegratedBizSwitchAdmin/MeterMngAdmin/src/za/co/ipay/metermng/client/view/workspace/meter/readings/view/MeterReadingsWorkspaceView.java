package za.co.ipay.metermng.client.view.workspace.meter.readings.view;

import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.history.MeterReadingsPlace;
import za.co.ipay.metermng.client.view.workspace.BaseWorkspace;

import com.google.gwt.core.client.GWT;
import com.google.gwt.place.shared.Place;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.user.client.ui.Widget;

/**
 * MeterReadingsWorkspaceView allows the user to specify some input such as meter type, meter, start and end
 * dates and to view the corresponding meter readings in a graph view and a table view.
 * 
 * <AUTHOR>
 */
public class MeterReadingsWorkspaceView extends BaseWorkspace {
     
    private MeterReadingsPlace currentPlace;
    @UiField(provided=true) MeterReadingsViews views;
    
//    private static Logger logger = Logger.getLogger(MeterReadingsWorkspaceView.class.getName());
    
    private static MeterReadingsWorkspaceViewUiBinder uiBinder = GWT.create(MeterReadingsWorkspaceViewUiBinder.class);

    interface MeterReadingsWorkspaceViewUiBinder extends UiBinder<Widget, MeterReadingsWorkspaceView> {
    }

    public MeterReadingsWorkspaceView(ClientFactory clientFactory, MeterReadingsPlace meterReadingsPlace) {
        this.clientFactory = clientFactory;
        this.currentPlace = meterReadingsPlace;
        this.views = new MeterReadingsViews(clientFactory, meterReadingsPlace, null);
        initWidget(uiBinder.createAndBindUi(this));                
        setPlaceString(MeterReadingsPlace.getPlaceAsString(meterReadingsPlace));
        setHeaderText(MessagesUtil.getInstance().getMessage("meterreadings.title"));
    }    
    
    @Override
    public void onLeaving() {

    }

    @Override
    public void onSelect() {

    }

    @Override
    public void onArrival(Place p) {       
    }

    @Override
    public void onClose() {

    }

    @Override
    public boolean handles(Place p) {        
        if (currentPlace != null) {
            return currentPlace.equals(p);
        } else {
            return false;
        }
    }    
}
