package za.co.ipay.metermng.client.event;

import com.google.gwt.event.shared.GwtEvent;

public class OpenSupplyGroupEvent extends GwtEvent<OpenSupplyGroupEventHandler> {

    public static Type<OpenSupplyGroupEventHandler> TYPE = new Type<OpenSupplyGroupEventHandler>();

    @Override
    public Type<OpenSupplyGroupEventHandler> getAssociatedType() {
        return TYPE;
    }

    @Override
    protected void dispatch(OpenSupplyGroupEventHandler handler) {
        handler.openSupplyGroup(this);
    }
}
