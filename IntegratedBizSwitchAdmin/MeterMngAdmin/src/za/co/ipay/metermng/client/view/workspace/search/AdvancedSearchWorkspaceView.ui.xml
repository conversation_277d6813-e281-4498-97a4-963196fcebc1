<!DOCTYPE ui:UiBinder SYSTEM "http://dl.google.com/gwt/DTD/xhtml.ent">
<ui:UiBinder xmlns:ui="urn:ui:com.google.gwt.uibinder" 
		     xmlns:g="urn:import:com.google.gwt.user.client.ui"
             xmlns:g2="urn:import:com.google.gwt.user.cellview.client"
             xmlns:widget="urn:import:za.co.ipay.gwt.common.client.widgets"
             xmlns:form="urn:import:za.co.ipay.gwt.common.client.form"
             xmlns:m="urn:import:za.co.ipay.metermng.client.view.component.search.meter"
             xmlns:c="urn:import:za.co.ipay.metermng.client.view.component.search.customer"
             xmlns:u="urn:import:za.co.ipay.metermng.client.view.component.search.usagepoint"
             xmlns:l="urn:import:za.co.ipay.metermng.client.view.component.search.location">
    <ui:style>

    </ui:style>

    <ui:with field="msg" type="za.co.ipay.metermng.client.i18n.UiMessages"/>

    <g:DockLayoutPanel ui:field="dockLayoutPanel" styleName="mainPanel">

        <g:north size="30">
            <form:PageHeader heading="{msg.getAdvancedSearchHeader}" ui:field="pageHeader"/>
        </g:north>

        <g:center>
            <g:ScrollPanel ui:field="formViewScrollPanel">
                <g:FlowPanel width="99%" height="100%">

                    <!-- Search form section -->
                    <g:HTML ui:field="dataDescription" text="" styleName="dataDescription"/>
                    <g:HorizontalPanel>
                        <m:MeterSearchForm ui:field="meterSearchForm"/>
                        <c:CustomerSearchForm ui:field="customerSearchForm"/>
                        <u:UsagePointSearchForm ui:field="usagePointSearchForm"/>
                        <l:LocationSearchForm ui:field="locationSearchForm"/>
                    </g:HorizontalPanel>
                    
                    <g:HTMLPanel>
                        <g:CheckBox text="{msg.getChckbxGetTotalLabel}" checked="false" ui:field="chckbxGetTotal" debugId="chckbxGetTotal"/>
                    </g:HTMLPanel>

                    <g:HTMLPanel ui:field="buttons" styleName="mainButtons">
                        <g:Button text="{msg.getSearchButton}" ui:field="saveBtn" debugId="saveBtn"/>
                        <g:Button text="{msg.getClearButton}" ui:field="clearBtn" debugId="clearBtn"/>
                    </g:HTMLPanel>

                    <!-- Search results section -->
                    <g:FlowPanel ui:field="searchResults" styleName="searchResults">
                        <g:HTML ui:field="tableTitle" text="{msg.getAdvancedSearchResultsHeader}"
                                styleName="pageSectionTitle"/>
                        <g2:CellTable ui:field="table"/>
                        <widget:TablePager ui:field="pager" styleName="pager"/>
                    </g:FlowPanel>
                    <g:HTML ui:field="stillCountingMessage" text=""/>
                    <g:HTMLPanel>
                 		<g:Button ui:field="btnExportCsv" debugId="btnExportCsv" text="{msg.getExportButton}" />
            		</g:HTMLPanel>
                </g:FlowPanel>
            </g:ScrollPanel>
        </g:center>
    </g:DockLayoutPanel>

</ui:UiBinder> 