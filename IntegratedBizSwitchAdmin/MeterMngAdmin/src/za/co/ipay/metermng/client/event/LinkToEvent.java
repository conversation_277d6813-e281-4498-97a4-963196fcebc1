package za.co.ipay.metermng.client.event;

import com.google.gwt.event.shared.GwtEvent;

public class LinkToEvent extends GwtEvent<LinkToEventHandler> {
	
	public static final int ENGINEERING_TOKEN_HISTORY = 1;
	public static final int CREDIT_TOKEN_HISTORY = 2;
	public static final int REMOVE_METER_COMPONENT = 3;
	
	public static final int METER_NUM = 4;
	public static final int USAGE_POINT_NAME = 5;
	
	public static Type<LinkToEventHandler> TYPE = new Type<LinkToEventHandler>();
    private int updateType;
    private String linkParamStr1;
    private int linkParamType;
    
	public LinkToEvent(int updateType, String linkParamString, int linkParamType) {
		super();
        this.updateType = updateType;
		this.linkParamStr1 = linkParamString;
		this.linkParamType = linkParamType;
	}
	
	public int getUpdateType() {
		return updateType;
	}

    public String getLinkParamStr1() {
        return linkParamStr1;
    }

    public void setLinkParamStr1(String linkParamStr1) {
        this.linkParamStr1 = linkParamStr1;
    }

    public int getLinkParamType() {
        return linkParamType;
    }

    public void setLinkParamType(int linkParamType) {
        this.linkParamType = linkParamType;
    }

    @Override
	public Type<LinkToEventHandler> getAssociatedType() {
		return TYPE;
	}

	@Override
	protected void dispatch(LinkToEventHandler handler) {
		handler.linkTo(this);
	}

}
