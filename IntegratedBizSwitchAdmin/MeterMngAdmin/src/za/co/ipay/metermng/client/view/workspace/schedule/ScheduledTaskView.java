package za.co.ipay.metermng.client.view.workspace.schedule;

import java.util.ArrayList;

import za.co.ipay.gwt.common.client.Dialogs;
import za.co.ipay.gwt.common.client.form.FormManager;
import za.co.ipay.gwt.common.client.handler.ConfirmHandler;
import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.gwt.common.client.validation.ClientValidatorUtil;
import za.co.ipay.gwt.common.client.workspace.SessionCheckCallback;
import za.co.ipay.gwt.common.client.workspace.SimpleTableView;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.resource.image.MediaResource.MediaResourceUtil;
import za.co.ipay.metermng.client.rpc.callback.ClientCallback;
import za.co.ipay.metermng.client.view.component.BaseComponent;
import za.co.ipay.metermng.client.view.workspace.schedule.taskclass.EnergyBalancingExportTaskPanel;
import za.co.ipay.metermng.client.view.workspace.schedule.taskclass.EnergyBalancingVarianceTaskPanel;
import za.co.ipay.metermng.client.view.workspace.schedule.taskclass.MeterReadingsExportTaskPanel;
import za.co.ipay.metermng.client.view.workspace.schedule.taskclass.TaskClassUi;
import za.co.ipay.metermng.mybatis.generated.model.MeterReadingType;
import za.co.ipay.metermng.mybatis.generated.model.TaskClass;
import za.co.ipay.metermng.mybatis.generated.model.TaskSchedule;
import za.co.ipay.metermng.shared.MeterMngStatics;
import za.co.ipay.metermng.shared.dto.schedule.EnergyBalancingExportScreenData;
import za.co.ipay.metermng.shared.dto.schedule.ScheduledTaskDto;
import za.co.ipay.metermng.shared.dto.schedule.ScheduledTaskScreenData;
import za.co.ipay.metermng.shared.dto.user.UserData;

import com.google.gwt.event.dom.client.ChangeEvent;
import com.google.gwt.event.dom.client.ChangeHandler;
import com.google.gwt.event.dom.client.ClickEvent;
import com.google.gwt.event.dom.client.ClickHandler;
import com.google.gwt.user.cellview.client.Column;
import com.google.gwt.user.cellview.client.TextColumn;
import com.google.gwt.user.client.ui.Button;
import com.google.gwt.view.client.ListDataProvider;

public class ScheduledTaskView extends BaseComponent implements FormManager<ScheduledTaskDto> {

    private TaskScheduleWorkspaceView parentWorkspace;
    private SimpleTableView<ScheduledTaskDto> view;
    private ScheduledTaskPanel panel;
    private ListDataProvider<ScheduledTaskDto> dataProvider;
    private TaskSchedule taskSchedule;
    private ScheduledTaskDto scheduledTask;

    public ScheduledTaskView(TaskScheduleWorkspaceView parentWorkspace, ClientFactory clientFactory,
            SimpleTableView<ScheduledTaskDto> view) {
        this.parentWorkspace = parentWorkspace;
        this.clientFactory = clientFactory;
        this.view = view;
        initUi();
    }

    private void initUi() {
        initView();
        initForm();
        createTable();
        loadInitData();
    }

    private void initView() {
        view.setFormManager(this);
    }

    private void initForm() {
        panel = new ScheduledTaskPanel(view.getForm(), clientFactory);
        panel.clearFields();

        view.getForm().setHasDirtyDataManager(parentWorkspace);
        view.getForm().getFormFields().add(panel);

        view.getForm().getSaveBtn().setText(MessagesUtil.getInstance().getMessage("button.create"));
        view.getForm().getSaveBtn().addClickHandler(new ClickHandler() {
            @Override
            public void onClick(ClickEvent event) {
                onSave();
            }
        });

        view.getForm().getOtherBtn().setText(MessagesUtil.getInstance().getMessage("button.cancel"));
        view.getForm().getOtherBtn().addClickHandler(new ClickHandler() {
            @Override
            public void onClick(ClickEvent event) {
                view.getForm().checkDirtyData(new ConfirmHandler() {
                    @Override
                    public void confirmed(boolean confirm) {
                        if (confirm) {
                            view.getForm().setDirtyData(false);
                            displaySelected(null);
                        }
                    }
                });
            }
        });

        view.getForm().getFormPanel().setHeadingText(MessagesUtil.getInstance().getMessage("scheduledtask.title.add"), "pageSectionTitle");

        panel.taskClassBox.addChangeHandler(new ChangeHandler() {
            @Override
            public void onChange(ChangeEvent event) {
                displayTaskClassUi();
            }
        });

        view.getForm().getBackBtn().setVisible(true);
        view.getForm().getBackBtn().setText(MessagesUtil.getInstance().getMessage("button.delete"));
        view.getForm().getBackBtn().addClickHandler(new ClickHandler() {
            @Override
            public void onClick(ClickEvent event) {
                onDelete();
            }
        });

        Button backButton = new Button(MessagesUtil.getInstance().getMessage("button.back"));
        backButton.addStyleName("gwt-Button");
        backButton.addClickHandler(new ClickHandler() {
            @Override
            public void onClick(ClickEvent event) {
                view.getForm().checkDirtyData(new ConfirmHandler() {
                    @Override
                    public void confirmed(boolean confirm) {
                        if (confirm) {
                            if (view.getForm().isDirtyData()) {
                                view.getForm().setDirtyData(false);
                            }
                            parentWorkspace.goToTaskSchedules(taskSchedule.getId());
                        }
                    }
                });
            }
        });
        view.getForm().getButtons().add(backButton);
    }

    private void displayTaskClassUi() {
        panel.clearTaskClassPanel();
        int index = panel.taskClassBox.getSelectedIndex();
        if (index > 0) {
            final String value = panel.taskClassBox.getValue(index);
            SessionCheckCallback sessionCheckCallback = new SessionCheckCallback() {
                @Override
                public void callback(SessionCheckResolution resolution) {
                    if (MeterMngStatics.METER_READINGS_EXPORT_TASK_ID.toString().equals(value)) {
                        final TaskClassUi taskClassUi = new MeterReadingsExportTaskPanel(clientFactory, view.getForm());
                        panel.taskClassPanel.add(taskClassUi);
                        clientFactory.getMeterRpc().getMeterReadingTypes(new ClientCallback<ArrayList<MeterReadingType>>() {
                            @Override
                            public void onSuccess(ArrayList<MeterReadingType> result) {
                                ((MeterReadingsExportTaskPanel) taskClassUi).setMeterReadingTypes(result);
                                ((MeterReadingsExportTaskPanel) taskClassUi).setTimePeriods();
                                if (scheduledTask != null && scheduledTask.getScheduledTask().getTaskClassId() != null) {
                                    if (scheduledTask.getScheduledTask().getTaskClassId().toString().equals(value)) {
                                        taskClassUi.setTaskClassContents(scheduledTask.getScheduledTask().getTaskContents());
                                    }
                                }
                            }
                        });
                    } else if (MeterMngStatics.ENERGY_BALANCING_EXPORT_TASK_ID.toString().equals(value)) {
                        final TaskClassUi taskClassUi = new EnergyBalancingExportTaskPanel(view.getForm());
                        panel.taskClassPanel.add(taskClassUi);
                        clientFactory.getMeterRpc().getEnergyBalancingExportScreenData(
                                new ClientCallback<EnergyBalancingExportScreenData>() {
                                    @Override
                                    public void onSuccess(EnergyBalancingExportScreenData result) {
                                        ((EnergyBalancingExportTaskPanel) taskClassUi).setMeterReadingTypes(result.getMeterReadingTypes());
                                        ((EnergyBalancingExportTaskPanel) taskClassUi).setSuperMeters(result.getSuperMeters());
                                        ((EnergyBalancingExportTaskPanel) taskClassUi).setTimePeriods();
                                        if (scheduledTask != null && scheduledTask.getScheduledTask().getTaskClassId() != null) {
                                            if (scheduledTask.getScheduledTask().getTaskClassId().toString().equals(value)) {
                                                taskClassUi.setTaskClassContents(scheduledTask.getScheduledTask().getTaskContents());
                                            }
                                        }
                                    }
                                });
                    } else if (MeterMngStatics.ENERGY_BALANCING_VARIANCE_TASK_ID.toString().equals(value)) {
                        final TaskClassUi taskClassUi = new EnergyBalancingVarianceTaskPanel(view.getForm(), taskSchedule.getCronExpression());
                        panel.taskClassPanel.add(taskClassUi);
                        clientFactory.getMeterRpc().getEnergyBalancingExportScreenData(
                                new ClientCallback<EnergyBalancingExportScreenData>() {
                                    @Override
                                    public void onSuccess(EnergyBalancingExportScreenData result) {
                                        ((EnergyBalancingVarianceTaskPanel) taskClassUi).setMeterReadingTypes(result.getMeterReadingTypes());
                                        ((EnergyBalancingVarianceTaskPanel) taskClassUi).setSuperMeters(result.getSuperMeters());
                                        if (scheduledTask != null && scheduledTask.getScheduledTask().getTaskClassId() != null) {
                                            if (scheduledTask.getScheduledTask().getTaskClassId().toString().equals(value)) {
                                                taskClassUi.setTaskClassContents(scheduledTask.getScheduledTask().getTaskContents());
                                            }
                                        }
                                    }
                                });
                    }
                }
            };
            clientFactory.handleSessionCheckCallback(sessionCheckCallback);
        }
    }

    private void createTable() {
        if (dataProvider == null) {
            Column<ScheduledTaskDto, ?> nameColumn = createNameColumn();
            Column<ScheduledTaskDto, ?> taskColumn = createTaskColumn();

            view.getTable().addColumn(nameColumn, MessagesUtil.getInstance().getMessage("scheduledtask.field.name"));
            view.getTable().addColumn(taskColumn, MessagesUtil.getInstance().getMessage("scheduledtask.field.class"));

            // Set the range to display
            view.getTable().setVisibleRange(0, getPageSize());

            // Set the data provider for the table
            dataProvider = new ListDataProvider<ScheduledTaskDto>();
            dataProvider.addDataDisplay(view.getTable());

            // Create the table's pager
            view.getPager().setDisplay(view.getTable());
        }
    }

    private Column<ScheduledTaskDto, ?> createNameColumn() {
        TextColumn<ScheduledTaskDto> nameColumn = new TextColumn<ScheduledTaskDto>() {
            @Override
            public String getValue(ScheduledTaskDto object) {
                return object.getScheduledTask().getScheduledTaskName();
            }
        };
        nameColumn.setSortable(true);
        return nameColumn;
    }

    private Column<ScheduledTaskDto, ?> createTaskColumn() {
        TextColumn<ScheduledTaskDto> column = new TextColumn<ScheduledTaskDto>() {
            @Override
            public String getValue(ScheduledTaskDto object) {
                return object.getTaskClass();
            }
        };
        column.setSortable(true);
        return column;
    }

    private void loadInitData() {
        clientFactory.getScheduleRpc().getScheduledTaskScreenData(new ClientCallback<ScheduledTaskScreenData>() {
            @Override
            public void onSuccess(ScheduledTaskScreenData screenData) {
                setInitData(screenData);
            }
        });
    }

    private void setInitData(ScheduledTaskScreenData screenData) {
        panel.taskClassBox.clear();
        panel.taskClassBox.addItem("", "");

        ArrayList<TaskClass> clazzes = screenData.getTaskClasses();
        if (clazzes != null) {
            for (TaskClass c : clazzes) {
                panel.taskClassBox.addItem(c.getTaskClassName(), c.getId().toString());
            }
        }
    }

    protected void setTaskSchedule(TaskSchedule taskSchedule) {
        this.taskSchedule = taskSchedule;
        if (taskSchedule != null) {
            view.setDataDetails(MessagesUtil.getInstance().getMessage("taskschedule.title.single")+": "+taskSchedule.getTaskScheduleName(), "");
            loadScheduledTasks();
        } else {
            dataProvider.getList().clear();
            dataProvider.refresh();
        }
    }

    private void loadScheduledTasks() {
        clientFactory.getScheduleRpc().getScheduleTasks(taskSchedule.getId(), new ClientCallback<ArrayList<ScheduledTaskDto>>() {
            @Override
            public void onSuccess(ArrayList<ScheduledTaskDto> result) {
                dataProvider.getList().clear();
                dataProvider.getList().addAll(result);
                view.getTable().setPageStart(0);
            }
        });
    }

    @Override
    public void displaySelected(ScheduledTaskDto selected) {
        displayTaskSchedule(selected);
    }

    private void displayTaskSchedule(ScheduledTaskDto selected) {
        clear();
        this.scheduledTask = selected;
        if (scheduledTask == null) {
            scheduledTask = new ScheduledTaskDto();
            view.getForm().getSaveBtn().setText(MessagesUtil.getInstance().getMessage("button.create"));
            view.getForm().getFormPanel().setHeadingText(MessagesUtil.getInstance().getMessage("scheduledtask.title.add"));
            view.clearTableSelection();
        } else {
            view.getForm().getSaveBtn().setText(MessagesUtil.getInstance().getMessage("button.update"));
            view.getForm().getFormPanel().setHeadingText(MessagesUtil.getInstance().getMessage("scheduledtask.title.update"));
        }

        panel.nameTextBox.setText(scheduledTask.getScheduledTask().getScheduledTaskName());

        //Users
        for(UserData user : scheduledTask.getUsers()) {
            panel.selectedUsersBox.addItem(user.getUsername()+" ("+user.getEmail()+")", user.getId().toString());
        }

        // Task class
        if (scheduledTask.getScheduledTask().getTaskClassId() != null) {
            for (int i = 0; i < panel.taskClassBox.getItemCount(); i++) {
                if (panel.taskClassBox.getValue(i).equals(scheduledTask.getScheduledTask().getTaskClassId().toString())) {
                    panel.taskClassBox.setSelectedIndex(i);
                    displayTaskClassUi();
                    break;
                }
            }
        } else {
            panel.taskClassBox.setSelectedIndex(0);
        }

        //Notify customer
        if (scheduledTask.getCustomer() != null) {
            panel.customerBox.setText(scheduledTask.getCustomer().getCustomerNames());
            panel.selectedCustomer = scheduledTask.getCustomer();
        }
    }

    protected void clear() {
        scheduledTask = null;
        view.getForm().getFormPanel().setHeadingText(MessagesUtil.getInstance().getMessage("scheduledtask.title.add"));
        view.getForm().getSaveBtn().setText(MessagesUtil.getInstance().getMessage("button.create"));
        panel.clearFields();
        panel.clearErrors();
        view.clearTableSelection();
    }

    private boolean isValidInput() {
        boolean valid = true;
        panel.clearErrors();

        ScheduledTaskDto dto = updateScheduledTask();

        if (!ClientValidatorUtil.getInstance().validateField(dto.getScheduledTask(), "scheduledTaskName", panel.nameElement)) {
            valid = false;
        }

        if (panel.selectedUsersBox.getItemCount() == 0 && panel.selectedCustomer == null) {
            valid = false;
            panel.selectedUsersElement.setErrorMsg(MessagesUtil.getInstance().getMessage("taskschedule.error.taskusers"));
        }
        if (panel.selectedCustomer != null
                && !panel.customerBox.getText().trim().equals("")
                && !panel.selectedCustomer.getCustomerNames().equals(panel.customerBox.getText())) {
            valid = false;
            panel.customerElement.setErrorMsg(MessagesUtil.getInstance().getMessage("taskschedule.error.customer.notselected"));
        }

        if (panel.taskClassBox.getSelectedIndex() > 0) {
            if (panel.taskClassPanel.getWidgetCount() > 0) {
                if (!((TaskClassUi) panel.taskClassPanel.getWidget(0)).isValidInput()) {
                    valid = false;
                }
            }
        } else if (!ClientValidatorUtil.getInstance().validateField(dto.getScheduledTask(), "taskClassId", panel.taskClassElement)) {
            valid = false;
        }

        return valid;
    }

    private ScheduledTaskDto updateScheduledTask() {
        ScheduledTaskDto m = new ScheduledTaskDto();
        updateScheduledTask(m);
        return m;
    }

    private void updateScheduledTask(ScheduledTaskDto dto) {
        if (taskSchedule != null) {
            dto.getScheduledTask().setTaskScheduleId(taskSchedule.getId());
        }
        if (scheduledTask != null) {
            dto.getScheduledTask().setId(scheduledTask.getScheduledTask().getId());
        }
        //name
        dto.getScheduledTask().setScheduledTaskName(panel.nameTextBox.getText());
        //users
        for(int i=0;i<panel.selectedUsersBox.getItemCount();i++) {
            dto.getUsers().add(
                    new UserData(Long.valueOf(panel.selectedUsersBox.getValue(i)),
                                  panel.selectedUsersBox.getItemText(i),
                                  ""));
        }
        //task class and contents
        int index = panel.taskClassBox.getSelectedIndex();
        if (index > 0) {
            dto.getScheduledTask().setTaskClassId(Long.valueOf(panel.taskClassBox.getValue(index)));
            if (panel.taskClassPanel.getWidgetCount() > 0) {
                dto.getScheduledTask().setTaskContents(((TaskClassUi) panel.taskClassPanel.getWidget(0)).getTaskClassContents());
            }
        }
        //notify customer
        if (panel.selectedCustomer != null) {
            dto.getScheduledTask().setCustomerAccountId(panel.selectedCustomer.getCustomerAccountId());
        } else {
            dto.getScheduledTask().setCustomerAccountId(null);
        }
    }

    private void onSave() {
        boolean validInput = isValidInput();
        if (validInput) {
            SessionCheckCallback sessionCheckCallback = new SessionCheckCallback() {
                @Override
                public void callback(SessionCheckResolution resolution) {
                    ScheduledTaskDto dto = updateScheduledTask();
                    clientFactory.getScheduleRpc().saveScheduledTask(dto,
                            new ClientCallback<Void>(view.getForm().getSaveBtn().getAbsoluteLeft(), view.getForm().getSaveBtn().getAbsoluteTop()) {
                                @Override
                                public void onSuccess(Void result) {
                                    view.getForm().setDirtyData(false);
                                    clear();
                                    loadScheduledTasks();
                                    Dialogs.displayInformationMessage(
                                            MessagesUtil.getInstance()
                                                    .getSavedMessage(
                                                            new String[] { MessagesUtil.getInstance().getMessage("scheduledtask.type") }),
                                            MediaResourceUtil.getInstance().getInformationIcon(),
                                            view.getForm().getSaveBtn().getAbsoluteLeft(),
                                            view.getForm().getSaveBtn().getAbsoluteTop(),
                                            MessagesUtil.getInstance().getMessage("button.close"));
                                }
                            });
                }
            };
            clientFactory.handleSessionCheckCallback(sessionCheckCallback);
        }
    }

    private void onDelete() {
        if (scheduledTask != null && scheduledTask.getScheduledTask().getId() != null) {
            Dialogs.confirm(MessagesUtil.getInstance().getMessage("scheduledtask.delete.confirm"),
                    MessagesUtil.getInstance().getMessage("option.positive"),
                    MessagesUtil.getInstance().getMessage("option.negative"),
                    MediaResourceUtil.getInstance().getQuestionIcon(),
                    new ConfirmHandler() {

                @Override
                public void confirmed(boolean confirm) {
                    if (confirm) {
                        SessionCheckCallback sessionCheckCallback = new SessionCheckCallback() {
                            @Override
                            public void callback(SessionCheckResolution resolution) {
                                clientFactory.getScheduleRpc().deleteScheduledTask(scheduledTask.getScheduledTask().getId(),
                                        new ClientCallback<Void>(view.getForm().getSaveBtn().getAbsoluteLeft(), view.getForm().getSaveBtn().getAbsoluteTop()) {
                                            @Override
                                            public void onSuccess(Void result) {
                                                view.getForm().setDirtyData(false);
                                                clear();
                                                loadScheduledTasks();
                                                Dialogs.displayInformationMessage(
                                                        MessagesUtil.getInstance().getMessage("scheduledtask.deleted"),
                                                        MediaResourceUtil.getInstance().getInformationIcon(),
                                                        view.getForm().getSaveBtn().getAbsoluteLeft(),
                                                        view.getForm().getSaveBtn().getAbsoluteTop(),
                                                        MessagesUtil.getInstance().getMessage("button.close"));
                                            }
                                        });
                            }
                        };
                        clientFactory.handleSessionCheckCallback(sessionCheckCallback);
                    }
                }
            });
        } else {
            Dialogs.displayErrorMessage(MessagesUtil.getInstance().getMessage("scheduledtask.error.delete.none.selected"), MediaResourceUtil.getInstance().getErrorIcon());
        }
    }
}
