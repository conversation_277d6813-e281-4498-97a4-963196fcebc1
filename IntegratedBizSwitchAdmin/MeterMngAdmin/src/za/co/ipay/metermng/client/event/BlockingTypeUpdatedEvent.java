package za.co.ipay.metermng.client.event;

import com.google.gwt.event.shared.GwtEvent;

import za.co.ipay.metermng.mybatis.generated.model.BlockingType;

public class BlockingTypeUpdatedEvent extends GwtEvent<BlockingTypeUpdatedEventHandler> {

	public static Type<BlockingTypeUpdatedEventHandler> TYPE = new Type<BlockingTypeUpdatedEventHandler>();

	private BlockingType blockingType;

	public BlockingTypeUpdatedEvent(BlockingType blockingType) {
		this.blockingType = blockingType;
	}

	public BlockingType getBlockingType() {
		return blockingType;
	}

	public void setBlockingType(BlockingType blockingType) {
		this.blockingType = blockingType;
	}

	@Override
	public Type<BlockingTypeUpdatedEventHandler> getAssociatedType() {
		return TYPE;
	}

	@Override
	protected void dispatch(BlockingTypeUpdatedEventHandler handler) {
		handler.processBlockingTypeUpdatedEvent(this);
	}

}
