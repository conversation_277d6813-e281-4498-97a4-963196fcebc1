package za.co.ipay.metermng.client.event;

import com.google.gwt.event.shared.GwtEvent;

public class TaskScheduleEvent extends GwtEvent<TaskScheduleEventHandler> {

    public static Type<TaskScheduleEventHandler> TYPE = new Type<TaskScheduleEventHandler>();

    private String name;
    
    public TaskScheduleEvent() {
    }
    
    public TaskScheduleEvent(String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }

    @Override
    public Type<TaskScheduleEventHandler> getAssociatedType() {
        return TYPE;
    }

    @Override
    protected void dispatch(TaskScheduleEventHandler handler) {
        handler.handleEvent(this);
    }

}
