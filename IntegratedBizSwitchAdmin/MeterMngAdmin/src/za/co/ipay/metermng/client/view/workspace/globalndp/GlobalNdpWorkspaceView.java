package za.co.ipay.metermng.client.view.workspace.globalndp;

import java.util.logging.Logger;

import za.co.ipay.gwt.common.client.form.PageHeader;
import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.history.GlobalNdpPlace;
import za.co.ipay.metermng.client.view.workspace.BaseWorkspace;

import com.google.gwt.core.client.GWT;
import com.google.gwt.place.shared.Place;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.user.client.ui.HTML;
import com.google.gwt.user.client.ui.Widget;

public class GlobalNdpWorkspaceView extends BaseWorkspace  {
    
    @UiField PageHeader pageHeader;
    
    @UiField HTML dataName;
    @UiField HTML dataDescription;            
    @UiField(provided=true) GlobalNdpPanel globalNdpPanel;
    
    private static Logger logger = Logger.getLogger(GlobalNdpWorkspaceView.class.getName());

    private static GlobalNdpWorkspaceViewUiBinder uiBinder = GWT.create(GlobalNdpWorkspaceViewUiBinder.class);

    interface GlobalNdpWorkspaceViewUiBinder extends UiBinder<Widget, GlobalNdpWorkspaceView> {
    }

    public GlobalNdpWorkspaceView(ClientFactory clientFactory, GlobalNdpPlace place) {
        this.clientFactory = clientFactory;
        globalNdpPanel = new GlobalNdpPanel(clientFactory, this);
        
        initWidget(uiBinder.createAndBindUi(this));
        setPlaceString(GlobalNdpPlace.getPlaceAsString(place));
        setHeaderText(MessagesUtil.getInstance().getMessage("global.ndp.tab.heading"));
        pageHeader.setHeading(MessagesUtil.getInstance().getMessage("global.ndp.heading"));
    }
    
    @Override
    public void onArrival(Place place) {
    }

    @Override
    public void onLeaving() {
    }

    @Override
    public void onSelect() {
    }

    @Override
    public void onClose() {
    }

    @Override
    public boolean handles(Place place) {
        return (place instanceof GlobalNdpPlace);
    }

}