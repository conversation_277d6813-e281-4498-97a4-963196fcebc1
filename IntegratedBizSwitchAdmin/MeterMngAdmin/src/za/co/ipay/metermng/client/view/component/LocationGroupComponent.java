package za.co.ipay.metermng.client.view.component;

import java.util.ArrayList;
import java.util.Collections;

import com.google.gwt.core.client.GWT;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.user.client.ui.FlowPanel;
import com.google.gwt.user.client.ui.Widget;
import za.co.ipay.gwt.common.client.form.HasDirtyData;
import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.rpc.callback.ClientCallback;
import za.co.ipay.metermng.client.view.component.selection.SelectionDataWidget;
import za.co.ipay.metermng.datatypes.RecordStatus;
import za.co.ipay.metermng.mybatis.generated.model.AppSetting;
import za.co.ipay.metermng.shared.appsettings.AppSettings;
import za.co.ipay.metermng.shared.dto.LocationData;
import za.co.ipay.metermng.shared.dto.SelectionDataItem;
import za.co.ipay.metermng.shared.dto.SelectionDataItem.SelectionDataType;

public class LocationGroupComponent extends BaseComponent {

    interface LocationGroupComponentUiBinder extends UiBinder<Widget, LocationGroupComponent> {
    }

    private static LocationGroupComponentUiBinder uiBinder = GWT.create(LocationGroupComponentUiBinder.class);

    private LocationData location;
    protected HasDirtyData hasDirtyData;
    boolean mustSelectLastLevel = false;

    @UiField FlowPanel groupPanel;

    public LocationGroupComponent() {
        initWidget(uiBinder.createAndBindUi(this));
    }

    public LocationGroupComponent(ClientFactory clientFactory, HasDirtyData hasDirtyData) {
        this.clientFactory = clientFactory;
        this.hasDirtyData = hasDirtyData;
        initWidget(uiBinder.createAndBindUi(this));
    }

    public void setLocation(LocationData theLocation) {
        this.location = theLocation;
        clearFields();
        mapDataToForm();
    }

    public LocationData getLocation() {
        mapFormToData();
        return location;
    }
    
    public void getLocationGroups(final boolean select) {
        // first get the latest APP_SETTING: location.required.to.all.levels
        clientFactory.getAppSettingRpc().getAppSettingByKey(AppSettings.LOCATION_REQUIRED_TO_ALL_LEVELS,
                new ClientCallback<AppSetting>() {
                    @Override
                    public void onSuccess(AppSetting result) {
                        mustSelectLastLevel = result != null && Boolean.parseBoolean(result.getValue());
                        getLocationGroupsWithSetting(select);
                    }
                });
    }

    protected void getLocationGroupsWithSetting(final boolean select) {
        Long userSessionGroupId = clientFactory.getUser().getSessionGroupId();
        if (userSessionGroupId != null) {
            clientFactory.getGroupRpc().getParentGenGroupIdForAccessGroup(userSessionGroupId, new ClientCallback<Long>() {
                @Override
                public void onSuccess(Long id) {
                    String idStr = null;
                    if (id != null) {
                        idStr = SelectionDataItem.GROUP_PREFIX + id;
                    }
                    getLocationGroupsWithSetting2(select, idStr);
                }
            });
        } else {
            getLocationGroupsWithSetting2(select, null);
        }
    }
    
    protected void getLocationGroupsWithSetting2(final boolean select, final String accessGroupParentGroupId) {
        clientFactory.getGroupRpc().getLocationGroups(new ClientCallback<ArrayList<SelectionDataItem>>() {
            @Override
            public void onSuccess(ArrayList<SelectionDataItem> result) {
                if (accessGroupParentGroupId != null) {
                    ArrayList<SelectionDataItem> children = result.get(0).getChildren();
                    for (SelectionDataItem item : children) {
                        // If user has logged in to a group, only use that group tree.
                        if(accessGroupParentGroupId.toString().equals(item.getId())) {
                            if (accessGroupParentGroupId.toString().equals(item.getId())) {
                                children = new ArrayList<>();
                                children.add(item);
                                result.get(0).setChildren(children);
                                break;
                            }
                        }
                    }
                }
                
                Collections.sort(result);
                for (SelectionDataItem item : result) {
                    SelectionDataWidget widget = selectionDataItemAlreadyAdded(item);
                    if (widget == null) {
                        SelectionDataWidget sdw = new SelectionDataWidget(clientFactory, clientFactory.getGroupRpc(),
                                item, false, mustSelectLastLevel, null, false, hasDirtyData, accessGroupParentGroupId);
                        groupPanel.add(sdw);
                    } else{
                        widget.clear();
                    }
                }
                if (select) {
                    setSelectedGroup();
                }
            }
        });
    }

    private SelectionDataWidget selectionDataItemAlreadyAdded(SelectionDataItem item) {
        for (int j = 0, count = groupPanel.getWidgetCount(); j < count; j++) {
            Widget widget = groupPanel.getWidget(j);
            if (widget instanceof SelectionDataWidget) {
                SelectionDataWidget sdw = (SelectionDataWidget) widget;
                if (sdw.getGroupTypeId().equals(item.getActualId())) {
                    return sdw;
                }
            }
        }
        return null;
    }

    public void setSelectedGroup() {
        if (location.getLocGroupId() != null) {
            for (int index = 0, count = groupPanel.getWidgetCount(); index < count; index++) {
                Widget widget = groupPanel.getWidget(index);
                if (widget instanceof SelectionDataWidget) {
                    SelectionDataWidget sdw = (SelectionDataWidget) widget;
                    sdw.setSelectedGroup(location.getGroupDepthList(), location.getGroupTypeId());
                    break;
                }
            }
        }
    }

    public void mapDataToForm() {
        if (location != null) {
            getLocationGroups(true);
        } else {
            clearFields();
            getLocationGroups(false);
        }

    }

    public void mapFormToData() {
        if (location == null) {
            location = new LocationData();
        }

        SelectionDataWidget sdw = null;
        for (int j = 0; j < groupPanel.getWidgetCount(); j++) {
            if (groupPanel.getWidget(j) instanceof SelectionDataWidget) {
                sdw = (SelectionDataWidget) groupPanel.getWidget(j);
                break;
            }
        }
        Long selectedGroupId = sdw == null ? null : sdw.getSelectedGroup();
        location.setLocGroupId(selectedGroupId);

        location.setRecordStatus(RecordStatus.ACT);
    }

    public void clearErrors() {
        SelectionDataWidget sdw = null;
        for (int j = 0; j < groupPanel.getWidgetCount(); j++) {
            if (groupPanel.getWidget(j) instanceof SelectionDataWidget) {
                sdw = (SelectionDataWidget) groupPanel.getWidget(j);
                break;
            }
        }

        if (sdw != null) {
            sdw.clearError();
        }

    }

    public void clearFields() {
        SelectionDataWidget sdw = null;
        for (int j = 0; j < groupPanel.getWidgetCount(); j++) {
            if (groupPanel.getWidget(j) instanceof SelectionDataWidget) {
                sdw = (SelectionDataWidget) groupPanel.getWidget(j);
                break;
            }
        }

        if (sdw != null) {
            sdw.clear();
        }
    }

    public boolean validate() {
        boolean validated = true;

        clearErrors();

        SelectionDataWidget sdw = null;
        Long selectedGroup;
        for (int j = 0; j < groupPanel.getWidgetCount(); j++) {
            if (groupPanel.getWidget(j) instanceof SelectionDataWidget) {
                sdw = (SelectionDataWidget) groupPanel.getWidget(j);
                if (sdw != null && sdw.getSelectedGroupDataType() != null
                        && sdw.getSelectedGroupDataType().equals(SelectionDataType.GROUP_TYPE)) {
                    sdw.setError(MessagesUtil.getInstance().getMessage("groups.error.select.at.minimum",
                            new String[] { sdw.getLastLevelLabel() }));
                    validated = false;
                }
                if (!mustSelectLastLevel && !sdw.isRequired()) {
                    continue;
                }
                selectedGroup = sdw.getSelectedGroup();
                if (!SelectionDataItem.isActualGroupId(selectedGroup)) {
                    if (!mustSelectLastLevel && j > 0) {
                        break;
                    }
                    sdw.setError(MessagesUtil.getInstance().getMessage("usagepoint.group.required"));
                    validated = false;
                }
            }
        }

        return validated;
    }

    public void remapLocation() {
        groupPanel.clear();
        mapDataToForm();
    }
}