<!DOCTYPE ui:UiBinder SYSTEM "http://dl.google.com/gwt/DTD/xhtml.ent">
<ui:UiBinder xmlns:ui="urn:ui:com.google.gwt.uibinder" 
             xmlns:g="urn:import:com.google.gwt.user.client.ui"
             xmlns:p1="urn:import:za.co.ipay.gwt.common.client.form">
	<ui:style>	
	</ui:style>
	
	  <ui:with field="msg" type="za.co.ipay.metermng.client.i18n.UiMessages" />

	  <g:FlowPanel>
	    <p1:FormRowPanel>
	      <p1:FormElement debugId="nameElement" ui:field="nameElement" labelText="{msg.getMdcName}:" helpMsg="{msg.getMdcNameHelp}" required="true">
	        <g:TextBox debugId="nameBox" text="" ui:field="nameTextBox" title="{msg.getMdcName}" />
	      </p1:FormElement>
	      <p1:FormElement debugId="descriptionElement" ui:field="descriptionElement" labelText="{msg.getMdcDescription}:" helpMsg="{msg.getMdcDescriptionHelp}">
	        <g:TextBox debugId="descriptionBox" ui:field="descriptionTextBox" title="{msg.getMdcDescription}" />
	      </p1:FormElement>
	      <p1:FormElement ui:field="activeElement" labelText="{msg.getMdcActive}:" helpMsg="{msg.getMdcActiveHelp}">
	        <g:CheckBox debugId="activeBox" ui:field="activeBox" />
	      </p1:FormElement>
	    </p1:FormRowPanel>
	    <p1:FormRowPanel>
	   		<p1:FormElement debugId="valueElement" ui:field="valueElement" labelText="{msg.getMdcValue}:" helpMsg="{msg.getMdcValueHelp}" required="true">
	        	<g:TextBox debugId="valueBox" text="" ui:field="valueTextBox" title="{msg.getMdcValue}" />
	      	</p1:FormElement>
	    </p1:FormRowPanel>
	  </g:FlowPanel>
	
</ui:UiBinder> 