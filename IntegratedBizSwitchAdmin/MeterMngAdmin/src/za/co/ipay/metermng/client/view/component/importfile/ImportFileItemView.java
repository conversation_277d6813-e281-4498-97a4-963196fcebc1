package za.co.ipay.metermng.client.view.component.importfile;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Logger;

import com.google.gwt.cell.client.ActionCell;
import com.google.gwt.cell.client.Cell.Context;
import com.google.gwt.cell.client.CheckboxCell;
import com.google.gwt.cell.client.DateCell;
import com.google.gwt.cell.client.FieldUpdater;
import com.google.gwt.core.client.GWT;
import com.google.gwt.event.dom.client.ChangeEvent;
import com.google.gwt.event.dom.client.ClickEvent;
import com.google.gwt.event.dom.client.ClickHandler;
import com.google.gwt.event.dom.client.KeyUpEvent;
import com.google.gwt.event.dom.client.KeyUpHandler;
import com.google.gwt.event.logical.shared.ValueChangeEvent;
import com.google.gwt.i18n.client.DateTimeFormat;
import com.google.gwt.safehtml.shared.SafeHtmlBuilder;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.uibinder.client.UiHandler;
import com.google.gwt.user.cellview.client.CellTable;
import com.google.gwt.user.cellview.client.Column;
import com.google.gwt.user.cellview.client.ColumnSortEvent;
import com.google.gwt.user.cellview.client.ColumnSortEvent.AsyncHandler;
import com.google.gwt.user.cellview.client.ColumnSortList;
import com.google.gwt.user.cellview.client.ColumnSortList.ColumnSortInfo;
import com.google.gwt.user.cellview.client.RowStyles;
import com.google.gwt.user.cellview.client.TextColumn;
import com.google.gwt.user.client.Window;
import com.google.gwt.user.client.ui.Anchor;
import com.google.gwt.user.client.ui.Button;
import com.google.gwt.user.client.ui.DockLayoutPanel;
import com.google.gwt.user.client.ui.HTML;
import com.google.gwt.user.client.ui.HTMLPanel;
import com.google.gwt.user.client.ui.HasHorizontalAlignment;
import com.google.gwt.user.client.ui.Image;
import com.google.gwt.user.client.ui.ListBox;
import com.google.gwt.user.client.ui.TextBox;
import com.google.gwt.user.client.ui.Widget;
import com.google.gwt.user.datepicker.client.DateBox;
import com.google.gwt.view.client.AsyncDataProvider;
import com.google.gwt.view.client.CellPreviewEvent;
import com.google.gwt.view.client.CellPreviewEvent.Handler;
import com.google.gwt.view.client.HasData;

import za.co.ipay.gwt.common.client.Dialogs;
import za.co.ipay.gwt.common.client.factory.ResourcesFactoryUtil;
import za.co.ipay.gwt.common.client.form.FormManager;
import za.co.ipay.gwt.common.client.form.Format.FormatUtil;
import za.co.ipay.gwt.common.client.form.PageHeader;
import za.co.ipay.gwt.common.client.resource.Messages;
import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.gwt.common.client.resource.StyleNames;
import za.co.ipay.gwt.common.client.widgets.StrictDateFormat;
import za.co.ipay.gwt.common.client.widgets.TablePager;
import za.co.ipay.gwt.common.client.workspace.SessionCheckCallback;
import za.co.ipay.gwt.common.client.workspace.WaitingDialog;
import za.co.ipay.gwt.common.client.workspace.WaitingDialog.WaitingDialogUtil;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.resource.image.MediaResource.MediaResourceUtil;
import za.co.ipay.metermng.client.rpc.callback.ClientCallback;
import za.co.ipay.metermng.client.view.component.BaseComponent;
import za.co.ipay.metermng.client.view.component.importfile.filetypehelperimpl.BaseFiletypeHelper;
import za.co.ipay.metermng.client.view.component.importfile.filetypehelperimpl.BulkBlockingHelper;
import za.co.ipay.metermng.client.view.component.importfile.filetypehelperimpl.BulkFreeIssueHelper;
import za.co.ipay.metermng.client.view.component.importfile.filetypehelperimpl.BulkIpayDebtImportHelper;
import za.co.ipay.metermng.client.view.component.importfile.filetypehelperimpl.BulkKeyChangeHelper;
import za.co.ipay.metermng.client.view.component.importfile.filetypehelperimpl.BulkMdcImportHelper;
import za.co.ipay.metermng.client.view.component.importfile.filetypehelperimpl.BulkPricingStructureChangeHelper;
import za.co.ipay.metermng.client.view.component.importfile.filetypehelperimpl.BulkStoreMovementHelper;
import za.co.ipay.metermng.client.view.component.importfile.filetypehelperimpl.MeterCustUpHelper;
import za.co.ipay.metermng.client.view.component.importfile.filetypehelperimpl.PricingStructureChangeImportHelper;
import za.co.ipay.metermng.client.view.component.importfile.filetypehelperimpl.RegisterReadingImportHelper;
import za.co.ipay.metermng.client.view.component.importfile.filetypehelperimpl.SAPCustomerMovementHelper;
import za.co.ipay.metermng.client.view.component.importfile.filetypehelperimpl.SAPDebtImportHelper;
import za.co.ipay.metermng.client.view.component.importfile.filetypehelperimpl.SamrasDebtImportHelper;
import za.co.ipay.metermng.client.view.component.importfile.filetypehelperimpl.SolarDebtImportHelper;
import za.co.ipay.metermng.client.view.component.importfile.filetypehelperimpl.TariffBulkImportHelper;
import za.co.ipay.metermng.client.view.workspace.importfile.ImportFileWorkspaceView;
import za.co.ipay.metermng.shared.MeterMngStatics;
import za.co.ipay.metermng.shared.dto.importfile.ImportFileDto;
import za.co.ipay.metermng.shared.dto.importfile.ImportFileItemDto;
import za.co.ipay.metermng.shared.dto.importfile.ImportFileItemListDto;
import za.co.ipay.metermng.shared.dto.importfile.ImportFileResultDto;

/* NOTES:
 * interface IFileTypeHelper <-- /filetypehelperimpl/BaseFiletypeHelper <-- various helper class implementations for specific fileTypes
 * that need custom columns in table or detail dialogue popups or extract logic
 *
 * TO ADD A NEW IMPLEMENTATION of ImportFileItem that needs custom columns or an extract:
 * 1. Create a class that extends BaseFiletypeHelper
 * 2. Add new columns to the table here and add to the map.
 * 3. Add method to interface and base that returns null, and implement in the new class.
 * 4. In method loadFileTypeHelper() in this class, instantiate this new Helper class.
 * (GWT Can't do the Class.forName(..).newInstance(), so here is if-then-else)
 */

public class ImportFileItemView extends BaseComponent implements FormManager<ImportFileItemDto> {

    private static final int DEFAULT_PAGE_SIZE = 15;

    @UiField DockLayoutPanel mainPanel;
    @UiField PageHeader pageHeader;

    @UiField(provided=true) FileDetailPanel fileDetailPanel;

	@UiField HTML dataName;

    @UiField TextBox txtbxfilter;
    @UiField ListBox filterDropdown;
    @UiField DateBox filterDatebox;

    @UiField HTMLPanel tablePanel;
    @UiField(provided=true) CellTable<ImportFileItemDto> table;
    @UiField TablePager pager;

    @UiField Button importSelectedBtn;
    @UiField Button importAllBtn;
    @UiField Button stopImportAllBtn;
    @UiField Button extractBtn;
    @UiField Button spareBtn;
    @UiField Button backBtn;

    private AsyncDataProvider<ImportFileItemDto> dataProvider;
    private AsyncHandler columnSortHandler;

    private Integer totalResults;
    private int filterTableColIndx;
    private String filterColumnName;
    private String filterString;
    private Date filterDate;

    private TextColumn<ImportFileItemDto> uploadSuccessfulCol;
    private Column<ImportFileItemDto, Date> uploadDateCol;
    private TextColumn<ImportFileItemDto> numImportAttemptsCol;
    private TextColumn<ImportFileItemDto> lastImportSuccessfulCol;
    private TextColumn<ImportFileItemDto> commentCol;

    private Column<ImportFileItemDto, Boolean> selectCol;
    private TextColumn<ImportFileItemDto> meterCol;
    private TextColumn<ImportFileItemDto> usagePointCol;
    private TextColumn<ImportFileItemDto> agrRefCol;
    private TextColumn<ImportFileItemDto> channelValueCol;
    private TextColumn<ImportFileItemDto> readingTimeStampCol;
    private TextColumn<ImportFileItemDto> tariffNameCol;
    private TextColumn<ImportFileItemDto> pricingStructureNameCol;
    private Column<ImportFileItemDto, ImportFileItemDto> viewDetailCol;

    private ImportFileWorkspaceView parentWorkspace;
    private ImportFileDto importFileDto;
    private ImportFileItemBaseDialogueBox importFileItemBaseDialogueBox;
    private List<Long> selectedItemsList = new ArrayList<>();

    private String importFileTypeClass;
    private IFileTypeHelper fileTypeHelper;


    private static Logger logger = Logger.getLogger(ImportFileItemView.class.getName());

    private static ImportFileItemViewUiBinder uiBinder = GWT.create(ImportFileItemViewUiBinder.class);

    interface ImportFileItemViewUiBinder extends UiBinder<Widget, ImportFileItemView> {
    }

	public ImportFileItemView(ImportFileWorkspaceView parentWorkspace, ClientFactory clientFactory) {
        this.clientFactory = clientFactory;
        this.parentWorkspace = parentWorkspace;
        fileDetailPanel = new FileDetailPanel(this);
        initTable();
        initWidget(uiBinder.createAndBindUi(this));
        initUi();
    }

	private void initTable() {
        table = new CellTable<ImportFileItemDto>(DEFAULT_PAGE_SIZE, ResourcesFactoryUtil.getInstance().getCellTableResources());
    }

    private void initUi() {
        pageHeader.setHeading(MessagesUtil.getInstance().getMessage("import.file.items.header"));
        Anchor anchor = new Anchor(MessagesUtil.getInstance().getMessage("import.upload.header"));
        anchor.addClickHandler(new ClickHandler() {
            @Override
            public void onClick(ClickEvent event) {
                processGoBack();
            }
        });
        pageHeader.addPageHeaderLink(anchor);

        dataName.setText(MessagesUtil.getInstance().getMessage("import.items.title"));

        initFilters();
    }

    public void setImportFileDto(ImportFileDto importFileDto) {
        this.importFileDto = importFileDto;
        this.importFileTypeClass = importFileDto.getImportFileTypeClass();
        loadFileTypeHelper(importFileTypeClass);
        fileTypeHelper.setExtractBtn(extractBtn, importFileDto.isExtractAvailable());
        fileTypeHelper.setSpareBtn(spareBtn);
        fileTypeHelper.setRegReadingsMsgVisible(fileDetailPanel);

        if (importFileDto.isHasActionParams()) {
            if (importFileDto.getImportFilename().startsWith("dummy")) {
                importSelectedBtn.removeFromParent();
                importAllBtn.setText(MessagesUtil.getInstance().getMessage("button.submit"));
            } else {
                importSelectedBtn.setText(MessagesUtil.getInstance().getMessage("button.process.selected"));
                importAllBtn.setText(MessagesUtil.getInstance().getMessage("button.process.all"));
            }
        } else {
            importSelectedBtn.setText(MessagesUtil.getInstance().getMessage("button.import.selected"));
            importAllBtn.setText(MessagesUtil.getInstance().getMessage("button.import.all"));
        }
        loadTable();
    }

    private void loadFileTypeHelper(String importFileTypeClass) {
        //(GWT Can't do the Class.forName(..).newInstance(), so here is if-then-else)
        try {
            if (MeterMngStatics.FILETYPE_SAP_CUSTOMER_MOVEMENTS.equals(importFileTypeClass)) {
                fileTypeHelper = new SAPCustomerMovementHelper(clientFactory, this);
            } else if (MeterMngStatics.FILETYPE_SAP_DEBT_IMPORT.equals(importFileTypeClass)) {
                fileTypeHelper = new SAPDebtImportHelper(clientFactory, this);
            } else if (MeterMngStatics.FILETYPE_SAMRAS_DEBT_IMPORT.equals(importFileTypeClass)) {
                fileTypeHelper = new SamrasDebtImportHelper(clientFactory, this);
            } else if (MeterMngStatics.FILETYPE_SOLAR_DEBT_IMPORT.equals(importFileTypeClass)) {
                fileTypeHelper = new SolarDebtImportHelper(clientFactory, this);
            } else if (MeterMngStatics.FILETYPE_REGISTER_READING_IMPORT.equals(importFileTypeClass)) {
                fileTypeHelper = new RegisterReadingImportHelper(clientFactory, this);
            } else if (MeterMngStatics.FILETYPE_BULK_TARIFF_UPDATER.equals(importFileTypeClass)) {
                fileTypeHelper = new TariffBulkImportHelper(clientFactory, this);
            } else if (MeterMngStatics.FILETYPE_METERCUSTUP_BULK_IMPORT.equals(importFileTypeClass)) {
                fileTypeHelper = new MeterCustUpHelper(clientFactory, this);
            } else  if (MeterMngStatics.FILETYPE_BULK_KEY_CHANGE_GENERATOR.equals(importFileTypeClass)) {
                fileTypeHelper = new BulkKeyChangeHelper(clientFactory, this);
            } else if (MeterMngStatics.FILETYPE_BULK_PRICING_STRUCTURE_CHANGE_GENERATOR.equals(importFileTypeClass)) {
                fileTypeHelper = new BulkPricingStructureChangeHelper(clientFactory, this);
            } else if (MeterMngStatics.FILETYPE_BULK_BLOCKING_GENERATOR.equals(importFileTypeClass)) {
                fileTypeHelper = new BulkBlockingHelper(clientFactory, this);
            } else if (MeterMngStatics.FILETYPE_BULK_STORE_MOVEMENT.equals(importFileTypeClass)) {
                fileTypeHelper = new BulkStoreMovementHelper(clientFactory, this);
            } else if (MeterMngStatics.FILETYPE_IPAY_DEBT_IMPORT.equals(importFileTypeClass)) {
                fileTypeHelper = new BulkIpayDebtImportHelper(clientFactory, this);
            } else if (MeterMngStatics.FILETYPE_PRICING_STRUCTURE_CHANGE_IMPORT.equals(importFileTypeClass)) {
                fileTypeHelper = new PricingStructureChangeImportHelper(clientFactory, this);
            } else if (MeterMngStatics.FILETYPE_BULK_MDC_IMPORT.equals(importFileTypeClass)) {
                fileTypeHelper = new BulkMdcImportHelper(clientFactory, this);
            } else if (MeterMngStatics.FILETYPE_BULK_FREE_ISSUE.equals(importFileTypeClass)) {
                fileTypeHelper = new BulkFreeIssueHelper(clientFactory, this);
            } else {
                fileTypeHelper = new BaseFiletypeHelper(clientFactory, this);
            }
        } catch (Exception e) {
            throw new RuntimeException("Unknown fileTypeHelperClass", e);
        }
    }

    private void initFilters() {
        filterDropdown.addItem("");
        filterDropdown.addItem(MessagesUtil.getInstance().getMessage("import.upload.successful.label"));
        filterDropdown.addItem(MessagesUtil.getInstance().getMessage("import.upload.date.label"));
        filterDropdown.addItem(MessagesUtil.getInstance().getMessage("import.last.successful.label"));

        filterDatebox
                .setFormat(new StrictDateFormat(DateTimeFormat.getFormat(FormatUtil.getInstance().getDateFormat())));
        filterDatebox.getTextBox().addKeyUpHandler(new KeyUpHandler() {
            @Override
            public void onKeyUp(KeyUpEvent event) {
               handleDateFilter();
            }
        });
    }

	private void createTable() {

	    selectCol = new Column<ImportFileItemDto, Boolean>(
	            new CheckboxCell(true, true)) {
	        @Override
	        public Boolean getValue(ImportFileItemDto object) {
	            boolean selected = object.isSelectForImport() || selectedItemsList.contains(object.getImportFileItem().getId());
	            return selected;
	        }
	        @Override
	        public void render(Context context, ImportFileItemDto object, SafeHtmlBuilder sb){
	            //Do not render select-for-import checkbox if already imported and fileType.allowItemsMultipleImport = false
                if (!importFileDto.getAllowItemsMultipleImport() &&
                        object.getImportFileItem().getLastImportSuccessful()) {
                } else {
	              super.render(context, object, sb);
	           }
	        }
	    };

	    selectCol.setFieldUpdater(new FieldUpdater<ImportFileItemDto, Boolean>() {

	        @Override
	        public void update(int index, ImportFileItemDto object, Boolean value) {
	            object.setSelectForImport(value);
	            Long id = object.getImportFileItem().getId();
	            if (selectedItemsList.contains(id)) {
	                if (!value) {
	                    selectedItemsList.remove(id);
	                }
	            } else {
	                if (value) {
	                    selectedItemsList.add(id);
	                }
	            }
	        }
	    });
	    selectCol.setHorizontalAlignment(HasHorizontalAlignment.ALIGN_CENTER);

        uploadSuccessfulCol = new TextColumn<ImportFileItemDto>() {
            @Override
            public String getValue(ImportFileItemDto object) {
                return String.valueOf(object.getImportFileItem().getUploadSuccessful());
            }
        };
        uploadSuccessfulCol.setSortable(true);
        uploadSuccessfulCol.setHorizontalAlignment(HasHorizontalAlignment.ALIGN_CENTER);

        DateCell dateCell = new DateCell(DateTimeFormat.getFormat(FormatUtil.getInstance().getDateTimeFormat()));
        uploadDateCol = new Column<ImportFileItemDto, Date>(dateCell) {
            @Override
            public Date getValue(ImportFileItemDto object) {
                return object.getImportFileItem().getUploadDate();
            }
        };
        uploadDateCol.setSortable(true);
        uploadDateCol.setHorizontalAlignment(HasHorizontalAlignment.ALIGN_CENTER);

        numImportAttemptsCol = new TextColumn<ImportFileItemDto>() {
            @Override
            public String getValue(ImportFileItemDto object) {
                return String.valueOf(object.getImportFileItem().getNumImportAttempts());
            }
        };
        numImportAttemptsCol.setSortable(true);
        numImportAttemptsCol.setHorizontalAlignment(HasHorizontalAlignment.ALIGN_CENTER);

        lastImportSuccessfulCol = new TextColumn<ImportFileItemDto>() {
            @Override
            public String getValue(ImportFileItemDto object) {
                return String.valueOf(object.getImportFileItem().getLastImportSuccessful());
            }
        };
        lastImportSuccessfulCol.setSortable(true);
        lastImportSuccessfulCol.setHorizontalAlignment(HasHorizontalAlignment.ALIGN_CENTER);

        commentCol = new TextColumn<ImportFileItemDto>() {
            @Override
            public String getValue(ImportFileItemDto object) {
                String comment = object.getImportFileItem().getComment();
                if (comment == null) comment = "";
                String extraComment = object.getImportFileItemImportComment();
                if (extraComment != null && !extraComment.isEmpty()) {
                    if (comment != null && !comment.isEmpty()) {
                        comment = comment + "; ";
                    }
                    comment = comment + extraComment;
                }
                return comment;
            }
        };

        @SuppressWarnings("rawtypes")
        Map<String, Column> tableColumnMap = new HashMap<>();

        meterCol = new TextColumn<ImportFileItemDto>() {
            @Override
            public String getValue(ImportFileItemDto object) {
                return fileTypeHelper.getMeterColumnValue(object);
            }
        };
        tableColumnMap.put("meterCol", meterCol);

        usagePointCol = new TextColumn<ImportFileItemDto>() {
            @Override
            public String getValue(ImportFileItemDto object) {
                return fileTypeHelper.getUsagePointColumnValue(object);
            }
        };
        tableColumnMap.put("usagePointCol", usagePointCol);

        agrRefCol = new TextColumn<ImportFileItemDto>() {
            @Override
            public String getValue(ImportFileItemDto object) {
                return fileTypeHelper.getAgrRefColumnValue(object);
            }
        };
        tableColumnMap.put("agrRefCol", agrRefCol);

        channelValueCol = new TextColumn<ImportFileItemDto>() {
            @Override
            public String getValue(ImportFileItemDto object) {
                return fileTypeHelper.getChannelValueColumnValue(object);
            }
        };
        tableColumnMap.put("channelValueCol", channelValueCol);

        readingTimeStampCol = new TextColumn<ImportFileItemDto>() {
            @Override
            public String getValue(ImportFileItemDto object) {
                return fileTypeHelper.getReadingTimeStampColumnValue(object);
            }
        };
        tableColumnMap.put("readingTimeStampCol", readingTimeStampCol);

        pricingStructureNameCol = new TextColumn<ImportFileItemDto>() {
            @Override
            public String getValue(ImportFileItemDto object) {
                return fileTypeHelper.getPricingStructureNameColumnValue(object);
            }
        };
        tableColumnMap.put("pricingStructureNameCol", pricingStructureNameCol);

        tariffNameCol = new TextColumn<ImportFileItemDto>() {
            @Override
            public String getValue(ImportFileItemDto object) {
                return fileTypeHelper.getTariffNameColumnValue(object);
            }
        };
        tableColumnMap.put("tariffNameCol", tariffNameCol);

        Messages messagesInstance = MessagesUtil.getInstance();
        ActionCell<ImportFileItemDto> viewDetailCell = new ActionCell<ImportFileItemDto>(
                messagesInstance.getMessage("import.upload.open.label"), new ActionCell.Delegate<ImportFileItemDto>() {
                    @Override
                    public void execute(ImportFileItemDto object) {
                        importFileItemBaseDialogueBox.setAutoHideEnabled(false);
                        importFileItemBaseDialogueBox.setAutoHideOnHistoryEventsEnabled(false);
                        importFileItemBaseDialogueBox.setAnimationEnabled(true);
                        importFileItemBaseDialogueBox.setModal(true);
                        importFileItemBaseDialogueBox.setGlassEnabled(true);
                        importFileItemBaseDialogueBox.setText(MessagesUtil.getInstance().getMessage("import.items.edit.header"));
                        importFileItemBaseDialogueBox.loadItem(object);
                        importFileItemBaseDialogueBox.setPopupPosition(Window.getClientWidth() / 2, 150);
                        importFileItemBaseDialogueBox.show();
                    }
                });

        viewDetailCol = new Column<ImportFileItemDto, ImportFileItemDto>(viewDetailCell) {
            public ImportFileItemDto getValue(ImportFileItemDto object) {
                return object;
            }
            @Override
            public void render(Context context, ImportFileItemDto object, SafeHtmlBuilder sb){
                boolean allowItemsMultipleImport = importFileDto.getAllowItemsMultipleImport();
                if (!allowItemsMultipleImport && object.getImportFileItem().getLastImportSuccessful()) {
                    //just don't render the button
                } else {
                  super.render(context, object, sb);
               }
            }
        };
        viewDetailCol.setHorizontalAlignment(HasHorizontalAlignment.ALIGN_CENTER);

        table.setWidth("auto", true);

        table.addColumn(selectCol, messagesInstance.getMessage("import.select.label"));
        table.addColumn(uploadSuccessfulCol, messagesInstance.getMessage("import.upload.successful.label"));
        table.addColumn(uploadDateCol, messagesInstance.getMessage("import.upload.date.label"));
        table.addColumn(numImportAttemptsCol, messagesInstance.getMessage("import.num.attempts.label"));
        table.addColumn(lastImportSuccessfulCol, messagesInstance.getMessage("import.last.successful.label"));
        table.addColumn(commentCol, messagesInstance.getMessage("import.comment.label"));
        fileTypeHelper.addCustomColumnsToTable(table, tableColumnMap);
        table.addColumn(viewDetailCol, messagesInstance.getMessage("import.upload.detail"));

        // Set the range to display
        table.setVisibleRange(0, getPageSize());

        //when allowItemsMultipleImport=false can't repeat the import
        if (!importFileDto.getAllowItemsMultipleImport()) {
            table.setRowStyles(new RowStyles<ImportFileItemDto>() {
                @Override
                public String getStyleNames(ImportFileItemDto rowObject, int rowIndex) {
                    if (rowObject.getImportFileItem().getLastImportSuccessful()) {
                        return "disabled";
                    } else {
                        return "normaltext";
                    }
                }
            });
        }

        // Set the data provider for the table
        dataProvider = new AsyncDataProvider<ImportFileItemDto>() {
            @Override
            protected void onRangeChanged(HasData<ImportFileItemDto> display) {
                //maintain current sort arrangement, so pick the last ColumnSortInfo off the top of the sortList
                ColumnSortList sortList = table.getColumnSortList();
                int columnIndex = table.getColumnIndex(uploadDateCol);
                boolean isAscending = false;

                if (sortList != null && sortList.size() != 0) {
                    @SuppressWarnings("unchecked")
                    Column<ImportFileItemDto, ?> sColumn = (Column<ImportFileItemDto, ?>) sortList.get(0).getColumn();
                    columnIndex = table.getColumnIndex(sColumn);
                    isAscending = sortList.get(0).isAscending();
                }

                if(totalResults != null) {                     //can only change range, if actually HAVE something in table!
                    getTableData(display.getVisibleRange().getStart(), columnIndex, isAscending );
                }
            }
        };
        dataProvider.addDataDisplay(table);

        // Create the table's pager
        pager.setDisplay(table);

        // Set the table's column sorter handler
        columnSortHandler = new AsyncHandler(table) {
            @Override
            public void onColumnSort(ColumnSortEvent event) {
                @SuppressWarnings("unchecked")
                int sortIndex = table.getColumnIndex((Column<ImportFileItemDto, ?>) event.getColumn());
                boolean isAscending = event.isSortAscending();

                getTableData(0, sortIndex, isAscending);
            }
        };

        table.addColumnSortHandler(columnSortHandler);
        table.getColumnSortList().push(uploadSuccessfulCol);
        table.getColumnSortList().push(uploadDateCol);
        table.getColumnSortList().push(numImportAttemptsCol);
        table.getColumnSortList().push(lastImportSuccessfulCol);
        table.getColumnSortList().push(uploadDateCol);  //last here so first time it sorts this (top of stack!)

        dataProvider.updateRowCount(0, true);
        table.setRowCount(0, true);

        clearFilter();
    }

	private String getColumnName(int index) {
	    //returns fieldName from database passed through to "order by" clause on server side
	    switch (index) {
	    case 1: return "upload_successful";
	    case 2: return "upload_date";
	    case 3: return "num_import_attempts";
	    case 4: return "last_import_successful";
	    default: return null;
	    }
	}

	@UiHandler("importSelectedBtn")
	public void importSelectedItems(ClickEvent e) {
	    if (selectedItemsList.isEmpty()) {
	        Dialogs.centreErrorMessage(MessagesUtil.getInstance().getMessage("import.selected.items.non"),
	                                   MediaResourceUtil.getInstance().getErrorIcon(),
	                                   MessagesUtil.getInstance().getMessage("button.close"));
	        return;
	    }

	    if (!startOfImport(importFileDto)) {
	        return;
	    }
	    SessionCheckCallback sessionCheckCallback = new SessionCheckCallback() {
            @Override
            public void callback(SessionCheckResolution resolution) {
                clientFactory.getImportFileDataRpc().importSelectedItems(clientFactory.getUser().getUserName(), importFileDto, selectedItemsList, clientFactory.isEnableAccessGroups(), new ClientCallback<ImportFileResultDto>() {
                    @Override
                    public void onSuccess(ImportFileResultDto importFileResultDto) {
                        String message = MessagesUtil.getInstance().getMessage("import.items.selected.success");
                        message = getImportFeedbackMessage(message, importFileResultDto);
                        Integer numFailedUpload = importFileResultDto.getNumFailedUpload();
                        if (numFailedUpload.compareTo(0) != 0 ) {
                            message = message + MessagesUtil.getInstance().getMessage("import.items.unsuccessful.uploads.reminder", new String[] {numFailedUpload.toString()});
                        }
                        postImport(message);
                    }
                    @Override
                    public void onFailure(Throwable caught) {
                        enableButtons();
                        //reset message about still busy importing
                        fileDetailPanel.setStillBusy(null, false);
                        super.onFailure(caught);
                    }
                });
            }
        };
        clientFactory.handleSessionCheckCallback(sessionCheckCallback);
    }

    @UiHandler("importAllBtn")
    public void importAllItems(ClickEvent e) {
        if (!startOfImport(importFileDto)) {
            return;
        }
        stopImportAllBtn.setVisible(true);
        stopImportAllBtn.setEnabled(true);
        SessionCheckCallback sessionCheckCallback = new SessionCheckCallback() {
            @Override
            public void callback(SessionCheckResolution resolution) {
                clientFactory.getImportFileDataRpc().importAllItems(clientFactory.getUser().getUserName(), importFileDto, clientFactory.isEnableAccessGroups(), new ClientCallback<ImportFileResultDto>() {
                    @Override
                    public void onSuccess(ImportFileResultDto importFileResultDto) {
                        String message = MessagesUtil.getInstance().getMessage("import.items.all.success");
                        message = getImportFeedbackMessage(message, importFileResultDto);
                        Integer numFailedUpload = importFileResultDto.getNumFailedUpload();
                        if (numFailedUpload.compareTo(0) != 0) {
                            message = message + MessagesUtil.getInstance().getMessage("import.items.unsuccessful.uploads.reminder", new String[]{numFailedUpload.toString()});
                        }
                        if (importFileResultDto.getStoppedImport()) {
                            message = message + " " + MessagesUtil.getInstance().getMessage("import.file.stopped");
                        }
                        postImport(message);

                    }

                    @Override
                    public void onFailure(Throwable caught) {
                        enableButtons();
                        stopImportAllBtn.setVisible(false);
                        stopImportAllBtn.setEnabled(true);
                        super.onFailure(caught);
                    }
                });
            }
        };
        clientFactory.handleSessionCheckCallback(sessionCheckCallback);
    }

    private String getImportFeedbackMessage(String message, ImportFileResultDto importFileResultDto) {
        if (!importFileResultDto.getSuccess()) {
            if (importFileResultDto.getAbortMsg() != null) {
                message = MessagesUtil.getInstance().getMessage("import.items.abort") + importFileResultDto.getAbortMsg();
            } else {
                message = MessagesUtil.getInstance().getMessage("import.items.errors");
            }
        }
        return message;
    }

    @UiHandler("extractBtn")
    public void extractItems(ClickEvent e) {
        SessionCheckCallback sessionCheckCallback = new SessionCheckCallback() {
            @Override
            public void callback(SessionCheckResolution resolution) {
                fileTypeHelper.generateCsvDownload(logger, importFileDto);
            }
        };
        clientFactory.handleSessionCheckCallback(sessionCheckCallback);
    }

    @UiHandler("spareBtn")
    public void handleSpareBtn(ClickEvent e) {
        SessionCheckCallback sessionCheckCallback = new SessionCheckCallback() {
            @Override
            public void callback(SessionCheckResolution resolution) {
                fileTypeHelper.handleSpareBtn(logger, importFileDto);
            }
        };
        clientFactory.handleSessionCheckCallback(sessionCheckCallback);
    }

    @UiHandler("stopImportAllBtn")
    public void stopImportAll(ClickEvent e) {
        SessionCheckCallback sessionCheckCallback = new SessionCheckCallback() {
            @Override
            public void callback(SessionCheckResolution resolution) {
                stopImportAllBtn.setEnabled(false);

                clientFactory.getImportFileDataRpc().stopImportAll(clientFactory.getUser().getUserName(), importFileDto, new ClientCallback<Void>() {
                    @Override
                    public void onSuccess(Void result) {
                        fileDetailPanel.setStillBusy(MessagesUtil.getInstance().getMessage("import.file.item.view.still.busy.stopped"), true);

                        Dialogs.centreErrorMessage(MessagesUtil.getInstance().getMessage("import.file.stopped.instruction"),
                                MediaResourceUtil.getInstance().getErrorIcon(),
                                MessagesUtil.getInstance().getMessage("button.close"));

                    }
                });
            }
        };
        clientFactory.handleSessionCheckCallback(sessionCheckCallback);
    }

    private void postImport(String message) {
        WaitingDialog waiting = WaitingDialogUtil.getCurrentInstance();
        if (waiting != null) {
            waiting.hide();
        }

        selectedItemsList.clear();
        enableButtons();
        stopImportAllBtn.setEnabled(true);
        stopImportAllBtn.setVisible(false);

        refreshTable();
        refreshFileDetailPanel();


        Dialogs.centreMessage(message, new Image(MediaResourceUtil.getInstance().getInformationIcon()),
                StyleNames.POPUP_MESSAGE,
                MessagesUtil.getInstance().getMessage("button.close"), null,
                true, true);

    }

    private boolean startOfImport(ImportFileDto importFileDto) {
        String actionParams = importFileDto.getActionParams();
        if (importFileDto.isHasActionParams() && (actionParams == null || actionParams.isEmpty())) {
            Dialogs.centreErrorMessage(MessagesUtil.getInstance().getMessage("import.upload.file.needs.action.params"),
                    MediaResourceUtil.getInstance().getErrorIcon(),
                    MessagesUtil.getInstance().getMessage("button.close"));
            return false;
        }

        disableButtons();
        fileDetailPanel.setStillBusy(MessagesUtil.getInstance().getMessage("import.file.item.view.still.busy"), true);
        String waitingText = fileTypeHelper.getWaitingText();
        WaitingDialogUtil.getInstance(MediaResourceUtil.getInstance().getWaitIcon(),
                backBtn.getAbsoluteLeft() + 100, backBtn.getAbsoluteTop(),
                waitingText);
        return true;
    }

    public void disableButtons() {
        importSelectedBtn.setEnabled(false);
        importAllBtn.setEnabled(false);
        extractBtn.setEnabled(false);
        spareBtn.setEnabled(false);
    }

    public void enableButtons() {
        importSelectedBtn.setEnabled(true);
        importAllBtn.setEnabled(true);
        extractBtn.setEnabled(true);
        spareBtn.setEnabled(true);
        backBtn.setEnabled(true);
    }

    protected void checkBusySetButtons(boolean isImportStillBusy) {
        if (isImportStillBusy) {
            disableButtons();
            if (importFileDto.getLastImportTypeAll() != null && importFileDto.getLastImportTypeAll()) {
                stopImportAllBtn.setVisible(true);
            } else {
                stopImportAllBtn.setVisible(false);
            }
        } else {
            enableButtons();
            stopImportAllBtn.setVisible(false);
        }
    }

    private void refreshFileDetailPanel() {
           clientFactory.getImportFileDataRpc().getImportFileDtoById(importFileDto.getId(), new ClientCallback<ImportFileDto>() {
                @Override
                public void onSuccess(ImportFileDto result) {
                    importFileDto = result;
                    fileDetailPanel.setLabels(importFileDto);
                    fileDetailPanel.setWarnMaxLicenseExceeded(importFileDto.getImportFileTypeClass(),
                            importFileDto.getLicenceCount(), importFileDto.getCurrentCount(),
                            (int) (importFileDto.getNumItems()-importFileDto.getNumSuccessfulImports()));
                    }
            });
    }

    protected String getImportFileTypeName() {
        return importFileDto == null ? null : importFileDto.getImportFileTypeName();
    }

    public void refreshTable() {
        //maintain current sort arrangement, so pick the last ColumnSortInfo off the top of the sortList
        ColumnSortList sortList = table.getColumnSortList();
        int columnIndex = table.getColumnIndex(uploadDateCol);
        boolean isAscending = true;

        if (sortList != null && sortList.size() != 0) {
            @SuppressWarnings("unchecked")
            Column<ImportFileItemDto, ?> sColumn = (Column<ImportFileItemDto, ?>) sortList.get(0).getColumn();
            columnIndex = table.getColumnIndex(sColumn);
            isAscending = sortList.get(0).isAscending();
        }

        getTableData(table.getVisibleRange().getStart(), columnIndex, isAscending );
    }

    @UiHandler("backBtn")
    public void doGoBack(ClickEvent e) {
        processGoBack();
    }

    private void processGoBack() {
        parentWorkspace.goToImportFile();
    }

	private void loadTable() {
	    fileDetailPanel.setLabels(importFileDto);//configureButtons(importFileDto);
        fileDetailPanel.setWarnMaxLicenseExceeded(importFileDto.getImportFileTypeClass(),
                importFileDto.getLicenceCount(), importFileDto.getCurrentCount(),
                (int) (importFileDto.getNumItems()-importFileDto.getNumSuccessfulImports()));

	    table.removeFromParent();
	    initTable();
	    createTable();
	    tablePanel.add(table);

        table.addCellPreviewHandler(new Handler<ImportFileItemDto>() {
            @Override
            public void onCellPreview(CellPreviewEvent<ImportFileItemDto> event) {
                if ("mouseover".equals(event.getNativeEvent().getType())) {
                    ImportFileItemDto cellElement = event.getValue();
                    table.getRowElement(event.getIndex() - table.getVisibleRange().getStart()).getCells().getItem(event.getColumn())
                            .setTitle(cellElement.getImportFileItem().getItemData());//
                }
            }
        });
        table.setWidth("auto", true);
        importFileItemBaseDialogueBox = fileTypeHelper.setImportFileItemBaseDialogueBox();

	    clearTable();
	    clearFilter();

	    final int start = 0;
	    uploadDateCol.setDefaultSortAscending(true);

	    int uploadStartDateColIndx = table.getColumnIndex(uploadDateCol);
	    setTableSortOrder(uploadStartDateColIndx);
	    getTableData(start, uploadStartDateColIndx, true);
	}

    private boolean setTableSortOrder(int tableColIndx) {
        Integer colIndxInSortList = findColIndxInSortList(tableColIndx);
        if (colIndxInSortList != null) {
            ColumnSortInfo colSortInfo = table.getColumnSortList().get(colIndxInSortList);
            if (colSortInfo.isAscending()) {          //already ascending...
                table.getColumnSortList().push(colSortInfo);
            } else {
                table.getColumnSortList().push(table.getColumn(tableColIndx));
            }
        } else {
            table.getColumnSortList().push(table.getColumn(tableColIndx));
        }

        return table.getColumnSortList().get(0).isAscending();
    }

    private void getTableData(final int start, int tableColIndex, Boolean isAscending) {
        clearTable();
        String sortColumnName = getColumnName(tableColIndex);

        final int pageSize = getPageSize();
        String order = "ASC";
        if (!isAscending) {
            order = "DESC";
        }

        clientFactory.getImportFileDataRpc().selectImportFileItems(importFileDto.getId(),
                start, pageSize, sortColumnName, filterColumnName, filterString, filterDate, order,
                new ClientCallback<ImportFileItemListDto>() {
            @Override
            public void onSuccess(ImportFileItemListDto resultDto) {
                pager.setPageStart(start);

                totalResults = 0;
                List<ImportFileItemDto> result = new ArrayList<ImportFileItemDto>();
                if (resultDto != null) {
                    result = resultDto.getListOfImportFileItemDto();
                    totalResults = resultDto.getResultCount();
                }
                //Set the actual total search results count ... done this way because will only send back the 10 results from start so can't use .size()
                dataProvider.updateRowCount(totalResults, true);
                table.setRowCount(totalResults, true);

                //Display the results
                dataProvider.updateRowData(start, result);
                logger.info("Set dataProvider data: start:" + start + " size:" + result.size());
            }
        });
    }

	public void clearTable() {
	    dataProvider.updateRowData(0, new ArrayList<ImportFileItemDto>());
	    dataProvider.updateRowCount(0, true);
	    totalResults = null;
	    table.setRowCount(0, true);

	    logger.info("clearTable(): Set dataProvider data: start:0  new ArrayList<ImportFileItemDto>()");
	}

	//*******************************************************************************************************
	private void clearFilter() {
	    filterColumnName = null;
	    filterString = null;
	    filterDate = null;

	    filterDropdown.setSelectedIndex(0);
	    txtbxfilter.setText("");
	    txtbxfilter.setVisible(true);
	    filterDatebox.getTextBox().setText("");
	    filterDatebox.setVisible(false);
	}

	@UiHandler("txtbxfilter")
	void handleFilterChange(KeyUpEvent event) {
	    if (filterDropdown.getSelectedIndex() < 1) {
	        filterString = null;
	        filterTableColIndx = table.getColumnIndex(uploadDateCol);
	    } else {
	        String textFilter = txtbxfilter.getText().trim();
	        filterString = textFilter.isEmpty() ? null : textFilter;
	    }
	    changeFilter(filterTableColIndx);
	}

	@UiHandler("filterDropdown")
	void handleFilterDropdownSelection(ChangeEvent changeEvent) {
	    filterColumnName = null;
	    filterString = null;
	    filterDate = null;

	    txtbxfilter.setText("");
	    filterDatebox.getTextBox().setText("");

        filterDatebox.setVisible(filterDropdown.getItemText(filterDropdown.getSelectedIndex()).equals(MessagesUtil.getInstance().getMessage("import.upload.date.label")));
        txtbxfilter.setVisible(!filterDropdown.getItemText(filterDropdown.getSelectedIndex()).equals(MessagesUtil.getInstance().getMessage("import.upload.date.label")));

	    //If have cleared the filter dropdown, leave filterColumnName null and reset the filtering
	    if (filterDropdown.getSelectedIndex() < 1) {
	        filterTableColIndx = table.getColumnIndex(uploadDateCol);
	        changeFilter(table.getColumnIndex(uploadDateCol));
	        return;
	    }

	    //establish WHICH column to filter on; using db field names to pass to group select
        if (filterDropdown.getValue(filterDropdown.getSelectedIndex()).equals(MessagesUtil.getInstance().getMessage("import.upload.successful.label"))) {
            filterTableColIndx = table.getColumnIndex(uploadSuccessfulCol);
        } else if (filterDropdown.getValue(filterDropdown.getSelectedIndex()).equals(MessagesUtil.getInstance().getMessage("import.upload.date.label"))) {
            filterTableColIndx = table.getColumnIndex(uploadDateCol);
        } else if (filterDropdown.getValue(filterDropdown.getSelectedIndex()).equals(MessagesUtil.getInstance().getMessage("import.last.successful.label"))) {
            filterTableColIndx = table.getColumnIndex(lastImportSuccessfulCol);
        } else {
            return;
        }

	    //Only set filterColumnName if there WAS actually a selection.
	    filterColumnName=getColumnName(filterTableColIndx);
	}

	@UiHandler("filterDatebox")
	void handleDateFilterChange(ValueChangeEvent<Date> event) {
	    handleDateFilter();
	}

	protected void handleDateFilter() {
	    if (filterDatebox.getTextBox().getText().trim().isEmpty()) {
	        clearFilter();
	        changeFilter(0);
	    } else {
	        filterDate = filterDatebox.getValue();
	        changeFilter(table.getColumnIndex(uploadDateCol));
	    }
	}

	private void changeFilter(int tableColIndx) {
	    final int start = 0;      //table.getVisibleRange().getStart();
	    Integer colIndxInSortList = findColIndxInSortList(tableColIndx);

	    if (colIndxInSortList != null) {
	        ColumnSortInfo colSortInfo = table.getColumnSortList().get(colIndxInSortList);
	        table.getColumnSortList().push(colSortInfo);
	    } else {
	        table.getColumnSortList().push(table.getColumn(tableColIndx));
	    }

	    getTableData(start, tableColIndx, table.getColumnSortList().get(0).isAscending());
	}

    private Integer findColIndxInSortList(int tableColIndx) {
        for (int i=0; i<table.getColumnSortList().size(); i++) {
            @SuppressWarnings("unchecked")
            Column<ImportFileItemDto, ?> sColumn = (Column<ImportFileItemDto, ?>) table.getColumnSortList().get(i).getColumn();
            if (table.getColumnIndex(sColumn) == tableColIndx) {
                return i;
            }
        }
        return null;
    }

    @Override
    public void displaySelected(ImportFileItemDto selected) {
    }

}
