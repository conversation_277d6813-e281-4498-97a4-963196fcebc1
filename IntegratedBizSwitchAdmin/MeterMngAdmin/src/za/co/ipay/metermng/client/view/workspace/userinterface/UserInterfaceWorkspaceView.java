package za.co.ipay.metermng.client.view.workspace.userinterface;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

import com.google.gwt.cell.client.CheckboxCell;
import com.google.gwt.cell.client.FieldUpdater;
import com.google.gwt.cell.client.TextInputCell;
import com.google.gwt.core.client.GWT;
import com.google.gwt.core.client.JavaScriptException;
import com.google.gwt.dom.client.Style;
import com.google.gwt.event.dom.client.ClickEvent;
import com.google.gwt.event.logical.shared.ResizeEvent;
import com.google.gwt.event.logical.shared.ResizeHandler;
import com.google.gwt.place.shared.Place;
import com.google.gwt.regexp.shared.RegExp;
import com.google.gwt.safehtml.shared.SafeHtmlBuilder;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.uibinder.client.UiHandler;
import com.google.gwt.user.cellview.client.CellTable;
import com.google.gwt.user.cellview.client.Column;
import com.google.gwt.user.cellview.client.HasKeyboardSelectionPolicy.KeyboardSelectionPolicy;
import com.google.gwt.user.cellview.client.TextColumn;
import com.google.gwt.user.client.Window;
import com.google.gwt.user.client.ui.HTML;
import com.google.gwt.user.client.ui.HTMLPanel;
import com.google.gwt.user.client.ui.ScrollPanel;
import com.google.gwt.user.client.ui.VerticalPanel;
import com.google.gwt.user.client.ui.Widget;
import com.google.gwt.view.client.ListDataProvider;

import za.co.ipay.gwt.common.client.Dialogs;
import za.co.ipay.gwt.common.client.factory.ResourcesFactoryUtil;
import za.co.ipay.gwt.common.client.form.PageHeader;
import za.co.ipay.gwt.common.client.handler.ConfirmHandler;
import za.co.ipay.gwt.common.client.resource.Messages;
import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.gwt.common.client.workspace.SessionCheckCallback;
import za.co.ipay.gwt.common.client.workspace.WorkspaceNotification;
import za.co.ipay.gwt.common.client.workspace.WorkspaceNotification.NotificationType;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.history.UserInterfacePlace;
import za.co.ipay.metermng.client.resource.image.MediaResource;
import za.co.ipay.metermng.client.resource.image.MediaResource.MediaResourceUtil;
import za.co.ipay.metermng.client.rpc.UserInterfaceRpcAsync;
import za.co.ipay.metermng.client.rpc.callback.ClientCallback;
import za.co.ipay.metermng.client.view.workspace.BaseWorkspace;
import za.co.ipay.metermng.mybatis.generated.model.Form;
import za.co.ipay.metermng.mybatis.generated.model.FormFields;
import za.co.ipay.metermng.shared.MeterMngStatics;
import za.co.ipay.metermng.shared.userinterface.UserInterfaceFormFields;

public class UserInterfaceWorkspaceView extends BaseWorkspace {

    @UiField VerticalPanel tablesPanel;
    @UiField ScrollPanel scrollPanel;
    @UiField HTMLPanel buttonsPanel;
    @UiField PageHeader pageHeader;

    private ArrayList<ListDataProvider<FormFields>> formFieldsData;
    private HashMap<Long, FormFields> fieldUpdates = new HashMap<Long, FormFields>();
    private int heightOffset;

    private static UserInterfaceWorkspaceViewUiBinder uiBinder = GWT.create(UserInterfaceWorkspaceViewUiBinder.class);

    interface UserInterfaceWorkspaceViewUiBinder extends UiBinder<Widget, UserInterfaceWorkspaceView> {
    }

    public UserInterfaceWorkspaceView(ClientFactory clientFactory, UserInterfacePlace place) {
        this.clientFactory = clientFactory;
        initWidget(uiBinder.createAndBindUi(this));
        setPlaceString(UserInterfacePlace.getPlaceAsString(place));
        final Messages messages = MessagesUtil.getInstance();
        setHeaderText(messages.getMessage("configure.user.interface"));

        final UserInterfaceRpcAsync userInterfaceRpcAsync = clientFactory.getUserInterfaceRpcAsync();
        userInterfaceRpcAsync.getForms(new ClientCallback<List<Form>>() {
            @Override
            public void onSuccess(final List<Form> formsList) {
                userInterfaceRpcAsync.getFormFieldsList(new ClientCallback<List<FormFields>>() {
                    @Override
                    public void onSuccess(List<FormFields> fieldsList) {
                        formFieldsData = new ArrayList<ListDataProvider<FormFields>>();
                        String fieldNameHeader = messages.getMessage("configure.user.interface.field.name");
                        String displayHeader = messages.getMessage("configure.user.interface.display");
                        String requiredHeader = messages.getMessage("bulk.upload.template.required") + "?";
                        String validationRegexHeader = messages.getMessage("configure.user.interface.validation.regex");
                        String regexFailedMessageHeader = messages
                                .getMessage("configure.user.interface.regex.failed.message");

                        TextColumn<FormFields> fieldNameColumn = new TextColumn<FormFields>() {
                            @Override
                            public String getValue(FormFields obj) {
                                String name = obj.getName();
                                if (UserInterfaceFormFields.TITLE.equals(obj.getValue())) {
                                    name += " ( " + MessagesUtil.getInstance().getMessage("enumerated.field") + " )";
                                }
                                return name;
                            }
                        };
                        Column<FormFields, Boolean> displayColumn = new Column<FormFields, Boolean>(
                                createCheckboxCell()) {
                            @Override
                            public Boolean getValue(FormFields obj) {
                                return obj.isDisplay();
                            }
                        };
                        displayColumn.setFieldUpdater(new FieldUpdater<FormFields, Boolean>() {
                            @Override
                            public void update(int index, FormFields object, Boolean value) {
                                boolean isValueEqual = value.booleanValue() == object.isDisplay();
                                FormFields field = getUpdateField(object, isValueEqual);
                                if (field != null) {
                                    field.setDisplay(value);
                                    if (isValueEqual) {
                                        handleFieldComparison(field, object);
                                    }
                                }
                            }
                        });
                        Column<FormFields, Boolean> requiredColumn = new Column<FormFields, Boolean>(
                                createCheckboxCell()) {
                            @Override
                            public Boolean getValue(FormFields obj) {
                                return obj.isRequired();
                            }
                        };
                        requiredColumn.setFieldUpdater(new FieldUpdater<FormFields, Boolean>() {
                            @Override
                            public void update(int index, FormFields object, Boolean value) {
                                boolean isValueEqual = value.booleanValue() == object.isRequired();
                                FormFields field = getUpdateField(object, isValueEqual);
                                if (field != null) {
                                    field.setRequired(value);
                                    if (isValueEqual) {
                                        handleFieldComparison(field, object);
                                    }
                                }
                            }
                        });
                        Column<FormFields, String> regexFailedMessageColumn = new Column<FormFields, String>(
                                createTextInputCell()) {
                            @Override
                            public String getValue(FormFields obj) {
                                if (UserInterfaceFormFields.TITLE.equals(obj.getValue())) {
                                    return obj.getEnumeratedValues();
                                }
                                return obj.getRegexFailedMessage();
                            }
                        };
                        regexFailedMessageColumn.setFieldUpdater(new FieldUpdater<FormFields, String>() {
                            @Override
                            public void update(int index, FormFields object, String value) {
                                String oldValue = object.getRegexFailedMessage();
                                boolean isEnumeratedValuesRecord = UserInterfaceFormFields.TITLE
                                        .equals(object.getValue());
                                if (isEnumeratedValuesRecord) {
                                    oldValue = object.getEnumeratedValues();
                                }
                                boolean isValueEqual = value.equals(getNonNullString(oldValue));
                                FormFields field = getUpdateField(object, isValueEqual);
                                if (field != null) {
                                    String newValue = getNullIfEmpty(value);
                                    if (isEnumeratedValuesRecord) {
                                        field.setEnumeratedValues(newValue);
                                    } else {
                                        field.setRegexFailedMessage(newValue);
                                    }
                                    if (isValueEqual) {
                                        handleFieldComparison(field, object);
                                    }
                                }
                            }
                        });

                        for (Form form : formsList) {
                            ListDataProvider<FormFields> fields = new ListDataProvider<FormFields>();
                            List<FormFields> formFieldsList = fields.getList();
                            Long formId = form.getId();
                            for (FormFields field : fieldsList) {
                                if (formId.compareTo(field.getFormId()) == 0) {
                                    formFieldsList.add(field);
                                }
                            }

                            if (!formFieldsList.isEmpty()) {
                                final CellTable<FormFields> table = new CellTable<FormFields>(15,
                                        ResourcesFactoryUtil.getInstance().getCellTableResources());
                                table.setKeyboardSelectionPolicy(KeyboardSelectionPolicy.DISABLED);
                                table.setWidth("100%");
                                String formName = form.getName();
                                HTML header = new HTML(formName);
                                header.setStyleName("pageSectionTitle");
                                tablesPanel.add(header);

                                Column<FormFields, String> validationRegexColumn = new Column<FormFields, String>(
                                        createTextInputCell()) {
                                    @Override
                                    public String getValue(FormFields obj) {
                                        return obj.getValidationRegex();
                                    }
                                };
                                validationRegexColumn.setFieldUpdater(new FieldUpdater<FormFields, String>() {
                                    @Override
                                    public void update(int index, FormFields object, String value) {
                                        Style style = table.getRowElement(index).getChild(3).getChild(0)
                                                .getParentElement().getStyle();
                                        try {
                                            RegExp.compile(value);
                                            style.clearBackgroundColor();
                                        } catch (JavaScriptException e) {
                                            style.setBackgroundColor("lightcoral");
                                        }

                                        boolean isValueEqual = value
                                                .equals(getNonNullString(object.getValidationRegex()));
                                        FormFields field = getUpdateField(object, isValueEqual);

                                        if (field != null) {
                                            field.setValidationRegex(getNullIfEmpty(value));
                                            if (isValueEqual) {
                                                handleFieldComparison(field, object);
                                            }
                                        }
                                    }
                                });

                                table.addColumn(fieldNameColumn, fieldNameHeader);
                                table.addColumn(displayColumn, displayHeader);
                                table.addColumn(requiredColumn, requiredHeader);
                                table.addColumn(validationRegexColumn, validationRegexHeader);
                                table.addColumn(regexFailedMessageColumn, regexFailedMessageHeader);

                                table.setColumnWidth(fieldNameColumn, "30%");
                                table.setColumnWidth(displayColumn, "80px");
                                table.setColumnWidth(requiredColumn, "80px");
                                table.setColumnWidth(validationRegexColumn, "35%");
                                table.setColumnWidth(regexFailedMessageColumn, "35%");

                                fields.addDataDisplay(table);
                                tablesPanel.add(table);
                                formFieldsData.add(fields);
                            }
                        }
                        // 82 = size of the top section
                        heightOffset = pageHeader.getOffsetHeight() + buttonsPanel.getOffsetHeight() + 82;
                        resizePanel();
                    }
                });
            }
        });

        Window.addResizeHandler(new ResizeHandler() {
            @Override
            public void onResize(ResizeEvent event) {
                resizePanel();
            }
        });
    }

    private FormFields getUpdateField(FormFields object, boolean isValueEqual) {
        Long id = object.getId();
        FormFields field = fieldUpdates.get(id);
        if (isValueEqual) {
            return field;
        } else {
            if (field == null) {
                field = new FormFields();
                field.setId(id);

                field.setDisplay(object.isDisplay());
                field.setRequired(object.isRequired());
                field.setValidationRegex(object.getValidationRegex());
                field.setRegexFailedMessage(object.getRegexFailedMessage());

                field.setFormId(object.getFormId());
                field.setValue(object.getValue());
                field.setName(object.getName());
                field.setDescription(object.getDescription());
                field.setEnumeratedValues(object.getEnumeratedValues());

                fieldUpdates.put(id, field);
            }
            return field;
        }
    }

    private void handleFieldComparison(FormFields tempField, FormFields savedField) {
        String tempValidationRegex = getNonNullString(tempField.getValidationRegex());
        String savedValidationRegex = getNonNullString(savedField.getValidationRegex());
        String tempRegexFailedMessage = getNonNullString(tempField.getRegexFailedMessage());
        String savedRegexFailedMessage = getNonNullString(savedField.getRegexFailedMessage());
        if (tempField.isDisplay() == savedField.isDisplay() && tempField.isRequired() == savedField.isRequired()
                && tempValidationRegex.equals(savedValidationRegex)
                && tempRegexFailedMessage.equals(savedRegexFailedMessage)) {
            fieldUpdates.remove(tempField.getId());
        }
    }

    private String getNonNullString(String value) {
        if (value == null) {
            value = "";
        }
        return value;
    }

    private String getNullIfEmpty(String value) {
        if (value.trim().isEmpty()) {
            value = null;
        }
        return value;
    }

    private CheckboxCell createCheckboxCell() {
        return new CheckboxCell() {
            @Override
            public void render(Context context, Boolean value, SafeHtmlBuilder sb) {
                String valueAttribute = value ? " checked" : "";
                String disabledAttribute = "";
                String fieldValue = ((FormFields) context.getKey()).getValue();

                if (isFieldDisabled(fieldValue) && (context.getColumn() == 1 || context.getColumn() == 2)) {
                    disabledAttribute = " disabled";
                }
                sb.appendHtmlConstant("<input type=\"checkbox\" tabindex=\"-1\"" + valueAttribute + disabledAttribute + "/>");
            }
        };
    }

    private TextInputCell createTextInputCell() {
        return new TextInputCell() {
            @Override
            public void render(Context context, String value, SafeHtmlBuilder sb) {
                if (UserInterfaceFormFields.TITLE.equals(((FormFields) context.getKey()).getValue())
                        && context.getColumn() == 3) {
                    sb.appendHtmlConstant("<div style=\"text-align: right;\">"
                            + MessagesUtil.getInstance().getMessage("configure.user.interface.enumerated.values.label")
                            + "</div>");
                } else {
                    String valueAttribute = "";
                    if (value != null) {
                        valueAttribute = " value=\"" + value + "\"";
                    }
                    sb.appendHtmlConstant("<input type=\"text\"" + valueAttribute
                            + " tabindex=\"-1\" class=\"gwt-TextBox\" style=\"width:100%\"/>");
                }
            }
        };
    }

    private void resizePanel() {
        scrollPanel.setHeight(Window.getClientHeight() - heightOffset + "px");
    }

    @Override
    public boolean handles(Place place) {
        return place instanceof UserInterfacePlace;
    }

    @SuppressWarnings("unchecked")
    @UiHandler("btnCancel")
    void handleCancel(ClickEvent e) {
        if (!fieldUpdates.isEmpty()) {
            Dialogs.confirmDiscardChanges(new ConfirmHandler() {
                @Override
                public void confirmed(boolean confirm) {
                    if (confirm) {
                        for (int i = 0; i < formFieldsData.size(); i++) {
                            ListDataProvider<FormFields> fields = formFieldsData.get(i);
                            // before every table there is a pageheader
                            CellTable<FormFields> table = (CellTable<FormFields>) tablesPanel.getWidget(i * 2 + 1);
                            fields.removeDataDisplay(table);
                            fields.addDataDisplay(table);
                        }
                        fieldUpdates.clear();
                    }
                }
            });
        }
    }

    @UiHandler("btnSave")
    void handleSave(ClickEvent e) {
        if (fieldUpdates.isEmpty()) {
            showDialog(true);
        } else {
            boolean valid = true;
            for (FormFields field : fieldUpdates.values()) {
                try {
                    RegExp.compile(field.getValidationRegex());
                } catch (JavaScriptException jse) {
                    valid = false;
                    break;
                }
            }
            if (valid) {
                SessionCheckCallback sessionCheckCallback = new SessionCheckCallback() {
                    @Override
                    public void callback(SessionCheckResolution resolution) {
                        clientFactory.getUserInterfaceRpcAsync().saveFormFields(fieldUpdates, new ClientCallback<Void>() {
                            @Override
                            public void onSuccess(Void result) {
                                for (ListDataProvider<FormFields> fields : formFieldsData) {
                                    for (FormFields field : fields.getList()) {
                                        FormFields update = fieldUpdates.get(field.getId());
                                        if (update != null) {
                                            field.setDisplay(update.isDisplay());
                                            field.setRequired(update.isRequired());
                                            field.setValidationRegex(update.getValidationRegex());
                                            field.setRegexFailedMessage(update.getRegexFailedMessage());
                                            field.setEnumeratedValues(update.getEnumeratedValues());
                                        }
                                    }
                                }
                                fieldUpdates.clear();
                                showDialog(true);

                                clientFactory.getWorkspaceContainer()
                                        .notifyWorkspaces(new WorkspaceNotification(NotificationType.DATA_UPDATED,
                                                MeterMngStatics.USER_INTERFACE_CONFIGURATION_MODIFIED, null));
                            }
                        });
                    }
                };
                clientFactory.handleSessionCheckCallback(sessionCheckCallback);
            } else {
                showDialog(false);
            }
        }
    }

    private void showDialog(boolean valid) {
        Messages messages = MessagesUtil.getInstance();
        MediaResource mediaResource = MediaResourceUtil.getInstance();
        if (valid) {
            Dialogs.displayInformationMessage(messages.getMessage("changes.saved"), mediaResource.getInformationIcon());
        } else {
            Dialogs.displayInformationMessage(messages.getMessage("configure.user.interface.invalid.regex"),
                    mediaResource.getErrorIcon());
        }
    }

    private boolean isFieldDisabled(String fieldValue) {
        return UserInterfaceFormFields.CUST_AGR_REF.equals(fieldValue)
            || UserInterfaceFormFields.UP_NAME.equals(fieldValue)
            || UserInterfaceFormFields.CUST_ACC_NAME.equals(fieldValue);
    }

}
