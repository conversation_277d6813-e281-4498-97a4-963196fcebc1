package za.co.ipay.metermng.client.view.workspace.schedule;

import za.co.ipay.gwt.common.client.Dialogs;
import za.co.ipay.gwt.common.client.form.FormElement;
import za.co.ipay.gwt.common.client.form.SimpleForm;
import za.co.ipay.gwt.common.client.handler.FormDataChangeHandler;
import za.co.ipay.gwt.common.client.handler.FormDataValueChangeHandler;
import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.form.SimpleFormPanel;
import za.co.ipay.metermng.client.resource.image.MediaResource.MediaResourceUtil;
import za.co.ipay.metermng.client.view.workspace.customer.CustomerNotifyDataSuggestOracle;
import za.co.ipay.metermng.client.view.workspace.customer.CustomerNotifySuggestion;
import za.co.ipay.metermng.client.view.workspace.group.user.UserSuggestOracle;
import za.co.ipay.metermng.client.view.workspace.group.user.UserSuggestion;
import za.co.ipay.metermng.client.view.workspace.schedule.taskclass.TaskClassUi;
import za.co.ipay.metermng.shared.MeterMngStatics;
import za.co.ipay.metermng.shared.dto.customer.CustomerNotifyData;
import za.co.ipay.metermng.shared.dto.user.UserData;

import com.google.gwt.core.client.GWT;
import com.google.gwt.event.dom.client.ClickEvent;
import com.google.gwt.event.dom.client.FocusEvent;
import com.google.gwt.event.dom.client.FocusHandler;
import com.google.gwt.event.logical.shared.SelectionEvent;
import com.google.gwt.event.logical.shared.SelectionHandler;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.uibinder.client.UiHandler;
import com.google.gwt.user.client.ui.Button;
import com.google.gwt.user.client.ui.FlowPanel;
import com.google.gwt.user.client.ui.ListBox;
import com.google.gwt.user.client.ui.SuggestBox;
import com.google.gwt.user.client.ui.SuggestOracle;
import com.google.gwt.user.client.ui.SuggestOracle.Suggestion;
import com.google.gwt.user.client.ui.TextBox;
import com.google.gwt.user.client.ui.Widget;

public class ScheduledTaskPanel extends SimpleFormPanel {
    
    @UiField TextBox nameTextBox;
    @UiField ListBox taskClassBox;    
    
    @UiField FormElement nameElement;
    @UiField FormElement taskClassElement;
    
    @UiField FormElement usernameElement;
    @UiField(provided=true) SuggestBox userBox;
    @UiField FormElement selectedUsersElement;
    @UiField ListBox selectedUsersBox;
    @UiField Button addButton;
    
    @UiField FormElement customerElement;
    @UiField(provided=true) SuggestBox customerBox;    
    
    @UiField FlowPanel taskClassPanel;
    
    UserData selectedUser;
    CustomerNotifyData selectedCustomer;

    private static ScheduledTaskPanelUiBinder uiBinder = GWT.create(ScheduledTaskPanelUiBinder.class);

    interface ScheduledTaskPanelUiBinder extends UiBinder<Widget, ScheduledTaskPanel> {
    }

    public ScheduledTaskPanel(SimpleForm form, ClientFactory clientFactory) {
        super(form);
        createUserBox(clientFactory);
        createCustomerBox(clientFactory);
        initWidget(uiBinder.createAndBindUi(this));
        addFieldHandlers();
    }

    private void createUserBox(ClientFactory clientFactory) {
        this.userBox = new SuggestBox(new UserSuggestOracle(clientFactory));
        userBox.addSelectionHandler(new SelectionHandler<SuggestOracle.Suggestion>() {            
            @Override
            public void onSelection(SelectionEvent<Suggestion> event) {
                if (event.getSelectedItem() instanceof UserSuggestion) {
                    selectedUser = ((UserSuggestion) event.getSelectedItem()).getUser();
                    form.setDirtyData(true);
                }                
            }
        });
        userBox.getValueBox().addFocusHandler(new FocusHandler() {            
            @Override
            public void onFocus(FocusEvent event) {
                userBox.showSuggestionList();
            }
        });
    }
    
    private void createCustomerBox(ClientFactory clientFactory) {
        customerBox = new SuggestBox(new CustomerNotifyDataSuggestOracle(clientFactory, MeterMngStatics.NOTIFY_EMAIL_TYPE));
        customerBox.addSelectionHandler(new SelectionHandler<SuggestOracle.Suggestion>() {            
            @Override
            public void onSelection(SelectionEvent<Suggestion> event) {
                if (event.getSelectedItem() instanceof CustomerNotifySuggestion) {
                    selectedCustomer = ((CustomerNotifySuggestion) event.getSelectedItem()).getCustomer();
                    form.setDirtyData(true);
                }                
            }
        });
        customerBox.getValueBox().addFocusHandler(new FocusHandler() {            
            @Override
            public void onFocus(FocusEvent event) {
                customerBox.showSuggestionList();
            }
        });
    }

    public void clearFields() {
        form.setDirtyData(false);
        nameTextBox.setText("");
        taskClassBox.setSelectedIndex(0);
        
        userBox.setText("");
        selectedUser = null;
        selectedUsersBox.clear();
        
        customerBox.setText("");
        selectedCustomer = null;
        
        clearTaskClassPanel();
    }
    
    public void clearErrors() {
        nameElement.setErrorMsg(null);
        taskClassElement.setErrorMsg(null);
        usernameElement.setErrorMsg(null);
        selectedUsersElement.setErrorMsg(null);
        customerElement.setErrorMsg(null);
    }
    
    protected void clearTaskClassPanel() {
        if (taskClassPanel.getWidgetCount() == 1) {
            ((TaskClassUi) taskClassPanel.getWidget(0)).removeFieldHandlers();
            taskClassPanel.remove(0);
        }
    }
    
    @Override
    public void addFieldHandlers() {
        nameTextBox.addChangeHandler(new FormDataChangeHandler(form));
        taskClassBox.addChangeHandler(new FormDataChangeHandler(form));
        userBox.addValueChangeHandler(new FormDataValueChangeHandler<String>(form));
        customerBox.addValueChangeHandler(new FormDataValueChangeHandler<String>(form));
    }
    
    @UiHandler("customerRemoveButton")
    public void removeCustomer(ClickEvent e) {
        this.selectedCustomer = null;
        customerBox.setText("");
    }
    
    @UiHandler("addButton")
    public void onAdd(ClickEvent e) {
        if (selectedUser != null) {
            if (selectedUser.getEmail() == null || selectedUser.getEmail().trim().equals("")) {
                Dialogs.displayErrorMessage(MessagesUtil.getInstance().getMessage("taskschedule.user.noemail"), 
                                            MediaResourceUtil.getInstance().getErrorIcon(),
                                            addButton.getAbsoluteLeft(),
                                            addButton.getAbsoluteTop(),
                                            MessagesUtil.getInstance().getMessage("button.close"));
            } else {
                form.setDirtyData(true);
                String id = selectedUser.getId().toString();
                String name = selectedUser.getUsername() +" ("+selectedUser.getEmail()+")";
                selectedUsersBox.addItem(name, id);
                userBox.setText("");
                selectedUser = null;
            }
        } else {
            usernameElement.setErrorMsg(MessagesUtil.getInstance().getMessage("taskschedule.error.no.user"));
        }
    }
    
    @UiHandler("removeButton")
    public void onRemove(ClickEvent e) {
        form.setDirtyData(true);
        for(int i=selectedUsersBox.getItemCount()-1;i>=0;i--) {
            if (selectedUsersBox.isItemSelected(i)) {
                selectedUsersBox.removeItem(i);
            }
        }
    }
}
