package za.co.ipay.metermng.client.history;

import java.util.logging.Logger;

import za.co.ipay.metermng.client.event.OpenGroupEvent;
import za.co.ipay.metermng.client.factory.ClientFactory;

import com.google.gwt.activity.shared.AbstractActivity;
import com.google.gwt.event.shared.EventBus;
import com.google.gwt.user.client.ui.AcceptsOneWidget;

public class GroupActivity extends AbstractActivity {

    private ClientFactory clientFactory;
    private GroupPlace thePlace;
    private static Logger logger = Logger.getLogger(GroupActivity.class.getName());

    public GroupActivity(GroupPlace groupPlace, ClientFactory clientFactory) {
        super();
        this.clientFactory = clientFactory;
        thePlace = groupPlace;
    }

    @Override
    public void start(AcceptsOneWidget panel, EventBus eventBus) {
        logger.info("Opening place: " + thePlace.getGroupType());
        if (thePlace.getGroupType() != null && !thePlace.getGroupType().isEmpty()) {
            clientFactory.getEventBus().fireEvent(new OpenGroupEvent(thePlace.getGroupType()));
        }
    }

}
