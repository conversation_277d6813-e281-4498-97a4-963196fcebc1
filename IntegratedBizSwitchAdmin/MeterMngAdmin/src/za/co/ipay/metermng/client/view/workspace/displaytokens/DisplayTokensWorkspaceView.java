package za.co.ipay.metermng.client.view.workspace.displaytokens;

import za.co.ipay.gwt.common.client.Dialogs;
import za.co.ipay.gwt.common.client.form.FormElement;
import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.gwt.common.client.workspace.SessionCheckCallback;
import za.co.ipay.ipayxml.ststoken.InitiateMeterTestReqMessage;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.history.DisplayTokensPlace;
import za.co.ipay.metermng.client.resource.image.MediaResource.MediaResourceUtil;
import za.co.ipay.metermng.client.rpc.callback.ClientCallback;
import za.co.ipay.metermng.client.view.workspace.BaseWorkspace;
import za.co.ipay.metermng.shared.TokenData;

import com.google.gwt.core.client.GWT;
import com.google.gwt.event.dom.client.BlurEvent;
import com.google.gwt.event.dom.client.ChangeEvent;
import com.google.gwt.event.dom.client.ChangeHandler;
import com.google.gwt.event.dom.client.ClickEvent;
import com.google.gwt.place.shared.Place;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.uibinder.client.UiHandler;
import com.google.gwt.user.client.ui.Button;
import com.google.gwt.user.client.ui.FlowPanel;
import com.google.gwt.user.client.ui.Label;
import com.google.gwt.user.client.ui.ListBox;
import com.google.gwt.user.client.ui.RadioButton;
import com.google.gwt.user.client.ui.TextBox;
import com.google.gwt.user.client.ui.Widget;

public class DisplayTokensWorkspaceView extends BaseWorkspace {

    private static DisplayTokensWorkspaceViewUiBinder uiBinder = GWT.create(DisplayTokensWorkspaceViewUiBinder.class);

    @UiField Label heading;
    @UiField FormElement tokenIssueListboxElement;
    @UiField ListBox tokenIssueListbox;
    @UiField Button btnGetToken;
    @UiField Label theTokenCode;
    @UiField Label theTokenName;
    @UiField FlowPanel theTokenPanel;
    @UiField TextBox txtbxMeterNumber;
    @UiField RadioButton tdigitMc;
    @UiField RadioButton fdigitMc;

    interface DisplayTokensWorkspaceViewUiBinder extends UiBinder<Widget, DisplayTokensWorkspaceView> {
    }

    public DisplayTokensWorkspaceView(ClientFactory clientFactory, DisplayTokensPlace place) {
        this.clientFactory = clientFactory;
        initWidget(uiBinder.createAndBindUi(this));
        setPlaceString("displayTokens:all");
        setHeaderText(MessagesUtil.getInstance().getMessage("displaytokens.title"));
        heading.setText(MessagesUtil.getInstance().getMessage("displaytokens.title"));
        initUi();
    }

    private void initUi() {
        btnGetToken.addStyleName("gwt-Button gwt-Button-ipay");
        tokenIssueListbox.addItem(MessagesUtil.getInstance().getMessage("display.initiate"));
        tokenIssueListbox.addItem(MessagesUtil.getInstance().getMessage("display.testload"));
        tokenIssueListbox.addItem(MessagesUtil.getInstance().getMessage("display.testdisplay"));
        tokenIssueListbox.addItem(MessagesUtil.getInstance().getMessage("display.totals"));
        tokenIssueListbox.addItem(MessagesUtil.getInstance().getMessage("display.krn"));
        tokenIssueListbox.addItem(MessagesUtil.getInstance().getMessage("display.ti"));
        tokenIssueListbox.addItem(MessagesUtil.getInstance().getMessage("display.testreader"));
        tokenIssueListbox.addItem(MessagesUtil.getInstance().getMessage("display.powerlimit"));
        tokenIssueListbox.addItem(MessagesUtil.getInstance().getMessage("display.tamper"));
        tokenIssueListbox.addItem(MessagesUtil.getInstance().getMessage("display.consumption"));
        tokenIssueListbox.addItem(MessagesUtil.getInstance().getMessage("display.version"));
        tokenIssueListbox.addItem(MessagesUtil.getInstance().getMessage("display.phase"));
        tokenIssueListbox.addChangeHandler(new ChangeHandler() {

            @Override
            public void onChange(ChangeEvent event) {
                theTokenCode.setText("");
                theTokenName.setText("");
                theTokenPanel.setVisible(false);
            }
        });
    }

    @UiHandler("txtbxMeterNumber")
    void handleMeterNumberBlur(BlurEvent blurEvent) {
        String mn = txtbxMeterNumber.getText();
        if(mn != null) {
            if (mn.trim().length()==11) {
                tdigitMc.setValue(true);
            } else if (mn.trim().length()==13) {
                fdigitMc.setValue(true);
            }

        }
    }

    @UiHandler("btnGetToken")
    void handleGetTokenButton(ClickEvent event) {
        SessionCheckCallback sessionCheckCallback = new SessionCheckCallback() {
            @Override
            public void callback(SessionCheckResolution resolution) {
                getToken();
            }
        };
        clientFactory.handleSessionCheckCallback(sessionCheckCallback);
    }

    private void getToken(){
        //Map form fields to data object
        ClientCallback<TokenData> tokenSvcAsyncCallback = new ClientCallback<TokenData>() {
            @Override
            public void onSuccess(TokenData result) {
                if (result==null) {
                    Dialogs.displayErrorMessage(MessagesUtil.getInstance().getMessage("error.token.retrieve"),
                                                MediaResourceUtil.getInstance().getErrorIcon(),
                                                MessagesUtil.getInstance().getMessage("button.close"));
                }  else if (result.getErrorMsg() != null) {
                    Dialogs.displayErrorMessage( (MessagesUtil.getInstance().getMessage("error.token.retrieve") + " (" + result.getResCode()+": "+result.getErrorMsg()+") "),
                            MediaResourceUtil.getInstance().getErrorIcon(),
                            MessagesUtil.getInstance().getMessage("button.close"));
                } else {

                    String code =  result.getEngineeringTokenCodes().get(0);

                    int len = code.length();
                    int numberOfSpaces = (len/4)+1;
                    char[] val = code.toCharArray();
                    char[] buf = new char[len+numberOfSpaces];
                    int j = 0;
                    for(int c=0; c<len; c++) {
                        if ((c%4) == 0) {
                            buf[j++] = ' ';
                        }
                        buf[j++] = val[c];
                    }
                    theTokenName.setText(tokenIssueListbox.getItemText(tokenIssueListbox.getSelectedIndex()) +" - "+ MessagesUtil.getInstance().getMessage("meter.token.code") +": ");
                    theTokenCode.setText(new String(buf, 0, j).trim());
                    theTokenPanel.setVisible(true);
                }
            }
        };

        theTokenPanel.setVisible(false);
        theTokenName.setText("");
        theTokenCode.setText("");

        String selected = tokenIssueListbox.getValue(tokenIssueListbox.getSelectedIndex());
        int displayControl = 0;
        boolean fourDigitManufacturerCode = fdigitMc.getValue();
        if (clientFactory != null) {
            if (selected.equals(MessagesUtil.getInstance().getMessage("display.initiate"))) {
                displayControl = InitiateMeterTestReqMessage.TEST_ALL;
            } else if (selected.equals(MessagesUtil.getInstance().getMessage("display.testload"))) {
                displayControl = InitiateMeterTestReqMessage.TEST_DISCONNECT;
            } else if (selected.equals(MessagesUtil.getInstance().getMessage("display.testdisplay"))) {
                displayControl = InitiateMeterTestReqMessage.TEST_OUTPUT_DEVICES;
            } else if (selected.equals(MessagesUtil.getInstance().getMessage("display.totals"))) {
                displayControl = InitiateMeterTestReqMessage.TEST_REGISTER_TOTALS;
            } else if (selected.equals(MessagesUtil.getInstance().getMessage("display.krn"))) {
                displayControl = InitiateMeterTestReqMessage.TEST_KEY_REVISION_NUMBER;
            } else if (selected.equals(MessagesUtil.getInstance().getMessage("display.ti"))) {
                displayControl = InitiateMeterTestReqMessage.TEST_TARIFF_INDEX;
            } else if (selected.equals(MessagesUtil.getInstance().getMessage("display.testreader"))) {
                displayControl = InitiateMeterTestReqMessage.TEST_INPUT_DEVICE;
            } else if (selected.equals(MessagesUtil.getInstance().getMessage("display.powerlimit"))) {
                displayControl = InitiateMeterTestReqMessage.TEST_MAX_POWER;
            } else if (selected.equals(MessagesUtil.getInstance().getMessage("display.tamper"))) {
                displayControl = InitiateMeterTestReqMessage.TEST_TAMPER_STATUS;
            } else if (selected.equals(MessagesUtil.getInstance().getMessage("display.consumption"))) {
                displayControl = InitiateMeterTestReqMessage.TEST_POWER_CONSUMPTION;
            } else if (selected.equals(MessagesUtil.getInstance().getMessage("display.version"))) {
                displayControl = InitiateMeterTestReqMessage.TEST_METER_VERSION;
            } else if (selected.equals(MessagesUtil.getInstance().getMessage("display.phase"))) {
                displayControl = InitiateMeterTestReqMessage.TEST_PHASE_POWER_UNBALANCE_LIMIT;
            }
            clientFactory.getTokenGeneratorRpc().requestInitiateMeterTestDisplayToken(displayControl, fourDigitManufacturerCode, tokenSvcAsyncCallback);
        }
    }

    @Override
    public void onArrival(Place place) {

    }

    @Override
    public void onLeaving() {
    }

    @Override
    public void onClose() {
    }

    @Override
    public void onSelect() {
    }

    @Override
    public boolean handles(Place place) {
        return place instanceof DisplayTokensPlace;
    }

}

