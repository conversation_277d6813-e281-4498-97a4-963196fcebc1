package za.co.ipay.metermng.client.view.workspace.bulkupload.metercustupbulkupload;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.metermng.mybatis.generated.model.AppSetting;
import za.co.ipay.metermng.mybatis.generated.model.FormFields;
import za.co.ipay.metermng.shared.dto.uploaddata.metercustupuploaddata.MeterCustUPBulkCsvMapToData;
import za.co.ipay.metermng.shared.group.GroupTypeData;

public class ClientMeterCustUPBulkCsvMapToData extends MeterCustUPBulkCsvMapToData {

    public ClientMeterCustUPBulkCsvMapToData(ArrayList<AppSetting> customFieldList,
            String customFieldsStatusUnavailable, List<GroupTypeData> groupTypeHierarchies, boolean enableNonBillable,
            Map<String, FormFields> formFieldsMap) {
        super(customFieldList, customFieldsStatusUnavailable, groupTypeHierarchies, enableNonBillable, formFieldsMap);
    }

    protected String getMessage(String key) {
        return MessagesUtil.getInstance().getMessage(key);
    }
}
