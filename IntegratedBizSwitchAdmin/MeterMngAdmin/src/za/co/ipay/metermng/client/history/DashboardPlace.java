package za.co.ipay.metermng.client.history;

import com.google.gwt.place.shared.Place;
import com.google.gwt.place.shared.PlaceTokenizer;
import com.google.gwt.place.shared.Prefix;

public class DashboardPlace extends Place {

    public static DashboardPlace ALL_DASHBOARD_PLACE = new DashboardPlace();
    public static final String DASHBOARD_PLACE_PREFIX = "dashboard"; 

    public DashboardPlace() {
    }

    @Prefix(value = DASHBOARD_PLACE_PREFIX)
    public static class Tokenizer implements PlaceTokenizer<DashboardPlace> {
        @Override
        public String getToken(DashboardPlace place) {
            return "all";
        }

        @Override
        public DashboardPlace getPlace(String token) {
            return new DashboardPlace();
        }
    }
}
