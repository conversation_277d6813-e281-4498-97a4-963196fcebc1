package za.co.ipay.metermng.client.history;

import za.co.ipay.metermng.client.event.MeterSearchEvent;
import za.co.ipay.metermng.client.factory.ClientFactory;

import com.google.gwt.activity.shared.AbstractActivity;
import com.google.gwt.event.shared.EventBus;
import com.google.gwt.user.client.ui.AcceptsOneWidget;

public class MeterActivity extends AbstractActivity {

    private ClientFactory clientFactory;
    private MeterPlace meterPlace;
    
    public MeterActivity(MeterPlace meterPlace, ClientFactory clientFactory) {
        super();
        this.clientFactory = clientFactory;
        this.meterPlace = meterPlace;
    }
    
    @Override
    public void start(AcceptsOneWidget panel, EventBus eventBus) {
        if (meterPlace.getMeterNumber() != null && !meterPlace.getMeterNumber().isEmpty()) {
            clientFactory.getEventBus().fireEvent(new MeterSearchEvent(meterPlace));
        }
    }

}
