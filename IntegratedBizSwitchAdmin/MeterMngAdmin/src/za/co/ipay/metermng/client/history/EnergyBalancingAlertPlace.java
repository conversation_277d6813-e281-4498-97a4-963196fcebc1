package za.co.ipay.metermng.client.history;

import com.google.gwt.place.shared.Place;
import com.google.gwt.place.shared.PlaceTokenizer;
import com.google.gwt.place.shared.Prefix;

public class EnergyBalancingAlertPlace extends Place {
    
    public static final String ALL = "all";
    
    private String meter;
    
    public EnergyBalancingAlertPlace(String meter) {
        this.meter = meter;
    }
    
    public String getMeter() {
        return meter;
    }

    public static String getPlaceAsString(EnergyBalancingAlertPlace place) {
        return "energybalancing:"+place.getMeter();
    }
    
    @Prefix(value = "energybalancing")
    public static class Tokenizer implements PlaceTokenizer<EnergyBalancingAlertPlace> {
        
        @Override
        public String getToken(EnergyBalancingAlertPlace place) {
            return place.getMeter();
        }

        @Override
        public EnergyBalancingAlertPlace getPlace(String token) {
            return new EnergyBalancingAlertPlace(token);
        }
    }
}
