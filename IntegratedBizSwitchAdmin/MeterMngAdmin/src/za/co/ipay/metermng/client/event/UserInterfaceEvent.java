package za.co.ipay.metermng.client.event;

import com.google.gwt.event.shared.GwtEvent;

public class UserInterfaceEvent extends GwtEvent<UserInterfaceEventHandler> {

    public static Type<UserInterfaceEventHandler> TYPE = new Type<UserInterfaceEventHandler>();

    private String name;

    public UserInterfaceEvent(String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }

    @Override
    public Type<UserInterfaceEventHandler> getAssociatedType() {
        return TYPE;
    }

    @Override
    protected void dispatch(UserInterfaceEventHandler handler) {
        handler.handleEvent(this);
    }
}
