package za.co.ipay.metermng.client.view.workspace;

import za.co.ipay.gwt.common.client.form.FormElement;
import za.co.ipay.gwt.common.client.form.SimpleForm;
import za.co.ipay.gwt.common.client.handler.FormDataChangeHandler;
import za.co.ipay.gwt.common.client.handler.FormDataClickHandler;
import za.co.ipay.metermng.client.form.SimpleFormPanel;
import za.co.ipay.metermng.client.view.component.MridComponent;

import com.google.gwt.core.client.GWT;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.user.client.ui.CheckBox;
import com.google.gwt.user.client.ui.TextBox;
import com.google.gwt.user.client.ui.Widget;

public class AuxTypePanel extends SimpleFormPanel {
    
    @UiField TextBox nameTextBox;
    @UiField TextBox descriptionTextBox;
    @UiField CheckBox activeBox;
    
    @UiField FormElement activeElement;
    @UiField FormElement nameElement;
    @UiField FormElement descriptionElement;
    
    @UiField (provided=true)MridComponent mridComponent;
    
    private static AuxTypePanelUiBinder uiBinder = GWT.create(AuxTypePanelUiBinder.class);

    interface AuxTypePanelUiBinder extends UiBinder<Widget, AuxTypePanel> {
    }

    public AuxTypePanel(SimpleForm form) {
        super(form);
        mridComponent = new MridComponent();
        initWidget(uiBinder.createAndBindUi(this));
        addFieldHandlers();
    }
    
    public void clearFields() {
        form.setDirtyData(false);
        nameTextBox.setText("");
        descriptionTextBox.setText("");
        activeBox.setValue(false);
        mridComponent.setMrid(null);
    }

    public void clearErrors() {
        activeElement.clearErrorMsg();
        nameElement.clearErrorMsg();
        descriptionElement.clearErrorMsg();
        mridComponent.clearErrorMsg();
    }

    @Override
    public void addFieldHandlers() {
        nameTextBox.addChangeHandler(new FormDataChangeHandler(form));
        descriptionTextBox.addChangeHandler(new FormDataChangeHandler(form));
        activeBox.addClickHandler(new FormDataClickHandler(form));
        mridComponent.addFieldHandlers(form);
    }
}