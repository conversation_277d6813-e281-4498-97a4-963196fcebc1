package za.co.ipay.metermng.client.history;

import com.google.gwt.place.shared.Place;
import com.google.gwt.place.shared.PlaceTokenizer;
import com.google.gwt.place.shared.Prefix;

public class SupplyGroupPlace extends Place {

    public static SupplyGroupPlace ALL_SUPPLY_GROUP_PLACE = new SupplyGroupPlace();

    public SupplyGroupPlace() {
    }

    @Prefix(value = "supplyGroup")
    public static class Tokenizer implements PlaceTokenizer<SupplyGroupPlace> {
        @Override
        public String getToken(SupplyGroupPlace place) {
            return "all";
        }

        @Override
        public SupplyGroupPlace getPlace(String token) {
            return new SupplyGroupPlace();
        }
    }
}
