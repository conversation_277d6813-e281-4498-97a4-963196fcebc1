package za.co.ipay.metermng.client.view.workspace.blockingtype;

import java.util.Comparator;
import java.util.List;
import java.util.logging.Logger;

import com.google.gwt.core.client.GWT;
import com.google.gwt.event.dom.client.ClickEvent;
import com.google.gwt.event.dom.client.ClickHandler;
import com.google.gwt.place.shared.Place;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.user.cellview.client.ColumnSortEvent.ListHandler;
import com.google.gwt.user.cellview.client.TextColumn;
import com.google.gwt.user.client.History;
import com.google.gwt.user.client.ui.Widget;
import com.google.gwt.view.client.ListDataProvider;

import za.co.ipay.gwt.common.client.Dialogs;
import za.co.ipay.gwt.common.client.form.FormManager;
import za.co.ipay.gwt.common.client.handler.ConfirmHandler;
import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.gwt.common.client.workspace.SessionCheckCallback;
import za.co.ipay.gwt.common.client.workspace.SimpleTableView;
import za.co.ipay.gwt.common.client.workspace.WorkspaceNotification;
import za.co.ipay.gwt.common.client.workspace.WorkspaceNotification.NotificationType;
import za.co.ipay.gwt.common.shared.exception.AccessControlException;
import za.co.ipay.metermng.client.event.BlockingTypeUpdatedEvent;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.history.BlockingTypePlace;
import za.co.ipay.metermng.client.resource.image.MediaResource.MediaResourceUtil;
import za.co.ipay.metermng.client.rpc.callback.ClientCallback;
import za.co.ipay.metermng.client.view.workspace.BaseWorkspace;
import za.co.ipay.metermng.mybatis.generated.model.BlockingType;
import za.co.ipay.metermng.shared.MeterMngStatics;

public class BlockingTypeWorkspaceView extends BaseWorkspace implements FormManager<BlockingType> {

    ListDataProvider<BlockingType> dataProvider;
    @UiField SimpleTableView<BlockingType> view;
    BlockingTypePanel panel;
    private Place place;

    BlockingType blockingType;

    private Logger logger = Logger.getLogger(BlockingTypeWorkspaceView.class.getName());

    private static BlockingTypeWorkspaceViewUiBinder uiBinder = GWT.create(BlockingTypeWorkspaceViewUiBinder.class);

    interface BlockingTypeWorkspaceViewUiBinder extends UiBinder<Widget, BlockingTypeWorkspaceView> {
    }

    public BlockingTypeWorkspaceView(ClientFactory clientFactory, BlockingTypePlace blockingTypePlace) {
        this.clientFactory = clientFactory;
        initWidget(uiBinder.createAndBindUi(this));
        initForm();
        logger.info("Arrived with place: " + blockingTypePlace.toString());
        setPlaceString(blockingTypePlace.toString());
        setHeaderText(MessagesUtil.getInstance().getMessage("blockingtype.title"));
        populate();
        actionPermissions();
    }

    private void initForm() {
        view.setFormManager(this);
        panel = new BlockingTypePanel(view.getForm());

        view.getForm().setHasDirtyDataManager(this);
        view.getForm().getFormFields().add(panel);

        view.getForm().getSaveBtn().setText(MessagesUtil.getInstance().getMessage("button.create"));
        view.getForm().getSaveBtn().addClickHandler(new ClickHandler() {
            @Override
            public void onClick(ClickEvent arg0) {
                onSaveButtonClick();
            }
        });
        view.getForm().getSaveBtn().ensureDebugId("saveButton");

        view.getForm().getOtherBtn().setText(MessagesUtil.getInstance().getMessage("button.cancel"));
        view.getForm().getOtherBtn().addClickHandler(new ClickHandler() {
            @Override
            public void onClick(ClickEvent event) {
                view.getForm().checkDirtyData(new ConfirmHandler() {
                    @Override
                    public void confirmed(boolean confirm) {
                        if (confirm) {
                            view.getForm().setDirtyData(false);
                            displaySelected(null);
                        }
                    }
                });
            }
        });
        view.getForm().getOtherBtn().ensureDebugId("cancelButton");

        view.getForm().getFormPanel().setHeadingText(MessagesUtil.getInstance().getMessage("blockingtype.title.add"));
    }

    private void onSaveButtonClick() {
        SessionCheckCallback sessionCheckCallback = new SessionCheckCallback() {
            @Override
            public void callback(SessionCheckResolution sessionCheckResolution) {
                if (view.getForm().getSaveBtn().getText().equals(MessagesUtil.getInstance().getMessage("button.create"))) {
                    addBlockingType();
                } else {
                    updateBlockingType();
                }
            }
        };
        clientFactory.handleSessionCheckCallback(sessionCheckCallback);
    }

    private void update() {
        if (blockingType == null) {
            blockingType = new BlockingType();
        }
        blockingType.setTypeName(panel.typeNameTextBox.getText());
        blockingType.setUnitsPerDay(panel.unitsPerDayBox.getValue());
        blockingType.setAmountPerDay(panel.maxAmountPerDayBox.getAmount());
        blockingType.setNumVends(panel.numVendsBox.getValue());
        blockingType.setMaxAmount(panel.maxAmountTextBox.getAmount());
        blockingType.setComplete(panel.completeCheckBox.getValue());
        blockingType.setMessage(panel.messageTextArea.getText());
    }

    private void addBlockingType() {
        update();
        if (isValid()) {
            clientFactory.getBlockingTypeRpc().addBlockingType(blockingType, new ClientCallback<BlockingType>(
                    view.getForm().getSaveBtn().getAbsoluteLeft(), view.getForm().getSaveBtn().getAbsoluteTop()) {
                @Override
                public void onSuccess(BlockingType blockingType) {
                    Dialogs.displayInformationMessage(
                            MessagesUtil.getInstance().getMessage("message.saved",
                                    new String[] { MessagesUtil.getInstance().getMessage("blockingtype.title") }),
                            MediaResourceUtil.getInstance().getInformationIcon(),
                            view.getForm().getSaveBtn().getAbsoluteLeft(), view.getForm().getSaveBtn().getAbsoluteTop(),
                            MessagesUtil.getInstance().getMessage("button.close"));
                    onArrival(BlockingTypePlace.ALL_BLOCKING_TYPES_PLACE);
                    clear();
                    clientFactory.getEventBus().fireEvent(new BlockingTypeUpdatedEvent(blockingType));
                    sendNotification();
                }

                @Override
                public void onFailure(Throwable caught) {
                    if (caught instanceof AccessControlException) {
                        clientFactory.getWorkspaceContainer().closeWorkspace(BlockingTypePlace.ALL_BLOCKING_TYPES_PLACE);
                    }
                    super.onFailure(caught);
                }
            });
        }
    }

    private void updateBlockingType() {
        update();
        if (isValid()) {
            clientFactory.getBlockingTypeRpc().updateBlockingType(blockingType, new ClientCallback<BlockingType>(
                    view.getForm().getSaveBtn().getAbsoluteLeft(), view.getForm().getSaveBtn().getAbsoluteTop()) {
                @Override
                public void onSuccess(BlockingType blockingType) {
                    Dialogs.displayInformationMessage(
                            MessagesUtil.getInstance().getMessage("message.saved",
                                    new String[] { MessagesUtil.getInstance().getMessage("blockingtype.title") }),
                            MediaResourceUtil.getInstance().getInformationIcon(),
                            view.getForm().getSaveBtn().getAbsoluteLeft(), view.getForm().getSaveBtn().getAbsoluteTop(),
                            MessagesUtil.getInstance().getMessage("button.close"));
                    onArrival(BlockingTypePlace.ALL_BLOCKING_TYPES_PLACE);
                    clear();
                    clientFactory.getEventBus().fireEvent(new BlockingTypeUpdatedEvent(blockingType));
                    sendNotification();
                }

                @Override
                public void onFailure(Throwable caught) {
                    if (caught instanceof AccessControlException) {
                        clientFactory.getWorkspaceContainer().closeWorkspace(BlockingTypePlace.ALL_BLOCKING_TYPES_PLACE);
                    }
                    super.onFailure(caught);
                }
            });
        }
    }

    private boolean isValid() {
        boolean valid = true;
        panel.clearErrors();
        if (!panel.isValid()) {
            valid = false;
        }
        return valid;
    }

    public void populate() {
        logger.info("populate");
        createBlockingTypeTable();
    }

    private void createBlockingTypeTable() {
        if (dataProvider != null) {
            return;
        }

        TextColumn<BlockingType> nameColumn = new TextColumn<BlockingType>() {
            @Override
            public String getValue(BlockingType blockingType) {
                return blockingType.getTypeName();
            }
        };

        nameColumn.setSortable(true);

        TextColumn<BlockingType> completeColumn = new TextColumn<BlockingType>() {
            @Override
            public String getValue(BlockingType blockingType) {
                if (blockingType.isComplete()) {
                    return MessagesUtil.getInstance().getMessage("button.yes");
                } else {
                    return MessagesUtil.getInstance().getMessage("button.no");
                }
            }
        };

        view.getTable().addColumn(nameColumn, MessagesUtil.getInstance().getMessage("blockingtype.form.typename"));
        view.getTable().addColumn(completeColumn, MessagesUtil.getInstance().getMessage("blockingtype.form.complete"));

        view.getTable().ensureDebugId("blockingTypeTable");

        dataProvider = new ListDataProvider<>();
        dataProvider.addDataDisplay(view.getTable());
        view.getPager().setDisplay(view.getTable());
        List<BlockingType> list = dataProvider.getList();

        ListHandler<BlockingType> columnSortHandler = new ListHandler<>(list);
        columnSortHandler.setComparator(nameColumn, new Comparator<BlockingType>() {
            public int compare(BlockingType o1, BlockingType o2) {
                return o1.getTypeName().compareTo(o2.getTypeName());
            }
        });
        view.getTable().addColumnSortHandler(columnSortHandler);
        view.getTable().getColumnSortList().push(nameColumn);
    }

    public void setBlockingType(BlockingType bt) {
        this.blockingType = bt;
        if (blockingType == null) {
            blockingType = new BlockingType();
            view.getForm().getFormPanel().setHeadingText(MessagesUtil.getInstance().getMessage("blockingtype.title.add"));
            view.getForm().getSaveBtn().setText(MessagesUtil.getInstance().getMessage("button.create"));
        } else {
            view.getForm().getFormPanel().setHeadingText(MessagesUtil.getInstance().getMessage("blockingtype.title.update"));
            view.getForm().getSaveBtn().setText(MessagesUtil.getInstance().getMessage("button.update"));
        }
        panel.typeNameTextBox.setText(blockingType.getTypeName());
        panel.unitsPerDayBox.setValue(blockingType.getUnitsPerDay());
        panel.maxAmountPerDayBox.setAmount(blockingType.getAmountPerDay());
        panel.numVendsBox.setValue(blockingType.getNumVends());
        panel.maxAmountTextBox.setAmount(blockingType.getMaxAmount());
        panel.completeCheckBox.setValue(blockingType.isComplete());
        panel.messageTextArea.setText(blockingType.getMessage());
    }

    public void clear() {
        logger.info("clearing blocking type");
        panel.clearFields();
        panel.clearErrors();
        setBlockingType(null);
        panel.handleCompleteCheckbox();
        view.clearTableSelection();
    }

    @Override
    public void onLeaving() {
    }

    @Override
    public void onArrival(final Place place) {
        logger.info("Arrived at blocking type: " + place.toString());
        this.place = place;
        loadBlockingTypes();
    }

    private void loadBlockingTypes() {
        clientFactory.getBlockingTypeRpc().getAllBlockingTypes(new ClientCallback<List<BlockingType>>() {
            @Override
            public void onSuccess(List<BlockingType> result) {
                List<BlockingType> list = dataProvider.getList();
                list.clear();
                list.addAll(result);
                setFromPlace(place);
            }

            @Override
            public void onFailure(Throwable caught) {
                if (caught instanceof AccessControlException) {
                    clientFactory.getWorkspaceContainer().closeWorkspace(BlockingTypePlace.ALL_BLOCKING_TYPES_PLACE);
                }
                super.onFailure(caught);
            }
        });
    }

    private void setFromPlace(Place place) {
        if (place instanceof BlockingTypePlace) {
            BlockingTypePlace p = (BlockingTypePlace) place;
            if (p.getBlockingTypeId() == null) {
                logger.info("No place id");
            } else if (BlockingTypePlace.ALL_DATA.equals(p.getBlockingTypeId())) {
                logger.info("Place: All data");
            } else {
                logger.info("Place: id:" + p.getBlockingTypeId());
                for (BlockingType bType : dataProvider.getList()) {
                    if (bType.getId().toString().equals(p.getBlockingTypeId())) {
                        logger.info("Found matching BlockingType: " + bType);
                        setBlockingType(bType);
                        view.setSelectedTableData(bType);
                    }
                }
                // Reset the current place
                History.newItem(BlockingTypePlace.ALL_BLOCKING_TYPES_PLACE.toString(), false);
            }
        }
    }

    private void sendNotification() {
        // Notify any affected tabs
        clientFactory.getWorkspaceContainer().notifyWorkspaces(
                new WorkspaceNotification(NotificationType.DATA_UPDATED, MeterMngStatics.BLOCKINGTYPES_MODIFIED));
    }

    @Override
    public void onClose() {
    }

    @Override
    public void onSelect() {

    }

    @Override
    public boolean handles(Place place) {
        return place instanceof BlockingTypePlace;
    }

    @Override
    public void displaySelected(BlockingType selected) {
        clear();
        setBlockingType(selected);
        panel.handleCompleteCheckbox();
    }

    private void actionPermissions() {
        if (!clientFactory.getUser().hasPermission(MeterMngStatics.EDIT_PERMISSION_MM_BLOCKING_TYPE)) {
            view.getForm().getButtons().removeFromParent();
        }
    }
}
