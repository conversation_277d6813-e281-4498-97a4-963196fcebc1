package za.co.ipay.metermng.client.history;

import java.util.logging.Logger;

import com.google.gwt.place.shared.PlaceTokenizer;
import com.google.gwt.place.shared.Prefix;

public class AuxChargeSchedulePlace extends BasePlace {
       
    public static AuxChargeSchedulePlace ALL_AUX_CHARGE_STRUCTURES_PLACE = new AuxChargeSchedulePlace(); 

    private String auxChargeStructureId;
    
    private static Logger logger = Logger.getLogger(AuxChargeSchedulePlace.class.getName());
    
    public AuxChargeSchedulePlace() {
        this.auxChargeStructureId = ALL_DATA;
    }
    
    public AuxChargeSchedulePlace(String auxChargeStructureId) {
        this.auxChargeStructureId = auxChargeStructureId;
    }
    
    public String getAuxChargeScheduleId() {
        return auxChargeStructureId;
    }
    
    @Override
    public String toString() {
        return "auxChargeStructure" + TOKEN_SEPERATOR + auxChargeStructureId;
    }

    @Prefix(value="auxChargeStructure")
    public static class Tokenizer implements PlaceTokenizer<AuxChargeSchedulePlace> {
        @Override
        public String getToken(AuxChargeSchedulePlace place) {
            return place.getAuxChargeScheduleId();
        }

        @Override
        public AuxChargeSchedulePlace getPlace(String token) {
            String[] tokens = (token == null) ? new String[0] : token.split(TOKEN_SEPERATOR);
            for(String part : tokens) {
                logger.info("Token part: "+part);
                return new AuxChargeSchedulePlace(part);
            }
            return new AuxChargeSchedulePlace();
        }
    }
}
