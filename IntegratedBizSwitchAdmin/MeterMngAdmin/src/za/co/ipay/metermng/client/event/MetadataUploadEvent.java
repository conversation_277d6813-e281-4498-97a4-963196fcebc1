package za.co.ipay.metermng.client.event;

import com.google.gwt.event.shared.GwtEvent;

public class MetadataUploadEvent extends GwtEvent<MetadataUploadEventHandler> {

    public static Type<MetadataUploadEventHandler> TYPE = new Type<>();

    private String name;

    public MetadataUploadEvent(String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }

    @Override
    public Type<MetadataUploadEventHandler> getAssociatedType() {
        return TYPE;
    }

    @Override
    protected void dispatch(MetadataUploadEventHandler handler) {
        handler.handleEvent(this);
    }

}