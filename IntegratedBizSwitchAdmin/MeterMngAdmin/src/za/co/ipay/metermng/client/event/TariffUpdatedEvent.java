package za.co.ipay.metermng.client.event;

import com.google.gwt.event.shared.GwtEvent;

public class TariffUpdatedEvent extends GwtEvent<TariffUpdatedEventHandler> {

    public static Type<TariffUpdatedEventHandler> TYPE = new Type<TariffUpdatedEventHandler>();
    
    public TariffUpdatedEvent() {
        
    }
    
	@Override
    public Type<TariffUpdatedEventHandler> getAssociatedType() {
        return TYPE;
    }

    @Override
    protected void dispatch(TariffUpdatedEventHandler handler) {
        handler.processTariffUpdatedEvent(this);
    }


}
