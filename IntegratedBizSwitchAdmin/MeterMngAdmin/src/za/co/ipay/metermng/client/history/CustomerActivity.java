package za.co.ipay.metermng.client.history;

import za.co.ipay.metermng.client.event.CustomerSearchEvent;
import za.co.ipay.metermng.client.factory.ClientFactory;

import com.google.gwt.activity.shared.AbstractActivity;
import com.google.gwt.event.shared.EventBus;
import com.google.gwt.user.client.ui.AcceptsOneWidget;

public class CustomerActivity extends AbstractActivity {

    private ClientFactory clientFactory;
    private CustomerPlace customerPlace;

    public CustomerActivity(CustomerPlace customerPlace, ClientFactory clientFactory) {
        super();
        this.clientFactory = clientFactory;
        this.customerPlace = customerPlace;
    }
    
    @Override
    public void start(AcceptsOneWidget panel, EventBus eventBus) {
        clientFactory.getEventBus().fireEvent(new CustomerSearchEvent(customerPlace));
    }

}
