package za.co.ipay.metermng.client.event;

import com.google.gwt.event.shared.GwtEvent;

public class PeriodsUpdatedEvent extends GwtEvent<PeriodsUpdatedEventHandler> {

    public static Type<PeriodsUpdatedEventHandler> TYPE = new Type<PeriodsUpdatedEventHandler>();
    
    public PeriodsUpdatedEvent() {
        
    }
    
	@Override
    public Type<PeriodsUpdatedEventHandler> getAssociatedType() {
        return TYPE;
    }

    @Override
    protected void dispatch(PeriodsUpdatedEventHandler handler) {
        handler.processPeriodsUpdatedEvent(this);
    }


}
