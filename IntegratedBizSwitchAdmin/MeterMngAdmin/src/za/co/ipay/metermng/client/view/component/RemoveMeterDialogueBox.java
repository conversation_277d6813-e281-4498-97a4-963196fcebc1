package za.co.ipay.metermng.client.view.component;

import java.util.ArrayList;
import java.util.Date;

import com.google.gwt.core.client.GWT;
import com.google.gwt.event.dom.client.ClickEvent;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiFactory;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.uibinder.client.UiHandler;
import com.google.gwt.user.client.ui.Button;
import com.google.gwt.user.client.ui.DialogBox;
import com.google.gwt.user.client.ui.FlowPanel;
import com.google.gwt.user.client.ui.HTML;
import com.google.gwt.user.client.ui.Label;
import com.google.gwt.user.client.ui.TextBox;
import com.google.gwt.user.client.ui.Widget;

import za.co.ipay.gwt.common.client.Dialogs;
import za.co.ipay.gwt.common.client.form.FormElement;
import za.co.ipay.gwt.common.client.form.HasDirtyData;
import za.co.ipay.gwt.common.client.form.LocalOnlyHasDirtyData;
import za.co.ipay.gwt.common.client.handler.ConfirmHandler;
import za.co.ipay.gwt.common.client.handler.FormDataChangeHandler;
import za.co.ipay.gwt.common.client.resource.Messages;
import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.gwt.common.client.validation.ClientValidatorUtil;
import za.co.ipay.gwt.common.client.widgets.IpayListBox;
import za.co.ipay.gwt.common.client.workspace.SessionCheckCallback;
import za.co.ipay.gwt.common.shared.LookupListItem;
import za.co.ipay.metermng.client.event.EndDeviceStoreUpdatedEvent;
import za.co.ipay.metermng.client.event.EndDeviceStoreUpdatedEventHandler;
import za.co.ipay.metermng.client.event.UsagePointUpdatedEvent;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.i18n.UiMessages;
import za.co.ipay.metermng.client.i18n.UiMessagesUtil;
import za.co.ipay.metermng.client.resource.image.MediaResource.MediaResourceUtil;
import za.co.ipay.metermng.client.rpc.callback.ClientCallback;
import za.co.ipay.metermng.client.view.component.meter.ContainsMeterComponent;
import za.co.ipay.metermng.client.view.workspace.UsagePointWorkspaceView;
import za.co.ipay.metermng.datatypes.RecordStatus;
import za.co.ipay.metermng.mybatis.generated.model.AppSetting;
import za.co.ipay.metermng.mybatis.generated.model.UpMeterInstall;
import za.co.ipay.metermng.shared.EndDeviceStoreData;
import za.co.ipay.metermng.shared.SpecialActionsData;
import za.co.ipay.metermng.shared.appsettings.AppSettings;
import za.co.ipay.metermng.shared.dto.UsagePointData;

public class RemoveMeterDialogueBox extends DialogBox {

    @UiField HTML clearMeterBalanceMsg;
    @UiField Button btnRemoveMeter;
    @UiField Button btnCancel;
    @UiField Label lblSelectMeterStore;
    @UiField IpayListBox lstbxStores;
    @UiField FlowPanel deviceMoveRefPanel;
    @UiField FormElement deviceMoveRefElement;
    @UiField TextBox deviceMoveRefTxtbx;
    @UiField(provided=true) SpecialActionsReasonComponent specialactionreasons;

    private ClientFactory clientFactory;
    private UsagePointData usagePointData;
    protected ContainsMeterComponent parentWorkspace;
    private Messages messages = Messages.MessagesUtil.getInstance();

    private HasDirtyData hasDirtyData = new LocalOnlyHasDirtyData();

    private static RemoveMeterDialogueBoxUiBinder uiBinder = GWT.create(RemoveMeterDialogueBoxUiBinder.class);
    interface RemoveMeterDialogueBoxUiBinder extends UiBinder<Widget, RemoveMeterDialogueBox> {
    }

    public RemoveMeterDialogueBox(ClientFactory clientFactory, ContainsMeterComponent parentWorkspace) {
        this.clientFactory = clientFactory;
        this.parentWorkspace = parentWorkspace;
        specialactionreasons = new SpecialActionsReasonComponent(clientFactory, null, SpecialActionsData.REMOVE_METER);
        setWidget(uiBinder.createAndBindUi(this));
        init();
        addHandlers();
        hasDirtyData.setDirtyData(false);
    }

    protected void init() {
        this.ensureDebugId("removeMeterDialogBox");
        populateDeviceMoveRef();
        populateStoresListBox();
    }

    protected void addHandlers() {
        clientFactory.getEventBus().addHandler(EndDeviceStoreUpdatedEvent.TYPE, new EndDeviceStoreUpdatedEventHandler() {
            @Override
            public void processEndDeviceStoreUpdatedEvent(EndDeviceStoreUpdatedEvent event) {
                SessionCheckCallback sessionCheckCallback = new SessionCheckCallback() {
                    @Override
                    public void callback(SessionCheckResolution sessionCheckResolution) {
                        populateStoresListBox();
                    }
                };
                clientFactory.handleSessionCheckCallback(sessionCheckCallback);
            }
        });

        addFieldHandler();
    }

    private void addFieldHandler() {
        deviceMoveRefTxtbx.addChangeHandler(new FormDataChangeHandler(hasDirtyData));
        lstbxStores.addChangeHandler(new FormDataChangeHandler(hasDirtyData));
        specialactionreasons.lstbxReasons.addChangeHandler(new FormDataChangeHandler(hasDirtyData));
        specialactionreasons.txtbxReasons.addChangeHandler(new FormDataChangeHandler(hasDirtyData));
    }

    public void setUsagePointData(UsagePointData usagePointData) {
        this.usagePointData = usagePointData;
    }

    public void displayClearMeterBalanceMsg() {
        if (usagePointData != null
                && usagePointData.getRecordStatus() == RecordStatus.ACT
                && usagePointData.getMeterData() != null
                && usagePointData.getMeterData().getMeterModelData() != null
                && usagePointData.getMeterData().getMeterModelData().getMdcId() != null
                && usagePointData.getMeterData().getMeterModelData().isDisplayMessage()) {
            clearMeterBalanceMsg.setText(MessagesUtil.getInstance().getMessage("remove.meter.pandisplay"));
            clearMeterBalanceMsg.setVisible(true);

        }
    }

    @UiFactory
    public UiMessages getUiMessages() {
        return UiMessagesUtil.getInstance();
    }

    @UiHandler("btnRemoveMeter")
    void handleRemoveMeter(ClickEvent event) {
    	if (isValid()) {
            SessionCheckCallback sessionCheckCallback = new SessionCheckCallback() {
                @Override
                public void callback(SessionCheckResolution sessionCheckResolution) {
                    handleRemoveMeter();
                }
            };
    		clientFactory.handleSessionCheckCallback(sessionCheckCallback);
    	}
    }

    private boolean isValid() {
        boolean valid = true;
        clearErrorMessages();
        if (deviceMoveRefElement.isVisible()) {
            if (deviceMoveRefElement.isRequired()
                    && (deviceMoveRefTxtbx.getText() == null || deviceMoveRefTxtbx.getText().trim().isEmpty())) {
                deviceMoveRefElement.setErrorMsg(MessagesUtil.getInstance().getMessage("error.field.is.required",
                        new String[] { deviceMoveRefElement.getLabelText() }));
                valid = false;
            }

            if (deviceMoveRefTxtbx.getText() != null && !deviceMoveRefTxtbx.getText().trim().isEmpty()) {
                UpMeterInstall upMeterInstall = new UpMeterInstall();
                upMeterInstall.setRemoveRef(deviceMoveRefTxtbx.getText());
                if (!ClientValidatorUtil.getInstance().validateField(upMeterInstall, "removeRef",
                        deviceMoveRefElement)) {
                    valid = false;
                }
            }
        }

        if (specialactionreasons.isAttached() && specialactionreasons.isVisible() && !specialactionreasons.validate()) {
            valid = false;
        }

        return valid;
    }

    @UiHandler("btnCancel")
    void handleCancelButton(ClickEvent event) {
        if (hasDirtyData.isDirtyData()) {
            Dialogs.confirmDiscardChanges(new ConfirmHandler() {
                @Override
                public void confirmed(boolean confirm) {
                    if (confirm) {
                        hasDirtyData.setDirtyData(false);
                        hide();
                    }
                }
            });
        } else {
            hide();

        }
    }

    private void handleRemoveMeter() {
        final Long deviceStoreId = Long.valueOf(lstbxStores.getValue(lstbxStores.getSelectedIndex()));
        clientFactory.getDeviceStoreRpc().getDeviceStore(deviceStoreId, new ClientCallback<EndDeviceStoreData>() {
            @Override
            public void onSuccess(EndDeviceStoreData result) {
                if(result!=null && result.isStoresOtherVendorsMeter()) {
                    String dialogMessage = messages.getMessage("devicestore.meters.move.dialog", new String[]{result.getName()});
                    Dialogs.confirm(dialogMessage, messages.getMessage("button.yes"), messages.getMessage("button.no"),
                            MediaResourceUtil.getInstance().getQuestionIcon(), new ConfirmHandler() {
                                @Override
                                public void confirmed(boolean confirm) {
                                    if (confirm) {
                                        moveMeterToDeviceStore(deviceStoreId);
                                    }
                                }
                            });
                }else {
                    moveMeterToDeviceStore(deviceStoreId);
                }
            }
        });
    }

    private void moveMeterToDeviceStore(Long deviceStoreId){

        boolean cont = true;

        usagePointData.getMeterData().setEndDeviceStoreId(deviceStoreId);
        usagePointData.setRecordStatus(RecordStatus.DAC);
        usagePointData.setInstallationDate(null);

        if (usagePointData.getUpMeterInstall() != null) {
            usagePointData.getUpMeterInstall().setRemoveDate(new Date());
            if (deviceMoveRefTxtbx.getText() != null && !deviceMoveRefTxtbx.getText().isEmpty()) {
            	usagePointData.getUpMeterInstall().setRemoveRef(deviceMoveRefTxtbx.getText());
            } else {
            	usagePointData.getUpMeterInstall().setRemoveRef(null);
            }
        }
        if (specialactionreasons.isAttached() && specialactionreasons.isVisible()) {
            cont = specialactionreasons.validate();
        }

        if (cont) {
            usagePointData.setReplaceMeterReasonsLog(specialactionreasons.getLogEntry());
            if (parentWorkspace instanceof UsagePointWorkspaceView) {
                int eventfunction = UsagePointUpdatedEvent.REMOVE_METER;
                UsagePointUpdatedEvent updateevent = new UsagePointUpdatedEvent(((UsagePointWorkspaceView) parentWorkspace), usagePointData, eventfunction);
                clientFactory.getEventBus().fireEvent(updateevent);
            }
            this.hide();
        }
    }

    public void populateDeviceMoveRef() {
        clientFactory.getAppSettingRpc().getAppSettingByKey(AppSettings.USAGEPOINT_DEVICE_MOVEMENT_REFERENCE,
                new ClientCallback<AppSetting>() {
                    @Override
                    public void onSuccess(AppSetting result) {
                        setDeviceMoveRefValue(result.getValue());
                    }
                });
    }

    private void populateStoresListBox() {
        ClientCallback<ArrayList<LookupListItem>> lookupSvcAsyncCallback = new ClientCallback<ArrayList<LookupListItem>>() {
            @Override
            public void onSuccess(ArrayList<LookupListItem> result) {
                lstbxStores.setLookupItems(result);
                if (usagePointData != null && usagePointData.getMeterData() != null && usagePointData.getMeterData().getEndDeviceStoreId() != null) {
                    lstbxStores.selectItemByValue(usagePointData.getMeterData().getEndDeviceStoreId().toString());
                }
            }

        };
        if (clientFactory != null) {
            lstbxStores.clear();
            clientFactory.getLookupRpc().getEndDeviceStoresLookupList(lookupSvcAsyncCallback);
        }
    }

    public void setDeviceMoveRefValue(String deviceMoveRefValue) {
        if (deviceMoveRefValue != null) {
            Messages messages = MessagesUtil.getInstance();
            if (deviceMoveRefValue.equals(messages.getMessage("user.custom.field.status.required"))) {
                deviceMoveRefPanel.setVisible(true);
                deviceMoveRefElement.setRequired(true);
            } else if (deviceMoveRefValue.equals(messages.getMessage("user.custom.field.status.optional"))) {
                deviceMoveRefPanel.setVisible(true);
                deviceMoveRefElement.setRequired(false);
            } else if (deviceMoveRefValue.equals(messages.getMessage("user.custom.field.status.unavailable"))) {
                deviceMoveRefPanel.setVisible(false);
                deviceMoveRefElement.setRequired(false);
            }
        }
    }

	public void clearErrorMessages() {
		specialactionreasons.clearErrorMessages();
		deviceMoveRefElement.clearErrorMsg();
	}

    public void clear() {
        clearMeterBalanceMsg.setVisible(false);
        lstbxStores.setSelectedIndex(0);
        specialactionreasons.clearErrorMessages();
        specialactionreasons.clearFields();
    }
}
