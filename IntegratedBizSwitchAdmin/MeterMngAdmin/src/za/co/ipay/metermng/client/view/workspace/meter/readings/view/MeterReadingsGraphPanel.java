package za.co.ipay.metermng.client.view.workspace.meter.readings.view;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;
import java.util.logging.Logger;

import org.moxieapps.gwt.highcharts.client.Axis;
import org.moxieapps.gwt.highcharts.client.Chart;
import org.moxieapps.gwt.highcharts.client.ChartSubtitle;
import org.moxieapps.gwt.highcharts.client.ChartTitle;
import org.moxieapps.gwt.highcharts.client.Color;
import org.moxieapps.gwt.highcharts.client.Legend;
import org.moxieapps.gwt.highcharts.client.Series;
import org.moxieapps.gwt.highcharts.client.ToolTip;
import org.moxieapps.gwt.highcharts.client.plotOptions.AreaPlotOptions;
import org.moxieapps.gwt.highcharts.client.plotOptions.Marker;

import za.co.ipay.gwt.common.client.Dialogs;
import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.metermng.client.resource.image.MediaResource.MediaResourceUtil;
import za.co.ipay.metermng.client.view.component.BaseComponent;
import za.co.ipay.metermng.mybatis.generated.model.MeterReadingType;
import za.co.ipay.metermng.shared.MeterMngStatics;
import za.co.ipay.metermng.shared.dto.meter.MeterReadingDto;
import za.co.ipay.metermng.shared.dto.meter.MeterReadingsDto;
import za.co.ipay.metermng.shared.utils.MeterMngCommonUtil;

import com.google.gwt.core.client.GWT;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.user.client.Window;
import com.google.gwt.user.client.ui.FlowPanel;
import com.google.gwt.user.client.ui.Widget;

public class MeterReadingsGraphPanel extends BaseComponent {
    
    @UiField FlowPanel chartPanel;
    private Chart chart;    
    private String singleMeterGraphType;
    private String chartSubtitle;
    
    private static Logger logger = Logger.getLogger(MeterReadingsGraphPanel.class.getName());

    private static MeterReadingsGraphPanelUiBinder uiBinder = GWT.create(MeterReadingsGraphPanelUiBinder.class);

    interface MeterReadingsGraphPanelUiBinder extends UiBinder<Widget, MeterReadingsGraphPanel> {
    }

    public MeterReadingsGraphPanel(String singleMeterGraphType) {
        this.singleMeterGraphType = singleMeterGraphType;
        initWidget(uiBinder.createAndBindUi(this));        
    }
    
    public void clear() {  
        if (chart != null) {
            chartPanel.remove(chart);
            chart = null;
        }
    }
    
    public void displayChartData(Date startDate, String graphType, String singleMeterNumber, MeterReadingsDto readings) {
        clear();
        if (readings.getReadings().size() == 0) {
            Dialogs.displayInformationMessage(MessagesUtil.getInstance().getMessage("meterreadings.noreadings"), MediaResourceUtil.getInstance().getInformationIcon());           
        } else {            
            createChart(startDate, graphType, singleMeterNumber, readings);
        }
    }
    
    private void createChart(Date startDate, String graphType, String singleMeterNumber, MeterReadingsDto readings) {
        MeterReadingType meterReadingType = readings.getMeterReadingType();
        String unitOfMeasure = MeterMngCommonUtil.getCorrectedUnitOfMeasure(meterReadingType.getUnitOfMeasure());
        if (singleMeterGraphType.equals(graphType)) {
            chartSubtitle = unitOfMeasure;
            chart = new Chart().setZoomType(Chart.ZoomType.X)
                    .setSpacingRight(20)
                    .setChartTitle(new ChartTitle()
                            .setText(MessagesUtil.getInstance().getMessage("meterreadings.chart.title.single") + ": "
                                    + meterReadingType.getName()))
                    .setToolTip(new ToolTip().setShared(true))
                    .setLegend(new Legend().setEnabled(false));
            
            Color fillColor = null;
            
            chart.setAreaPlotOptions(
                    new AreaPlotOptions()
                        .setFillColor(fillColor)  
                        .setLineWidth(1)  
                        .setMarker(new Marker().setEnabled(false).setHoverState(new Marker().setEnabled(true).setRadius(5)))  
                        .setShadow(false)  
                        .setHoverStateLineWidth(1)  
            );  
    
            chart.getXAxis()
                 .setType(Axis.Type.DATE_TIME)
                 .setMaxZoom(MeterMngStatics.ONE_HOUR * 4)
                 .setAxisTitleText(MessagesUtil.getInstance().getMessage("meterreadings.chart.xtitle"))
                 .setStartOnTick(true)
                 .setMin(startDate.getTime());  
    
            chart.getYAxis()
                 .setAxisTitleText(unitOfMeasure)
                 .setMin(0)
                 .setMax((readings.getMaxReading() / 1000) + 2)
                 .setStartOnTick(true)
                 .setShowFirstLabel(true);
            
            //Display the readings
            Iterator<String> numbers = readings.getReadings().keySet().iterator();
            String name = "";
            String chartName = "";
            while(numbers.hasNext()) {
                name = numbers.next();
                chartName = name;
                if (MeterReadingsDto.SINGLE_METER.equals(name)) {
                    chartName = singleMeterNumber;
                }
                createChartSeries(true, chartName, null, readings.getReadings().get(name), graphType);
            }
            
        } else {
            // Energy Balancing chart
            chart = new Chart()
                    .setType(Series.Type.LINE)
                    .setZoomType(Chart.ZoomType.X)
                    .setChartTitle(new ChartTitle()
                            .setText(MessagesUtil.getInstance().getMessage("meterreadings.chart.title.balancing") + ": "
                                    + meterReadingType.getName()))
                    .setChartSubtitle(new ChartSubtitle().setText(unitOfMeasure))
                    .setToolTip(new ToolTip().setShared(true))
                    .setLegend(new Legend()
                            .setLayout(Legend.Layout.VERTICAL)
                            .setAlign(Legend.Align.RIGHT)
                            .setVerticalAlign(Legend.VerticalAlign.TOP)
                            .setX(-10)
                            .setY(100)
                            .setBorderWidth(1)
                            );
  
            chart.getXAxis()
            .setType(Axis.Type.DATE_TIME)
            .setMaxZoom(MeterMngStatics.ONE_HOUR * 4)
            .setAxisTitleText(MessagesUtil.getInstance().getMessage("meterreadings.chart.xtitle"))
            .setStartOnTick(true)
            .setMin(startDate.getTime());  
  
            chart.getYAxis()
            .setAxisTitleText(unitOfMeasure)
            .setMin(0)
            .setMax(readings.getMaxReading() / 1000 + 2)
            .setStartOnTick(true)
            .setShowFirstLabel(true);
      
            // Display the readings
            Iterator<String> numbers = readings.getReadings().keySet().iterator();
            String name = "";
            String chartName = "";
            String colour = null;
            String darkColor = "#324a76";
            String lightColor = "#6495ED";
            while (numbers.hasNext()) {                
                name = numbers.next();
                chartName = name;
                colour = null;
                if (MeterReadingsDto.SUPER_METER.equals(name)) {
                    chartName = MessagesUtil.getInstance().getMessage("meterreadings.meter.field.super") + " " + readings.getBalancingMeter();
                    colour = lightColor;
                } else if (MeterReadingsDto.METER_TOTALS.equals(name)) {
                    chartName = MessagesUtil.getInstance().getMessage("meterreadings.meter.totals");
                    colour = darkColor;
                }
                createChartSeries(false, chartName, colour, readings.getReadings().get(name), graphType);
            }
        
        } //end 
        
        //Add the chart to the UI
        chartPanel.add(chart.setWidth(Window.getClientWidth() * 0.8));
    }
    
    protected void resizeChart() {
        if (chart != null) {
            chart.setSizeToMatchContainer();
            logger.info("resized chart: "+chart.getOffsetHeight()+" "+chart.getOffsetWidth());
        }
    }
    
    private void createChartSeries(boolean areaChart, String name, String colour, ArrayList<MeterReadingDto> readings, String graphType) {
        if (readings.size() > 0) {
            logger.info("Creating series: "+name+" "+colour);
            
            long pointInterval = MeterMngStatics.ONE_HOUR; //default
            if (readings.size() > 2) {
                pointInterval = (readings.get(1).getStart().getTime() - readings.get(0).getStart().getTime());
                logger.info("Working out pointInterval: "+pointInterval);
            }
            //This fixes the issue of missing meter reading in between the search dates
            int readingIndex=0;
            Date previousIntervalDate=null;
            int readingCount=readings.size();
            int expectedIntervalCount= (int) ((readings.get(readingCount-1).getStart().getTime()-readings.get(0).getStart().getTime())/pointInterval)+1;
            
            Number[] points = new Number[readings.size()];
            BigDecimal totalConsumption = BigDecimal.ZERO;
            for(int i=0;i<expectedIntervalCount;i++) {           
            	if(previousIntervalDate==null){
            		previousIntervalDate=readings.get(0).getStart();
            	}
            	if((readings.get(readingIndex).getStart().getTime()-previousIntervalDate.getTime())>pointInterval){
            		points[i] =null;
            		previousIntervalDate=new Date(previousIntervalDate.getTime()+pointInterval);
            	}else{
            		Double reading = readings.get(readingIndex).getReading() / 1000; // kwh not kw
            		points[i] = reading;
            		previousIntervalDate=readings.get(readingIndex).getStart();
            		readingIndex++;
            		totalConsumption = totalConsumption.add(BigDecimal.valueOf(reading));
            	}
            }
            if (graphType.equals(MessagesUtil.getInstance().getMessage("meterreadings.type.graph.single"))) {
                chart.setChartSubtitle(new ChartSubtitle().setText(totalConsumption + " " + chartSubtitle));
            } else {
                chart.setChartSubtitle(new ChartSubtitle().setText(chartSubtitle));
            }
            Series series = null;
            if (areaChart) {
                series = chart.createSeries().setType(Series.Type.AREA).setName(name);
            } else {
                series = chart.createSeries().setName(name);
            }
            
            series.setPlotOptions(new AreaPlotOptions().setFillColor("#FFFFFF"));
            
            if (colour != null) {
                series.setPlotOptions(new AreaPlotOptions()
                .setMarker(new Marker().setEnabled(false).setHoverState(new Marker().setEnabled(true).setRadius(5)))
                .setLineWidth(1)
                .setPointInterval(pointInterval)
                .setPointStart(readings.get(0).getStart().getTime())
                .setColor(colour));
            } else {
               series.setPlotOptions(new AreaPlotOptions()  
                .setMarker(new Marker().setEnabled(false).setHoverState(new Marker().setEnabled(true).setRadius(5)))
                .setLineWidth(1)
                .setPointInterval(pointInterval)
                .setPointStart(readings.get(0).getStart().getTime()));
            }
            series.setPoints(points);
            chart.addSeries(series);  
            logger.info("Added series: "+name);
        } else {
            logger.info("No readings for series: "+name+" "+colour);
        }
    }   
}
