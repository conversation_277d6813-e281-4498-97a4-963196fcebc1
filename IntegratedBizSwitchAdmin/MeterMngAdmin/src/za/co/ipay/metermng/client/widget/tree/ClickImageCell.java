package za.co.ipay.metermng.client.widget.tree;

import com.google.gwt.cell.client.AbstractCell;
import com.google.gwt.dom.client.BrowserEvents;
import com.google.gwt.safehtml.shared.SafeHtmlBuilder;

public class ClickImageCell extends AbstractCell<String> {

    private String altText;
    private String toolTipText;

    public ClickImageCell(String altText) {
        super(BrowserEvents.CLICK);
        this.altText = altText;
    }

    public ClickImageCell(String altText, String toolTipText) {
        this(altText);
        this.toolTipText = toolTipText;
    }

    @Override
    public void render(Context context, String value, SafeHtmlBuilder sb) {
        if (value != null) {
            if (toolTipText != null) {
                sb.appendHtmlConstant("<img src='" + value + "' alt='" + altText + "' title='" + toolTipText + "'/>");
            } else {
                sb.appendHtmlConstant("<img src='" + value + "' alt='" + altText + "'/>");
            }
        }
    }
}
