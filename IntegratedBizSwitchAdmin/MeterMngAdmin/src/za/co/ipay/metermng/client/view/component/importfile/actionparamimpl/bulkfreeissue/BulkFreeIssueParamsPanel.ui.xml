<!DOCTYPE ui:UiBinder SYSTEM "http://dl.google.com/gwt/DTD/xhtml.ent">
<ui:UiBinder xmlns:ui="urn:ui:com.google.gwt.uibinder" 
             xmlns:g="urn:import:com.google.gwt.user.client.ui" 
             xmlns:f="urn:import:za.co.ipay.gwt.common.client.form"
             >

    <ui:with field="msg" type="za.co.ipay.metermng.client.i18n.UiMessages" />

    <g:VerticalPanel ui:field="bulkFreeIssueParamsPanel" spacing="10">
        <f:FormRowPanel>
            <g:Label ui:field="bulkFreeIssueParamHeading" styleName="gwt-Label-header" />
        </f:FormRowPanel>
        
        <f:FormRowPanel>
            <f:FormElement ui:field="unitsElement" labelText="{msg.getBulkFreeIssueUnits}:" required="false">
                <g:IntegerBox ui:field="unitsBox" styleName="gwt-TextBox" />
            </f:FormElement>
        </f:FormRowPanel>
        
        <f:FormRowPanel>
            <f:FormElement ui:field="descriptionElement" labelText="{msg.getBulkFreeIssueDescription}:" required="false">
                <g:TextBox ui:field="descriptionBox" styleName="gwt-TextBox" maxLength="100" />
            </f:FormElement>
        </f:FormRowPanel>
        
        <f:FormRowPanel>
            <f:FormElement ui:field="userReferenceElement" labelText="{msg.getBulkFreeIssueUserReference}:" required="false">
                <g:TextBox ui:field="userReferenceBox" styleName="gwt-TextBox" maxLength="50" />
            </f:FormElement>
        </f:FormRowPanel>
        
        <!-- Special Actions Reason Component will be added programmatically -->
        
    </g:VerticalPanel>
</ui:UiBinder>
