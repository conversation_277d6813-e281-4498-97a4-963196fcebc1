<!DOCTYPE ui:UiBinder SYSTEM "http://dl.google.com/gwt/DTD/xhtml.ent">
<ui:UiBinder xmlns:ui="urn:ui:com.google.gwt.uibinder"
             xmlns:g="urn:import:com.google.gwt.user.client.ui"
             xmlns:p1="urn:import:za.co.ipay.gwt.common.client.widgets"
             xmlns:p2="urn:import:za.co.ipay.gwt.common.client.form">
    <ui:style>

    </ui:style>
    <ui:with field="msg" type="za.co.ipay.metermng.client.i18n.UiMessages"/>
    <g:VerticalPanel ui:field="parentPanel">
        <p1:Message ui:field="feedBack" debugId="feedback"/>
        <g:FlowPanel ui:field="freetextflowpanel">
            <p2:FormElement ui:field="enterReason" helpMsg="{msg.getSpecialActionEnterReason}"
                            labelText="{msg.getSpecialActionEnterReason}:" required="true">
                <g:TextBox styleName="gwt-TextBox" ui:field="txtbxReasons" visibleLength="25"/>
            </p2:FormElement>
        </g:FlowPanel>
        <g:Label text="{msg.getSpecialActionAndOr}" horizontalAlignment="ALIGN_CENTER" ui:field="andOrLabel"></g:Label>
        <g:FlowPanel ui:field="selectreasonflowpanel">
            <p2:FormElement ui:field="selectReason" helpMsg="{msg.getSpecialActionSelectReason}"
                            labelText="{msg.getSpecialActionSelectReason}:" required="true">
                <g:ListBox visibleItemCount="1" ui:field="lstbxReasons" styleName="gwt-ListBox-ipay"
                           multipleSelect="false" debugId="lstbxReasons"/>
            </p2:FormElement>
        </g:FlowPanel>
    </g:VerticalPanel>
</ui:UiBinder>


