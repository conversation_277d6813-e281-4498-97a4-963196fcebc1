package za.co.ipay.metermng.client.view.workspace.billingdet;

import java.util.ArrayList;
import java.util.List;
import java.util.logging.Logger;

import com.google.gwt.core.client.GWT;
import com.google.gwt.event.dom.client.ClickEvent;
import com.google.gwt.event.dom.client.ClickHandler;
import com.google.gwt.place.shared.Place;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.user.cellview.client.TextColumn;
import com.google.gwt.user.client.ui.Widget;
import com.google.gwt.view.client.ListDataProvider;
import za.co.ipay.gwt.common.client.Dialogs;
import za.co.ipay.gwt.common.client.form.FormManager;
import za.co.ipay.gwt.common.client.handler.ConfirmHandler;
import za.co.ipay.gwt.common.client.resource.Messages;
import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.gwt.common.client.validation.ClientValidatorUtil;
import za.co.ipay.gwt.common.client.widgets.ScrollableTabLayoutPanel;
import za.co.ipay.gwt.common.client.workspace.SessionCheckCallback;
import za.co.ipay.gwt.common.client.workspace.SimpleTableView;
import za.co.ipay.gwt.common.client.workspace.TabLayoutWorkspaceContainer;
import za.co.ipay.gwt.common.client.workspace.Workspace;
import za.co.ipay.gwt.common.client.workspace.WorkspaceCreateCallback;
import za.co.ipay.gwt.common.client.workspace.WorkspaceFactory;
import za.co.ipay.gwt.common.client.workspace.WorkspaceNotification;
import za.co.ipay.gwt.common.client.workspace.WorkspaceNotification.NotificationType;
import za.co.ipay.gwt.common.shared.exception.AccessControlException;
import za.co.ipay.metermng.client.event.BillingDetEvent;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.history.BillingDetPlace;
import za.co.ipay.metermng.client.resource.image.MediaResource.MediaResourceUtil;
import za.co.ipay.metermng.client.rpc.callback.ClientCallback;
import za.co.ipay.metermng.client.view.component.tariff.ITariffUIClass;
import za.co.ipay.metermng.client.view.workspace.BaseWorkspace;
import za.co.ipay.metermng.client.view.workspace.pricing.PricingStructureWorkspaceView;
import za.co.ipay.metermng.client.widget.StatusTableColumn;
import za.co.ipay.metermng.datatypes.RecordStatus;
import za.co.ipay.metermng.shared.MeterMngStatics;
import za.co.ipay.metermng.shared.dto.BillingDetAppliesDto;
import za.co.ipay.metermng.shared.dto.IdNameDto;

public class BillingDetWorkspaceView extends BaseWorkspace implements FormManager<BillingDetAppliesDto> {

    private Logger logger = Logger.getLogger("BillingDetWorkspaceView");

    private ClientFactory clientFactory;

    private BillingDetAppliesDto billingDetAppliesDto;

    private List<BillingDetAppliesDto> billingDetList;
    @UiField SimpleTableView<BillingDetAppliesDto> view;
    protected BillingDetPanel panel;

    private ListDataProvider<BillingDetAppliesDto> dataProvider = new ListDataProvider<BillingDetAppliesDto>();
    private static String yesText = MessagesUtil.getInstance().getMessage("option.positive");

    private static BillingDetUiBinder uiBinder = GWT.create(BillingDetUiBinder.class);

    public static final class BillingDetWorkspaceFactory implements WorkspaceFactory {
        private ClientFactory clientFactory;

        public BillingDetWorkspaceFactory(ClientFactory clientFactory) {
            this.clientFactory = clientFactory;
            clientFactory.getWorkspaceContainer().register(this);
        }

        @Override
        public void createWorkspace(Place place, WorkspaceCreateCallback workspaceCreateCallback) {
            if (!clientFactory.getUser().hasPermission(MeterMngStatics.ACCESS_PERMISSION_MM_BILLING_DET_ADMIN)) {
                Dialogs.displayErrorMessage(MessagesUtil.getInstance().getMessage("error.accessdenied"),
                        MediaResourceUtil.getInstance().getLockedIcon(),
                        MessagesUtil.getInstance().getMessage("button.close"));
                workspaceCreateCallback.onWorkspaceCreationFailed(new AccessControlException("Access is Denied"));
                return;
            }
            try {
                BillingDetWorkspaceView billingDetWorkspaceView = new BillingDetWorkspaceView(clientFactory, (BillingDetPlace) place);
                workspaceCreateCallback.onWorkspaceCreated(billingDetWorkspaceView);
            } catch (Exception e) {
                workspaceCreateCallback.onWorkspaceCreationFailed(e);
            }
        }

        @Override
        public boolean handles(Place place) {
            return place instanceof BillingDetPlace;
        }

    }


    interface BillingDetUiBinder extends UiBinder<Widget, BillingDetWorkspaceView> {
    }

    public BillingDetWorkspaceView(ClientFactory clientFactory, BillingDetPlace place) {
        this.clientFactory = clientFactory;
        initWidget(uiBinder.createAndBindUi(this));
        setPlaceString("billingDet:all");
        setHeaderText(MessagesUtil.getInstance().getMessage("billingdet.tab.label"));
        initUi();
    }

    private void initUi() {
        initView();
        initForm();
        populate();
        initUI();
    }

    private void initView() {
        view.setFormManager(this);
    }

    private void initForm() {
        panel = new BillingDetPanel(view.getForm());
        view.getForm().setHasDirtyDataManager(this);
        view.getForm().getFormFields().add(panel);

        view.getForm().getFormPanel().setHeadingText(MessagesUtil.getInstance().getMessage("billingdet.title.add"));

        view.getForm().getSaveBtn().setText(MessagesUtil.getInstance().getMessage("button.create"));
        view.getForm().getSaveBtn().addClickHandler(new ClickHandler() {
            @Override
            public void onClick(ClickEvent event) {
                onSaveButtonClick();
            }
        });
        view.getForm().getSaveBtn().ensureDebugId("saveButton");

        view.getForm().getOtherBtn().setText(MessagesUtil.getInstance().getMessage("button.cancel"));
        view.getForm().getOtherBtn().addClickHandler(new ClickHandler() {
            @Override
            public void onClick(ClickEvent event) {
                view.getForm().checkDirtyData(new ConfirmHandler() {
                    @Override
                    public void confirmed(boolean confirm) {
                        if (confirm) {
                            view.getForm().setDirtyData(false);
                            displaySelected(null);
                        }
                    }
                });
            }
        });
        view.getForm().getOtherBtn().ensureDebugId("cancelButton");
    }

    private void initUI() {
        TextColumn<BillingDetAppliesDto> nameColumn = new TextColumn<BillingDetAppliesDto>() {
            @Override
            public String getValue(BillingDetAppliesDto billingDetAppliesDto) {
                return billingDetAppliesDto.getName();
            }
        };
        nameColumn.setSortable(true);
        TextColumn<BillingDetAppliesDto> descriptionColumn = new TextColumn<BillingDetAppliesDto>() {
            @Override
            public String getValue(BillingDetAppliesDto billingDetAppliesDto) {
                return billingDetAppliesDto.getDescription();
            }
        };
        TextColumn<BillingDetAppliesDto> discountColumn = new TextColumn<BillingDetAppliesDto>() {
            @Override
            public String getValue(BillingDetAppliesDto billingDetAppliesDto) {
                return (billingDetAppliesDto.isDiscount()) ? yesText : "";
            }
        };
        TextColumn<BillingDetAppliesDto> appliesToColumn = new TextColumn<BillingDetAppliesDto>() {
            @Override
            public String getValue(BillingDetAppliesDto billingDetAppliesDto) {
                StringBuilder sb = new StringBuilder();
                for (IdNameDto idName : billingDetAppliesDto.getAppliesToBillingDets()) {
                    sb.append(idName.getName()).append(", ");
                }
                String appliesTo = sb.toString();
                if (appliesTo.length() > 2) {
                    appliesTo = appliesTo.substring(0, appliesTo.length() - 2);
                }
                return appliesTo;
            }
        };
        StatusTableColumn<BillingDetAppliesDto> statusColumn = new StatusTableColumn<BillingDetAppliesDto>();

        // Add the columns.
        view.getTable().addColumn(nameColumn, MessagesUtil.getInstance().getMessage("auxtype.field.name"));
        view.getTable().addColumn(descriptionColumn, MessagesUtil.getInstance().getMessage("auxtype.field.description"));
        view.getTable().addColumn(discountColumn, MessagesUtil.getInstance().getMessage("billingdet.discount.label"));
        view.getTable().addColumn(appliesToColumn, MessagesUtil.getInstance().getMessage("billingdet.applies.to.label"));
        view.getTable().addColumn(statusColumn, MessagesUtil.getInstance().getMessage("auxtype.field.status"));
        view.getTable().ensureDebugId("billingDetTable");

        dataProvider.addDataDisplay(view.getTable());
        view.getPager().setDisplay(view.getTable());
        view.getTable().setPageSize(getPageSize());
    }

    private void setBillingDet(BillingDetAppliesDto type) {
        panel.clearErrors();
        panel.clearFields();
        this.billingDetAppliesDto = type;
        List<BillingDetAppliesDto> billingDetAppliesDtoDropDownList = new ArrayList<BillingDetAppliesDto>();
        if (billingDetAppliesDto != null) {
            //remove the selected billingDet from the dropdown list
            for(BillingDetAppliesDto bdd: billingDetList) {
                if (!bdd.getId().equals(billingDetAppliesDto.getId())) {
                    billingDetAppliesDtoDropDownList.add(bdd);
                }
            }
            view.getForm().getSaveBtn().setText(MessagesUtil.getInstance().getMessage("button.update"));
            view.getForm().getFormPanel().setHeadingText(MessagesUtil.getInstance().getMessage("billingdet.title.update"));
        } else {
            billingDetAppliesDto = new BillingDetAppliesDto();
            billingDetAppliesDto.setRecordStatus(RecordStatus.ACT);
            billingDetAppliesDtoDropDownList.addAll(billingDetList);
            view.getForm().getSaveBtn().setText(MessagesUtil.getInstance().getMessage("button.create"));
            view.getForm().getFormPanel().setHeadingText(MessagesUtil.getInstance().getMessage("billingdet.title.add"));
            view.clearTableSelection();
        }

        panel.nameTextBox.setText(billingDetAppliesDto.getName());
        panel.descriptionTextBox.setText(billingDetAppliesDto.getDescription());
        panel.activeBox.setValue(RecordStatus.ACT.equals(billingDetAppliesDto.getRecordStatus()));
        panel.discountBox.setValue(billingDetAppliesDto.isDiscount());
        if (billingDetAppliesDto.getAppliesToBillingDets() == null || billingDetAppliesDto.getAppliesToBillingDets().isEmpty()) {
            panel.radioNone.setValue(true);
        } else if (billingDetAppliesDto.isPercentage()) {
            panel.radioPercentage.setValue(true);
        } else {
            panel.radioFlatRate.setValue(true);
        }
        panel.populateAppliesToDropDown(billingDetAppliesDtoDropDownList);
        panel.setSelectedAppliesTo(billingDetAppliesDto);
        panel.taxableBox.setValue(billingDetAppliesDto.isTaxable());
    }

    public void populate() {
        clientFactory.getBillingDetRpc().getBillingDetAppliesDtoList(new ClientCallback<ArrayList<BillingDetAppliesDto>>() {
            @Override
            public void onSuccess(ArrayList<BillingDetAppliesDto> result) {
                billingDetList = result;
                dataProvider.setList(billingDetList);
                dataProvider.refresh();

                panel.populateAppliesToDropDown(result);
            }
        });
    }

    @Override
    public void onLeaving() {

    }

    void onSaveButtonClick() {
        if (view.getForm().getSaveBtn().getText().equals(MessagesUtil.getInstance().getMessage("button.create"))){
            saveBillingDet();
        } else {
            updateBillingDet();
        }
    }

    private BillingDetAppliesDto update() {
        BillingDetAppliesDto bdpToUpdate = new BillingDetAppliesDto();
        if (this.billingDetAppliesDto != null && this.billingDetAppliesDto.getId() != null) {
            bdpToUpdate.setId(this.billingDetAppliesDto.getId());
        }
        formToMap(bdpToUpdate);
        return bdpToUpdate;
    }

    private BillingDetAppliesDto formToMap(BillingDetAppliesDto bdd) {
        bdd.setName(panel.nameTextBox.getText());
        bdd.setDescription(panel.descriptionTextBox.getText());
        bdd.setRecordStatus(panel.activeBox.getValue() ? RecordStatus.ACT : RecordStatus.DAC);
        bdd.setDiscount(panel.discountBox.getValue());
        bdd.setPercentage(panel.radioPercentage.getValue());
        bdd.setAppliesToBillingDets(getPanelAppliesToBillingDets());
        bdd.setTaxable(panel.taxableBox.getValue());
        return bdd;
    }

    private List<IdNameDto> getPanelAppliesToBillingDets() {
        ArrayList<IdNameDto> selected = new ArrayList<IdNameDto>();
        for(int i=0;i<panel.appliesToListBox.getItemCount();i++) {
            if (panel.appliesToListBox.isItemSelected(i) && !panel.appliesToListBox.getItemText(i).isEmpty()) {
                selected.add(new IdNameDto(Long.valueOf(panel.appliesToListBox.getValue(i)), panel.appliesToListBox.getItemText(i)));
            }
        }
        return selected;
    }

    private boolean isValid() {
        boolean valid = true;
        panel.clearErrors();

        BillingDetAppliesDto bdd = formToMap(new BillingDetAppliesDto());

        if (!ClientValidatorUtil.getInstance().validateField(bdd, "name", panel.nameElement)) {
            valid = false;
        }
        if (!ClientValidatorUtil.getInstance().validateField(bdd, "description", panel.descriptionElement)) {
            valid = false;
        }

        Messages messages = MessagesUtil.getInstance();
        if (bdd.isDiscount() && panel.radioNone.getValue()) {
            panel.appliesToError.setText(messages.getMessage("unitcharge.discount.type.charge.error"));
            panel.appliesToError.setVisible(true);
            valid = false;
        }

        if (panel.radioNone.getValue()) {
            if (!bdd.getAppliesToBillingDets().isEmpty()) {
                panel.appliesToElement.setErrorMsg(messages.getMessage("billingdet.lnk.error.both"));
                valid = false;
            }
        } else {
            if (bdd.getAppliesToBillingDets().isEmpty()) {
                panel.appliesToElement.setErrorMsg(messages.getMessage("billingdet.lnk.error.both"));
                valid = false;
            }
        }

        if (!bdd.isTaxable() && panel.radioNone.getValue()) {
            panel.appliesToError.setText(messages.getMessage("billingdet.lnk.error.taxable"));
            panel.appliesToError.setVisible(true);
            valid = false;
        }

        return valid;
    }

    public void refreshPage(ArrayList<BillingDetAppliesDto> result) {
        setBillingDet(null);
        List<BillingDetAppliesDto> list = dataProvider.getList();
        list.clear();
        list.addAll(result);
        dataProvider.refresh();
        view.getTable().redraw();
        panel.populateAppliesToDropDown(result);
        clientFactory.getEventBus().fireEvent(new BillingDetEvent());
        sendNotification();
    }

    public void saveBillingDet() {
        SessionCheckCallback sessionCheckCallback = new SessionCheckCallback() {
            @Override
            public void callback(SessionCheckResolution sessionCheckResolution) {
                if (isValid()) {
                    //check if have tariff UI open and populated with RegisterReadingBlockTariffView
                    final int tabCount = clientFactory.getWorkspaceContainer().getWorkspacesCount();
                    final ScrollableTabLayoutPanel tabLayoutPanel = ((TabLayoutWorkspaceContainer)clientFactory.getWorkspaceContainer()).getTabLayoutPanel();
                    boolean askToClose = false;
                    for (int i = 0; i < tabCount; i++) {
                        Workspace w = (Workspace) tabLayoutPanel.getWidget(i);
                        if(w instanceof PricingStructureWorkspaceView) {
                            ITariffUIClass tariffUIClass = ((PricingStructureWorkspaceView) w).getTariffView().getPanel().getTariffUIClass();
                            if (null == tariffUIClass) {
                                continue;
                            }
                            if (tariffUIClass.getClass().getSimpleName().contains("RegisterReadingBlockTariffView")) {
                                askToClose = true;
                                break;
                            }
                        }
                    }
                    if (askToClose) {
                        final Messages messages = MessagesUtil.getInstance();
                        Dialogs.confirm(messages.getMessage("billingDet.change.regread.panel.open"),
                                messages.getMessage("button.yes"),
                                messages.getMessage("button.no"),
                                MediaResourceUtil.getInstance().getQuestionIcon(),
                                new ConfirmHandler() {
                                    @Override
                                    public void confirmed(boolean confirm) {
                                        if(confirm) {
                                            saveBillingDetContinue();
                                        } else {
                                            return;
                                        }
                                    }
                                },
                                view.getForm().getSaveBtn().getAbsoluteLeft() + view.getForm().getSaveBtn().getOffsetWidth(),
                                view.getForm().getSaveBtn().getAbsoluteTop() - 50);  //minus to allow for height of message in case its at the bottom of screen
                    } else {
                        saveBillingDetContinue();
                    }
                }
            }
        };
        clientFactory.handleSessionCheckCallback(sessionCheckCallback);
    }

    private void saveBillingDetContinue() {
        final BillingDetAppliesDto bdToUpdate = update();
        clientFactory.getBillingDetRpc().saveBillingDet(bdToUpdate, new ClientCallback<ArrayList<BillingDetAppliesDto>>(view.getForm().getSaveBtn().getAbsoluteLeft(), view.getForm().getSaveBtn().getAbsoluteTop()) {
            @Override
            public void onSuccess(ArrayList<BillingDetAppliesDto> result) {
                if (result != null) {
                    Dialogs.displayInformationMessage(MessagesUtil.getInstance().getSavedMessage(new String[] { MessagesUtil.getInstance().getMessage("billingdet.name") }),
                            MediaResourceUtil.getInstance().getInformationIcon(),
                            view.getForm().getSaveBtn().getAbsoluteLeft(), view.getForm().getSaveBtn().getAbsoluteTop(),
                            MessagesUtil.getInstance().getMessage("button.close"));
                    refreshPage(result);
                }
            }

            @Override
            public void onFailure(Throwable caught) {
                if (caught instanceof AccessControlException) {
                    clientFactory.getWorkspaceContainer().closeWorkspaceNow(BillingDetPlace.ALL_BILLING_DET_PLACE);
                }
                super.onFailure(caught);
            };
        });
    }

    public void updateBillingDet() {
        RecordStatus bdPanelRecordStatus = panel.activeBox.getValue() ? RecordStatus.ACT : RecordStatus.DAC;
        if (billingDetAppliesDto != null && !billingDetAppliesDto.getRecordStatus().equals(bdPanelRecordStatus) ) {
            SessionCheckCallback sessionCheckCallback = new SessionCheckCallback() {
                @Override
                public void callback(SessionCheckResolution sessionCheckResolution) {
                    clientFactory.getUsagePointRpc().countUpWithMdcUsingBillingDetId(billingDetAppliesDto.getId(), clientFactory.getUser().getUserName(), new ClientCallback<List<Integer>>() {
                        @Override
                        public void onSuccess(List<Integer> result) {
                            int act = result.get(0) == null ? 0 : result.get(0);
                            int dac = result.get(1) == null ? 0 : result.get(1);
                            if (act + dac > 0) {
                                logger.info("BillingDetWorkspaceView: on change status of billingDetAppliesDto: " + billingDetAppliesDto.getName() + " ActiveUpWithMdcUsingBillingDetId = " + act + " inactive=" + dac + " Warning displayed to user: " + clientFactory.getUser().getUserName());
                                final Messages messages = MessagesUtil.getInstance();
                                Dialogs.confirm(messages.getMessage("warning.change.status.billing.det.but.in.use", new String[] {result.get(0).toString(), result.get(1).toString()}),
                                        messages.getMessage("button.yes"),
                                        messages.getMessage("button.no"),
                                        MediaResourceUtil.getInstance().getQuestionIcon(),
                                        new ConfirmHandler() {
                                            @Override
                                            public void confirmed(boolean confirm) {
                                                if(confirm) {
                                                    saveBillingDet();
                                                } else {
                                                    panel.activeBox.setValue(!panel.activeBox.getValue());
                                                    return;
                                                }
                                            }
                                        },
                                        view.getForm().getSaveBtn().getAbsoluteLeft() + view.getForm().getSaveBtn().getOffsetWidth(), view.getForm().getSaveBtn().getAbsoluteTop() - 50);  //minus to allow for height of message in case its at the bottom of screen
                            } else {
                                saveBillingDet();
                            }
                        }
                    });
                }
            };
            clientFactory.handleSessionCheckCallback(sessionCheckCallback);
        } else {
            saveBillingDet();
        }
    }

    private void sendNotification() {
        //Notify any affected tabs
        clientFactory.getWorkspaceContainer().notifyWorkspaces(new WorkspaceNotification(NotificationType.DATA_UPDATED, "billingDet"));
    }

    @Override
    public void onArrival(Place place) {
        logger.info("Arrived at billing det");
        clientFactory.getBillingDetRpc().getBillingDetAppliesDtoList(new ClientCallback<ArrayList<BillingDetAppliesDto>>() {
            @Override
            public void onSuccess(ArrayList<BillingDetAppliesDto> result) {
                List<BillingDetAppliesDto> list = dataProvider.getList();
                list.clear();
                list.addAll(result);
                dataProvider.refresh();
            }

            @Override
            public void onFailure(Throwable caught) {
                if (caught instanceof AccessControlException) {
                    clientFactory.getWorkspaceContainer().closeWorkspaceNow(BillingDetPlace.ALL_BILLING_DET_PLACE);
                }
                super.onFailure(caught);
            }
        });
    }

    @Override
    public void onClose() {
    }

    @Override
    public boolean handles(Place place) {
        return  place instanceof BillingDetPlace;
    }

    @Override
    public void onSelect() {

    }

    @Override
    public void displaySelected(BillingDetAppliesDto selected) {
        setBillingDet(selected);
    }
}
