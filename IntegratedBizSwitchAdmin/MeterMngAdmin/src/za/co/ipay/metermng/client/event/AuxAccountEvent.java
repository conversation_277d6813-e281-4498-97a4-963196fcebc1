package za.co.ipay.metermng.client.event;

import com.google.gwt.event.shared.GwtEvent;

public class AuxAccountEvent extends GwtEvent<AuxAccountEventHandler> {

    public static Type<AuxAccountEventHandler> TYPE = new Type<AuxAccountEventHandler>();
    
    public static final int AUX_ACNT_ADDED = 1;
    public static final int AUX_ACNT_UPDATED = 2;
    public static final int AUX_ACNT_FREE_ISSUE_SELECTED = 3;
    
    private Long customerAgreementId;
    private Long auxAccountId;
    private int auxAccountEventType;
    
    public AuxAccountEvent(Long customerAgreementId, Long auxAccountId, int auxAccountEventType) {
        this.customerAgreementId = customerAgreementId;
        this.auxAccountId = auxAccountId;
        this.auxAccountEventType = auxAccountEventType;
    }
    
    public AuxAccountEvent(Long auxAccountId, int auxAccountEventType) {
        this.auxAccountId = auxAccountId;
        this.auxAccountEventType = auxAccountEventType;
    }
    
    public Long getCustomerAgreementId() {
        return customerAgreementId;
    }
    
    public Long getAuxAccountId() {
		return auxAccountId;
	}

    public int getAuxAccountEventType() {
    	return auxAccountEventType;
    }
    
	@Override
    public Type<AuxAccountEventHandler> getAssociatedType() {
        return TYPE;
    }

    @Override
    protected void dispatch(AuxAccountEventHandler handler) {
        handler.processAuxAccountEvent(this);
    }


}
