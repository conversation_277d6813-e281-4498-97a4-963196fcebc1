package za.co.ipay.metermng.client.view.workspace.meter.balancing.meter;

import za.co.ipay.gwt.common.client.Dialogs;
import za.co.ipay.gwt.common.client.handler.ConfirmHandler;
import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.gwt.common.client.widgets.TablePager;
import za.co.ipay.gwt.common.client.workspace.SessionCheckCallback;
import za.co.ipay.gwt.common.client.workspace.SimpleFormDisplayView;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.history.EnergyBalancingMeterPlace;
import za.co.ipay.metermng.client.resource.image.MediaResource.MediaResourceUtil;
import za.co.ipay.metermng.client.rpc.callback.ClientCallback;
import za.co.ipay.metermng.client.view.workspace.BaseWorkspace;
import za.co.ipay.metermng.shared.dto.meter.EnergyBalancingDto;

import com.google.gwt.core.client.GWT;
import com.google.gwt.event.dom.client.ClickEvent;
import com.google.gwt.event.dom.client.ClickHandler;
import com.google.gwt.place.shared.Place;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.user.cellview.client.CellTable;
import com.google.gwt.user.client.ui.Widget;
import com.google.gwt.view.client.ListDataProvider;

/**
 * EnergyBalancingMeterWorkspaceView allows the user to specify a date range and a variation percent for super/sub meter
 * readings. Any readings within the date range above the variation percent are displayed.
 *
 * <AUTHOR>
 */
public class EnergyBalancingMeterWorkspaceView extends BaseWorkspace {

    @UiField SimpleFormDisplayView view;
    EnergyBalancingMeterPanel panel;
    CellTable<EnergyBalancingDto> table;
    TablePager pager;
    ListDataProvider<EnergyBalancingDto> dataProvider;

    private static EnergyBalancingWorkspaceViewUiBinder uiBinder = GWT.create(EnergyBalancingWorkspaceViewUiBinder.class);

    interface EnergyBalancingWorkspaceViewUiBinder extends UiBinder<Widget, EnergyBalancingMeterWorkspaceView> {
    }

    public EnergyBalancingMeterWorkspaceView(ClientFactory clientFactory, EnergyBalancingMeterPlace place) {
        this.clientFactory = clientFactory;
        initWidget(uiBinder.createAndBindUi(this));
        setPlaceString(EnergyBalancingMeterPlace.getPlaceAsString(place));
        setHeaderText(MessagesUtil.getInstance().getMessage("energybalancing.meter.title"));
        initUi();
    }

    private void initUi() {
        initForm();
    }

    private void initForm() {
        panel = new EnergyBalancingMeterPanel(clientFactory, view.getForm());
        view.getForm().setHasDirtyDataManager(this);
        view.getForm().getFormFields().add(panel);

        view.getForm().getSaveBtn().setText(MessagesUtil.getInstance().getMessage("button.save"));
        view.getForm().getSaveBtn().addClickHandler(new ClickHandler() {
            @Override
            public void onClick(ClickEvent event) {
                onSave();
            }
        });
        view.getForm().getSaveBtn().ensureDebugId("saveButton");

        view.getForm().getOtherBtn().setText(MessagesUtil.getInstance().getMessage("button.clear"));
        view.getForm().getOtherBtn().addClickHandler(new ClickHandler() {
            @Override
            public void onClick(ClickEvent event) {
                view.getForm().checkDirtyData(new ConfirmHandler() {
                    @Override
                    public void confirmed(boolean confirm) {
                        if (confirm) {
                            view.getForm().setDirtyData(false);
                            onClear();
                        }
                    }
                });
            }
        });
        view.getForm().getOtherBtn().ensureDebugId("cancelButton");

        view.getForm().getBackBtn().setText(MessagesUtil.getInstance().getMessage("button.delete"));
        view.getForm().getBackBtn().addClickHandler(new ClickHandler() {
            @Override
            public void onClick(ClickEvent event) {
                onDelete();
            }
        });
        view.getForm().getBackBtn().setVisible(true);
        view.getForm().getBackBtn().ensureDebugId("deleteButton");

        view.getForm().getFormPanel().setHeadingText(MessagesUtil.getInstance().getMessage("energybalancing.meter.title"), "pageSectionTitle");
    }

    private void onClear() {
        panel.clearErrors();
        panel.clearFields();
        if (dataProvider != null) {
            dataProvider.getList().clear();
        }
    }

    private void onSave() {
        if (isValidInput()) {
            SessionCheckCallback sessionCheckCallback = new SessionCheckCallback() {
                @Override
                public void callback(SessionCheckResolution resolution) {
                    clientFactory.getMeterRpc()
                            .saveSuperSubMeters(panel.selectedSuperMeter.getId(),
                                    panel.getSelectedSubMeters(),
                                    new ClientCallback<Void>() {
                                        @Override
                                        public void onSuccess(Void result) {
                                            view.getForm().setDirtyData(false);
                                            panel.clearFields();
                                            panel.clearErrors();
                                            Dialogs.displayInformationMessage(
                                                    MessagesUtil.getInstance().getMessage("energybalancing.meter.save"),
                                                    MediaResourceUtil.getInstance().getInformationIcon());
                                        }
                                    });
                }
            };
            clientFactory.handleSessionCheckCallback(sessionCheckCallback);
        }
    }

    private boolean isValidInput() {
        boolean valid = true;
        panel.clearErrors();

        if (panel.selectedSuperMeter == null) {
            valid = false;
            panel.superMeterElement.setErrorMsg(MessagesUtil.getInstance().getMessage("energybalancing.error.super"));
        }

        if (panel.selectedMetersBox.getItemCount() == 0) {
            valid = false;
            panel.selectedMetersElement.setErrorMsg(MessagesUtil.getInstance().getMessage("energybalancing.error.sub.selected"));
        }

        return valid;
    }

    private void onDelete() {
        boolean valid = true;
        if (panel.selectedSuperMeter == null) {
            valid = false;
            panel.superMeterElement.setErrorMsg(MessagesUtil.getInstance().getMessage("energybalancing.error.super"));
        }

        if (valid) {
            Dialogs.confirm(MessagesUtil.getInstance().getMessage("energybalancing.confirm.delete"),
                            MessagesUtil.getInstance().getMessage("option.positive"),
                            MessagesUtil.getInstance().getMessage("option.negative"),
                            MediaResourceUtil.getInstance().getQuestionIcon(),
                            new ConfirmHandler() {
                                @Override
                                public void confirmed(boolean confirm) {
                                    if (confirm) {
                                        SessionCheckCallback sessionCheckCallback = new SessionCheckCallback() {
                                            @Override
                                            public void callback(SessionCheckResolution resolution) {
                                                clientFactory.getMeterRpc().deleteSuperMeter(panel.selectedSuperMeter.getId(),
                                                        new ClientCallback<Void>() {
                                                            @Override
                                                            public void onSuccess(Void result) {
                                                                view.getForm().setDirtyData(false);
                                                                panel.clearFields();
                                                                panel.clearErrors();
                                                                Dialogs.displayInformationMessage(
                                                                        MessagesUtil.getInstance().getMessage("energybalancing.meter.deleted"),
                                                                        MediaResourceUtil.getInstance().getInformationIcon());
                                                            }
                                                        });
                                            }
                                        };
                                        clientFactory.handleSessionCheckCallback(sessionCheckCallback);
                                    }
                                }
            });
        }
    }

    @Override
    public void onLeaving() {

    }

    @Override
    public void onSelect() {

    }

    @Override
    public void onArrival(Place place) {
    }

    @Override
    public void onClose() {

    }

    @Override
    public boolean handles(Place place) {
        return (place instanceof EnergyBalancingMeterPlace);
    }
}
