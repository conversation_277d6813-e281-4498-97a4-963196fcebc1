package za.co.ipay.metermng.client.event;

import com.google.gwt.event.shared.GwtEvent;

public class OpenDisplayTokensEvent extends GwtEvent<OpenDisplayTokensEventHandler> {

    public static Type<OpenDisplayTokensEventHandler> TYPE = new Type<OpenDisplayTokensEventHandler>();

    @Override
    public Type<OpenDisplayTokensEventHandler> getAssociatedType() {
        return TYPE;
    }

    @Override
    protected void dispatch(OpenDisplayTokensEventHandler handler) {
        handler.openDisplayTokens(this);
    }
}
