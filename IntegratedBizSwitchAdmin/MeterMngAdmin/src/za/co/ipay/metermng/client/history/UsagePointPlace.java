package za.co.ipay.metermng.client.history;

import za.co.ipay.metermng.shared.MeterMngStatics;

import com.google.gwt.place.shared.PlaceTokenizer;
import com.google.gwt.place.shared.Prefix;

public class UsagePointPlace extends AbstractUsagePointPlace {

    public static final UsagePointPlace NEW_USAGEPOINT_PLACE = new UsagePointPlace("new");
    
    private String usagePointName;
    private boolean isFromURL = true;

    public UsagePointPlace(String usagePointName) {
        this.usagePointName = usagePointName;
    }
    
    public UsagePointPlace(String usagePointName, String previousHistoryToken) {
        this.usagePointName = usagePointName;
        this.previousHistoryToken = previousHistoryToken;
    }

    public UsagePointPlace(String usagePointName, boolean isFromURL) {
        super();
        this.usagePointName = usagePointName;
        this.isFromURL = isFromURL;
    }

    @Override
    public boolean isFromURL() {
        return isFromURL;
    }

    public String getUsagePointName() {
        return usagePointName;
    }

    @Override
    public boolean isNew() {
        return usagePointName.equalsIgnoreCase("new");
    }
    
    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((usagePointName == null) ? 0 : usagePointName.hashCode());
        return result;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        UsagePointPlace other = (UsagePointPlace) obj;
        if (usagePointName == null) {
            if (other.usagePointName != null)
                return false;
        } else if (!usagePointName.equals(other.usagePointName))
            return false;
        return true;
    }

    @Prefix(value = "usagepoint")
    public static class Tokenizer implements PlaceTokenizer<UsagePointPlace> {
        @Override
        public String getToken(UsagePointPlace place) {
            if (place.getPreviousHistoryToken() != null && !place.getPreviousHistoryToken().equals("")) {
                return place.getUsagePointName() + MeterMngStatics.PLACE_TOKEN_SEPARATOR + place.getPreviousHistoryToken();
            } else {
                return place.getUsagePointName();
            }
        }

        @Override
        public UsagePointPlace getPlace(String token) {
            if (token != null && token.length() > 0) {
                int index = token.indexOf(MeterMngStatics.PLACE_TOKEN_SEPARATOR);
                if (index > -1) {
                    return new UsagePointPlace(token.substring(0, index), token.substring(index+1));
                }
            }
            return new UsagePointPlace(token);
        }
    }
}
