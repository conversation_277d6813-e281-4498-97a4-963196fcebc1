package za.co.ipay.metermng.client.view.workspace.group.all;

import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.metermng.client.view.component.group.GenGroupParentComponent;
import za.co.ipay.metermng.shared.MeterMngStatics;
import za.co.ipay.metermng.shared.group.GroupTypeData;

import com.google.gwt.core.client.GWT;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.user.client.ui.Label;
import com.google.gwt.user.client.ui.Widget;

/**
 * GroupTypePanel is a very simple wrapper around a single read-only group type. No need to display anything on the UI
 * as the group type is visible else where on the UI.
 * 
 * <AUTHOR>
 */
public class GroupTypeSingleSelectionPanel extends GroupTypeSelection {
    
    @UiField Label instructions;
    private GenGroupParentComponent parentWorkspace;
    private GroupTypeData groupTypeData;

    private static GroupTypePanelUiBinder uiBinder = GWT.create(GroupTypePanelUiBinder.class);

    interface GroupTypePanelUiBinder extends UiBinder<Widget, GroupTypeSingleSelectionPanel> {
    }

    public GroupTypeSingleSelectionPanel(GenGroupParentComponent parentWorkspace, String singleSelectionType) {
        super(null);
        this.parentWorkspace = parentWorkspace;        
        initWidget(uiBinder.createAndBindUi(this));
        setInstructions(singleSelectionType);
    }
    
    private void setInstructions(String singleSelectionType) {
        if (MeterMngStatics.ACCESS_GROUP_TYPE.equals(singleSelectionType)) {
            instructions.setText(MessagesUtil.getInstance().getMessage("accessgroup.access.instructions"));
        }
    }

    public void setGroupType(GroupTypeData groupTypeData) {
        this.groupTypeData = groupTypeData;
        clearFields();
        if (groupTypeData != null) {
            parentWorkspace.loadGroupHierarchies(groupTypeData.getId());
            parentWorkspace.loadGroups(groupTypeData.getId());
        }
        parentWorkspace.updateRoot();
    }
    
    @Override
    public GroupTypeData getGroupTypeData() {
        if (groupTypeData != null) {
            return groupTypeData;
        }
        return null;
    }
    
    @Override
    public Long getGroupTypeId() {
        if (groupTypeData != null) {
            return groupTypeData.getId();
        }
        return null;
    }

    @Override
    public String getGroupTypeName() {
        if (groupTypeData != null) {
            return groupTypeData.getName();
        }
        return null;
    }

    @Override
    public void setFocus() {
      //no implementation
    }

    @Override
    public void addFieldHandlers() {
      //no implementation
    }

    @Override
    public void clearFields() {
      //no implementation
    }

    @Override
    public void clearErrors() {
      //no implementation
    }

    @Override
    public void selectGroupType(Long groupTypeId) {
    }
}
