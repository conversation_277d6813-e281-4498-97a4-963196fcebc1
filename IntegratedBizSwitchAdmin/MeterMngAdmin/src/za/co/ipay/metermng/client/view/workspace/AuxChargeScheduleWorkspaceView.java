package za.co.ipay.metermng.client.view.workspace;

import java.util.Comparator;
import java.util.List;
import java.util.logging.Logger;

import com.google.gwt.core.client.GWT;
import com.google.gwt.event.dom.client.ClickEvent;
import com.google.gwt.event.dom.client.ClickHandler;
import com.google.gwt.place.shared.Place;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.user.cellview.client.ColumnSortEvent.ListHandler;
import com.google.gwt.user.cellview.client.TextColumn;
import com.google.gwt.user.client.History;
import com.google.gwt.user.client.ui.Widget;
import com.google.gwt.view.client.ListDataProvider;
import za.co.ipay.gwt.common.client.Dialogs;
import za.co.ipay.gwt.common.client.form.FormManager;
import za.co.ipay.gwt.common.client.handler.ConfirmHandler;
import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.gwt.common.client.workspace.SessionCheckCallback;
import za.co.ipay.gwt.common.client.workspace.SimpleTableView;
import za.co.ipay.gwt.common.client.workspace.WorkspaceCreateCallback;
import za.co.ipay.gwt.common.client.workspace.WorkspaceFactory;
import za.co.ipay.gwt.common.client.workspace.WorkspaceNotification;
import za.co.ipay.gwt.common.client.workspace.WorkspaceNotification.NotificationType;
import za.co.ipay.gwt.common.shared.exception.AccessControlException;
import za.co.ipay.metermng.client.event.AuxChargeScheduleUpdatedEvent;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.history.AuxChargeSchedulePlace;
import za.co.ipay.metermng.client.resource.image.MediaResource.MediaResourceUtil;
import za.co.ipay.metermng.client.rpc.callback.ClientCallback;
import za.co.ipay.metermng.client.widget.StatusTableColumn;
import za.co.ipay.metermng.datatypes.RecordStatus;
import za.co.ipay.metermng.mybatis.generated.model.AuxChargeSchedule;
import za.co.ipay.metermng.shared.MeterMngStatics;

public class AuxChargeScheduleWorkspaceView extends BaseWorkspace implements FormManager<AuxChargeSchedule> {

    ListDataProvider<AuxChargeSchedule> dataProvider;
    @UiField SimpleTableView<AuxChargeSchedule> view;
    AuxChargeSchedulePanel panel;

    private Place place;

    private Logger logger = Logger.getLogger("AuxChargeScheduleWorkspaceView");

    private static AuxChargeStructureWorkspaceViewUiBinder uiBinder = GWT.create(AuxChargeStructureWorkspaceViewUiBinder.class);

    interface AuxChargeStructureWorkspaceViewUiBinder extends UiBinder<Widget, AuxChargeScheduleWorkspaceView> {
    }

    public static final class AuxChargeStructureWorkspaceFactory implements WorkspaceFactory {
        private ClientFactory clientFactory;

        public AuxChargeStructureWorkspaceFactory(ClientFactory clientFactory) {
            this.clientFactory = clientFactory;
            clientFactory.getWorkspaceContainer().register(this);
        }

        @Override
        public void createWorkspace(Place place, WorkspaceCreateCallback workspaceCreateCallback) {
            if (!clientFactory.getUser().hasPermission(MeterMngStatics.ACCESS_PERMISSION_MM_AUX_SCHEDULE_ADMIN)) {
                Dialogs.displayErrorMessage(MessagesUtil.getInstance().getMessage("error.accessdenied"),
                        MediaResourceUtil.getInstance().getLockedIcon(),
                        MessagesUtil.getInstance().getMessage("button.close"));
                workspaceCreateCallback.onWorkspaceCreationFailed(new AccessControlException("Access is Denied"));
                return;
            }
            try {
                AuxChargeScheduleWorkspaceView auxChargeStructureWorkspaceView = new AuxChargeScheduleWorkspaceView(clientFactory, (AuxChargeSchedulePlace) place);
                workspaceCreateCallback.onWorkspaceCreated(auxChargeStructureWorkspaceView);
            } catch (Exception e) {
                workspaceCreateCallback.onWorkspaceCreationFailed(e);
            }
        }

        @Override
        public boolean handles(Place place) {
            return place instanceof AuxChargeSchedulePlace;
        }
    }

    public AuxChargeScheduleWorkspaceView(ClientFactory clientFactory, AuxChargeSchedulePlace auxChargeStructurePlace) {
        this.clientFactory = clientFactory;
        initWidget(uiBinder.createAndBindUi(this));
        initForm();
        logger.info("Arrived with place: "+auxChargeStructurePlace.toString());
        setPlaceString(auxChargeStructurePlace.toString());
        setHeaderText(MessagesUtil.getInstance().getMessage("auxchargeschedule.title"));
        populate();
        actionPermissions();
    }

    AuxChargeSchedule auxChargeSchedule;

    private void initForm() {
        view.setFormManager(this);
        panel = new AuxChargeSchedulePanel(view.getForm());
        view.getForm().setHasDirtyDataManager(this);
        view.getForm().getFormFields().add(panel);

        view.getForm().getSaveBtn().setText(MessagesUtil.getInstance().getMessage("button.create"));
        view.getForm().getSaveBtn().addClickHandler(new ClickHandler() {
            @Override
            public void onClick(ClickEvent arg0) {
                onSaveClick();
            }
        });
        view.getForm().getSaveBtn().ensureDebugId("saveButton");

        view.getForm().getOtherBtn().setText(MessagesUtil.getInstance().getMessage("button.cancel"));
        view.getForm().getOtherBtn().addClickHandler(new ClickHandler() {
            @Override
            public void onClick(ClickEvent event) {
                view.getForm().checkDirtyData(new ConfirmHandler() {
                    @Override
                    public void confirmed(boolean confirm) {
                        if (confirm) {
                            view.getForm().setDirtyData(false);
                            displaySelected(null);
                        }
                    }
                });
            }
        });
        view.getForm().getOtherBtn().ensureDebugId("cancelButton");

        view.getForm().getFormPanel().setHeadingText(MessagesUtil.getInstance().getMessage("auxchargeschedule.title.add"));
    }

    private void onSaveClick() {
        if (panel.isValidInput()) {
        	//update the form backing object
            auxChargeSchedule = panel.populateFromForm(auxChargeSchedule);
            SessionCheckCallback sessionCheckCallback = new SessionCheckCallback() {
                @Override
                public void callback(SessionCheckResolution resolution) {
                    clientFactory.getAuxChargeStructureRpc().updateAuxChargeSchedule(auxChargeSchedule,
                            new ClientCallback<AuxChargeSchedule>(view.getForm().getSaveBtn().getAbsoluteLeft(), view.getForm().getSaveBtn().getAbsoluteTop()) {
                                @Override
                                public void onSuccess(AuxChargeSchedule auxChargeSchedule) {
                                    Dialogs.displayInformationMessage(
                                            MessagesUtil.getInstance().getMessage("message.saved",
                                                    new String[] {MessagesUtil.getInstance().getMessage("auxchargeschedule.title")}),
                                            MediaResourceUtil.getInstance().getInformationIcon(),
                                            view.getForm().getSaveBtn().getAbsoluteLeft(), view.getForm().getSaveBtn().getAbsoluteTop(),
                                            MessagesUtil.getInstance().getMessage("button.close"));
                                    onArrival(AuxChargeSchedulePlace.ALL_AUX_CHARGE_STRUCTURES_PLACE);
                                    clear();
                                    clientFactory.getEventBus().fireEvent(new AuxChargeScheduleUpdatedEvent(auxChargeSchedule));
                                }

                                @Override
                                public void onFailure(Throwable caught) {
                                    if (caught instanceof AccessControlException) {
                                        clientFactory.getWorkspaceContainer().closeWorkspaceNow(AuxChargeSchedulePlace.ALL_AUX_CHARGE_STRUCTURES_PLACE);
                                    }
                                    super.onFailure(caught);
                                }
                            });
                }
            };
            clientFactory.handleSessionCheckCallback(sessionCheckCallback);
        }
    }

    public void populate() {
        logger.info("populate");
        createAuxChargeScheduleTable();
        panel.populate();
    }

    private void createAuxChargeScheduleTable() {
        if (dataProvider != null) {
            return;
        }

        TextColumn<AuxChargeSchedule> nameColumn = new TextColumn<AuxChargeSchedule>() {
          @Override
          public String getValue(AuxChargeSchedule auxChargeSchedule) {
            return auxChargeSchedule.getScheduleName();
          }
        };

        nameColumn.setSortable(true);

        view.getTable().addColumn(nameColumn, MessagesUtil.getInstance().getMessage("auxchargeschedule.field.name"));
        view.getTable().addColumn(new StatusTableColumn<AuxChargeSchedule>(), MessagesUtil.getInstance().getMessage("auxchargeschedule.field.status"));

        view.getTable().ensureDebugId("auxChargesScheduleTable");

        dataProvider = new ListDataProvider<AuxChargeSchedule>();
        dataProvider.addDataDisplay(view.getTable());
        view.getPager().setDisplay(view.getTable());
        List<AuxChargeSchedule> list = dataProvider.getList();

        ListHandler<AuxChargeSchedule> columnSortHandler = new ListHandler<AuxChargeSchedule>(list);
        columnSortHandler.setComparator(nameColumn,
            new Comparator<AuxChargeSchedule>() {
              public int compare(AuxChargeSchedule o1, AuxChargeSchedule o2) {
                  return o1.getScheduleName().compareTo(o2.getScheduleName());
              }
            });
        view.getTable().addColumnSortHandler(columnSortHandler);
        view.getTable().getColumnSortList().push(nameColumn);
    }

    public void setAuxChargeSchedule(AuxChargeSchedule acs) {
        this.auxChargeSchedule = acs;
        if (auxChargeSchedule == null) {
            auxChargeSchedule = new AuxChargeSchedule();
            auxChargeSchedule.setRecordStatus(RecordStatus.DAC);
            auxChargeSchedule.setIsArrears(false);
            view.getForm().getFormPanel().setHeadingText(MessagesUtil.getInstance().getMessage("auxchargeschedule.title.add"));
            view.getForm().getSaveBtn().setText(MessagesUtil.getInstance().getMessage("button.create"));
        } else {
            view.getForm().getFormPanel().setHeadingText(MessagesUtil.getInstance().getMessage("auxchargeschedule.title.update"));
            view.getForm().getSaveBtn().setText(MessagesUtil.getInstance().getMessage("button.update"));
        }
        panel.setAuxChargeSchedule(acs);
    }

    public void clear() {
        logger.info("clearing aux charge schedule");
        panel.clearFields();
        panel.clearErrors();
        setAuxChargeSchedule(null);
    }

    @Override
    public void onLeaving() {
    }

    @Override
    public void onArrival(final Place place) {
        logger.info("Arrived at aux charge schedule: "+place.toString());
        this.place = place;
        loadAuxChargeSchedules();
    }

    private void loadAuxChargeSchedules() {
        clientFactory.getAuxChargeStructureRpc().getAllAuxChargeSchedules(
                new ClientCallback<List<AuxChargeSchedule>>() {
                    @Override
                    public void onSuccess(List<AuxChargeSchedule> result) {
                        List<AuxChargeSchedule> list = dataProvider.getList();
                        list.clear();
                        list.addAll(result);
                        setFromPlace(place);
                    }

                    @Override
                    public void onFailure(Throwable caught) {
                        if (caught instanceof AccessControlException) {
                            clientFactory.getWorkspaceContainer().closeWorkspaceNow(AuxChargeSchedulePlace.ALL_AUX_CHARGE_STRUCTURES_PLACE);
                        }
                        super.onFailure(caught);
                    }
                });
    }

    private void setFromPlace(Place place) {
        if (place instanceof AuxChargeSchedulePlace) {
            AuxChargeSchedulePlace p = (AuxChargeSchedulePlace) place;
            if (p.getAuxChargeScheduleId() == null) {
                logger.info("No place id");
            } else if (AuxChargeSchedulePlace.ALL_DATA.equals(p.getAuxChargeScheduleId())) {
                logger.info("Place: All data");
            } else {
                logger.info("Place: id:"+p.getAuxChargeScheduleId());
                //Check if the current data contains the targeted id and display it if found
                //TODO (otherwise out of luck if lots of data - need some backend search fn to load specific data)
                //TODO Also resetting of window's current place when data selected in the data
                for(AuxChargeSchedule schedule : dataProvider.getList()) {
                    if (schedule.getId().toString().equals(p.getAuxChargeScheduleId())) {
                        logger.info("Found matching AuxChargeSchedule: "+schedule);
                        setAuxChargeSchedule(schedule);
                        view.setSelectedTableData(schedule);
                    }
                }
                //Reset the current place
                History.newItem(AuxChargeSchedulePlace.ALL_AUX_CHARGE_STRUCTURES_PLACE.toString(), false);
            }
        }
    }

    @Override
    public void onClose() {
    }

    @Override
    public void onSelect() {

    }

    @Override
    public boolean handles(Place place) {
        return  place instanceof AuxChargeSchedulePlace;
    }

    @Override
    public void displaySelected(AuxChargeSchedule selected) {
        clear();
        setAuxChargeSchedule(selected);
    }

    @Override
    public void handleNotification(WorkspaceNotification notification) {
        logger.info("Received notification: "+notification);
        if (MeterMngStatics.USER_CURRENT_GROUP.equals(notification.getDataType())
                && NotificationType.DATA_UPDATED == notification.getNotificationType()) {
            logger.info("The user's current group has changed - reloading this AuxChargeSchedule workspace...");
            clear();
            SessionCheckCallback sessionCheckCallback = new SessionCheckCallback() {
                @Override
                public void callback(SessionCheckResolution resolution) {
                    loadAuxChargeSchedules();
                }
            };
            clientFactory.handleSessionCheckCallback(sessionCheckCallback);
        }
    }

    private void actionPermissions() {
        if (!clientFactory.getUser().hasPermission(MeterMngStatics.EDIT_PERMISSION_MM_AUX_SCHEDULE)) {
            view.getForm().getButtons().removeFromParent();
        }
    }
}
