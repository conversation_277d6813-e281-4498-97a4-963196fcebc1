<!DOCTYPE ui:UiBinder SYSTEM "http://dl.google.com/gwt/DTD/xhtml.ent">
<ui:UiBinder xmlns:ui="urn:ui:com.google.gwt.uibinder" xmlns:g="urn:import:com.google.gwt.user.client.ui"
	xmlns:p1="urn:import:za.co.ipay.gwt.common.client.form" xmlns:g2="urn:import:com.google.gwt.user.datepicker.client"
	xmlns:w="urn:import:za.co.ipay.gwt.common.client.widgets">
	<ui:style>
	
	</ui:style>

	<ui:with field="msg" type="za.co.ipay.metermng.client.i18n.UiMessages" />

	<g:FlowPanel debugId="placeToClick" >
	
        <g:Label text="{msg.getSuperSubMeterInstructions}" styleName="verticalSpace" />

		<p1:FormRowPanel>
			<p1:FormElement ui:field="superMeterElement" debugId="superMeterElement" labelText="{msg.getSuperMeterNumber}:" required="true" helpMsg="{msg.getSuperMeterNumberHelp}">
				<g:SuggestBox ui:field="superMeterBox" debugId="superMeterBox" styleName="gwt-TextBox" />
			</p1:FormElement>
		</p1:FormRowPanel>
		
		<p1:FormRowPanel>
            <p1:FormElement ui:field="subMeterElement" debugId="subMeterElement" labelText="{msg.getSubMeterNumber}:" required="true" helpMsg="{msg.getSubMeterNumberHelp}">
                <g:SuggestBox ui:field="subMeterBox" debugId="subMeterBox" styleName="gwt-TextBox" />
            </p1:FormElement>
            <p1:FormElement ui:field="addElement" labelText="" required="false">
                <g:Button ui:field="addButton" debugId="addButton" text="&gt;" />
            </p1:FormElement>
            <p1:FormElement ui:field="selectedMetersElement" debugId="selectedMetersElement" labelText="{msg.getSubMeters}:" required="true" helpMsg="{msg.getSelectedSubMetersHelp}">
                <g:VerticalPanel>
                    <g:ListBox ui:field="selectedMetersBox" debugId="selectedMetersBox" styleName="gwt-TextBox veryWideSelect"  multipleSelect="true" visibleItemCount="5" />
                    <g:Button ui:field="removeButton" debugId="removeButton" text="{msg.getRemoveButton}" styleName="gwt-Button verticalSpace" />
                </g:VerticalPanel>
            </p1:FormElement>
        </p1:FormRowPanel>

	</g:FlowPanel>

</ui:UiBinder> 