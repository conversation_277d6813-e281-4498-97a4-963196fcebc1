package za.co.ipay.metermng.client.history;

import com.google.gwt.place.shared.Place;
import com.google.gwt.place.shared.PlaceTokenizer;
import com.google.gwt.place.shared.Prefix;

public class MeterBulkUploadPlace extends Place {
	
	public static MeterBulkUploadPlace ALL_PLACE = new MeterBulkUploadPlace("all");

    private String name;
    
    public MeterBulkUploadPlace(String name) {
        this.name = name;
    }
    
    public String getName() {
        return name;
    }
    
    public static String getPlaceAsString(MeterBulkUploadPlace p) {
        return "meterbulkupload:"+p.getName();
    }

    @Prefix(value = "meterbulkupload")
    public static class Tokenizer implements PlaceTokenizer<MeterBulkUploadPlace> {
        
        @Override
        public String getToken(MeterBulkUploadPlace place) {
            return "all";
        }

        @Override
        public MeterBulkUploadPlace getPlace(String token) {
            return new MeterBulkUploadPlace(token);
        }
    }

}
