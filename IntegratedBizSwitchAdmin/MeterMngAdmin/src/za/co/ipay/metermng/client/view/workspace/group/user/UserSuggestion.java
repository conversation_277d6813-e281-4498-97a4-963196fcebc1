package za.co.ipay.metermng.client.view.workspace.group.user;

import za.co.ipay.metermng.shared.dto.user.UserData;

import com.google.gwt.user.client.ui.SuggestOracle;

public class UserSuggestion implements SuggestOracle.Suggestion {
    
    private UserData user;
    
    public UserSuggestion(UserData user) {
        this.user = user;
    }

    @Override
    public String getDisplayString() {
        return user.getUsername();
    }

    @Override
    public String getReplacementString() {
        return user.getUsername();
    }

    public UserData getUser() {
        return user;
    }
}
