package za.co.ipay.metermng.client.event;

import com.google.gwt.event.shared.GwtEvent;

public class CalendarUpdatedEvent extends GwtEvent<CalendarUpdatedEventHandler> {

    public static Type<CalendarUpdatedEventHandler> TYPE = new Type<CalendarUpdatedEventHandler>();
    
    public CalendarUpdatedEvent() {
        
    }
    
	@Override
    public Type<CalendarUpdatedEventHandler> getAssociatedType() {
        return TYPE;
    }

    @Override
    protected void dispatch(CalendarUpdatedEventHandler handler) {
        handler.processCalendarUpdatedEvent(this);
    }


}
