package za.co.ipay.metermng.client.history;

import com.google.gwt.place.shared.Place;
import com.google.gwt.place.shared.PlaceTokenizer;
import com.google.gwt.place.shared.Prefix;

public class MeterCustUPUploadPlace extends Place {

    public static MeterCustUPUploadPlace ALL_PLACE = new MeterCustUPUploadPlace("all");

    private String name;
    
    public MeterCustUPUploadPlace(String name) {
        this.name = name;
    }
    
    public String getName() {
        return name;
    }
    
    public static String getPlaceAsString(MeterCustUPUploadPlace p) {
        return "metercustupupload:"+p.getName();
    }

    @Prefix(value = "metercustupupload")
    public static class Tokenizer implements PlaceTokenizer<MeterCustUPUploadPlace> {
        
        @Override
        public String getToken(MeterCustUPUploadPlace place) {
            return "all";
        }

        @Override
        public MeterCustUPUploadPlace getPlace(String token) {
            return new MeterCustUPUploadPlace(token);
        }
    }
}
