package za.co.ipay.metermng.client.history;

import com.google.gwt.place.shared.Place;
import com.google.gwt.place.shared.PlaceTokenizer;
import com.google.gwt.place.shared.Prefix;

public class MeterModelPlace extends Place {
    
    public static final String ALL = "all";
    
    private String name;
    
    public MeterModelPlace(String name) {
        this.name = name;
    }
    
    public String getName() {
        return name;
    }

    public static String getPlaceAsString(MeterModelPlace place) {
        return "metermodel:"+place.getName();
    }
    
    @Prefix(value = "metermodel")
    public static class Tokenizer implements PlaceTokenizer<MeterModelPlace> {
        
        @Override
        public String getToken(MeterModelPlace place) {
            return place.getName();
        }

        @Override
        public MeterModelPlace getPlace(String token) {
            return new MeterModelPlace(token);
        }
    }
}
