package za.co.ipay.metermng.client.view.workspace.schedule.cron;

import com.google.gwt.core.client.GWT;
import com.google.gwt.event.shared.HandlerRegistration;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.user.client.ui.ListBox;
import com.google.gwt.user.client.ui.Widget;
import za.co.ipay.gwt.common.client.form.FormElement;
import za.co.ipay.gwt.common.client.form.SimpleForm;
import za.co.ipay.gwt.common.client.handler.FormDataChangeHandler;
import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.metermng.client.form.SimpleFormPanel;

public class MonthlySchedulePanel extends SimpleFormPanel implements ScheduleUi {

    @UiField(provided=true) DayPanel dayPanel;
    @UiField FormElement dayElement;
    @UiField ListBox dayBox;
    HandlerRegistration dayReg;
    
    private static DailySchedulePanelUiBinder uiBinder = GWT.create(DailySchedulePanelUiBinder.class);

    interface DailySchedulePanelUiBinder extends UiBinder<Widget, MonthlySchedulePanel> {
    }

    public MonthlySchedulePanel(SimpleForm form) {
        super(form);
        dayPanel = new DayPanel(form);
        initWidget(uiBinder.createAndBindUi(this));
        addFieldHandlers();
        initUi();
    }
    
    private void initUi() {
        dayBox.addItem("");        
        for(int i=1;i<=31;i++) {
            dayBox.addItem(""+i);
        }
    }
    
    @Override
    public void setCronExpression(String cronExpression) {        
        CronExpressionParser parser = new CronExpressionParser(cronExpression);
        
        int hour = -1;
        int min = -1;
        String hours = parser.getHours();
        if (hours != null && !hours.trim().equals("")) {
            hour = Integer.valueOf(hours);
        }
        String minutes = parser.getMinutes();
        if (minutes != null && !minutes.trim().equals("")) {
            min = Integer.valueOf(minutes);
        }
        if (hour != -1 && min != -1) {
            dayPanel.setTime(hour, min);
        }
        
        setItem(dayBox, parser.getDayOfMonth());
    }

    private void setItem(ListBox box, String value) {
        if (value == null || value.trim().equals("")) {
            box.setSelectedIndex(0);
        } else {
            for(int i=0;i<box.getItemCount();i++) {
                if (box.getItemText(i).equalsIgnoreCase(value)) {
                    box.setSelectedIndex(i);
                    return;
                }
            }
        }
    }
    
    @Override
    public String getCronExpression() {
        //0 30 8 20 * ? - 20th of the month at 8:30:00
        return CronExpressionParser.getCronExpression(
                "0",
                dayPanel.getMinute(),
                dayPanel.getHour(),
                dayBox.getValue(dayBox.getSelectedIndex()),
                CronExpressionParser.ALL,
                CronExpressionParser.ANY);
    }
    
    @Override
    public boolean isValidInput() {
        dayElement.setErrorMsg(null);
        if (dayBox.getSelectedIndex() > 0) {
            return dayPanel.isValidInput();
        } else {
            dayElement.setErrorMsg(MessagesUtil.getInstance().getMessage("taskschedule.error.daymonth"));
            dayPanel.isValidInput(); //do the validation here too            
        }
        return false;
    }

    @Override
    public void addFieldHandlers() {
        dayReg = dayBox.addChangeHandler(new FormDataChangeHandler(form));
    }
    
    @Override
    public void removeFieldHandlers() {
        dayPanel.removeFieldHandlers();
        dayReg.removeHandler();
    }

    @Override
    public void clearFields() {
        dayPanel.clearFields();
        dayBox.setSelectedIndex(0);
    }

    @Override
    public void clearErrors() {
        dayPanel.clearErrors();
        dayElement.setErrorMsg(null);
    }
}
