package za.co.ipay.metermng.client.view.component;

import java.util.logging.Logger;

import com.google.gwt.core.client.GWT;
import com.google.gwt.event.dom.client.ChangeEvent;
import com.google.gwt.event.dom.client.ClickEvent;
import com.google.gwt.event.logical.shared.SelectionEvent;
import com.google.gwt.event.logical.shared.ValueChangeEvent;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiFactory;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.uibinder.client.UiHandler;
import com.google.gwt.user.client.ui.Button;
import com.google.gwt.user.client.ui.DialogBox;
import com.google.gwt.user.client.ui.ListBox;
import com.google.gwt.user.client.ui.MenuItem;
import com.google.gwt.user.client.ui.SuggestBox;
import com.google.gwt.user.client.ui.SuggestBox.DefaultSuggestionDisplay;
import com.google.gwt.user.client.ui.TextBox;
import com.google.gwt.user.client.ui.Widget;

import za.co.ipay.gwt.common.client.Dialogs;
import za.co.ipay.gwt.common.client.form.HasDirtyData;
import za.co.ipay.gwt.common.client.form.LocalOnlyHasDirtyData;
import za.co.ipay.gwt.common.client.handler.ConfirmHandler;
import za.co.ipay.gwt.common.client.handler.FormDataChangeHandler;
import za.co.ipay.gwt.common.client.handler.FormDataValueChangeHandler;
import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.gwt.common.client.widgets.Message;
import za.co.ipay.gwt.common.client.workspace.SessionCheckCallback;
import za.co.ipay.metermng.client.event.UsagePointUpdatedEvent;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.i18n.UiMessages;
import za.co.ipay.metermng.client.i18n.UiMessagesUtil;
import za.co.ipay.metermng.client.resource.image.MediaResource.MediaResourceUtil;
import za.co.ipay.metermng.client.rpc.callback.ClientCallback;
import za.co.ipay.metermng.client.view.workspace.UsagePointWorkspaceView;
import za.co.ipay.metermng.shared.CustomerSuggestion;
import za.co.ipay.metermng.shared.CustomerSuggestionsOracle;
import za.co.ipay.metermng.shared.dto.CustomerAgreementData;
import za.co.ipay.metermng.shared.dto.UsagePointData;

public class AssignCustomerDialogueBox extends DialogBox {

    private static Logger logger = Logger.getLogger(AssignCustomerDialogueBox.class.getName());
    private static AssignCustomerDialogueBoxUiBinder uiBinder = GWT.create(AssignCustomerDialogueBoxUiBinder.class);

    @UiField Button btnAssignCustomer;
    @UiField Button btnCancel;
    @UiField Message feedBack;

    @UiField ListBox searchCriterionOptions;
    @UiField(provided = true) SuggestBox suggestionBoxAssignCustomerAgrRef;
    @UiField(provided = true) SuggestBox suggestionBoxAssignCustomerIdNum;
    @UiField(provided = true) SuggestBox suggestionBoxAssignCustomerSurname;

    private CustomerSuggestion selectedCustomer;
    private ClientFactory clientFactory;
    private UsagePointData usagePointData;
    protected UsagePointWorkspaceView usagePointWorkspaceView;
    private HasDirtyData hasDirtyDataLocal = new LocalOnlyHasDirtyData();

    private boolean isFetch = true;
    private final String surnameOption = "surname";
    private final String idNumOption = "id-num";
    private final String agreementRefOption = "agr-ref";

    interface AssignCustomerDialogueBoxUiBinder extends UiBinder<Widget, AssignCustomerDialogueBox> {
    }

    public AssignCustomerDialogueBox(ClientFactory clientFactory, UsagePointWorkspaceView usagePointWorkspaceView) {
        this.clientFactory = clientFactory;
        this.usagePointWorkspaceView = usagePointWorkspaceView;
        CustomerSuggestionsOracle customerSuggestionsOracleSurname = new CustomerSuggestionsOracle(clientFactory, true, false, false);
        CustomerSuggestionsOracle customerSuggestionsOracleIdNum = new CustomerSuggestionsOracle(clientFactory, true, true, false);
        CustomerSuggestionsOracle customerSuggestionsOracleAgrRef = new CustomerSuggestionsOracle(clientFactory, true, false, true);
        suggestionBoxAssignCustomerAgrRef = createSuggestionBox(customerSuggestionsOracleAgrRef);
        suggestionBoxAssignCustomerIdNum = createSuggestionBox(customerSuggestionsOracleIdNum);
        suggestionBoxAssignCustomerSurname = createSuggestionBox(customerSuggestionsOracleSurname);
        setWidget(uiBinder.createAndBindUi(this));
        setSearchCriterion();
        feedBack.setVisible(false);
        this.ensureDebugId("assignCustomerDialogBox");
    }

    private SuggestBox createSuggestionBox(CustomerSuggestionsOracle customerSuggestionsOracle) {
        final SuggestBox box = new SuggestBox(customerSuggestionsOracle, new TextBox(), new ScrollableDefaultSuggestionDisplay());
        box.addValueChangeHandler(new FormDataValueChangeHandler<String>(hasDirtyDataLocal){
            @Override
            public void onValueChange(ValueChangeEvent<String> event) {
                super.onValueChange(event);
                if(selectedCustomer!=null && !box.getText().equals(selectedCustomer.getCustomerName())){
                    selectedCustomer = null;
                }
            }
        });
        return box;
    }

    private void setSearchCriterion() {
        searchCriterionOptions.addItem(MessagesUtil.getInstance().getMessage("customer.search.listbox.item_agr_ref"), agreementRefOption);
        searchCriterionOptions.addItem(MessagesUtil.getInstance().getMessage("customer.search.listbox.item_id_num"), idNumOption);
        searchCriterionOptions.addItem(MessagesUtil.getInstance().getMessage("customer.search.listbox.item_surname"), surnameOption);
        searchCriterionOptions.setSelectedIndex(2); // Default to surname
        searchCriterionOptions.addChangeHandler(new FormDataChangeHandler(hasDirtyDataLocal));
    }

    public void clearFields() {
        feedBack.setText("");
        feedBack.setVisible(false);
        selectedCustomer = null;
        resetSuggestionBoxes();
        searchCriterionOptions.setSelectedIndex(2);
    }

    private void resetSuggestionBoxes() {
        suggestionBoxAssignCustomerIdNum.getValueBox().setText("");
        suggestionBoxAssignCustomerAgrRef.getValueBox().setText("");
        suggestionBoxAssignCustomerSurname.getValueBox().setText("");
    }

    public void setUsagePointData(UsagePointData usagePointData) {
        this.usagePointData = usagePointData;
    }

    public void setIsFetch(boolean isFetch) {
        this.isFetch = isFetch;
    }

    public void setBtnAssignCustomerText(String btnText) {
        btnAssignCustomer.setText(MessagesUtil.getInstance().getMessage(btnText));
    }

    @UiFactory
    public UiMessages getUiMessages() {
        return UiMessagesUtil.getInstance();
    }

    @UiHandler("suggestionBoxAssignCustomerAgrRef")
    void handleSuggestBoxAgrRef(SelectionEvent<com.google.gwt.user.client.ui.SuggestOracle.Suggestion> se) {
        makeSelection((CustomerSuggestion) se.getSelectedItem(), suggestionBoxAssignCustomerAgrRef);
    }

    @UiHandler("suggestionBoxAssignCustomerSurname")
    void handleSuggestBoxSurname(SelectionEvent<com.google.gwt.user.client.ui.SuggestOracle.Suggestion> se) {
        makeSelection((CustomerSuggestion) se.getSelectedItem(), suggestionBoxAssignCustomerSurname);
    }

    @UiHandler("suggestionBoxAssignCustomerIdNum")
    void handleSuggestBoxIdNum(SelectionEvent<com.google.gwt.user.client.ui.SuggestOracle.Suggestion> se) {
        makeSelection((CustomerSuggestion) se.getSelectedItem(), suggestionBoxAssignCustomerIdNum);
    }

    @UiHandler("searchCriterionOptions")
    void handleSearchCriterion(ChangeEvent event) {
        String item = searchCriterionOptions.getValue(searchCriterionOptions.getSelectedIndex());
        suggestionBoxAssignCustomerSurname.setVisible(item.equals(surnameOption));
        suggestionBoxAssignCustomerAgrRef.setVisible(item.equals(agreementRefOption));
        suggestionBoxAssignCustomerIdNum.setVisible(item.equals(idNumOption));
        resetSuggestionBoxes();
    }

    @UiHandler("btnCancel")
    void handleCancelBtn(ClickEvent event) {
        if (hasDirtyDataLocal.isDirtyData()) {
            Dialogs.confirmDiscardChanges(new ConfirmHandler() {
                @Override
                public void confirmed(boolean confirm) {
                    if (confirm) {
                        hasDirtyDataLocal.setDirtyData(false);
                        hide();
                    }
                }
            });
        } else {
            this.hide();
        }
    }

    private void makeSelection(CustomerSuggestion selectedCustomer, SuggestBox suggestBox) {
        feedBack.setText("");
        feedBack.setVisible(false);
        this.selectedCustomer = selectedCustomer;
        suggestBox.setText(selectedCustomer.getCustomerName());
        btnAssignCustomer.setEnabled(true);
        if (!clientFactory.isEnableMultiUp() && !selectedCustomer.isUnassigned()) {
            btnAssignCustomer.setEnabled(false);
            feedBack.setText(MessagesUtil.getInstance().getMessage("customer.assigned"));
            feedBack.setType(Message.MESSAGE_TYPE_ERROR);
            feedBack.setVisible(true);
        }
    }

    @UiHandler("btnAssignCustomer")
    void handleAssignCustomer(ClickEvent event) {
        if (selectedCustomer != null) {
            final Long customerId = Long.valueOf(selectedCustomer.getCustomerId());
            SessionCheckCallback sessionCheckCallback = new SessionCheckCallback() {
                @Override
                public void callback(SessionCheckResolution resolution) {
                    clientFactory.getSearchRpc().getCustomerAgreementDatabyCustId(customerId, new ClientCallback<CustomerAgreementData>() {
                        @Override
                        public void onSuccess(CustomerAgreementData result) {
                            // Check if there is another open tab for the same customer, with related details (UP, Meter)
                            UsagePointData tempUPData = new UsagePointData();
                            tempUPData.setCustomerAgreementId(result.getId());
                            tempUPData.setId(usagePointData.getId());
                            tempUPData.setMeterId(usagePointData.getMeterId());
                            if(usagePointWorkspaceView.checkDuplicateDataOnOtherTabs(tempUPData)){
                                return;
                            }
                            assignCustomer(customerId);
                        }
                    });
                }
            };
            clientFactory.handleSessionCheckCallback(sessionCheckCallback);
        } else {
            Dialogs.displayErrorMessage(MessagesUtil.getInstance().getMessage("error.no.selection"),
                    MediaResourceUtil.getInstance().getErrorIcon(),
                    btnAssignCustomer.getAbsoluteLeft(), btnAssignCustomer.getAbsoluteTop() + btnAssignCustomer.getOffsetHeight(),
                    null);
        }
    }

    private int usagePointUpdateEventType(){
        if (!isFetch) {
            logger.info("Assign Customer option was clicked");
            return UsagePointUpdatedEvent.ASSIGN_CUSTOMER;
        }
        logger.info("Fetch Customer option clicked");
        return UsagePointUpdatedEvent.FETCH_CUSTOMER;
    }

    private void assignCustomer(Long customerId) {
        final int updateType = usagePointUpdateEventType();
        UsagePointUpdatedEvent event = new UsagePointUpdatedEvent(usagePointWorkspaceView, usagePointData, updateType);
        event.setAssignToCustomerId(customerId);
        event.setLeft(this.getAbsoluteLeft() + (this.getOffsetWidth() / 2));
        event.setTop(this.getAbsoluteTop());
        clientFactory.getEventBus().fireEvent(event);
        this.hide();
    }

    private class ScrollableDefaultSuggestionDisplay extends DefaultSuggestionDisplay {
        @Override
        protected void moveSelectionDown() {
            super.moveSelectionDown();
            scrollSelectedItemIntoView();
        }

        @Override
        protected void moveSelectionUp() {
            super.moveSelectionUp();
            scrollSelectedItemIntoView();
        }

        private void scrollSelectedItemIntoView() {
            getSelectedMenuItem().getElement().scrollIntoView();
        }

        private native MenuItem getSelectedMenuItem() /*-{
            var menu = <EMAIL>::suggestionMenu;
            return <EMAIL>::selectedItem;
        }-*/;
    }
}
