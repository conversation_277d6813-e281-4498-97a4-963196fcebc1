package za.co.ipay.metermng.client.factory;

import java.util.Date;
import java.util.List;

import com.google.gwt.maps.client.base.LatLng;
import com.google.gwt.place.shared.PlaceController;
import com.google.gwt.place.shared.PlaceHistoryMapper;
import com.google.web.bindery.event.shared.EventBus;
import za.co.ipay.gwt.common.client.rpc.PingRpcAsync;
import za.co.ipay.gwt.common.client.workspace.SessionCheckCallback;
import za.co.ipay.gwt.common.client.workspace.WorkspaceContainer;
import za.co.ipay.metermng.client.MeterMngAdmin.LoadedHandler;
import za.co.ipay.metermng.client.event.ScheduledEventDispatcher;
import za.co.ipay.metermng.client.rpc.AppSettingRpcAsync;
import za.co.ipay.metermng.client.rpc.AuxAccountsRpcAsync;
import za.co.ipay.metermng.client.rpc.AuxChargeScheduleRpcAsync;
import za.co.ipay.metermng.client.rpc.AuxTypeRpcAsync;
import za.co.ipay.metermng.client.rpc.BillingDetRpcAsync;
import za.co.ipay.metermng.client.rpc.BlockingTypeRpcAsync;
import za.co.ipay.metermng.client.rpc.CalendarRpcAsync;
import za.co.ipay.metermng.client.rpc.CustomerRpcAsync;
import za.co.ipay.metermng.client.rpc.DashBoardRpcAsync;
import za.co.ipay.metermng.client.rpc.DeviceStoreRpcAsync;
import za.co.ipay.metermng.client.rpc.GroupRpcAsync;
import za.co.ipay.metermng.client.rpc.ImportFileDataRpcAsync;
import za.co.ipay.metermng.client.rpc.KeyIndicatorRpcAsync;
import za.co.ipay.metermng.client.rpc.LocationRpcAsync;
import za.co.ipay.metermng.client.rpc.LookupRpcAsync;
import za.co.ipay.metermng.client.rpc.ManufacturerRpcAsync;
import za.co.ipay.metermng.client.rpc.MdcChannelRpcAsync;
import za.co.ipay.metermng.client.rpc.MdcRpcAsync;
import za.co.ipay.metermng.client.rpc.MeterMngAppRpcAsync;
import za.co.ipay.metermng.client.rpc.MeterModelRpcAsync;
import za.co.ipay.metermng.client.rpc.MeterRpcAsync;
import za.co.ipay.metermng.client.rpc.NdpRpcAsync;
import za.co.ipay.metermng.client.rpc.NotificationRpcAsync;
import za.co.ipay.metermng.client.rpc.PricingStructureRpcAsync;
import za.co.ipay.metermng.client.rpc.SalesPerResourceRpcAsync;
import za.co.ipay.metermng.client.rpc.ScheduleRpcAsync;
import za.co.ipay.metermng.client.rpc.SearchRpcAsync;
import za.co.ipay.metermng.client.rpc.SpecialActionsRpcAsync;
import za.co.ipay.metermng.client.rpc.SupplyGroupRpcAsync;
import za.co.ipay.metermng.client.rpc.TokenGenerationRpcAsync;
import za.co.ipay.metermng.client.rpc.UsagePointGroupsRpcAsync;
import za.co.ipay.metermng.client.rpc.UsagePointRpcAsync;
import za.co.ipay.metermng.client.rpc.UserInterfaceRpcAsync;
import za.co.ipay.metermng.client.rpc.UserRpcAsync;
import za.co.ipay.metermng.client.view.PrimaryLayoutView;
import za.co.ipay.metermng.client.view.component.SearchWidget;
import za.co.ipay.metermng.client.view.menu.CustomerMenuView;
import za.co.ipay.metermng.client.view.menu.CustomerSearchView;
import za.co.ipay.metermng.client.view.menu.GroupsMenuView;
import za.co.ipay.metermng.client.view.menu.MeterMenuView;
import za.co.ipay.metermng.client.view.menu.MeterSearchView;
import za.co.ipay.metermng.client.view.menu.PricingStructureMenuView;
import za.co.ipay.metermng.client.view.menu.SearchMenuView;
import za.co.ipay.metermng.shared.CustomerAccountSuggestionsOracle;
import za.co.ipay.metermng.shared.CustomerAgreementSuggestionsOracle;
import za.co.ipay.metermng.shared.CustomerSuggestionsOracle;
import za.co.ipay.metermng.shared.DayProfileSuggestionsOracle;
import za.co.ipay.metermng.shared.PeriodSuggestionsOracle;
import za.co.ipay.metermng.shared.SeasonSuggestionsOracle;
import za.co.ipay.metermng.shared.dto.user.MeterMngUser;

public interface ClientFactory {

    // the eventbus and placecontrollers
    EventBus getEventBus();
    ScheduledEventDispatcher getScheduledEventDispatcher();
    PlaceController getPlaceController();
    PlaceHistoryMapper getPlaceHistoryMapper();

    // the views
    void createViews();
    PrimaryLayoutView getPrimaryLayoutView();
    WorkspaceContainer getWorkspaceContainer();
    SearchMenuView getSearchMenuView();
    MeterMenuView getMeterMenuView();
    MeterSearchView getMeterSearchView();
    CustomerMenuView getCustomerMenuView();
    CustomerSearchView getCustomerSearchView();
    CustomerSuggestionsOracle getCustomerSuggestionsOracle();
    CustomerAgreementSuggestionsOracle getCustomerAgreementSuggestionsOracle();
    CustomerAccountSuggestionsOracle getCustomerAccountSuggestionsOracle();
    SearchWidget getSearchWidget();
    GroupsMenuView getGroupsMenuView();
    PricingStructureMenuView getPricingStructureMenuView();

    //the request's locale
    void setLocaleName(String localeName);
    String getLocaleName();
    //app's config and i18n messages
    void loadConfigAndMessages(LoadedHandler loadedHandler);

    // Services
    MeterMngAppRpcAsync getMeterMngAppRpc();
    SearchRpcAsync getSearchRpc();
    LookupRpcAsync getLookupRpc();
    UsagePointRpcAsync getUsagePointRpc();
    CustomerRpcAsync getCustomerRpc();
    MeterRpcAsync getMeterRpc();

    KeyIndicatorRpcAsync getKeyIndicatorRpc();
    UsagePointGroupsRpcAsync getUsagePointGroupsRpc();
    SalesPerResourceRpcAsync getSalesPerResourceRpc();
    DashBoardRpcAsync getDashBoardRpc();
    PricingStructureRpcAsync getPricingStructureRpc();

    CalendarRpcAsync getCalendarRpc();
    TokenGenerationRpcAsync getTokenGeneratorRpc();
    AuxChargeScheduleRpcAsync getAuxChargeStructureRpc();
    AuxTypeRpcAsync getAuxTypeRpc();
    SupplyGroupRpcAsync getSupplyGroupRpc();
    AuxAccountsRpcAsync getAuxAccountsRpc();
    GroupRpcAsync getGroupRpc();
    DeviceStoreRpcAsync getDeviceStoreRpc();
    UserRpcAsync getUserRpc();
    ManufacturerRpcAsync getManufacturerRpc();
    MdcRpcAsync getMdcRpc();
    MeterModelRpcAsync getMeterModelRpc();
    ScheduleRpcAsync getScheduleRpc();
    LocationRpcAsync getLocationRpc();
    AppSettingRpcAsync getAppSettingRpc();
    NdpRpcAsync getNdpRpc();
    BillingDetRpcAsync getBillingDetRpc();
    MdcChannelRpcAsync getMdcChannelRpc();
    SpecialActionsRpcAsync getSpecialActionsRpc();
    NotificationRpcAsync getNotificationRpcAsync();
    BlockingTypeRpcAsync getBlockingTypeRpc();
    ImportFileDataRpcAsync getImportFileDataRpc();
    UserInterfaceRpcAsync getUserInterfaceRpcAsync();
    PingRpcAsync getPingRpc();

    SeasonSuggestionsOracle getSeasonSuggestionsOracle();
    PeriodSuggestionsOracle getPeriodSuggestionsOracle();
    DayProfileSuggestionsOracle getDayProfileSuggestionsOracle();

    void setUser(MeterMngUser user);
    void displayUserGroup(String groupName);
    MeterMngUser getUser();            //RC: Watchit! THis is not kept up to date when current userGroup changes!!
    boolean isDemoMode();
    void setDemoMode(boolean demoMode);
    boolean isEnableSTS();
    void setEnableSTS(boolean enableSTS);
    boolean isEnableCentianSTS();
    void setEnableCentianSTS(boolean enableCentianSTS);
    boolean isUseMancoLogo();
    void setUseMancoLogo(boolean useMancoLogo);
    String getLogoUrl();
    void setLogoUrl(String logoUrl);
    boolean isEnableMultiUp();
    void setEnableMultiUp(boolean enableMultiUp);
    boolean isEnableMultiCustAgr();
    void setEnableMultiCustAgr(boolean enableMultiCustAgr);
    boolean isEnableNonBillable();
    void setEnableNonBillable(boolean enableNonBillable);
    boolean isEnableAccessGroups();
    void setEnableAccessGroups(boolean enableAccessGroups);

    boolean isGroupGlobalUser();
    boolean isGroupGroupUser();
    String getGlobalGroupName();
    boolean hasAccessGroupsEditPermissions(Long objectGroupId);

    boolean isAllowReversalsLastTrans();
    void setAllowReversalsLastTrans(boolean allowReversalsLastTrans);
    boolean isAllowReversalsOlderTrans();
    void setAllowReversalsOlderTrans(boolean allowReversalsOlderTrans);

    boolean isIEBrowser();

    void setGoogleMapsReady(boolean isGoogleMapsReady);
    boolean isGoogleMapsReady();

    void setStoreLocations(String storeLocations);
    List<LatLng> getStoreLocations();

    void updateAppSettingsChangedDate();

    boolean hasAppSettingsChanged(Date date);

    int getGroupsTreeDisplaySize();
    void setGroupsTreeDisplaySize(int size);
    int getGroupsSuggestBoxDisplaySize();
    void setGroupsSuggestBoxDisplaySize(int size);

    void handleSessionCheckCallback(final SessionCheckCallback sessionCheckCallback);
}
