package za.co.ipay.metermng.client.view.workspace.globalndp;

import com.google.gwt.core.client.GWT;
import com.google.gwt.event.dom.client.ClickEvent;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.uibinder.client.UiHandler;
import com.google.gwt.user.client.ui.Button;
import com.google.gwt.user.client.ui.CheckBox;
import com.google.gwt.user.client.ui.Widget;

import za.co.ipay.gwt.common.client.Dialogs;
import za.co.ipay.gwt.common.client.form.HasDirtyData;
import za.co.ipay.gwt.common.client.form.HasDirtyDataManager;
import za.co.ipay.gwt.common.client.handler.ConfirmHandler;
import za.co.ipay.gwt.common.client.handler.FormDataClickHandler;
import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.gwt.common.client.workspace.SessionCheckCallback;
import za.co.ipay.gwt.common.client.workspace.Workspace;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.resource.image.MediaResource.MediaResourceUtil;
import za.co.ipay.metermng.client.rpc.callback.ClientCallback;
import za.co.ipay.metermng.client.view.component.BaseComponent;
import za.co.ipay.metermng.client.view.component.group.entity.ndp.ContainsNdpPanel;
import za.co.ipay.metermng.client.view.component.group.entity.ndp.SchedulePanel;
import za.co.ipay.metermng.datatypes.RecordStatus;
import za.co.ipay.metermng.mybatis.generated.model.NdpSchedule;
import za.co.ipay.metermng.shared.MeterMngStatics;
import za.co.ipay.metermng.shared.NdpScheduleData;

public class GlobalNdpPanel extends BaseComponent implements ContainsNdpPanel {

    @UiField CheckBox scheduleActiveBox;
    @UiField Button btnSave;
    @UiField Button btnCancel;

    @UiField(provided=true) SchedulePanel schedulePanel;

    private NdpScheduleData ndpScheduleData;
    private HasDirtyData hasDirtyData;
    private Workspace workspace;

    private static GlobalNdpPanelUiBinder uiBinder = GWT.create(GlobalNdpPanelUiBinder.class);

    interface GlobalNdpPanelUiBinder extends UiBinder<Widget, GlobalNdpPanel> {
    }

    public GlobalNdpPanel(ClientFactory clientFactory, Workspace workspace) {
        this.clientFactory = clientFactory;
        this.workspace = workspace;
        schedulePanel = new SchedulePanel(clientFactory, this, null);

        initWidget(uiBinder.createAndBindUi(this));
        scheduleActiveBox.addClickHandler(new FormDataClickHandler(hasDirtyData));
        hasDirtyData = workspace.createAndRegisterHasDirtyData();
        scheduleActiveBox.addClickHandler(new FormDataClickHandler(hasDirtyData));
        getGlobalScheduleData();
    }

    private void getGlobalScheduleData() {
        clientFactory.getNdpRpc().getGlobalNdpScheduleData(new ClientCallback<NdpScheduleData>()  {
            @Override
            public void onSuccess(NdpScheduleData result) {
                ndpScheduleData = result;
                if (result == null) {
                    schedulePanel.setVisible(false);
                    btnSave.setText(MessagesUtil.getInstance().getMessage("button.create"));
                    disableActivate();
                    Dialogs.displayInformationMessage(MessagesUtil.getInstance().getMessage("global.ndp.none"),
                            MediaResourceUtil.getInstance().getInformationIcon(),
                            btnSave.getAbsoluteLeft() + btnSave.getOffsetWidth(),
                            btnSave.getAbsoluteTop() - btnSave.getOffsetHeight(),
                            null);
                    return;
                }
                display(ndpScheduleData);
                if (!clientFactory.getUser().hasPermission(MeterMngStatics.GLOBAL_NDP_PERMISSION)) {
                    //then has clientFactory.getUser().hasPermission(MeterMngStatics.VIEW_ONLY_MM_NDP))
                    scheduleActiveBox.setEnabled(false);
                    btnSave.removeFromParent();
                    btnCancel.removeFromParent();
                }
            }
        });
    }


    @UiHandler("btnSave")
    public void save(ClickEvent e) {
        SessionCheckCallback sessionCheckCallback = new SessionCheckCallback() {
            @Override
            public void callback(SessionCheckResolution resolution) {
                if (ndpScheduleData == null) {
                    add();
                } else {
                    save();
                }
            }
        };
        clientFactory.handleSessionCheckCallback(sessionCheckCallback);
    }

    @UiHandler("btnCancel")
    public void cancel(ClickEvent e) {
        hasDirtyData.checkDirtyData(new ConfirmHandler() {
            @Override
            public void confirmed(boolean confirm) {
                if (confirm) {
                    hasDirtyData.setDirtyData(false);
                    if (ndpScheduleData == null) {
                        scheduleActiveBox.setEnabled(false);
                    } else {
                        displayActive(ndpScheduleData);
                    }
                }
            }
        });
    }

    protected void display(NdpScheduleData ndpScheduleData) {
        this.ndpScheduleData = ndpScheduleData;

        displayActive(ndpScheduleData);

        schedulePanel.display(ndpScheduleData);
    }

    private void displayActive(NdpScheduleData ndpScheduleData) {
        boolean active = ndpScheduleData.getNdpSchedule().getRecordStatus().equals(RecordStatus.ACT);
        scheduleActiveBox.setValue(active);
        scheduleActiveBox.setEnabled(true);
        if (!active) {
            if (!ndpScheduleData.isNdpTimeEntered()) {   //i.e. have not entered at least one NDP time yet
                scheduleActiveBox.setEnabled(false);
            }
        }
    }

    @Override
    public boolean isActiveBoxTicked() {
        return scheduleActiveBox.getValue().equals(Boolean.TRUE);
    }

    @Override
    public void enableActivate() {
        scheduleActiveBox.setEnabled(true);
    }

    @Override
    public void disableActivate() {
        scheduleActiveBox.setValue(false);
        scheduleActiveBox.setEnabled(false);
    }

    @Override
    public void deactivateSchedule() {
        scheduleActiveBox.setValue(false);
        scheduleActiveBox.setEnabled(false);
        ndpScheduleData.getNdpSchedule().setRecordStatus(RecordStatus.DAC);
        //save schedule
        SessionCheckCallback sessionCheckCallback = new SessionCheckCallback() {
            @Override
            public void callback(SessionCheckResolution resolution) {
                clientFactory.getNdpRpc().saveNdpSchedule(ndpScheduleData.getNdpSchedule(), new ClientCallback<NdpSchedule>() {
                    @Override
                    public void onSuccess(NdpSchedule result) {
                        Dialogs.displayInformationMessage(MessagesUtil.getInstance().getMessage("global.ndp.schedule.activation.saved"),
                                MediaResourceUtil.getInstance().getInformationIcon(),
                                btnSave.getAbsoluteLeft() + btnSave.getOffsetWidth(),
                                btnSave.getAbsoluteTop() - btnSave.getOffsetHeight(),
                                null);
                    }
                });
            }
        };
        clientFactory.handleSessionCheckCallback(sessionCheckCallback);
    }


    private void add() {
        //create Schedule
        btnSave.setEnabled(false);
        clientFactory.getNdpRpc().addNdpSchedule(Boolean.TRUE,
                new ClientCallback<NdpSchedule>(btnSave.getAbsoluteLeft(), btnSave.getAbsoluteTop()) {
             @Override
             public void onSuccess(NdpSchedule result) {
                Dialogs.displayInformationMessage(
                        MessagesUtil.getInstance().getMessage("global.ndp.schedule.new.added"),
                        MediaResourceUtil.getInstance().getInformationIcon(),
                        btnSave.getAbsoluteLeft(), btnSave.getAbsoluteTop(),
                        MessagesUtil.getInstance().getMessage("button.close"));

                btnSave.setText(MessagesUtil.getInstance().getMessage("button.update"));
                btnSave.setEnabled(true);

                NdpScheduleData addScheduleData = new NdpScheduleData(result);

                schedulePanel.setVisible(true);
                display(addScheduleData);
            }

            @Override
            public void onFailureClient() {
                btnSave.setEnabled(true);
            }
        });
    }

    private void save() {
        //disable buttons
        btnSave.setEnabled(false);
        schedulePanel.disableButtons();

        if (hasDirtyData.isDirtyData()) {
            if (scheduleActiveBox.getValue()) {
                ndpScheduleData.getNdpSchedule().setRecordStatus(RecordStatus.ACT);
            } else {
                ndpScheduleData.getNdpSchedule().setRecordStatus(RecordStatus.DAC);
            }
            //save schedule
            clientFactory.getNdpRpc().saveNdpSchedule(ndpScheduleData.getNdpSchedule(), new ClientCallback<NdpSchedule>() {
                @Override
                public void onSuccess(NdpSchedule result) {
                    hasDirtyData.setDirtyData(false);
                    Dialogs.displayInformationMessage(MessagesUtil.getInstance().getMessage("global.ndp.schedule.activation.saved"),
                                                      MediaResourceUtil.getInstance().getInformationIcon(),
                                                      btnSave.getAbsoluteLeft() + btnSave.getOffsetWidth(),
                                                      btnSave.getAbsoluteTop() - btnSave.getOffsetHeight(),
                                                      null);
                    enableButtons();
                    display(ndpScheduleData);
                }

                @Override
                public void onFailureClient() {
                    enableButtons();
                }
            });
        } else {
            Dialogs.displayInformationMessage(MessagesUtil.getInstance().getMessage("ndp.schedule.activation.no.change"),
                                              MediaResourceUtil.getInstance().getInformationIcon(),
                                              btnSave.getAbsoluteLeft() + btnSave.getOffsetWidth(),
                                              btnSave.getAbsoluteTop() - btnSave.getOffsetHeight(),
                                              null);
            enableButtons();
        }
    }


    private void enableButtons() {
        btnSave.setEnabled(true);
        schedulePanel.enableButtons();
    }

    @Override
    public HasDirtyDataManager getHasDirtyDataManager() {
        return workspace;
    }
}
