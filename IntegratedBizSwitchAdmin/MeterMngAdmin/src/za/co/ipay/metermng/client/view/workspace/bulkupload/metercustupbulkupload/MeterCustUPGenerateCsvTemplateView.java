package za.co.ipay.metermng.client.view.workspace.bulkupload.metercustupbulkupload;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.logging.Logger;

import com.google.gwt.event.dom.client.ClickEvent;
import com.google.gwt.event.dom.client.ClickHandler;
import com.google.gwt.user.client.ui.Button;
import com.google.gwt.user.client.ui.CheckBox;

import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.gwt.common.client.workspace.SimpleView;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.view.component.BaseComponent;
import za.co.ipay.metermng.client.view.component.bulkupload.GenerateCsvTemplatePanel;
import za.co.ipay.metermng.shared.MeterMngStatics;
import za.co.ipay.metermng.shared.bulkupload.dto.metercustupuploaddata.MeterCustUPBulkCsvData;
import za.co.ipay.metermng.shared.bulkupload.metercustup.CsvColumnData;
import za.co.ipay.metermng.shared.group.GroupComponentType;

public class MeterCustUPGenerateCsvTemplateView extends BaseComponent {

	private MeterCustUPUploadWorkspaceView parentWorkspace;
	private SimpleView view;
	private GenerateCsvTemplatePanel<MeterCustUPBulkCsvData> generateCsvTemplatePanel;

	private String customFieldsStatusUnavailable;

	private ClientMeterCustUPBulkCsvMapToData clientMeterCustUPBulkCsvMapToData;
    private Map<String, List<String>> groupTypeNameSet = new LinkedHashMap<>();

	private LinkedHashMap<String, Boolean> result = new LinkedHashMap<>();

	private static Logger logger = Logger.getLogger(MeterCustUPUploadWorkspaceView.class.getName());

	public MeterCustUPGenerateCsvTemplateView(MeterCustUPUploadWorkspaceView parentWorkspace,
											  ClientFactory clientFactory, SimpleView view2) {
		this.parentWorkspace = parentWorkspace;
		this.clientFactory = clientFactory;
		this.clientMeterCustUPBulkCsvMapToData = parentWorkspace.getClientMeterCustUPBulkCsvMapToData();
		this.view = view2;
		view.getPageHeader().setHeading(MessagesUtil.getInstance().getMessage("bulk.upload.heading.metercustup"));
		initUi();
	}

	private void initUi() {
		generateCsvTemplatePanel = new GenerateCsvTemplatePanel<MeterCustUPBulkCsvData>(clientFactory, MeterMngStatics.METER_CUST_UP_UPLOAD);
        initView();
	}

	public GenerateCsvTemplatePanel<MeterCustUPBulkCsvData> getGenerateCsvTemplatePanel() {
		return generateCsvTemplatePanel;
	}

	private void initView() {
		addCheckboxes();
		addDownloadTemplateButton();
		this.view.getViewForm().add(generateCsvTemplatePanel.getMainPanel());
	}

	private void addDownloadTemplateButton() {
		String downloadTemplateLabel = MessagesUtil.getInstance().getMessage("bulk.upload.file.button.gentemplate");
		Button downloadTemplateButtion = new Button(downloadTemplateLabel, new ClickHandler() {
			@Override
			public void onClick(ClickEvent event) {
				genDownload(createDownloadStream(), "MetersCustUP-template.csv");
			}

		});

		generateCsvTemplatePanel.getButtons().add(downloadTemplateButtion);
	}

	public static native void genDownload(String csvContent, String fileName) /*-{

        var download = function (content, fileName, mimeType) {
            var a = document.createElement('a');
            mimeType = mimeType || 'application/octet-stream';

            if (navigator.msSaveBlob) { // IE10
                navigator.msSaveBlob(new Blob([content], {
                    type: mimeType
                }), fileName);
            } else if (URL && 'download' in a) { //html5 A[download]
                a.href = URL.createObjectURL(new Blob([content], {
                    type: mimeType
                }));
                a.setAttribute('download', fileName);
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
            } else {
                location.href = 'data:application/octet-stream,' + encodeURIComponent(content); // only this mime type is supported
            }
        }

        download(csvContent, fileName, 'text/csv;encoding:utf-8');
    }-*/;

	private String createDownloadStream() {
		StringBuffer buffer = new StringBuffer();
		StringBuffer bufferLine2 = new StringBuffer();
		
		LinkedHashMap<String,CsvColumnData> dataMap = clientMeterCustUPBulkCsvMapToData.getCsvHeadingToDataNameMap();
		
		// Generate Line 1 & 2 simultaneously
		for (Entry<String, Boolean> entry : result.entrySet()) {
		    if (entry.getValue()) {
		        String groupTypeName = entry.getKey();
		        if (groupTypeNameSet.containsKey(groupTypeName)) {
		            List<String> groupTypeHierarchyNames = groupTypeNameSet.get(groupTypeName);
		            for (String hierarchyName : groupTypeHierarchyNames) {
		                buffer.append(dataMap.get(hierarchyName).getInfoHeading() + ",");
		                bufferLine2.append(hierarchyName + ",");
		            }    

		        } else {
		            buffer.append(dataMap.get(entry.getKey()).getInfoHeading() + ",");
		            bufferLine2.append(entry.getKey() + ",");
		        }
		    }
		}
		
		buffer.replace(buffer.length() - 1, buffer.length(), "\n");
		bufferLine2.replace(bufferLine2.length() - 1, bufferLine2.length(), "");
		
		buffer.append(bufferLine2);
		return buffer.toString();
	}

	public void addCheckboxes() {
		for (Entry<String, CsvColumnData> entry : clientMeterCustUPBulkCsvMapToData.getCsvHeadingToDataNameMap().entrySet()) {
			if (entry.getValue().getGroupComponentType().equals(GroupComponentType.USAGE_POINT_GROUP)) {
			    addToGroupTypeNameSet(entry, "group");
				
			} else if (entry.getValue().getGroupComponentType().equals(GroupComponentType.LOCATION_GROUP)) {
			    addToGroupTypeNameSet(entry, "main");

			} else {
				String key = entry.getKey();
				result.put(key, false);
				CheckBox cb = makeCheckBox(key, entry.getValue().getIsRequired());
		        cb.addClickHandler(new ClickHandler() {
		            @Override
		            public void onClick(ClickEvent event) {
		                boolean checked = ((CheckBox) event.getSource()).getValue();

		                if (checked) {
		                    result.put(((CheckBox) event.getSource()).getName(), Boolean.TRUE);
		                } else {
		                    result.put(((CheckBox) event.getSource()).getName(), Boolean.FALSE);
		                }
		            }
		        });
				        
				generateCsvTemplatePanel.getTemplateForm().add(cb);
			}
		}
	}
	
	private void addToGroupTypeNameSet(Entry<String, CsvColumnData> entry, String formName) {
        String groupTypeName = entry.getValue().getGroupTypeName();
        if (groupTypeNameSet.containsKey(groupTypeName)) {
            groupTypeNameSet.get(groupTypeName).add(entry.getKey());
        } else {
            List<String> groupHierarchies = new ArrayList<>();
            groupHierarchies.add(entry.getKey());
            groupTypeNameSet.put(groupTypeName, groupHierarchies);

            CheckBox cb = makeCheckBox(groupTypeName, entry.getValue().getIsRequired());
            addClickHandlerGroupCheckBox(cb, groupTypeName);
            generateCsvTemplatePanel.getTemplateFormPanel(formName).add(cb);
        }
	}
	
	private CheckBox makeCheckBox(String name, boolean isRequired) {
	    CheckBox cb = new CheckBox(name);
        cb.setName(name);
        if (isRequired) {
            result.put(name, Boolean.TRUE);
            cb.setValue(true);
            cb.setEnabled(false);
        }
        return cb;
	}
	
	private void addClickHandlerGroupCheckBox(CheckBox cb, final String groupTypeName) {
	    cb.addClickHandler(new ClickHandler() {
            @Override
            public void onClick(ClickEvent event) {
                boolean checked = ((CheckBox) event.getSource()).getValue();
                List<String> values = groupTypeNameSet.get(groupTypeName);

                if (checked) {
                    for (String cvsHeadingKey : values) {
                        result.put(cvsHeadingKey, Boolean.TRUE);
                    }
                } else {
                    for (String cvsHeadingKey : values) {
                        result.put(cvsHeadingKey, Boolean.FALSE);
                    }
                }

            }
        });
	}
	
	public void recreateCheckboxes() {
	    generateCsvTemplatePanel.getTemplateForm().clear();
	    result.clear();
	    addCheckboxes();
	}
}
