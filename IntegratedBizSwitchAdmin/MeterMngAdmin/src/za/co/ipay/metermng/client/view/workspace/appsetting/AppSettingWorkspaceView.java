package za.co.ipay.metermng.client.view.workspace.appsetting;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.logging.Logger;

import com.google.gwt.cell.client.FieldUpdater;
import com.google.gwt.cell.client.NumberCell;
import com.google.gwt.core.client.GWT;
import com.google.gwt.event.dom.client.ClickEvent;
import com.google.gwt.event.dom.client.ClickHandler;
import com.google.gwt.i18n.client.NumberFormat;
import com.google.gwt.place.shared.Place;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.user.cellview.client.CellTable;
import com.google.gwt.user.cellview.client.Column;
import com.google.gwt.user.cellview.client.ColumnSortEvent;
import com.google.gwt.user.cellview.client.ColumnSortEvent.AsyncHandler;
import com.google.gwt.user.cellview.client.ColumnSortEvent.ListHandler;
import com.google.gwt.user.cellview.client.ColumnSortList;
import com.google.gwt.user.cellview.client.TextColumn;
import com.google.gwt.user.client.rpc.AsyncCallback;
import com.google.gwt.user.client.ui.Button;
import com.google.gwt.user.client.ui.VerticalPanel;
import com.google.gwt.user.client.ui.Widget;
import com.google.gwt.view.client.AsyncDataProvider;
import com.google.gwt.view.client.HasData;
import com.google.gwt.view.client.ListDataProvider;
import com.google.gwt.view.client.Range;
import com.google.gwt.view.client.SelectionChangeEvent;
import com.google.gwt.view.client.SingleSelectionModel;
import za.co.ipay.gwt.common.client.Dialogs;
import za.co.ipay.gwt.common.client.factory.ResourcesFactoryUtil;
import za.co.ipay.gwt.common.client.form.FormManager;
import za.co.ipay.gwt.common.client.form.SimpleForm;
import za.co.ipay.gwt.common.client.handler.ConfirmHandler;
import za.co.ipay.gwt.common.client.resource.Messages;
import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.gwt.common.client.validation.ClientValidatorUtil;
import za.co.ipay.gwt.common.client.widgets.TablePager;
import za.co.ipay.gwt.common.client.workspace.SessionCheckCallback;
import za.co.ipay.gwt.common.client.workspace.SimpleTableView;
import za.co.ipay.gwt.common.client.workspace.WorkspaceNotification;
import za.co.ipay.gwt.common.client.workspace.WorkspaceNotification.NotificationType;
import za.co.ipay.metermng.client.event.PricingStructureUpdateEvent;
import za.co.ipay.metermng.client.event.AppSettingEvent;
import za.co.ipay.metermng.client.event.PricingStructureUpdateEvent;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.history.AppSettingPlace;
import za.co.ipay.metermng.client.resource.image.MediaResource.MediaResourceUtil;
import za.co.ipay.metermng.client.rpc.callback.ClientCallback;
import za.co.ipay.metermng.client.view.component.appsetting.PowerLimitDialogueBox;
import za.co.ipay.metermng.client.view.workspace.BaseWorkspace;
import za.co.ipay.metermng.mybatis.generated.model.AppSetting;
import za.co.ipay.metermng.mybatis.generated.model.GroupType;
import za.co.ipay.metermng.shared.MeterMngStatics;
import za.co.ipay.metermng.shared.appsettings.AppSettings;
import za.co.ipay.metermng.shared.appsettings.PowerLimitValue;
import za.co.ipay.metermng.shared.dto.FilterCriteria;
import za.co.ipay.metermng.shared.util.MeterMngSharedUtils;
import za.co.ipay.metermng.shared.utils.MeterMngCommonUtil;

import static za.co.ipay.metermng.shared.MeterMngStatics.ADMIN_PERMISSION_APP_SETTING_VEND_UNITS;
import static za.co.ipay.metermng.shared.MeterMngStatics.VEND_UNITS_AMOUNT_PATTERN;

public class AppSettingWorkspaceView extends BaseWorkspace implements FormManager<AppSetting> {

    @UiField SimpleTableView<AppSetting> view;
    private AppSettingPanel panel;
    private AsyncDataProvider<AppSetting> dataProvider;
    private AppSetting selectedAppSetting;
    private CellTable<PowerLimitValue> powerLimitTable;
    private ListDataProvider<PowerLimitValue> powerLimitDataProvider;
    private int powerVoltage;
    private boolean locationGroupIsRequired = false;
    private AppSettingFilterPanel filterPanel;

    private static Logger logger = Logger.getLogger(AppSettingWorkspaceView.class.getName());

    private static AppSettingWorkspaceViewUiBinder uiBinder = GWT.create(AppSettingWorkspaceViewUiBinder.class);

    interface AppSettingWorkspaceViewUiBinder extends UiBinder<Widget, AppSettingWorkspaceView> {
    }

    public AppSettingWorkspaceView(ClientFactory clientFactory, AppSettingPlace place) {
        this.clientFactory = clientFactory;
        initWidget(uiBinder.createAndBindUi(this));
        setPlaceString(AppSettingPlace.getPlaceAsString(place));
        setHeaderText(MessagesUtil.getInstance().getMessage("appsetting.header"));
        clientFactory.getAppSettingRpc().getAppSettingByKey("powerlimit.voltage", new ClientCallback<AppSetting>() {
            @Override
            public void onSuccess(AppSetting result) {
                powerVoltage = Integer.parseInt(result.getValue());
            }
        });
        initUi();
    }

    private void initUi() {
        initView();
        initForm();
        createTable();
        initPowerLimitsTable();
    }

    private void initView() {
        view.setFormManager(this);
    }

    private void initForm() {
        panel = new AppSettingPanel(clientFactory, view.getForm());
        view.getForm().ensureDebugId("appSettingPanel");

        filterPanel = new AppSettingFilterPanel(clientFactory, this);
        view.getFiltersPanel().add(filterPanel);

        view.getForm().setHasDirtyDataManager(this);
        view.getForm().getFormFields().add(panel);
        view.getTable().ensureDebugId("appSettingTable");
        view.getForm().getSaveBtn().ensureDebugId("updateButton");
        view.getForm().getOtherBtn().ensureDebugId("cancelButton");

        view.getForm().getSaveBtn().setText(MessagesUtil.getInstance().getMessage("button.update"));
        view.getForm().getSaveBtn().addClickHandler(new ClickHandler() {
            @Override
            public void onClick(ClickEvent event) {
                onSave();
            }
        });

        view.getForm().getOtherBtn().setText(MessagesUtil.getInstance().getMessage("button.cancel"));
        view.getForm().getOtherBtn().addClickHandler(new ClickHandler() {
            @Override
            public void onClick(ClickEvent event) {
                view.getForm().checkDirtyData(new ConfirmHandler() {
                    @Override
                    public void confirmed(boolean confirm) {
                        if (confirm) {
                            view.getForm().setDirtyData(false);
                            displaySelected(null);
                        }
                    }
                });
            }
        });

        view.getForm().getFormPanel().setHeadingText(MessagesUtil.getInstance().getMessage("appsetting.title.update"));
        view.getForm().setVisible(false);
    }

    private void createTable() {
        if (dataProvider == null) {
            Column<AppSetting, ?> nameColumn = createNameColumn();
            Column<AppSetting, ?> valueColumn = createValueColumn();
            Column<AppSetting, ?> descriptionColumn = createDescriptionColumn();
            view.getTable().addColumn(nameColumn, MessagesUtil.getInstance().getMessage("appsetting.field.name"));
            view.getTable().addColumn(valueColumn, MessagesUtil.getInstance().getMessage("appsetting.field.value"));
            view.getTable().addColumn(descriptionColumn, MessagesUtil.getInstance().getMessage("appsetting.field.description"));

            // Set the range to display
            view.getTable().setVisibleRange(0, getPageSize());

            // Set the data provider for the table
            dataProvider = new AsyncDataProvider<AppSetting>() {
                @Override
                protected void onRangeChanged(HasData<AppSetting> display) {
                    final int start = display.getVisibleRange().getStart();
                    String sortColumn = null;
                    boolean isAscending = true;
                    ColumnSortList sortList = view.getTable().getColumnSortList();
                    if (sortList != null && sortList.size() != 0) {
                        @SuppressWarnings("unchecked")
                        Column<AppSetting, ?> sColumn = (Column<AppSetting, ?>) sortList.get(0).getColumn();
                        Integer columnIndex = view.getTable().getColumnIndex(sColumn);
                        sortColumn = getColumnName(columnIndex);
                        isAscending = sortList.get(0).isAscending();
                    }
                    FilterCriteria filterCriteria = FilterCriteria.createDefaultCriteria()
                            .setStartRow(start)
                            .setPageSize(getPageSize())
                            .setSortAscending(isAscending)
                            .setSortField(sortColumn);
                    restrictVendUnitAmountPermission(filterCriteria);
                    clientFactory.getAppSettingRpc().getAppSettings(filterCriteria, filterPanel.txtSearchText.getText(),
                            new ClientCallback<ArrayList<AppSetting>>() {
                                @Override
                                public void onSuccess(ArrayList<AppSetting> result) {
                                    dataProvider.updateRowData(start, result);
                                }
                            });
                }
            };
            dataProvider.addDataDisplay(view.getTable());

            // Create the table's pager
            view.getPager().setDisplay(view.getTable());
            view.getPager().ensureDebugId("pager");

            // Set the table's column sorter handler
            AsyncHandler columnSortHandler = new AsyncHandler(view.getTable()) {
                @Override
                public void onColumnSort(ColumnSortEvent event) {
                    final int start = view.getTable().getVisibleRange().getStart();
                    @SuppressWarnings("unchecked")
                    int sortIndex = view.getTable().getColumnIndex((Column<AppSetting, ?>) event.getColumn());
                    FilterCriteria filterCriteria = FilterCriteria.createDefaultCriteria()
                            .setStartRow(start)
                            .setPageSize(getPageSize())
                            .setSortAscending(event.isSortAscending())
                            .setSortField(getColumnName(sortIndex));
                    restrictVendUnitAmountPermission(filterCriteria);
                    clientFactory.getAppSettingRpc().getAppSettings(filterCriteria, filterPanel.txtSearchText.getText(),
                            new ClientCallback<ArrayList<AppSetting>>() {
                                public void onSuccess(ArrayList<AppSetting> result) {
                                    dataProvider.updateRowData(start, result);
                                }
                            });
                }
            };
            view.getTable().addColumnSortHandler(columnSortHandler);
            view.getTable().getColumnSortList().push(nameColumn);
            view.getTable().getColumnSortList().push(valueColumn);
            view.getTable().getColumnSortList().push(descriptionColumn);
        }
    }

    private String getColumnName(int index) {
        return "name";
    }

    private Column<AppSetting, ?> createNameColumn() {
        TextColumn<AppSetting> nameColumn = new TextColumn<AppSetting>() {
            @Override
            public String getValue(AppSetting obj) {
                return obj.getName();
            }
        };
        nameColumn.setSortable(true);
        return nameColumn;
    }

    private Column<AppSetting, ?> createValueColumn() {
        return new TextColumn<AppSetting>() {
            @Override
            public String getValue(AppSetting obj) {
                return obj.getValue();
            }
        };
    }

    private Column<AppSetting, ?> createDescriptionColumn() {
        return new TextColumn<AppSetting>() {
            @Override
            public String getValue(AppSetting obj) {
                return obj.getDescription();
            }
        };
    }


    @Override
    public void onArrival(Place place) {
        getAppSettingCount(false);
    }

    void getAppSettingCount(final boolean refreshTable) {
        FilterCriteria filterCriteria = FilterCriteria.createDefaultCriteria();
        restrictVendUnitAmountPermission(filterCriteria);
        final CellTable<AppSetting> table = view.getTable();
        table.setPageStart(0);
        clientFactory.getAppSettingRpc().getAppSettingCount(filterCriteria, filterPanel.txtSearchText.getText(),
                new ClientCallback<Integer>() {
                    @Override
                    public void onFailureClient() {
                        table.setRowCount(0, true);
                        dataProvider.updateRowCount(0, true);
                    }

                    @Override
                    public void onSuccess(Integer result) {
                        table.setRowCount(result, true);
                        dataProvider.updateRowCount(result, true);
                        // Force a table update - reloads data from the back end
                        if (refreshTable) {
                            refreshTable(false);
                        }
                    }
                });
    }

    private void restrictVendUnitAmountPermission(FilterCriteria filterCriteria) {
        if (filterCriteria != null) {
            if (!clientFactory.getUser().hasPermission(ADMIN_PERMISSION_APP_SETTING_VEND_UNITS)) {
                String columnNameId = "key";
                FilterCriteria.Value value = new FilterCriteria.Value();
                value.setValueType(FilterCriteria.Type.NOT_LIKE);
                value.setValue(VEND_UNITS_AMOUNT_PATTERN);
                FilterCriteria.Pair<String, FilterCriteria.Value> pair = new FilterCriteria.Pair<>(columnNameId, value);
                filterCriteria.setColumnValuesFilter(pair);
            }
        }
    }

    @Override
    public void onLeaving() {

    }

    @Override
    public void onSelect() {

    }

    @Override
    public void onClose() {

    }

    @Override
    public boolean handles(Place place) {
        return (place instanceof AppSettingPlace);
    }

    @Override
    public void displaySelected(AppSetting selected) {
        displayAppSetting(selected);
    }

    private void displayAppSetting(AppSetting selected) {
        clear();
        this.selectedAppSetting = selected;
        if (selectedAppSetting == null) {
            selectedAppSetting = new AppSetting();
            view.getForm().setVisible(false);
            view.clearTableSelection();
        } else {
            view.getForm().setVisible(true);
        }

        panel.nameTextBox.setText(selectedAppSetting.getName());
        panel.descriptionTextArea.setText(selectedAppSetting.getDescription());

        String appSettingKey = selectedAppSetting.getKey();
        panel.dataTypeViewListBtnElement.setVisible(false);
        panel.valueElement.setRequired(true);
        if (appSettingKey != null && !appSettingKey.isEmpty())  {
            if (appSettingKey.toLowerCase().contains(".status")) {
                panel.valueElement.setVisible(true);
                panel.valueTextBox.setVisible(false);
                panel.statusListBox.setVisible(true);
                panel.statusListBox.selectItemByValue(selectedAppSetting.getValue());

                panel.powerLimitTableElement.setVisible(false);
                panel.dataTypeElement.setVisible(false);
                panel.groupTypeElement.setVisible(false);
                panel.setDataTypeList("");

            } else if (appSettingKey.equalsIgnoreCase(AppSettings.POWER_LIMIT_SETTINGS)) {
                panel.valueElement.setVisible(false);
                panel.powerLimitTableElement.setVisible(true);

                List<PowerLimitValue> powerLimits = PowerLimitValue.fromAppSetting(selectedAppSetting);
                List<PowerLimitValue> list = powerLimitDataProvider.getList();
                list.clear();
                list.addAll(powerLimits);

            } else if (appSettingKey.toLowerCase().contains(".datatype")) {
                    panel.valueElement.setVisible(false);
                    panel.powerLimitTableElement.setVisible(false);
                    panel.dataTypeViewListBtnElement.setVisible(false);
                    panel.groupTypeElement.setVisible(false);
                    panel.dataTypeElement.setVisible(true);
                panel.dataTypeListBox.selectItemByValue(selectedAppSetting.getValue());

                if (MessagesUtil.getInstance().getMessage("user.custom.field.datatype.list").equals(selectedAppSetting.getValue())) {
                        panel.descriptionTextArea.setEnabled(false);
                    panel.setDataTypeList(selectedAppSetting.getDescription());
                        panel.dataTypeViewListBtnElement.setVisible(true);
                    } else {
                        panel.descriptionTextArea.setEnabled(true);
                    }
            } else if (appSettingKey.toLowerCase().contains(".grouptype")) {
                panel.valueElement.setVisible(false);
                panel.powerLimitTableElement.setVisible(false);
                panel.dataTypeViewListBtnElement.setVisible(false);
                panel.groupTypeElement.setVisible(true);
                panel.groupTypeListBox.selectItemByValue(selectedAppSetting.getValue());
            } else if (appSettingKey.toLowerCase().contains(".label")) {
                panel.valueElement.setRequired(true);
                labelSetting();
            } else {
                labelSetting();
            }
        } else {
            labelSetting();
        }
    }

    private void labelSetting() {
        panel.valueElement.setVisible(true);
        panel.valueTextBox.setVisible(true);
        panel.statusListBox.setVisible(false);
        panel.valueTextBox.setValue(selectedAppSetting.getValue());
        panel.powerLimitTableElement.setVisible(false);
        panel.dataTypeElement.setVisible(false);
        panel.groupTypeElement.setVisible(false);
    }

    private void initPowerLimitsTable() {
        powerLimitTable = new CellTable<PowerLimitValue>();
        TextColumn<PowerLimitValue> labelColumn = new TextColumn<PowerLimitValue>() {
            @Override
            public String getValue(PowerLimitValue powerLimit) {
                return powerLimit.getLabel() == null ? "" : powerLimit.getLabel();
            }
        };
        labelColumn.setFieldUpdater(new FieldUpdater<PowerLimitValue, String>() {

            @Override
            public void update(int index, PowerLimitValue limit, String value) {
                limit.setLabel(value);
                powerLimitTable.redraw();
            }

        });
        labelColumn.setSortable(true);
        // Create value column and set column formatter.
        NumberFormat format = NumberFormat.getFormat("###0");
        Column<PowerLimitValue, Number> valueColumn = new Column<PowerLimitValue, Number>(new NumberCell(format)) {
            @Override
            public Integer getValue(PowerLimitValue powerLimit) {
                return powerLimit.getValue() == null ? 0 : powerLimit.getValue();
            }
        };
        valueColumn.setFieldUpdater(new FieldUpdater<PowerLimitValue, Number>() {
            @Override
            public void update(int index, PowerLimitValue limit, Number value) {
                logger.info("Field updated: " + limit.getLabel());
                limit.setValue(value.intValue());
                powerLimitTable.redraw();
            }

        });

        // Add the columns.
        powerLimitTable.addColumn(labelColumn, MessagesUtil.getInstance().getMessage("power.limit.table.label.header"));
        powerLimitTable.addColumn(valueColumn, MessagesUtil.getInstance().getMessage("power.limit.table.value.header"));
        // Create a data provider.
        powerLimitDataProvider = new ListDataProvider<PowerLimitValue>();
        // Connect the table to the data provider.
        powerLimitDataProvider.addDataDisplay(powerLimitTable);

        final SingleSelectionModel<PowerLimitValue> selectionModel = new SingleSelectionModel<PowerLimitValue>();
        powerLimitTable.setSelectionModel(selectionModel);
        // Add a ColumnSortEvent.ListHandler to connect sorting to the
        ListHandler<PowerLimitValue> columnSortHandler = new ListHandler<PowerLimitValue>(powerLimitDataProvider.getList());
        columnSortHandler.setComparator(labelColumn, new Comparator<PowerLimitValue>() {
            public int compare(PowerLimitValue o1, PowerLimitValue o2) {
                if (o1 == o2) {
                    return 0;
                }
                // Compare the name columns.
                if (o1 != null) {
                    return (o2 != null) ? o1.getLabel().compareTo(o2.getLabel()) : 1;
                }
                return -1;
            }
        });
        powerLimitTable.addColumnSortHandler(columnSortHandler);
        // We know that the data is sorted alphabetically by default.
        powerLimitTable.getColumnSortList().push(labelColumn);
        // Add pager
        final TablePager pager = new TablePager();
        pager.setDisplay(powerLimitTable);
        pager.setPageSize(5);
        selectionModel.addSelectionChangeHandler(new SelectionChangeEvent.Handler() {
            public void onSelectionChange(SelectionChangeEvent event) {
                PowerLimitValue selectedPowerLimit = selectionModel.getSelectedObject();
                if (selectedPowerLimit != null) {
                    // Create the new pop up.
                    new PowerLimitDialogueBox(view.getForm(), powerLimitDataProvider,
                            powerLimitTable.getKeyboardSelectedRow() + pager.getPage() * pager.getPageSize(),
                            powerVoltage).show();
                    selectionModel.clear();
                }
            }
        });

        VerticalPanel vPanel = new VerticalPanel();
        vPanel.add(powerLimitTable);
        vPanel.add(pager);

        Button addPowerLimit = new Button(MessagesUtil.getInstance().getMessage("power.limit.add.button.prompt"));
        addPowerLimit.addClickHandler(new ClickHandler() {
            @Override
            public void onClick(ClickEvent event) {
                // Create the new popup.
                final PowerLimitDialogueBox popup = new PowerLimitDialogueBox(view.getForm(), powerLimitDataProvider,
                        powerVoltage);
                popup.show();
            }
        });
        panel.powerLimitTableElement.add(addPowerLimit);
        panel.powerLimitTableElement.add(vPanel);
    }

    private void clear() {
        selectedAppSetting = null;
        panel.clearFields();
        panel.clearErrors();
        view.getForm().setVisible(false);
        view.clearTableSelection();
    }

    private boolean isValidInput(AppSetting dto) {
        boolean valid = true;
        panel.clearErrors();

        if (!ClientValidatorUtil.getInstance().validateField(dto, "name", panel.nameElement)) {
            valid = false;
            logger.info("Invalid dto name: "+dto.getName());
        }
        String key = dto.getKey().toLowerCase();
        if (dto.getValue() == null || dto.getValue().trim().equals("")) {
            if (key.contains(".custom_") && key.endsWith(".label")) {
                panel.valueElement.showErrorMsg(MessagesUtil.getInstance().getMessage("error.field.value.null"));
                valid = false;
            }
            if (!dto.getKey().equals(AppSettings.VENDING_MAX_VEND_AMT)
                    && !dto.getKey().equals(AppSettings.VENDING_MIN_VEND_AMT)
                    && !dto.getKey().toLowerCase().contains("label")) {
                panel.valueElement.showErrorMsg(MessagesUtil.getInstance().getMessage("error.field.value.null"));
                valid = false;
            }
        } else if (dto.getKey().toLowerCase().contains("datatype") &&
                !ClientValidatorUtil.getInstance().validateField(dto, "value", panel.dataTypeElement)) {
            panel.dataTypeElement.showErrorMsg(MessagesUtil.getInstance().getMessage("error.field.value.null"));
            valid = false;
        } else if (dto.getKey().toLowerCase().equals(AppSettings.LOCATION_REQUIRED_TO_ALL_LEVELS)) {
            if (!dto.getValue().trim().toLowerCase().equals("true") && !dto.getValue().trim().toLowerCase().equals("false")) {
                panel.valueElement.showErrorMsg(MessagesUtil.getInstance().getMessage("error.field.value.boolean"));
                valid = false;
            }
            if (dto.getValue().trim().toLowerCase().equals("true") && !locationGroupIsRequired) {
                panel.valueElement.showErrorMsg(MessagesUtil.getInstance().getMessage("error.field.value.location.level"));
                valid = false;
            }
        } else if (!ClientValidatorUtil.getInstance().validateField(dto, "value", panel.valueElement)) {
            valid = false;
        }

        if (!ClientValidatorUtil.getInstance().validateField(dto, "description", panel.descriptionElement)) {
            valid = false;
        }

        if (dto.getKey().equals(AppSettings.MULTI_USAGE_POINT_ENABLE)
                || dto.getKey().equals(AppSettings.VENDING_MIN_MAX_VEND_UNITS_ENABLED)
                || dto.getKey().equals(AppSettings.UP_ALLOW_FUTURE_INSTALLATION_DATES)
                || dto.getKey().equals(AppSettings.PRICINGSTRUCTURE_DISPLAYSUGGESTIONBOXES)
                || dto.getKey().equals(AppSettings.AUTO_POPULATE_AGREEMENT_REF)
                || dto.getKey().equals(AppSettings.AUTO_POPULATE_ACCOUNT_NAME)
                || dto.getKey().equals(AppSettings.ACCUMULATE_AUX_DURING_SUSPENSION)
                || dto.getKey().equals(AppSettings.VENDING_RECEIPT_TARIFF_INCLUDE_TAX)
                || dto.getKey().equals(AppSettings.BLOCKCHARGE_HAS_UNITCHARGE)
                || dto.getKey().equals(AppSettings.VALIDATE_ALL_BILLING_DETERMINANTS_MODEL_VS_TARIFF)
                || dto.getKey().equals(AppSettings.VENDRES_ADD_AUX_SUMMARY_TOTALS)
                || dto.getKey().equals(AppSettings.VENDING_RECEIPT_TARIFF_STRING_IN_CUSTOMER_MESSAGE)
                || dto.getKey().equals(AppSettings.METERS_IN_STORE)
                || dto.getKey().equals(AppSettings.VENDING_ALLOW_DEBT_ONLY_VEND)
                || dto.getKey().equals(AppSettings.CUSTOMER_ACCOUNT_NOTIFY_NEW_AUX)
                || dto.getKey().equals(AppSettings.CUSTOMER_ACCOUNT_NOTIFY_AUX_ADJUSTMENT)) {
            if (!dto.getValue().equalsIgnoreCase("true") && !dto.getValue().equalsIgnoreCase("false")) {
                panel.valueElement.showErrorMsg(MessagesUtil.getInstance().getMessage("error.field.value.boolean"));
                valid = false;
            }
        }

        if (dto.getKey().equals(AppSettings.TARIFF_FIRST_MONTH)
                || dto.getKey().equals(AppSettings.AUX_MONTHLY_FIRST_MONTH)) {
            if (!dto.getValue().equalsIgnoreCase("PRO_RATA")
                    && !dto.getValue().equalsIgnoreCase("FREE")
                    && !dto.getValue().equalsIgnoreCase("FULL")) {
                panel.valueElement.showErrorMsg(MessagesUtil.getInstance().getMessage("error.field.value.monthly.charge"));
                valid = false;
            }
        }

        String appSettingKey = selectedAppSetting.getKey();
        if (!MeterMngSharedUtils.isNumber(dto.getValue())
            && (appSettingKey.equals(AppSettings.CUSTOMER_ACCOUNT_DISCONNECT_THRESHOLD)
                || appSettingKey.equals(AppSettings.CUSTOMER_ACCOUNT_RECONNECT_THRESHOLD)
                || appSettingKey.equals(AppSettings.CUSTOMER_ACCOUNT_LOW_BALANCE_THRESHOLD)
                || appSettingKey.equals(AppSettings.CUSTOMER_ACCOUNT_EMERGENCY_CREDIT_THRESHOLD)
                    || appSettingKey.toLowerCase().equals("reversal.time.limit")
                || appSettingKey.toLowerCase().equals("search.search_by_meter_number_character_threshold")))
        {
            panel.valueElement.showErrorMsg(MessagesUtil.getInstance().getMessage("error.numeric.value"));
            valid = false;
        }

        if (appSettingKey.toLowerCase().equals("vending.max_vend_units") || appSettingKey.toLowerCase().equals("vending.min_vend_units")
            || (!dto.getValue().isEmpty()
                        && (appSettingKey.toLowerCase().equals("vending.max_vend_amt") || appSettingKey.toLowerCase().equals("vending.min_vend_amt"))) ) {
             try {
                Double.valueOf(dto.getValue());
            } catch (NumberFormatException e) {
                panel.valueElement.showErrorMsg(MessagesUtil.getInstance().getMessage("error.numeric.value"));
                valid = false;
            }
        }

        if (dto.getKey().equals(MeterMngStatics.APP_SETTING_FROM_EMAIL_KEY)
                && !MeterMngCommonUtil.isValidEmail(dto.getValue())) {
            panel.valueElement.showErrorMsg(MessagesUtil.getInstance().getMessage("error.email.invalid"));
            valid = false;
        }

        return valid;
    }

    private AppSetting processUpdatedAppSetting() {
        AppSetting appSetting = new AppSetting();
        processUpdatedAppSetting(appSetting);
        return appSetting;
    }

    private void processUpdatedAppSetting(AppSetting updatedAppSetting) {
        if (selectedAppSetting != null) {
            updatedAppSetting.setId(selectedAppSetting.getId());
            updatedAppSetting.setKey(selectedAppSetting.getKey());
        }
        updatedAppSetting.setName(panel.nameTextBox.getText());
        String description = panel.descriptionTextArea.getText();
        if (selectedAppSetting.getKey().endsWith("datatype") && selectedAppSetting.getValue().equals("List")) {
            String[] thelist = description.split(",");
            StringBuilder stringBuilder = new StringBuilder("");
            for (int i=0; i<thelist.length;i++) {
                if (i>0) {
                    stringBuilder.append(",");
                }
                stringBuilder.append(thelist[i].trim());
            }
            description = stringBuilder.toString();
        }
        updatedAppSetting.setDescription(description);

        if (updatedAppSetting.getKey() != null && updatedAppSetting.getKey().toLowerCase().contains(".status")) {
            updatedAppSetting.setValue(panel.statusListBox.getValue(panel.statusListBox.getSelectedIndex()));
        } else if (selectedAppSetting.getKey() != null && !selectedAppSetting.getKey().isEmpty()
                   && selectedAppSetting.getKey().equalsIgnoreCase(AppSettings.POWER_LIMIT_SETTINGS)) {
            String strPowerLimits = getPowerLimitsString();
            updatedAppSetting.setValue(strPowerLimits);
        } else if (updatedAppSetting.getKey().toLowerCase().contains(".datatype")) {
            updatedAppSetting.setValue(panel.dataTypeListBox.getValue(panel.dataTypeListBox.getSelectedIndex()));
        } else if (updatedAppSetting.getKey().toLowerCase().contains(".grouptype")) {
            updatedAppSetting.setValue(panel.groupTypeListBox.getValue(panel.groupTypeListBox.getSelectedIndex()));
        } else {
            updatedAppSetting.setValue(panel.valueTextBox.getText());
        }
    }

    private String getPowerLimitsString() {
        String strPowerLimits = "";
        // Copy power limits list so that we can sort by value before
        // creating application setting value string
        List<PowerLimitValue> newList = new ArrayList<PowerLimitValue>(powerLimitDataProvider.getList());
        Collections.sort(newList, new Comparator<PowerLimitValue>() {
            @Override
            public int compare(PowerLimitValue a, PowerLimitValue b) {
                return a.getValue().compareTo(b.getValue());
            }
        });
        boolean isFirst = true;
        for (PowerLimitValue powerLimit : newList) {
            if (isFirst) {
                strPowerLimits += powerLimit.getLabel() + "," + powerLimit.getValue();
                isFirst = false;
            } else {
                strPowerLimits += "|" + powerLimit.getLabel() + "," + powerLimit.getValue();
            }
        }
        return strPowerLimits;
    }

    private void onSave() {
        SessionCheckCallback sessionCheckCallback = new SessionCheckCallback() {
            @Override
            public void callback(SessionCheckResolution sessionCheckResolution) {
                final AppSetting updatedAppSetting = processUpdatedAppSetting();

                //for locationGroup Setting get the location group isRequired setting
                if (updatedAppSetting.getKey().equalsIgnoreCase(AppSettings.LOCATION_REQUIRED_TO_ALL_LEVELS) &&
                    updatedAppSetting.getValue() != null && updatedAppSetting.getValue().trim().equalsIgnoreCase("true")) {
                    clientFactory.getGroupRpc().getLocationGroupType(new AsyncCallback<GroupType>() {

                        @Override
                        public void onSuccess(GroupType result) {
                            locationGroupIsRequired = result.isRequired();
                            onSaveContinue(updatedAppSetting);
                        }

                        @Override
                        public void onFailure(Throwable arg0) {
                            onSaveContinue(updatedAppSetting);
                        }
                    });
                } else {
                    onSaveContinue(updatedAppSetting);
                }
            }
        };
        clientFactory.handleSessionCheckCallback(sessionCheckCallback);
    }

    private void onSaveContinue(final AppSetting updatedAppSetting) {
        if (selectedAppSetting.getKey().endsWith("datatype") && selectedAppSetting.getValue().equals("List")) {
            Dialogs.confirm(
                    ResourcesFactoryUtil.getInstance().getMessages().getMessage("question.custom.field.used"),
                    ResourcesFactoryUtil.getInstance().getMessages().getMessage("question.custom.field.used.option.yes"),
                    ResourcesFactoryUtil.getInstance().getMessages().getMessage("question.custom.field.used.option.no"),
                    ResourcesFactoryUtil.getInstance().getQuestionIcon(),
                    new ConfirmHandler() {
                        @Override
                        public void confirmed(boolean confirm) {
                            if (confirm) {
                                saveAppSetting(updatedAppSetting);
                            } else {
                                //event.setCanceled(true);
                                logger.fine("Canceled saving appSetting");
                            }
                        }
                    }
            );
        } else {
            saveAppSetting(updatedAppSetting);
        }
    }

    private void saveAppSetting(AppSetting updatedAppSetting) {
        if (isValidInput(updatedAppSetting)) {
            final AppSetting as = processUpdatedAppSetting();
            final String key = as.getKey();
            logger.info("MMA AppSetting: " + key + " updated by user " + clientFactory.getUser().getUserName());
            final SimpleForm form = view.getForm();
            clientFactory.getAppSettingRpc().saveAppSetting(as, new ClientCallback<Void>() {
                @Override
                public void onSuccess(Void result) {
                    form.setDirtyData(false);
                    boolean refresh = false;
                    if (as.getId() == null) {
                        refresh = true;
                    }
                    refreshTable(refresh);
                    Messages messages = MessagesUtil.getInstance();
                    Dialogs.displayInformationMessage(
                            messages.getSavedMessage(new String[] { messages.getMessage("appsetting.name") }),
                            MediaResourceUtil.getInstance().getInformationIcon());
                    clear();
                    if (AppSettings.MULTI_USAGE_POINT_ENABLE.equals(key)) {
                        clientFactory.setEnableMultiUp("true".equalsIgnoreCase(as.getValue()));
                    } else if (AppSettings.MULTI_CUST_AGR_ENABLE.equals(key)) {
                        clientFactory.setEnableMultiCustAgr("true".equalsIgnoreCase(as.getValue()));
                    } else if (AppSettings.GROUPS_TREE_DISPLAY_SIZE.equals(key)) {
                        clientFactory.setGroupsTreeDisplaySize(AppSettings.toRequiredInteger(as.getValue(), AppSettings.GROUPS_TREE_DISPLAY_SIZE_DEFAULT));
                    } else if (AppSettings.GROUPS_SUGGEST_BOX_DISPLAY_SIZE.equals(key)) {
                        clientFactory.setGroupsSuggestBoxDisplaySize(AppSettings.toRequiredInteger(as.getValue(), AppSettings.GROUPS_SUGGEST_BOX_DISPLAY_SIZE_DEFAULT));
                    } else if (AppSettings.PRICINGSTRUCTURE_DISPLAYSUGGESTIONBOXES.equals(key)) {
                        clientFactory.getEventBus().fireEvent(new PricingStructureUpdateEvent(as));
                    } else if (AppSettings.POWERLIMIT_VOLTAGE_UPDATED.equals(key)) {
                        powerVoltage = Integer.parseInt(as.getValue());
                        clientFactory.getAppSettingRpc().getAppSettingByKey(AppSettings.POWER_LIMIT_SETTINGS,
                                new ClientCallback<AppSetting>() {
                                    @Override
                                    public void onSuccess(final AppSetting appSetting) {
                                        List<PowerLimitValue> powerLimits = PowerLimitValue.fromAppSetting(appSetting);
                                        for (PowerLimitValue powerLimitValue : powerLimits) {
                                            powerLimitValue.setLabel(powerLimitValue.generateLabel(powerLimitValue.getValue(), powerVoltage));
                                        }
                                        List<PowerLimitValue> list = powerLimitDataProvider.getList();
                                        list.clear();
                                        list.addAll(powerLimits);
                                        appSetting.setValue(getPowerLimitsString());
                                        clientFactory.getAppSettingRpc().saveAppSetting(appSetting,
                                                new ClientCallback<Void>() {
                                                    @Override
                                                    public void onSuccess(Void unused) {
                                                        clientFactory.getEventBus().fireEvent(new AppSettingEvent(appSetting));
                                                    }
                                                });
                                    }
                                });
                    } else {
                        sendNotification(key);
                    }
                }
            });
        }
    }

    private void sendNotification(String key) {
        //Notify any affected tabs
        clientFactory.getWorkspaceContainer().notifyWorkspaces(
                new WorkspaceNotification(NotificationType.DATA_UPDATED, MeterMngStatics.APPSETTINGS_MODIFIED, key));
        clientFactory.updateAppSettingsChangedDate();
    }

    //Method to force the table to refresh its current page. A new row could of been added or just the data should be
    //reloaded due to other changes like disabled user.
    private void refreshTable(boolean insertedNew) {
        if (insertedNew) {
            view.getTable().setRowCount(view.getTable().getRowCount() + 1, true);
        }
        Range range = view.getTable().getVisibleRange();
        view.getTable().setVisibleRangeAndClearData(range, true);
    }
}
