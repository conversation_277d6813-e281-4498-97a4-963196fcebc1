package za.co.ipay.metermng.client.event;

import com.google.gwt.event.shared.GwtEvent;

public class AuxAccountUploadEvent extends GwtEvent<AuxAccountUploadEventHandler> {

	public static Type<AuxAccountUploadEventHandler> TYPE = new Type<AuxAccountUploadEventHandler>();

	private String name;

	public AuxAccountUploadEvent(String name) {
		this.name = name;
	}

	public String getName() {
		return name;
	}

	@Override
	public Type<AuxAccountUploadEventHandler> getAssociatedType() {
		return TYPE;
	}

	@Override
	protected void dispatch(AuxAccountUploadEventHandler handler) {
		handler.handleEvent(this);
	}

}
