package za.co.ipay.metermng.client.history;

import java.util.logging.Logger;

import com.google.gwt.place.shared.PlaceTokenizer;
import com.google.gwt.place.shared.Prefix;

public class BlockingTypePlace extends BasePlace {

	public static BlockingTypePlace ALL_BLOCKING_TYPES_PLACE = new BlockingTypePlace();

	private String blockingTypeId;

	private static Logger logger = Logger.getLogger(BlockingTypePlace.class.getName());

	public BlockingTypePlace() {
		this.blockingTypeId = ALL_DATA;
	}

	public BlockingTypePlace(String blockingTypeId) {
		this.blockingTypeId = blockingTypeId;
	}

	public String getBlockingTypeId() {
		return blockingTypeId;
	}

	@Override
	public String toString() {
		return "blockingType" + TOKEN_SEPERATOR + blockingTypeId;
	}

	@Prefix(value = "blockingType")
	public static class Tokenizer implements PlaceTokenizer<BlockingTypePlace> {
		@Override
		public String getToken(BlockingTypePlace place) {
			return place.getBlockingTypeId();
		}

		@Override
		public BlockingTypePlace getPlace(String token) {
			String[] tokens = (token == null) ? new String[0] : token.split(TOKEN_SEPERATOR);
			for (String part : tokens) {
				logger.info("Token part: " + part);
				return new BlockingTypePlace(part);
			}
			return new BlockingTypePlace();
		}
	}
}
