package za.co.ipay.metermng.client.history;

import com.google.gwt.place.shared.Place;
import com.google.gwt.place.shared.PlaceTokenizer;
import com.google.gwt.place.shared.Prefix;

public class DeviceStorePlace extends Place {

    public static DeviceStorePlace ALL_DEVICE_STORE_PLACE = new DeviceStorePlace();

    public DeviceStorePlace() {
    }

    @Prefix(value = "deviceStore")
    public static class Tokenizer implements PlaceTokenizer<DeviceStorePlace> {
        @Override
        public String getToken(DeviceStorePlace place) {
            return "all";
        }

        @Override
        public DeviceStorePlace getPlace(String token) {
            return new DeviceStorePlace();
        }
    }
}
