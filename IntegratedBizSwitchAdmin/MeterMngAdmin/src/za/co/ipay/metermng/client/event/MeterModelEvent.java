package za.co.ipay.metermng.client.event;

import com.google.gwt.event.shared.GwtEvent;

public class MeterModelEvent extends GwtEvent<MeterModelEventHandler> {
    
    public static Type<MeterModelEventHandler> TYPE = new Type<MeterModelEventHandler>();
    
    private String name;
    
    public MeterModelEvent(String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }

    @Override
    public Type<MeterModelEventHandler> getAssociatedType() {
        return TYPE;
    }

    @Override
    protected void dispatch(MeterModelEventHandler handler) {
        handler.handleEvent(this);
    }
}
