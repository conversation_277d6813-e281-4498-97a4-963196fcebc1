package za.co.ipay.metermng.client.history;

import com.google.gwt.place.shared.Place;
import com.google.gwt.place.shared.PlaceTokenizer;
import com.google.gwt.place.shared.Prefix;

public class SpecialActionsPlace extends Place {
    
    public static final SpecialActionsPlace ALL = new SpecialActionsPlace("all");
    
    private String name;
    
    public SpecialActionsPlace(String name) {
        this.name = name;
    }
    
    public String getName() {
        return name;
    }

    public static String getPlaceAsString(SpecialActionsPlace place) {
        return "specialActions:"+place.getName();
    }
    
    @Prefix(value = "specialActions")
    public static class Tokenizer implements PlaceTokenizer<SpecialActionsPlace> {
        
        @Override
        public String getToken(SpecialActionsPlace place) {
            return place.getName();
        }

        @Override
        public SpecialActionsPlace getPlace(String token) {
            return new SpecialActionsPlace(token);
        }
    }
}
