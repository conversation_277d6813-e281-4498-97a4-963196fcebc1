package za.co.ipay.metermng.client.history;

import com.google.gwt.place.shared.Place;
import com.google.gwt.place.shared.PlaceTokenizer;
import com.google.gwt.place.shared.Prefix;

public class AuxTransUploadPlace extends Place {

	public static AuxTransUploadPlace ALL_PLACE = new AuxTransUploadPlace("all");

	private String name;

	public AuxTransUploadPlace(String name) {
		this.name = name;
	}

	public String getName() {
		return name;
	}

	public static String getPlaceAsString(AuxTransUploadPlace p) {
		return "auxtransupload:" + p.getName();
	}

	@Prefix(value = "auxtransupload")
	public static class Tokenizer implements PlaceTokenizer<AuxTransUploadPlace> {

		@Override
		public String getToken(AuxTransUploadPlace place) {
			return "all";
		}

		@Override
		public AuxTransUploadPlace getPlace(String token) {
			return new AuxTransUploadPlace(token);
		}
	}

}
