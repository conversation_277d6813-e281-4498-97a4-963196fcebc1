package za.co.ipay.metermng.client.view.workspace.appsetting;

import za.co.ipay.gwt.common.client.form.SimpleForm;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.form.SimpleFormPanel;

import com.google.gwt.core.client.GWT;
import com.google.gwt.event.dom.client.ChangeEvent;
import com.google.gwt.event.dom.client.ChangeHandler;
import com.google.gwt.event.dom.client.ClickEvent;
import com.google.gwt.event.dom.client.ClickHandler;
import com.google.gwt.event.dom.client.KeyUpEvent;
import com.google.gwt.event.dom.client.KeyUpHandler;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.user.client.ui.Button;
import com.google.gwt.user.client.ui.TextBox;
import com.google.gwt.user.client.ui.Widget;

public class AppSettingFilterPanel extends SimpleFormPanel {

    private AppSettingWorkspaceView appSettingWorkspaceView;
    @UiField TextBox txtSearchText;
    @UiField Button clearButton;
    private String searchText = null;

    private static AppSettingsFilterPanelUiBinder uiBinder = GWT.create(AppSettingsFilterPanelUiBinder.class);

    interface AppSettingsFilterPanelUiBinder extends UiBinder<Widget, AppSettingFilterPanel> {
    }

    public AppSettingFilterPanel(ClientFactory clientFactory, AppSettingWorkspaceView appSettingWorkspaceView) {
        super(new SimpleForm());
        this.appSettingWorkspaceView = appSettingWorkspaceView;
        initWidget(uiBinder.createAndBindUi(this));
        addFieldHandlers();
    }

    @Override
    public void addFieldHandlers() {
        txtSearchText.addChangeHandler(new ChangeHandler() {
            @Override
            public void onChange(ChangeEvent event) {
                if (!txtSearchText.getText().equals(searchText)) {
                    searchText = txtSearchText.getText();
                    appSettingWorkspaceView.getAppSettingCount(true);
                }
            }
        });
        txtSearchText.addKeyUpHandler(new KeyUpHandler() {
            @Override
            public void onKeyUp(KeyUpEvent event) {
                if (!txtSearchText.getText().equals(searchText)) {
                    searchText = txtSearchText.getText();
                    appSettingWorkspaceView.getAppSettingCount(true);
                }
            }
        });
        clearButton.addClickHandler(new ClickHandler() {
            @Override
            public void onClick(ClickEvent event) {
                txtSearchText.setText("");
                appSettingWorkspaceView.getAppSettingCount(true);
            }
        });
    }

    @Override
    public void clearFields() {
    }

    @Override
    public void clearErrors() {
    }
}
