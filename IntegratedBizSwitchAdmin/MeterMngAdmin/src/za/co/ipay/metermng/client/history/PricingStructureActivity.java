package za.co.ipay.metermng.client.history;

import za.co.ipay.metermng.client.event.OpenPricingStructureEvent;
import za.co.ipay.metermng.client.factory.ClientFactory;

import com.google.gwt.activity.shared.AbstractActivity;
import com.google.gwt.event.shared.EventBus;
import com.google.gwt.user.client.ui.AcceptsOneWidget;

public class PricingStructureActivity extends AbstractActivity {
    
    private PricingStructurePlace pricingStructurePlace;
    private ClientFactory clientFactory;
    
    public PricingStructureActivity(PricingStructurePlace pricingStructurePlace, ClientFactory clientFactory) {
        super();
        this.pricingStructurePlace = pricingStructurePlace;
        this.clientFactory = clientFactory;
    }
    
    @Override
    public void start(AcceptsOneWidget panel, EventBus eventBus) {
        clientFactory.getEventBus().fireEvent(new OpenPricingStructureEvent(pricingStructurePlace));
    }
}
