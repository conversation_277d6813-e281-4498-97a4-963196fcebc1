package za.co.ipay.metermng.client.history;

import com.google.gwt.place.shared.Place;
import com.google.gwt.place.shared.PlaceTokenizer;
import com.google.gwt.place.shared.Prefix;

public class GlobalNdpPlace extends Place {
    
    public static final String ALL = "all";
    
    private String name;
    
    public GlobalNdpPlace(String name) {
        this.name = name;
    }
    
    public String getName() {
        return name;
    }

    public static String getPlaceAsString(GlobalNdpPlace place) {
        return "globalNdp:"+place.getName();
    }
    
    @Prefix(value = "globalNdp")
    public static class Tokenizer implements PlaceTokenizer<GlobalNdpPlace> {
        
        @Override
        public String getToken(GlobalNdpPlace place) {
            return place.getName();
        }

        @Override
        public GlobalNdpPlace getPlace(String token) {
            return new GlobalNdpPlace(token);
        }
    }
}
