package za.co.ipay.metermng.server.rpc;

import org.apache.log4j.Logger;

import za.co.ipay.email.shared.EmailBuilder;
import za.co.ipay.exception.RemoteServiceException;
import za.co.ipay.gwt.common.shared.exception.ServiceException;
import za.co.ipay.metermng.client.rpc.NotificationRpc;
import za.co.ipay.metermng.server.mybatis.service.EmailService;
import za.co.ipay.metermng.service.SmsService;

/**
 * <AUTHOR> href="mailto:<EMAIL>"><PERSON></a>
 *         Created on 3/24/17.
 */
public class NotificationRpcImpl extends BaseMeterMngRpc implements NotificationRpc {

    private static final long serialVersionUID = 1L;

    private EmailService emailService;
    private SmsService smsService;

    {
        this.logger = Logger.getLogger(NotificationRpcImpl.class);
    }

    @Override
    public void sendSms(String messsage, String recipient) {
        try {
            smsService.sendSms(messsage, recipient);
        } catch (RemoteServiceException e) {
           throw new ServiceException(e.getMessage());
        }
    }

    @Override
    public void sendEmail(EmailBuilder emailBuilder)  throws ServiceException {
        try {
            emailService.sendEmail(emailBuilder);
        } catch (RemoteServiceException e) {
            throw new ServiceException(e.getMessage());
        }
    }

    public void setEmailService(EmailService emailService) throws ServiceException {
        this.emailService = emailService;
    }

    public void setSmsService(SmsService smsService) throws ServiceException {
        this.smsService = smsService;
    }
}
