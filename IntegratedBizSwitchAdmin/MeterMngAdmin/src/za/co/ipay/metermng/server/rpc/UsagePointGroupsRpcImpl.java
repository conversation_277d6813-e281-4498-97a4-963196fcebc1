package za.co.ipay.metermng.server.rpc;

import java.util.ArrayList;

import org.apache.log4j.Logger;

import za.co.ipay.gwt.common.shared.exception.ServiceException;
import za.co.ipay.gwt.common.shared.exception.ValidationException;
import za.co.ipay.metermng.client.rpc.UsagePointGroupsRpc;
import za.co.ipay.metermng.server.mybatis.service.UsagePointGroupsService;
import za.co.ipay.metermng.shared.dto.ItemValuePairDto;
import za.co.ipay.metermng.shared.dto.dashboard.UsagePointGroupsAddedContainerDto;

public class UsagePointGroupsRpcImpl extends BaseMeterMngRpc implements UsagePointGroupsRpc {

    private static final long serialVersionUID = 1L;

    private UsagePointGroupsService usagePointGroupsService;

    public UsagePointGroupsRpcImpl() {
        logger = Logger.getLogger(UsagePointGroupsRpcImpl.class);
    }

    public void setUsagePointGroupsService(UsagePointGroupsService usagePointGroupsService) {
        this.usagePointGroupsService = usagePointGroupsService;
    }

    @Override
    public ArrayList<UsagePointGroupsAddedContainerDto> getUsagePointGroupsAdded()
            throws ValidationException, ServiceException {

        return  usagePointGroupsService.getUsagePointGroupsAdded();
     }

	@Override
	public ArrayList<ItemValuePairDto> getUsagePointGroupsList()
			throws ValidationException, ServiceException {
 
		return usagePointGroupsService.getUsagePointGroupsList();
	}
}
