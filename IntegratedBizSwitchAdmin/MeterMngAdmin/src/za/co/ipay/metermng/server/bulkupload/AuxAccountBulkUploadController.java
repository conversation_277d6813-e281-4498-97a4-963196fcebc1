package za.co.ipay.metermng.server.bulkupload;

import java.math.BigDecimal;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Set;

import org.apache.commons.beanutils.BeanUtils;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;

import za.co.ipay.gwt.common.shared.exception.ValidationException;
import za.co.ipay.gwt.common.shared.validation.ValidateUtil;
import za.co.ipay.metermng.cim.MridUtil;
import za.co.ipay.metermng.datatypes.RecordStatus;
import za.co.ipay.metermng.mybatis.generated.mapper.AuxAccountMapper;
import za.co.ipay.metermng.mybatis.generated.mapper.AuxTypeMapper;
import za.co.ipay.metermng.mybatis.generated.model.AuxAccount;
import za.co.ipay.metermng.mybatis.generated.model.AuxAccountExample;
import za.co.ipay.metermng.mybatis.generated.model.AuxChargeSchedule;
import za.co.ipay.metermng.mybatis.generated.model.AuxType;
import za.co.ipay.metermng.mybatis.generated.model.AuxTypeExample;
import za.co.ipay.metermng.mybatis.generated.model.CustomerAgreement;
import za.co.ipay.metermng.mybatis.generated.model.Meter;
import za.co.ipay.metermng.mybatis.generated.model.UsagePoint;
import za.co.ipay.metermng.server.mybatis.service.AuxChargeScheduleService;
import za.co.ipay.metermng.server.mybatis.service.BulkUploadService;
import za.co.ipay.metermng.server.mybatis.service.CustomerAgreementService;
import za.co.ipay.metermng.server.mybatis.service.MeterService;
import za.co.ipay.metermng.server.mybatis.service.UsagePointService;
import za.co.ipay.metermng.server.util.MeterMngUtil;
import za.co.ipay.metermng.server.validation.ServerValidatorUtil;
import za.co.ipay.metermng.shared.AuxAccountData;
import za.co.ipay.metermng.shared.bulkupload.BulkUploadException;
import za.co.ipay.metermng.shared.dto.uploaddata.auxaccountupload.AuxAccountCsvData;
import za.co.ipay.metermng.shared.dto.uploaddata.auxaccountupload.AuxAccountCsvMapToData;

@Controller
@RequestMapping(value = "/**/secure/auxaccountbulkupload.do")
public class AuxAccountBulkUploadController extends BulkUploadController {
	
	@Autowired
	private UsagePointService usagePointService;

	@Autowired
	private MeterService meterService;

	@Autowired
	private CustomerAgreementService customerAgreementService;

	@Autowired
	private AuxChargeScheduleService auxChargeScheduleService;
	
	@Autowired
	private AuxAccountMapper auxAccountMapper;

	@Autowired
	private AuxTypeMapper auxTypeMapper;

	@Autowired
	private BulkUploadService bulkUploadService;

	private Logger logger = Logger.getLogger(AuxAccountBulkUploadController.class.getName());

	private static final String AGREEMENT_REF = "agreementref";
	private static final String METER_NUM = "meternumber";
	private static final String USAGEPOINT_NAME = "usagepointname";

	private static final String DEBT = "debt";
	private static final String REFUND = "refund";

	private DateFormat dateFormatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
	{
		dateFormatter.setLenient(false);
	}
	private Set<String> acname_agrrefSet;
	private Set<String> acpriority_agrrefSet;
	private ArrayList<AuxType> auxTypeList;
	private ArrayList<AuxChargeSchedule> auxChargeScheduleList;
	private ArrayList<AuxAccount> auxAccountDBList;

	private ArrayList<AuxAccountCsvData> processAaccountList;
	private ArrayList<AuxAccountData> auxAccountDataList;

	private AuxAccountCsvMapToData csvMapData;

	private void setupDataMapping() {
		csvMapData = new AuxAccountCsvMapToData();
		csvFieldMap = new HashMap<Integer, String>();
		headingIndicator = "Identifier Type";
	}

	@Override
	void constructCsvFieldMap(String headingLine) throws BulkUploadException {
		csvFieldMap = csvMapData.constructCsvFieldMap(headingLine);
	}

	// *********************************************************************************************
	// ********** VALIDATE UPLOADED FILE (FILE STRUCTURE THEN CONTENT) *****************************
	@Override
	void validationPreProcessing() {
		setupDataMapping();
		acname_agrrefSet = new HashSet<String>();
		acpriority_agrrefSet = new HashSet<String>();
		checkPreLoadedTables();
	}

	@Override
	String validateTrans(String thisLine) {
		/*
		 * Validation Rules. FIELDS : (IdentifierType, Identifier, auxAccountName, auxTypeName, priority, chargeScheduleName, principleAmount, balance) 
		 * IdentifierType : (Required, valid-values-are-(meterNumber, usagePointName, agreementRef))
		 * Identifier : (Required, validation-is-based-on-IdentifierType) 
		 * 		> meterNumber : (If provided; must-exists-in-DB, must-have-status-ACT, must-have-UP) 
		 * 		> usagePointName : (If provided; must-exists-in-DB, must-have-status-ACT, must-have-Customer-Agreement_ID) 
		 * 		> agreementRef : (If provided, must-exists-in-DB, must-have-status-ACT)
		 * auxAccountName : (Required, 100 Chars Max) 
		 * auxTypeName : (Required, must-exists-in-DB, must-have-status-ACT) 
		 * priority: (Required, Integer, From 0 - 12) 
		 * chargeScheduleName : (Required, must-exists-in-DB, must-have-status-ACT) 
		 * principleAmount : (Not Required, if-provided-must-be-Number, default-NULL) 
		 * balance : (Required, must-be-Number)
		 * suspendUntil : (Not Required, must be valid date)
		 * startDate  : (Not Required, must be valid date and <= suspendUntil) if not input, db will default to now()
		 */
		
		AuxAccountCsvData auxAccountRowData = new AuxAccountCsvData(csvFieldMap, thisLine, false);
		StringBuilder validationError = new StringBuilder(100);

		if (auxAccountRowData.isUnexpectedCommas()) {
			validationError.append("bulk.upload.invalid.unexpected.commas");
			return validationError.toString();
		}

		Long customerAgreementId = null;

		// Identifier Type
		String identifierType = null;
		if (!ValidateUtil.isNotNullOrBlank(auxAccountRowData.getIdentifierType())) {
			validationError.append(addValidationError("required", "auxaccount.upload.identifierType"));
		} else {
			identifierType = auxAccountRowData.getIdentifierType().toLowerCase();
			if (!identifierType.equals(AGREEMENT_REF) && !identifierType.equals(USAGEPOINT_NAME)
					&& !identifierType.equals(METER_NUM)) {
				validationError.append("auxaccount.upload.invalid.identifiertype").append(";");
			}
		}

		// Identifier
		String identifierError = null;
		if (!ValidateUtil.isNotNullOrBlank(auxAccountRowData.getIdentifier())) {
			validationError.append(addValidationError("required", "auxaccount.upload.identifier"));
		} else if (identifierType != null) {
			if (identifierType.equals(USAGEPOINT_NAME)) {
				UsagePoint up = usagePointService.getUsagePointByName(auxAccountRowData.getIdentifier());
				if (up == null) {
					identifierError = "auxaccount.upload.invalid.identifier";
				} else {
					if (up.getCustomerAgreementId() == null) {
						identifierError = "auxaccount.upload.invalid.agreement";
					} else {
						customerAgreementId = up.getCustomerAgreementId();
					}
				}

			} else if (identifierType.equals(METER_NUM)) {
				Meter m = meterService.getMeterByNumber(auxAccountRowData.getIdentifier());
				if (m == null) {
					identifierError = "auxaccount.upload.invalid.identifier";
				} else {
					UsagePoint up = usagePointService.getUsagePointByMeterId(m.getId());
					if (up == null || (up != null && up.getCustomerAgreementId() == null)) {
						identifierError = "auxaccount.upload.invalid.usagepoint.or.agreement";
					} else {
						customerAgreementId = up.getCustomerAgreementId();
					}
				}
			} else if (identifierType.equals(AGREEMENT_REF)) {
				CustomerAgreement ca = customerAgreementService
						.getCustomerAgreementByAgreementRef(auxAccountRowData.getIdentifier());
				if (ca == null) {
					identifierError = "auxaccount.upload.invalid.identifier";
				} else {
					customerAgreementId = ca.getId();
				}
			}
			if (identifierError != null) {
				validationError.append(identifierError).append(";");
			}
		}

		CustomerAgreement agreementRef = null;
		Long validCustomerAgreementId = null;
		// CustomerAgreementRef is Not Null and is ACT
		if (customerAgreementId != null) {
			agreementRef = customerAgreementService.getCustomerAgreementById(customerAgreementId);
			if (agreementRef == null) {
				validationError.append(addValidationError("invalid", "auxaccount.upload.customerref"));
			} else if (!agreementRef.getRecordStatus().equals(RecordStatus.ACT)) {
				validationError.append(addValidationError("invalid", "auxaccount.upload.customerref"));
			} else {
				validCustomerAgreementId = customerAgreementId;
			}
		}

		// auxAccountName
		if (!ValidateUtil.isNotNullOrBlank(auxAccountRowData.getAuxAccountName())) {
			validationError.append(addValidationError("required", "auxaccount.upload.auxaccountname"));
		} else if (auxAccountRowData.getAuxAccountName().length() > 100) {
			validationError.append("auxaccount.upload.invalid.auxaccountname").append(";");
		}

		// auxTypeName
		if (!ValidateUtil.isNotNullOrBlank(auxAccountRowData.getAuxTypeName())) {
			validationError.append(addValidationError("required", "auxaccount.upload.auxtype"));
		} else {
			AuxType auxType = getAuxTypeByName(auxAccountRowData.getAuxTypeName());
			if (auxType == null) {
				validationError.append(addValidationError("invalid", "auxaccount.upload.auxtype"));
			}
		}

		// auxAccountPriority
		Integer auxAccountPriority = null;
		if (!ValidateUtil.isNotNullOrBlank(auxAccountRowData.getAuxAccountPriority())) {
			validationError.append(addValidationError("required", "auxaccount.upload.accountpriority"));
		} else {
			if (!ValidateUtil.isValidInteger(auxAccountRowData.getAuxAccountPriority())) {
				validationError.append(addValidationError("invalid", "auxaccount.upload.accountpriority"));
			} else {
				auxAccountPriority = Integer.parseInt(auxAccountRowData.getAuxAccountPriority());
			}
		}

		// chargeScheduleName
		if (!ValidateUtil.isNotNullOrBlank(auxAccountRowData.getChargeScheduleName())) {
			validationError.append(addValidationError("required", "auxaccount.upload.chrgschdlname"));
		} else {
			AuxChargeSchedule auxChargeScedule = getAuxChargeScheduleByName(auxAccountRowData.getChargeScheduleName());
			if (auxChargeScedule == null) {
				validationError.append(addValidationError("invalid", "auxaccount.upload.chrgschdlname"));
			}
		}

		// principleAmount
		if (ValidateUtil.isNotNullOrBlank(auxAccountRowData.getPrincipleAmount())) {
			try {
                new BigDecimal(auxAccountRowData.getPrincipleAmount());
			} catch (NumberFormatException n) {
				validationError.append("auxaccount.upload.invalid.principleamaount").append(";");
			}
		}

		// balance
		BigDecimal balance = null;
		if (!ValidateUtil.isNotNullOrBlank(auxAccountRowData.getBalance())) {
			validationError.append(addValidationError("required", "auxaccount.upload.balance"));
		} else {
			try {
				balance = new BigDecimal(auxAccountRowData.getBalance());
				if (balance.compareTo(BigDecimal.ZERO) < 0) {
					validationError.append("auxaccount.upload.invalid.balance.amount").append(";");
				}
			} catch (NumberFormatException n) {
				validationError.append("auxaccount.upload.invalid.balance").append(";");
			}
		}
		
		// Balance Type
		String balanceType = auxAccountRowData.getBalanceType();
		if (!ValidateUtil.isNotNullOrBlank(balanceType)) {
			validationError.append(addValidationError("required", "auxaccount.upload.balanceType"));
		} else {
			switch(balanceType.toLowerCase()) {
				case DEBT : 
				case REFUND : balanceType = balanceType.toLowerCase(); break;
				default : validationError.append("auxaccount.upload.invalid.balancetype").append(";"); break;
			}
		}

		// suspendUntil
		Date suspendDate = null;
		if (ValidateUtil.isNotNullOrBlank(auxAccountRowData.getSuspendUntil())) {
		    if (!MeterMngUtil.isValidDateFormat(auxAccountRowData.getSuspendUntil(), null)) {
		        validationError.append("auxaccount.upload.suspendUntil.format").append(";");
		    } else {
		        try {
		            suspendDate = dateFormatter.parse(auxAccountRowData.getSuspendUntil());
		            // suspendDate may not be in the past.
		            Date currentDate = new Date();
		            if (suspendDate.before(currentDate)) {
		                validationError.append("auxaccount.upload.suspendUntil.in.past").append(";");
		            }
		        } catch (ParseException e) {
		            validationError.append("auxaccount.upload.suspendUntil.invalid.date").append(";");
		        }
		    }
		}

        // startDate
		Date startDate = null;
        if (ValidateUtil.isNotNullOrBlank(auxAccountRowData.getStartDate())) {
            if (!MeterMngUtil.isValidDateFormat(auxAccountRowData.getStartDate(), null)) {
                validationError.append("auxaccount.upload.startDate.format").append(";");
            } else {
                try {
                    startDate = dateFormatter.parse(auxAccountRowData.getStartDate());
                    // startDate must be <= suspendUntil if that is not null
                    if (suspendDate != null && startDate.after(suspendDate)) {
                        validationError.append("auxaccount.upload.startDate.greater").append(";");
                    }
                } catch (ParseException e) {
                    validationError.append("auxaccount.upload.startDate.invalid.date").append(";");
                }
            }
        }
		
		// Duplicate Check (auxAccountName + customerAgreementId)
		if (ValidateUtil.isNotNullOrBlank(auxAccountRowData.getAuxAccountName()) && validCustomerAgreementId != null) {
			String dupError = checkForDuplicateAccountName(auxAccountRowData.getAuxAccountName(), validCustomerAgreementId);
			if (dupError != null) {
				validationError.append(dupError).append(";");
			}
		}
		// Duplicate Check (auxAccountPriority + customerAgreementId)
		if (auxAccountPriority != null && validCustomerAgreementId != null) {
			String dupError = checkForDuplicateAccountPriority(auxAccountPriority, validCustomerAgreementId);
			if (dupError != null) {
				validationError.append(dupError).append(";");
			}
		}

		int errorCount = validationError.toString().lastIndexOf(";");
		if (errorCount <= 0) {

			AuxAccount auxAccount = createAuxAccount(auxAccountRowData);
			try {
				ServerValidatorUtil.getInstance().validateDataForValidationMessages(auxAccount);
			} catch (ValidationException e) {
				logger.debug("AuxAccountBulkUpload: Exception= " + e);

				for (String key : e.getFieldErrorMessages().keySet()) { // Map<String, ValidationMessage>
					if (key.equals("mrid"))	continue;
					validationError.append("NOTKEY").append(e.getFieldErrorMessages().get(key).getMessage()).append(";");
				}
			}
		}

		String returnString = validationError.toString();
		int lastIndx = returnString.lastIndexOf(";");
		if (lastIndx > 0) {
			returnString = returnString.substring(0, lastIndx);
		}
		return returnString;
	}

	private String checkForDuplicateAccountName(String auxAccountName, Long customerAgreementId) {
		
		boolean isDuplicateAccount = false;
		String uniqueValue = auxAccountName.toLowerCase() + "_" + customerAgreementId;
		
		// Confirm a/c is not in CSV file
		if (acname_agrrefSet.contains(uniqueValue)) isDuplicateAccount = true;
		
		// Confirm a/c is not in DB
		if (isAuxAccountInDB(auxAccountName, customerAgreementId)) isDuplicateAccount = true;

		if (!isDuplicateAccount) {
			acname_agrrefSet.add(uniqueValue);
			return null;
		} else {
			return "auxaccount.upload.invalid.duplicate";
		}
	}
	
	private String checkForDuplicateAccountPriority(Integer priority, Long customerAgreementId) {

		boolean isDuplicateAccount = false;
		String uniqueValue = priority + "_" + customerAgreementId;

		// Confirm a/c is not in CSV file
		if (acpriority_agrrefSet.contains(uniqueValue)) isDuplicateAccount = true;

		// Confirm a/c is not in DB
		if (isRefPriorityInDB(priority, customerAgreementId)) isDuplicateAccount = true;

		if (!isDuplicateAccount) {
			acpriority_agrrefSet.add(uniqueValue);
			return null;
		} else {
			return "customer.auxaccount.error.unique.priority";
		}
	}

	// **************************************************************************************
	// ********** P R O C E S S U P L O A D      ********************************************
	@Override
	void processTransactionsPreProcessing() {
		setupDataMapping();
		processAaccountList = new ArrayList<AuxAccountCsvData>();
		auxAccountDataList = new ArrayList<AuxAccountData>();
		checkPreLoadedTables();
	}

	@Override
	void addToProcessList(String thisLine) {
		AuxAccountCsvData aacd = new AuxAccountCsvData(csvFieldMap, thisLine, false);
		processAaccountList.add(aacd);
		auxAccountDataList.add(createAuxAccount(aacd));
	}

	private AuxAccountData createAuxAccount(AuxAccountCsvData auxAccountRowData) {
		AuxAccountData auxAccountData = new AuxAccountData();
		HashMap<String, Object> properties = new HashMap<String, Object>();

		Long customerAgreementId = null;
		String identifierType = auxAccountRowData.getIdentifierType().toLowerCase();
		if (identifierType.equals(USAGEPOINT_NAME)) {
			UsagePoint up = usagePointService.getUsagePointByName(auxAccountRowData.getIdentifier());
			customerAgreementId = up.getCustomerAgreementId();
		} else if (identifierType.equals(METER_NUM)) {
			Meter m = meterService.getMeterByNumber(auxAccountRowData.getIdentifier());
			UsagePoint up = usagePointService.getUsagePointByMeterId(m.getId());
			customerAgreementId = up.getCustomerAgreementId();
		} else if (identifierType.equals(AGREEMENT_REF)) {
			CustomerAgreement ca = customerAgreementService.getCustomerAgreementByAgreementRef(auxAccountRowData.getIdentifier());
			customerAgreementId = ca.getId();
		}

		properties.put("customerAgreementId", customerAgreementId);

		AuxType auxType = getAuxTypeByName(auxAccountRowData.getAuxTypeName());
		properties.put("auxTypeId", auxType.getId());

		AuxChargeSchedule auxChargeScedule = getAuxChargeScheduleByName(auxAccountRowData.getChargeScheduleName());
		properties.put("auxChargeScheduleId", auxChargeScedule.getId());

		properties.put("accountName", auxAccountRowData.getAuxAccountName());
		properties.put("accountPriority", Integer.parseInt(auxAccountRowData.getAuxAccountPriority()));
		if (auxAccountRowData.getBalanceType().toLowerCase().equals(DEBT)) {
			properties.put("balance", new BigDecimal(auxAccountRowData.getBalance()).negate());
		} else if (auxAccountRowData.getBalanceType().toLowerCase().equals(REFUND)) {
			properties.put("balance", new BigDecimal(auxAccountRowData.getBalance()));
		}
		properties.put("recordStatus", RecordStatus.ACT);

		Date suspendDate = null;
		if (auxAccountRowData.getSuspendUntil() != null) {
			try {
			    suspendDate = dateFormatter.parse(auxAccountRowData.getSuspendUntil());
				properties.put("suspendUntil", suspendDate);
			} catch (Exception e) {
				logger.debug("Suspend Until!! exception = " + e);
				return null;
			}
		}

		Date startDate = null;
		if (auxAccountRowData.getStartDate() != null) {
		    try {
		        startDate = dateFormatter.parse(auxAccountRowData.getStartDate());
		        properties.put("startDate", startDate);
		    } catch (Exception e) {
		        logger.debug("Start Date!! exception = " + e);
		        return null;
		    }
		}
		
		if (auxAccountRowData.getPrincipleAmount() != null) {
			properties.put("principleAmount", new BigDecimal(auxAccountRowData.getPrincipleAmount()));
		}
		
		auxAccountData.setMrid(MridUtil.getMrid());
		auxAccountData.setMridExternal(false);
		
		try {
			BeanUtils.populate(auxAccountData, properties);
		} catch (Exception e) {
			logger.debug("AuxAccountBulkUploadController: createAuxAccount: Beanutils failed!! exception = " + e);
			return null;
		}
		logger.debug("new AuxAccount " + auxAccountData.getAccountName());
		return auxAccountData;
	}

	@Override
	String processUpload() {
		logger.info("**** Bulk AuxAccount upload input listSize=" + auxAccountDataList.size());
		return bulkUploadService.auxAccountDataBulkUpload(auxAccountDataList);
	}

	// Load common lists from the DB. This values are re/set per upload.
	private void checkPreLoadedTables() {

		auxTypeList = new ArrayList<AuxType>();
		AuxTypeExample auxTypeExample = new AuxTypeExample();
		auxTypeExample.createCriteria().andRecordStatusEqualTo(RecordStatus.ACT);
		auxTypeList = (ArrayList<AuxType>) auxTypeMapper.selectByExample(auxTypeExample);

		auxChargeScheduleList = new ArrayList<AuxChargeSchedule>();
		auxChargeScheduleList = (ArrayList<AuxChargeSchedule>) auxChargeScheduleService
				.getAllActiveAuxChargeSchedules(getUser().getCurrentGroupId());

		auxAccountDBList = new ArrayList<AuxAccount>();
		AuxAccountExample auxAccntExample = new AuxAccountExample();
		auxAccountDBList = (ArrayList<AuxAccount>)auxAccountMapper.selectByExample(auxAccntExample);
		
	}

	private AuxType getAuxTypeByName(String name) {
		for (AuxType at : auxTypeList) {
			if (at.getName().toLowerCase().equals(name.toLowerCase())) return at;
		}
		return null;
	}

	private AuxChargeSchedule getAuxChargeScheduleByName(String name) {
		for (AuxChargeSchedule acs : auxChargeScheduleList) {
			if (acs.getScheduleName().toLowerCase().equals(name.toLowerCase())) return acs;
		}
		return null;
	}

	private boolean isAuxAccountInDB(String acName, Long agreementId) {
		for (AuxAccount ac : auxAccountDBList) {
			if (ac.getAccountName().toLowerCase().equals(acName.toLowerCase()) && 
					ac.getCustomerAgreementId().equals(agreementId)) return true;
		}
		return false;
	}
	
	private boolean isRefPriorityInDB(Integer priority, Long agreementId) {
		for (AuxAccount ac : auxAccountDBList) {
			if (ac.getAccountPriority().equals(priority) && ac.getCustomerAgreementId().equals(agreementId)) return true;
		}
		return false;
	}

}
