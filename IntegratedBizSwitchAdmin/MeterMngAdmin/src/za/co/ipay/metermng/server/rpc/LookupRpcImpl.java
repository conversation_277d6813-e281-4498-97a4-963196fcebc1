package za.co.ipay.metermng.server.rpc;

import org.apache.log4j.Logger;
import za.co.ipay.gwt.common.shared.LookupListItem;
import za.co.ipay.gwt.common.shared.dto.IdNameDto;
import za.co.ipay.gwt.common.shared.exception.ServiceException;
import za.co.ipay.metermng.client.rpc.LookupRpc;
import za.co.ipay.metermng.server.mybatis.service.LookupService;
import za.co.ipay.metermng.shared.MeterMngStatics;
import za.co.ipay.metermng.shared.dto.PtrScreenDataDto;
import za.co.ipay.metermng.shared.dto.meter.AddMeterReadingScreenDataDto;
import za.co.ipay.metermng.shared.dto.meter.AddSuperMeterReadingScreenDataDto;
import za.co.ipay.metermng.shared.dto.usagepoint.MeterUpMdcChannelInfo;
import za.co.ipay.metermng.shared.dto.user.MeterMngUser;

import java.util.ArrayList;
import java.util.Date;

public class LookupRpcImpl extends BaseMeterMngRpc implements LookupRpc {

    private static final long serialVersionUID = 1L;

    private LookupService lookupService;

    public LookupRpcImpl() {
        logger = Logger.getLogger(LookupRpcImpl.class);
    }

    @Override
    public ArrayList<LookupListItem> getPricingStructureLookupList(Long serviceResourceId, Long meterTypeId,
            ArrayList<Long> paymentModeIds, boolean hasTariffCurrentlyRunning) throws ServiceException {
        return lookupService.getAvailablePricingStructures(getUser().getCurrentGroupId(), serviceResourceId,
                meterTypeId, paymentModeIds, hasTariffCurrentlyRunning);
    }

    @Override
    public ArrayList<LookupListItem> getAvailablePricingStructuresByMeterModelId(Long meterModelId, boolean hasTariffCurrentlyRunning) throws ServiceException {
        return lookupService.getAvailablePricingStructuresByMeterModelId(getUser().getCurrentGroupId(), meterModelId, hasTariffCurrentlyRunning);
    }

    @Override
    public ArrayList<LookupListItem> getPricingStructureLookupList(boolean hasTariffCurrentlyRunning) throws ServiceException {
        return lookupService.getAvailablePricingStructures(getUser().getCurrentGroupId(), hasTariffCurrentlyRunning);
    }
    
    @Override
    public ArrayList<LookupListItem> getPricingStructureLookupListFromMeterModel(Long meterModelId, boolean hasTariffCurrentlyRunning) throws ServiceException {
        return lookupService.getPricingStructureLookupListFromMeterModel(getUser().getCurrentGroupId(), meterModelId, hasTariffCurrentlyRunning);
    }

    @Override
    public ArrayList<LookupListItem> getAlgCodeLookupList() {
        return lookupService.getAvailableAlgCodes();
    }

    @Override
    public ArrayList<LookupListItem> getTtCodeLookupList() {
        return lookupService.getAvailableTtCodes();
    }

    @Override
    public ArrayList<LookupListItem> getSgKrnLookupList() {
        return lookupService.getAvailableSupplyGroupCodes();
    }
    
    @Override
    public ArrayList<LookupListItem> getSgKrnLookupList(boolean excludeExpired){
        return lookupService.getAvailableSupplyGroupCodes(excludeExpired);
    }

    @Override
    public ArrayList<LookupListItem> getAuxTypeLookupList() {
        return lookupService.getAvailableAuxTypes();
    }

    @Override
    public ArrayList<LookupListItem> getAuxChargeScheduleLookupList() throws ServiceException {
        MeterMngUser user = getUser();
        return lookupService.getAvailableAuxChargeSchedules(user.getCurrentGroupId());
    }

    @Override
    public ArrayList<LookupListItem> getAuxAccountsLookupList(Long customerId) {
        return lookupService.getAvailableAuxAccounts(customerId);
    }

    @Override
    public ArrayList<LookupListItem> getEndDeviceStoresLookupList() throws ServiceException {
        return lookupService.getAvailableEndDeviceStores(getUser().getCurrentGroupId(), getUser().getSessionGroupId());
    }

    @Override
    public ArrayList<LookupListItem> getMeterTypeLookupList() {
        return lookupService.getAvailableMeterTypes();
    }

    @Override
    public ArrayList<LookupListItem> getMeterModelLookupList() {
        return lookupService.getAvailableMeterModels();
    }

    @Override
    public ArrayList<LookupListItem> getMeterModelByPricingStructureIdLookupList(Long pricingStructureId) {
        return lookupService.getAvailableMeterModelsByPricingStructureId(pricingStructureId);
    }

    @Override
    public ArrayList<LookupListItem> getTouSeasonLookupList() {
        return lookupService.getAvailableSeasons(true);
    }

    @Override
    public PtrScreenDataDto getPtrScreenData() throws ServiceException {
        return lookupService.getPtrScreenData();
    }

    @Override
    public AddMeterReadingScreenDataDto getAddMeterReadingScreenData() throws ServiceException {
        return lookupService.getAddMeterReadingScreenData();
    }
    
    @Override
    public AddSuperMeterReadingScreenDataDto getAddSuperMeterReadingScreenData() throws ServiceException {
        return lookupService.getAddSuperMeterReadingScreenData();
    }
    
    @Override
    public ArrayList<IdNameDto> getPaymentModes() throws ServiceException {
        return lookupService.getPaymentModes();
    }

    @Override
    public MeterUpMdcChannelInfo getMeterUpMdcChannelInfo(MeterUpMdcChannelInfo meterUpMdcChannelInfo) {
        return lookupService.getMeterUpMdcChannelInfo(meterUpMdcChannelInfo);
    }
    
    @Override
    public ArrayList<LookupListItem> getBlockingTypeLookupList() throws ServiceException {
        return lookupService.getAllBlockingTypes();
    }

    @Override
    public ArrayList<LookupListItem> getPricingStructureLookupListForStartDate(Date startDate) throws ServiceException {
        return lookupService.getPricingStructureLookupListForStartDate(getUser().getCurrentGroupId(), startDate);
    }
    
    @Override
    public ArrayList<LookupListItem> getDeviceStoreLookupListForAccessGroup(Long accessGroupId) throws ServiceException {
        boolean allRegionsPerm = getUser().getPermissions().contains(MeterMngStatics.ADMIN_PERMISSION_MM_STORE_MOVEMENT_DIRECT.toString());
        return lookupService.getDeviceStoreLookupListForAccessGroup(getUser().getCurrentGroupId(), accessGroupId, allRegionsPerm);
    }
    
    //------------------------------------------------------------------------------------------------------------------------
    public void setLookupService(LookupService lookupService) {
        this.lookupService = lookupService;
    }
}
