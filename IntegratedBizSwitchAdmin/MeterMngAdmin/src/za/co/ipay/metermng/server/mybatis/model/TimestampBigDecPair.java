/**
 *
 */
package za.co.ipay.metermng.server.mybatis.model;

import java.io.Serializable;
import java.math.BigDecimal;
import java.sql.Date;

/**
 * <AUTHOR>
 *
 */
public class TimestampBigDecPair implements Serializable {

    /**
     *
     */
    private static final long serialVersionUID = 1L;

    private Date timestamp;
    private BigDecimal bigDecValue;

    /**
     * @return the timestamp
     */
    public Date getTimestamp() {
        return this.timestamp;
    }

    /**
     * @param timestamp the timestamp to set
     */
    public void setTimestamp(Date timestamp) {
        this.timestamp = timestamp;
    }

    /**
     * @return the bigDecValue
     */
    public BigDecimal getBigDecValue() {
        return this.bigDecValue;
    }

    /**
     * @param bigDecValue the bigDecValue to set
     */
    public void setBigDecValue(BigDecimal bigDecValue) {
        this.bigDecValue = bigDecValue;
    }
}
