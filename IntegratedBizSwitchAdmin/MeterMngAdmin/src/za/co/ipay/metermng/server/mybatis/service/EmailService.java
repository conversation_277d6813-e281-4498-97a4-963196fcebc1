package za.co.ipay.metermng.server.mybatis.service;

import java.io.File;
import java.util.ArrayList;
import java.util.List;

import org.apache.commons.mail.EmailAttachment;
import org.apache.commons.mail.EmailException;
import org.apache.commons.mail.MultiPartEmail;
import org.apache.log4j.Logger;

import za.co.ipay.email.service.EmailServiceImpl;
import za.co.ipay.exception.RemoteServiceException;
import za.co.ipay.metermng.shared.dto.user.UserData;

/**
 * EmailService uses the Tom<PERSON>'s mail session to send emails.
 *
 * <AUTHOR>
 *
 */
public class EmailService extends EmailServiceImpl {

    private static Logger logger = Logger.getLogger(EmailService.class);

    public void sendEmail(String fromName, String fromEmail, UserData to, String subject, String message, File[] attachments) throws RemoteServiceException {
        List<UserData> users = new ArrayList<UserData>();
        users.add(to);
        sendEmail(fromName, fromEmail, users, subject, message, attachments);
    }

    public void sendEmail(String fromName, String fromEmail, List<UserData> to, String subject, String message, File[] attachments) throws RemoteServiceException {
        if (session == null) {
            throw new RemoteServiceException("No mail session available to send emails!");
        }

        logger.debug("Sending email to: " + to + " subject:" + subject);
        try {
            // Create the email message
            MultiPartEmail email = new MultiPartEmail();

            //set the email's details
            for (UserData u : to) {
                email.addTo(u.getEmail());
                logger.info("Sending to: " + u.getEmail());
            }
            email.setFrom(fromEmail, fromName);
            email.setSubject(subject);
            email.setMsg(message);

            // Create the attachments
            if (attachments != null) {
                for (File file : attachments) {
                    EmailAttachment attachment = new EmailAttachment();
                    attachment.setPath(file.getAbsolutePath());
                    attachment.setDisposition(EmailAttachment.ATTACHMENT);
                    attachment.setName(file.getName());
                    email.attach(attachment);
                }
            }

            //Send email using the session
            email.setMailSession(session);
            email.send();
            logger.debug("Sent email to: " + to + " subject:" + subject);

        } catch (EmailException e) {
            logger.error("Error sending email:", e);
            throw new RemoteServiceException("Unable to send email for subject: " + subject);
        }
    }
}
