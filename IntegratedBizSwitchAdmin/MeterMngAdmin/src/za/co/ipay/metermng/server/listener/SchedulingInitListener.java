package za.co.ipay.metermng.server.listener;

import javax.servlet.ServletContextEvent;
import javax.servlet.ServletContextListener;

import org.apache.log4j.Logger;
import org.springframework.context.ApplicationContext;
import org.springframework.web.context.support.WebApplicationContextUtils;

import za.co.ipay.metermng.server.mybatis.service.SchedulingService;

/**
 * SchedulingInitListener is used to set up and shut down the application's scheduled tasks. This listener needs to be
 * defined in the web.xml file. It is called on start up of the Spring context and when the context is destroyed.
 * 
 * <AUTHOR>
 */
public class SchedulingInitListener implements ServletContextListener {
    
    private SchedulingService schedulingService;

    private static Logger logger = Logger.getLogger(SchedulingInitListener.class);
    
    @Override
    public void contextInitialized(ServletContextEvent event) {
        ApplicationContext ac = WebApplicationContextUtils.getRequiredWebApplicationContext(event.getServletContext());
        this.schedulingService = (SchedulingService) ac.getBean("schedulingService");
        logger.info("Set schedulingservice: "+schedulingService);
        
        logger.info("Starting scheduling...");
        schedulingService.startScheduling();
        logger.info("Started scheduling.");
    }
    
    @Override
    public void contextDestroyed(ServletContextEvent event) {
        if (schedulingService != null) {
            logger.info("Shutting down scheduling...");
            schedulingService.shutDownScheduling();
            logger.info("Shut down scheduling.");
        }
    }
}
