package za.co.ipay.metermng.server.mybatis.mapper;

import java.sql.Date;
import java.util.ArrayList;

import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Select;

import za.co.ipay.metermng.shared.dto.ItemValuePairDto;

public interface IUsagePointGroupsMapper {
    @Select("SELECT date_rec_modified "
            + "FROM group_hierarchy gh "
            + "LEFT OUTER JOIN gen_group gg "
            + "ON gg.group_hierarchy_id = gh.group_hierarchy_id "
            + "LEFT OUTER JOIN gen_group_hist ggh "
            + "ON gg.gen_group_id = ggh.gen_group_id "
            + "WHERE group_type_id = #{GROUP_TYPE_ID} "
            + "AND gh.parent_id is null "
            + "AND ggh.user_action = 'insert' "
            + "AND date_rec_modified > #{startDate}")
    public ArrayList<Date> getFirstLevelGroupsAdded(@Param("GROUP_TYPE_ID") Long GROUP_TYPE_ID,
            @Param("startDate") Date startDate);

    @Select("SELECT date_rec_modified "
            + "FROM group_hierarchy gh "
            + "LEFT OUTER JOIN gen_group gg "
            + "ON gg.group_hierarchy_id = gh.group_hierarchy_id "
            + "LEFT OUTER JOIN gen_group_hist ggh "
            + "ON gg.gen_group_id = ggh.gen_group_id "
            + "WHERE group_type_id = #{GROUP_TYPE_ID} "
            + "AND gh.parent_id = #{ownersGroupHierarchyId} "
            + "AND ggh.user_action = 'insert' "
            + "AND date_rec_modified > #{startDate}")
    public ArrayList<Date> getSubLevelGroupsAdded(@Param("GROUP_TYPE_ID") Long GROUP_TYPE_ID,
            @Param("startDate") Date startDate,
            @Param("ownersGroupHierarchyId") Long ownersGroupHierarchyId);

    @Select("SELECT group_type_name AS item, group_type_name AS value "
            + "FROM group_type "
            + "WHERE record_status = 'ACT' "
            + "AND access_group = biz_false "
            + "AND location_group = biz_false")
    @ResultMap("za.co.ipay.metermng.server.mybatis.mapper.IUsagePointGroupsMapper.UsagePointGroupsListMap")
    public ArrayList<ItemValuePairDto> getUsagePointGroupsList();
}