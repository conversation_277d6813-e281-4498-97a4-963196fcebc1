package za.co.ipay.metermng.server.mybatis.mapper;

import java.util.Date;
import java.util.List;
import java.util.Map;

import org.apache.log4j.Logger;

import com.google.gwt.i18n.client.DateTimeFormat;

import za.co.ipay.utils.CalendarUtils;

/**
 * GroupSearchQueryBuilder is used to build up the queries used to look for combinations of group selections
 * <AUTHOR>
 */
public class GroupSearchQueryBuilder {
    
    private static Logger logger = Logger.getLogger(GroupSearchQueryBuilder.class);
    
    public static String getMeterOnlineBulkKernelDataByGenGroupLeafNodesCount(Map<String, Object> params) {   
        return performOnlineBulkDataSearch(params, true);
    }    
                         
    public static String selectMeterOnlineBulkKernelDataByGenGroupLeafNodes(Map<String, Object> params) { 
        return performOnlineBulkDataSearch(params, false);
    }
    
    @SuppressWarnings("unchecked")
    private static String performOnlineBulkDataSearch(Map<String, Object> params, boolean count) {    
        List<Long> selGrpIdList = (List<Long>) params.get("selGrpIdList");
        String sortColumn = (String) params.get("sortColumn");
        String filterColumn = (String) params.get("filterColumn");
        //had to use two objects filterString & filterDate because 'java.lang.Object' has no instantiable subtypes
        String filterString = (String) params.get("filterString");   
        Date filterDate = (Date) params.get("filterDate");
        String order = (String) params.get("order");
        
        logger.debug("performOnlineBulkDataSearch: selGrpIdList.size= " + selGrpIdList.size() + "  grpIdList= " + selGrpIdList + "  sortColumn= " + sortColumn + "  order= " + order);
        
        StringBuilder sbSelect = new StringBuilder();
        StringBuilder sbWhere = new StringBuilder();
        
        // Add pricing_structure_name only if we need it
        boolean needPs = "ps.pricing_structure_name".equalsIgnoreCase(sortColumn)
                || "ps.pricing_structure_name".equalsIgnoreCase(filterColumn);
        
        if (count) {
            //Select count only
            sbSelect.append("select count(1) ");
        } else {
            //Select
            sbSelect.append("select up.usage_point_id, up.usage_point_name, up.installation_date, ");
            sbSelect.append("up.customer_agreement_id, up.service_location_id, up.record_status, ");
            sbSelect.append("me.meter_id, me.meter_num, me.end_device_store_id, me.breaker_id, me.replace_reason_log_id, me.enc_key, ");
            sbSelect.append("me.meter_uri_address, me.meter_uri_port, me.meter_uri_protocol, me.meter_uri_params, ");
            sbSelect.append("st.sts_curr_supply_group_id, st.sts_curr_supply_group_code, st.sts_token_tech_id, ");
            sbSelect.append("st.sts_algorithm_code_id, st.sts_curr_key_revision_num, st.sts_curr_tariff_index, ");
            sbSelect.append("mt.meter_type_id, mt.meter_type_name, ");
            sbSelect.append("mm.meter_model_id, mm.model_name, mm.mdc_id, ");
            sbSelect.append("cu.surname, cu.phone1, ");
            sbSelect.append("lo.suite_num, ");
            sbSelect.append("ds.store_name ");
            if (needPs) {
                sbSelect.append(", ps.pricing_structure_name ");
            }
        }
        
        //From
        sbSelect.append("from up_gen_group_lnk a ");
        sbSelect.append("join usage_point up on up.usage_point_id=a.usage_point_id ");
        sbSelect.append("left join meter me on me.meter_id=up.meter_id ");
        sbSelect.append("left join sts_meter st on st.meter_id=me.meter_id ");
        sbSelect.append("left join meter_type mt on mt.meter_type_id=me.meter_type_id ");
        sbSelect.append("left join meter_model mm on mm.meter_model_id=me.meter_model_id ");
        sbSelect.append("left join customer_agreement ca on ca.customer_agreement_id=up.customer_agreement_id ");
        sbSelect.append("left join customer cu on cu.customer_id=ca.customer_id ");
        sbSelect.append("left join location lo on lo.location_id=up.service_location_id ");
        sbSelect.append("left join end_device_store ds on ds.end_device_store_id=me.end_device_store_id ");
                
        if (needPs) {
            sbSelect.append("left join up_pricing_structure ups on ups.usage_point_id = up.usage_point_id ")
                    .append("left join pricing_structure ps on ps.pricing_structure_id = ups.pricing_structure_id ");
        }
        
        //Where
        String sbAndOrWhere = " ";
        if (selGrpIdList.size() > 0) {
            sbWhere.append("where a.gen_group_id = ").append(selGrpIdList.get(0)).append(" ");

            for (int i = 1; i < selGrpIdList.size(); i++) {
                if (i > 26) {
                    //ERROR! TODO
                }
                char lower = (char) ('a' + i);
                sbSelect.append("join up_gen_group_lnk ").append(lower).append(" ")
                .append("on a.usage_point_id = ").append(lower).append(".usage_point_id ")
                .append("and a.up_gen_group_lnk_id != ").append(lower).append(".up_gen_group_lnk_id ");

                sbWhere.append("and ").append(lower).append(".gen_group_id = ").append(selGrpIdList.get(i)).append(" ");
            }
            sbAndOrWhere = "and ";
        } else {
            sbAndOrWhere = "where ";
        }
        
        if (filterColumn != null && filterString != null) { 
            sbWhere.append(sbAndOrWhere).append(filterColumn).append(" like '%").append(filterString).append("%' ");

        } else if (filterColumn != null && filterColumn.toLowerCase().contains("date") && filterDate != null) {
                DateTimeFormat sdf = DateTimeFormat.getFormat("yyyy-MM-dd HH:mm:ss");
            
                Date dtFrom = new Date(CalendarUtils.clearTime(filterDate).getTime());
                String dtFromStr = sdf.format(dtFrom);

                Date dtTo = new Date(dtFrom.getTime());
                String dtToStr = sdf.format(CalendarUtils.addDays(dtTo, 1));
                
                logger.info("dtFrom=" + dtFromStr + " dtTo=" + dtToStr);
                sbWhere.append(sbAndOrWhere).append(filterColumn).append(" >= '").append(dtFromStr).append("' ");
                sbWhere.append("and ").append(filterColumn).append(" < '").append(dtToStr).append("' ");
        } 
        
        sbSelect.append(sbWhere);
        
        //order by
        if (!count && sortColumn != null) {
            sbSelect.append("order by ").append(sortColumn).append(" ").append(order);
        }
        
        String selectString = sbSelect.toString();
        logger.debug("performOnlineBulkDataSearch sqlStmt= " + selectString);
        return selectString;
    }
}
