package za.co.ipay.metermng.server.mybatis.mapper;

import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Select;
import za.co.ipay.metermng.server.mybatis.model.StringBigDecPair;

import java.math.BigDecimal;
import java.sql.Date;
import java.util.List;

public interface IBuyingIndexMapper {
    @Select("SELECT COUNT(DISTINCT usage_point_id) FROM ( " +
            "SELECT uph.usage_point_id,uph.date_rec_modified,uph.record_status " +
            "FROM (SELECT usage_point_id,max(date_rec_modified) lastdatemodified " +
            "FROM usage_point_hist uph WHERE meter_id IS NOT NULL " +
            "AND gen_group_id = #{GROUP_TYPE_ID} " +
            "AND date_rec_modified < #{startDate} " +
            "GROUP BY usage_point_id) x " +
            "INNER JOIN usage_point_hist uph ON uph.usage_point_id = x.usage_point_id " +
            "AND uph.date_rec_modified = x.lastdatemodified " +
            "UNION ALL " +
            "SELECT usage_point_id,date_rec_modified,record_status " +
            "FROM usage_point_hist WHERE meter_id IS NOT NULL " +
            "AND gen_group_id = #{GROUP_TYPE_ID} " +
            "AND date_rec_modified >= #{startDate} " +
            "AND date_rec_modified < #{endDate}) bar " +
            "WHERE record_status = 'ACT'")
    BigDecimal getActiveUsagePointCountForTimePeriod(@Param("GROUP_TYPE_ID") Long GROUP_TYPE_ID,
                                                           @Param("startDate") Date startDate,
                                                           @Param("endDate") Date endDate);

    @Select("SELECT COUNT(DISTINCT usage_point_id) count, extract(year from candidates.trans_date) || '-' || extract(month from candidates.TRANS_DATE) tsdata " +
            "FROM (SELECT ct.usage_point_id, up.gen_group_id, trans_date " +
            "FROM customer_trans ct " +
            "INNER JOIN usage_point up " +
            "ON ct.usage_point_id = up.usage_point_id " +
            "WHERE trans_date >= #{startDate} " +
            "AND ct.customer_trans_type_id != 2 " +
            "AND up.gen_group_id = #{GROUP_TYPE_ID} AND reversed = biz_false) candidates " +
            "GROUP BY extract(year from candidates.trans_date) || '-' || extract(month from candidates.TRANS_DATE)")
    @ResultMap("za.co.ipay.metermng.server.mybatis.mapper.IBuyingIndexMapper.BuyingIndexMap")
    List<StringBigDecPair> getCountOfUniqueTransactingMetersPerMonth(@Param("GROUP_TYPE_ID") Long GROUP_TYPE_ID,
                                                                                 @Param("startDate") Date startDate);
}




