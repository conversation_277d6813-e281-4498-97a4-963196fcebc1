package za.co.ipay.metermng.server.mybatis.service;

import java.lang.reflect.UndeclaredThrowableException;
import java.sql.SQLException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import org.apache.ibatis.session.RowBounds;
import org.apache.log4j.Logger;
import org.springframework.transaction.annotation.Transactional;

import za.co.ipay.gwt.common.server.util.ExposedReloadableResourceBundleMessageSource;
import za.co.ipay.gwt.common.shared.dto.IdNameDto;
import za.co.ipay.gwt.common.shared.exception.ServiceException;
import za.co.ipay.gwt.common.shared.exception.ValidationException;
import za.co.ipay.gwt.common.shared.exception.ValidationMessage;
import za.co.ipay.ipayxml.mdc.ControlReqMessage;
import za.co.ipay.ipayxml.mdc.ControlReqMessage.ControlType;
import za.co.ipay.ipayxml.mdc.ControlReqMessage.Param;
import za.co.ipay.ipayxml.mdc.Override;
import za.co.ipay.ipayxml.metermng.InspectionReqMessage;
import za.co.ipay.ipayxml.metermng.InspectionResMessage;
import za.co.ipay.metermng.cim.MridUtil;
import za.co.ipay.metermng.datatypes.CustomerTransTypeE;
import za.co.ipay.metermng.datatypes.CyclicChargeCalcAtE;
import za.co.ipay.metermng.datatypes.MeterTypeE;
import za.co.ipay.metermng.datatypes.RecordStatus;
import za.co.ipay.metermng.event.DefaultEventNotificationService;
import za.co.ipay.metermng.event.EventNotificationService;
import za.co.ipay.metermng.event.types.DeviceMovementEvent;
import za.co.ipay.metermng.event.types.DeviceMovementEvent.MovementTypeE;
import za.co.ipay.metermng.ipayxml.IpayXmlMessageService;
import za.co.ipay.metermng.mybatis.custom.mapper.TariffSupportMapper;
import za.co.ipay.metermng.mybatis.custom.mapper.UsagePointCustomMapper;
import za.co.ipay.metermng.mybatis.custom.mapper.VendMapper;
import za.co.ipay.metermng.mybatis.custom.model.TariffWithCalcClass;
import za.co.ipay.metermng.mybatis.generated.mapper.AppSettingMapper;
import za.co.ipay.metermng.mybatis.generated.mapper.ChargeWriteoffMapper;
import za.co.ipay.metermng.mybatis.generated.mapper.CustomerTransMapper;
import za.co.ipay.metermng.mybatis.generated.mapper.MeterMapper;
import za.co.ipay.metermng.mybatis.generated.mapper.MeterReadingMapper;
import za.co.ipay.metermng.mybatis.generated.mapper.PricingStructureMapper;
import za.co.ipay.metermng.mybatis.generated.mapper.SpecialActionReasonsLogMapper;
import za.co.ipay.metermng.mybatis.generated.mapper.TransItemTypeMapper;
import za.co.ipay.metermng.mybatis.generated.mapper.UnitsAccountMapper;
import za.co.ipay.metermng.mybatis.generated.mapper.UnitsTransMapper;
import za.co.ipay.metermng.mybatis.generated.mapper.UpGenGroupLnkMapper;
import za.co.ipay.metermng.mybatis.generated.mapper.UpMeterInstallMapper;
import za.co.ipay.metermng.mybatis.generated.mapper.UpPricingStructureHistMapper;
import za.co.ipay.metermng.mybatis.generated.mapper.UpPricingStructureMapper;
import za.co.ipay.metermng.mybatis.generated.mapper.UsagePointMapper;
import za.co.ipay.metermng.mybatis.generated.model.AppSetting;
import za.co.ipay.metermng.mybatis.generated.model.BlockingType;
import za.co.ipay.metermng.mybatis.generated.model.Customer;
import za.co.ipay.metermng.mybatis.generated.model.CustomerAgreement;
import za.co.ipay.metermng.mybatis.generated.model.CustomerTrans;
import za.co.ipay.metermng.mybatis.generated.model.CustomerTransExample;
import za.co.ipay.metermng.mybatis.generated.model.CustomerTransItem;
import za.co.ipay.metermng.mybatis.generated.model.GenGroup;
import za.co.ipay.metermng.mybatis.generated.model.GroupType;
import za.co.ipay.metermng.mybatis.generated.model.Location;
import za.co.ipay.metermng.mybatis.generated.model.Meter;
import za.co.ipay.metermng.mybatis.generated.model.MeterReadingExample;
import za.co.ipay.metermng.mybatis.generated.model.PricingStructure;
import za.co.ipay.metermng.mybatis.generated.model.SpecialActionReasonsLog;
import za.co.ipay.metermng.mybatis.generated.model.TransItemType;
import za.co.ipay.metermng.mybatis.generated.model.UnitsAccount;
import za.co.ipay.metermng.mybatis.generated.model.UnitsAccountExample;
import za.co.ipay.metermng.mybatis.generated.model.UnitsTrans;
import za.co.ipay.metermng.mybatis.generated.model.UnitsTransExample;
import za.co.ipay.metermng.mybatis.generated.model.UpGenGroupLnk;
import za.co.ipay.metermng.mybatis.generated.model.UpMeterInstall;
import za.co.ipay.metermng.mybatis.generated.model.UpMeterInstallExample;
import za.co.ipay.metermng.mybatis.generated.model.UpPricingStructure;
import za.co.ipay.metermng.mybatis.generated.model.UpPricingStructureExample;
import za.co.ipay.metermng.mybatis.generated.model.UpPricingStructureHist;
import za.co.ipay.metermng.mybatis.generated.model.UpPricingStructureHistExample;
import za.co.ipay.metermng.mybatis.generated.model.UsagePoint;
import za.co.ipay.metermng.mybatis.generated.model.UsagePointExample;
import za.co.ipay.metermng.mybatis.generated.model.UsagePointExample.Criteria;
import za.co.ipay.metermng.server.mybatis.mapper.AccessGroupMapper;
import za.co.ipay.metermng.server.mybatis.mapper.ReadingsCustomMapper;
import za.co.ipay.metermng.shared.ChannelCompatibilityE;
import za.co.ipay.metermng.shared.ChargeWriteoffData;
import za.co.ipay.metermng.shared.CustomerTransItemData;
import za.co.ipay.metermng.shared.CustomerTransItemOutstandCharges;
import za.co.ipay.metermng.shared.IpayResponseData;
import za.co.ipay.metermng.shared.MeterMngStatics;
import za.co.ipay.metermng.shared.appsettings.AppSettings;
import za.co.ipay.metermng.shared.dto.LocationData;
import za.co.ipay.metermng.shared.dto.MdcChannelMatchDto;
import za.co.ipay.metermng.shared.dto.MdcChannelReadingsDto;
import za.co.ipay.metermng.shared.dto.MeterData;
import za.co.ipay.metermng.shared.dto.UpGenGroupLinkData;
import za.co.ipay.metermng.shared.dto.UpPricingStructureData;
import za.co.ipay.metermng.shared.dto.UsagePointData;
import za.co.ipay.metermng.shared.dto.meter.MdcChannelDto;
import za.co.ipay.metermng.shared.dto.tariff.TariffPanelDto;
import za.co.ipay.metermng.shared.dto.usagepoint.UsagePointFetchDto;
import za.co.ipay.metermng.shared.tariff.TariffWithData;
import za.co.ipay.metermng.tariff.CyclicChargesResult;
import za.co.ipay.metermng.tariff.IThinSmartTariffCalculator;
import za.co.ipay.metermng.tariff.TariffRetrieval;
import za.co.ipay.metermng.tariff.cyclic.CyclicCharge;
import za.co.ipay.metermng.tariff.cyclic.UsagePointOutstandingCyclicCharges;
import za.co.ipay.metermng.tariff.thin.AccountAdjustmentResult;
import za.co.ipay.metermng.tariff.thin.IUnitsAccountAdjustmentProcessor;
import za.co.ipay.metermng.tariff.thin.IpayXmlMessageWithDelay;
import za.co.ipay.metermng.tariff.thin.UnitsAdjustment;
import za.co.ipay.utils.i18n.ITranslationAware;

public class UsagePointService {

    private UsagePointMapper usagePointMapper;
    private UsagePointCustomMapper usagePointCustomMapper;
    private UpPricingStructureMapper upPricingStructureMapper;
    private TariffSupportMapper tariffSupportMapper;
    private UpPricingStructureHistMapper upPricingStructureHistMapper;
    private PricingStructureMapper pricingStructureMapper;
    private LocationService locationService;
    private UpGenGroupLnkMapper upGenGroupLnkMapper;
    private UpMeterInstallMapper upMeterInstallMapper;
    private MeterService meterService;
    private STSMeterService stsMeterService;
    private PricingStructureService pricingStructureService;
    private MeterReadingGeneratorService meterReadingGeneratorService;
    private MeterReadingMapper meterReadingMapper;
    private SpecialActionReasonsLogMapper specialActionReasonsLogMapper;
    private SpecialActionsService specialActionsService;
    private VendMapper vendMapper;
    private AppSettingMapper appSettingMapper;
    private ChargeWriteoffMapper chargeWriteoffMapper;
    private TransItemTypeMapper transItemTypeMapper;
    private RegisterReadingService registerReadingService;
    private BlockingTypeService blockingTypeService;
    private IpayXmlMessageService ipayXmlMessageService;
    private GroupService groupService;
    private EventNotificationService eventNotificationService;
    private MdcChannelService mdcChannelService;
    private UnitsTransMapper unitsTransMapper;
    private ITranslationAware translationAware;
    private IUnitsAccountAdjustmentProcessor unitsAccountAdjustmentProcessor;
    private CustomerService customerService;
    private MeterMapper meterMapper;
    private MeterModelService meterModelService;
    private ExposedReloadableResourceBundleMessageSource messageSource;
    private ReadingsCustomMapper readingsCustomMapper;
    private AppSettingService appSettingService;
    private CustomerAgreementService customerAgreementService;
    private UnitsAccountMapper unitsAccountMapper;
    private CustomerTransMapper customerTransMapper;
    private AccessGroupMapper accessGroupMapper;

    private static final Logger logger = Logger.getLogger(UsagePointService.class);
    public static final String CURRENT_PS = "current";
    public static final String FUTURE_PS = "future";

    public void setUsagePointMapper(UsagePointMapper usagePointMapper) {
        this.usagePointMapper = usagePointMapper;
    }

    public void setUsagePointCustomMapper(UsagePointCustomMapper usagePointCustomMapper) {
        this.usagePointCustomMapper = usagePointCustomMapper;
    }

    public void setUpPricingStructureMapper(UpPricingStructureMapper upPricingStructureMapper) {
        this.upPricingStructureMapper = upPricingStructureMapper;
    }

    public void setTariffSupportMapper(TariffSupportMapper tariffSupportMapper) {
        this.tariffSupportMapper = tariffSupportMapper;
    }

    public void setUpPricingStructureHistMapper(UpPricingStructureHistMapper upPricingStructureHistMapper) {
        this.upPricingStructureHistMapper = upPricingStructureHistMapper;    
    }
    
    public void setPricingStructureMapper(PricingStructureMapper pricingStructureMapper) {
        this.pricingStructureMapper = pricingStructureMapper;
    }

    public void setLocationService(LocationService locationService) {
        this.locationService = locationService;
    }

    public void setUpGenGroupLnkMapper(UpGenGroupLnkMapper upGenGroupLnkMapper) {
        this.upGenGroupLnkMapper = upGenGroupLnkMapper;
    }

    public void setMeterService(MeterService meterService) {
        this.meterService = meterService;
    }

    public void setStsMeterService(STSMeterService stsMeterService) {
        this.stsMeterService = stsMeterService;
    }

    public void setPricingStructureService(PricingStructureService pricingStructureService) {
        this.pricingStructureService = pricingStructureService;
    }

    public void setUpMeterInstallMapper(UpMeterInstallMapper upMeterInstallMapper) {
        this.upMeterInstallMapper = upMeterInstallMapper;
    }

    public void setRegisterReadingService(RegisterReadingService registerReadingService) {
        this.registerReadingService = registerReadingService;
    }

    public void setMeterReadingMapper(MeterReadingMapper meterReadingMapper) {
        this.meterReadingMapper = meterReadingMapper;
    }


    public void setMeterReadingGeneratorService(MeterReadingGeneratorService meterReadingGeneratorService) {
        this.meterReadingGeneratorService = meterReadingGeneratorService;
    }

    public void setBlockingTypeService(BlockingTypeService blockingTypeService) {
        this.blockingTypeService = blockingTypeService;
    }

    public void setSpecialActionReasonsLogMapper(SpecialActionReasonsLogMapper specialActionReasonsLogMapper) {
        this.specialActionReasonsLogMapper = specialActionReasonsLogMapper;
    }
    
    public void setSpecialActionsService(SpecialActionsService specialActionsService) {
        this.specialActionsService = specialActionsService;
    }

    public void setVendMapper(VendMapper vendMapper) {
        this.vendMapper = vendMapper;
    }

    public void setAppSettingMapper(AppSettingMapper appSettingMapper) {
        this.appSettingMapper = appSettingMapper;
    }

    public void setChargeWriteoffMapper(ChargeWriteoffMapper chargeWriteoffMapper) {
        this.chargeWriteoffMapper = chargeWriteoffMapper;
    }

    public void setTransItemTypeMapper(TransItemTypeMapper transItemTypeMapper) {
        this.transItemTypeMapper = transItemTypeMapper;
    }

    public void setIpayXmlMessageService(IpayXmlMessageService ipayXmlMessageService) {
        this.ipayXmlMessageService = ipayXmlMessageService;
    }

    public void setGroupService(GroupService groupService) {
        this.groupService = groupService;
    }

    public void setEventNotificationService(DefaultEventNotificationService eventNotificationService) {
        this.eventNotificationService = eventNotificationService;
    }
    
    public void setMdcChannelService(MdcChannelService mdcChannelService) {
        this.mdcChannelService = mdcChannelService;
    }
    
    public void setReadingsCustomMapper(ReadingsCustomMapper readingsCustomMapper) {
        this.readingsCustomMapper = readingsCustomMapper;
    }
    
    public void setAppSettingService(AppSettingService appSettingService) {
        this.appSettingService = appSettingService;        
    }

    public void setUnitsTransMapper(UnitsTransMapper unitsTransMapper) {
        this.unitsTransMapper = unitsTransMapper;
    }

    public void setTranslationAware(ITranslationAware translationAware) {
        this.translationAware = translationAware;
    }

    public void setUnitsAccountAdjustmentProcessor(IUnitsAccountAdjustmentProcessor unitsAccountAdjustmentProcessor) {
        this.unitsAccountAdjustmentProcessor = unitsAccountAdjustmentProcessor;
    }

    public void setCustomerService(CustomerService customerService) {
        this.customerService = customerService;
    }

    public void setMeterMapper(MeterMapper meterMapper) {
        this.meterMapper = meterMapper;
    }

    public void setMeterModelService(MeterModelService meterModelService) {
        this.meterModelService = meterModelService;
    }

    public void setMessageSource(ExposedReloadableResourceBundleMessageSource messageSource) {
        this.messageSource = messageSource;
    }

    public void setCustomerAgreementService(CustomerAgreementService customerAgreementService) {
        this.customerAgreementService = customerAgreementService;
    }

    public void setUnitsAccountMapper(UnitsAccountMapper unitsAccountMapper) {
        this.unitsAccountMapper = unitsAccountMapper;
    }

    public void setCustomerTransMapper(CustomerTransMapper customerTransMapper) {
        this.customerTransMapper = customerTransMapper;
    }

    public void setAccessGroupMapper(AccessGroupMapper accessGroupMapper) {
        this.accessGroupMapper = accessGroupMapper;
    }
    //----------------------------------------------------------------------------------------------------------------------

    @Transactional(readOnly = true)
    public UsagePoint getUsagePointByMeterId(Long meterId) {
        UsagePointExample usagePointExample = new UsagePointExample();
        usagePointExample.createCriteria().andMeterIdEqualTo(meterId);
        List<UsagePoint> list = usagePointMapper.selectByExample(usagePointExample);
        if (list == null || list.size() < 1) {
            return null;
        }
        return list.get(0);
    }

    @Transactional(readOnly = true)
    public UsagePoint getUsagePointByCustomerAgreementId(Long customerAgreementId) {
        UsagePointExample usagePointExample = new UsagePointExample();
        usagePointExample.createCriteria().andCustomerAgreementIdEqualTo(customerAgreementId);
        List<UsagePoint> list = usagePointMapper.selectByExample(usagePointExample);
        if (list == null || list.size() < 1) {
            return null;
        }
        return list.get(0);
    }

    @Transactional(readOnly = true)
    public ArrayList<UsagePoint> getUsagePointsByCustomerAgreementId(Long customerAgreementId) {
        UsagePointExample usagePointExample = new UsagePointExample();
        usagePointExample.createCriteria().andCustomerAgreementIdEqualTo(customerAgreementId);
        List<UsagePoint> list = usagePointMapper.selectByExample(usagePointExample);
        return new ArrayList<UsagePoint>(list);
    }

    @Transactional(readOnly = true)
    public UsagePoint getUsagePointByName(String usagePointName) {
        UsagePointExample usagePointExample = new UsagePointExample();
        usagePointExample.createCriteria().andNameEqualTo(usagePointName);
        List<UsagePoint> list = usagePointMapper.selectByExample(usagePointExample);
        if (list == null || list.size() < 1) {
            return null;
        }
        UsagePoint usagePoint = list.get(0);
        return usagePoint;
    }

    @Transactional(readOnly = true)
    public UsagePoint getUsagePointById(Long theId) {
        if (theId != null) {
            return usagePointMapper.selectByPrimaryKey(theId);
        } else {
            return null;
        }
    }

    @Transactional
    public UsagePoint prepAndUpdateByPrimaryKeySelective(UsagePoint record) throws ServiceException {
        //go via here to update the UsagePoint row to avoid overwriting the lastxxx fields when they might have been changed by something else in the interim
        
        if (record.getActivationDate() == null && record.getRecordStatus() == RecordStatus.ACT) {
            record.setActivationDate(record.getInstallationDate());
        }

        //reminder: these fields are set to null so that the updateByPrimaryKeySelective below does NOT update them
        record.setLastPurchaseDate(null);
        record.setLastCustomerTransId(null);
        record.setLastCyclicChargeDate(null);
        record.setLastDemandChargeDate(null);
        record.setLastMdcConnectControl(null);

        int updated = 0;
        String error = "usagepoint.save.error";
        try {
            updated = usagePointMapper.updateByPrimaryKeySelective(record);
        } catch(Exception e) {
            error = checkIfLicenseError(error, e);
        }

        if (updated != 1) {
            throw new ServiceException(error);
        }
        return usagePointMapper.selectByPrimaryKey(record.getId());
    }

    private static String checkIfLicenseError(String error, Exception e) {
        Throwable c = e;
        if (c instanceof UndeclaredThrowableException) {
            // When an unexpected exception is thrown via an rpc call, the actual cause of the exception is not
            // immediately clear. The exception caught is an UndeclaredThrowableException. Loop through the getCause()
            // method in the stacktrace looking for a sql exception. While testing it didn't seem consistent but
            // was within the first 4 getCause() calls so limiting it to 4 attempts.
            int i=4;
            while ( i >= 0 && !(c instanceof SQLException) ) {
                c = c.getCause();
                i--;
            }
        }
        if (c.getLocalizedMessage() != null
                && c.getLocalizedMessage().toLowerCase().contains("license check failed")) {
            error = "usagepoint.save.license.error";
        }
        return error;
    }

    @Transactional
    public UsagePointData updateUsagePoint(UsagePointData usagePointData, LocationData serviceLocation,
                                           Location usagePointLocation, List<MdcChannelReadingsDto> channelReadingsList) throws ValidationException, ServiceException {

        boolean isNewInstallDtSmallerEqualTOExisting = true;
        boolean isChangeInstallationDate = false;
        Long numReadings = getCountReadingsForMeter(usagePointData.getMeterId());
        
        Long usagePointId = usagePointData.getId();
        //get usagePointData by name!
        UsagePoint existing = getUsagePointByName(usagePointData.getName());
        if (existing != null) {
            if (!existing.getId().equals(usagePointId)) {
                throw new ValidationException(new ValidationMessage("error.field.usagepoint.name.duplicate", new String[]{usagePointData.getName()}, true));
            }
            //test to see if changed installation date on the new usagePointData, is there maybe NOW trans or readings  on the existing that came after the UI was opened
            numReadings = numReadings + getCountReadingsForUP(existing.getId());
            if (usagePointData.getInstallationDate() != null && existing.getInstallationDate() != null) {
                if (!usagePointData.getInstallationDate().equals(existing.getInstallationDate())) {
                    if (existing.getLastPurchaseDate() != null || existing.getLastCyclicChargeDate() != null || numReadings.compareTo(0L) != 0) {
                        throw new ServiceException("meter.change.installation.date.error.trans");
                    } else {
                        isChangeInstallationDate = true;
                        if (usagePointData.getInstallationDate().compareTo(existing.getInstallationDate()) > 0) {
                            isNewInstallDtSmallerEqualTOExisting = false;
                        }
                    }
                } 
            }
        }
        
        //check for duplicate mrid
        if (getMridExistence(usagePointData.getMrid(), usagePointData.getId())) {
            throw new ValidationException(new ValidationMessage("up.mrid.external.unique.validation", true));
        }
        
        //if new already greater than current, no need for further installdate checks
        if (usagePointData.getInstallationDate() != null && isChangeInstallationDate && !isNewInstallDtSmallerEqualTOExisting) {
            checkInstallationDate(usagePointId, usagePointData.getMeterId(), usagePointData.getInstallationDate());
        }
        
        boolean isFirstEverActivation = false;
        if (usagePointData.getRecordStatus() == RecordStatus.ACT && usagePointData.getActivationDate() == null) {   
            usagePointData.setActivationDate(usagePointData.getInstallationDate());
            isFirstEverActivation = true;
        }
        
        if (usagePointData.isChangeActivationDate()) {
            if (numReadings.compareTo(0L) != 0 || existing != null && (existing.getLastPurchaseDate() != null || existing.getLastCyclicChargeDate() != null)) {
                throw new ServiceException("meter.change.activation.date.error.trans");
            } else {
                usagePointData.setActivationDate(usagePointData.getInstallationDate());
            }
        }
        
        if (serviceLocation != null) {
            if (serviceLocation.getId() == null) {
                serviceLocation.setMrid(MridUtil.getMrid());
                serviceLocation = locationService.saveLocation(serviceLocation);
                usagePointData.setServiceLocationId(serviceLocation.getId());
            } else {
                serviceLocation = locationService.saveLocation(serviceLocation);
            }
            usagePointData.setServiceLocation(serviceLocation);
        }

        if (usagePointLocation != null) {
            if (usagePointLocation.getId() == null) {
                locationService.saveLocation(serviceLocation);
                usagePointData.setUpLocationId(usagePointLocation.getId());
            } else {
                locationService.saveLocation(serviceLocation);
            }
        }

        if (usagePointData.getActiveStatusUsagePointReasonsLog() != null && usagePointData.getActiveStatusUsagePointReasonsLog().getId() == null) {
            if (specialActionReasonsLogMapper.insert(usagePointData.getActiveStatusUsagePointReasonsLog()) != 1) {
                throw new ServiceException("usagepoint.save.error");
            }
            usagePointData.setActiveStatusReasonLogId(usagePointData.getActiveStatusUsagePointReasonsLog().getId());
        }

        if (usagePointData.getChangePricingStructureReasonsLogCurrentPs() != null) {
            if (specialActionReasonsLogMapper.insert(usagePointData.getChangePricingStructureReasonsLogCurrentPs()) != 1) {
                throw new ServiceException("usagepoint.save.error");
            }
        }

        if (usagePointData.getChangePricingStructureReasonsLogFuturePs() != null) {
            if (specialActionReasonsLogMapper.insert(usagePointData.getChangePricingStructureReasonsLogFuturePs()) != 1) {
                throw new ServiceException("usagepoint.save.error");
            }
        }     
        
        BlockingType blockingType = usagePointData.getBlockingType();
        Long oldBlockingTypeId = null;
        if (blockingType != null) {
            oldBlockingTypeId = blockingType.getId();
        }
        Long newBlockingTypeId = usagePointData.getBlockingTypeId();
        if (newBlockingTypeId != oldBlockingTypeId) {
            Date blockingStartDate = new Date();
            if (usagePointId != null) {
                if (newBlockingTypeId == null) {
                    if (usagePointCustomMapper.removeBlockFromUsagePoint(usagePointId) != 1) {
                        throw new ServiceException("usagepoint.save.error");
                    }
                    blockingStartDate = null;
                } else {
                    if (usagePointCustomMapper.removeBlockReasonOnlyFromUsagePoint(usagePointId) != 1) {
                        throw new ServiceException("usagepoint.save.error");
                    }
                    usagePointData.setBlockReasonLogId(null);
                }
            }
            usagePointData.setBlockingStartDate(blockingStartDate);
        }
        SpecialActionReasonsLog specialActionReasonsLog = usagePointData.getBlockingUsagePointReasonsLog();
        if (specialActionReasonsLog != null && specialActionReasonsLog.getId() == null) {
            if (specialActionReasonsLogMapper.insert(specialActionReasonsLog) != 1) {
                throw new ServiceException("usagepoint.save.error");
            }
            usagePointData.setBlockReasonLogId(specialActionReasonsLog.getId());
        }

        UnitsAccount unitsAccount = usagePointData.getUnitsAccount();
        UnitsAccountExample unitsAccountExample = new UnitsAccountExample();
        String accountName = unitsAccount.getAccountName();
        unitsAccountExample.createCriteria().andAccountNameEqualTo(accountName);
        Long unitsAccountId = unitsAccount.getId();
        List<UnitsAccount> unitsAccounts = unitsAccountMapper.selectByExample(unitsAccountExample);
        if (unitsAccounts != null && !unitsAccounts.isEmpty()) {
            UnitsAccount existingAccount = unitsAccounts.get(0);
            if (existingAccount != null && !existingAccount.getId().equals(unitsAccountId)) {
                throw new ValidationException(new ValidationMessage("error.field.accountname.duplicate", new String[] { accountName }, true));
            }
        }

        if (usagePointData.isUnitsAccountChanged() || unitsAccountId == null) {
            if (unitsAccount.getRecordStatus() == null) {
                unitsAccount.setRecordStatus(usagePointData.getRecordStatus());
            }
            if (unitsAccountId == null) {
                unitsAccount.setMrid(MridUtil.getMrid());
                if (unitsAccountMapper.insert(unitsAccount) != 1) {
                    throw new ServiceException("units.account.error.save");
                }
                usagePointData.setUnitsAccountId(unitsAccount.getId());
            } else {
                if (unitsAccountMapper.updateByPrimaryKey(unitsAccount) != 1) {
                    throw new ServiceException("units.account.error.save");
                }
            }
        }

        if (usagePointId == null) {
            Date activationDate = usagePointData.getActivationDate();
            RecordStatus upStatus = usagePointData.getRecordStatus();
            Long activeStatusReasonLogId = usagePointData.getActiveStatusReasonLogId();
            usagePointData.setRecordStatus(RecordStatus.DAC);
            usagePointData.setActivationDate(null);
            usagePointData.setActiveStatusReasonLogId(null);
            int updated = 0;
            String error = "usagepoint.save.error";
            try {
                updated = usagePointMapper.insert(usagePointData);
            } catch(Exception e) {
                error = checkIfLicenseError(error, e);
            }

            if (updated != 1) {
                throw new ServiceException(error);
            }

            usagePointId = usagePointData.getId();
            // Future + Current
            processAndSaveUPPricingStructures(usagePointData, activationDate, null, false, false, false);
            
            // Only ACTivate UP if all data has been saved.
            if (upStatus == RecordStatus.ACT) {
                usagePointData.setRecordStatus(RecordStatus.ACT);
                usagePointData.setActivationDate(activationDate);
                usagePointData.setActiveStatusReasonLogId(activeStatusReasonLogId);
                updated = 0;
                try {
                    updated = usagePointMapper.updateByPrimaryKeySelective(usagePointData);
                } catch (Exception e) {
                    error = checkIfLicenseError(error, e);
                }

                if (updated != 1) {
                    throw new ServiceException(error);
                }
            }
        } else {
            UsagePoint up = prepAndUpdateByPrimaryKeySelective(usagePointData);
            up = checkCustomFields(usagePointData, up);
            boolean updateCurrentPS = false;
            if (isFirstEverActivation || usagePointData.isChangeActivationDate()) {
                updateCurrentPS = true;
            }
            processAndSaveUPPricingStructures(usagePointData, up.getActivationDate(), null, true, updateCurrentPS, false);
            usagePointData.setUsagePoint(up);
        }

        if (usagePointData.getMeterId() != null
                && (usagePointData.getMeterData() != null && usagePointData.getMeterData().getEndDeviceStoreId() != null)) {
            usagePointData.getMeterData().setEndDeviceStoreId(null);
            meterService.saveMeter(usagePointData.getMeterData());
        }
        ArrayList<UpGenGroupLinkData> groups = usagePointData.getSelectedGroups();

        if (groups != null && !groups.isEmpty()) {
            UpGenGroupLinkData upGenGroupLinkData;
            UpGenGroupLnk existingLink;
            Iterator<UpGenGroupLinkData> iterator = groups.iterator();

            while (iterator.hasNext()) {
                upGenGroupLinkData = iterator.next();

                if (upGenGroupLinkData.getId() != null) {
                    if (upGenGroupLinkData.getGenGroupId() == null) {
                        // Handle deletion
                        if (upGenGroupLnkMapper.deleteByPrimaryKey(upGenGroupLinkData.getId()) != 1) {
                            throw new ServiceException("usagepoint.group.error.delete");
                        }
                        iterator.remove();
                    } else {
                        // Handle update
                        existingLink = upGenGroupLnkMapper.selectByPrimaryKey(upGenGroupLinkData.getId());
                        if (existingLink != null &&
                                (!existingLink.getUsagePointId().equals(upGenGroupLinkData.getUsagePointId()) ||
                                        !existingLink.getGenGroupId().equals(upGenGroupLinkData.getGenGroupId()))) {
                            if (upGenGroupLnkMapper.updateByPrimaryKey(upGenGroupLinkData) != 1) {
                                throw new ServiceException("usagepoint.group.error.save");
                            }
                        }
                    }
                } else {
                    // Handle insertion
                    upGenGroupLinkData.setUsagePointId(usagePointId);
                    if (upGenGroupLnkMapper.insert(upGenGroupLinkData) != 1) {
                        throw new ServiceException("usagepoint.group.error.save");
                    }
                }
            }
        }
        //Set BlockingType
        if (usagePointData.getBlockingTypeId() != null) {
            usagePointData.setBlockingType(blockingTypeService.getBlockingTypeById(usagePointData.getBlockingTypeId()));
        }
        //Set PricingStructure
        if (usagePointData.getUpPricingStructureData().getUpPricingStructure() != null) {
            long psId = usagePointData.getUpPricingStructureData().getUpPricingStructure().getPricingStructureId();
            usagePointData.getUpPricingStructureData().setPricingStructure(pricingStructureService.getPricingStructure(psId));
        }

        //UpMeterInstall
        UpMeterInstall upMeterInstall = usagePointData.getUpMeterInstall();
        if (upMeterInstall != null && upMeterInstall.getId() == null) {
            upMeterInstall.setMeterId(usagePointData.getMeterId());
            upMeterInstall.setUsagePointId(usagePointId);
            if (upMeterInstallMapper.insert(upMeterInstall) == 1) {
                meterReadingGeneratorService.updateMeterReadingsAfterDate(usagePointData.getMeterId(), usagePointId, upMeterInstall.getInstallDate());
                registerReadingService.updateRegisterReadingsAfterDate(usagePointData.getMeterId(), usagePointId, upMeterInstall.getInstallDate());
                if (channelReadingsList != null && !channelReadingsList.isEmpty()) {
                    registerReadingService.generateInitialRegisterReadingsforChannels(channelReadingsList, usagePointId, usagePointData.getMeterId(), upMeterInstall.getInstallDate(), upMeterInstall.getId());
                }
                DeviceMovementEvent deviceMovementEvent = new DeviceMovementEvent();
                deviceMovementEvent.setMovementTypeE(MovementTypeE.INSTALLATION);
                deviceMovementEvent.setCurrentUpMeterInstallId(upMeterInstall.getId());
                eventNotificationService.newEvent(deviceMovementEvent);
            }
        } else if (upMeterInstall != null && upMeterInstall.getId() != null) {
            if (channelReadingsList != null && !channelReadingsList.isEmpty()) {
                registerReadingService.generateInitialRegisterReadingsforChannels(channelReadingsList, usagePointId, usagePointData.getMeterId(), upMeterInstall.getInstallDate(), upMeterInstall.getId());
            }
            //changing installation Date - UPDATE current upMeterInstall
            if (isChangeInstallationDate) {
                upMeterInstall.setInstallDate(usagePointData.getInstallationDate());
                upMeterInstallMapper.updateByPrimaryKey(upMeterInstall);
            }
        }
        usagePointData.setUpMeterInstall(upMeterInstall);          //reset the current upMeterInstall in UsagePointData
        usagePointData.setChangeActivationDate(false); // Reset boolean UI field.
        return usagePointData;
    }

    @Transactional
    protected void processAndSaveUPPricingStructures(UsagePointData usagePointData, Date activationDate, Date installDate,
            boolean isUpdate, boolean updatedActivationDate, boolean isReplaceMeter) {
        boolean updateCurrent = false;
        boolean updateFuture = false;
        boolean insertCurrent = false;
        boolean insertNewCurrent = false;
        boolean insertFuture = false;
        boolean updatePreviousEndDateAndChangeReason = false;
        Long usagePointId = usagePointData.getId();
        Map<String, UpPricingStructure> upps = pricingStructureService.getCurrentAndFutureUPPricingStructures(usagePointId);
        UpPricingStructure futurexDb = upps.get(FUTURE_PS);
        UpPricingStructure currentxDb = upps.get(CURRENT_PS);
        UpPricingStructure newCurrent = null;
        UpPricingStructure currentUPPS = usagePointData.getUpPricingStructureData().getUpPricingStructure();
        UpPricingStructureData futureUIData = usagePointData.getUpPricingStructureData().getFutureUpPricingStructureData(); 
        Date oldStartDate = null;
        if (currentxDb != null && currentxDb.getStartDate() != null) {
            oldStartDate  = currentxDb.getStartDate();
        }
        // START: Used by Fetch Meter/Fetch UP to clear future PS
        if (futurexDb != null && futureUIData == null) {
            if (upPricingStructureMapper.deleteByPrimaryKey(futurexDb.getId()) != 1) {
                throw new ServiceException("usagepoint.ps.delete.error");
            }
            futurexDb = null;
            futureUIData = null;
            currentxDb.setEndDate(null);
            currentUPPS.setEndDate(null);
            updateCurrent = true;
        }
        // END: Used by Fetch Meter/Fetch UP to clear future PS
        if (updatedActivationDate) {
            // 1. was there a future
            if (futureUIData != null && futureUIData.getUpPricingStructure().getStartDate() != null && 
                    (activationDate.after(futureUIData.getUpPricingStructure().getStartDate()) ||
                     activationDate.equals(futureUIData.getUpPricingStructure().getStartDate()))) {
                if (currentxDb != null && currentxDb.getId() != null) {
                    UpPricingStructureExample example = new UpPricingStructureExample();
                    example.createCriteria().andUsagePointIdEqualTo(currentxDb.getUsagePointId()).andStartDateLessThanOrEqualTo(currentxDb.getStartDate());
                    deleteByExampleUpPs(example);
                }
                currentxDb = null;
                currentUPPS = null;
                futureUIData.getUpPricingStructure().setStartDate(activationDate);
                if (futurexDb != null && futurexDb.getId() != null) {
                    futurexDb.setStartDate(activationDate);
                    updateFuture = updatedActivationDate;
                } else {
                    insertFuture = updatedActivationDate;
                }
            } else {
                UpPricingStructureExample example = new UpPricingStructureExample();
                example.createCriteria().andUsagePointIdEqualTo(currentxDb.getUsagePointId()).andStartDateLessThan(currentxDb.getStartDate());
                deleteByExampleUpPs(example);
                currentxDb.setStartDate(activationDate);
                currentUPPS.setStartDate(activationDate);
                updateCurrent = updatedActivationDate;
            }
        }
        if (isUpdate) {
            if (currentUPPS != null && currentxDb != null && !currentUPPS.getPricingStructureId().equals(currentxDb.getPricingStructureId())) {
                if (!usagePointData.hasNoTransactions()) { // has transactions
                    Date crossoverDt = new Date();
                    if (installDate != null) {
                        crossoverDt = installDate;
                    }        
                    currentxDb.setEndDate(crossoverDt);
                    updateCurrent = true;
                    newCurrent = new UpPricingStructure();
                    newCurrent.setUsagePointId(currentxDb.getUsagePointId());
                    newCurrent.setPricingStructureId(currentUPPS.getPricingStructureId());
                    newCurrent.setStartDate(crossoverDt);
                    insertNewCurrent = true;
                } else {
                    if (isReplaceMeter) {
                        // if its a MeterReplacement and new PS was captured then use the new InstallData.
                        currentxDb.setStartDate(installDate);
                        if (oldStartDate != null) {
                            updatePreviousEndDateAndChangeReason(oldStartDate, currentxDb, false);
                        }
                    } else if(usagePointData.hasNoTransactions()){ // No transactions but PS is changing
                        updatePreviousEndDateAndChangeReason = true;
                    }
                    currentxDb.setPricingStructureId(currentUPPS.getPricingStructureId());
                    updateCurrent = true;
                }
            }
            
            if (futurexDb != null) {
                UpPricingStructure uiFuture = null;
                if (futureUIData != null && futureUIData.getUpPricingStructure() != null) {
                    uiFuture = futureUIData.getUpPricingStructure();
                    if (!(futurexDb.getPricingStructureId().equals(uiFuture.getPricingStructureId()) && futurexDb.getStartDate().equals(uiFuture.getStartDate()))) {
                        if (uiFuture.getStartDate() != null && !futurexDb.getStartDate().equals(uiFuture.getStartDate())) {
                            futurexDb.setStartDate(uiFuture.getStartDate());
                            currentxDb.setEndDate(futurexDb.getStartDate());
                            updateCurrent = true;
                        }
                        futurexDb.setPricingStructureId(uiFuture.getPricingStructureId());
                        updateFuture = true;
                    }
                }
            } else {
                if (futureUIData != null && futureUIData.getUpPricingStructure() != null 
                        && futureUIData.getUpPricingStructure().getId() == null) {
                    futurexDb = futureUIData.getUpPricingStructure();
                    futurexDb.setUsagePointId(usagePointId);
                    insertFuture = true;
                    if (currentxDb != null) {
                        currentxDb.setEndDate(futurexDb.getStartDate());
                        updateCurrent = true;
                    }
                }
            }
        } else {
            if (futureUIData != null && futureUIData.getUpPricingStructure() != null) {
                futurexDb = futureUIData.getUpPricingStructure();
                futurexDb.setUsagePointId(usagePointId);
                insertFuture = true;
            }
            currentxDb = currentUPPS;
            currentxDb.setUsagePointId(usagePointId);
            if (activationDate != null) currentxDb.setStartDate(activationDate);
            currentxDb.setEndDate(futurexDb == null ? null : futurexDb.getStartDate());
            insertCurrent = true;
        }
        
        if (insertFuture || updateFuture) {
            if (usagePointData.getChangePricingStructureReasonsLogFuturePs() != null ) {
                futurexDb.setChangeReasonLogId(usagePointData.getChangePricingStructureReasonsLogFuturePs().getId());
                currentxDb.setChangeReasonLogId(usagePointData.getChangePricingStructureReasonsLogFuturePs().getId());
            } else {
                futurexDb.setChangeReasonLogId(null);
            }
            if (insertFuture && upPricingStructureMapper.insert(futurexDb) != 1) {
                throw new ServiceException("usagepoint.ps.save.error");
            } else if (updateFuture && upPricingStructureMapper.updateByPrimaryKey(futurexDb) != 1) {
                throw new ServiceException("usagepoint.ps.save.error");
            }
        }
        
        if (insertCurrent || updateCurrent) {
            if (usagePointData.getChangePricingStructureReasonsLogCurrentPs() != null ) {
                currentxDb.setChangeReasonLogId(usagePointData.getChangePricingStructureReasonsLogCurrentPs().getId());
            } else {
                currentxDb.setChangeReasonLogId(null);
            }
            if (updatePreviousEndDateAndChangeReason) {
                updatePreviousEndDateAndChangeReason(oldStartDate, currentxDb, true);
            }
            if (insertCurrent && upPricingStructureMapper.insert(currentxDb) != 1) {
                throw new ServiceException("usagepoint.ps.save.error");
            } else if (updateCurrent && upPricingStructureMapper.updateByPrimaryKey(currentxDb) != 1) {
                throw new ServiceException("usagepoint.ps.save.error");
            }
        }
        
        if (insertNewCurrent) {
            if (futurexDb != null) newCurrent.setEndDate(futurexDb.getStartDate());
            if (usagePointData.getChangePricingStructureReasonsLogCurrentPs() != null ) {
                newCurrent.setChangeReasonLogId(usagePointData.getChangePricingStructureReasonsLogCurrentPs().getId());
            }
            if (upPricingStructureMapper.insert(newCurrent) != 1) {
                throw new ServiceException("usagepoint.ps.save.error");
            }
            currentxDb = newCurrent;
        }
        
        if (futurexDb != null && (currentxDb == null || futurexDb.getStartDate().before(new Date()))) {
            currentxDb = futurexDb;
            futurexDb = null;
        }
        UpPricingStructureData data = usagePointData.getUpPricingStructureData();
        data.setUpPricingStructure(currentxDb);
        data.setPricingStructure(pricingStructureMapper.selectByPrimaryKey(currentxDb.getPricingStructureId()));
        if (futurexDb != null) {
            data.setFutureUpPricingStructureData(new UpPricingStructureData());
            data.getFutureUpPricingStructureData().setUpPricingStructure(futurexDb);
            data.getFutureUpPricingStructureData().setPricingStructure(pricingStructureMapper.selectByPrimaryKey(futurexDb.getPricingStructureId()));
        }
    }
    
    @Transactional
    public void deleteByExampleUpPs(UpPricingStructureExample example) {
        //MybatisGeneratorHistoryProxy only handles deleteByPrimaryKey - don't want deleteByExample as an easy do_all, only specific deletions are allowed in MMA 
        List<UpPricingStructure> uppsList = upPricingStructureMapper.selectByExample(example);
        if (uppsList != null && !uppsList.isEmpty()) {
            for (UpPricingStructure upps : uppsList) {
                if (upPricingStructureMapper.deleteByPrimaryKey(upps.getId()) != 1) {
                    throw new ServiceException("usagepoint.ps.delete.error");
                }
            }
        }
    }

    @Transactional
    public void deleteAllFutureUpPs(Long usagePointId) {
        Map<String, UpPricingStructure> upps = pricingStructureService.getCurrentAndFutureUPPricingStructures(usagePointId);
        UpPricingStructure currentUPPS = upps.get(CURRENT_PS);
        if (currentUPPS.getEndDate() != null) {
            UpPricingStructureExample example = new UpPricingStructureExample();
            example.createCriteria().andUsagePointIdEqualTo(usagePointId).andStartDateGreaterThan(new Date());
            deleteByExampleUpPs(example);

            currentUPPS.setEndDate(null);
            currentUPPS.setChangeReasonLogId(null);
            if (upPricingStructureMapper.updateByPrimaryKey(currentUPPS) != 1) {
                throw new ServiceException("usagepoint.ps.save.error");
            }
        }
    }

    @Transactional
    public void updatePreviousEndDateAndChangeReason(Date oldStartDate, UpPricingStructure current, boolean updatePsChangeReason) {
        UpPricingStructureExample example = new UpPricingStructureExample();
        example.createCriteria().andUsagePointIdEqualTo(current.getUsagePointId()).andStartDateEqualTo(oldStartDate);
        List<UpPricingStructure> uppsList = upPricingStructureMapper.selectByExample(example);
        if (uppsList.isEmpty()) {
            return;
        } 
        UpPricingStructure secondLastPS = uppsList.get(0);
        secondLastPS.setEndDate(current.getStartDate());
        if (updatePsChangeReason && current.getChangeReasonLogId() != null) {
            secondLastPS.setChangeReasonLogId(current.getChangeReasonLogId());
        }
        if (upPricingStructureMapper.updateByPrimaryKey(secondLastPS) != 1) {
            throw new ServiceException("");
        }
    }

    @Transactional(readOnly = true)
    public UpPricingStructureData getCurrentAndFutureUPPricingStructures(Long usagePointId) {
        Map<String, UpPricingStructure> upps = pricingStructureService.getCurrentAndFutureUPPricingStructures(usagePointId);
        UpPricingStructure future = upps.get(FUTURE_PS);
        UpPricingStructure current = upps.get(CURRENT_PS);
        UpPricingStructureData uppsData = new UpPricingStructureData();
        if (current != null) {
            uppsData.setUpPricingStructure(current);
            uppsData.setPricingStructure(pricingStructureMapper.selectByPrimaryKey(current.getPricingStructureId()));
        }
        if (future != null) {
            uppsData.setFutureUpPricingStructureData(new UpPricingStructureData());
            uppsData.getFutureUpPricingStructureData().setUpPricingStructure(future);
            uppsData.getFutureUpPricingStructureData().setPricingStructure(pricingStructureMapper.selectByPrimaryKey(future.getPricingStructureId()));
        }
        return uppsData;
    }

    @Transactional(readOnly = true)
    public Date getUPFirstTariffStartDate(Long usagePointId) {
        UpPricingStructure upPS = pricingStructureService.selectAllUPPricingStructures(usagePointId).get(0);
        TariffWithCalcClass firstTariff = pricingStructureService.getAllTariffByPricingStructureId(upPS.getPricingStructureId()).get(0);
        return firstTariff.getStartDate();
    }

    @Transactional
    public UsagePoint checkCustomFields(UsagePointData usagePointData, UsagePoint upFromDb) {
        //because doing a prepAndUpdateByPrimaryKeySelective on an update to preserve certain fields asis on db, will only update fields that are not null
        //this method returns the latest usagePoint from DB
        //so if the customfields have been made null, these do not get updated; update them now onto the latest copy from DB - which then has the same date fields 
        boolean updateCustomFields = false;
        if ((usagePointData.getCustomVarchar1() != null && !usagePointData.getCustomVarchar1().equals(upFromDb.getCustomVarchar1())) ||
                (upFromDb.getCustomVarchar1() != null && !upFromDb.getCustomVarchar1().equals(usagePointData.getCustomVarchar1()))) {
            updateCustomFields = true;
            upFromDb.setCustomVarchar1(usagePointData.getCustomVarchar1());
        }
        if ((usagePointData.getCustomVarchar2() != null && !usagePointData.getCustomVarchar2().equals(upFromDb.getCustomVarchar2())) ||
                (upFromDb.getCustomVarchar2() != null && !upFromDb.getCustomVarchar2().equals(usagePointData.getCustomVarchar2()))) {
            updateCustomFields = true;
            upFromDb.setCustomVarchar2(usagePointData.getCustomVarchar2());
        }
        if ((usagePointData.getCustomNumeric1() != null && !usagePointData.getCustomNumeric1().equals(upFromDb.getCustomNumeric1())) ||
                (upFromDb.getCustomNumeric1() != null && !upFromDb.getCustomNumeric1().equals(usagePointData.getCustomNumeric1()))) {
            updateCustomFields = true;
            upFromDb.setCustomNumeric1(usagePointData.getCustomNumeric1());
        }
        if ((usagePointData.getCustomNumeric2() != null && !usagePointData.getCustomNumeric2().equals(upFromDb.getCustomNumeric2())) ||
                (upFromDb.getCustomNumeric2() != null && !upFromDb.getCustomNumeric2().equals(usagePointData.getCustomNumeric2()))) {
            updateCustomFields = true;
            upFromDb.setCustomNumeric2(usagePointData.getCustomNumeric2());
        }
        if ((usagePointData.getCustomTimestamp1() != null && !usagePointData.getCustomTimestamp1().equals(upFromDb.getCustomTimestamp1())) ||
                (upFromDb.getCustomTimestamp1() != null && !upFromDb.getCustomTimestamp1().equals(usagePointData.getCustomTimestamp1()))) {
            updateCustomFields = true;
            upFromDb.setCustomTimestamp1(usagePointData.getCustomTimestamp1());
        }
        if ((usagePointData.getCustomTimestamp2() != null && !usagePointData.getCustomTimestamp2().equals(upFromDb.getCustomTimestamp2())) ||
                (upFromDb.getCustomTimestamp2() != null && !upFromDb.getCustomTimestamp2().equals(usagePointData.getCustomTimestamp2()))) {
            updateCustomFields = true;
            upFromDb.setCustomTimestamp2(usagePointData.getCustomTimestamp2());
        }

        if (updateCustomFields) {

            int updated = 0;
            String error = "usagepoint.save.error";
            try {
                updated = usagePointMapper.updateByPrimaryKey(upFromDb);
            } catch(Exception e) {
                error = checkIfLicenseError(error, e);
            }

            if (updated != 1) {
                throw new ServiceException(error);
            }
        }
        return upFromDb;
    }

    private boolean upAllowFutureInstallationDates() {
        AppSetting appSetting = appSettingService.getAppSettingByKey(AppSettings.UP_ALLOW_FUTURE_INSTALLATION_DATES);
        if (appSetting != null && appSetting.getValue().trim().equalsIgnoreCase("true")) {
            return true;
        } else {
            return false;
        }
    }
    
    public ServiceException validateInstallationDate(Long usagePointId, Long oldMeterId, String newMeterNum, Date newInstallDate) {
        Long newMeterId = null;
        if (newMeterNum != null) {
            Meter newMeter = meterService.getMeterByNumber(newMeterNum);
            if (newMeter != null) {
                newMeterId = newMeter.getId();
            }
        }
         
        try {
            if (oldMeterId != null) {
                //Check that new installation date is after the last meter reading date and/or the last register reading date of the currently attached meter (old one)
                //throws ServiceException if not
                checkDateIsAfterLastReadingDate(oldMeterId, newInstallDate,
                        "usagepointworkspace.error.meter.installdate.before.last.reading",
                        "usagepointworkspace.error.meter.installdate.before.last.register.reading");
            }
            
            //Check installDate against new Meter Info
            checkInstallationDate(usagePointId, newMeterId, newInstallDate);
            
        } catch (ServiceException e) {
            //returning the ServiceException as an object - it already has both the message Key and the args
            return e;
        }
        return null;
    }
    
    @Transactional
    public void checkInstallationDate(Long usagePointId, Long meterId, Date newInstallDate) throws ServiceException {
        // Note that Installation date on UP may be null if saving a new unattached UP || removed a meter from the UP 
        // Can now edit the installation date if no trans on the UP and no readings (meter or regRead) on the meter (planio 7873, 9616 and others!)

        // reject if new inst. date < last removal date of METER last install
        if (meterId != null) {
            //set that meter's devicestore to null
            UpMeterInstall upMeterInstall = getLastUpMeterInstallRemoveDateForMeter(meterId);
            if (upMeterInstall != null && upMeterInstall.getRemoveDate() != null
                    && newInstallDate != null
                    && newInstallDate.before(upMeterInstall.getRemoveDate())) {
                throw new ServiceException("usagepoint.error.new.installdate.before.removaldate.meter", new String[]{(new SimpleDateFormat("dd MMM yyyy HH:mm:ss").format(upMeterInstall.getRemoveDate()))});
            }
        }
        
        if (usagePointId != null) {
            //reject if new inst. date < last removal date on UP last install
            UpMeterInstall upMeterInstall = getLastUpMeterInstallRemoveDate(usagePointId);
            if (upMeterInstall != null && upMeterInstall.getRemoveDate() != null
                    && newInstallDate != null
                    && newInstallDate.before(upMeterInstall.getRemoveDate())) {
                throw new ServiceException("usagepoint.error.new.installdate.before.removal", new String[]{(new SimpleDateFormat("dd MMM yyyy HH:mm:ss").format(upMeterInstall.getRemoveDate()))});
            }

            // a new installation date cannot be BEFORE a last STS Vend Trans - this can happen on a meter Replacement / Fetch UP (planio 15152)
            Date lastVendTransDate = getLastVendOrTopupTransDate(usagePointId);
            if (lastVendTransDate != null && !newInstallDate.after(lastVendTransDate)) {
                throw new ServiceException("usagepoint.error.new.installdate.before.last.sts.vend.date", new String[]{(new SimpleDateFormat("dd MMM yyyy HH:mm:ss").format(lastVendTransDate))});
            }

            // a new installation date cannot be BEFORE current ps start date on the UP - this can happen on a meter Replacement / Fetch UP (planio 15152)
            Map<String, UpPricingStructure> upps = pricingStructureService.getCurrentAndFutureUPPricingStructures(usagePointId);
            if (!upps.isEmpty()) {
                Date currentPsStartDate = upps.get(CURRENT_PS).getStartDate();
                if (!newInstallDate.after(currentPsStartDate)) {
                    throw new ServiceException("usagepoint.error.new.installdate.before.current.ps.start.date", new String[]{(new SimpleDateFormat("dd MMM yyyy HH:mm:ss").format(currentPsStartDate))});
                }
            }
        }
    }

    @Transactional
    public String updateUsagePointFromNewFetch(UsagePointFetchDto usagePointFetchDto, List<MdcChannelReadingsDto> channelReadingsList) throws ServiceException {
        boolean updateUP = false;
        UsagePoint usagePoint = getUsagePointById(usagePointFetchDto.getUsagePointId());
        Long usagePointId = usagePoint.getId();
        Long meterId = usagePointFetchDto.getMeterId();
        Date newInstallationDate = usagePointFetchDto.getInstallDate();
        Long accessGroupId = usagePointFetchDto.getAccessGroupId();

        Long numReadings = getCountReadingsForMeterAndUP(meterId, usagePointId);
        if (newInstallationDate != null) {
            if (newInstallationDate.after(new Date())) {
                if (!upAllowFutureInstallationDates()) {
                    throw new ServiceException("error.field.installdate.future");
                } else if (usagePoint.getLastPurchaseDate() != null || usagePoint.getLastCyclicChargeDate() != null || numReadings.compareTo(0L) != 0) {
                    throw new ServiceException("meter.replace.installation.date.error.trans");
                } 
            } else { 
                //check new installDate cannot be before last removal date of either the UP or the new Meter
                checkInstallationDate(usagePointId, meterId, newInstallationDate);
            }
        }

        if (meterId != null) {
            MeterData meterData = new MeterData(meterService.getMeterById(meterId));
            if (meterData.getMeterTypeId().equals(MeterTypeE.STS.getId())) {
                meterData.setStsMeter(stsMeterService.getMeterById(meterId));
            }
            meterData.setEndDeviceStoreId(null);
            meterData.setAccessGroupId(accessGroupId);
            meterService.saveMeter(meterData);

            updateUP = true;
            usagePoint.setMeterId(meterId);
            usagePoint.setInstallationDate(newInstallationDate);

            //create a new up_meter_install!!
            UpMeterInstall upMeterInstall = new UpMeterInstall();
            upMeterInstall.setMeterId(meterId);
            upMeterInstall.setUsagePointId(usagePoint.getId());
            upMeterInstall.setInstallDate(usagePoint.getInstallationDate());

            //So now update any readings that might have come in while the meter was unattached!
            if (upMeterInstallMapper.insert(upMeterInstall) == 1) {
                meterReadingGeneratorService.updateMeterReadingsAfterDate(usagePoint.getMeterId(), usagePoint.getId(), usagePoint.getInstallationDate());
                registerReadingService.updateRegisterReadingsAfterDate(usagePoint.getMeterId(), usagePoint.getId(), usagePoint.getInstallationDate());
                if (channelReadingsList != null && !channelReadingsList.isEmpty()) {
                    registerReadingService.generateInitialRegisterReadingsforChannels(channelReadingsList, usagePoint.getId(), usagePoint.getMeterId(), usagePoint.getInstallationDate(), upMeterInstall.getId());
                }
                DeviceMovementEvent deviceMovementEvent = new DeviceMovementEvent();
                deviceMovementEvent.setMovementTypeE(MovementTypeE.INSTALLATION);
                deviceMovementEvent.setCurrentUpMeterInstallId(upMeterInstall.getId());
                eventNotificationService.newEvent(deviceMovementEvent);
            }
        }

        if (usagePointFetchDto.getCurrentPricingStructureId() != null || usagePointFetchDto.getFuturePricingStructureId() == null) {
            Map<String, UpPricingStructure> upps = pricingStructureService.getCurrentAndFutureUPPricingStructures(usagePointId);
            UpPricingStructure future = upps.get(FUTURE_PS);
            UpPricingStructure current = upps.get(CURRENT_PS);
            if (usagePointFetchDto.getFuturePricingStructureId() == null && future != null && future.getId() != null) {
                if (upPricingStructureMapper.deleteByPrimaryKey(future.getId()) != 1) {
                    throw new ServiceException("usagepoint.save.error");
                }
                current.setEndDate(null);
            }
            if (usagePointFetchDto.getCurrentPricingStructureId() != null) {
                if (current.getStartDate() != null) {
                    updatePreviousEndDateAndChangeReason(current.getStartDate(), current, false);
                }
                current.setStartDate(usagePoint.getInstallationDate() == null ? new Date() : usagePoint.getInstallationDate());
                current.setPricingStructureId(usagePointFetchDto.getCurrentPricingStructureId());
                current.setUsagePointId(usagePoint.getId());
                if (upPricingStructureMapper.updateByPrimaryKey(current) != 1) {
                    throw new ServiceException("usagepoint.save.error");
                }
            }
        }

        if (usagePointFetchDto.getCustomerAgreementId() != null) {
            updateUP = true;
            usagePoint.setCustomerAgreementId(usagePointFetchDto.getCustomerAgreementId());
        }

        if (usagePoint.getAccessGroupId() == null) {
            updateUP = true;
            usagePoint.setAccessGroupId(accessGroupId);
        }

        if (updateUP) {

            int updated = 0;
            String error = "usagepoint.save.error";
            try {
                updated = usagePointMapper.updateByPrimaryKey(usagePoint);
            } catch(Exception e) {
                error = checkIfLicenseError(error, e);
            }

            if (updated != 1) {
                throw new ServiceException(error);
            }
        }
        return usagePoint.getName();
    }

    @Transactional
    public void removeCustomerFromUsagePoint(UsagePointData usagePointData) throws ServiceException {
        UsagePointExample usagePointExample = new UsagePointExample();
        usagePointExample.createCriteria().andIdEqualTo(usagePointData.getId())
                .andCustomerAgreementIdEqualTo(usagePointData.getCustomerAgreementId());
        List<UsagePoint> list = usagePointMapper.selectByExample(usagePointExample);
        if (list != null && list.size() > 0) {
            UsagePoint usagePoint = list.get(0);
            if (usagePoint != null) {
                SpecialActionReasonsLog specialActionReasonsLog = usagePointData.getUnassignCustomerReasonsLog();
                if (specialActionReasonsLog != null) {
                    if (specialActionReasonsLogMapper.insert(specialActionReasonsLog) != 1) {
                        throw new ServiceException("usagepoint.save.error");
                    }
                    usagePoint.setUnassignCustomerLogId(specialActionReasonsLog.getId());
                }
                usagePoint.setCustomerAgreementId(null);
                usagePointMapper.updateByPrimaryKey(usagePoint);
            }
        }
    }

    @Transactional
    public UsagePoint assignCustomerToUsagePoint(Long usagePointId, Long customerAgreementId, Long accessGroupId) throws ServiceException {
        customerService.updateAccessGroupForCustomerAgeement(customerAgreementId, accessGroupId);
        // Handle a case where its a Multi-UP setup.
        ArrayList<UsagePoint> upList = getUsagePointsByCustomerAgreementId(customerAgreementId);
        if (upList != null) {
            for (UsagePoint up : upList) {
                updateAccessGroupForUsagePoint(up.getId(), accessGroupId);
            }
        }
        UsagePoint usagePoint = usagePointMapper.selectByPrimaryKey(usagePointId);
        if (usagePoint != null) {
            usagePoint.setCustomerAgreementId(customerAgreementId);
            usagePoint.setAccessGroupId(accessGroupId);
            usagePoint = prepAndUpdateByPrimaryKeySelective(usagePoint);
        }
        return usagePoint;
    }

    @Transactional
    public void removeMeterFromUsagePointB4Reassign(Long usagePointId, MeterData currentMeter, Date newInstallationDate) throws ServiceException, ValidationException {
        //Check that new installation date is after the last meter reading date and/or the last register reading date of the currently attached meter
        //throws ServiceException if not
        checkDateIsAfterLastReadingDate(currentMeter.getId(), newInstallationDate,
                "usagepointworkspace.error.meter.installdate.before.last.reading",
                "usagepointworkspace.error.meter.installdate.before.last.register.reading"); 
        
        UsagePointExample usagePointExample = new UsagePointExample();
        usagePointExample.createCriteria().andIdEqualTo(usagePointId).andMeterIdEqualTo(currentMeter.getId());
        List<UsagePoint> list = usagePointMapper.selectByExample(usagePointExample);
        if (list != null && list.size() > 0) {
            UsagePoint usagePoint = list.get(0);
            if (usagePoint != null) {
                if (usagePointCustomMapper.removeMeterFromUsagePoint(usagePointId) != 1) {
                    throw new ServiceException("usagepoint.save.error");
                }
            }
        }
        meterService.saveMeter(currentMeter);
        if (currentMeter.getStsMeter() != null) {
            stsMeterService.updateStsMeter(currentMeter.getStsMeter());
        }
    }

    @Transactional
    public UsagePoint reAssignMeterToUsagePoint(UsagePointData usagePointData, MeterData meter, Long accessGroupId, List<MdcChannelReadingsDto> channelReadingsList) throws ServiceException, ValidationException {
        Date now = new Date();
        RecordStatus upRecordStatus = usagePointData.getRecordStatus();
        MeterData meterData = usagePointData.getMeterData();
        //first insert special action reason log entry
        DeviceMovementEvent deviceMovementEvent = new DeviceMovementEvent();

        if (usagePointData.getReplaceMeterReasonsLog() != null) {
            if (specialActionReasonsLogMapper.insert(usagePointData.getReplaceMeterReasonsLog()) != 1) {
                throw new ServiceException("usagepoint.save.error");
            }
            usagePointData.setReplaceReasonLogId(usagePointData.getReplaceMeterReasonsLog().getId());
            if (meterData != null) {
                meterData.setReplaceReasonLogId(usagePointData.getReplaceReasonLogId());
                if (meterData.getStsMeter() != null) {
                    meterData.getStsMeter().setReplaceReasonLogId(usagePointData.getReplaceReasonLogId());
                }
                //save meter with replace_reason_log_id
                meterData.setAccessGroupId(accessGroupId);
                meterService.saveMeter(meterData);
            }
            meter.setReplaceReasonLogId(usagePointData.getReplaceReasonLogId());
            deviceMovementEvent.setReasonLogId(usagePointData.getReplaceReasonLogId());
        }

        UpMeterInstall upMeterInstall = usagePointData.getUpMeterInstall();
        Long usagePointId = usagePointData.getId();
        //first remove current meter from usagePoint
        if (usagePointData.getMeterId() != null) {
            removeMeterFromUsagePointB4Reassign(usagePointId, usagePointData.getMeterData(), upMeterInstall.getInstallDate());
        } else {
            deviceMovementEvent.setMovementTypeE(DeviceMovementEvent.MovementTypeE.INSTALLATION);
        }

        //Now allocate new meter
        UsagePoint theusagePoint = usagePointMapper.selectByPrimaryKey(usagePointId);
        if (theusagePoint != null) {
            Date newInstallationDate = upMeterInstall.getInstallDate();
            //check that the new installDate can only be in the future IF there are no new trans on the UP or trans on the new meter
            Long numReadings = getCountReadingsForMeterAndUP(meter.getId(), usagePointId);
            if (newInstallationDate.after(now)) {
                if (!upAllowFutureInstallationDates()) {
                    throw new ServiceException("error.field.installdate.future");
                } else if (theusagePoint.getLastPurchaseDate() != null || theusagePoint.getLastCyclicChargeDate() != null || numReadings.compareTo(0L) != 0) {
                    throw new ServiceException("meter.replace.installation.date.error.trans");
                } 
            } else { 
                //check new installDate cannot be before last removal date of either the UP or the new Meter
                checkInstallationDate(usagePointId, meter.getId(), newInstallationDate);
            }
            
            if (usagePointData.isChangeActivationDate()) {
                if (theusagePoint.getLastPurchaseDate() == null && theusagePoint.getLastCyclicChargeDate() == null && numReadings == 0) {
                    theusagePoint.setActivationDate(theusagePoint.getInstallationDate());
                 } else {
                    throw new ServiceException("meter.change.activation.date.error.trans");
                }
            }
            
            theusagePoint.setMeterId(meter.getId());
            theusagePoint.setReplaceReasonLogId(usagePointData.getReplaceReasonLogId());
            theusagePoint.setInstallationDate(newInstallationDate);
            if (upMeterInstall != null) {
                upMeterInstallMapper.insert(upMeterInstall);
            }
            
            theusagePoint.setAccessGroupId(accessGroupId);
            theusagePoint = prepAndUpdateByPrimaryKeySelective(theusagePoint);
            // ActivationDateChanged = TRUE
            if (usagePointData.isChangeActivationDate()) {
                processAndSaveUPPricingStructures(usagePointData, theusagePoint.getActivationDate(), newInstallationDate, true, true, true);
            } else {
                processAndSaveUPPricingStructures(usagePointData, theusagePoint.getActivationDate(), newInstallationDate, true, false, true);
            }
            
            meter.setAccessGroupId(accessGroupId);
            meterService.saveMeter(meter);
            if (meter.getStsMeter() != null) {
                stsMeterService.updateStsMeter(meter.getStsMeter());
            }
            if (upMeterInstall != null) {
                deviceMovementEvent.setCurrentUpMeterInstallId(upMeterInstall.getId());
                UpMeterInstall previousUpMeterInstall = usagePointData.getPreviousUpMeterInstall();
                if (previousUpMeterInstall != null) {
                    upMeterInstallMapper.updateByPrimaryKey(previousUpMeterInstall);
                    deviceMovementEvent.setPreviousUpMeterInstallId(previousUpMeterInstall.getId());
                    deviceMovementEvent.setMovementTypeE(DeviceMovementEvent.MovementTypeE.REPLACEMENT);
                } else {
                    deviceMovementEvent.setMovementTypeE(DeviceMovementEvent.MovementTypeE.INSTALLATION);
                }
                eventNotificationService.newEvent(deviceMovementEvent);
                meterReadingGeneratorService.updateMeterReadingsAfterDate(meter.getId(), usagePointData.getId(),
                        upMeterInstall.getInstallDate());
                registerReadingService.updateRegisterReadingsAfterDate(meter.getId(), usagePointData.getId(),
                        upMeterInstall.getInstallDate());
                if (channelReadingsList != null && !channelReadingsList.isEmpty()) {
                    registerReadingService.generateInitialRegisterReadingsforChannels(channelReadingsList,
                            usagePointData.getId(), meter.getId(), upMeterInstall.getInstallDate(),
                            upMeterInstall.getId());
                }
            }
            usagePointData.setUsagePoint(theusagePoint);
        }
        usagePointData.setChangeActivationDate(false); // Reset boolean UI field.
        sendClearMeterBalanceMessage(upRecordStatus, meterData);
        return usagePointData;
    }

    private void sendClearMeterBalanceMessage(RecordStatus upRecordStatus, MeterData meterData) {
        if (upRecordStatus != null && meterData != null
                && upRecordStatus == RecordStatus.ACT
                && meterData.getMeterModelData() != null 
                && meterData.getMeterModelData().getMdcId() != null
                && meterData.getMeterModelData().isDisplayMessage()) {
            try {
                ControlReqMessage.Param[] parameters = new ControlReqMessage.Param[1];
                parameters[0] = new ControlReqMessage.Param("message", null);
                customerService.sendIpayXmlMessage(meterData, ControlType.PAN_DISPLAY, Override.NONE, parameters);
            } catch (Exception ex) {
                ex.printStackTrace();
                logger.error("An error occured sending PAN_DISPLAY on removeMeterFromUsagePoint");
            }
        }
    }

    private Long getCountReadingsForMeter(Long meterId) {
        Long numReadings = 0L;    
        if (meterId != null) {
            numReadings = readingsCustomMapper.getMeterReadingsCountForMeter(meterId);
            numReadings = numReadings + readingsCustomMapper.getRegReadCountForMeter(meterId);
        }
        return numReadings;
    }

    private Long getCountReadingsForUP(Long usagePointId) {
        Long numReadings = 0L;   
        if (usagePointId != null) {
            numReadings = numReadings + readingsCustomMapper.getMeterReadingsCountForUP(usagePointId);
            numReadings = numReadings + readingsCustomMapper.getRegReadCountForUP(usagePointId);
        }
        return numReadings;
    }

    public Long getCountReadingsForMeterAndUP(Long meterId, Long usagePointId) {
        return getCountReadingsForMeter(meterId) + getCountReadingsForUP(usagePointId);    
    }

    @Transactional
    public void checkDateIsAfterLastReadingDate(Long meterId, Date checkDate, String meterReadErrorkey, String regReadErrorKey) throws ServiceException {
        //checkDate must be after the last meter reading date of the meterId
        MeterReadingExample mrExample = new MeterReadingExample();
        mrExample.createCriteria().andMeterIdEqualTo(meterId).andReadingEndGreaterThanOrEqualTo(checkDate);
        int count = meterReadingMapper.countByExample(mrExample);
        if (count > 0) {
            throw new ServiceException(meterReadErrorkey);
        }

        //checkDate must be after the last register reading date of the meterId
        count = registerReadingService.countRegisterReadingsForMeterFromDate(meterId, checkDate);
        if (count > 0) {
            throw new ServiceException(regReadErrorKey);
        }

    }
    
    @Transactional
    public void removeMeterFromUsagePoint(UsagePointData usagePointData) throws ServiceException, ValidationException {
        Long meterId = usagePointData.getMeterData().getId();
        UpMeterInstall upMeterInstall = usagePointData.getUpMeterInstall();
        MeterData meterData = usagePointData.getMeterData();
        Date removalDate = upMeterInstall.getRemoveDate();

        //Check that removalDate is after the last meter reading date and/or the last register Reading date of the currently attached meter
        //throws ServiceException if not
        checkDateIsAfterLastReadingDate(meterId, removalDate,
                "usagepointworkspace.error.meter.removedate.before.last.reading",
                "usagepointworkspace.error.meter.removedate.before.last.register.reading");   

        //first insert special action reason log entry
        if (usagePointData.getReplaceMeterReasonsLog() != null) {
            if (specialActionReasonsLogMapper.insert(usagePointData.getReplaceMeterReasonsLog()) != 1) {
                throw new ServiceException("usagepoint.save.error");
            }
            usagePointData.setReplaceReasonLogId(usagePointData.getReplaceMeterReasonsLog().getId());

            if (usagePointData.getMeterData() != null) {
                usagePointData.getMeterData().setReplaceReasonLogId(usagePointData.getReplaceReasonLogId());
                if (usagePointData.getMeterData().getStsMeter() != null) {
                    usagePointData.getMeterData().getStsMeter().setReplaceReasonLogId(usagePointData.getReplaceReasonLogId());
                }
                //save meter with replace_reason_log_id
                meterService.saveMeter(usagePointData.getMeterData());
            }

        }

        RecordStatus upRecordStatus = null;
        UsagePointExample usagePointExample = new UsagePointExample();
        Long usagePointId = usagePointData.getId();
        usagePointExample.createCriteria().andIdEqualTo(usagePointId).andMeterIdEqualTo(meterId);
        List<UsagePoint> list = usagePointMapper.selectByExample(usagePointExample);
        if (list != null && list.size() > 0) {
            UsagePoint usagePoint = list.get(0);
            if (usagePoint != null) {
                upRecordStatus = usagePoint.getRecordStatus();
                if (usagePointCustomMapper.removeMeterFromUsagePoint(usagePointData.getId()) != 1) {
                    throw new ServiceException("usagepoint.save.error");
                }

                usagePoint.setRecordStatus(RecordStatus.DAC);
                usagePoint.setMeterId(null);
                usagePoint.setInstallationDate(null);

                //Because the usage point is automatically deactivated when a meter is removed we add the reassign meter reason log id to the deactivated reason log id as well
                usagePoint.setActiveStatusReasonLogId(usagePointData.getReplaceReasonLogId());
                usagePoint.setReplaceReasonLogId(usagePointData.getReplaceReasonLogId());

                prepAndUpdateByPrimaryKeySelective(usagePoint);

                if (usagePointData.getUpMeterInstall() != null) {
                    upMeterInstallMapper.updateByPrimaryKey(usagePointData.getUpMeterInstall());         //to save remove date

                    DeviceMovementEvent deviceMovementEvent = new DeviceMovementEvent();
                    deviceMovementEvent.setReasonLogId(usagePointData.getReplaceReasonLogId());
                    deviceMovementEvent.setMovementTypeE(DeviceMovementEvent.MovementTypeE.REMOVAL);
                    deviceMovementEvent.setPreviousUpMeterInstallId(usagePointData.getUpMeterInstall().getId());
                    eventNotificationService.newEvent(deviceMovementEvent);
                }

                MeterData meter = usagePointData.getMeterData();
                meterService.saveMeter(meter);                   //to save new device store
                if (meter.getStsMeter() != null) {
                    stsMeterService.updateStsMeter(meter.getStsMeter());
                }
            }
        }
        sendClearMeterBalanceMessage(upRecordStatus, meterData);
    }

    @Transactional(readOnly = true)
    public boolean isExistingUsagePointsWithGroup() {
        UsagePointExample example = new UsagePointExample();
        example.createCriteria().andGenGroupIdIsNotNull();
        RowBounds rowBounds = new RowBounds(RowBounds.NO_ROW_OFFSET, 1);
        List<UsagePoint> points = usagePointMapper.selectByExampleWithRowbounds(example, rowBounds);
        if (points.size() == 1) {
            return true;
        } else {
            return false;
        }
    }

    @Transactional(readOnly = true)
    public UpMeterInstall getLastUpMeterInstallRemoveDateIsNull(Long usagePointId) {
        UpMeterInstallExample upMeterInstallExample = new UpMeterInstallExample();
        upMeterInstallExample.createCriteria().andUsagePointIdEqualTo(usagePointId).andInstallDateIsNotNull().andRemoveDateIsNull();
        upMeterInstallExample.setOrderByClause("up_meter_install_id desc");
        List<UpMeterInstall> list = upMeterInstallMapper.selectByExample(upMeterInstallExample);
        if (list == null || list.size() < 1) {
            return null;
        }
        UpMeterInstall upMeterInstall = list.get(0);
        return upMeterInstall;
    }

    @Transactional(readOnly = true)
    public UpMeterInstall getLastUpMeterInstallRemoved(Long usagePointId) {
        // USed by Fetch Meter instead of replaceMeter - after previous meter was removed
        UpMeterInstallExample upMeterInstallExample = new UpMeterInstallExample();
        upMeterInstallExample.createCriteria().andUsagePointIdEqualTo(usagePointId).andInstallDateIsNotNull();
        upMeterInstallExample.setOrderByClause("up_meter_install_id desc");
        List<UpMeterInstall> list = upMeterInstallMapper.selectByExample(upMeterInstallExample);
        if (list == null || list.size() < 1) {
            return null;
        }
        UpMeterInstall upMeterInstall = list.get(0);
        return upMeterInstall;
    }

    @Transactional(readOnly = true)
    public UpMeterInstall getLastUpMeterInstallRemoveDate(Long usagePointId) {
        UpMeterInstallExample upMeterInstallExample = new UpMeterInstallExample();
        upMeterInstallExample.createCriteria().andUsagePointIdEqualTo(usagePointId).andInstallDateIsNotNull().andRemoveDateIsNotNull();
        upMeterInstallExample.setOrderByClause("up_meter_install_id desc");
        List<UpMeterInstall> list = upMeterInstallMapper.selectByExample(upMeterInstallExample);
        if (list == null || list.size() < 1) {
            return null;
        }
        UpMeterInstall upMeterInstall = list.get(0);
        return upMeterInstall;
    }

    @Transactional(readOnly = true)
    public UpMeterInstall getLastUpMeterInstallRemoveDateForMeter(Long meterId) {
        UpMeterInstallExample upMeterInstallExample = new UpMeterInstallExample();
        upMeterInstallExample.createCriteria().andMeterIdEqualTo(meterId).andInstallDateIsNotNull().andRemoveDateIsNotNull();
        upMeterInstallExample.setOrderByClause("up_meter_install_id desc");
        List<UpMeterInstall> list = upMeterInstallMapper.selectByExample(upMeterInstallExample);
        if (list == null || list.size() < 1) {
            return null;
        }
        UpMeterInstall upMeterInstall = list.get(0);
        return upMeterInstall;
    }

    @Transactional(readOnly = true)
    public UpMeterInstall getLastUpMeterInstallForMeter(Long meterId) {
        UpMeterInstallExample upMeterInstallExample = new UpMeterInstallExample();
        upMeterInstallExample.createCriteria().andMeterIdEqualTo(meterId).andInstallDateIsNotNull();
        upMeterInstallExample.setOrderByClause("up_meter_install_id desc");
        List<UpMeterInstall> list = upMeterInstallMapper.selectByExample(upMeterInstallExample);
        if (list == null || list.size() < 1) {
            return null;
        }
        UpMeterInstall upMeterInstall = list.get(0);
        return upMeterInstall;
    }

    @Transactional(readOnly = true)
    public UpMeterInstall getLastUpMeterInstallForMeterNullRemoveDate(Long meterId) {
        UpMeterInstallExample upMeterInstallExample = new UpMeterInstallExample();
        upMeterInstallExample.createCriteria().andMeterIdEqualTo(meterId).andInstallDateIsNotNull().andRemoveDateIsNull();
        upMeterInstallExample.setOrderByClause("up_meter_install_id desc");
        List<UpMeterInstall> list = upMeterInstallMapper.selectByExample(upMeterInstallExample);
        if (list == null || list.size() < 1) {
            return null;
        }
        UpMeterInstall upMeterInstall = list.get(0);
        return upMeterInstall;
    }
    

    @Transactional(readOnly = true)
    public Date getLastVendOrTopupTransDate(Long upId) {
        Date lastVendTransDate = null;
        CustomerTransExample example = new CustomerTransExample();
        example.createCriteria().andUsagePointIdEqualTo(upId)
                                .andCustomerTransTypeIdEqualTo(CustomerTransTypeE.VEND.getId())
                                .andReversedEqualTo(Boolean.FALSE);
        example.setOrderByClause("trans_date desc");
        List<CustomerTrans> list = customerTransMapper.selectByExampleWithRowbounds(example, new RowBounds(0, 1));
        if (!list.isEmpty()) {
            lastVendTransDate = list.get(0).getTransDate();
        }
        return lastVendTransDate;
    }

    @Transactional(readOnly = true)
    public List<UsagePoint> getUsagePointSearchSuggestions(String query, long groupId, int limit) {
        return usagePointCustomMapper.getUsagePointSuggestions(query.toLowerCase(), groupId, new RowBounds(0, limit));
    }

    @Transactional(readOnly = true)
    public Boolean checkIfInUse(Long pricingStructureId) {
        List<UpPricingStructure> upPSPoints = tariffSupportMapper.checkIfPricingStructureInUse(pricingStructureId, new Date());
        if (upPSPoints.size() > 0) {
            return Boolean.TRUE;
        } else {
            return Boolean.FALSE;
        }
    }

    @Transactional(readOnly = true)
    public Boolean isValidAccessGroupUpdate(Long usagePointId, Long customerAgreementId, Long accessGroupId) {
        PricingStructure pricingStructure = null;
        if (customerAgreementId != null) {
            ArrayList<UsagePoint> upList = getUsagePointsByCustomerAgreementId(customerAgreementId);
            for (UsagePoint up : upList) {
                pricingStructure = pricingStructureService.getCurrentOrFirstUsagePointPricingStructure(up.getId());
                if ((accessGroupId == null && pricingStructure.getAccessGroupId() != null) ||
                        (accessGroupId != null && pricingStructure.getAccessGroupId() != null &&
                        !accessGroupId.equals(pricingStructure.getAccessGroupId()))){
                    return Boolean.FALSE;
                }
            }
        } else if (usagePointId != null) {
            UsagePoint up = getUsagePointById(usagePointId);
            pricingStructure = pricingStructureService.getCurrentOrFirstUsagePointPricingStructure(up.getId());
            if ((accessGroupId == null && pricingStructure.getAccessGroupId() != null) ||
                    (accessGroupId != null && pricingStructure.getAccessGroupId() != null &&
                    !accessGroupId.equals(pricingStructure.getAccessGroupId()))){
                return Boolean.FALSE;
            }
        }
        return Boolean.TRUE;
    }

    @Transactional(readOnly = true)
    public List<UsagePoint> getUsagePointsFromGroups(int start, int pageSize, List<UpGenGroupLnk> genGroupLinkList) {
        RowBounds rowBounds = new RowBounds(start, pageSize);
        List<Long> usagePointIdList = new ArrayList<Long>();
        for (UpGenGroupLnk link : genGroupLinkList) {
            usagePointIdList.add(link.getUsagePointId());
        }

        UsagePointExample example = new UsagePointExample();
        example.createCriteria().andIdIn(usagePointIdList);
        List<UsagePoint> uPoints = usagePointMapper.selectByExampleWithRowbounds(example, rowBounds);
        if (uPoints.size() > 0) {
            return uPoints;
        } else {
            return new ArrayList<UsagePoint>();
        }
    }

    @Transactional(readOnly = true)
    public CustomerTransItemOutstandCharges getCustomerTransItemFromCyclicCharges(UsagePointData usagePointData, Date filterDate) throws ServiceException {
        UsagePointOutstandingCyclicCharges upOutstandingCyclicCharges = new UsagePointOutstandingCyclicCharges(vendMapper, appSettingMapper, upPricingStructureMapper, pricingStructureMapper);
        CustomerTransItemOutstandCharges charges = new CustomerTransItemOutstandCharges();
        
        try {
            HashMap<Long, TransItemType> transItemTypeMap = new HashMap<Long, TransItemType>();
            
            List<CyclicChargesResult> results = upOutstandingCyclicCharges.calculate(usagePointData, filterDate);
            charges.setVendCustTransItemOutStand(genCustTransItemOutputForCyclicCharges(transItemTypeMap, results));
            
            if (usagePointData.isHasBillingCyclicCharges()) {
                UsagePoint usagePoint = usagePointMapper.selectByPrimaryKey(usagePointData.getId());
                //VendCharges calc refetches the UP inside method to CreditMeterReq, but billing calc does not, and view has temporarily set it on the object
                //mainly need to remember if it was null
                results = upOutstandingCyclicCharges.calculateBillingCharges(usagePointData, filterDate, usagePoint.getLastBillingCyclicChargeDate());
                charges.setBillingCustTransItemOutStand(genCustTransItemOutputForCyclicCharges(transItemTypeMap, results));
            }
            
            return charges;
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    private List<CustomerTransItemData> genCustTransItemOutputForCyclicCharges(HashMap<Long, TransItemType> transItemTypeMap, 
            List<CyclicChargesResult> results) {
        
        List<CustomerTransItemData> fixedDataItems = new ArrayList<CustomerTransItemData>();
        List<CustomerTransItem> fixedItems = new ArrayList<CustomerTransItem>();
        for (CyclicChargesResult cyclicChargesResult : results) {
            fixedItems.addAll(cyclicChargesResult.getFixedItems());
        }
        
        for (CustomerTransItem ctItem : fixedItems) {
            CustomerTransItemData customerTransItemData = new CustomerTransItemData(ctItem);
            if (customerTransItemData.getTransItemType() != null) {
                if (!transItemTypeMap.containsKey(customerTransItemData.getTransItemType())) {
                    TransItemType transitemtype = transItemTypeMapper.selectByPrimaryKey(customerTransItemData.getTransItemType());
                    transItemTypeMap.put(customerTransItemData.getTransItemType(), transitemtype);
                }
                customerTransItemData.setTransItemTypeDetails(transItemTypeMap.get(customerTransItemData.getTransItemType()));
            }
            customerTransItemData.setCycleId(ctItem.getCycleId());
            fixedDataItems.add(customerTransItemData);
        }
        return fixedDataItems;
    }
    
    @Transactional
    public void writeoffOutstandingCyclicCharges(Long usagePointId, Date upLastCyclicChargeDate, Date upLastBillingCyclicChargeDate,
            Date upNewCyclicChargeDate, String userRecEntered, CustomerTransItemOutstandCharges outstandCharges, SpecialActionReasonsLog logEntry) {

        List<CustomerTransItemData> outstandVendCharges = outstandCharges.getVendCustTransItemOutStand();
        List<CustomerTransItemData> outstandBillingCharges = outstandCharges.getBillingCustTransItemOutStand();

        try {
            UsagePoint usagePoint = usagePointCustomMapper.selectUsagePointForUpdate(usagePointId);
            UsagePointData usagePointData = new UsagePointData();
            usagePointData.setUsagePoint(usagePoint);
            if (!outstandVendCharges.isEmpty() && !outstandBillingCharges.isEmpty()) {
                usagePointData.setLastCyclicChargeDate(upLastCyclicChargeDate);
                usagePointData.setLastBillingCyclicChargeDate(upLastBillingCyclicChargeDate);
                usagePointCustomMapper.updateUsagePointBothLastCyclicChargeDate(upNewCyclicChargeDate, usagePointData.getId());
            } else if (!outstandVendCharges.isEmpty()) {
                usagePointData.setLastCyclicChargeDate(upLastCyclicChargeDate);
                usagePointCustomMapper.updateUsagePointLastCyclicChargeDate(upNewCyclicChargeDate, usagePointData.getId());
            } if (!outstandBillingCharges.isEmpty()) {
                usagePointData.setLastBillingCyclicChargeDate(upLastBillingCyclicChargeDate);
                usagePointCustomMapper.updateUsagePointLastBillingCyclicChargeDate(upNewCyclicChargeDate, usagePointData.getId());
            }
            
            if (specialActionReasonsLogMapper.insert(logEntry) != 1) {
                throw new ServiceException("usagepoint.charge.writeoff.trans.failure");
            }
            
            for (CustomerTransItemData customerTransItemData : outstandVendCharges) {
                insertNewChargeWriteoffData(usagePointData, customerTransItemData, userRecEntered, upNewCyclicChargeDate, CyclicChargeCalcAtE.V, logEntry.getId());
            }
            for (CustomerTransItemData customerTransItemData : outstandBillingCharges) {
                insertNewChargeWriteoffData(usagePointData, customerTransItemData, userRecEntered, upNewCyclicChargeDate, CyclicChargeCalcAtE.B, logEntry.getId());
            }
        } catch (Exception e) {
            throw new ServiceException("usagepoint.charge.writeoff.trans.failure");
        }

        
    }
    
    private void insertNewChargeWriteoffData(UsagePointData usagePointData, CustomerTransItemData customerTransItemData, String userRecEntered, Date upNewCyclicChargeDate, CyclicChargeCalcAtE cyclicChargeCalcAtE, Long reasonId) {
        ChargeWriteoffData chargeWriteoffData = new ChargeWriteoffData(usagePointData, customerTransItemData, userRecEntered, upNewCyclicChargeDate, cyclicChargeCalcAtE, reasonId);
        if (chargeWriteoffMapper.insert(chargeWriteoffData) != 1) {
            throw new ServiceException("chargewriteoff.save.error");
        }
    }
    
    @Transactional
    public Boolean isBillingCyclicChargesInTariffHistSinceLastChargeDate(UsagePointData usagePointData) throws Exception {
        TariffRetrieval tariffRetrieval = new TariffRetrieval(vendMapper, upPricingStructureMapper, pricingStructureMapper, logger);
        Date lastCyclicDate = usagePointData.getLastCyclicChargeDate();
        Date lastBillingCyclicDate = usagePointData.getLastBillingCyclicChargeDate();
        if (lastCyclicDate == null || (lastBillingCyclicDate != null && lastBillingCyclicDate.before(lastCyclicDate))) {
            lastCyclicDate = lastBillingCyclicDate;
        }
        List<TariffWithCalcClass> tariffs =  tariffRetrieval.getTariffs(usagePointData.getId(), lastCyclicDate, usagePointData.getLastPurchaseDate(), 
                usagePointData.getActivationDate(), new Date());
        
        List<TariffWithCalcClass> tariffsWithChargesAt = tariffs.stream()
                .filter(t -> t.getCalcContents().contains("cyclicChargeCalcAtE"))
                .collect(Collectors.toList());
        
        boolean isBillingCyclicCharges = false;
        for (TariffWithCalcClass tariffWithCalcClass : tariffsWithChargesAt) {
            IThinSmartTariffCalculator thinSmartCalculator = (IThinSmartTariffCalculator) Class.forName(tariffWithCalcClass.getCalcClass()).newInstance();

            List<CyclicCharge> charges = thinSmartCalculator.getBillingCyclicCharges(tariffWithCalcClass.getCalcContents());
            if (charges != null && !charges.isEmpty()) {
                isBillingCyclicCharges = true;
                break;
            }
        }
        return isBillingCyclicCharges;
    }

    public IpayResponseData sendMeterInspectionRequestMsg(UsagePointData usagePointData, String comment) {
        za.co.ipay.ipayxml.metermng.domain.Location location = null;
        za.co.ipay.ipayxml.metermng.domain.Meter meter = null;
        za.co.ipay.ipayxml.metermng.domain.UsagePoint usagePoint = null;
        za.co.ipay.ipayxml.metermng.domain.Customer customer = null;
        try {
            // UP
            usagePoint = new za.co.ipay.ipayxml.metermng.domain.UsagePoint(
                    usagePointData.getMrid(), usagePointData.getName());
            // Meter
            za.co.ipay.metermng.mybatis.generated.model.Meter upMeter = usagePointData.getMeterData();
            meter = new za.co.ipay.ipayxml.metermng.domain.Meter(
                    upMeter.getMeterNum(), upMeter.getMrid(), upMeter.getSerialNum());
            // Location
            String groupTypeName = "";
            List<String> genGroupNames = new ArrayList<>();
            if (usagePointData.getServiceLocation() != null) {
                GroupType groupType = groupService.getGroupTypeById(usagePointData.getServiceLocation().getGroupTypeId());
                groupTypeName = groupType.getName();
                for (Long genGroupId : usagePointData.getServiceLocation().getGroupDepthList()) {
                    GenGroup genGroup = groupService.getGenGroup(genGroupId);
                    genGroupNames.add(genGroup.getName());
                }
            }
            za.co.ipay.ipayxml.metermng.domain.GroupData groupData = new za.co.ipay.ipayxml.metermng.domain.GroupData(groupTypeName, genGroupNames);
            za.co.ipay.metermng.mybatis.generated.model.Location upLocation = usagePointData.getServiceLocation();
            location = new za.co.ipay.ipayxml.metermng.domain.Location(
                    upLocation.getMrid(), groupData,
                    (upLocation.getLatitude() == null ? "" : upLocation.getLatitude().toString()),
                    (upLocation.getLongitude() == null ? "" : upLocation.getLongitude().toString()),
                    upLocation.getErfNumber(), upLocation.getStreetNum(), upLocation.getSuiteNum(),
                    upLocation.getBuildingName(), upLocation.getAddressLine1(), upLocation.getAddressLine2(),
                    upLocation.getAddressLine3());
            // Customer
            za.co.ipay.metermng.mybatis.generated.model.Customer upCustomer = usagePointData.getCustomerAgreementData().getCustomerData();
            customer = new za.co.ipay.ipayxml.metermng.domain.Customer(
                    upCustomer.getMrid(), upCustomer.getFirstnames(), upCustomer.getSurname(),
                    upCustomer.getEmail1(), upCustomer.getEmail2(), upCustomer.getPhone1(), upCustomer.getPhone2());
        } catch (Exception ex) {
            return null;
        }
        
        IpayResponseData data = null;
        try {
            SpecialActionReasonsLog inspectReason = usagePointData.getInspectMeterReasonsLog();
            String reasonStr = "";
            if (inspectReason != null) {
                reasonStr = inspectReason.getReasonText() == null ? "" : inspectReason.getReasonText();
            }
            InspectionReqMessage inspectionReqMessage = new InspectionReqMessage(ipayXmlMessageService.getClient(),
                    ipayXmlMessageService.getTerm(), location, meter, usagePoint, customer, reasonStr, comment);
    
            InspectionResMessage inspectionResMessage = (InspectionResMessage) ipayXmlMessageService.sendIpayXml(inspectionReqMessage);
            if (inspectReason != null && specialActionReasonsLogMapper.insert(inspectReason) != 1) {
                logger.info("Unabled to save SpecialAction Inspect-Meter Reason.");
            }
            data = new IpayResponseData();
            data.setResRef(inspectionResMessage.getRef());
            data.setResCode(inspectionResMessage.getResCode());
            data.setResMsg(inspectionResMessage.getRes());
        } catch (Exception ex) {
            throw new ServiceException("usagepoint.meter.inspection.request.processing.error");
        }  
        return data;
    }
    
    //----------------------------------------------------------------------------------------------------------------
    //Methods to check correlation between Mdc, billingDetIds and changes to meterModels on UPs
    @Transactional(readOnly = true)
    public List<Integer> countUpWithMdcUsingBillingDetId(Long billingDetId, String userName, Long userGenGroupId) {
        List<Integer> countUp = new ArrayList<>();
        Integer act = usagePointCustomMapper.countUpWithMdcUsingBillingDetId(billingDetId, "ACT", userGenGroupId);
        Integer dac = usagePointCustomMapper.countUpWithMdcUsingBillingDetId(billingDetId, "DAC", userGenGroupId);
        countUp.add(act);
        countUp.add(dac);
        logger.info("BillingDetWorkspaceView: on change status of billingDetId: " + billingDetId + " ActiveUpWithMdcUsingBillingDetId = " + act.toString() + " inactive = " + dac.toString() + " Warning displayed to user: " + userName);
        return countUp;
    }

    @Transactional(readOnly = true)
    public MdcChannelMatchDto countUpWithMdc(Long mdcId, String userName, Long userGenGroupId) {
        MdcChannelMatchDto dto = new MdcChannelMatchDto();
        dto.setActiveUpCount(usagePointCustomMapper.countUpWithMdc(mdcId, "ACT", userGenGroupId));
        dto.setInactiveUpCount(usagePointCustomMapper.countUpWithMdc(mdcId, "DAC", userGenGroupId));
        dto.setUpWithThinUnitsPsCount(usagePointCustomMapper.countUpWithMdcAndThinUnitsPs(mdcId, userGenGroupId, new Date()));
        return dto;
    }
    
    @Transactional(readOnly = true)
    protected Set<Long> getMdcBillingDetIds(Long mdcId) {
        Set<Long> mdcBillingDetIds = new HashSet<Long>(); 
        if (mdcId != null) {
            //get list billing det for this MDC
            mdcBillingDetIds.addAll(mdcChannelService.getBillingDetIdsUsedByChannelsOnMdc(mdcId));
        }
        return mdcBillingDetIds;
    }
    
    private ChannelCompatibilityE matchBillingDetsMdcToPS(Long mdcId, Set<Long> mdcBillingDetIds, Long userGenGroupId) {
        ChannelCompatibilityE match = ChannelCompatibilityE.NO_DATA;    //as default 
        if (mdcId != null) {
            //find all psId from Up with meters that are models using this mdcId
            Set<Long> pricingStructureIds  = new HashSet<Long>();
            pricingStructureIds.addAll(pricingStructureService.getPricingStructureIdsforUpWithMdc(mdcId, userGenGroupId));
            //run through each ps, get billing dets for it and check if any have a NO-MATCH - if so send that back for error
            for (Long psId : pricingStructureIds) {
                //this only checks CURRENT tariff billing det Ids
                TariffPanelDto dto = pricingStructureService.getCurrentTariffDtoByPricingStructureId(psId);
                if (dto.getTariffClass().getCalcClass().contains("RegisterReading")) {
                    TariffWithData tariffWithData = dto.getTariffWithData();
                    List<Long> regReadTariffBillingDetIds = pricingStructureService.getBillingDetIdsFromTariffWithData(tariffWithData);
                    match = pricingStructureService.checkChannelCompatibility(mdcBillingDetIds, regReadTariffBillingDetIds);
                    if (match.equals(ChannelCompatibilityE.PARTIAL_MATCH)
                            || match.equals(ChannelCompatibilityE.NONE_MATCH)) {
                        break;    //only need one that doesn't exactly match
                    } 
                } 
            }
        }
        return match;
    }
    
    @Transactional(readOnly = true)
    public MdcChannelMatchDto getMdcChannelCompatibility(Long mdcId, String userName, Long userGenGroupId) {
        MdcChannelMatchDto dto = countUpWithMdc(mdcId, userName, userGenGroupId);
        Set<Long> mdcBillingDetIds = getMdcBillingDetIds(mdcId);
        dto.setMatch(matchBillingDetsMdcToPS(mdcId, mdcBillingDetIds, userGenGroupId));
        return dto;
    }
    
    @Transactional(readOnly = true)
    public MdcChannelMatchDto getMdcChannelUpdateCompatibility(Long mdcId, MdcChannelDto updatedMdcChannel, String userName, Long userGenGroupId) {
        Set<Long> mdcBillingDetIds = new HashSet<Long>(); 
        if (mdcId != null) {
            //get all the channels for this mdc
            //Replace the one channel with this one's new values or ADD if not there
            // now get the distinct billing det Id's for them all
            List<MdcChannelDto> channelDtoList = mdcChannelService.getMdcChannelDtos(mdcId);

            boolean addUpdatedMdcChannel = true;
            Long updatedMdcId = updatedMdcChannel.getMdcChannel().getId();
            for (MdcChannelDto mcd : channelDtoList) {
                if (mcd.getMdcChannel().getId().equals(updatedMdcId)) {
                    addUpdatedMdcChannel = false;
                    mcd = updatedMdcChannel;
                }
                for (IdNameDto idName : mcd.getBillingDetList()) {
                    mdcBillingDetIds.add(idName.getId());
                }
            }
            if (addUpdatedMdcChannel) {
                for (IdNameDto idName : updatedMdcChannel.getBillingDetList()) {
                    mdcBillingDetIds.add(idName.getId());
                }
            }
        }
        
        MdcChannelMatchDto dto = countUpWithMdc(mdcId, userName, userGenGroupId);
        dto.setMatch(matchBillingDetsMdcToPS(mdcId, mdcBillingDetIds, userGenGroupId));
        return dto;
    }
    

    @Transactional(readOnly = true)
    public MdcChannelMatchDto getMeterModelChannelCompatibility(Long meterModelId, Long newMdcId, String userName, Long userGenGroupId) {
        ChannelCompatibilityE match = ChannelCompatibilityE.NO_DATA;    //as default 
        Set<Long> mdcBillingDetIds = getMdcBillingDetIds(newMdcId);
        
        //find all psId from Up with meters that are models using this mdcId
        Set<Long> pricingStructureIds  = new HashSet<Long>();
        pricingStructureIds.addAll(pricingStructureService.getPricingStructureIdsforUpWithMeterModel(meterModelId, userGenGroupId));
        //run through each ps, get billing dets for it and check if any have a NO-MATCH - if so send that back for error
        for (Long psId : pricingStructureIds) {
            //this only checks CURRENT tariff billing det Ids
            TariffPanelDto dto = pricingStructureService.getCurrentTariffDtoByPricingStructureId(psId);
            if (dto.getTariffClass().getCalcClass().contains("RegisterReading")) {
                TariffWithData tariffWithData = dto.getTariffWithData();
                List<Long> regReadTariffBillingDetIds = pricingStructureService.getBillingDetIdsFromTariffWithData(tariffWithData);
                match = pricingStructureService.checkChannelCompatibility(mdcBillingDetIds, regReadTariffBillingDetIds);
                if (match.equals(ChannelCompatibilityE.PARTIAL_MATCH)
                        || match.equals(ChannelCompatibilityE.NONE_MATCH)) {
                    break;    //only need one that doesn't Match
                } 
            } 
        }
        
        MdcChannelMatchDto dto = new MdcChannelMatchDto();
        dto.setMatch(match);   
        dto.setActiveUpCount(usagePointCustomMapper.countUpWithMeterModel(meterModelId, "ACT", userGenGroupId));
        dto.setInactiveUpCount(usagePointCustomMapper.countUpWithMeterModel(meterModelId, "DAC", userGenGroupId));
        dto.setUpWithThinUnitsPsCount(usagePointCustomMapper.countUpWithMeterModelIdAndThinUnitsPs(meterModelId, userGenGroupId, new Date()));
        return dto;
    }

    @Transactional(readOnly = true)
    public ArrayList<UnitsTrans> getUnitsAccountTransactions(Long unitsAccountId) {
        UnitsTransExample example = new UnitsTransExample();
        example.createCriteria().andUnitsAccountIdEqualTo(unitsAccountId);
        ArrayList<UnitsTrans> unitsTransactions = new ArrayList<UnitsTrans>(unitsTransMapper.selectByExample(example));
        unitsTransactions.forEach(unitsTransaction -> {
            if (unitsTransaction.getReceiptNum() == null) {
                Long customerTransId = unitsTransaction.getCustomerTransId();
                if (customerTransId != null) {
                    unitsTransaction
                            .setReceiptNum(customerTransMapper.selectByPrimaryKey(customerTransId).getReceiptNum());
                }
            }
        });
        return unitsTransactions;
    }

    @Transactional
    public AccountAdjustmentResult inputUnitsAccountAdjustment(UnitsTrans unitsTrans) throws ServiceException {
        Long usagePointId = unitsTrans.getUsagePointId();
        UsagePoint usagePoint = getUsagePointById(usagePointId);
        if (usagePoint == null) {
            logger.error(
                    "inputUnitsAccountAdjustment: usagePoint not found! getUsagePointById(unitsTrans.getUsagePointId()) id="
                            + usagePointId);
            throw new ServiceException("customer.txn.error.no.usagepoint");
        }
        Long customerAgreementId = usagePoint.getCustomerAgreementId();
        CustomerAgreement customerAgreement = customerAgreementService.getCustomerAgreementById(customerAgreementId);
        if (customerAgreement == null) {
            logger.error(
                    "inputUnitsAccountAdjustment: customerAgreement not found! usagePoint.getCustomerAgreementId()="
                            + customerAgreementId);
            throw new ServiceException("customer.txn.no.agreement");
        }
        Customer customer = customerService.getCustomerById(customerAgreement.getCustomerId());

        Meter meter = meterMapper.selectByPrimaryKey(usagePoint.getMeterId());
        AccountAdjustmentResult accountAdjustmentResult = unitsAccountAdjustmentProcessor
                .processUnitsAdjustment(new UnitsAdjustment(logger, translationAware, ipayXmlMessageService.getClient(),
                        ipayXmlMessageService.getTerm(), customer, usagePoint, customerAgreement, meter,
                        meterModelService.getMeterModelById(meter.getMeterModelId()), unitsTrans, null));

        for (final IpayXmlMessageWithDelay ipayXmlMessageWithDelay : accountAdjustmentResult.getIpayXmlMessageList()) {
            // Send message here in seperate thread:
            new Thread(new Runnable() {
                public void run() {
                    customerService.sendIpayXmlMessage(ipayXmlMessageWithDelay);
                }
            }).start();
        }
        if (accountAdjustmentResult.getNotifications().size() > 0) {
            Map<String, String> fromEmailDetailsMap = appSettingService.getFromEmailAppSettingDetails();
            final String fromName = fromEmailDetailsMap.get("fromName");
            final String fromEmail = fromEmailDetailsMap.get("fromEmail");

            // From the result, send the emails
            new Thread(new Runnable() {
                public void run() {
                    customerService.sendEmails(fromName, fromEmail, accountAdjustmentResult);
                }
            }).start();
        } else {
            logger.info("UsagePointService: Units Account Adjustment: NO emails to send");
        }
        return accountAdjustmentResult;
    }

    @Transactional(readOnly = true)
    public UnitsAccount getUnitsAccount(Long unitsAccountId) {
        return unitsAccountMapper.selectByPrimaryKey(unitsAccountId);
    }

    @Transactional(readOnly = true)
    public List<UpPricingStructureData> getAllUPPricingStructuresData(Long usagePointId, boolean usingAccessGroup) {
        //this is used bu the ViewAll button on the UsagePOintComponent - so all the ps go into one list - which now includes the future PS
        UpPricingStructureExample example = new UpPricingStructureExample();
        example.createCriteria().andUsagePointIdEqualTo(usagePointId);
        example.setOrderByClause("start_date asc");
        List<UpPricingStructure> dbList = upPricingStructureMapper.selectByExample(example);
        List<UpPricingStructureData> uiList = new ArrayList<>(dbList.size());
        PricingStructure ps = null;
        for (UpPricingStructure upPS : dbList) {
            UpPricingStructureData data = new UpPricingStructureData();
            data.setUpPricingStructure(upPS);
            ps = pricingStructureMapper.selectByPrimaryKey(upPS.getPricingStructureId());
            if (ps == null && usingAccessGroup) {
                ps = new PricingStructure();
                ps.setName(MeterMngStatics.ACCESSGROUP_RLS_HIDDEN_DATA);
            }
            data.setPricingStructure(ps);
            if (upPS.getChangeReasonLogId() != null) {
                data.setChangeReasonText(specialActionsService.getSpecialActionReasonsLog(upPS.getChangeReasonLogId()).getReasonText());
            }
            // get last changed info from hist
            RowBounds rowBounds = new RowBounds(RowBounds.NO_ROW_OFFSET, 1);
            UpPricingStructureHistExample hex = new UpPricingStructureHistExample();
            hex.createCriteria().andIdEqualTo(upPS.getId());
            hex.setOrderByClause("date_rec_modified desc");
            List<UpPricingStructureHist> uppsHistList = upPricingStructureHistMapper.selectByExampleWithRowbounds(hex, rowBounds);
            if (!uppsHistList.isEmpty()) {
                UpPricingStructureHist uppsHist = uppsHistList.get(0);
                data.setDateRecModified(uppsHist.getDateRecModified());
                data.setUserRecEntered(uppsHist.getUserRecEntered());
            }
            uiList.add(data);
        }
        return uiList;
    }

    @Transactional
    public Boolean deleteUpPricingStructure(Long upPricingStructureId, Long usagePointId) throws ServiceException {
        UpPricingStructure deletedUPPS = pricingStructureService.getCurrentAndFutureUPPricingStructures(usagePointId).get(UsagePointService.FUTURE_PS);
        deletedUPPS.setChangeReasonLogId(null); // This UUPS id is equal to upPricingStructureId
        if (upPricingStructureMapper.updateByPrimaryKey(deletedUPPS) != 1) {
            throw new ServiceException("usagepoint.ps.save.error");
        }
        if (upPricingStructureMapper.deleteByPrimaryKey(upPricingStructureId) != 1) {
            throw new ServiceException("usagepoint.ps.delete.error");
        }

        UpPricingStructure currentUPPS = pricingStructureService.getCurrentOrFirstUsagePointUpPricingStructure(usagePointId);
        currentUPPS.setEndDate(null);
        currentUPPS.setChangeReasonLogId(null);
        if (upPricingStructureMapper.updateByPrimaryKey(currentUPPS) != 1) {
            throw new ServiceException("usagepoint.ps.save.error");
        }
        return Boolean.TRUE;
    }

    public IpayResponseData sendSyncUnitsAccountBalance(MeterData meterData, Long unitsAccountId) throws Exception {
        Param[] parameters = new Param[1];
        parameters[0] = new Param("balance", String
                .valueOf(unitsAccountMapper.selectByPrimaryKey(unitsAccountId).getAccountBalance().doubleValue()));
        return customerService.sendIpayXmlMessage(meterData, ControlType.SYNC_BALANCE, null, parameters);
    }

    @Transactional(readOnly = true)
    public UnitsTrans getUnitsTransactionFromCustomerTransId(Long customerTransId) {
        UnitsTransExample example = new UnitsTransExample();
        example.createCriteria().andCustomerTransIdEqualTo(customerTransId);
        return unitsTransMapper.selectByExample(example).stream().findFirst().orElseGet(() -> null);
    }
    
    public boolean getMridExistence(String mrid, Long custId) {
        UsagePointExample example = new UsagePointExample();
        Criteria criteria = example.createCriteria().andMridEqualTo(mrid);
        if(custId != null) {
            criteria.andIdNotEqualTo(custId);
        }
        return !usagePointMapper.selectByExample(example).isEmpty();
    }

    @Transactional
    public int insertSpecialActionReasonLog(SpecialActionReasonsLog specialActionReasonsLog) {
        return specialActionReasonsLogMapper.insert(specialActionReasonsLog);
    }

    @Transactional
    public boolean clearGroupedLocation(Long locationId) {
        int updated = 0;
        Location location = locationService.getLocationById(locationId);
        if (location.getLocGroupId() != null) {
            location.setLocGroupId(null);
            updated = locationService.updateLocation(location);
        }
        return updated > 0;
    }

    @Transactional
    public int clearAccessGroupForUsagePoint(Long usagePointId) {
        // On CLEAR record is still accessible due to RLS.
        int updated = 0;
        int upUpdated = accessGroupMapper.clearGroupForUsagePoint(usagePointId);
        if (upUpdated != 0) {
            UsagePoint upRecord = getUsagePointById(usagePointId);
            upRecord = prepAndUpdateByPrimaryKeySelective(upRecord);
            updated = upRecord.getId().intValue();
        }
        return updated;
    }

    @Transactional
    public int updateAccessGroupForUsagePoint(Long usagePointId, Long accessGroupId) {
        // On UPDATE record might be inaccessible due to RLS.
        UsagePoint upRecord = getUsagePointById(usagePointId);
        upRecord.setAccessGroupId(accessGroupId);
        upRecord = prepAndUpdateByPrimaryKeySelective(upRecord);
        return 1;
    }

    @Transactional
    public int clearAccessGroupForMeter(Long meterId) {
        return meterService.clearAccessGroupForMeter(meterId);
    }

    @Transactional
    public int updateAccessGroupForMeter(Long meterId, Long accessGroupId) {
        return meterService.updateAccessGroupForMeter(meterId, accessGroupId);
    }
}
