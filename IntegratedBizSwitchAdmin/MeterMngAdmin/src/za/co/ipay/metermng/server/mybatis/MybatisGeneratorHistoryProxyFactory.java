package za.co.ipay.metermng.server.mybatis;

import java.lang.reflect.Proxy;

import za.co.ipay.utils.web.ICurrentUserNameProvider;

public class MybatisGeneratorHistoryProxyFactory {
    private ICurrentUserNameProvider userNameProvider;

    public MybatisGeneratorHistoryProxyFactory(ICurrentUserNameProvider userNameProvider) {
        this.userNameProvider = userNameProvider;
    }

    public Object createProxy(Object dao, Object historyDao) throws Exception {
        return Proxy.newProxyInstance(dao.getClass().getClassLoader(), dao.getClass().getInterfaces(), new MybatisGeneratorHistoryProxy(dao, historyDao, userNameProvider));
    }

    public void setUserNameProvider(ICurrentUserNameProvider userNameProvider) {
        this.userNameProvider = userNameProvider;
    }
}
