package za.co.ipay.metermng.server.bulkupload;

import java.util.ArrayList;
import java.util.HashMap;

import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;

import za.co.ipay.gwt.common.shared.validation.ValidateUtil;
import za.co.ipay.metermng.datatypes.RecordStatus;
import za.co.ipay.metermng.mybatis.generated.model.AccountTrans;
import za.co.ipay.metermng.mybatis.generated.model.AuxAccount;
import za.co.ipay.metermng.mybatis.generated.model.CustomerAgreement;
import za.co.ipay.metermng.server.mybatis.service.AuxAccountService;
import za.co.ipay.metermng.server.mybatis.service.BulkUploadService;
import za.co.ipay.metermng.shared.bulkupload.BulkUploadException;
import za.co.ipay.metermng.shared.dto.uploaddata.transuploaddata.AuxTransCsvData;
import za.co.ipay.metermng.shared.dto.uploaddata.transuploaddata.TransCsvMapToData;

@Controller
@RequestMapping(value = "/**/secure/auxtransbulkupload.do")
public class AuxTransBulkUploadController extends TransBulkUploadCommon {

	@Autowired
	private AuxAccountService auxAccountService;

	@Autowired
	private BulkUploadService bulkUploadService;

	private static final boolean IS_CUSTOMER_TRANS_UPLOAD = false;
	private Logger logger = Logger.getLogger(AuxTransBulkUploadController.class.getName());

	private ArrayList<AuxTransCsvData> processTransList;
	private TransCsvMapToData csvMapData;

	public void setupDataMapping() {
		csvMapData = new TransCsvMapToData(IS_CUSTOMER_TRANS_UPLOAD);
		csvFieldMap = new HashMap<Integer, String>();
		headingIndicator = "Aux Account Name";
		setCustomerTransUpload(IS_CUSTOMER_TRANS_UPLOAD);
	}

	@Override
	void constructCsvFieldMap(String headingLine) throws BulkUploadException {
		csvFieldMap = csvMapData.constructCsvFieldMap(headingLine);
	}

	// *********************************************************************************************
	// ********** VALIDATE UPLOADED FILE (FILE STRUCTURE THEN CONTENT) *****************************
	@Override
	void validationPreProcessing() {
		setupDataMapping();
		dateFormatter.setLenient(false);
	}

	@Override
	String validateTrans(String thisLine) {
		/*
		 * Original Validation Rules FIELDS:
		 * auxAccountName,agreementRef,amtInclTax,amtTax,transDate,accountRef,comment 
		 * COMMON FIELDS: amtInclTax,amtTax,transDate,accountRef,comment
		 * auxAccountName : (Required) 
		 * agreementRef : (Required, must-exists-in-DB) 
		 * 	> AuxAccountName + AgreementRef(customer_agreement_id) provide a unique AuxAccount(must-exists-in-DB, must-have-status-ACT).
		 */
		AuxTransCsvData auxTrans = new AuxTransCsvData(csvFieldMap, thisLine, WITH_ERROR_STRING);
		StringBuilder validationError = new StringBuilder();
		Long customerAgreementId = null;

		if (auxTrans.isUnexpectedCommas()) {
			validationError.append("bulk.upload.invalid.unexpected.commas");
			return validationError.toString();
		}

		// Aux Account Name
		if (!ValidateUtil.isNotNullOrBlank(auxTrans.getAuxAccountName())) {
			validationError.append(addValidationError("required", "auxaccount.trans.upload.auxaccountname"));
		} else if (auxTrans.getAuxAccountName().length() > 100) {
			validationError.append("auxaccount.trans.upload.invalid.auxaccountname").append(";");
		}

		// Agreement Ref
		if (!ValidateUtil.isNotNullOrBlank(auxTrans.getAgreementRef())) {
			validationError.append(addValidationError("required", "auxaccount.trans.upload.agreementref"));
		} else {
			CustomerAgreement ca = customerAgreementService.getCustomerAgreementByAgreementRef(auxTrans.getAgreementRef());
			if (ca == null) {
				validationError.append(addValidationError("invalid", "auxaccount.trans.upload.agreementref"));
			} else {
				customerAgreementId = ca.getId();
			}
		}

		// AuxAccountName + AgreementRef (Unique AuxAccount(must-exists-in-DB, must-have-status-ACT)).
		if (customerAgreementId != null && auxTrans.getAuxAccountName() != null) {
			AuxAccount auxAccount = auxAccountService.getAuxAccountByCustomerAgrIdAndAccName(customerAgreementId,
					auxTrans.getAuxAccountName());
			if (auxAccount == null) {
				validationError.append("auxaccount.trans.upload.invalid.auxaccount").append(";");
			} else if (!auxAccount.getRecordStatus().equals(RecordStatus.ACT)) {
				validationError.append("auxaccount.trans.upload.invalid.auxaccount").append(";");
			}
		}

		// Performs Validation of Common Fields
		validateTransCommonFields(validationError, auxTrans);

		String returnString = validationError.toString();
		int lastIndx = returnString.lastIndexOf(";");
		if (lastIndx > 0) {
			returnString = returnString.substring(0, lastIndx);
		}

		return returnString;
	}

	// ***********************************************************************************
	// ********** P R O C E S S U P L O A D   ********************************************
	@Override
	void processTransactionsPreProcessing() {
		setupDataMapping();
		processTransList = new ArrayList<AuxTransCsvData>();
		accountTransList = new ArrayList<AccountTrans>();
		dateFormatter.setLenient(false);
	}

	@Override
	void addToProcessList(String thisLine) {
		AuxTransCsvData auxTrans = new AuxTransCsvData(csvFieldMap, thisLine, WITH_ERROR_STRING);
		processTransList.add(auxTrans);
		accountTransList.add(createAccountTrans(auxTrans));
	}

	private AccountTrans createAccountTrans(AuxTransCsvData auxTrans) {

		CustomerAgreement ca = customerAgreementService.getCustomerAgreementByAgreementRef(auxTrans.getAgreementRef());
		Long customerAgreementId = ca.getId();
		logger.info("******* The customer agreement ref is " + ca.getAgreementRef());

		AuxAccount aa = auxAccountService.getAuxAccountByCustomerAgrIdAndAccName(customerAgreementId, auxTrans.getAuxAccountName());
		Long auxAccountId = aa.getId();
		logger.info("******* The aux account name is " + aa.getAccountName());

		AccountTrans accountTrans = createAccountTrans(auxTrans, customerAgreementId, auxAccountId);
		return accountTrans;
	}

	@Override
	String processUpload() {
		logger.info("**** Bulk AuxAccount Transaction adjustments input listSize=" + accountTransList.size());
		return bulkUploadService.auxTransBulkUpload(accountTransList, processTransList);
	}

}
