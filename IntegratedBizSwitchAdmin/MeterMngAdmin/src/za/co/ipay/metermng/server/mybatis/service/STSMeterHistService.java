package za.co.ipay.metermng.server.mybatis.service;

import java.util.List;

import org.apache.ibatis.session.RowBounds;
import org.springframework.transaction.annotation.Transactional;

import za.co.ipay.metermng.mybatis.generated.mapper.StsAlgorithmMapper;
import za.co.ipay.metermng.mybatis.generated.mapper.StsMeterHistMapper;
import za.co.ipay.metermng.mybatis.generated.mapper.StsSupplyGroupMapper;
import za.co.ipay.metermng.mybatis.generated.mapper.StsTokenTechMapper;
import za.co.ipay.metermng.mybatis.generated.model.StsAlgorithm;
import za.co.ipay.metermng.mybatis.generated.model.StsMeterHist;
import za.co.ipay.metermng.mybatis.generated.model.StsMeterHistExample;
import za.co.ipay.metermng.mybatis.generated.model.StsSupplyGroup;
import za.co.ipay.metermng.mybatis.generated.model.StsTokenTech;
import za.co.ipay.metermng.shared.STSMeterHistData;

public class STSMeterHistService {
    
    private StsMeterHistMapper stsMeterHistMapperImpl;
    private StsAlgorithmMapper stsAlgorithmMapper;
    private StsTokenTechMapper stsTokenTechMapper;
    private StsSupplyGroupMapper stsSupplyGroupMapper;
    
    public void setStsMeterHistMapperImpl(StsMeterHistMapper stsMeterHistMapper) {
        this.stsMeterHistMapperImpl = stsMeterHistMapper;
    }
    
    public void setStsAlgorithmMapper(StsAlgorithmMapper stsAlgorithmMapper) {
		this.stsAlgorithmMapper = stsAlgorithmMapper;
	}

	public void setStsTokenTechMapper(StsTokenTechMapper stsTokenTechMapper) {
		this.stsTokenTechMapper = stsTokenTechMapper;
	}

	public void setStsSupplyGroupMapper(StsSupplyGroupMapper stsSupplyGroupMapper) {
		this.stsSupplyGroupMapper = stsSupplyGroupMapper;
	}

	@Transactional(readOnly=true)
	public List<StsMeterHist> getMeterHistoryByMeterId(Long meterId) {
    	StsMeterHistExample stsMeterHistExample = new StsMeterHistExample();
    	stsMeterHistExample.createCriteria().andIdEqualTo(meterId);
    	stsMeterHistExample.setOrderByClause("date_rec_modified desc");
        List<StsMeterHist> list = stsMeterHistMapperImpl.selectByExample(stsMeterHistExample);
        return list;
    }
	
	   @Transactional(readOnly=true)
	    public Long getLatestMeterHistoryIdByMeterId(Long meterId) {
	        StsMeterHistExample stsMeterHistExample = new StsMeterHistExample();
	        stsMeterHistExample.createCriteria().andIdEqualTo(meterId);
	        stsMeterHistExample.setOrderByClause("date_rec_modified desc");
	        List<StsMeterHist> list = stsMeterHistMapperImpl.selectByExampleWithRowbounds(stsMeterHistExample, new RowBounds(0, 1));
	        if(list.isEmpty()) {
	            return null;
	        }
	        return list.get(0).getStsMeterHistId();
	    }
	
	@Transactional(readOnly=true)
	public STSMeterHistData updateStsInformation(STSMeterHistData stsMeterHistData) {
		if (stsMeterHistData.getStsAlgorithmCodeId() != null) {
			StsAlgorithm stsAlgorithm = stsAlgorithmMapper.selectByPrimaryKey(stsMeterHistData.getStsAlgorithmCodeId());
			if (stsAlgorithm != null) {
				stsMeterHistData.setAlgCode(stsAlgorithm.getStsAlgorithmCode());
			}
		}
		
		if (stsMeterHistData.getStsTokenTechId() != null) {
			StsTokenTech stsTokenTech = stsTokenTechMapper.selectByPrimaryKey(stsMeterHistData.getStsTokenTechId());
			if (stsTokenTech != null) {
				stsMeterHistData.setTokentechcode(stsTokenTech.getStsTokenTechCode());
			}
		}
		
		if (stsMeterHistData.getStsCurrSupplyGroupId() != null) {	
			StsSupplyGroup stsSupplyGroup = stsSupplyGroupMapper.selectByPrimaryKey(stsMeterHistData.getStsCurrSupplyGroupId());
			if (stsSupplyGroup != null) {
				stsMeterHistData.setStsCurrKeyRevisionNum(stsSupplyGroup.getKeyRevisionNum());
				stsMeterHistData.setStsCurrSupplyGroupCode(stsSupplyGroup.getSupplyGroupCode());
			}
		}
		return stsMeterHistData;
	}  
}
