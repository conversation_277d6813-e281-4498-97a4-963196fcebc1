package za.co.ipay.metermng.server.mybatis.service;

import static java.util.Calendar.MONTH;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Random;

import org.apache.log4j.Logger;
import org.springframework.jdbc.UncategorizedSQLException;
import org.springframework.transaction.annotation.Transactional;

import za.co.ipay.gwt.common.shared.exception.ServiceException;
import za.co.ipay.gwt.common.shared.exception.ValidationException;
import za.co.ipay.gwt.common.shared.exception.ValidationMessage;
import za.co.ipay.jdbc.exception.UniqueConstraintViolationException;
import za.co.ipay.metermng.mybatis.custom.mapper.MeterReadingCustomMapper;
import za.co.ipay.metermng.mybatis.custom.model.MeterReadingRange;
import za.co.ipay.metermng.mybatis.generated.mapper.MeterReadingFactMapper;
import za.co.ipay.metermng.mybatis.generated.mapper.MeterReadingMapper;
import za.co.ipay.metermng.mybatis.generated.mapper.ReadingQualityMapper;
import za.co.ipay.metermng.mybatis.generated.mapper.RegisterReadingMapper;
import za.co.ipay.metermng.mybatis.generated.mapper.TimeDimMapper;
import za.co.ipay.metermng.mybatis.generated.model.MeterReading;
import za.co.ipay.metermng.mybatis.generated.model.MeterReadingExample;
import za.co.ipay.metermng.mybatis.generated.model.MeterReadingFact;
import za.co.ipay.metermng.mybatis.generated.model.MeterReadingFactExample;
import za.co.ipay.metermng.mybatis.generated.model.MeterReadingType;
import za.co.ipay.metermng.mybatis.generated.model.ReadingQuality;
import za.co.ipay.metermng.mybatis.generated.model.RegisterReading;
import za.co.ipay.metermng.mybatis.generated.model.RegisterReadingExample;
import za.co.ipay.metermng.mybatis.generated.model.TimeDim;
import za.co.ipay.metermng.mybatis.generated.model.TimeDimExample;
import za.co.ipay.metermng.shared.dto.meter.MeterReadingVariation;

public class MeterReadingGeneratorService {

    private MeterReadingMapper meterReadingMapper;
    private ReadingQualityMapper readingQualityMapper;
    private MeterReadingCustomMapper meterReadingCustomMapper;
    private TimeDimMapper timeDimMapper;
    private MeterReadingFactMapper meterReadingFactMapper;
    private RegisterReadingMapper registerReadingMapper;
    private MdcChannelService mdcChannelService;
    
    private static final String TEST_QUALITY_TYPE = "1.4.5";
    
    private enum HourType {
        OFF_PEAK, PEAK, PM_PEAK, STANDARD
    }

    private static final HourType[] HOUR_TYPES = new HourType[] { HourType.OFF_PEAK, HourType.OFF_PEAK,
            HourType.OFF_PEAK, HourType.OFF_PEAK, HourType.OFF_PEAK, // 0,1,2,3,4
            HourType.OFF_PEAK, // 5
            HourType.STANDARD, HourType.PEAK, HourType.PEAK, // 6, 7, 8
            HourType.STANDARD, HourType.STANDARD, HourType.STANDARD, HourType.STANDARD, // 9, 10, 11, 12
            HourType.STANDARD, HourType.STANDARD, HourType.STANDARD, HourType.STANDARD, // 13, 14, 15, 16
            HourType.PM_PEAK, HourType.PM_PEAK, HourType.PM_PEAK, HourType.PM_PEAK, // 17, 18, 19, 20
            HourType.STANDARD, HourType.STANDARD, HourType.STANDARD // 21, 22, 23
    };

    private static Map<HourType, Double[]> hourTypeRanges;
    static {
        hourTypeRanges = new HashMap<HourType, Double[]>();
        hourTypeRanges.put(HourType.OFF_PEAK, new Double[] { 0.1, 0.3 });
        hourTypeRanges.put(HourType.STANDARD, new Double[] { 0.31, 0.6 });
        hourTypeRanges.put(HourType.PEAK, new Double[] { 0.61, 0.9 });
        hourTypeRanges.put(HourType.PM_PEAK, new Double[] { 1.0, 2.0 });
    }
    
    private static Map<HourType, Double[]> weekendHourTypeRanges;
    static {
        weekendHourTypeRanges = new HashMap<HourType, Double[]>();
        weekendHourTypeRanges.put(HourType.OFF_PEAK, new Double[] { 0.2, 0.4 });
        weekendHourTypeRanges.put(HourType.STANDARD, new Double[] { 0.41, 0.7 });
        weekendHourTypeRanges.put(HourType.PEAK, new Double[] { 0.71, 1.0 });
        weekendHourTypeRanges.put(HourType.PM_PEAK, new Double[] { 1.1, 2.2 });
    }
    
    private static final Logger logger = Logger.getLogger(MeterReadingGeneratorService.class);

    @Transactional(readOnly = false)
    public void generateMeterReadings(Long meterId, Long usagePointId, Date start, Date end, long intervalMinutes,
            List<Long> readingTypeIds, Map<Long, String> readingTypeValues, Date zeroStart, Date zeroEnd,
            int zeroInstances, Date missingStart, Date missingEnd, int missingInstances,
            final ArrayList<Long> mdcChannelIds)
            throws ValidationException, ServiceException {
        boolean isMonthlyInterval = intervalMinutes == 44640;
        Date earliestDate = new Date(start.getTime());
        Date latestDate = new Date(end.getTime());
        List<RegisterReading> existingRegisterReadings = null;
        List<MeterReading> existingMeterReadings = null;
        if (mdcChannelIds == null) {
            MeterReadingExample meterReadingExample = new MeterReadingExample();
            meterReadingExample.createCriteria().andMeterIdEqualTo(meterId);
            meterReadingExample.setOrderByClause("reading_start");
            existingMeterReadings = meterReadingMapper.selectByExample(meterReadingExample);
            if (existingMeterReadings != null && !existingMeterReadings.isEmpty()) {
                Date tempDate = existingMeterReadings.get(0).getReadingStart();
                if (!tempDate.after(earliestDate)) {
                    earliestDate.setTime(tempDate.getTime());
                }
                tempDate = existingMeterReadings.get(existingMeterReadings.size() - 1).getReadingStart();
                if (tempDate.after(latestDate)) {
                    latestDate.setTime(tempDate.getTime());
                }
            }
        } else {
            RegisterReadingExample registerReadingExample = new RegisterReadingExample();
            registerReadingExample.createCriteria().andMeterIdEqualTo(meterId)
                    .andReadingTimestampGreaterThanOrEqualTo(start).andReadingTimestampLessThan(end);
            registerReadingExample.setOrderByClause("reading_timestamp");
            existingRegisterReadings = registerReadingMapper.selectByExample(registerReadingExample);
        }

        Calendar factCal = Calendar.getInstance();

        factCal.setTime(earliestDate);
        factCal.set(Calendar.HOUR_OF_DAY, 00);
        factCal.set(Calendar.MINUTE, 00);
        factCal.set(Calendar.SECOND, 00);
        Date factDate = factCal.getTime();
        HashMap<Long, MeterReadingFact> mrfByReadingTypeMap = new HashMap<Long, MeterReadingFact>();

        long currentTime = earliestDate.getTime();
        Calendar calendar = Calendar.getInstance();
        double latestReading = 0;
        while (currentTime < latestDate.getTime()) {
            calendar.setTimeInMillis(currentTime);
            logger.info("Current time: " + calendar.getTime());
            int day = calendar.get(Calendar.DAY_OF_WEEK);
            int hourOfDay = calendar.get(Calendar.HOUR_OF_DAY);
            HourType hourType = HOUR_TYPES[hourOfDay];
            
            int currentMax = calendar.getActualMaximum(Calendar.DAY_OF_MONTH);
            if (isMonthlyInterval) {
                int currentDay = calendar.get(Calendar.DAY_OF_MONTH);
                if (currentDay >= 29) {
                    Calendar nextMonth = Calendar.getInstance();
                    nextMonth.setTimeInMillis(currentTime);
                    nextMonth.add(Calendar.MONTH, 1);
                    int nextMonthMax = nextMonth.getActualMaximum(Calendar.DAY_OF_MONTH);
                    if (currentDay > nextMonthMax) {
                        currentMax += nextMonthMax - currentDay;
                    }
                }
                intervalMinutes = 1440 * currentMax;
            }
            
            double reading = 0;
            if (mdcChannelIds == null) {
                factCal.clear();
                factCal.setTimeInMillis(currentTime);
                factCal.set(Calendar.HOUR_OF_DAY, 00);
                factCal.set(Calendar.MINUTE, 00);
                factCal.set(Calendar.SECOND, 00);
                Date nowDate = factCal.getTime();

                if (nowDate.compareTo(factDate) != 0) {
                    // write all the previous meterReadingFact per readingTypeId & reset totals to
                    // zero for each
                    mrfByReadingTypeMap = insertMeterReadingFacts(factDate, readingTypeIds, mrfByReadingTypeMap);
                    factDate = nowDate;
                }
            }
            
            Date currentDate = new Date(currentTime);
            for (Long meterReadingTypeId : readingTypeIds) {
                if (reading == 0) {
                    if (intervalMinutes <= 60) {
                        if (Calendar.SATURDAY == day || Calendar.SUNDAY == day) {
                            reading = calculateWeekEndReading(hourType);
                        } else {
                            reading = calculateWeekDayReading(hourType);
                        }
                    }

                    if (intervalMinutes == 30 || intervalMinutes == 60) {
                        reading *= intervalMinutes / 15;
                    } else if (intervalMinutes == 1440) {
                        reading = getDayReading(day);
                    } else if (intervalMinutes == 10080) {
                        for (HourType hourTypeTemp : HOUR_TYPES) {
                            reading += calculateWeekEndReading(hourTypeTemp) * 2;
                            reading += calculateWeekDayReading(hourTypeTemp) * 5;
                        }
                    } else if (intervalMinutes > 10080) {
                        Calendar tempCalendar = Calendar.getInstance();
                        tempCalendar.setTimeInMillis(currentTime);
                        for (int i = 0; i < currentMax; i++) {
                            reading += getDayReading(tempCalendar.get(Calendar.DAY_OF_WEEK));
                            tempCalendar.add(Calendar.DAY_OF_YEAR, 1);
                        }
                    }
                }
                if (missingStart != null && missingEnd != null && !missingStart.after(currentDate)
                        && missingEnd.after(currentDate)
                        && (missingInstances == -1 || new Random().nextInt(101) <= missingInstances)) {
                    reading = -1;
                }
                if (zeroStart != null && zeroEnd != null && !zeroStart.after(currentDate) && zeroEnd.after(currentDate)
                        && (zeroInstances == -1 || new Random().nextInt(101) <= zeroInstances)) {
                    reading = 0;
                }
                // TODO modify the reading per meter reading type

                RegisterReading registerReading = null;
                MeterReading meterReading = null;
                if (mdcChannelIds == null && existingMeterReadings != null && !existingMeterReadings.isEmpty()) {
                    for (int i = 0; i < existingMeterReadings.size(); i++) {
                        MeterReading meterReadingTemp = existingMeterReadings.get(i);
                        long time = meterReadingTemp.getReadingStart().getTime();
                        if (time < currentTime) {
                            updateMeterReadingTypeMap(mrfByReadingTypeMap, meterReadingTypeId,
                                    meterReadingTemp.getReadingValue(), meterId, usagePointId);
                            existingMeterReadings.remove(i--);
                        } else if (time == currentTime
                                && meterReadingTemp.getMeterReadingTypeId() == meterReadingTypeId) {
                            meterReading = meterReadingTemp;
                            existingMeterReadings.remove(i);
                            break;
                        } else if (time > currentTime) {
                            break;
                        }
                    }
                } else if (existingRegisterReadings != null && !existingRegisterReadings.isEmpty()) {
                    for (int i = 0; i < existingRegisterReadings.size(); i++) {
                        RegisterReading registerReadingTemp = existingRegisterReadings.get(i);
                        long time = registerReadingTemp.getReadingTimestamp().getTime();
                        if (time < currentTime) {
                            existingRegisterReadings.remove(i--);
                        } else if (time == currentTime
                                && registerReadingTemp.getMeterReadingTypeId() == meterReadingTypeId) {
                            registerReading = registerReadingTemp;
                            latestReading = registerReading.getReadingValue().doubleValue() + reading * 1000;
                            existingRegisterReadings.remove(i);
                            break;
                        } else if (time > currentTime) {
                            break;
                        }
                    }
                }

                if (mdcChannelIds == null) {
                    if (meterReading == null && reading != -1 && start.getTime() <= currentTime
                            && end.getTime() > currentTime) {
                        // insert meter reading for current time interval and meter reading type
                        meterReading = insertMeterReading(meterId, usagePointId, meterReadingTypeId,
                                readingTypeValues.get(meterReadingTypeId), currentTime, intervalMinutes,
                                reading * 1000);
                        insertReadingQuality(meterReading);
                    }
                    if (meterReading != null) {
                        updateMeterReadingTypeMap(mrfByReadingTypeMap, meterReadingTypeId,
                                meterReading.getReadingValue(), meterId, usagePointId);
                    }
                } else if (registerReading == null && reading != -1) {
                    latestReading += reading * 1000;
                    final long currentTimeTemp = currentTime;
                    final double latestReadingTemp = latestReading;
                    mdcChannelService
                            .getMdcChannelsForReadingTypesAndMeter(
                                    new ArrayList<Long>(Arrays.asList(meterReadingTypeId)), meterId)
                            .forEach(mdcChannel -> {
                                Long mdcChannelId = mdcChannel.getId();
                                if (mdcChannelIds.contains(mdcChannelId)) {
                                    insertRegisterReading(meterId, usagePointId, meterReadingTypeId,
                                            readingTypeValues.get(meterReadingTypeId), currentTimeTemp,
                                            latestReadingTemp, mdcChannelId);
                                }
                            });
                }
            }

            currentTime += intervalMinutes * 60 * 1000;
        } // end while
          // insert final meterReadingFact
        if (mdcChannelIds == null) {
            insertMeterReadingFacts(factDate, readingTypeIds, mrfByReadingTypeMap);
        }
    }
    
    private double getDayReading(int day) {
        double reading = 0;
        for (HourType hourTypeTemp : HOUR_TYPES) {
            if (Calendar.SATURDAY == day || Calendar.SUNDAY == day) {
                reading += calculateWeekEndReading(hourTypeTemp);
            } else {
                reading += calculateWeekDayReading(hourTypeTemp);
            }
        }
        return reading;
    }

    private void updateMeterReadingTypeMap(HashMap<Long, MeterReadingFact> mrfByReadingTypeMap, Long meterReadingTypeId,
            BigDecimal readingValue, Long meterId, Long usagePointId) {
        MeterReadingFact mrf = null;
        int numReadings = 1;
        if (mrfByReadingTypeMap.containsKey(meterReadingTypeId)) {
            mrf = mrfByReadingTypeMap.get(meterReadingTypeId);
            readingValue = mrf.getReadingValue().add(readingValue);
            numReadings += mrf.getNumReadings();
        } else {
            mrf = new MeterReadingFact();
            mrf.setMeterId(meterId);
            mrf.setMeterReadingTypeId(meterReadingTypeId);
            mrf.setUsagePointId(usagePointId);
        }
        mrf.setReadingValue(readingValue);
        mrf.setNumReadings(numReadings);
        mrfByReadingTypeMap.put(meterReadingTypeId, mrf);
    }

    private RegisterReading insertRegisterReading(Long meterId, Long usagePointId, Long meterReadingTypeId,
            String meterReadingTypeValue, long currentTime, double reading, Long mdcChannelId)
            throws ValidationException, ServiceException {
        try {
            RegisterReading registerReading = new RegisterReading();
            registerReading.setMeterId(meterId);
            registerReading.setReadingTimestamp(new Date(currentTime));
            registerReading.setReadingValue(new BigDecimal(reading));
            registerReading.setReadingType(meterReadingTypeValue);
            registerReading.setMeterReadingTypeId(meterReadingTypeId);
            registerReading.setUsagePointId(usagePointId);
            registerReading.setMdcChannelId(mdcChannelId);
            registerReading.setTest(true);
            registerReadingMapper.insert(registerReading);
            return registerReading;
        } catch (UncategorizedSQLException e) {
            if (e.getCause() instanceof UniqueConstraintViolationException) {
                throw new ValidationException(new ValidationMessage("demo.addmeterreadings.error.duplicates", true));
            } else {
                logger.debug("Error inserting registerReading: " + e.getMessage() + " " + e.getCause());
                throw new ServiceException("demo.addmeterreadings.error.insert", true);
            }
        }
    }

    private MeterReading insertMeterReading(Long meterId, Long usagePointId, Long meterReadingTypeId,
            String meterReadingTypeValue, long currentTime, long intervalMinutes, double reading)
            throws ValidationException, ServiceException {
        try {
            MeterReading meterReading = new MeterReading();
            meterReading.setDateCreated(new Date());
            meterReading.setMeterId(meterId);
            meterReading.setMeterReadingTypeId(meterReadingTypeId);
            meterReading.setReadingStart(new Date(currentTime));
            long time = currentTime + intervalMinutes * 60 * 1000;
            meterReading.setReadingEnd(new Date(time));
            meterReading.setReadingTimestamp(new Date(time + 1000 * 5));
            meterReading.setReadingType(meterReadingTypeValue);
            meterReading.setReadingValue(new BigDecimal(reading));
            meterReading.setUsagePointId(usagePointId);
            meterReading.setTest(true);
            meterReadingMapper.insert(meterReading);
            return meterReading;
        } catch (UncategorizedSQLException e) {
            if (e.getCause() instanceof UniqueConstraintViolationException) {
                throw new ValidationException(new ValidationMessage("demo.addmeterreadings.error.duplicates", true));
            } else {
                logger.debug("Error inserting meterReading: " + e.getMessage() + " " + e.getCause());
                throw new ServiceException("demo.addmeterreadings.error.insert", true);
            }
        }
    }
    
    private void insertReadingQuality(MeterReading meterReading) {
        ReadingQuality readingQuality = new ReadingQuality();
        readingQuality.setMeterReadingId(meterReading.getId());
        readingQuality.setQualityType(TEST_QUALITY_TYPE);
        readingQualityMapper.insert(readingQuality);
    }
    
    private HashMap<Long, MeterReadingFact> insertMeterReadingFacts(Date factDate, List<Long> readingTypeIds, HashMap<Long, MeterReadingFact> mrfByReadingTypeMap) 
                                         throws ValidationException, ServiceException {
        try {
            //make a timeDim if not one
            Calendar cal = Calendar.getInstance();
            cal.setTime(factDate);
            TimeDimExample tdExample = new TimeDimExample();
            tdExample.createCriteria().andTimeYearEqualTo(cal.get(Calendar.YEAR))
                                        .andTimeMonthEqualTo(cal.get(MONTH) + 1)
                                        .andTimeDayEqualTo(cal.get(Calendar.DAY_OF_MONTH));
            List<TimeDim> timeDimList = timeDimMapper.selectByExample(tdExample);
            TimeDim timeDim = null;
            if (timeDimList == null || timeDimList.size() < 1) {
                //not found - make a new one
                timeDim = new TimeDim();
                timeDim.setTimeYear(cal.get(Calendar.YEAR));
                int month = cal.get(MONTH);
                timeDim.setTimeQuarter(month / 3 + 1);
                timeDim.setTimeMonth(month + 1);
                timeDim.setTimeDay(cal.get(Calendar.DAY_OF_MONTH));
                timeDimMapper.insert(timeDim);
            } else {
                timeDim = timeDimList.get(0);
            }
            if (timeDim == null) {
                logger.debug("Error inserting timeDim for date: " + factDate.toString());
                throw new ServiceException("demo.addmeterreadings.error.insert.timeDim", true);
            }

            for(Long meterReadingTypeId : readingTypeIds) { 
                MeterReadingFact mrf = mrfByReadingTypeMap.get(meterReadingTypeId);
                mrf.setTimeDimId(timeDim.getId());
                meterReadingFactMapper.insert(mrf);

                mrf.setReadingValue(BigDecimal.ZERO);
                mrf.setNumReadings(0);
                mrfByReadingTypeMap.put(meterReadingTypeId, mrf); 
            }
        } catch (UncategorizedSQLException e) {            
            if (e.getCause() instanceof UniqueConstraintViolationException) {
                throw new ValidationException(new ValidationMessage("demo.addmeterreadings.error.duplicate.Facts", true));
            } else {
                logger.debug("Error inserting meterReadingFact: "+e.getMessage()+" "+e.getCause());
                throw new ServiceException("demo.addmeterreadings.error.insert.fact", true);
            }
        }
        return mrfByReadingTypeMap;
    }

    public void deleteMeterReadingFactsForMeter(Long meterId) {
        MeterReadingFactExample mrfExample = new MeterReadingFactExample();
        mrfExample.createCriteria().andMeterIdEqualTo(meterId);
        meterReadingFactMapper.deleteByExample(mrfExample);
    }
    
    private double calculateReading(Map<HourType, Double[]> ranges, HourType hourType) {
        Double[] range = ranges.get(hourType);
        Random rand = new Random();

        int min = Double.valueOf(range[0] * 100).intValue();
        int max = Double.valueOf(range[1] * 100).intValue();

        int randomNum = rand.nextInt((max - min) + 1) + min;
        double reading = Double.valueOf(randomNum) / 100;
        return reading;
    }
    
    private double calculateWeekDayReading(HourType hourType) {
        return calculateReading(hourTypeRanges, hourType);
    }

    private double calculateWeekEndReading(HourType hourType) {
        return calculateReading(weekendHourTypeRanges, hourType);
    }

    //Method to generate a super meter's meter readings using it's sub meters existing meter readings. 
    //The super meter's meter readings will contain variations for the specified hours and percentage. 
    //Otherwise the super meter's meter reading will be the total of the sub meters readings.  
    @Transactional(readOnly = false)
    public void generateSuperMeterReadings(Long superMeterId, Long superMeterUsagePointId,
                                            Date start, Date end, int intervalMinutes, 
                                            MeterReadingType meterReadingType,
                                            List<Long> subMeterIds,
                                            List<MeterReadingVariation> variations) {
        if (!subMeterIds.isEmpty()) {
            Long meterId = subMeterIds.get(0);
            List<MeterReadingRange> available = meterReadingCustomMapper.getAvailableMeterReadings(meterId, meterReadingType.getId());
            Calendar time = Calendar.getInstance();
            MeterReadingVariation variation = null;
            for(MeterReadingRange readingRange : available) {
                List<BigDecimal> values = meterReadingCustomMapper.getValuesForMeters(meterReadingType.getId(), readingRange.getStart(), readingRange.getEnd(), subMeterIds);
                BigDecimal total = BigDecimal.ZERO;
                time.setTime(readingRange.getStart());
                int hour = time.get(Calendar.HOUR_OF_DAY);
                for(BigDecimal value : values) {   
                    variation = getVariation(variations, hour);
                    if (variation != null) {
                        BigDecimal modValue = value.multiply(variation.getPercentage());                            
                        total = total.add(modValue);
                    } else {
                        total = total.add(value);
                    }
                }
                //Save the super meter's meter reading
                MeterReading meterReading = insertMeterReading(superMeterId, superMeterUsagePointId, meterReadingType.getId(), 
                                                               meterReadingType.getValue(), readingRange.getStart().getTime(), 
                                                               intervalMinutes, total.doubleValue());
                insertReadingQuality(meterReading);  
            } //end for readingRange
        }
    }
    
    private MeterReadingVariation getVariation(List<MeterReadingVariation> variations, int hour) {
        for(MeterReadingVariation v : variations) {
            if (v.getHourOfDay() == hour) {
                logger.debug("Got matching variation: "+v);
                return v;
            }
        }
        logger.debug("No matching variation for hour: "+hour);
        return null;
    }
    
    @Transactional
    public void updateMeterReadingsAfterDate(Long meterId, Long usagePointId, Date installationDate) {
        updateMeterReadingFact(meterId, usagePointId, installationDate);
        
        MeterReadingExample meterReadingExample = new MeterReadingExample();
        meterReadingExample.createCriteria().andMeterIdEqualTo(meterId).andReadingStartGreaterThanOrEqualTo(installationDate);
        MeterReading meterReading = new MeterReading();
        meterReading.setUsagePointId(usagePointId);
        meterReading.setDateCreated(null);
        meterReadingMapper.updateByExampleSelective(meterReading, meterReadingExample);
    }
    
    private void updateMeterReadingFact(Long meterId, Long usagePointId, Date installationDate)
                                        throws ValidationException, ServiceException {
        Calendar cal = Calendar.getInstance();
        cal.setTime(installationDate);
        int installYear = cal.get(Calendar.YEAR);
        int installMonth = cal.get(Calendar.MONTH) + 1;
        int installDay = cal.get(Calendar.DAY_OF_MONTH);
        
        //Read all the TimeDim's from date of installation going forward
        TimeDimExample tdExample = new TimeDimExample();
        tdExample.createCriteria().andTimeYearGreaterThanOrEqualTo(installYear).andTimeMonthGreaterThanOrEqualTo(installMonth).andTimeDayGreaterThanOrEqualTo(installDay);
        List<TimeDim> timeDimList = timeDimMapper.selectByExample(tdExample);
        if (timeDimList == null || timeDimList.size() < 1) {
            return;
        }

        //TODO maybe do this for efficiency, or compare what postgres actually does faster (spidy sense says join)
        // would need to do second query to find actual day which may need splitting
        //update meter_reading_fact mrf set usage_point_id = ? from time_dim td where mrf.meter_id = ? and mrf.time_dim_id = td.time_dim_id and time_year > ? and time_month > ? and time_day > ?
         
        //create a list of the TimeDim id's to read all the meterReadingFacts with & store the TimeDimId = installDate
        List<Long> checkTimeDimList = new ArrayList<Long>();
        Long installTimeDimId = null;
        for (TimeDim td : timeDimList) {
            checkTimeDimList.add(td.getId());
            if (td.getTimeYear() == installYear && td.getTimeMonth() == installMonth && td.getTimeDay() == installDay) {
                installTimeDimId = td.getId();
            }
        }

        //Read all meter_reading_facts for the meter_id from date of installation using the list of TimeDim id's
        MeterReadingFactExample mrfExample = new MeterReadingFactExample();
        mrfExample.createCriteria().andMeterIdEqualTo(meterId).andTimeDimIdIn(checkTimeDimList);
        List<MeterReadingFact> mrFactsforMeterList = meterReadingFactMapper.selectByExample(mrfExample);
        if (mrFactsforMeterList == null || mrFactsforMeterList.size() < 1) {
            return;
        }
        
        //find the meterReadingFacts for the day of the installation for the relevant meterReadingId's. These might have to split
        List<MeterReadingFact> mrFactInstallDayList = new ArrayList<MeterReadingFact>();
        if (installTimeDimId != null) {
            for (MeterReadingFact mrFact : mrFactsforMeterList) {
                if (mrFact.getTimeDimId() == installTimeDimId) {
                    mrFactInstallDayList.add(mrFact);
                }
            }
        }

        if (mrFactInstallDayList.size() > 0) {
            cal.set(Calendar.HOUR_OF_DAY, 0);
            Date startReadingsDate = cal.getTime();
            cal.set(Calendar.HOUR_OF_DAY, 24);
            Date endReadingsDate = cal.getTime();
            
            for (MeterReadingFact mrFact : mrFactInstallDayList) {
                mrFactsforMeterList.remove(mrFact);
 
                MeterReadingExample meterReadingExample = new MeterReadingExample();
                meterReadingExample.createCriteria().andMeterIdEqualTo(meterId).andMeterReadingTypeIdEqualTo(mrFact.getMeterReadingTypeId()).andReadingStartGreaterThanOrEqualTo(startReadingsDate).andReadingEndLessThanOrEqualTo(endReadingsDate);
                List<MeterReading> meterReadingList = meterReadingMapper.selectByExample(meterReadingExample);

                Integer numReadingsBefore = 0;
                BigDecimal readingValueBefore = new BigDecimal(0);
                Integer numReadingsAfter = 0;
                BigDecimal readingValueAfter = new BigDecimal(0);
                for (MeterReading mr : meterReadingList) {
                    if (mr.getReadingStart().before(installationDate)) {
                        numReadingsBefore++;
                        readingValueBefore = readingValueBefore.add(mr.getReadingValue());
                    } else {
                        numReadingsAfter++;
                        readingValueAfter = readingValueAfter.add(mr.getReadingValue());
                    }
                }

                mrFact.setNumReadings(numReadingsBefore);
                mrFact.setReadingValue(readingValueBefore);
                meterReadingFactMapper.updateByPrimaryKey(mrFact);

                MeterReadingFact newMeterReadingFact = new MeterReadingFact();
                newMeterReadingFact.setMeterId(meterId);
                newMeterReadingFact.setUsagePointId(usagePointId);
                newMeterReadingFact.setTimeDimId(mrFact.getTimeDimId());
                newMeterReadingFact.setMeterReadingTypeId(mrFact.getMeterReadingTypeId());
                newMeterReadingFact.setNumReadings(numReadingsAfter);
                newMeterReadingFact.setReadingValue(readingValueAfter);
                try {
                    meterReadingFactMapper.insert(newMeterReadingFact);
                } catch (UncategorizedSQLException e) {            
                    if (e.getCause() instanceof UniqueConstraintViolationException) {
                        throw new ValidationException(new ValidationMessage("demo.addmeterreadings.error.duplicate.Facts", true));
                    } else {
                        logger.debug("Error inserting meterReadingFact: "+e.getMessage()+" "+e.getCause());
                        throw new ServiceException("demo.addmeterreadings.error.insert.fact", true);
                    }
                }
            }
        }
        
        //rewrite all the remaining MeterReadingFact from AFTER the installationDate for this meter with the new usagePointId
        for (MeterReadingFact mrFact : mrFactsforMeterList) {
            mrFact.setUsagePointId(usagePointId);
            meterReadingFactMapper.updateByPrimaryKey(mrFact);
        }
        return;
    }
    
    public void setMeterReadingMapper(MeterReadingMapper meterReadingMapper) {
        this.meterReadingMapper = meterReadingMapper;
    }

    public void setReadingQualityMapper(ReadingQualityMapper readingQualityMapper) {
        this.readingQualityMapper = readingQualityMapper;
    }

    public void setMeterReadingCustomMapper(MeterReadingCustomMapper meterReadingCustomMapper) {
        this.meterReadingCustomMapper = meterReadingCustomMapper;
    }

    public void setTimeDimMapper(TimeDimMapper timeDimMapper) {
        this.timeDimMapper = timeDimMapper;
    }

    public void setMeterReadingFactMapper(MeterReadingFactMapper meterReadingFactMapper) {
        this.meterReadingFactMapper = meterReadingFactMapper;
    }
    
    public void setRegisterReadingMapper(RegisterReadingMapper registerReadingMapper) {
        this.registerReadingMapper = registerReadingMapper;
    }

    public void setMdcChannelService(MdcChannelService mdcChannelService) {
        this.mdcChannelService = mdcChannelService;
    }
}
