package za.co.ipay.metermng.server.bulkupload;

import java.math.BigDecimal;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;

import org.apache.commons.beanutils.BeanUtils;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

import za.co.ipay.gwt.common.shared.validation.ValidateUtil;
import za.co.ipay.metermng.datatypes.AccountTransTypeE;
import za.co.ipay.metermng.datatypes.RecordStatus;
import za.co.ipay.metermng.mybatis.generated.model.AccountTrans;
import za.co.ipay.metermng.server.mybatis.service.CustomerAgreementService;
import za.co.ipay.metermng.server.util.MeterMngUtil;
import za.co.ipay.metermng.shared.AccountTransData;
import za.co.ipay.metermng.shared.dto.uploaddata.transuploaddata.TransCsvData;

@Controller
public abstract class TransBulkUploadCommon extends BulkUploadController {

	@Autowired
	protected CustomerAgreementService customerAgreementService;

	protected static final boolean WITH_ERROR_STRING = false;
	private Logger logger = Logger.getLogger(TransBulkUploadCommon.class.getName());

	protected DateFormat dateFormatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
	{
		dateFormatter.setLenient(false);
	}

	protected ArrayList<AccountTrans> accountTransList;
	protected boolean isCustomerTransUpload;

	public void validateTransCommonFields(StringBuilder validationError, TransCsvData transCsvData) {
		/* 
		 * Original Validation Rules
		 * COMMON FIELDS: amtInclTax,amtTax,transDate,accountRef,comment
		 * amtInclTax : (Required, must-be-NUMBER)
		 * amtTax : (Required, must-be-NUMBER)
		 * transDate : (If provided : must-have-valid-format, must-not-be-in-the-future. If not provided: use-current-time). 
		 * accountRef : (Required, 100-Chars-Max)
		 * ourRef : (Required, 100-Chars-Max)
		 * comment : (If provided : 255-Chars-Max)
		 */

		// amtInclTax
		if (ValidateUtil.isNullOrBlank(transCsvData.getAmtInclTax())) {
			validationError.append(addValidationError("required", "trans.bulk.upload.amt.incl.tax"));
		} else {
			try {
				BigDecimal amtInclTax = new BigDecimal(transCsvData.getAmtInclTax());
			} catch (NumberFormatException n) {
				validationError.append("trans.bulk.upload.invalid.amt.incl.tax").append(";");
			}
		}

		// amtTax
		if (ValidateUtil.isNullOrBlank(transCsvData.getAmtTax())) {
			validationError.append(addValidationError("required", "trans.bulk.upload.amt.tax"));
		} else {
			try {
				BigDecimal amtTax = new BigDecimal(transCsvData.getAmtTax());
			} catch (NumberFormatException n) {
				validationError.append("trans.bulk.upload.invalid.amt.tax").append(";");
			}
		}

		// transDate
		if (ValidateUtil.isNotNullOrBlank(transCsvData.getTransDate())) {
	           if (!MeterMngUtil.isValidDateFormat(transCsvData.getTransDate(), null)) {
	                validationError.append("trans.bulk.upload.format.error.trans.date").append(";");
	            } else {
	                try {
	                    Date date = dateFormatter.parse(transCsvData.getTransDate());
	                    // may not be in future
	                    if (date.after(new Date())) {
	                        validationError.append("trans.bulk.upload.trans.date.in.future").append(";");
	                    }
	                } catch (ParseException e) {
	                    validationError.append("trans.bulk.upload.invalid.trans.date").append(";");
	                }
	            }
	        }

		// accountRef
		if (ValidateUtil.isNullOrBlank(transCsvData.getAccountRef())) {
			validationError.append(addValidationError("required", "trans.bulk.upload.account.ref"));
		} else {
			if (transCsvData.getAccountRef().length() > 100) {
				validationError.append("trans.bulk.upload.invalid.account.ref").append(";");
			}
		}

		// ourRef
		if (ourRef.length() > 100) {
			validationError.append("trans.bulk.upload.invalid.our.ref").append(";");
		}

		// comment
		if (transCsvData.getComment() != null) {
			if (transCsvData.getComment().length() > 255) {
				validationError.append("trans.bulk.upload.invalid.comment").append(";");
			}
		}
	}

	protected AccountTransData createAccountTrans(TransCsvData transCsvData, Long agreementId, Long accountId) {
		AccountTransData accountTrans = new AccountTransData();
		HashMap<String, Object> properties = new HashMap<String, Object>();

		Long customerAgreementId = agreementId;
		Long customerAccountId = null;
		Long auxAccountId = null;

		if (isCustomerTransUpload) {
			customerAccountId = accountId;
			properties.put("customerAccountId", customerAccountId);
		} else {
			auxAccountId = accountId;
			properties.put("auxAccountId", auxAccountId);
		}

		properties.put("customerAgreementId", customerAgreementId);
		properties.put("accountTransTypeId", AccountTransTypeE.ADJUSTMENT.getId());

		Date now = new Date();

		if (ValidateUtil.isNullOrBlank(transCsvData.getTransDate())) {
			properties.put("transDate", now);
		} else {
			try {
				Date transDate = dateFormatter.parse(transCsvData.getTransDate());
				properties.put("transDate", transDate);
			} catch (ParseException e) {
				// validated above!
			}
		}
		properties.put("dateEntered", now);
		properties.put("comment", transCsvData.getComment());
		properties.put("accountRef", transCsvData.getAccountRef());
		properties.put("ourRef", ourRef);
		properties.put("amtInclTax", new BigDecimal(transCsvData.getAmtInclTax()));
		properties.put("amtTax", new BigDecimal(transCsvData.getAmtTax()));
		properties.put("userRecEntered", user);
		properties.put("recordStatus", RecordStatus.ACT);

		try {
			BeanUtils.populate(accountTrans, properties);
		} catch (Exception e) {
			logger.debug("TransBulkUploadCommon: createAccountTrans: Beanutils failed!! exception = " + e.getMessage());
			return null;
		}
		logger.debug("new AccountTrans: customerAgreementId=" + accountTrans.getCustomerAgreementId()
				+ ", customerAccountId=" + accountTrans.getCustomerAccountId() + ", auxAccountId="
				+ accountTrans.getAuxAccountId());
		return accountTrans;
	}

	public boolean isCustomerTransUpload() {
		return isCustomerTransUpload;
	}

	public void setCustomerTransUpload(boolean isCustomerTransUpload) {
		this.isCustomerTransUpload = isCustomerTransUpload;
	}

}
