package za.co.ipay.metermng.server.mybatis.service;

import org.apache.commons.beanutils.BeanUtils;
import org.springframework.transaction.annotation.Transactional;
import za.co.ipay.gwt.common.shared.exception.ServiceException;
import za.co.ipay.metermng.datatypes.CustomerTransTypeE;
import za.co.ipay.metermng.mybatis.custom.mapper.CustomerTransCustomMapper;
import za.co.ipay.metermng.mybatis.custom.model.CustomerTransAlphaData;
import za.co.ipay.metermng.mybatis.custom.model.CustomerTransAlphaDataWithTotals;
import za.co.ipay.metermng.mybatis.generated.mapper.CustomerAgreementMapper;
import za.co.ipay.metermng.mybatis.generated.mapper.CustomerMapper;
import za.co.ipay.metermng.mybatis.generated.mapper.CustomerTransExtraMapper;
import za.co.ipay.metermng.mybatis.generated.mapper.CustomerTransItemMapper;
import za.co.ipay.metermng.mybatis.generated.mapper.CustomerTransMapper;
import za.co.ipay.metermng.mybatis.generated.mapper.MeterMapper;
import za.co.ipay.metermng.mybatis.generated.mapper.StsMeterMapper;
import za.co.ipay.metermng.mybatis.generated.mapper.TariffMapper;
import za.co.ipay.metermng.mybatis.generated.mapper.TransItemTypeMapper;
import za.co.ipay.metermng.mybatis.generated.mapper.UsagePointMapper;
import za.co.ipay.metermng.mybatis.generated.model.CustomerTrans;
import za.co.ipay.metermng.mybatis.generated.model.CustomerTransExample;
import za.co.ipay.metermng.mybatis.generated.model.CustomerTransExtra;
import za.co.ipay.metermng.mybatis.generated.model.CustomerTransExtraExample;
import za.co.ipay.metermng.mybatis.generated.model.CustomerTransItem;
import za.co.ipay.metermng.mybatis.generated.model.CustomerTransItemExample;
import za.co.ipay.metermng.mybatis.generated.model.TransItemType;
import za.co.ipay.metermng.shared.CustomerTransItemData;

import java.lang.reflect.InvocationTargetException;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

@SuppressWarnings("unused")
public class CustomerTransService {

    private CustomerTransMapper customerTransMapper;
    private CustomerTransItemMapper customerTransItemMapper;
    private UsagePointMapper usagePointMapper;
    private CustomerAgreementMapper customerAgreementMapper;
    private CustomerMapper customerMapper;
    private MeterMapper meterMapper;
    private StsMeterMapper stsMeterMapper;
    private TransItemTypeMapper transItemTypeMapper;
    private CustomerTransCustomMapper customerTransCustomMapper;
    private CustomerTransExtraMapper customerTransExtraMapper;
    private TariffMapper tariffMapper;

    public void setCustomerTransMapper(CustomerTransMapper customerTransMapper) {
        this.customerTransMapper = customerTransMapper;
    }

    public void setCustomerTransItemMapper(CustomerTransItemMapper customerTransItemMapper) {
        this.customerTransItemMapper = customerTransItemMapper;
    }

    public void setUsagePointMapper(UsagePointMapper usagePointMapper) {
        this.usagePointMapper = usagePointMapper;
    }

    public void setCustomerAgreementMapper(CustomerAgreementMapper customerAgreementMapper) {
        this.customerAgreementMapper = customerAgreementMapper;
    }

    public void setCustomerMapper(CustomerMapper customerMapper) {
        this.customerMapper = customerMapper;
    }

    public void setMeterMapper(MeterMapper meterMapper) {
        this.meterMapper = meterMapper;
    }

    public void setStsMeterMapper(StsMeterMapper stsMeterMapper) {
        this.stsMeterMapper = stsMeterMapper;
    }

    public void setTransItemTypeMapper(TransItemTypeMapper transItemTypeMapper) {
        this.transItemTypeMapper = transItemTypeMapper;
    }

    public void setCustomerTransCustomMapper(CustomerTransCustomMapper customerTransCustomMapper) {
        this.customerTransCustomMapper = customerTransCustomMapper;
    }

    public void setCustomerTransExtraMapper (CustomerTransExtraMapper customerTransExtraMapper) {
        this.customerTransExtraMapper = customerTransExtraMapper;
    }

    public void setTariffMapper(TariffMapper tariffMapper) {
    	this.tariffMapper = tariffMapper;
    }
    //------------------------------------------------------------------------------------------------------------------------------

    @Transactional(readOnly=true)
    public List<CustomerTransAlphaData> getCustomerTransByMeterId(Long meterId) {
    	return customerTransCustomMapper.selectCustomerTransForMeterHistory(meterId);
    }

    @Transactional(readOnly=true)
    public List<CustomerTransAlphaDataWithTotals> getCustomerTransWithTotalsByMeterId(Long meterId) {
        return customerTransCustomMapper.selectCustomerTransForMeterHistoryWithTotals(meterId);
    }

    @Transactional(readOnly=true)
    public List<CustomerTransAlphaData> getCustomerVendTransByMeterId(Long meterId) {
        return customerTransCustomMapper.selectCustomerTransForMeterVendingHistory(meterId, CustomerTransTypeE.VEND.getId());
    }

    public List<CustomerTransItemData> getCustomerTransItem(CustomerTransAlphaDataWithTotals customerTransData) throws ServiceException{
        List<CustomerTransItemData> custTransItemDataList = getCustomerTransItem(customerTransData.getId());

        if (custTransItemDataList.isEmpty()) { 
            // No corresponding customer_trans_item found for customer_trans_id. Use the single customer_trans entry.
            CustomerTransItemData customerTransItemData = new CustomerTransItemData();
            
            customerTransItemData.setTransItemTypeDetails(new TransItemType());
            customerTransItemData.setCustomerTransId(customerTransData.getId());
            try {
                BeanUtils.copyProperties(customerTransItemData, customerTransData);
            } catch (Exception e) {
                throw new ServiceException(e.getMessage());
            }
            custTransItemDataList.add(customerTransItemData);
        }
        return custTransItemDataList;
    }
    
    @Transactional(readOnly=true)
    public List<CustomerTransItemData> getCustomerTransItem(Long customerTransId) throws ServiceException{
        CustomerTransItemExample customerTransItemExample = new CustomerTransItemExample();
        customerTransItemExample.createCriteria().andCustomerTransIdEqualTo(customerTransId);
        customerTransItemExample.setOrderByClause("customer_trans_item_id asc");
        List<CustomerTransItem> itemList = customerTransItemMapper.selectByExample(customerTransItemExample);

        List<CustomerTransItemData> custTransItemDataList = new ArrayList<CustomerTransItemData>();

        HashMap<Long, TransItemType> transItemTypeMap = new HashMap<Long, TransItemType>();

        if (itemList != null && !itemList.isEmpty()) {
            for (CustomerTransItem cit : itemList ) {
                CustomerTransItemData customerTransItemData = new CustomerTransItemData(cit);
                if (customerTransItemData.getTransItemType() != null) {
                    if (!transItemTypeMap.containsKey(customerTransItemData.getTransItemType())) {
                        TransItemType transitemtype = transItemTypeMapper.selectByPrimaryKey(customerTransItemData.getTransItemType());
                        transItemTypeMap.put(customerTransItemData.getTransItemType(), transitemtype);
                    }
                    customerTransItemData.setTransItemTypeDetails(transItemTypeMap.get(customerTransItemData.getTransItemType()));
                }
                custTransItemDataList.add(customerTransItemData);
            }
        }
        custTransItemDataList.sort((o1, o2) -> o2.getTransItemTypeDetails().getName()
                .compareToIgnoreCase(o1.getTransItemTypeDetails().getName()));
        return custTransItemDataList;
    }


    @Transactional(readOnly=true)
    public int getCustomerTransCountByMeterId(Long meterId) {
        CustomerTransExample customerTransExample = new CustomerTransExample();
        customerTransExample.createCriteria().andMeterIdEqualTo(meterId);
        return customerTransMapper.countByExample(customerTransExample);
    }

    @Transactional(readOnly=true)
    public List<CustomerTransAlphaData> getCustomerTransByUsagePointId(Long upId) {
        return customerTransCustomMapper.selectCustomerTransForUsagePointHistory(upId);
    }

    @Transactional
    public void updateCustTransLastReprintDate(Long customerTransId, Date reprintDate) throws ServiceException {
        customerTransCustomMapper.updateCustTransLastReprintDate(customerTransId, reprintDate);
    }
    
    @Transactional
    public void updateCustTransLastReprintDateBulk(List<Long> customerTransIds, Date reprintDate) throws ServiceException {
        customerTransCustomMapper.updateCustTransLastReprintDateBulk(customerTransIds, reprintDate);
    }

    @Transactional(readOnly=true)
    public Boolean getCustTransIsReversed(Long customerTransId) {
        return customerTransMapper.selectByPrimaryKey(customerTransId).isReversed();
    }

    @Transactional
    public void createCustTransExtra(Long customerTransId, Date reprintDate, String userName) {
        //add an entry for the reprint to the Customer_trans_extra
        CustomerTransExtra customerTransExtra = new CustomerTransExtra();
        customerTransExtra.setCustomerTransId(customerTransId);
        customerTransExtra.setReprintDate(reprintDate);
        customerTransExtra.setClient("MeterMngAdmin");
        customerTransExtra.setUsername(userName);
        customerTransExtraMapper.insert(customerTransExtra);
    }

    @Transactional(readOnly=true)
    public List<CustomerTransExtra> getCustTransExtraListOrderByReprintDateAsc(Long customerTransId) {
        CustomerTransExtraExample cteExample = new CustomerTransExtraExample();
        cteExample.createCriteria().andCustomerTransIdEqualTo(customerTransId);
        cteExample.setOrderByClause("reprint_date asc");
        return customerTransExtraMapper.selectByExample(cteExample);
    }

    @Transactional(readOnly = true)
    public List<CustomerTransItem> getCustomerTransItemsForMeterId(Long meterId) {
        return customerTransCustomMapper.getCustomerTransItemsForMeter(meterId);
    }

    @Transactional(readOnly=true)
    public List<CustomerTransAlphaDataWithTotals>getCustomerTransWithTotalsByUsagePointId(Long usagepointId) {
        return customerTransCustomMapper.selectCustomerTransForUsagePointHistoryWithTotals(usagepointId);
    }
}
