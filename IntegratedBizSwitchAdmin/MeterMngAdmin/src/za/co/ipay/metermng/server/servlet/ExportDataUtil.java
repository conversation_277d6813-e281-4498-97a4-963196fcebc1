package za.co.ipay.metermng.server.servlet;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Iterator;
import java.util.List;
import java.util.Locale;

import org.springframework.context.support.AbstractMessageSource;
import org.springframework.context.support.DefaultMessageSourceResolvable;

import za.co.ipay.metermng.client.view.workspace.meter.readings.view.StartDateComparator;
import za.co.ipay.metermng.mybatis.generated.model.MeterReadingType;
import za.co.ipay.metermng.shared.dto.meter.MeterReadingDto;
import za.co.ipay.metermng.shared.dto.meter.MeterReadingsDto;
import za.co.ipay.metermng.shared.utils.MeterMngCommonUtil;
import za.co.ipay.utils.files.CsvWriter;

public class ExportDataUtil {

    public static CsvWriter getMeterReadings(AbstractMessageSource messageSource, AbstractMessageSource formatSource, Locale locale, MeterReadingsDto readings) {
        String[] fieldNames = new String[] {
                messageSource.getMessage(new DefaultMessageSourceResolvable("export.field.meter"), locale),
                messageSource.getMessage(new DefaultMessageSourceResolvable("export.field.date"), locale),
                messageSource.getMessage(new DefaultMessageSourceResolvable("export.field.start"), locale),
                messageSource.getMessage(new DefaultMessageSourceResolvable("export.field.end"), locale),
                messageSource.getMessage(new DefaultMessageSourceResolvable("export.field.reading"), locale),
                messageSource.getMessage(new DefaultMessageSourceResolvable("export.field.receiptnum"), locale) };
        CsvWriter writer = new CsvWriter(fieldNames, true);
        if (readings.getReadings().size() > 0) {
            Iterator<String> numbers = readings.getReadings().keySet().iterator();
            String name = "";
            while (numbers.hasNext()) {
                name = numbers.next();
                ArrayList<MeterReadingDto> r = readings.getReadings().get(name);
                writeRows(formatSource, locale, r, writer);
            }
        }
        return writer;
    }

    public static CsvWriter getMeterBalancingReadings(AbstractMessageSource messageSource, 
                                                        AbstractMessageSource formatSource, 
                                                        Locale locale, 
                                                        MeterReadingsDto readings,
                                                        BigDecimal variation) {
        String[] fieldNames = new String[] {
                messageSource.getMessage(new DefaultMessageSourceResolvable("export.field.metertype"), locale),
                messageSource.getMessage(new DefaultMessageSourceResolvable("export.field.date"), locale),
                messageSource.getMessage(new DefaultMessageSourceResolvable("export.field.start"), locale),
                messageSource.getMessage(new DefaultMessageSourceResolvable("export.field.end"), locale),
                messageSource.getMessage(new DefaultMessageSourceResolvable("export.field.reading"), locale) };
        CsvWriter writer = new CsvWriter(fieldNames, true);
        if (readings.getReadings().size() > 0) {
            // super meter line
            List<Object> row = new ArrayList<Object>();
            row.add(messageSource.getMessage(new DefaultMessageSourceResolvable("meterreadings.meter.field.super"), locale));
            row.add(readings.getBalancingMeter());
            writer.addRow(row);

            //sub meters line
            row.clear();
            row.add(messageSource.getMessage(new DefaultMessageSourceResolvable("meterreadings.meter.field.subs"), locale));
            for (String sub : readings.getSubMeters()) {
                row.add(sub);
            }
            writer.addRow(row);
            
            //variance line
            if (variation != null) {
                row.clear();
                row.add(messageSource.getMessage(new DefaultMessageSourceResolvable("energybalancing.variation"), locale));
                row.add(variation.toPlainString()+"%");
                writer.addRow(row);
            }
            
            // readings
            Iterator<String> numbers = readings.getReadings().keySet().iterator();
            String name = "";
            ArrayList<MeterReadingDto> all = new ArrayList<MeterReadingDto>();
            while (numbers.hasNext()) {
                name = numbers.next();
                ArrayList<MeterReadingDto> r = readings.getReadings().get(name);
                all.addAll(r);
            }
            Collections.sort(all, new StartDateComparator());
            writeRows(formatSource, locale, all, writer);
        }
        return writer;
    }

    public static CsvWriter getCustomerTransDetails(AbstractMessageSource messageSource,
            AbstractMessageSource formatSource, Locale locale, List<ArrayList<String>> list) {
        return getCsvDetails(messageSource, formatSource, locale, list,
                new String[] { messageSource.getMessage(new DefaultMessageSourceResolvable("meter.txn.date"), locale),
                        messageSource.getMessage(new DefaultMessageSourceResolvable("meter.txn.type"), locale),
                        messageSource.getMessage(new DefaultMessageSourceResolvable("meter.txn.customer"), locale),
                        messageSource.getMessage(new DefaultMessageSourceResolvable("meter.txn.client"), locale),
                        messageSource.getMessage(new DefaultMessageSourceResolvable("meter.txn.term"), locale),
                        messageSource.getMessage(new DefaultMessageSourceResolvable("meter.txn.ref"), locale),
                        messageSource.getMessage(new DefaultMessageSourceResolvable("meter.txn.isreversed"), locale),
                        messageSource.getMessage(new DefaultMessageSourceResolvable("meter.txn.revref"), locale),
                        messageSource.getMessage(new DefaultMessageSourceResolvable("meter.txn.reversal.reason"),
                                locale),
                        messageSource.getMessage(new DefaultMessageSourceResolvable("meter.txn.reversed.by"), locale),
                        messageSource.getMessage(new DefaultMessageSourceResolvable("meter.txn.receiptnum"), locale),
                        messageSource.getMessage(new DefaultMessageSourceResolvable("meter.txn.amount.column"), locale),
                        messageSource.getMessage(new DefaultMessageSourceResolvable("meter.txn.type"), locale),
                        messageSource.getMessage(new DefaultMessageSourceResolvable("meter.txn.description"), locale),
                        messageSource.getMessage(new DefaultMessageSourceResolvable("meter.txn.token"), locale),
                        messageSource.getMessage(new DefaultMessageSourceResolvable("meter.txn.amount"), locale),
                        messageSource.getMessage(new DefaultMessageSourceResolvable("meter.txn.tax"), locale),
                        messageSource.getMessage(new DefaultMessageSourceResolvable("meter.txn.units"), locale),
                        messageSource.getMessage(new DefaultMessageSourceResolvable("meter.txn.tariff"), locale) });
    }

    public static CsvWriter getCustomerAccountTransactionDetails(AbstractMessageSource messageSource,
            AbstractMessageSource formatSource, Locale locale, List<ArrayList<String>> list) {
        return getCsvDetails(messageSource, formatSource, locale, list,
                new String[] {
                        messageSource.getMessage(new DefaultMessageSourceResolvable("customer.txn.ent.date"), locale),
                        messageSource.getMessage(new DefaultMessageSourceResolvable("customer.txn.user"), locale),
                        messageSource.getMessage(new DefaultMessageSourceResolvable("customer.txn.trans.type"), locale),
                        messageSource.getMessage(new DefaultMessageSourceResolvable("customer.txn.trans.date"), locale),
                        messageSource.getMessage(new DefaultMessageSourceResolvable("customer.txn.comment"), locale),
                        messageSource.getMessage(new DefaultMessageSourceResolvable("customer.txn.our.ref"), locale),
                        messageSource.getMessage(new DefaultMessageSourceResolvable("customer.txn.amt"), locale),
                        messageSource.getMessage(new DefaultMessageSourceResolvable("customer.txn.tax"), locale),
                        messageSource.getMessage(new DefaultMessageSourceResolvable("customer.txn.bal"), locale) });
    }

    public static CsvWriter getUsagePointUnitsAccountTransactionDetails(AbstractMessageSource messageSource,
            AbstractMessageSource formatSource, Locale locale, List<ArrayList<String>> list) {
        return getCsvDetails(messageSource, formatSource, locale, list,
                new String[] {
                        messageSource.getMessage(new DefaultMessageSourceResolvable("customer.txn.ent.date"), locale),
                        messageSource.getMessage(new DefaultMessageSourceResolvable("customer.txn.user"), locale),
                        messageSource.getMessage(new DefaultMessageSourceResolvable("units.transaction.type"), locale),
                        messageSource.getMessage(new DefaultMessageSourceResolvable("customer.txn.trans.date"), locale),
                        messageSource.getMessage(new DefaultMessageSourceResolvable("customer.txn.comment"), locale),
                        messageSource.getMessage(new DefaultMessageSourceResolvable("customer.txn.our.ref"), locale),
                        messageSource.getMessage(new DefaultMessageSourceResolvable("usagepoint.txn.amt"), locale),
                        messageSource.getMessage(new DefaultMessageSourceResolvable("customer.txn.bal"), locale) });
    }

    private static CsvWriter getCsvDetails(AbstractMessageSource messageSource, AbstractMessageSource formatSource,
            Locale locale, List<ArrayList<String>> list, String[] fieldNames) {
        CsvWriter writer = new CsvWriter(fieldNames, true);
        for (ArrayList<String> row : list) {
            writer.addRow(row);
        }
        return writer;
    }
    
    public static CsvWriter getAdvancedSearchCsv(AbstractMessageSource messageSource, AbstractMessageSource formatSource, Locale locale, List<ArrayList<String>> list) {
        String[] fieldNames = new String[] {
                messageSource.getMessage(new DefaultMessageSourceResolvable("meter.number.column"), locale)
                };
        return getCsvDetails(messageSource, formatSource, locale, list,fieldNames);
    }

    private static void writeRows(AbstractMessageSource formatSource, Locale locale, List<MeterReadingDto> all,
            CsvWriter writer) {
        SimpleDateFormat dateFormat = new SimpleDateFormat(formatSource.getMessage(new DefaultMessageSourceResolvable("date.pattern"), locale));
        SimpleDateFormat timeFormat = new SimpleDateFormat(formatSource.getMessage(new DefaultMessageSourceResolvable("time.pattern"), locale));
        DecimalFormat decimalFormat = new DecimalFormat(formatSource.getMessage(new DefaultMessageSourceResolvable("decimal.pattern"), locale));
        List<Object> row = new ArrayList<Object>();
        for (MeterReadingDto reading : all) {
            row.clear();
            row.add(reading.getMeterNumber());
            row.add(dateFormat.format(reading.getStart()));
            row.add(timeFormat.format(reading.getStart()));
            row.add(timeFormat.format(reading.getEnd()));
            row.add(decimalFormat.format(reading.getReading() / 1000));
            String receiptNum = reading.getReceiptNum();
            if (receiptNum == null) {
                receiptNum = "";
            }
            row.add(receiptNum);
            writer.addRow(row);
        }
    }
    
    public static String getMeterReadingsFileName(AbstractMessageSource messageSource, Locale locale,
            String meterNumber, MeterReadingType readingType, String fileExtension) {
        return messageSource.getMessage(new DefaultMessageSourceResolvable("export.singlemeter.filename.prefix"),
                locale) + meterNumber + "-" + getFileName(readingType.getName()) + "-"
                + getFileName(MeterMngCommonUtil.getCorrectedUnitOfMeasure(readingType.getUnitOfMeasure()))
                + fileExtension;
    }

    public static String getEnergyBalancingFileName(AbstractMessageSource messageSource, Locale locale,
            String meterNumber, MeterReadingType readingType, String fileExtension) {
        return messageSource.getMessage(new DefaultMessageSourceResolvable("export.energybalancing.filename.prefix"),
                locale) + meterNumber + "-" + getFileName(readingType.getName()) + "-"
                + getFileName(MeterMngCommonUtil.getCorrectedUnitOfMeasure(readingType.getUnitOfMeasure()))
                + fileExtension;
    }
    
    public static String getFileName(String name) {        
        return name.replaceAll("[^a-zA-Z0-9.-]", "_");
    }

    private ExportDataUtil() {
        // no instances required
    }
}
