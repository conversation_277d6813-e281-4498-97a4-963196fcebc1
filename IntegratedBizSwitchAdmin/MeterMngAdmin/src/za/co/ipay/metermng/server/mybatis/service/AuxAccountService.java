package za.co.ipay.metermng.server.mybatis.service;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

import org.springframework.transaction.annotation.Transactional;

import za.co.ipay.gwt.common.shared.exception.ServiceException;
import za.co.ipay.gwt.common.shared.exception.ValidationException;
import za.co.ipay.gwt.common.shared.exception.ValidationMessage;
import za.co.ipay.metermng.datatypes.RecordStatus;
import za.co.ipay.metermng.mybatis.custom.mapper.AuxSupportMapper;
import za.co.ipay.metermng.mybatis.generated.mapper.AuxAccountMapper;
import za.co.ipay.metermng.mybatis.generated.mapper.AuxChargeScheduleMapper;
import za.co.ipay.metermng.mybatis.generated.mapper.AuxTypeMapper;
import za.co.ipay.metermng.mybatis.generated.mapper.SpecialActionReasonsLogMapper;
import za.co.ipay.metermng.mybatis.generated.model.AuxAccount;
import za.co.ipay.metermng.mybatis.generated.model.AuxAccountExample;
import za.co.ipay.metermng.mybatis.generated.model.AuxChargeSchedule;
import za.co.ipay.metermng.mybatis.generated.model.AuxChargeScheduleExample;
import za.co.ipay.metermng.mybatis.generated.model.AuxType;
import za.co.ipay.metermng.mybatis.generated.model.CustomerAgreement;
import za.co.ipay.metermng.server.mybatis.mapper.ICustomerAgreementAuxAccntMapper;
import za.co.ipay.metermng.server.validation.ServerValidatorUtil;
import za.co.ipay.metermng.service.auxaccount.AuxAccountCustomService;
import za.co.ipay.metermng.shared.AuxAccountData;
import za.co.ipay.metermng.shared.auxaccounthist.CustAuxAccountHistData;

public class AuxAccountService {

    private AuxAccountMapper auxAccountMapper;
    private AuxChargeScheduleMapper auxChargeScheduleMapper;
    private AuxTypeMapper auxTypeMapper;
    private ICustomerAgreementAuxAccntMapper customerAgreementAuxAccountMapper;
    private SpecialActionReasonsLogMapper specialActionReasonsLogMapper;
    private AuxSupportMapper auxSupportMapper;
    private AuxAccountCustomService auxAccountCustomService;
    
    @Transactional(readOnly=true)
    public ArrayList<AuxAccountData> getAuxAccountsByCustomerAgr(CustomerAgreement customerAgreement) {
        AuxAccountExample auxAccntExample = new AuxAccountExample();
        auxAccntExample.createCriteria().andCustomerAgreementIdEqualTo(customerAgreement.getId());
        auxAccntExample.setOrderByClause("account_priority asc");
        List<AuxAccount> list = auxAccountMapper.selectByExample(auxAccntExample);
        ArrayList<AuxAccountData> returnList = new ArrayList<AuxAccountData>(list.size());
        if (list != null && !list.isEmpty()) {
            Iterator<AuxAccount> listit = list.iterator();
            AuxType auxType;
            AuxChargeSchedule auxChargeSchedule;
            AuxAccountData auxAccountData;
            while (listit.hasNext()) {
                auxAccountData = new AuxAccountData(listit.next());
                if (auxAccountData.getAuxTypeId() != null) {
                    auxType = auxTypeMapper.selectByPrimaryKey(auxAccountData.getAuxTypeId());
                    auxAccountData.setAuxType(auxType);
                }
                if (auxAccountData.getAuxChargeScheduleId() != null) {
                    auxChargeSchedule = auxChargeScheduleMapper.selectByPrimaryKey(auxAccountData.getAuxChargeScheduleId());
                    auxAccountData.setAuxChargeSchedule(auxChargeSchedule);
                }
                auxAccountData.setFreeIssueAccount(auxAccountData.getId().equals(customerAgreement.getFreeIssueAuxAccId()));
                auxAccountData.setDebtStatus(auxAccountCustomService.getDebtStatus(auxAccountData));
                returnList.add(auxAccountData);
            }
        }
        return returnList;
    }

    @Transactional
    public AuxAccountData updateAuxAccount(AuxAccountData auxAccountData) throws ValidationException, ServiceException {
        //accountPriority must be unique
        AuxAccountExample auxAccntExample = new AuxAccountExample();
        auxAccntExample.createCriteria().andCustomerAgreementIdEqualTo(auxAccountData.getCustomerAgreementId());
        List<AuxAccount> list = auxAccountMapper.selectByExample(auxAccntExample);
        if (list != null && !list.isEmpty()) {
            Iterator<AuxAccount> listit = list.iterator();
            while (listit.hasNext()) {
                AuxAccount axa = listit.next();
                if(auxAccountData.getId() != null && auxAccountData.getId().equals(axa.getId())) {
                    //if the account already exists, use the balance value from DB instead
                    auxAccountData.setBalance(axa.getBalance());
                }
                if (auxAccountData.getAccountPriority().equals(axa.getAccountPriority())) {
                    if (auxAccountData.getId() != null && auxAccountData.getId().equals(axa.getId())) {
                        //comparing to itself - ignore
                    } else {
                        throw new ValidationException(new ValidationMessage("customer.auxaccount.error.unique.priority", true));
                    }
                }
            }
        }
        
        AuxAccount auxMrid = getAuxAccountByMrid(auxAccountData.getMrid());
        if ((auxMrid != null && auxAccountData.getId() == null) || 
                (auxMrid != null && auxAccountData.getId() != null && !auxAccountData.getId().equals(auxMrid.getId()))) {
            throw new ValidationException(new ValidationMessage("aux.account.mrid.external.unique.validation", true));
        }
        
        if (auxAccountData.getSpecialActionReasonsLog() != null) {
            if (specialActionReasonsLogMapper.insert(auxAccountData.getSpecialActionReasonsLog()) != 1) {
                throw new ServiceException("customer.auxaccount.error.save");
            }
            if (auxAccountData.getId() == null) {
                auxAccountData.setCreateReasonLogId(auxAccountData.getSpecialActionReasonsLog().getId());
            } else {
                auxAccountData.setUpdateReasonLogId(auxAccountData.getSpecialActionReasonsLog().getId());
            }
        } else {
            auxAccountData.setCreateReasonLogId(null);
            auxAccountData.setUpdateReasonLogId(null);
        }

        AuxType auxType;
        AuxChargeSchedule auxChargeSchedule = auxAccountData.getAuxChargeSchedule();
        
        if (auxChargeSchedule!= null && auxChargeSchedule.isAccountSpecific()) {
            auxChargeSchedule.setStartDate(auxAccountData.getStartDate()); 
            validateSpecificAcs(auxChargeSchedule);
            if (auxAccountData.getAuxChargeScheduleId()== null && auxChargeSchedule.getId() == null) {
                if(auxChargeScheduleMapper.insert(auxChargeSchedule) != 1) {
                    throw new ServiceException("auxchargeschedule.error.save");
                }
                auxAccountData.setAuxChargeScheduleId(auxChargeSchedule.getId());
            } else if (auxAccountData.getAuxChargeScheduleId() != null && auxChargeSchedule.isAccountSpecific()) {
                if (auxChargeScheduleMapper.updateByPrimaryKey(auxChargeSchedule) != 1) {
                    throw new ServiceException("auxchargeschedule.error.save");
                }
            }
        }
        
        if (auxAccountData.getId() == null) {
            auxAccountData.setPrincipleAmount(auxAccountData.getBalance());
            auxAccountData.setAuxChargeSchedule(auxChargeScheduleMapper.selectByPrimaryKey(auxAccountData.getAuxChargeScheduleId()));
            if (auxAccountMapper.insert(auxAccountData) != 1) {
                throw new ServiceException("customer.auxaccount.error.save");
            }
        } else {
            if (auxAccountMapper.updateByPrimaryKey(auxAccountData) != 1) {
                throw new ServiceException("customer.auxaccount.error.save");
            }
            if (!auxAccountData.getRecordStatus().equals(RecordStatus.ACT) && auxAccountData.isFreeIssueAccount()) {
                if (customerAgreementAuxAccountMapper.updateCustomerAgreementSetFreeIssueAuxAccIdToNull(auxAccountData.getId(), auxAccountData.getCustomerAgreementId()) != 1) {
                    throw new ServiceException("customer.freeissue.error.save");
                }
                auxAccountData.setFreeIssueAccount(false);
            }
            if (auxAccountData.getAuxChargeScheduleId() != null) {
                auxChargeSchedule = auxChargeScheduleMapper.selectByPrimaryKey(auxAccountData.getAuxChargeScheduleId());
                auxAccountData.setAuxChargeSchedule(auxChargeSchedule);
            }
        }
        
        if (auxAccountData.getAuxTypeId() != null) {
            auxType = auxTypeMapper.selectByPrimaryKey(auxAccountData.getAuxTypeId());
            auxAccountData.setAuxType(auxType);
        }

        auxAccountData.setDebtStatus(auxAccountCustomService.getDebtStatus(auxAccountData));
        return auxAccountData;
    }
    
    public void validateSpecificAcs(AuxChargeSchedule auxChargeSchedule) {
        ServerValidatorUtil.getInstance().validateDataForValidationMessages(auxChargeSchedule);
        AuxChargeScheduleExample example = new AuxChargeScheduleExample();
        if (auxChargeSchedule.getId() != null) {
            example.createCriteria().andScheduleNameEqualTo(auxChargeSchedule.getScheduleName()).andIdNotEqualTo(auxChargeSchedule.getId());
        } else {
            example.createCriteria().andScheduleNameEqualTo(auxChargeSchedule.getScheduleName());
        }
        List<AuxChargeSchedule> acs = auxChargeScheduleMapper.selectByExample(example);
        if (acs != null && acs.size() >= 1) {
            throw new ValidationException(new ValidationMessage("auxchargeschedule.error.duplicate", new String[]{auxChargeSchedule.getScheduleName()}, true));
        } 
    }

    @Transactional(readOnly = true)
    public AuxAccount getAuxAccountByMrid(String mrid) {
        AuxAccountExample auxAccntExample = new AuxAccountExample();
        auxAccntExample.createCriteria().andMridEqualTo(mrid);
        List<AuxAccount> list = auxAccountMapper.selectByExample(auxAccntExample);
        if (list == null || list.isEmpty()) {
            return null;
        }

        AuxAccount auxAccount = list.get(0);
        return auxAccount;
    }

    @Transactional(readOnly = true)
    public AuxAccount getAuxAccountByCustomerAgrIdAndAccName(Long customerAgrId, String accountName) {
		AuxAccountExample auxAccntExample = new AuxAccountExample();
		auxAccntExample.createCriteria().andCustomerAgreementIdEqualTo(customerAgrId).andAccountNameEqualTo(accountName);
		List<AuxAccount> list = auxAccountMapper.selectByExample(auxAccntExample);
		if (list == null || list.isEmpty()) {
			return null;
		}

		AuxAccount auxAccount = list.get(0);
		return auxAccount;
	}

    @Transactional(readOnly = true)
    public List<CustAuxAccountHistData> getAuxAccountHistory(AuxAccount auxAccount) {
        Long auxAccountId = auxAccount.getId();
        List<CustAuxAccountHistData> result = auxSupportMapper.getCustAuxAccountHistData(auxAccountId);

        return result;
    }

    @Transactional
    public boolean isAuxTypeInUse(Long auxTypeId) {
        AuxAccountExample example = new AuxAccountExample();
        example.createCriteria().andAuxTypeIdEqualTo(auxTypeId);
        return auxAccountMapper.countByExample(example) > 0;
    }

    public void setAuxAccountMapper(AuxAccountMapper auxAccountMapper) {
        this.auxAccountMapper = auxAccountMapper;
    }

    public void setAuxChargeScheduleMapper(AuxChargeScheduleMapper auxChargeScheduleMapper) {
        this.auxChargeScheduleMapper = auxChargeScheduleMapper;
    }

    public void setAuxTypeMapper(AuxTypeMapper auxTypeMapper) {
        this.auxTypeMapper = auxTypeMapper;
    }

    public void setCustomerAgreementAuxAccountMapper(ICustomerAgreementAuxAccntMapper customerAgreementAuxAccountMapper) {
        this.customerAgreementAuxAccountMapper = customerAgreementAuxAccountMapper;
    }

    public void setSpecialActionReasonsLogMapper(SpecialActionReasonsLogMapper specialActionReasonsLogMapper) {
	this.specialActionReasonsLogMapper = specialActionReasonsLogMapper;
    }

    public void setAuxSupportMapper(AuxSupportMapper auxSupportMapper) {
        this.auxSupportMapper = auxSupportMapper;
    }
    
    public void setAuxAccountCustomService (AuxAccountCustomService auxAccountCustomService) {
        this.auxAccountCustomService = auxAccountCustomService;
    }
}
