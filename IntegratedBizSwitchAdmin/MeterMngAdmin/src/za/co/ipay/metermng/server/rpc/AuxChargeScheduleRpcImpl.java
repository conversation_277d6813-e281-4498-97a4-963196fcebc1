package za.co.ipay.metermng.server.rpc;

import java.util.List;

import org.apache.log4j.Logger;

import za.co.ipay.gwt.common.shared.exception.AccessControlException;
import za.co.ipay.gwt.common.shared.exception.ServiceException;
import za.co.ipay.gwt.common.shared.exception.ValidationException;
import za.co.ipay.gwt.common.shared.exception.ValidationMessage;
import za.co.ipay.metermng.client.rpc.AuxChargeScheduleRpc;
import za.co.ipay.metermng.mybatis.generated.model.AuxAccount;
import za.co.ipay.metermng.mybatis.generated.model.AuxChargeSchedule;
import za.co.ipay.metermng.server.mybatis.service.AuxChargeScheduleService;
import za.co.ipay.metermng.server.validation.ServerValidatorUtil;
import za.co.ipay.metermng.shared.dto.user.MeterMngUser;

public class AuxChargeScheduleRpcImpl extends BaseMeterMngRpc implements AuxChargeScheduleRpc {
    
    private static final long serialVersionUID = 1L;
    
    public AuxChargeScheduleRpcImpl() {
        logger = Logger.getLogger(AuxChargeScheduleRpcImpl.class);
    }
    
    private AuxChargeScheduleService auxChargeScheduleService;
    
    public void setAuxChargeScheduleService(AuxChargeScheduleService auxChargeScheduleService) {
        this.auxChargeScheduleService = auxChargeScheduleService;
    }

    @Override
    public List<AuxChargeSchedule> getAllAuxChargeSchedules() throws ServiceException, AccessControlException {
        MeterMngUser user = getUser();
        return auxChargeScheduleService.getAllAuxChargeSchedules(user.getCurrentGroupId());
    }

    @Override
    public List<AuxAccount> getAllAuxAccounts() throws AccessControlException {
        return auxChargeScheduleService.getAllAuxAccounts();
    }

    @Override
    public AuxChargeSchedule updateAuxChargeSchedule(AuxChargeSchedule auxChargeSchedule) throws ValidationException, ServiceException, AccessControlException {
        auxChargeSchedule.setGenGroupId(getUser().getCurrentGroupId());
        ServerValidatorUtil.getInstance().validateDataForValidationMessages(auxChargeSchedule);
        AuxChargeSchedule tempAcs = auxChargeScheduleService.getAuxChargeScheduleByName(auxChargeSchedule.getScheduleName());
        if (tempAcs != null) {
            if (auxChargeSchedule.getId() == null || !auxChargeSchedule.getId().equals(tempAcs.getId())) {
                    throw new ValidationException(new ValidationMessage("auxchargeschedule.error.duplicate", new String[]{auxChargeSchedule.getScheduleName()}, true));
            }
        }
        return auxChargeScheduleService.updateAuxChargeSchedule(auxChargeSchedule);
    }
}
