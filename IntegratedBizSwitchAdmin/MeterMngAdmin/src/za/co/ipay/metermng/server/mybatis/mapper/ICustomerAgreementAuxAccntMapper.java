package za.co.ipay.metermng.server.mybatis.mapper;

import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

public interface ICustomerAgreementAuxAccntMapper {
    
    @Update("update customer_agreement set free_issue_aux_acc_id = null where free_issue_aux_acc_id = #{aux_accnt_id} and customer_agreement_id = #{cust_agr_id}")
    public int updateCustomerAgreementSetFreeIssueAuxAccIdToNull(@Param("aux_accnt_id") Long freeIssueAuxAccId, @Param("cust_agr_id") Long customerAgreementId);
    
}
