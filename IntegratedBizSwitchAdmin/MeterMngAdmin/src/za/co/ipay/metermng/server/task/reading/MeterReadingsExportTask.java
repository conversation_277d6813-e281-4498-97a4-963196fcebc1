package za.co.ipay.metermng.server.task.reading;

import java.io.File;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Locale;

import org.apache.log4j.Logger;
import org.springframework.context.support.AbstractMessageSource;
import org.springframework.context.support.DefaultMessageSourceResolvable;

import za.co.ipay.gwt.common.shared.exception.ServiceException;
import za.co.ipay.gwt.common.shared.validation.ValidateUtil;
import za.co.ipay.metermng.mybatis.custom.model.MeterDto;
import za.co.ipay.metermng.mybatis.generated.model.MeterReadingType;
import za.co.ipay.metermng.mybatis.generated.model.TaskSchedule;
import za.co.ipay.metermng.server.servlet.ExportDataUtil;
import za.co.ipay.metermng.server.task.BaseTask;
import za.co.ipay.metermng.shared.dto.ItemValuePairDto;
import za.co.ipay.metermng.shared.dto.meter.MeterReadingsDto;
import za.co.ipay.metermng.shared.dto.schedule.ScheduledTaskDto;
import za.co.ipay.metermng.shared.dto.user.UserData;
import za.co.ipay.metermng.shared.schedule.MeterReadingsExportTaskData;
import za.co.ipay.metermng.shared.schedule.MeterReadingsExportTaskUtil;
import za.co.ipay.utils.files.CsvWriter;

/**
 * MeterReadingsExportTask is a task used to export meter readings as a CSV file for a specific meter between a
 * calculated start and end date (daily, weekly or monthly). The file is then emailed to the task's users.
 * 
 * <AUTHOR>
 */
public class MeterReadingsExportTask extends BaseTask {
    
  //Note: don't change this class' package and name unless you also update the corresponding TaskClass row in the db
  //and the db static data scripts - thank you
    
    private static Logger logger = Logger.getLogger(MeterReadingsExportTask.class);

    @Override
    public void execute(TaskSchedule taskSchedule, ScheduledTaskDto scheduledTaskDto) throws ServiceException {
        MeterReadingsExportTaskData data = MeterReadingsExportTaskUtil.getTaskClassContents(scheduledTaskDto.getScheduledTask().getTaskContents());        
        MeterDto meterDto = meterService.getMeterDto(data.getMeterNumber());
        if (meterDto != null) {
            MeterReadingType type = meterService.getMeterReadingTypeByValue(data.getMeterReadingType());
            if (type != null) {
                Date[] startAndEnd = getDateRange(data.getTimePeriod());
                if (startAndEnd != null) {
                    Date start = startAndEnd[0];
                    Date end = startAndEnd[1];
                    logger.info("Exporting meterReadings for meter:"+meterDto.getNumber()+" meterReadingType:"+type.getName()+" start:"+start+" end:"+end);
                    
                    //Get the meter's meter readings                    
                    MeterReadingsDto meterReadings = meterService.getMeterReadings(meterDto.getId(), type.getId(), start, end);

                    //Locale
                    Locale locale = getLocale();
                    
                    //Export as CSV file
                    CsvWriter writer = ExportDataUtil.getMeterReadings(messageSource, formatSource, locale, meterReadings);
                    File csvFile = writeCsvFile(meterDto.getNumber(), type, writer, locale); 
                    logger.info("Wrote CSV file: "+csvFile.getAbsolutePath());
                    
                    //Email to task users and/or if there is a customer then notify them too
                    ItemValuePairDto fromNameAndEmail = getFromEmailDets();
                    String fromName = fromNameAndEmail.getItem();
                    String fromEmail = fromNameAndEmail.getValue();
                    String subject = getSubject(taskSchedule.getTaskScheduleName(), scheduledTaskDto.getScheduledTask().getScheduledTaskName(), locale);
                    String message = getMessage(taskSchedule, scheduledTaskDto, meterDto.getNumber(), type, start, end, locale);
                    List<UserData> users = scheduledTaskDto.getUsers();
                    if (scheduledTaskDto.getCustomer() != null 
                            && ValidateUtil.isNotNullOrBlank(scheduledTaskDto.getCustomer().getNotifyEmail())) {
                        users.add(new UserData(scheduledTaskDto.getCustomer().getNotifyEmail()));
                        logger.debug("Added customer for task notify: "+scheduledTaskDto.getCustomer().getNotifyEmail());
                    }
                    try {
                        emailService.sendEmail(fromName, fromEmail, users, subject, message, new File[]{csvFile});
                    } catch (Exception e) {
                        logger.error("Error sending email: ", e);
                        throw new ServiceException("Unable to send scheduledTask email: "+scheduledTaskDto.getScheduledTask().getScheduledTaskName());
                    }
                } else {
                    logger.error("TaskSchedule: "+scheduledTaskDto.getScheduledTask().getScheduledTaskName()+" unknown timePeriod for: "+data.getTimePeriod());    
                }
            } else {
                logger.error("TaskSchedule: "+scheduledTaskDto.getScheduledTask().getScheduledTaskName()+" no meterReadingType found for: "+data.getMeterReadingType());    
            }
        } else {
            logger.error("TaskSchedule: "+scheduledTaskDto.getScheduledTask().getScheduledTaskName()+" no meter found for: "+data.getMeterNumber());
        }
    }
    
    private String getSubject(String taskScheduleName, String scheduledTaskName, Locale locale) {
        StringBuilder sb = new StringBuilder();
        sb.append(messageSource.getMessage(new DefaultMessageSourceResolvable("scheduledtask.email.subject.taskschedule"), locale));
        sb.append(taskScheduleName);
        sb.append(messageSource.getMessage(new DefaultMessageSourceResolvable("scheduledtask.email.subject.scheduledtask"), locale));
        sb.append(scheduledTaskName);
        return sb.toString();
    }
    
    private String getMessage(TaskSchedule taskSchedule, ScheduledTaskDto scheduledTaskDto, 
                               String meterNumber, MeterReadingType readingType, 
                               Date start, Date end, 
                               Locale locale) {
        StringBuilder sb = new StringBuilder();
        
        sb.append(messageSource.getMessage(new DefaultMessageSourceResolvable("scheduledtask.email.message.taskschedule"), locale));
        sb.append(taskSchedule.getTaskScheduleName());
        sb.append(LINE_SEPARATOR);
        
        sb.append(messageSource.getMessage(new DefaultMessageSourceResolvable("scheduledtask.email.message.scheduledtask"), locale));
        sb.append(scheduledTaskDto.getScheduledTask().getScheduledTaskName());
        sb.append(LINE_SEPARATOR);
        
        sb.append(messageSource.getMessage(new DefaultMessageSourceResolvable("scheduledtask.email.message.meter"), locale));
        sb.append(meterNumber);
        sb.append(LINE_SEPARATOR);
        
        SimpleDateFormat sdf = new SimpleDateFormat(formatSource.getMessage(new DefaultMessageSourceResolvable("datetime.pattern"), locale));
        
        sb.append(messageSource.getMessage(new DefaultMessageSourceResolvable("scheduledtask.email.message.start"), locale));
        sb.append(sdf.format(start));
        sb.append(LINE_SEPARATOR);

        sb.append(messageSource.getMessage(new DefaultMessageSourceResolvable("scheduledtask.email.message.end"), locale));
        sb.append(sdf.format(end));
        sb.append(LINE_SEPARATOR);
        
        sb.append(messageSource.getMessage(new DefaultMessageSourceResolvable("scheduledtask.email.message"), locale));
        return sb.toString();
    }

    @Override
    public String getFileName(AbstractMessageSource messageSource, Locale locale, String meterNumber,
            MeterReadingType readingType, String fileExtension) {
        return ExportDataUtil.getMeterReadingsFileName(messageSource, locale, meterNumber, readingType, fileExtension);
    }
}