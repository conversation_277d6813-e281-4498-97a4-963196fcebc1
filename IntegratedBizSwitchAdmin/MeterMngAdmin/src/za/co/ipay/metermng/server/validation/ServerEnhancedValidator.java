package za.co.ipay.metermng.server.validation;

import java.text.MessageFormat;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;
import java.util.MissingResourceException;

import javax.validation.Validator;
import javax.validation.groups.Default;

import org.apache.log4j.Logger;
import org.hibernate.validator.resourceloading.ResourceBundleLocator;
import org.springframework.context.i18n.LocaleContextHolder;

import za.co.ipay.gwt.common.shared.exception.ValidationException;
import za.co.ipay.gwt.common.shared.exception.ValidationMessage;
import za.co.ipay.gwt.common.shared.exception.ValidationMessage.Severity;
import za.co.ipay.gwt.common.shared.validation.EnhancedValidator;
import za.co.ipay.gwt.common.shared.validation.group.ClientGroup;
import za.co.ipay.gwt.common.shared.validation.group.ServerGroup;


/**
 *  ServerEnhancedValidator is used to perform validation on the server-side which can do field level validation as
 *  well as other server specific validation.
 */
public class ServerEnhancedValidator extends EnhancedValidator {

    private ResourceBundleLocator resourceBundleLocator;
    
    private static Logger logger = Logger.getLogger(ServerEnhancedValidator.class);
    
    public ServerEnhancedValidator(ResourceBundleLocator resourceBundleLocator, Validator validator) {        
        super(validator);
        this.resourceBundleLocator = resourceBundleLocator;
    }
    
    /**
     * Method to validate a data instance using the standard server-side groups and return the error messages as 
     * ValidationMessage instances per attribute.
     * @param data The current data instance.
     * @throws A ValidationException if there are any validation errors.
     */
    public <T> void validateDataForValidationMessages(T data) 
        throws ValidationException {
        validateDataForValidationMessages(data, false, Default.class, ClientGroup.class, ServerGroup.class);
    }
    
    /**
     * Method to validate a data instance and return the error messages as ValidationMessage instances per attribute.
     * @param data The current data instance.
     * @param groups The validation groups that should be applied.
     * @throws A ValidationException if there are any validation errors.
     */
    public <T> void validateDataForValidationMessages(T data, boolean allowNull, Class<?>... groups) 
        throws ValidationException {
        Map<String, ValidationMessage> fieldValidationErrors = new HashMap<String, ValidationMessage> ();
        Map<String, String> errors = validateData(data, allowNull, groups);
        Iterator<String> fields = errors.keySet().iterator();
        String field = "";
        String message = "";
        while (fields.hasNext()) {
            field = fields.next();
            message = errors.get(field);
            logger.info("field:"+field+" message:"+message);
            fieldValidationErrors.put(field, new ValidationMessage(Severity.ERROR, message));
        }
        if (fieldValidationErrors.size() > 0) {
            throw new ValidationException(fieldValidationErrors);
        }
    }

    public <T> void validateNotNull(T data, String dataName) throws ValidationException {
        if (data == null) {
            throw new ValidationException(new ValidationMessage(Severity.ERROR, getMessage("error.datatype.null", getMessage(dataName))));
        }
    }
    
    @Override
    protected String getNullDataErrorMessage() {
        return getMessage(NULL_DATA_ERROR); 
    }
    
    public String getMessage(String key) {
        try {
            return resourceBundleLocator.getResourceBundle(LocaleContextHolder.getLocale()).getString(key);
        } catch (MissingResourceException e) {
            logger.error("Unknown message for key: "+key);
            return key;
        }
    }
    
    public String getMessage(String key, Object... params) {
        try {
            return MessageFormat.format(resourceBundleLocator.getResourceBundle(LocaleContextHolder.getLocale()).getString(key), params);
        } catch (MissingResourceException e) {
            logger.error("Unknown message for key: "+key);
            return key;
        }
    }
}