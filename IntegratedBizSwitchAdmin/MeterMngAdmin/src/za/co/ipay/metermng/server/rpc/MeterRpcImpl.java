package za.co.ipay.metermng.server.rpc;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import org.apache.log4j.Logger;
import za.co.ipay.gwt.common.shared.exception.ServiceException;
import za.co.ipay.gwt.common.shared.exception.ValidationException;
import za.co.ipay.gwt.common.shared.exception.ValidationMessage;
import za.co.ipay.ipayxml.mdc.ControlReqMessage;
import za.co.ipay.ipayxml.mdc.ControlReqMessage.ControlType;
import za.co.ipay.ipayxml.mdc.ControlReqMessage.Param;
import za.co.ipay.metermng.cim.MridUtil;
import za.co.ipay.metermng.client.rpc.MeterRpc;
import za.co.ipay.metermng.datatypes.MeterTypeE;
import za.co.ipay.metermng.mybatis.custom.mapper.MdcChannelCustomMapper;
import za.co.ipay.metermng.mybatis.custom.mapper.RegisterReadingCustomMapper;
import za.co.ipay.metermng.mybatis.custom.model.CustomerTransAlphaData;
import za.co.ipay.metermng.mybatis.custom.model.CustomerTransAlphaDataWithTotals;
import za.co.ipay.metermng.mybatis.custom.model.MeterDto;
import za.co.ipay.metermng.mybatis.custom.model.RegisterReadingExt;
import za.co.ipay.metermng.mybatis.generated.model.CustomerTrans;
import za.co.ipay.metermng.mybatis.generated.model.CustomerTransExtra;
import za.co.ipay.metermng.mybatis.generated.model.CustomerTransItem;
import za.co.ipay.metermng.mybatis.generated.model.Meter;
import za.co.ipay.metermng.mybatis.generated.model.MeterHist;
import za.co.ipay.metermng.mybatis.generated.model.MeterReadingType;
import za.co.ipay.metermng.mybatis.generated.model.SpecialActionReasonsLog;
import za.co.ipay.metermng.mybatis.generated.model.StsEngineeringToken;
import za.co.ipay.metermng.mybatis.generated.model.StsMeter;
import za.co.ipay.metermng.mybatis.generated.model.StsMeterHist;
import za.co.ipay.metermng.server.mybatis.service.CustomerService;
import za.co.ipay.metermng.server.mybatis.service.CustomerTransService;
import za.co.ipay.metermng.server.mybatis.service.DeviceStoreService;
import za.co.ipay.metermng.server.mybatis.service.EngineeringTokensService;
import za.co.ipay.metermng.server.mybatis.service.MdcTransService;
import za.co.ipay.metermng.server.mybatis.service.MeterHistService;
import za.co.ipay.metermng.server.mybatis.service.MeterModelService;
import za.co.ipay.metermng.server.mybatis.service.MeterService;
import za.co.ipay.metermng.server.mybatis.service.MeterTypeService;
import za.co.ipay.metermng.server.mybatis.service.RegisterReadingService;
import za.co.ipay.metermng.server.mybatis.service.STSMeterHistService;
import za.co.ipay.metermng.server.mybatis.service.STSMeterService;
import za.co.ipay.metermng.server.util.MeterMngUtil;
import za.co.ipay.metermng.shared.CustomerTransItemData;
import za.co.ipay.metermng.shared.EndDeviceStoreData;
import za.co.ipay.metermng.shared.IpayResponseData;
import za.co.ipay.metermng.shared.MdcTransData;
import za.co.ipay.metermng.shared.MeterHistData;
import za.co.ipay.metermng.shared.STSMeterHistData;
import za.co.ipay.metermng.shared.StsEngineeringTokenData;
import za.co.ipay.metermng.shared.dto.IdNameDto;
import za.co.ipay.metermng.shared.dto.MeterData;
import za.co.ipay.metermng.shared.dto.StsMeterData;
import za.co.ipay.metermng.shared.dto.dashboard.MeterCountDto;
import za.co.ipay.metermng.shared.dto.meter.EnergyBalancingDto;
import za.co.ipay.metermng.shared.dto.meter.MeterReadingDto;
import za.co.ipay.metermng.shared.dto.meter.MeterReadingVariation;
import za.co.ipay.metermng.shared.dto.meter.MeterReadingsDto;
import za.co.ipay.metermng.shared.dto.meter.SuperSubMeterDto;
import za.co.ipay.metermng.shared.dto.meter.VerifyTokenDto;
import za.co.ipay.metermng.shared.dto.schedule.EnergyBalancingExportScreenData;
import za.co.ipay.metermng.shared.dto.usagepoint.MeterUpMdcChannelInfo;
import za.co.ipay.metermng.shared.dto.user.MeterMngUser;

public class MeterRpcImpl extends BaseMeterMngRpc implements MeterRpc {

    private static final long serialVersionUID = -3607053150154039284L;
    private STSMeterService stsMeterService;
    private STSMeterHistService stsMeterHistService;
    private MeterHistService meterHistService;
    private CustomerTransService customerTransService;
    private CustomerService customerService;
    private EngineeringTokensService engineeringTokensService;
    private DeviceStoreService deviceStoreService;
    private MeterService meterService;
    private MeterTypeService meterTypeService;
    private MeterModelService meterModelService;
    private MdcTransService mdcTransService;
    private RegisterReadingService registerReadingService;
    private RegisterReadingCustomMapper registerReadingCustomMapper;
    private MdcChannelCustomMapper mdcChannelCustomMapper;

    public MeterRpcImpl() {
        logger = Logger.getLogger(MeterRpcImpl.class);
    }

    public void setStsMeterService(STSMeterService stsMeterService) {
        this.stsMeterService = stsMeterService;
    }

    public void setMeterTypeService(MeterTypeService meterTypeService) {
        this.meterTypeService = meterTypeService;
    }

    public void setMeterModelService(MeterModelService meterModelService) {
        this.meterModelService = meterModelService;
    }

    public void setStsMeterHistService(STSMeterHistService stsMeterHistService) {
        this.stsMeterHistService = stsMeterHistService;
    }

    public void setMeterHistService(MeterHistService meterHistService) {
        this.meterHistService = meterHistService;
    }

    public void setCustomerTransService(CustomerTransService customerTransService) {
        this.customerTransService = customerTransService;
    }

    public void setCustomerService(CustomerService customerService) {
        this.customerService = customerService;
    }

    public void setEngineeringTokensService(EngineeringTokensService engineeringTokensService) {
        this.engineeringTokensService = engineeringTokensService;
    }

    public void setDeviceStoreService(DeviceStoreService deviceStoreService) {
        this.deviceStoreService = deviceStoreService;
    }

    public void setMeterService(MeterService meterService) {
        this.meterService = meterService;
    }

    public void setMdcTransService(MdcTransService mdcTransService) {
        this.mdcTransService = mdcTransService;
    }

    public void setRegisterReadingService(RegisterReadingService registerReadingService) {
        this.registerReadingService = registerReadingService;
    }

    public void setRegisterReadingCustomMapper(RegisterReadingCustomMapper registerReadingCustomMapper) {
        this.registerReadingCustomMapper = registerReadingCustomMapper;
    }

    public void setMdcChannelCustomMapper(MdcChannelCustomMapper mdcChannelCustomMapper) {
        this.mdcChannelCustomMapper = mdcChannelCustomMapper;
    }

    //-------------------------------------------------------------------------------------------------------------------

    @Override
    public MeterData updateMeter(MeterData meterData) throws ValidationException, ServiceException {
        // Validate the meter wrapper
        if (meterData == null) {
            throw new ValidationException(new ValidationMessage("meter.error.invalid", true));
        } else if (meterService.getMridExistence(meterData.getMrid(), meterData.getId())) {
            throw new ValidationException(new ValidationMessage("meter.mrid.external.unique.validation", true));
        }

        // Save meter
        meterService.saveMeter(meterData);
        meterData.setMeterType(meterTypeService.getMeterType(meterData.getMeterTypeId()));
        meterData.setMeterModelData(meterModelService.getMeterModelById(meterData.getMeterModelId()));

        return meterData;
    }

    @Override
    public Void saveInitRegReadings(MeterUpMdcChannelInfo meterUpMdcChannelInfo) throws ValidationException, ServiceException {
        registerReadingService.generateInitialRegisterReadingsforChannels(
                meterUpMdcChannelInfo.getChannelList(),
                meterUpMdcChannelInfo.getUsagePointId(),
                meterUpMdcChannelInfo.getMeterId(),
                meterUpMdcChannelInfo.getInstallationDate(),
                meterUpMdcChannelInfo.getUpMeterInstallId());
        return null;
    }

    @Override
    public MeterData getMeter(Long meterId) {
        MeterData meterData = new MeterData(meterService.getMeterById(meterId));
        if (meterData.getMeterTypeId().equals(MeterTypeE.STS.getId())) {
            meterData.setStsMeter(stsMeterService.getMeterById(meterId));
        }
        meterData.setMeterModelData(meterModelService.getMeterModelById(meterData.getMeterModelId()));
        return meterData;
    }

    @Override
    public MeterDto getMeterByMeterNumber(String number) throws ServiceException {
        MeterMngUser user = getUser();
        return meterService.getMeterDto(number, user.getCurrentGroupId());
    }

    @Override
    public ArrayList<STSMeterHistData> getStsMeterHistory(Long meterId, boolean usingAccessGroup) {
        ArrayList<STSMeterHistData> thelist = new ArrayList<STSMeterHistData>();
        List<StsMeterHist> listfromdb = stsMeterHistService.getMeterHistoryByMeterId(meterId);
        if (listfromdb != null) {
            STSMeterHistData stsMeterHistData;
            STSMeterHistData prev_record = null;
            Iterator<StsMeterHist> listIt = listfromdb.iterator();

            while (listIt.hasNext()) {
                stsMeterHistData = new STSMeterHistData(listIt.next());
                stsMeterHistData = stsMeterHistService.updateStsInformation(stsMeterHistData);
                EndDeviceStoreData deviceStore;
                if (stsMeterHistData.getEndDeviceStoreId() != null) {
                    deviceStore = deviceStoreService.getHistDeviceStore(stsMeterHistData.getEndDeviceStoreId(), usingAccessGroup);
                    stsMeterHistData.setEndDeviceStoreName(deviceStore.getName());
                }
                if (prev_record != null) {
                    findStsChanges(prev_record, stsMeterHistData);
                }
                prev_record = stsMeterHistData;
                thelist.add(stsMeterHistData);
            }
        }
        return thelist;
    }


    @Override
    public ArrayList<MeterHistData> getMeterHistory(Long meterId, boolean usingAccessGroup) {
        ArrayList<MeterHistData> thelist = new ArrayList<MeterHistData>();
        List<MeterHist> listfromdb = meterHistService.getMeterHistoryByMeterId(meterId);
        if (listfromdb != null) {
            MeterHistData meterHistData;
            MeterHistData prev_record = null;
            Iterator<MeterHist> listIt = listfromdb.iterator();

            while (listIt.hasNext()) {
                meterHistData = new MeterHistData(listIt.next());
                EndDeviceStoreData deviceStore;
                if (meterHistData.getMeterHist().getEndDeviceStoreId() != null) {
                    deviceStore = deviceStoreService.getHistDeviceStore(meterHistData.getMeterHist().getEndDeviceStoreId(), usingAccessGroup);
                    meterHistData.setEndDeviceStoreName(deviceStore.getName());
                }
                if (prev_record != null) {
                    findChanges(prev_record, meterHistData);
                }
                prev_record = meterHistData;
                thelist.add(meterHistData);
            }
        }
        return thelist;
    }

    @Override
    public ArrayList<CustomerTransAlphaData> getTransactionHistory(Long meterId) {
        return new ArrayList<CustomerTransAlphaData>(customerTransService.getCustomerTransByMeterId(meterId));
    }

    @Override
    public ArrayList<CustomerTransAlphaDataWithTotals> getTransactionHistoryWithTotals(Long meterId) {
        return new ArrayList<CustomerTransAlphaDataWithTotals>(customerTransService.getCustomerTransWithTotalsByMeterId(meterId));
    }


    @Override
    public ArrayList<CustomerTransAlphaData> getVendTransactionHistory(Long meterId) {
        return new ArrayList<CustomerTransAlphaData>(customerTransService.getCustomerVendTransByMeterId(meterId));
    }

    @Override
    public List<CustomerTransItemData> getTransactionCustomerTransItem(CustomerTransAlphaDataWithTotals customerTransData) throws ServiceException{
        return customerTransService.getCustomerTransItem(customerTransData);
    }

    @Override
    public List<CustomerTransItemData> getTransactionCustomerTransItem(Long customerTransId) throws ServiceException{
        return customerTransService.getCustomerTransItem(customerTransId);
    }

    @Override
    public ArrayList<StsEngineeringTokenData> getEngineeringTokenTransactions(Long meterId, boolean usingAccessGroup) {
        return new ArrayList<StsEngineeringTokenData>(engineeringTokensService.getEngineeringTokensByMeterId(meterId, usingAccessGroup));
    }

    private void findStsChanges(STSMeterHistData obj1, STSMeterHistData obj2) {
        if (obj1.getId().equals(obj2.getId())) {
            obj1.setRecordStatusChanged(!obj1.getRecordStatus().equals(obj2.getRecordStatus()));
            obj1.setSerialNumChanged(MeterMngUtil.areValuesDifferent(obj1.getSerialNum(), obj2.getSerialNum()));
            obj1.setMridChanged(!obj1.getMrid().equals(obj2.getMrid()));
            obj1.setMridExternalChanged(obj1.isMridExternal() != obj2.isMridExternal());
            obj1.setStsTokenTechCodeChanged(
                    MeterMngUtil.areValuesDifferent(obj1.getStsTokenTechId(), obj2.getStsTokenTechId()));
            obj1.setStsAlgorithmCodeChanged(
                    MeterMngUtil.areValuesDifferent(obj1.getStsAlgorithmCodeId(), obj2.getStsAlgorithmCodeId()));
            obj1.setStsCurrSupplyGroupCodeChanged(MeterMngUtil.areValuesDifferent(obj1.getStsCurrSupplyGroupCode(),
                    obj2.getStsCurrSupplyGroupCode()));
            obj1.setStsCurrKeyRevisionNumChanged(
                    MeterMngUtil.areValuesDifferent(obj1.getStsCurrKeyRevisionNum(), obj2.getStsCurrKeyRevisionNum()));
            obj1.setStsCurrTariffIndexChanged(
                    MeterMngUtil.areValuesDifferent(obj1.getStsCurrTariffIndex(), obj2.getStsCurrTariffIndex()));
            obj1.setEndDeviceStoreChanged(
                    MeterMngUtil.areValuesDifferent(obj1.getEndDeviceStoreId(), obj2.getEndDeviceStoreId()));
        }
    }

    private void findChanges(MeterHistData obj1, MeterHistData obj2) {
        MeterHist meterHist1 = obj1.getMeterHist();
        MeterHist meterHist2 = obj2.getMeterHist();
        if (meterHist1.getId().equals(meterHist2.getId())) {
            obj1.setRecordStatusChanged(!meterHist1.getRecordStatus().equals(meterHist2.getRecordStatus()));
            obj1.setSerialNumChanged(
                    MeterMngUtil.areValuesDifferent(meterHist1.getSerialNum(), meterHist2.getSerialNum()));
            obj1.setMridChanged(!meterHist1.getMrid().equals(meterHist2.getMrid()));
            obj1.setMridExternalChanged(meterHist1.isMridExternal() != meterHist2.isMridExternal());
            obj1.setEndDeviceStoreChanged(MeterMngUtil.areValuesDifferent(meterHist1.getEndDeviceStoreId(),
                    meterHist2.getEndDeviceStoreId()));
        }
    }

    @Override
    public int getTransactionCount(Long meterId) {
        return customerTransService.getCustomerTransCountByMeterId(meterId);
    }

    @Override
    public ArrayList<MeterData> getMetersByEndDeviceStore(Long endDeviceStoreId) throws ServiceException {
        ArrayList<MeterData> thelist = new ArrayList<MeterData>();
        List<Meter> listfromdb = meterService.getMetersByEndDeviceStoreId(endDeviceStoreId);
        if (listfromdb != null) {
            MeterData meterData;
            Iterator<Meter> listIt = listfromdb.iterator();
            while (listIt.hasNext()) {
                meterData = new MeterData(listIt.next());
                if (meterData.getMeterTypeId().equals(MeterTypeE.STS.getId())) {
                    meterData.setStsMeter(stsMeterService.getMeterById(meterData.getId()));
                }
                thelist.add(meterData);
            }
        }
        return thelist;
    }

    @Override
    public ArrayList<MeterReadingType> getMeterReadingTypes() throws ServiceException {
        return meterService.getMeterReadingTypes(Boolean.TRUE);
    }

    @Override
    public ArrayList<MeterDto> getSuperMeters() throws ServiceException {
        return new ArrayList<MeterDto>(meterService.getSuperMeters());
    }

    @Override
    public SuperSubMeterDto getSubMeters(Long superMeterId) throws ServiceException {
        return meterService.getSubMetersForSuperMeter(superMeterId);
    }

    @Override
    public MeterReadingsDto getMeterReadings(Long meterId, Long meterReadingTypeId, Date start, Date end) throws ServiceException {
        return meterService.getMeterReadings(meterId, meterReadingTypeId, start, end);
    }

    @Override
    public MeterReadingsDto getMeterBalancingReadings(Long balancingMeterId, Long meterReadingTypeId, Date start, Date end)
            throws ServiceException {
        return meterService.getMeterBalancingReadings(balancingMeterId, meterReadingTypeId, start, end);
    }

    @Override
    public ArrayList<EnergyBalancingDto> checkEnergyBalancingMeters(Long meterReadingTypeId,
            Date startDate, Date endDate,
            double percentVariation) throws ServiceException {
        return meterService.checkEnergyBalancingMeters(meterReadingTypeId, startDate, endDate, percentVariation);
    }

    @Override
    public IpayResponseData addMeterReadings(MeterDto meterDto, Date start, Date end, int intervalMinutes,
            ArrayList<Long> readingTypeIds, int deleteExistingReadings, boolean doTariffCalc, Date zeroStart,
            Date zeroEnd, int zeroInstances, Date missingStart, Date missingEnd, int missingInstances,
            ArrayList<Long> mdcChannelIds) throws ValidationException, ServiceException {
        Long meterId = getMeterIdFromMeterDto(meterDto);
        if (meterId == null) {
            throw new ServiceException("Meter does not exist");
        }
        Long usagePointId = meterService.addMeterReadings(meterId, start, end, intervalMinutes, readingTypeIds,
                deleteExistingReadings, zeroStart, zeroEnd, zeroInstances, missingStart, missingEnd, missingInstances,
                mdcChannelIds);
        if (doTariffCalc) {
            // note in AddMeterReadingsView, doTariffCalc is only true if there IS a
            // usagePoint!
            return meterService.doTariffCalc(usagePointId);
        } else {
            return null;
        }
    }

    private Long getMeterIdFromMeterDto(MeterDto meterDto) {
        Long meterId = meterDto.getId();
        if (meterId == null) {
            Meter meter = meterService.getMeterByNumber(meterDto.getNumber());
            if (meter == null) {
                return null;
            }
            meterId = meter.getId();
        }
        return meterId;
    }

    @Override
    public Void saveSuperSubMeters(Long superMeterId, ArrayList<Long> subMeterIds) throws ValidationException, ServiceException {
        meterService.saveSuperSubMeters(superMeterId, subMeterIds);
        return null;
    }

    @Override
    public Void deleteSuperMeter(Long superMeterId) throws ValidationException, ServiceException {
        meterService.deleteSuperMeter(superMeterId);
        return null;
    }

    @Override
    public EnergyBalancingExportScreenData getEnergyBalancingExportScreenData() throws ValidationException, ServiceException {
        EnergyBalancingExportScreenData screenData = new EnergyBalancingExportScreenData();
        screenData.setMeterReadingTypes(meterService.getMeterReadingTypes(true));
        screenData.setSuperMeters(new ArrayList<MeterDto>(meterService.getSuperMeters()));
        return screenData;
    }

    @Override
    public void addSuperMeterReadings(Long superMeterId,
                                       Date start, Date end, int intervalMinutes,
                                       Long readingTypeId,
                                       boolean deleteExistingSuperMeterReadings,
                                       boolean regenerateSubMeterReadings,
                                       List<MeterReadingVariation> variations)
            throws ValidationException, ServiceException {
        meterService.addSuperMeterReadings(superMeterId, start, end, intervalMinutes, readingTypeId, deleteExistingSuperMeterReadings, regenerateSubMeterReadings, variations);
    }

    @Override
    public String getNewMrid() {
        return MridUtil.getMrid();
    }

    @Override
    public ArrayList<MeterCountDto> getMeterCountByModel() throws ValidationException, ServiceException {
        return meterService.getMeterCountByModel(getUser().getCurrentGroupId());
    }

    @Override
    public ArrayList<MdcTransData> getMdcTransByMeterId(Long meterId) throws ServiceException {
        return mdcTransService.getMdcTransByMeterId(meterId);
    }

    @SuppressWarnings("deprecation")
    @Override
    public List<RegisterReadingExt> getRegisterReadingsByMeterId(Long meterId, Date fromDate, Date toDate)
            throws ServiceException {
        List<RegisterReadingExt> rrExtList = registerReadingCustomMapper.selectRegisterReadingsForDisplay(meterId,
                fromDate, toDate);
        rrExtList = setDeterminantNameList(rrExtList);
        return rrExtList;
    }

    @Override
    public List<RegisterReadingExt> getRegisterReadingsByUsagePointId(Long usagePointId, Date fromDate, Date toDate)
            throws ServiceException {
        List<RegisterReadingExt> rrExtList = registerReadingCustomMapper.selectUpRegisterReadingsForDisplay(usagePointId, fromDate, toDate);
        rrExtList = setDeterminantNameList(rrExtList);
        return rrExtList;
    }

    private List<RegisterReadingExt> setDeterminantNameList(List<RegisterReadingExt> rrExtList) {
        if (rrExtList != null && !rrExtList.isEmpty()) {
            Set<Long> chnIdList = rrExtList.stream().map(RegisterReadingExt::getMdcChannelId).collect(Collectors.toSet());
            List<IdNameDto> channelIdBdNameList = mdcChannelCustomMapper.selectBillingDetNamesForMdcChannel(new ArrayList<>(chnIdList));
            Map<Long, List<String>> channelBdMap = channelIdBdNameList
                    .stream()
                    .collect(Collectors.groupingBy(IdNameDto::getId,
                    HashMap::new, Collectors.mapping(IdNameDto::getName, Collectors.toList())));

            rrExtList.forEach(x -> {
                x.setDeterminantNameList(channelBdMap.get(x.getMdcChannelId()));
            });
        }
        return rrExtList;
    }

    @Override
    public IpayResponseData sendConnectDisconnectMsg(MeterData meterData, String messageType, String overrideType, String relayId) throws Exception {
        List<Param> paramList = new ArrayList<>();
        ControlType controlType = ControlType.valueOf(messageType);
        // Add relayId if controlType is CONNECT or DISCONNECT but NOT CONNECT_DISCONNECT (isConnectDisconnectType() method)
        if ((controlType == ControlReqMessage.ControlType.CONNECT || controlType == ControlReqMessage.ControlType.DISCONNECT)
                && relayId != null) {
            paramList.add(new ControlReqMessage.Param(za.co.ipay.ipayxml.mdc.Constants.PARAM_RELAY_ID, relayId));
        }
        // BreakerId is being set in CustomerService.sendIpayXmlMessage because meterData is insufficient
        // and need the updated meter information
        Param[] parameters = paramList.toArray(new Param[0]);
        return customerService.sendIpayXmlMessage(meterData, controlType,
                za.co.ipay.ipayxml.mdc.Override.valueOf(overrideType), parameters);
    }

    @Override
    public IpayResponseData sendPowerLimitMsg(MeterData meterData, String overrideType, String powerLimit) throws Exception {
            Param[] parameters = new Param[1];
            parameters[0] = new ControlReqMessage.Param(za.co.ipay.ipayxml.mdc.Constants.PARAM_POWER_LIMIT_KW, powerLimit);
        return customerService.sendIpayXmlMessage(meterData, ControlType.valueOf("POWER_LIMIT"),
                za.co.ipay.ipayxml.mdc.Override.valueOf(overrideType), parameters);
    }

	@Override
	public Long selectLastCustAgrTransId(Long customerAgreementId) throws Exception {
		return meterService.selectLastCustAgrTransId(customerAgreementId);
	}

    @Override
    public IpayResponseData sendVendReversalMsg(CustomerTrans customerTrans, boolean allowOlderReversals,
            String userName, SpecialActionReasonsLog specialActionReasonsLog, String comment) throws Exception {
        return meterService.doVendReversal(customerTrans, allowOlderReversals, userName, specialActionReasonsLog,
                comment);
    }

    @Override
    public Void updateCustTransLastReprintDate(Long customerTransId, Date reprintDate, String userName) throws ServiceException {
        customerTransService.updateCustTransLastReprintDate(customerTransId, reprintDate);
        //add an entry for the reprint to the Customer_trans_extra
        customerTransService.createCustTransExtra(customerTransId, reprintDate, userName);
        return null;
    }

    @Override
	public Void updateCustTransLastReprintDateBulk(List<Long> customerTransIds, Date reprintDate, String userName) {
        customerTransService.updateCustTransLastReprintDateBulk(customerTransIds, reprintDate);
        for (Long id : customerTransIds) {
            customerTransService.createCustTransExtra(id, reprintDate, userName);
        }
        return null;
	}
    
    @Override
    public Boolean getCustTransIsReversed(Long customerTransId) throws Exception {
        return customerTransService.getCustTransIsReversed(customerTransId);
    }

    @Override
    public List<CustomerTransExtra> getCustTransExtraListOrderByReprintDateAsc(Long customerTransId) throws Exception {
        return customerTransService.getCustTransExtraListOrderByReprintDateAsc(customerTransId);
    }

    @Override
    public List<CustomerTransItem> getCustomerTransItemsForMeterId(Long meterId) throws Exception {
        return customerTransService.getCustomerTransItemsForMeterId(meterId);
    }

    @Override
    public MeterReadingDto getReadingsDateRangeForMeter(MeterDto meterDto, ArrayList<Long> readingTypeIds,
            boolean intervalReadings) {
        Long meterId = getMeterIdFromMeterDto(meterDto);
        if (meterId == null) {
            return null;
        }
        return meterService.getReadingsDateRangeForMeter(meterId, readingTypeIds, intervalReadings);
    }

    @Override
    public Void updateMeterPowerLimit(Meter meter, StsMeter stsMeter, BigDecimal powerLimit, String powerLimitLabel) {
        meterService.updateMeterPowerLimit(meter, powerLimit, powerLimitLabel);
        stsMeterService.updateStsMeterPowerLimit(stsMeter, powerLimit, powerLimitLabel);
        return null;
    }

    @Override
    public ArrayList<StsEngineeringTokenData> getStdBsstEngineeringTokens(Long customerTransId, String meterNumber) throws ServiceException {
        return engineeringTokensService.getStdBsstEngineeringTokensByCustomerTrans(customerTransId, meterNumber);
    }

    @Override
    public VerifyTokenDto verifyToken(String token, String meterNumber, StsMeterData stsMeterData) throws ServiceException {
        return meterService.verifyToken(token, meterNumber, stsMeterData);
    }

    @Override
    public StsEngineeringToken getKeyChangeTokensForCustomerTrans(long customerTransId) {
        return engineeringTokensService.getKeyChangeTokensForCustomerTrans(customerTransId);
    }

    @Override
    public StsEngineeringToken getFirstEngineeringTokenByCustomerTrans(long customerTransId) {
        return engineeringTokensService.getFirstEngineeringTokenByCustomerTrans(customerTransId);
    }

}

