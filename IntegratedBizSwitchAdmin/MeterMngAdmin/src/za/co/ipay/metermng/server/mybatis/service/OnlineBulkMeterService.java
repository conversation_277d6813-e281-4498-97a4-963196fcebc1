package za.co.ipay.metermng.server.mybatis.service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Iterator;

import org.apache.log4j.Logger;
import org.springframework.context.support.DefaultMessageSourceResolvable;
import org.springframework.transaction.annotation.Transactional;

import za.co.ipay.exception.RemoteServiceException;
import za.co.ipay.gwt.common.server.util.ExposedReloadableResourceBundleMessageSource;
import za.co.ipay.gwt.common.shared.exception.ServiceException;
import za.co.ipay.gwt.common.shared.exception.ValidationException;
import za.co.ipay.ipayxml.IpayXmlMessage;
import za.co.ipay.ipayxml.metermng.StsEngTokenResMessage;
import za.co.ipay.metermng.cim.MridUtil;
import za.co.ipay.metermng.datatypes.MeterTypeE;
import za.co.ipay.metermng.datatypes.RecordStatus;
import za.co.ipay.metermng.datatypes.StsEngineeringTokenTypeE;
import za.co.ipay.metermng.mybatis.generated.mapper.CustomerAccountMapper;
import za.co.ipay.metermng.mybatis.generated.mapper.CustomerAgreementMapper;
import za.co.ipay.metermng.mybatis.generated.mapper.CustomerMapper;
import za.co.ipay.metermng.mybatis.generated.mapper.LocationMapper;
import za.co.ipay.metermng.mybatis.generated.mapper.StsAlgorithmMapper;
import za.co.ipay.metermng.mybatis.generated.mapper.StsSupplyGroupMapper;
import za.co.ipay.metermng.mybatis.generated.mapper.StsTokenTechMapper;
import za.co.ipay.metermng.mybatis.generated.mapper.UnitsAccountMapper;
import za.co.ipay.metermng.mybatis.generated.mapper.UpGenGroupLnkMapper;
import za.co.ipay.metermng.mybatis.generated.mapper.UpMeterInstallMapper;
import za.co.ipay.metermng.mybatis.generated.mapper.UpPricingStructureMapper;
import za.co.ipay.metermng.mybatis.generated.mapper.UsagePointMapper;
import za.co.ipay.metermng.mybatis.generated.model.Customer;
import za.co.ipay.metermng.mybatis.generated.model.CustomerAccount;
import za.co.ipay.metermng.mybatis.generated.model.CustomerAgreement;
import za.co.ipay.metermng.mybatis.generated.model.Location;
import za.co.ipay.metermng.mybatis.generated.model.Mdc;
import za.co.ipay.metermng.mybatis.generated.model.Meter;
import za.co.ipay.metermng.mybatis.generated.model.MeterModel;
import za.co.ipay.metermng.mybatis.generated.model.MeterType;
import za.co.ipay.metermng.mybatis.generated.model.StsAlgorithm;
import za.co.ipay.metermng.mybatis.generated.model.StsAlgorithmExample;
import za.co.ipay.metermng.mybatis.generated.model.StsMeter;
import za.co.ipay.metermng.mybatis.generated.model.StsSupplyGroup;
import za.co.ipay.metermng.mybatis.generated.model.StsSupplyGroupExample;
import za.co.ipay.metermng.mybatis.generated.model.StsTokenTech;
import za.co.ipay.metermng.mybatis.generated.model.StsTokenTechExample;
import za.co.ipay.metermng.mybatis.generated.model.UnitsAccount;
import za.co.ipay.metermng.mybatis.generated.model.UpGenGroupLnk;
import za.co.ipay.metermng.mybatis.generated.model.UpMeterInstall;
import za.co.ipay.metermng.mybatis.generated.model.UpMeterInstallExample;
import za.co.ipay.metermng.mybatis.generated.model.UpPricingStructure;
import za.co.ipay.metermng.mybatis.generated.model.UsagePoint;
import za.co.ipay.metermng.service.SmsService;
import za.co.ipay.metermng.shared.MeterOnlineBulkData;
import za.co.ipay.metermng.shared.MeterOnlineBulkKernelData;
import za.co.ipay.metermng.shared.TokenData;
import za.co.ipay.metermng.shared.dto.LocationData;
import za.co.ipay.metermng.shared.dto.MdcChannelReadingsDto;
import za.co.ipay.metermng.shared.dto.MeterData;
import za.co.ipay.metermng.shared.dto.MeterOnlineBulkDto;
import za.co.ipay.metermng.shared.dto.UpGenGroupLinkData;
import za.co.ipay.metermng.shared.dto.user.MeterMngUser;

public class OnlineBulkMeterService {
    private STSMeterService stsMeterService;
    private LocationService locationService;
    private UpGenGroupLnkService upGenGroupLnkService;
    private DeviceStoreService deviceStoreService;
    private MeterService meterService;
    private MeterTypeService meterTypeService;
    private MeterModelService meterModelService;
    private GroupSearchService groupSearchService;
    private MeterReadingGeneratorService meterReadingGeneratorService;
    private RegisterReadingService registerReadingService;
    private UsagePointService usagePointService;
    private TokenGenerationService tokenGenerationService;
    private MdcService mdcService;
    
    private StsAlgorithmMapper stsAlgorithmMapper;
    private StsTokenTechMapper stsTokenTechMapper;
    private StsSupplyGroupMapper stsSupplyGroupMapper;
    
    private CustomerMapper customerMapper;
    private CustomerAccountMapper customerAccountMapper;
    private CustomerAgreementMapper customerAgreementMapper;
    private LocationMapper locationMapper;
    private UsagePointMapper usagePointMapper;
    private UnitsAccountMapper unitsAccountMapper;
    private UpPricingStructureMapper upPricingStructureMapper;
    private UpMeterInstallMapper upMeterInstallMapper;
    private UpGenGroupLnkMapper upGenGroupLnkMapper;
    
    private AppSettingService appSettingService;
    
    private SmsService smsService;
    private ExposedReloadableResourceBundleMessageSource messageSource;
    
    private static Logger logger = Logger.getLogger(OnlineBulkMeterService.class);

    public OnlineBulkMeterService() {
    }
    
    public Integer getOnlineBulkUpDataFromUpGroupSelectionCount(int start, int pageSize, ArrayList<UpGenGroupLinkData> selectedGroupsList, String sortColumn, String filterColumn, String filterString, Date filterDate, String order) throws ServiceException {
        ArrayList<Long> selGrpIdList = new ArrayList<Long>();
        for (UpGenGroupLinkData upd : selectedGroupsList) {
            selGrpIdList.add(upd.getGenGroupId());
        }
        if (selGrpIdList.isEmpty()) {
            return null;
        }
        return groupSearchService.getMeterOnlineBulkDataFromGenGroupLeafNodesCount(start, pageSize, selGrpIdList, sortColumn, filterColumn, filterString, filterDate, order);
    }

    public MeterOnlineBulkDto getOnlineBulkUpDataFromUpGroupSelection(int start, int pageSize, ArrayList<UpGenGroupLinkData> selectedGroupsList, String sortColumn, String filterColumn, String filterString, Date filterDate, String order) throws ServiceException {
        MeterOnlineBulkDto dto = new MeterOnlineBulkDto();
        
        //had to use two objects filterString & filterDate because 'java.lang.Object' has no instantiable subtypes
        ArrayList<Long> selGrpIdList = new ArrayList<Long>();
        for (UpGenGroupLinkData upd : selectedGroupsList) {
            selGrpIdList.add(upd.getGenGroupId());
        }
        if (selGrpIdList.isEmpty()) {
            return dto;
        }
        
        Integer totalResultCount = groupSearchService.getMeterOnlineBulkDataFromGenGroupLeafNodesCount(start, pageSize, selGrpIdList, sortColumn, filterColumn, filterString, filterDate, order); 
        dto.setResultCount(totalResultCount);
        
        List<MeterOnlineBulkKernelData> kernelList = groupSearchService.getMeterOnlineBulkDataFromGenGroupLeafNodes(start, pageSize, selGrpIdList, sortColumn, filterColumn, filterString, filterDate, order) ;
        List<MeterOnlineBulkData> dataList = new ArrayList<MeterOnlineBulkData>();
        
        for (MeterOnlineBulkKernelData kernel : kernelList) {
            MeterOnlineBulkData data = new MeterOnlineBulkData();
            data.setKernelData(kernel);
            
            // Get Service location
            if (data.getServiceLocationId() != null) {
                LocationData locData = locationService.getLocationDataById(data.getServiceLocationId());
                data.setLocationGroupDepthList(locData.getGroupDepthList());
                data.setLocationGroupTypeId(locData.getGroupTypeId());
            }
            
            // Get usagepoint groups
            ArrayList<UpGenGroupLinkData> groups = upGenGroupLnkService.getUpGenGroupListByUsagePointId(kernel.getUsagePointId());
            HashMap<Long, UpGenGroupLinkData> groupsMap = new HashMap<Long, UpGenGroupLinkData>();
            if (groups != null) {
                for (int i = 0; i < groups.size(); i++) {
                    groupsMap.put(groups.get(i).getGroupTypeId(), groups.get(i));
                }
            }
            data.setUpgengroups(groupsMap);
            
            //mdc stuff & upmeterInstall
            if (kernel.getMdcId() != null) {
                Mdc mdc = mdcService.getMdc(kernel.getMdcId());
                data.setMdcName(mdc.getName());
                data.setHasChannels(mdcService.isMdcActiveChannels(mdc.getId()));
            }
            
            //UpMeterInstall
            if (data.getUsagePointId() != null && data.getMeterId() != null) {
                UpMeterInstallExample upMeterInstallExample = new UpMeterInstallExample();
                upMeterInstallExample.createCriteria().andUsagePointIdEqualTo(data.getUsagePointId()).andMeterIdEqualTo(data.getMeterId());
                UpMeterInstall upMeterInstall = upMeterInstallMapper.selectByExample(upMeterInstallExample).get(0);
                if (upMeterInstall != null) {
                    data.setUpMeterInstallId(upMeterInstall.getId());
                }
            }
            
            dataList.add(data);
        }
        
        dto.setMoblist(dataList);
        return dto;
    }
    
    public MeterOnlineBulkData getMeterOnlineBulkForMeterNum(String meterNum, Long deviceStoreId) throws ServiceException {
        MeterOnlineBulkData  mobData = new MeterOnlineBulkData();

        //Meter meter = meterService.getMeterByNumberInDeviceStores(meterNum);
        Meter meter = meterService.getMeterByNumber(meterNum);
        if (meter == null) {
            return null;
        }
        mobData.setMeterId(meter.getId());
        mobData.setMeterNum(meterNum);
        mobData.setDeviceStoreId(meter.getEndDeviceStoreId());
        mobData.setBreakerId(meter.getBreakerId());
        mobData.setReplaceReasonLogId(meter.getReplaceReasonLogId());
        mobData.setEncKey(meter.getEncKey());
        mobData.setMeterUriAddress(meter.getMeterUriAddress());
        mobData.setMeterUriPort(meter.getMeterUriPort());
        mobData.setMeterUriProtocol(meter.getMeterUriProtocol());
        mobData.setMeterUriParams(meter.getMeterUriParams());
        
        MeterType mt = meterTypeService.getMeterType(meter.getMeterTypeId());
        mobData.setMeterTypeId(mt.getId());
        mobData.setMeterTypeName(mt.getName());
        
        if (mt.getId().equals(MeterTypeE.STS.getId())) {
            StsMeter stsMeter = stsMeterService.getMeterByMeterNumber(meterNum);
            if (stsMeter == null) {
                return null;
            }
            mobData.setStsCurrSupplyGroupCodeId(stsMeter.getStsCurrSupplyGroupId());            
            mobData.setStsCurrSupplyGroupCode(stsMeter.getStsCurrSupplyGroupCode());
            mobData.setStsAlgorithmCodeId(stsMeter.getStsAlgorithmCodeId());
            mobData.setStsTokenTechCodeId(stsMeter.getStsTokenTechId());
            mobData.setStsCurrTariffIndex(stsMeter.getStsCurrTariffIndex());
            mobData.setStsCurrKeyRevisionNum(stsMeter.getStsCurrKeyRevisionNum());            
        }
        
        MeterModel model = meterModelService.getMeterModelById(meter.getMeterModelId());
        mobData.setMeterModelId(meter.getMeterModelId());        
        mobData.setMeterModelName(model.getName());
        
        if (meter.getEndDeviceStoreId() != null && meter.getEndDeviceStoreId().compareTo(deviceStoreId) != 0 ) {
            mobData.setDeviceStoreName(deviceStoreService.getDeviceStore(meter.getEndDeviceStoreId()).getName());
        }
        
        if (model.getMdcId() != null) {
            Mdc mdc = mdcService.getMdc(model.getMdcId());
            mobData.setMdcId(mdc.getId());
            mobData.setMdcName(mdc.getName());
            mobData.setHasChannels(mdcService.isMdcActiveChannels(mdc.getId()));
        }
        
        return mobData;
    }
    
    @Transactional
    public MeterOnlineBulkData saveMeterOnlineBulk(MeterOnlineBulkData mobData, MeterMngUser user) throws ServiceException {
        Meter meter = meterService.getMeterByNumber(mobData.getMeterNum());
        if (meter == null) {
            meter = new Meter();
        }
        MeterData meterData = new MeterData(meter);
        if (mobData.getStsCurrSupplyGroupCodeId() != null) {
            meterData.setMeterTypeId(MeterTypeE.STS.getId());
        } else {
            meterData.setMeterTypeId(MeterTypeE.GENERIC.getId());
        }
        
        if (meterData.getMeterTypeId().equals(MeterTypeE.STS.getId())) {
            logger.info("saveMeterOnlineBulk: stsMeter");
            StsMeter stsMeter = populateStsMeter(mobData);
            //??error.supplyserver=The Supply Group's code and revision number combination must be unique.
            meterData.setStsMeter(stsMeter);
        }

        meterData.setMeterNum(mobData.getMeterNum());
        meterData.setMeterModelId(mobData.getMeterModelId());
        meterData.setMridExternal(Boolean.FALSE);
        meterData.setEndDeviceStoreId(null);
        meterData.setRecordStatus(RecordStatus.ACT);
        meterData.setBreakerId(mobData.getBreakerId());
        meterData.setEncKey(mobData.getEncKey());
        meterData.setMeterUriAddress(mobData.getMeterUriAddress());
        meterData.setMeterUriPort(mobData.getMeterUriPort());
        meterData.setMeterUriProtocol(mobData.getMeterUriProtocol());
        meterData.setMeterUriParams(mobData.getMeterUriParams());

        try {
            meterService.saveMeter(meterData);
        } catch (ValidationException e) {
            logger.error("OnlineBulkMeterService: saveMeter failed for meterNum="+mobData.getMeterNum());
            throw new ServiceException("online.bulk.meter.save.error");
        }

        Customer customer = new Customer();
        customer.setCustomerKindId(1L);
        customer.setLocaleValue("en_ZA");
        customer.setMrid(MridUtil.getMrid());
        customer.setCustomerReference("CUST"+IpayXmlMessage.genRef());
        customer.setSurname(mobData.getSurname());
        customer.setPhone1(mobData.getPhone1());
        Long genGroupId = user.getCurrentGroupId();
        customer.setGenGroupId(genGroupId);
        if (customerMapper.insert(customer) != 1) {
            throw new ServiceException("customer.error.save");
        }

        CustomerAccount customerAccount = new CustomerAccount();
        customerAccount.setCustomerId(customer.getId());
        customerAccount.setRecordStatus(RecordStatus.ACT);
        customerAccount.setMrid(MridUtil.getMrid());
        customerAccount.setAccountName("ACC"+IpayXmlMessage.genRef());
        if (customerAccountMapper.insert(customerAccount) != 1) {
            throw new ServiceException("customeraccount.error.save");
        }

        CustomerAgreement customerAgreement = new CustomerAgreement();
        customerAgreement.setCustomerId(customer.getId());
        customerAgreement.setCustomerAccountId(customerAccount.getId());
        customerAgreement.setAgreementRef("AGR"+IpayXmlMessage.genRef());
        customerAgreement.setMrid(MridUtil.getMrid());
        if (customerAgreementMapper.insert(customerAgreement) != 1) {
            throw new ServiceException("customeraggreement.error.save");
        }

        Location location = new Location();
        location.setLocGroupId(mobData.getLocationGroupTypeId());
        location.setSuiteNum(mobData.getSuiteNum());
        location.setMrid(MridUtil.getMrid());
        if (locationMapper.insert(location) != 1) {
            throw new ServiceException("location.error.save");
        }

        UnitsAccount unitsAccount = new UnitsAccount();
        unitsAccount.setMrid(MridUtil.getMrid());
        unitsAccount.setAccountName("UBA"+IpayXmlMessage.genRef());
        if (unitsAccountMapper.insert(unitsAccount) != 1) {
            throw new ServiceException("units.account.error.save");
        }

        UsagePoint usagePoint = new UsagePoint();
        usagePoint.setName("UP"+IpayXmlMessage.genRef());
        usagePoint.setMeterId(meterData.getId());
        usagePoint.setCustomerAgreementId(customerAgreement.getId());
        Date installationDate = mobData.getInstallationDate();
        usagePoint.setInstallationDate(installationDate);
        usagePoint.setRecordStatus(RecordStatus.ACT);
        usagePoint.setActivationDate(installationDate);
        usagePoint.setGenGroupId(genGroupId);
        usagePoint.setMrid(MridUtil.getMrid());
        usagePoint.setServiceLocationId(location.getId());
        usagePoint.setUnitsAccountId(unitsAccount.getId());

        //check again here if meter already attached to UP, then disallow it
        if (meterData.getId() != null) {
            UsagePoint existingUp = usagePointService.getUsagePointByMeterId(meterData.getId());
            if (existingUp != null) {
                logger.debug("saveMeterOnlineBulk: existing UsagePoint found on serverside. Reject add Meter to Group! meterNum= " + meterData.getMeterNum()
                                                    + "  meterId = " + meterData.getId());
                throw new ServiceException("meter.attached.to.up", new String[] {meterData.getMeterNum(), existingUp.getName()});
            }
        }
        if (usagePointMapper.insert(usagePoint) != 1) {
            throw new ServiceException("usagepoint.save.error");
        }
        UpPricingStructure upPricingStructure = new UpPricingStructure();
        upPricingStructure.setUsagePointId(usagePoint.getId());
        upPricingStructure.setPricingStructureId(mobData.getCurrentPricingStructureId());
        upPricingStructure.setStartDate(new Date());
        if (upPricingStructureMapper.insert(upPricingStructure) != 1) {
            throw new ServiceException("usagepoint.save.error");
        }

        //upMeterInstall
        UpMeterInstall upMeterInstall = new UpMeterInstall();
        upMeterInstall.setUsagePointId(usagePoint.getId());
        upMeterInstall.setMeterId(meterData.getId());
        upMeterInstall.setInstallDate(installationDate);
        if (upMeterInstallMapper.insert(upMeterInstall) == 1) {
            meterReadingGeneratorService.updateMeterReadingsAfterDate(meterData.getId(), usagePoint.getId(),
                    installationDate);
            registerReadingService.updateRegisterReadingsAfterDate(meterData.getId(), usagePoint.getId(),
                    installationDate);
            List<MdcChannelReadingsDto> channelReadingsList = mobData.getMdcChannelReadingsDtoList();
            if (channelReadingsList != null && !channelReadingsList.isEmpty()) {
                registerReadingService.generateInitialRegisterReadingsforChannels(channelReadingsList, usagePoint.getId(), meterData.getId(), upMeterInstall.getInstallDate(), upMeterInstall.getId());
            }
        } else {
            logger.error("OnlineBulkMeterService: upMeterInstallMapper.insert failed");
            throw new ServiceException("usagepoint.save.error");
        }

        // Get usagepoint groups
        ArrayList<UpGenGroupLinkData> groups = new ArrayList<UpGenGroupLinkData>(mobData.getUpgengroups().values());
        if (groups != null) {
            for (int i = 0; i < groups.size(); i++) {
                UpGenGroupLnk lnk = new UpGenGroupLnk();
                lnk.setGenGroupId(groups.get(i).getGenGroupId());
                lnk.setUsagePointId(usagePoint.getId());
                if (upGenGroupLnkMapper.insert(lnk) != 1) {
                    logger.error("OnlineBulkMeterService: upGenGrpLnkMapper.insert failed");
                    throw new ServiceException("usagepoint.save.error");
                }
            }
        }
        mobData.setUsagePointId(usagePoint.getId());
        mobData.setUsagePointName(usagePoint.getName());
        mobData.setCustomerAgreementId(customerAgreement.getId());

        if (mobData.getFreeIssueUnits() != null &&
                mobData.getFreeIssueUnits().compareTo(BigDecimal.ZERO) > 0) {
            mobData.setStsMeter(meterData.getStsMeter());
            mobData.setTokenData(getFreeIssueToken(mobData));
        }

        return mobData;
    }

    private StsMeter populateStsMeter(MeterOnlineBulkData mobData) {
        StsMeter stsMeter = stsMeterService.getMeterByMeterNumber(mobData.getMeterNum());
        if (stsMeter == null) {
            stsMeter = new StsMeter();
        }
        StsSupplyGroup stsSupplyGroup = getSupplyGroup(mobData.getStsCurrSupplyGroupCodeId());
        stsMeter.setStsCurrSupplyGroupId(stsSupplyGroup.getId());
        stsMeter.setStsCurrSupplyGroupCode(stsSupplyGroup.getSupplyGroupCode());
        stsMeter.setStsCurrKeyRevisionNum(stsSupplyGroup.getKeyRevisionNum());
        stsMeter.setStsTokenTechId(mobData.getStsTokenTechCodeId());
        stsMeter.setStsTokenTechCode(getTokenTechCode(mobData.getStsTokenTechCodeId()));

        stsMeter.setStsAlgorithmCodeId(mobData.getStsAlgorithmCodeId());
        stsMeter.setStsAlgorithmCode(getAlgorithmCode(mobData.getStsAlgorithmCodeId()));

        stsMeter.setStsCurrTariffIndex(mobData.getStsCurrTariffIndex());
        
        return stsMeter;
    }

    private StsSupplyGroup getSupplyGroup(Long currSupplyGroupCodeId) {
        StsSupplyGroupExample stssgExample = new StsSupplyGroupExample();
        stssgExample.createCriteria().andIdEqualTo(currSupplyGroupCodeId);
        ArrayList<StsSupplyGroup> list = (ArrayList<StsSupplyGroup>) stsSupplyGroupMapper.selectByExample(stssgExample);
        if (list == null || list.size() < 1) {
            logger.error("OnlineBulkMeterService: getSupplyGroupCode() failed for id="+currSupplyGroupCodeId); 
            throw new ServiceException("online.bulk.sts.meter.save.error");
        } else {
            return list.get(0);
        }
    }
    
    private String getTokenTechCode(Long tokenTechCodeId) {
        StsTokenTechExample ststtExample = new StsTokenTechExample();
        ststtExample.createCriteria().andIdEqualTo(tokenTechCodeId);
        ArrayList<StsTokenTech> list = (ArrayList<StsTokenTech>) stsTokenTechMapper.selectByExample(ststtExample);
        if (list == null || list.size() < 1) {
            logger.error("OnlineBulkMeterService: getTokenTechCode() failed for id="+tokenTechCodeId); 
            throw new ServiceException("online.bulk.sts.meter.save.error");
        } else {
            return list.get(0).getStsTokenTechCode();
        }
    }
    
    private String getAlgorithmCode(Long algorithmCodeId) {
        StsAlgorithmExample stsaExample = new StsAlgorithmExample();
        stsaExample.createCriteria().andIdEqualTo(algorithmCodeId);
        ArrayList<StsAlgorithm> list = (ArrayList<StsAlgorithm>) stsAlgorithmMapper.selectByExample(stsaExample);
        if (list == null || list.size() < 1) {
            logger.error("OnlineBulkMeterService: getAlgorithmCode() failed for id="+algorithmCodeId); 
            throw new ServiceException("online.bulk.sts.meter.save.error");
        } else {
            return list.get(0).getStsAlgorithmCode();
        }
    }
    
    private TokenData getFreeIssueToken(MeterOnlineBulkData mobData) {
        logger.info("saveMeterOnlineBulk(): getFreeIssueToken");
        TokenData tokenData = null;
        StsEngTokenResMessage res = null;
        try {
            res = tokenGenerationService.sendStsEngTokenReqMessage(null, mobData.getStsMeter().getMeterNum(),
                    mobData.getFreeIssueTokenUserReference(), StsEngineeringTokenTypeE.FREE_ISSUE,
                    mobData.getFreeIssueUnits().intValue() * 10, mobData.getFreeIssueTokenReasonsLog());
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (res != null) {
            tokenData = new TokenData();
            tokenData.setResCode(res.getResCode());
            if (res.isSuccessful()) {
                tokenData.setEngineeringTokenCodes(new ArrayList<String>(Arrays.asList(res.getTokenCodes())));
            } else {
                tokenData.setErrorMsg(res.getRes());
            }
        }
        //SMS token
        logger.info("saveMeterOnlineBulk(): getFreeIssueToken: isSendSms = " + mobData.isSendSms() + "    to " + mobData.getPhone1());
        if (mobData.isSendSms()) {
            String thecode = tokenData.getEngineeringTokenCodes().get(0);
            //prep tokenCode for output
            int len = thecode.length();
            int numberOfSpaces = (len/4)+1;
            char[] val = thecode.toCharArray();
            char[] buf = new char[len+numberOfSpaces];
            int j = 0;
            for(int c=0; c<len; c++) {
                if ((c%4) == 0) {
                    buf[j++] = ' ';
                }
                buf[j++] = val[c];    
            }
            String tokenCode = new String(buf, 0, j).trim();

            String companyName = appSettingService.getAppSettingByKey("company.name").getValue();
            if (companyName == null) {
                companyName = "";
            }
            
            String tokenMessage = messageSource.getMessage(new DefaultMessageSourceResolvable("meter.online.bulk.free.issue.sms"), null);
            tokenMessage = tokenMessage.replace("{0}", mobData.getMeterNum()).replace("{1}", tokenCode).replace("{2}", companyName);
            try {
                smsService.sendSms(tokenMessage, mobData.getPhone1());
                logger.info("saveMeterOnlineBulk(): getFreeIssueToken: SMS SENT to " + mobData.getPhone1());
            } catch (RemoteServiceException e) {
                tokenData.setErrorMsg("Token has been saved but sms failed because " + e.getMessage());
                logger.info("saveMeterOnlineBulk(): getFreeIssueToken: smsService.sendSms() throws RemoteServiceException=" + e.getMessage());
            }
        }
        
        return tokenData;
    }
    
    @Transactional
    public MeterOnlineBulkData updateMeterOnlineBulk(MeterOnlineBulkData mobData, MeterMngUser user) throws ServiceException {
        Meter meter = meterService.getMeterByNumber(mobData.getMeterNum());
        MeterData meterData = new MeterData(meter);
        
        if (meterData.getMeterTypeId().equals(MeterTypeE.STS.getId())) {
            logger.info("updateMeterOnlineBulk: stsMeter");
            StsMeter stsMeter = populateStsMeter(mobData);
            //??error.supplyserver=The Supply Group's code and revision number combination must be unique.
            meterData.setStsMeter(stsMeter);
        }    
        
        meterData.setMeterModelId(mobData.getMeterModelId());
        meterData.setBreakerId(mobData.getBreakerId());
        meterData.setEncKey(mobData.getEncKey());
        meterData.setMeterUriAddress(mobData.getMeterUriAddress());
        meterData.setMeterUriPort(mobData.getMeterUriPort());
        meterData.setMeterUriProtocol(mobData.getMeterUriProtocol());
        meterData.setMeterUriParams(mobData.getMeterUriParams());
        
        try {
            meterService.saveMeter(meterData);
        } catch (ValidationException e) {
            logger.error("OnlineBulkMeterService: update Meter failed for meterNum="+mobData.getMeterNum()); 
            throw new ServiceException("online.bulk.meter.save.error");
        }

        Customer customer = customerMapper.selectByPrimaryKey(customerAgreementMapper.selectByPrimaryKey(mobData.getCustomerAgreementId()).getCustomerId());
        customer.setSurname(mobData.getSurname());
        customer.setPhone1(mobData.getPhone1());
        if (customerMapper.updateByPrimaryKey(customer) != 1) {
            throw new ServiceException("customer.error.save");
        }

        UsagePoint usagePoint = usagePointMapper.selectByPrimaryKey(mobData.getUsagePointId());
        
        Location location = locationMapper.selectByPrimaryKey(usagePoint.getServiceLocationId());
        location.setLocGroupId(mobData.getLocationGroupTypeId());
        location.setSuiteNum(mobData.getSuiteNum());
        if (locationMapper.updateByPrimaryKey(location) != 1) {
            throw new ServiceException("location.error.save");
        }
        
        List<MdcChannelReadingsDto> channelReadingsList = mobData.getMdcChannelReadingsDtoList();
        if (channelReadingsList != null && !channelReadingsList.isEmpty()) {
            UpMeterInstallExample upMeterInstallExample = new UpMeterInstallExample();
            upMeterInstallExample.createCriteria().andUsagePointIdEqualTo(usagePoint.getId()).andMeterIdEqualTo(meter.getId());
            UpMeterInstall upMeterInstall = upMeterInstallMapper.selectByExample(upMeterInstallExample).get(0);
            if (upMeterInstall != null) {
                registerReadingService.generateInitialRegisterReadingsforChannels(channelReadingsList, usagePoint.getId(), meterData.getId(), upMeterInstall.getInstallDate(), upMeterInstall.getId());
            }
        }

        if (usagePointMapper.updateByPrimaryKey(usagePoint) != 1) {
            throw new ServiceException("usagepoint.save.error");
        }

        mobData.setLocationGroupTypeId(usagePoint.getServiceLocationId());
        
        //update gengroups
        ArrayList<UpGenGroupLinkData> groups = mobData.getSelectedGroups();

        if (groups != null && !groups.isEmpty()) {
            UpGenGroupLinkData upGenGroupLinkData;
            UpGenGroupLnk existingLink;
            Iterator<UpGenGroupLinkData> iterator = groups.iterator();

            while (iterator.hasNext()) {
                upGenGroupLinkData = iterator.next();

                if (upGenGroupLinkData.getId() != null) {
                    if (upGenGroupLinkData.getGenGroupId() == null) {
                        // Handle deletion
                        if (upGenGroupLnkMapper.deleteByPrimaryKey(upGenGroupLinkData.getId()) != 1) {
                            throw new ServiceException("usagepoint.group.error.delete");
                        }
                        iterator.remove();
                    } else {
                        // Handle update
                        existingLink = upGenGroupLnkMapper.selectByPrimaryKey(upGenGroupLinkData.getId());
                        if (existingLink != null &&
                                (!existingLink.getUsagePointId().equals(upGenGroupLinkData.getUsagePointId()) ||
                                        !existingLink.getGenGroupId().equals(upGenGroupLinkData.getGenGroupId()))) {
                            if (upGenGroupLnkMapper.updateByPrimaryKey(upGenGroupLinkData) != 1) {
                                throw new ServiceException("usagepoint.group.error.save");
                            }
                        }
                    }
                } else {
                    // Handle insertion
                    upGenGroupLinkData.setUsagePointId(mobData.getUsagePointId());
                    if (upGenGroupLnkMapper.insert(upGenGroupLinkData) != 1) {
                        throw new ServiceException("usagepoint.group.error.save");
                    }
                }
            }
        }
        return mobData;
    }
    
    //************************************************************************************************************************
    public void setStsMeterService(STSMeterService stsMeterServe) {
        this.stsMeterService = stsMeterServe;
    }

    public void setLocationService(LocationService locationService) {
        this.locationService = locationService;
    }

    public void setUpGenGroupLnkService(UpGenGroupLnkService usagePointGroupLnkService) {
        this.upGenGroupLnkService = usagePointGroupLnkService;
    }

    public void setDeviceStoreService(DeviceStoreService deviceStoreService) {
        this.deviceStoreService = deviceStoreService;
    }
    
    public void setMeterTypeService(MeterTypeService meterTypeService) {
        this.meterTypeService = meterTypeService;
    }
    
    public void setMeterService(MeterService meterService) {
        this.meterService = meterService;
    }
    
    public void setMeterModelService(MeterModelService meterModelService) {
        this.meterModelService = meterModelService;
    }

    public void setGroupSearchService(GroupSearchService groupSearchService) {
        this.groupSearchService = groupSearchService;
    }

    public void setMeterReadingGeneratorService(MeterReadingGeneratorService meterReadingGeneratorService) {
        this.meterReadingGeneratorService = meterReadingGeneratorService;
    }

    public void setRegisterReadingService(RegisterReadingService registerReadingService) {
        this.registerReadingService = registerReadingService;
    }
    
    public void setUsagePointService(UsagePointService usagePointService) {
        this.usagePointService = usagePointService;
    }
    
    public void setStsAlgorithmMapper(StsAlgorithmMapper stsAlgorithmMapper) {
        this.stsAlgorithmMapper = stsAlgorithmMapper;
    }

    public void setStsTokenTechMapper(StsTokenTechMapper stsTokenTechMapper) {
        this.stsTokenTechMapper = stsTokenTechMapper;
    }

    public void setStsSupplyGroupMapper(StsSupplyGroupMapper stsSupplyGroupMapper) {
        this.stsSupplyGroupMapper = stsSupplyGroupMapper;
    }
    
    public void setCustomerMapper(CustomerMapper customerMapper) {
        this.customerMapper = customerMapper;
    }

    public void setCustomerAgreementMapper(CustomerAgreementMapper customerAgreementMapper) {
        this.customerAgreementMapper = customerAgreementMapper;
    }

    public void setCustomerAccountMapper(CustomerAccountMapper customerAccountMapper) {
        this.customerAccountMapper = customerAccountMapper;
    }
    
    public void setLocationMapper(LocationMapper locationMapper) {
        this.locationMapper = locationMapper;
    }
    
    public void setUsagePointMapper(UsagePointMapper usagePointMapper) {
        this.usagePointMapper = usagePointMapper;
    }
    
    public void setUnitsAccountMapper(UnitsAccountMapper unitsAccountMapper) {
        this.unitsAccountMapper = unitsAccountMapper;
    }
    
    public void setUpMeterInstallMapper(UpMeterInstallMapper upMeterInstallMapper){
        this.upMeterInstallMapper = upMeterInstallMapper;
    }

    public void setUpGenGroupLnkMapper(UpGenGroupLnkMapper upGenGroupLnkMapper) {
        this.upGenGroupLnkMapper = upGenGroupLnkMapper;
    }
    
    public void setSmsService(SmsService smsService) throws ServiceException {
        this.smsService = smsService;
    }

    public void setMessageSource(ExposedReloadableResourceBundleMessageSource messageSource) {
        this.messageSource = messageSource;
    }
    
    public void setAppSettingService(AppSettingService appSettingService) {
        this.appSettingService = appSettingService;
    }

    public void setTokenGenerationService(TokenGenerationService tokenGenerationService) {
        this.tokenGenerationService = tokenGenerationService;
    }
    public void setMdcService(MdcService mdcService) {
        this.mdcService = mdcService;
    }

    public void setUpPricingStructureMapper(UpPricingStructureMapper upPricingStructureMapper) {
        this.upPricingStructureMapper = upPricingStructureMapper;
    }

}
