package za.co.ipay.metermng.server.mybatis.service;

import java.sql.Date;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Collections;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;

import org.apache.log4j.Logger;
import org.springframework.transaction.annotation.Transactional;

import za.co.ipay.gwt.common.shared.exception.ServiceException;
import za.co.ipay.metermng.mybatis.generated.mapper.GroupHierarchyMapper;
import za.co.ipay.metermng.mybatis.generated.model.GroupHierarchy;
import za.co.ipay.metermng.mybatis.generated.model.GroupHierarchyExample;
import za.co.ipay.metermng.mybatis.generated.model.GroupType;
import za.co.ipay.metermng.server.mybatis.mapper.IUsagePointGroupsMapper;
import za.co.ipay.metermng.shared.MeterMngStatics;
import za.co.ipay.metermng.shared.dto.ItemValuePairDto;
import za.co.ipay.metermng.shared.dto.dashboard.TsDataCountTableDto;
import za.co.ipay.metermng.shared.dto.dashboard.UsagePointGroupsAddedContainerDto;

public class UsagePointGroupsService {

    // How far back in months to fetch data
    private static final long GRAPH_HISTORY = 2L;
    private LocalDate endDate;
    private LocalDate startDate;

    private static Logger logger = Logger.getLogger(UsagePointGroupsService.class);

    private Long groupTypeID;
    private IUsagePointGroupsMapper usagePointGroupsMapper;
    private GroupHierarchyMapper groupHierarchyMapper;

    private Long rootGroupHierarchyId;

    private GroupService groupService;
    private AppSettingService appSettingService;

    public UsagePointGroupsService() {}

    public void setiUsagePointGroupsMapper(IUsagePointGroupsMapper usagePointGroupsMapper) {
        this.usagePointGroupsMapper = usagePointGroupsMapper;
    }

    public void setGroupHierarchyMapper(GroupHierarchyMapper groupHierarchyMapper) {
        this.groupHierarchyMapper = groupHierarchyMapper;
    }

    public void setGroupService(GroupService groupService) {
        this.groupService = groupService;
    }

    public void setAppSettingService(AppSettingService appSettingService) {
        this.appSettingService = appSettingService;
    }

    @Transactional(readOnly = true)
    public ArrayList<ItemValuePairDto> getUsagePointGroupsList() {
        ArrayList<ItemValuePairDto> usagePointGroupsList = usagePointGroupsMapper.getUsagePointGroupsList();
        return usagePointGroupsList;
    }

    @Transactional(readOnly = true)
    public ArrayList<UsagePointGroupsAddedContainerDto> getUsagePointGroupsAdded() {

        endDate = LocalDate.now();
        startDate = endDate.minusMonths(GRAPH_HISTORY).withDayOfMonth(1);

        setGroupTypeID();

        // OwnersGroupHierarchyId is used to find the buildings group id from the groups_hierarchy table by checking the parent_id column.
        GroupHierarchy rootGroupHierarchy;
        
        // Check if group type value in appsetting returns a valid ID. If not log and return null data so panel won't display.
        if (groupTypeID != null) {
            rootGroupHierarchy = getRootGroupHierarchy(groupTypeID);
        } else {
            rootGroupHierarchy = null;
        }
        
        ArrayList<UsagePointGroupsAddedContainerDto> usagePointGroupsAddedContainer = new ArrayList<>();

        if (rootGroupHierarchy != null) {

            rootGroupHierarchyId = rootGroupHierarchy.getId();


            // Fetch root group data. This is where parent_id in group_hierarchy table is null for given GROUP_TYPE_ID
            UsagePointGroupsAddedContainerDto rootGroupDataSet = new UsagePointGroupsAddedContainerDto();

            // Fetch group name from DB.
            rootGroupDataSet.setUpGroupName(rootGroupHierarchy.getName());

            // Set daily data
            rootGroupDataSet.setDailyUsagePointGroupsAddedDtos(this.getRootDataSet());

            // Set monthly data
            rootGroupDataSet.setMonthlyUsagePointGroupsAddedDtos(this.getMonthlyDataSet(rootGroupDataSet.getDailyUsagePointGroupsAddedDtos()));

            usagePointGroupsAddedContainer.add(rootGroupDataSet);

            //Drill down the group hierarchy
            Long parentGroupHierarchyId = rootGroupHierarchyId;
            Long groupHierarchyId = null;

            GroupHierarchy groupHierarchy = getGroupHierarchy(groupTypeID, parentGroupHierarchyId);

            if (groupHierarchy != null) {
                groupHierarchyId = groupHierarchy.getId();
            }

            while (groupHierarchy != null) {
                UsagePointGroupsAddedContainerDto subGroupDataSet = new UsagePointGroupsAddedContainerDto();

                subGroupDataSet.setUpGroupName(groupHierarchy.getName());

                // Set daily data
                subGroupDataSet.setDailyUsagePointGroupsAddedDtos(this.getSubDataSet(parentGroupHierarchyId));

                // Set monthly data
                subGroupDataSet.setMonthlyUsagePointGroupsAddedDtos(this.getMonthlyDataSet(subGroupDataSet.getDailyUsagePointGroupsAddedDtos()));

                usagePointGroupsAddedContainer.add(subGroupDataSet);

                // Drill down the group hierarchy
                parentGroupHierarchyId = groupHierarchyId;

                groupHierarchy = getGroupHierarchy(groupTypeID, parentGroupHierarchyId);
                if (groupHierarchy != null) {
                    groupHierarchyId = groupHierarchy.getId();
                }
            }
        }

        return usagePointGroupsAddedContainer;
    }

    private GroupHierarchy getRootGroupHierarchy(Long groupTypeId) throws ServiceException {
        GroupHierarchyExample example = new GroupHierarchyExample();
        example.createCriteria().andGroupTypeIdEqualTo(groupTypeId).andParentIdIsNull();
        List<GroupHierarchy> groupHierarchy = groupHierarchyMapper.selectByExample(example);

        if (groupHierarchy.size() == 1) {
            return groupHierarchy.get(0);
        } else {
            return null;
        }
    }

    private GroupHierarchy getGroupHierarchy(Long groupTypeId, Long parentGroupHierarchyId ) throws ServiceException {
        GroupHierarchyExample example = new GroupHierarchyExample();
        example.createCriteria().andGroupTypeIdEqualTo(groupTypeId).andParentIdEqualTo(parentGroupHierarchyId);
        List<GroupHierarchy> groupHierarchy = groupHierarchyMapper.selectByExample(example);

        if (groupHierarchy.size() == 1) {
            return groupHierarchy.get(0);
        } else {
            return null;
        }
    }

    private void setGroupTypeID() {
        String groupTypeName = appSettingService.getAppSettingByKey(MeterMngStatics.DASHBOARD_UPGROUP_ADDED_GROUPTYPE).getValue();
        GroupType groupType = groupService.getGroupTypeByName(groupTypeName);
        if (groupType != null) {
        	groupTypeID = groupType.getId();
        } else {
            groupTypeID = null;
            logger.debug("USAGE POINT GROUPS PANEL: Group type defined in app settings does not exist in database. Usage point groups panel will not be displayed.");
        }
    }

    private ArrayList<TsDataCountTableDto> getMonthlyDataSet(ArrayList<TsDataCountTableDto> dailyDataSet) {
        //Configure X axis range.
        ArrayList<String> xPoints = new ArrayList<>();

        Date today = Date.valueOf(LocalDate.now());
        SimpleDateFormat outputFormat = new SimpleDateFormat("yyyy-MM");

        String dateToday = outputFormat.format(today);
        String[] splitDate = dateToday.split("-");

        int year = Integer.parseInt(splitDate[0]);
        int month = Integer.parseInt(splitDate[1]);

        // Working backwards from today, add dates to X axis points rolling over year at month zero.
        for (long i = GRAPH_HISTORY; i >= 0; i--) {
            try {
                xPoints.add(outputFormat.format(outputFormat.parse(year + "-" + month)));
            } catch (ParseException e) {
                // TODO Auto-generated catch block
                e.printStackTrace();
            }
            month--;

            if (month == 0) {
                // roll back to previous year and set month to 12
                month = 12;
                year--;
            }
        }

        Collections.reverse(xPoints);

        //Create keys corresponding to months and set totals to zero.
        LinkedHashMap<String,Integer> lMap = new LinkedHashMap<>();
        for (String dateString : xPoints) {
            lMap.put(dateString, 0);
        }

        // Create a mapping of totals for month to year-month key.
        for (TsDataCountTableDto usagePointGroupsAddedDto : dailyDataSet) {
                String[] splitString = usagePointGroupsAddedDto.getTsData().split("-");
                int yearDto = Integer.parseInt(splitString[0]);
                int monthDto = Integer.parseInt(splitString[1]);
                String dtoDate = String.format("%d-%02d", yearDto,monthDto);
                if (lMap.containsKey(dtoDate)){
                    int newTotal = lMap.get(dtoDate) + usagePointGroupsAddedDto.getCount();
                    lMap.put(dtoDate, newTotal);
                }
        }

        // Add mapped points to UsagePointGroupsAddedDto's
        ArrayList<TsDataCountTableDto> monthlyUsagePointGroupsAddedDtos = new ArrayList<>();

        for (Map.Entry<String,Integer> entry : lMap.entrySet()) {
            TsDataCountTableDto dtoToSet = new TsDataCountTableDto();
            dtoToSet.setTsData(entry.getKey());
            dtoToSet.setCount(entry.getValue());
            monthlyUsagePointGroupsAddedDtos.add(dtoToSet);
        }
        return monthlyUsagePointGroupsAddedDtos;
    }

    private ArrayList<TsDataCountTableDto> getRootDataSet() {
        // Use new method of getting dataset
        Date sqlStartDate = Date.valueOf(this.startDate);
        ArrayList<Date> sqlDateArray = usagePointGroupsMapper.getFirstLevelGroupsAdded(groupTypeID,sqlStartDate);

        LocalDate entryDate = this.startDate;
        TreeMap<String,Integer> graphMapping = new TreeMap<>();

        // Create mapping of yyyy-MM-dd daily keys from start date to end date and set their values to 0.
        while (entryDate.isBefore(endDate.plusDays(1))) {
            graphMapping.put(entryDate.toString(), 0);
            entryDate = entryDate.plusDays(1);
        }

        for (Date date : sqlDateArray) {
            graphMapping.put(date.toString(), graphMapping.get(date.toString()) + 1);
        }

        ArrayList<TsDataCountTableDto> theData = new ArrayList<>();

        for (Map.Entry<String, Integer> entry : graphMapping.entrySet()) {
            theData.add(new TsDataCountTableDto(entry.getKey(),entry.getValue()));
        }

        return theData;
    }

    private ArrayList<TsDataCountTableDto> getSubDataSet(Long subGroupHierarchyId) {
        // Use new method of getting dataset
        Date sqlStartDate = Date.valueOf(this.startDate);
        ArrayList<Date> sqlDateArray = usagePointGroupsMapper.getSubLevelGroupsAdded(groupTypeID, sqlStartDate, subGroupHierarchyId);

        LocalDate entryDate = this.startDate;
        TreeMap<String,Integer> graphMapping = new TreeMap<>();

        while (entryDate.isBefore(endDate.plusDays(1))) {
            graphMapping.put(entryDate.toString(), 0);
            entryDate = entryDate.plusDays(1);
        }

        for (Date date : sqlDateArray) {
            graphMapping.put(date.toString(), graphMapping.get(date.toString()) + 1);
        }

        ArrayList<TsDataCountTableDto> theData = new ArrayList<>();

        for (Map.Entry<String, Integer> entry : graphMapping.entrySet()) {
            theData.add(new TsDataCountTableDto(entry.getKey(),entry.getValue()));
        }

        return theData;
    }
}