package za.co.ipay.metermng.server.rpc;

import java.util.ArrayList;
import java.util.List;

import org.apache.log4j.Logger;

import za.co.ipay.gwt.common.shared.exception.AccessControlException;
import za.co.ipay.gwt.common.shared.exception.ServiceException;
import za.co.ipay.gwt.common.shared.exception.ValidationException;
import za.co.ipay.metermng.client.rpc.MdcRpc;
import za.co.ipay.metermng.mybatis.generated.model.Mdc;
import za.co.ipay.metermng.server.mybatis.service.MdcService;

public class MdcRpcImpl extends BaseMeterMngRpc implements MdcRpc {

    private static final long serialVersionUID = 1L;
    
    private MdcService mdcService;
    
    public MdcRpcImpl() {
        this.logger = Logger.getLogger(MdcRpcImpl.class);
    }

    @Override
    public Integer getMdcCount() throws ServiceException {
        return mdcService.getMdcCount();
    }

    @Override
    public ArrayList<Mdc> getMdcs(int startRow, int pageSize, String sortField, boolean isAscending)
            throws ServiceException {
        return mdcService.getMdcs(startRow, pageSize, sortField, isAscending);
    }

    @Override
    public List<Mdc> getMdcs(Boolean enabled) throws ServiceException {
        return mdcService.getMdcs(enabled);
    }
    
    @Override
    public Mdc getMdcById(Long mdcId) throws ServiceException {
        return mdcService.getMdc(mdcId);
    }
    
    @Override
    public void saveMdc(Mdc mdc) throws ValidationException, ServiceException, AccessControlException {
        mdcService.saveMdc(mdc);
    }

    public void setMdcService(MdcService mdcService) {
        this.mdcService = mdcService;
    }    
}
