package za.co.ipay.metermng.server.task;

import java.util.Locale;

import org.springframework.context.support.AbstractMessageSource;

import za.co.ipay.gwt.common.server.util.ExposedReloadableResourceBundleMessageSource;
import za.co.ipay.gwt.common.shared.exception.ServiceException;
import za.co.ipay.metermng.mybatis.generated.model.MeterReadingType;
import za.co.ipay.metermng.mybatis.generated.model.TaskSchedule;
import za.co.ipay.metermng.server.mybatis.service.AppSettingService;
import za.co.ipay.metermng.server.mybatis.service.EmailService;
import za.co.ipay.metermng.server.mybatis.service.MeterService;
import za.co.ipay.metermng.server.mybatis.service.ScheduleService;
import za.co.ipay.metermng.shared.dto.schedule.ScheduledTaskDto;

/**
 * Task defines the functionality for a ScheduledTask's execution.
 * <AUTHOR>
 */
public interface Task {

    public void setScheduleService(ScheduleService scheduleService);
    
    public void setMeterService(MeterService meterService);
    
    public void setEmailService(EmailService emailService);
    
    public void setAppSettingService(AppSettingService appSettingService);

    public void setMessageSource(ExposedReloadableResourceBundleMessageSource messageSource);
    public void setFormatSource(ExposedReloadableResourceBundleMessageSource formatSource);
    
    public void setUseDefaultLocale(boolean useDefaultLocale);
    public void setDefaultLocaleName(String defaultLocaleName);
    
    public void execute(TaskSchedule taskSchedule, ScheduledTaskDto scheduledTaskDto) throws ServiceException;
    
    public String getFileName(AbstractMessageSource messageSource, Locale locale, String meterNumber, MeterReadingType readingType, String fileExtension);
    
}