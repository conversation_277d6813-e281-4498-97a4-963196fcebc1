package za.co.ipay.metermng.server.mybatis.service;

import java.util.List;

import org.apache.ibatis.session.RowBounds;
import org.springframework.transaction.annotation.Transactional;

import za.co.ipay.metermng.mybatis.generated.mapper.CustomerAgreementMapper;
import za.co.ipay.metermng.mybatis.generated.model.CustomerAgreement;
import za.co.ipay.metermng.mybatis.generated.model.CustomerAgreementExample;
import za.co.ipay.metermng.server.mybatis.mapper.ICustomerSuggestionMapper;

public class CustomerAgreementService {

    private CustomerAgreementMapper customerAgreementMapper;
    private ICustomerSuggestionMapper customerSuggestionMapper;
    private RowBounds rowBounds = new RowBounds(0, 20);
     
    public void setCustomerAgreementMapper(CustomerAgreementMapper customerAgreementMapper) {
        this.customerAgreementMapper = customerAgreementMapper;
    }

    public void setCustomerSuggestionMapper(ICustomerSuggestionMapper customerSuggestionMapper) {
        this.customerSuggestionMapper = customerSuggestionMapper;
    }
    
    @Transactional(readOnly=true)
    public CustomerAgreement getCustomerAgreementById(Long agreementId) {
        return customerAgreementMapper.selectByPrimaryKey(agreementId);
    }
    
    //This only works for systems where one customer agreement exists per customer
    @Transactional(readOnly=true)
    public CustomerAgreement getCustomerAgreementByCustomerId(Long agreementId) {
        CustomerAgreementExample customerAgreementExample = new CustomerAgreementExample();
        customerAgreementExample.createCriteria().andCustomerIdEqualTo(agreementId);
        List<CustomerAgreement> list = customerAgreementMapper.selectByExample(customerAgreementExample);
        if (list == null || list.size() == 0) {
            return null;
        } else {
            return list.get(0);
        }
    }
    
    @Transactional(readOnly=true)
    public CustomerAgreement getCustomerAgreementByAgreementRef(String agreementRef) {
        CustomerAgreementExample customerAgreementExample = new CustomerAgreementExample();
        customerAgreementExample.createCriteria().andAgreementRefEqualTo(agreementRef);
        List<CustomerAgreement> list = customerAgreementMapper.selectByExample(customerAgreementExample);
        if (list == null || list.size() == 0) {
            return null;
        } else {
            return list.get(0);
        }
    } 
    

    @Transactional(readOnly = true)
    public List<CustomerAgreement> getCustomerAgreementSearchSuggestions(String query, Long groupId) {
        if (groupId == null) {
            return customerSuggestionMapper.findAgreementByLikeRef(query.toLowerCase(), rowBounds);
       } else {
            return customerSuggestionMapper.findAgreementByLikeRefAndGroup(query.toLowerCase(), groupId, rowBounds);
       }
    }
}
