<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="za.co.ipay.metermng.server.mybatis.mapper.IUsagePointHistoryMapper" >
  <resultMap id="ExtendedResultMap" type="za.co.ipay.metermng.shared.UsagePointHistData" extends="za.co.ipay.metermng.mybatis.generated.mapper.UsagePointHistMapper.BaseResultMap">
    <result column="meter_num" property="meterNumber" jdbcType="VARCHAR" />
	<result column="agreement_ref" property="customerAgreementRef" jdbcType="VARCHAR" /> 
	<result column="initials" property="initials" jdbcType="VARCHAR" />
	<result column="surname" property="surname" jdbcType="VARCHAR" />
	<result column="erf_number" property="serviceLocationErf" jdbcType="VARCHAR" />
	<result column="type_name" property="blockingTypeName" jdbcType="VARCHAR" />
	<result column="block_reason_log_text" property="blockReasonLogText" jdbcType="VARCHAR" />
	<result column="replace_reason_log_text" property="replaceReasonLogText" jdbcType="VARCHAR" />
	<result column="status_reason_log_text" property="statusReasonLogText" jdbcType="VARCHAR" />
  </resultMap>
	<resultMap id="CustomerUsagePointMiscInfo" type="za.co.ipay.metermng.shared.CustomerUsagePointMiscInfo">
		<result column="agreement_ref" property="agreementRef" jdbcType="VARCHAR" />
		<result column="usage_point_name" property="usagePointName" jdbcType="VARCHAR" />
		<result column="customer_name" property="customerName" jdbcType="VARCHAR" />
		<result column="address_line1" property="addressLine1" jdbcType="VARCHAR" />
		<result column="address_line2" property="addressLine2" jdbcType="VARCHAR" />
		<result column="address_line3" property="addressLine3" jdbcType="VARCHAR" />
	</resultMap>
</mapper>
