package za.co.ipay.metermng.server.mybatis.service;

import java.util.List;

import org.apache.ibatis.session.RowBounds;
import org.springframework.transaction.annotation.Transactional;

import za.co.ipay.metermng.mybatis.generated.mapper.CustomerAccountMapper;
import za.co.ipay.metermng.mybatis.generated.model.CustomerAccount;
import za.co.ipay.metermng.mybatis.generated.model.CustomerAccountExample;
import za.co.ipay.metermng.server.mybatis.mapper.ICustomerSuggestionMapper;

public class CustomerAccountService {

    private CustomerAccountMapper customerAccountMapper;
    private ICustomerSuggestionMapper customerSuggestionMapper;
    private RowBounds rowBounds = new RowBounds(0, 20);
     
    public void setCustomerAccountMapper(CustomerAccountMapper customerAccountMapper) {
        this.customerAccountMapper = customerAccountMapper;
    }

    public void setCustomerSuggestionMapper(ICustomerSuggestionMapper customerSuggestionMapper) {
        this.customerSuggestionMapper = customerSuggestionMapper;
    }
    

    @Transactional(readOnly=true)
    public CustomerAccount getCustomerAccountByAccountName(String accountName) {
        CustomerAccountExample exampleAccount = new CustomerAccountExample();
        exampleAccount.createCriteria().andAccountNameEqualTo(accountName);
        List<CustomerAccount> list = customerAccountMapper.selectByExample(exampleAccount);
        if (list == null || list.size() == 0) {
            return null;
        } else {
            return list.get(0);
        }
    } 
    

    @Transactional(readOnly = true)
    public List<CustomerAccount> getCustomerAccountSearchSuggestions(String query, Long groupId) {
        if (groupId == null) {
            return customerSuggestionMapper.findAccountByLikeName(query.toLowerCase(), rowBounds);
       } else {
            return customerSuggestionMapper.findAccountByLikeNameAndGroup(query.toLowerCase(), groupId, rowBounds);
       }
    }
}
