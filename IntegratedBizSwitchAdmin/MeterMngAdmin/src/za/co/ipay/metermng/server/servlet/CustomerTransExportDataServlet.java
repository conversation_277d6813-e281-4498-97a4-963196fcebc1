package za.co.ipay.metermng.server.servlet;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.text.DateFormat;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;
import java.util.List;
import java.util.Locale;

import javax.servlet.ServletConfig;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.log4j.Logger;
import org.springframework.context.ApplicationContext;
import org.springframework.context.support.DefaultMessageSourceResolvable;
import org.springframework.web.context.WebApplicationContext;

import za.co.ipay.gwt.common.shared.exception.ServiceException;
import za.co.ipay.metermng.mybatis.custom.model.CustomerTransAlphaDataWithTotals;
import za.co.ipay.metermng.server.mybatis.service.CustomerService;
import za.co.ipay.metermng.server.mybatis.service.CustomerTransService;
import za.co.ipay.metermng.shared.CustomerAccountTransData;
import za.co.ipay.metermng.shared.CustomerTransItemData;
import za.co.ipay.metermng.shared.MeterMngStatics;
import za.co.ipay.metermng.shared.dto.CustomerAgreementData;
import za.co.ipay.metermng.shared.dto.CustomerData;
import za.co.ipay.metermng.shared.dto.user.MeterMngUser;
import za.co.ipay.utils.files.CsvWriter;

/**
 * CustomerTransExportDataServlet is used to export customer-transaction data for a specific Meter or usagePoint.
 * The currently supported file format is CSV. 
 */
public class CustomerTransExportDataServlet extends SuperExportDataServlet {

    private static final long serialVersionUID = 1L;
    private static Logger logger = Logger.getLogger(CustomerTransExportDataServlet.class.getName());

    private DateFormat filednameDate = new SimpleDateFormat("yyyyMMddHHmmss");

    //for future updates to the export function use the shared class for static references
    //centralize and avoid 'magic strings'
    private static final String USAGEPOINT_CUSTOMER_TRANS_EXPORT = "usagepoint";
    private static final String METER_CUSTOMER_TRANS_EXPORT = "meter";

    private String filterValue;
    private String filterElement;
    private Locale requestLocale;
    private SimpleDateFormat dateTimeFormat;
    private CustomerTransService customerTransService;
    private CustomerService customerService;

    @Override
    public void init(ServletConfig config) throws ServletException {
        super.init(config);
        ApplicationContext ac = (ApplicationContext) config.getServletContext().getAttribute(WebApplicationContext.ROOT_WEB_APPLICATION_CONTEXT_ATTRIBUTE);
        this.customerTransService = (CustomerTransService) ac.getBean("customerTransService");
        this.customerService = (CustomerService) ac.getBean("customerService");
        if (customerTransService == null || customerService == null || messageSource == null || formatSource == null) {
            throw new ServletException("Missing beans not set in the CustomerTransExportDataServlet: "
                    + customerTransService + " " + customerService + " " + messageSource + " " + formatSource);
        }
    }

    public void doPost(HttpServletRequest request, HttpServletResponse response) throws IOException, ServletException {
        doDataExport(request, response);
    }

    public void doGet(HttpServletRequest request, HttpServletResponse response) throws IOException, ServletException {
        doDataExport(request, response);
    }

    public void doDataExport(HttpServletRequest request, HttpServletResponse response) throws IOException {
        requestLocale = request.getLocale();
        filterValue = request.getParameter("filtervalue");
        filterElement = request.getParameter("filterelement");
        dateTimeFormat = new SimpleDateFormat(formatSource.getMessage(new DefaultMessageSourceResolvable("datetime.pattern"), requestLocale));
        try {
            CsvWriter csvContents = null;
            MeterMngUser user = getCurrentUser(request);
            if (user != null) {
                
                String type = request.getParameter("exporttype");
                String filename = getExportFileName(request);
                boolean hasFilter = isFilterApplied();
                
                if (type.equals(USAGEPOINT_CUSTOMER_TRANS_EXPORT)) {
                    String id = request.getParameter(USAGEPOINT_CUSTOMER_TRANS_EXPORT);
                    csvContents = buildUsagePointData(id, hasFilter);
                } else if (type.equals(METER_CUSTOMER_TRANS_EXPORT)) {
                    String id = request.getParameter(METER_CUSTOMER_TRANS_EXPORT);
                    csvContents = buildMeterData(id, hasFilter);
                } else if (type.equals(MeterMngStatics.CUSTOMER_CUSTOMER_TRANS_EXPORT)) {
                    String id = request.getParameter(MeterMngStatics.CUSTOMER_CUSTOMER_TRANS_EXPORT);
                    csvContents = buildCustomerData(id, hasFilter);
                } else if (type.equals(MeterMngStatics.AUX_CUSTOMER_CUSTOMER_TRANS_EXPORT)) {
                    String id = request.getParameter(MeterMngStatics.AUX_CUSTOMER_CUSTOMER_TRANS_EXPORT);
                    csvContents = buildCustomerAuxData(id, hasFilter);
                }

                if (csvContents == null) {
                    writeError(response, messageSource.getMessage(new DefaultMessageSourceResolvable("export.error.nodata"), requestLocale));
                } else {
                    logger.info("Writing csv content...");
                    InputStream inputStream = new ByteArrayInputStream(csvContents.getCSVContents().getBytes());
                    response.setContentType("text/csv");
                    response.setHeader("Content-Disposition", "attachment; fileName=\"" + filename + "\"");
                    response.setHeader("content-Length", String.valueOf(stream(inputStream, response.getOutputStream())));
                    logger.info("Completed export");
                }
            } else {
                logger.error("No session/logged in user found.");
                writeError(response, messageSource.getMessage(new DefaultMessageSourceResolvable("export.denied"), requestLocale));
            }
        } catch (Exception e) {
            logger.error("Error exporting data:", e);
            writeError(response, messageSource.getMessage(new DefaultMessageSourceResolvable("export.error"), requestLocale));
        }
    }

    private CsvWriter buildMeterData(String id, boolean hasFilter) {
        try {
            Long meterId = new Long(id);
            List<ArrayList<String>> finalList = new ArrayList<ArrayList<String>>();
            List<CustomerTransAlphaDataWithTotals> meterTransList = customerTransService.getCustomerTransWithTotalsByMeterId(meterId);
            if (hasFilter) {
                for (CustomerTransAlphaDataWithTotals transData : meterTransList) {
                    if (isValid(transData)) {
                        List<CustomerTransItemData> meterTransItemList = customerTransService.getCustomerTransItem(transData);
                        finalList.addAll(buildExportDataList(transData, meterTransItemList));
                    }
                }
            } else {
                for (CustomerTransAlphaDataWithTotals transData : meterTransList) {
                    List<CustomerTransItemData> meterTransItemList = customerTransService.getCustomerTransItem(transData);
                    finalList.addAll(buildExportDataList(transData, meterTransItemList));
                }
            }

            return ExportDataUtil.getCustomerTransDetails(messageSource, formatSource, requestLocale, finalList);
        } catch (ServiceException e) {
            logger.error("Error getting export data:", e);
        }
        return null;
    }

    private CsvWriter buildCustomerData(String id, boolean hasFilter) {
        try {
            CustomerAgreementData customerAgreementData = new CustomerAgreementData();
            customerAgreementData.setCustomerAccountId(new Long(id));
            customerAgreementData.setCustomerData(new CustomerData());
            List<ArrayList<String>> list = new ArrayList<ArrayList<String>>();
            DecimalFormat currencyFormat = new DecimalFormat(
                    formatSource.getMessage(new DefaultMessageSourceResolvable("currency.pattern"), requestLocale));
            for (CustomerAccountTransData customerAccountTransData : customerService
                    .fetchCustomerAccountTransactions(customerAgreementData)) {
                if (!hasFilter || isValid(customerAccountTransData)) {
                    ArrayList<String> exportDataRow = new ArrayList<String>();
                    exportDataRow.add(dateTimeFormat.format(customerAccountTransData.getDateEntered()));
                    exportDataRow.add(customerAccountTransData.getUserRecEntered());
                    exportDataRow.add(customerAccountTransData.getTransType());
                    exportDataRow.add(dateTimeFormat.format(customerAccountTransData.getTransDate()));
                    exportDataRow.add(customerAccountTransData.getComment());
                    exportDataRow.add(customerAccountTransData.getOurRef());
                    exportDataRow.add(removeCommaFromAmount(currencyFormat.format(customerAccountTransData.getAmtInclTax())));
                    exportDataRow.add(removeCommaFromAmount(currencyFormat.format(customerAccountTransData.getAmtTax())));
                    exportDataRow.add(removeCommaFromAmount(currencyFormat.format(customerAccountTransData.getResultantBalance())));
                    list.add(exportDataRow);
                }
            }
            return ExportDataUtil.getCustomerAccountTransactionDetails(messageSource, formatSource, requestLocale,
                    list);
        } catch (ServiceException e) {
            logger.error("Error getting export data:", e);
        }
        return null;
    }
    
    private CsvWriter buildCustomerAuxData(String id, boolean hasFilter) {
    	try {
    	List<ArrayList<String>> list = new ArrayList<ArrayList<String>>();
    	DecimalFormat currencyFormat = new DecimalFormat(
                formatSource.getMessage(new DefaultMessageSourceResolvable("currency.pattern"), requestLocale));
        for (CustomerAccountTransData customerAuxAccountTransData : customerService.fetchAuxiliaryAccountTransactions(new Long(id))) {
            if (!hasFilter || isValid(customerAuxAccountTransData)) {
                ArrayList<String> exportDataRow = new ArrayList<String>();
                exportDataRow.add(dateTimeFormat.format(customerAuxAccountTransData.getDateEntered()));
                exportDataRow.add(nullToEmpty(customerAuxAccountTransData.getUserRecEntered()));
                exportDataRow.add(customerAuxAccountTransData.getTransType());
                exportDataRow.add(dateTimeFormat.format(customerAuxAccountTransData.getTransDate()));
                exportDataRow.add(nullToEmpty(customerAuxAccountTransData.getComment()));
                exportDataRow.add(customerAuxAccountTransData.getOurRef());
                exportDataRow.add(removeCommaFromAmount(currencyFormat.format(customerAuxAccountTransData.getAmtInclTax())));
                exportDataRow.add(removeCommaFromAmount(currencyFormat.format(customerAuxAccountTransData.getAmtTax())));
                exportDataRow.add(removeCommaFromAmount(currencyFormat.format(customerAuxAccountTransData.getResultantBalance())));
                list.add(exportDataRow);
            }
        }
        return ExportDataUtil.getCustomerAccountTransactionDetails(messageSource, formatSource, requestLocale,
                list);
    	}  catch (ServiceException e) {
            logger.error("Error getting export data:", e);
        }
    	return null;
    }
    
    private String removeCommaFromAmount(String amount) {
        return amount.replace(",", "");
    }
    
    private CsvWriter buildUsagePointData(String id, boolean hasFilter) {
        try {
            Long usagepointId = new Long(id);
            List<ArrayList<String>> finalList = new ArrayList<ArrayList<String>>();

            List<CustomerTransAlphaDataWithTotals> upTransList = customerTransService.getCustomerTransWithTotalsByUsagePointId(usagepointId);
            List<CustomerTransItemData> upTransItemList = null;
            if (hasFilter) {
                for (CustomerTransAlphaDataWithTotals transData : upTransList) {
                    if (isValid(transData)) {
                        upTransItemList = customerTransService.getCustomerTransItem(transData);
                        finalList.addAll(buildExportDataList(transData, upTransItemList));
                    }
                }
            } else {
                for (CustomerTransAlphaDataWithTotals transData : upTransList) {
                    upTransItemList = customerTransService.getCustomerTransItem(transData);
                    finalList.addAll(buildExportDataList(transData, upTransItemList));
                }
            }

            return ExportDataUtil.getCustomerTransDetails(messageSource, formatSource, requestLocale, finalList);
        } catch (ServiceException e) {
            logger.error("Error getting export data:", e);
        }
        return null;
    }

    private List<ArrayList<String>> buildExportDataList(CustomerTransAlphaDataWithTotals transData,
            List<CustomerTransItemData> transItemList) {
        List<ArrayList<String>> list = new ArrayList<ArrayList<String>>();
        Iterator<CustomerTransItemData> itemsItr = transItemList.listIterator();
        while (itemsItr.hasNext()) {
            ArrayList<String> exportDataRow = new ArrayList<String>();
            exportDataRow.add(dateTimeFormat.format(transData.getTransDate())); // Date
            exportDataRow.add(transData.getTransTypeName()); // Type
            exportDataRow.add(getcustomerName(transData)); // Customer
            exportDataRow.add(transData.getClient()); // Client
            exportDataRow.add(transData.getTerminal()); // Term
            exportDataRow.add(transData.getVendRefReceived()); // Ref
            String reversed = (transData.isReversed()
                    ? messageSource.getMessage(new DefaultMessageSourceResolvable("button.yes"), requestLocale)
                    : messageSource.getMessage(new DefaultMessageSourceResolvable("button.no"), requestLocale));
            exportDataRow.add(reversed); // isreversed
            exportDataRow.add(getCsvValue(transData.getRevRefReceived())); // revref
            exportDataRow.add(getCsvValue(transData.getReversalReason())); // reversalreason
            exportDataRow.add(getCsvValue(transData.getReversedBy())); // reversedby
            exportDataRow.add(transData.getReceiptNum()); // receiptnum
            exportDataRow.add(getCsvValue(transData.getAmtInclTax())); // amount

            CustomerTransItemData transItemData = itemsItr.next();
            exportDataRow.add(transItemData.getTransItemTypeDetails().getName());// type
            exportDataRow.add(getCsvValue(transItemData.getDescription())); // description
            exportDataRow.add(getCsvValue(transItemData.getToken())); // token
            exportDataRow.add(getCsvValue(transItemData.getAmtInclTax())); // amount
            exportDataRow.add(getCsvValue(transItemData.getAmtTax())); // tax
            exportDataRow.add(getCsvValue(transItemData.getUnits())); // units
            exportDataRow.add(getCsvValue(transItemData.getTariff())); // tariff

            list.add(exportDataRow);
        }
        return list;
    }
    
    private String getCsvValue(Object value) {
        if (value == null) {
            return "";
        } else if (value instanceof BigDecimal) {
            value = ((BigDecimal) value).toString();
        }
        return (String) value;
    }

    private String getcustomerName(CustomerTransAlphaDataWithTotals data) {
        String thename = "";
        if (data.getTitle() != null) {
            thename += (data.getTitle() + " ");
        }
        if (data.getInitials() != null) {
            thename += (data.getInitials() + " ");
        } else if (data.getFirstnames() != null) {
            thename += (data.getFirstnames() + " ");
        }
        if (data.getSurname() != null) {
            thename += data.getSurname();
        }
        return thename;
    }
    
    private boolean isValid(CustomerTransAlphaDataWithTotals value) {
        if (filterElement
                .equals(messageSource.getMessage(new DefaultMessageSourceResolvable("meter.txn.date"), requestLocale))
                || filterElement.equals("Date")) {
            return isValidDateFilter(filterValue.split(","), value.getTransDate());
        } else if (filterElement
                .equals(messageSource.getMessage(new DefaultMessageSourceResolvable("meter.txn.ref"), requestLocale))
                || filterElement.equals("Reference")) {
            return value.getVendRefReceived().toLowerCase().contains(filterValue.toLowerCase());
        } else if (filterElement.equals(
                messageSource.getMessage(new DefaultMessageSourceResolvable("meter.txn.receiptnum"), requestLocale))
                || filterElement.equals("Receipt Number")) {
            return value.getReceiptNum().toLowerCase().contains(filterValue.toLowerCase());
        } else {
            return value.getSerialNum().toLowerCase().contains(filterValue);
        }
    }
    
    private boolean isValidDateFilter(String filterDates[], Date date) {
        // will check if the date is within the two filter dates. adding 1 day to the
        // end date to make the date inclusive
        return !((!filterDates[0].isEmpty() && new Date(Long.parseLong(filterDates[0])).after(date))
                || (filterDates.length == 2 && new Date(Long.parseLong(filterDates[1]) + ********).before(date)));
    }
    
    private boolean isValid(CustomerAccountTransData value) {
        if (filterElement.equals(
                messageSource.getMessage(new DefaultMessageSourceResolvable("customer.txn.trans.date"), requestLocale))
                || filterElement.equals("Transaction Date")) {
            return isValidDateFilter(filterValue.split(","), value.getTransDate());
        } else if (filterElement.equals(
                messageSource.getMessage(new DefaultMessageSourceResolvable("customer.txn.trans.type"), requestLocale))
                || filterElement.equals("Transaction Type")) {
            return value.getTransType().toLowerCase().contains(filterValue.toLowerCase());
        } else if (filterElement.equals(
                messageSource.getMessage(new DefaultMessageSourceResolvable("customer.txn.our.ref"), requestLocale))
                || filterElement.equals("Our Reference")) {
            return value.getOurRef().toLowerCase().contains(filterValue.toLowerCase());
        }
        return false;
    }

    private boolean isFilterApplied() {
        if (filterElement != null && !filterElement.trim().isEmpty()) {
            if (filterValue != null && !filterValue.trim().isEmpty()) {
                return true;
            }
        }
        return false;
    }

    public String getExportFileName(HttpServletRequest request) {
        StringBuilder sb = new StringBuilder();
        sb.append(ExportDataUtil.getFileName(request.getParameter("filenameprefix")));
        sb.append("_");
        sb.append(filednameDate.format(new Date()));
        sb.append(".csv");
        return sb.toString();
    }
    
    private String nullToEmpty(String input) {
        return (input != null) ? input : "";
    }

}
