package za.co.ipay.metermng.server.rpc;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;

import org.apache.log4j.Logger;

import za.co.ipay.gwt.common.shared.exception.ServiceException;
import za.co.ipay.gwt.common.shared.exception.ValidationException;
import za.co.ipay.ipayxml.IpayXmlMessage;
import za.co.ipay.metermng.client.rpc.UsagePointRpc;
import za.co.ipay.metermng.mybatis.custom.model.CustomerTransAlphaData;
import za.co.ipay.metermng.mybatis.custom.model.CustomerTransAlphaDataWithTotals;
import za.co.ipay.metermng.mybatis.generated.model.SpecialActionReasonsLog;
import za.co.ipay.metermng.mybatis.generated.model.UnitsTrans;
import za.co.ipay.metermng.mybatis.generated.model.UsagePoint;
import za.co.ipay.metermng.server.mybatis.service.CustomerTransService;
import za.co.ipay.metermng.server.mybatis.service.MdcTransService;
import za.co.ipay.metermng.server.mybatis.service.SpecialActionsService;
import za.co.ipay.metermng.server.mybatis.service.UpGenGroupLnkService;
import za.co.ipay.metermng.server.mybatis.service.UsagePointHistService;
import za.co.ipay.metermng.server.mybatis.service.UsagePointService;
import za.co.ipay.metermng.server.util.MeterMngUtil;
import za.co.ipay.metermng.shared.CustomerTransItemData;
import za.co.ipay.metermng.shared.CustomerTransItemOutstandCharges;
import za.co.ipay.metermng.shared.CustomerUsagePointMiscInfo;
import za.co.ipay.metermng.shared.IpayResponseData;
import za.co.ipay.metermng.shared.MdcTransData;
import za.co.ipay.metermng.shared.UsagePointHistData;
import za.co.ipay.metermng.shared.dto.LocationData;
import za.co.ipay.metermng.shared.dto.MdcChannelMatchDto;
import za.co.ipay.metermng.shared.dto.MdcChannelReadingsDto;
import za.co.ipay.metermng.shared.dto.MeterData;
import za.co.ipay.metermng.shared.dto.UpGenGroupLinkData;
import za.co.ipay.metermng.shared.dto.UpMeterInstallHistData;
import za.co.ipay.metermng.shared.dto.UpPricingStructureData;
import za.co.ipay.metermng.shared.dto.UpPricingStructureHistData;
import za.co.ipay.metermng.shared.dto.UsagePointData;
import za.co.ipay.metermng.shared.dto.meter.MdcChannelDto;
import za.co.ipay.metermng.shared.dto.user.MeterMngUser;
import za.co.ipay.metermng.tariff.thin.AccountAdjustmentResult;
import za.co.ipay.metermng.tariff.thin.IpayXmlMessageWithDelay;

@SuppressWarnings("unused")
public class UsagePointRpcImpl extends BaseMeterMngRpc implements UsagePointRpc {

    private static final long serialVersionUID = 7490298324677290717L;

    private UsagePointService usagePointService;
    private UsagePointHistService usagePointHistService;
    private CustomerTransService customerTransService;
    private UpGenGroupLnkService upGenGroupLnkService;
    private MdcTransService mdcTransService;
    private SpecialActionsService specialActionsService;

    public UsagePointRpcImpl() {
        logger = Logger.getLogger(UsagePointRpcImpl.class);
    }

    @Override
    public UsagePointData updateUsagePointComponent(UsagePointData usagePointData, LocationData serviceLocation,
                                                    LocationData usagePointLocation)
            throws ValidationException, ServiceException {
        return updateUsagePointComponent(usagePointData, serviceLocation, usagePointLocation, null);
    }

    @Override
    public UsagePointData updateUsagePointComponent(UsagePointData usagePointData, LocationData serviceLocation,
                                                    LocationData usagePointLocation,
                                                    List<MdcChannelReadingsDto> channelReadingsList)
            throws ValidationException, ServiceException {
        usagePointData.setGenGroupId(getUser().getCurrentGroupId());
        UsagePointData upData = usagePointService.updateUsagePoint(usagePointData, serviceLocation, usagePointLocation, channelReadingsList);
        if (upData != null) {
            ArrayList<UpGenGroupLinkData> groups = upGenGroupLnkService.getUpGenGroupListByUsagePointId(upData.getId());
            HashMap<Long, UpGenGroupLinkData> groupsMap = new HashMap<Long, UpGenGroupLinkData>();
            if (groups != null) {
                for (int i = 0; i < groups.size(); i++) {
                    groupsMap.put(groups.get(i).getGroupTypeId(), groups.get(i));
                }
            }
            upData.setUpgengroups(groupsMap);
        }
        return upData;
    }

    @Override
    public void removeCustomerFromUsagePoint(UsagePointData usagePointData) throws ServiceException {
        usagePointService.removeCustomerFromUsagePoint(usagePointData);

    }

    @Override
    public UsagePoint assignCustomerToUsagePoint(Long usagePointId, Long customerAgreementId) throws ServiceException {
        Long accessGroupId = getUser().getSessionGroupId();
        return usagePointService.assignCustomerToUsagePoint(usagePointId, customerAgreementId, accessGroupId);
    }
    
    @Override
    public ServiceException validateInstallationDate(Long usagePointId, Long oldMeterId, String meterNum, Date newInstallDate) {
        return usagePointService.validateInstallationDate(usagePointId, oldMeterId, meterNum, newInstallDate);
    }

    @Override
    public UsagePoint reAssignMeterToUsagePoint(UsagePointData usagePoint, MeterData meter, List<MdcChannelReadingsDto> channelReadingsList) throws ValidationException, ServiceException {
        UsagePoint toReturn = null;
        if (usagePoint.getId() != null) {
            Long accessGroupId = getUser().getSessionGroupId();
            toReturn = usagePointService.reAssignMeterToUsagePoint(usagePoint, meter, accessGroupId, channelReadingsList);
        }
        
        return toReturn;
    }
    
    @Override
    public void removeMeterFromUsagePoint(UsagePointData usagePointData) throws ServiceException, ValidationException {
        usagePointService.removeMeterFromUsagePoint(usagePointData); 
    }

    @Override
    public ArrayList<CustomerTransAlphaData> fetchTransactionHistory(Long usagepointId) {
        return (ArrayList<CustomerTransAlphaData>)customerTransService.getCustomerTransByUsagePointId(usagepointId);
            }

    @Override
    public ArrayList<CustomerTransAlphaDataWithTotals> fetchTransactionHistoryWithTotals(Long usagepointId) throws ServiceException {

        return (ArrayList<CustomerTransAlphaDataWithTotals>)customerTransService.getCustomerTransWithTotalsByUsagePointId(usagepointId);
    }

    @Override
    public ArrayList<UsagePointHistData> fetchUsagePointHistory(Long usagePointId) {
        ArrayList<UsagePointHistData> thelist = new ArrayList<UsagePointHistData>();
        List<UsagePointHistData> listfromdb = usagePointHistService.getUsagePointHistoryByUsagePointId(usagePointId);
        if (listfromdb != null) {
            UsagePointHistData upHistData;
            UsagePointHistData prev_record = null;
            Iterator<UsagePointHistData> listIt = listfromdb.iterator();
            while (listIt.hasNext()) {
                upHistData = listIt.next();
                if (prev_record != null) {
                    findChanges(prev_record, upHistData);
                }
                prev_record = upHistData;
                thelist.add(upHistData);
            }
        }
        return thelist;
    }
    
    @Override
    public UsagePoint getUsagePointByName(String usagePointName) throws ServiceException {
        return usagePointService.getUsagePointByName(usagePointName);
    }
    
    @Override
    public CustomerTransItemOutstandCharges getCustomerTransItemFromCyclicCharges(UsagePointData usagePointData, Date filterDate) throws ServiceException {
        return usagePointService.getCustomerTransItemFromCyclicCharges(usagePointData, filterDate);
    }
    
    @Override
    public void writeoffOutstandingCyclicCharges(Long usagePointId, Date upLastCyclicChargeDate, Date upLastBillingCyclicChargeDate,
            Date upNewCyclicChargeDate, String userRecEntered, CustomerTransItemOutstandCharges outstandCharges, SpecialActionReasonsLog logEntry) throws ServiceException {
        usagePointService.writeoffOutstandingCyclicCharges(usagePointId, upLastCyclicChargeDate, upLastBillingCyclicChargeDate, upNewCyclicChargeDate, userRecEntered, outstandCharges, logEntry);
    }

    @Override
    public IpayResponseData sendMeterInspectionRequestMsg(UsagePointData usagePointData, String comment) {
        return usagePointService.sendMeterInspectionRequestMsg(usagePointData, comment);
    }

    private void findChanges(UsagePointHistData obj1, UsagePointHistData obj2) {
        if (obj1.getId().equals(obj2.getId())) {
            obj1.setRecordStatusChanged(!obj1.getRecordStatus().equals(obj2.getRecordStatus()));
            obj1.setActiveStatusReasonLogIdChanged(
                    MeterMngUtil.areValuesDifferent(obj1.getStatusReasonLogText(), obj2.getStatusReasonLogText()));
            obj1.setNameChanged(MeterMngUtil.areValuesDifferent(obj1.getName(), obj2.getName()));
            obj1.setMeterIdChanged(MeterMngUtil.areValuesDifferent(obj1.getMeterId(), obj2.getMeterId()));
            obj1.setCustomerAgreementIdChanged(
                    MeterMngUtil.areValuesDifferent(obj1.getCustomerAgreementId(), obj2.getCustomerAgreementId()));
            obj1.setServiceLocationIdChanged(
                    MeterMngUtil.areValuesDifferent(obj1.getServiceLocationId(), obj2.getServiceLocationId()));
            obj1.setBlockingTypeIdChanged(
                    MeterMngUtil.areValuesDifferent(obj1.getBlockingTypeId(), obj2.getBlockingTypeId()));
            obj1.setBlockReasonLogIdChanged(
                    MeterMngUtil.areValuesDifferent(obj1.getBlockReasonLogText(), obj2.getBlockReasonLogText()));
            obj1.setReplaceReasonLogIdChanged(
                    MeterMngUtil.areValuesDifferent(obj1.getReplaceReasonLogText(), obj2.getReplaceReasonLogText()));
        }
    }
    
    @Override
    public String getAutoGeneratedRef() {
        return IpayXmlMessage.genRef();
    }
    

    @Override
    public ArrayList<MdcTransData> getMdcTransByUsagePoint(Long usagePointId) throws ServiceException {
        return mdcTransService.getMdcTransByUsagePoint(usagePointId);
    }

    @Override
    public Boolean checkIfInUse(Long pricingStructureId) {
        return usagePointService.checkIfInUse(pricingStructureId);
    }
    
    @Override
    public Boolean isValidAccessGroupUpdate(Long usagePointId, Long customerAgreementId, Long accessGroupId) {
        return usagePointService.isValidAccessGroupUpdate(usagePointId, customerAgreementId, accessGroupId);
    }

    @Override
    public ArrayList<UsagePoint> fetchUsagePointsByCustomerAgreementId(Long customerAgreementId) {
        return usagePointService.getUsagePointsByCustomerAgreementId(customerAgreementId);
    }

    @Override
    public List<Integer> countUpWithMdcUsingBillingDetId(Long billingDetId, String userName) {
        return usagePointService.countUpWithMdcUsingBillingDetId(billingDetId, userName, getUser().getCurrentGroupId());
    }

    @Override
    public MdcChannelMatchDto countUpWithMdc(Long mdcId, String userName) throws ServiceException {
        return usagePointService.countUpWithMdc(mdcId, userName, getUser().getCurrentGroupId());
    }
    
    @Override
    public MdcChannelMatchDto getMdcChannelCompatibility(Long mdcId, String userName) {
        return usagePointService.getMdcChannelCompatibility(mdcId, userName, getUser().getCurrentGroupId());
    }
    
    @Override
    public MdcChannelMatchDto getMdcChannelUpdateCompatibility(Long mdcId, MdcChannelDto updatedMdcChannel, String userName) {
        return usagePointService.getMdcChannelUpdateCompatibility(mdcId, updatedMdcChannel, userName, getUser().getCurrentGroupId());
    }

    @Override
    public MdcChannelMatchDto getMeterModelChannelCompatibility(Long meterModelId, Long newMdcId, String userName) {
        return usagePointService.getMeterModelChannelCompatibility(meterModelId, newMdcId, userName, getUser().getCurrentGroupId());
    }

    @Override
    public ArrayList<UnitsTrans> getUnitsAccountTransactions(Long unitsAccountId) {
        return usagePointService.getUnitsAccountTransactions(unitsAccountId);
    }

    @Override
    public BigDecimal inputUnitsAccountAdjustment(UnitsTrans unitsTrans) throws ServiceException {
        return usagePointService.inputUnitsAccountAdjustment(unitsTrans).getCurrentAccountBalance();
    }
 
    @Override
    public IpayResponseData sendSyncUnitsAccountBalance(MeterData meterData, Long unitsAccountId) {
        try {
            return usagePointService.sendSyncUnitsAccountBalance(meterData, unitsAccountId);
        } catch (Exception e) {
            logger.error("UsagePointRpcImpl sendSynchmsg Exception = " + e.getMessage() + " " + e);
            return null;
        }
    }
    
    @Override
    public UnitsTrans getUnitsTransactionFromCustomerTransId(Long customerTransId) {
        return usagePointService.getUnitsTransactionFromCustomerTransId(customerTransId);
    }

    @Override
    public ArrayList<UpMeterInstallHistData> fetchUpMeterInstallHistory(Long usagePointId, Long meterId, boolean usingAccessGroup) {
        return usagePointHistService.getUpMeterInstallHistDataList(usagePointId, meterId, usingAccessGroup);
    }
    
    @Override 
    public List<UpPricingStructureData> getAllUPPricingStructures(Long usagePointId, boolean usingAccessGroup) throws ServiceException {
        return usagePointService.getAllUPPricingStructuresData(usagePointId, usingAccessGroup);
    }
    
    @Override
    public Boolean deleteUpPricingStructure(Long upPricingStructureId, Long usagePointId) throws ServiceException {
        return usagePointService.deleteUpPricingStructure(upPricingStructureId, usagePointId);
    }
    
    @Override
    public List<UpPricingStructureHistData> fetchUpPricingStructureHistory(Long usagePointId, boolean usingAccessGroup) {
        return usagePointHistService.getUpPricingStructureHistDataList(usagePointId, usingAccessGroup);
    }
    
    @Override
    public CustomerUsagePointMiscInfo getLatestCustomerUsagePointMiscInfoByUsagePointId(long usagePointId, Date date) {
        return usagePointHistService.getLatestCustomerUsagePointMiscInfoByUsagePointId(usagePointId, date);
    }
    
    ///////////////////////////////////////////////////////////////////////////////////////////////////

    public void setUsagePointService(UsagePointService usagePointService) {
        this.usagePointService = usagePointService;
    }

    public void setUsagePointHistService(UsagePointHistService usagePointhistService) {
        this.usagePointHistService = usagePointhistService;
    }

    public void setCustomerTransService(CustomerTransService ctService) {
        this.customerTransService = ctService;
    }

    public void setUpGenGroupLnkService(UpGenGroupLnkService upgengrouplnkService) {
        this.upGenGroupLnkService = upgengrouplnkService;
    }

    public void setMdcTransService(MdcTransService mdcTransService) {
        this.mdcTransService = mdcTransService;
    }
    
    public void setSpecialActionsService(SpecialActionsService specialActionsService) {
        this.specialActionsService = specialActionsService;
    }
}
