package za.co.ipay.metermng.server.mybatis.mapper;

import java.sql.Timestamp;
import java.util.List;

import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.session.RowBounds;

import za.co.ipay.metermng.shared.CustomerUsagePointMiscInfo;
import za.co.ipay.metermng.shared.UsagePointHistData;


public interface IUsagePointHistoryMapper {
    
    @Select("select u.*, m.meter_num, ca.agreement_ref, c.initials, c.surname, l.erf_number, bt.type_name, " +
    		"sarl.reason_text as block_reason_log_text, sarl2.reason_text as replace_reason_log_text, sarl3.reason_text as status_reason_log_text from usage_point_hist u " +
    		"left join customer_agreement ca on u.customer_agreement_id = ca.customer_agreement_id " +
    		"left join customer c on ca.customer_id = c.customer_id " +
    		"left join location l on u.service_location_id = l.location_id " +
    		"left join meter m on u.meter_id = m.meter_id " +
    		"left join blocking_type bt on bt.blocking_type_id = u.blocking_type_id " +
    		"left join special_action_reasons_log sarl on sarl.special_action_reasons_log_id = u.block_reason_log_id " +
    		"left join special_action_reasons_log sarl2 on sarl2.special_action_reasons_log_id = u.replace_reason_log_id " +
    		"left join special_action_reasons_log sarl3 on sarl3.special_action_reasons_log_id = u.active_status_reason_log_id " +
    		"where u.usage_point_id = #{usagepointid} order by u.date_rec_modified desc")
    @ResultMap("za.co.ipay.metermng.server.mybatis.mapper.IUsagePointHistoryMapper.ExtendedResultMap")
    public List<UsagePointHistData> getUsagePointHistoryByUsagePointId(@Param("usagepointid") Long usagepointid);
    
    @Select("select u.usage_point_hist_id from usage_point_hist u where u.usage_point_id = #{usagepointid} order by u.date_rec_modified desc")
    @ResultMap("za.co.ipay.metermng.server.mybatis.mapper.IUsagePointHistoryMapper.ExtendedResultMap")
    public List<UsagePointHistData> getLatestUsagePointHistoryIdByUsagePointId(@Param("usagepointid") Long usagepointid, RowBounds rowBounds);

    @Select("SELECT agreement_ref, usage_point_name, firstnames, surname, address_line1, address_line2, address_line3 FROM usage_point_hist INNER JOIN customer_agreement_hist ON customer_agreement_hist.customer_agreement_id = usage_point_hist.customer_agreement_id INNER JOIN customer_hist ON "
            + "customer_hist.customer_id = customer_agreement_hist.customer_id INNER JOIN location_hist ON location_hist.location_id = customer_hist.physical_location_id WHERE usage_point_id = #{usagePointId} AND location_hist.date_rec_modified < #{timestamp} AND customer_hist.date_rec_modified < "
            + "#{timestamp} AND customer_agreement_hist.date_rec_modified < #{timestamp} AND usage_point_hist.date_rec_modified < #{timestamp} ORDER BY location_hist.date_rec_modified DESC, customer_hist.date_rec_modified DESC, customer_agreement_hist.date_rec_modified DESC, "
            + "usage_point_hist.date_rec_modified DESC")
    @ResultMap("za.co.ipay.metermng.server.mybatis.mapper.IUsagePointHistoryMapper.CustomerUsagePointMiscInfo")
    public List<CustomerUsagePointMiscInfo> getLatestCustomerUsagePointMiscInfoByUsagePointId(
            @Param("usagePointId") long usagePointId, @Param("timestamp") Timestamp timestamp);
}