package za.co.ipay.metermng.server.mybatis.service;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.GregorianCalendar;
import java.util.List;

import org.apache.log4j.Logger;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;

import za.co.ipay.gwt.common.shared.exception.AccessControlException;
import za.co.ipay.gwt.common.shared.exception.ServiceException;
import za.co.ipay.gwt.common.shared.exception.ValidationException;
import za.co.ipay.metermng.datatypes.RecordStatus;
import za.co.ipay.metermng.mybatis.generated.mapper.NdpDayProfileMapper;
import za.co.ipay.metermng.mybatis.generated.mapper.NdpScheduleMapper;
import za.co.ipay.metermng.mybatis.generated.mapper.NdpSeasonMapper;
import za.co.ipay.metermng.mybatis.generated.mapper.NdpSpecialDayMapper;
import za.co.ipay.metermng.mybatis.generated.model.NdpDayProfile;
import za.co.ipay.metermng.mybatis.generated.model.NdpDayProfileExample;
import za.co.ipay.metermng.mybatis.generated.model.NdpSchedule;
import za.co.ipay.metermng.mybatis.generated.model.NdpScheduleExample;
import za.co.ipay.metermng.mybatis.generated.model.NdpSeason;
import za.co.ipay.metermng.mybatis.generated.model.NdpSeasonExample;
import za.co.ipay.metermng.mybatis.generated.model.NdpSpecialDay;
import za.co.ipay.metermng.mybatis.generated.model.NdpSpecialDayExample;
import za.co.ipay.metermng.shared.NdpDayProfileData;
import za.co.ipay.metermng.shared.NdpScheduleData;

public class NdpService {

    private NdpScheduleMapper ndpScheduleMapper;
    private NdpSeasonMapper ndpSeasonMapper;
    private NdpDayProfileMapper ndpDayProfileMapper;
    private NdpSpecialDayMapper ndpSpecialDayMapper;
    
    Calendar cal = GregorianCalendar.getInstance();
    
    private Logger logger = Logger.getLogger(NdpService.class);
    
    //--------- Setters ------------------------------------------------------------------------------------------
    public void setNdpScheduleMapper(NdpScheduleMapper ndpScheduleMapper) {
        this.ndpScheduleMapper = ndpScheduleMapper;
    }
    
    public void setNdpSeasonMapper(NdpSeasonMapper ndpSeasonMapper) {
        this.ndpSeasonMapper = ndpSeasonMapper;
    }
    
    public void setNdpDayProfileMapper(NdpDayProfileMapper ndpDayProfileMapper) {
        this.ndpDayProfileMapper = ndpDayProfileMapper;
    }
    
    public void setNdpSpecialDayMapper(NdpSpecialDayMapper ndpSpecialDayMapper) {
        this.ndpSpecialDayMapper = ndpSpecialDayMapper;
    }

    //--------- Methods ------------------------------------------------------------------------------------------
    @Transactional(readOnly=true)
    public NdpScheduleData getGlobalNdpScheduleData() throws ServiceException, AccessControlException{
        NdpScheduleExample ndpExample = new NdpScheduleExample();
        ndpExample.createCriteria().andGlobalScheduleEqualTo(true);
        List<NdpSchedule> lst = ndpScheduleMapper.selectByExample(ndpExample);
        if (lst != null && lst.size() > 0) {
            return getNdpScheduleData(lst.get(0).getId());
        } else {
            logger.error("*** NdpService : No Global NDP Schedule.");
            return null;
        }
    }
    
    @Transactional(readOnly=true)
    public NdpScheduleData getNdpScheduleData(Long ndpScheduleId) throws ServiceException, AccessControlException{
        NdpScheduleExample ndpExample = new NdpScheduleExample();
        ndpExample.createCriteria().andIdEqualTo(ndpScheduleId);
        List<NdpSchedule> lst = ndpScheduleMapper.selectByExample(ndpExample);
        if (lst != null && lst.size() == 1) {
            //all good, just double check that found;
        } else {
            logger.error("*** NdpService ERROR: Retrieve Ndp Schedule data problem: list is null or list.size() != 1; id = " + ndpScheduleId.toString());
            throw new ServiceException("Retrieve Ndp Schedule data problem. Contact Support.");
        }
        
        NdpScheduleData data = new NdpScheduleData(lst.get(0));
        data.setNdpSeasonList((ArrayList<NdpSeason>)getNdpSeasons(ndpScheduleId));
        data.setNdpSpecialDayList((ArrayList<NdpSpecialDay>)getNdpSpecialDays(ndpScheduleId));
        
        //establish whether an NDP time has been entered yet (then the schedule can be activated)
        if (data.getNdpSchedule().getRecordStatus().equals(RecordStatus.ACT)) {
            data.setNdpTimeEntered(true);
        } else {
            data.setNdpTimeEntered(isDayProfilePresentForSchedule(data.getNdpSeasonList(), data.getNdpSpecialDayList(), null, null));
        }
        
        return data;
    }
    
    @Transactional
    @PreAuthorize("hasAnyRole('mm_ndp_admin', 'mm_ndp_group_admin')")
    public NdpSchedule addNdpSchedule(Boolean isGlobal) throws ServiceException, AccessControlException {
        NdpSchedule ndpSchedule = new NdpSchedule();
        ndpSchedule.setGlobalSchedule(isGlobal);
        ndpSchedule.setRecordStatus(RecordStatus.DAC);
        if (ndpScheduleMapper.insert(ndpSchedule) == 1) {
            return ndpSchedule;
        } else {
            throw new ServiceException("ndp.error.save.schedule");
        }
    }
    
    @Transactional
    @PreAuthorize("hasAnyRole('mm_ndp_admin', 'mm_ndp_group_admin')")
    public NdpSchedule saveNdpSchedule(NdpSchedule ndpSchedule) throws  ServiceException, AccessControlException{
        if (ndpScheduleMapper.updateByPrimaryKey(ndpSchedule) != 1) {
            throw new ServiceException("ndp.error.save.schedule");
        }
        return ndpSchedule;
    }
    
    @Transactional(readOnly=true)
    public Boolean isDayProfilePresentForSchedule(ArrayList<NdpSeason> checkTheseSeasons, ArrayList<NdpSpecialDay> checkTheseSpecialDays, NdpDayProfileData excludeDeleteDay, ArrayList<NdpDayProfileData> deleteTheseDayProfiles) throws ServiceException, AccessControlException {
        Boolean isPresent = false;
        for (NdpSeason ss : checkTheseSeasons) {
            ArrayList<NdpDayProfile> dayProfilesList = (ArrayList<NdpDayProfile>) getNdpSeasonDayProfiles(ss.getId());
            ArrayList<NdpDayProfile> removeTheseNdp = new ArrayList<NdpDayProfile>();
            
            //exclude the dayProfiles already waiting to be deleted & then exclude the new dayprofile to be deleted now
            for (NdpDayProfile ndp : dayProfilesList) {
                if (deleteTheseDayProfiles != null) {
                    for (NdpDayProfileData ndpd : deleteTheseDayProfiles) {
                        logger.info(">>>RC: dayprofiles already waiting to be deleted: ndp.getId()=" + ndp.getId() + "  ndpd.getNdpDayProfile().getId()= " + ndpd.getNdpDayProfile().getId());
                        if (ndp.getId().equals(ndpd.getNdpDayProfile().getId())) {
                            removeTheseNdp.add(ndp);
                        }
                    }
                }
                if (excludeDeleteDay != null && excludeDeleteDay.getNdpDayProfile().getId() != null) {
                    logger.info(">>>RC: dayprofile now waiting to be deleted: ndp.getId()=" + ndp.getId() + "  excludeDeleteDay.getNdpDayProfile().getId()= " + excludeDeleteDay.getNdpDayProfile().getId());
                    if (ndp.getId().equals(excludeDeleteDay.getNdpDayProfile().getId())) {
                        removeTheseNdp.add(ndp);
                    }
                }
            }
            logger.info(">>>RC: removeTheseNdp.size() = " + removeTheseNdp.size());
            dayProfilesList.removeAll(removeTheseNdp);
            if (dayProfilesList != null && !dayProfilesList.isEmpty()) {
                isPresent = true;
                break;
            }
            logger.info(">>RC: isDayProfilePresentForSchedule: dayprofilesList is empty!!");
        }
        //if still not day Times - also check the special day times
        if (!isPresent) {
            for (NdpSpecialDay sd : checkTheseSpecialDays) {
                ArrayList<NdpDayProfile> dayProfilesList = (ArrayList<NdpDayProfile>) getNdpSpecialDayProfiles(sd.getId());
                ArrayList<NdpDayProfile> removeTheseNdp = new ArrayList<NdpDayProfile>();
                
                //exclude the dayProfiles already waiting to be deleted & then exclude the new dayprofile to be deleted now
                for (NdpDayProfile ndp : dayProfilesList) {
                    if (deleteTheseDayProfiles != null) {
                        for (NdpDayProfileData ndpd : deleteTheseDayProfiles) {
                            logger.info(">>>RC: SPECIAL dayprofiles already waiting to be deleted: ndp.getId()=" + ndp.getId() + "  ndpd.getNdpDayProfile().getId()= " + ndpd.getNdpDayProfile().getId());
                            if (ndp.getId().equals(ndpd.getNdpDayProfile().getId())) {
                                removeTheseNdp.add(ndp);
                            }
                        }
                    }
                    if (excludeDeleteDay != null && excludeDeleteDay.getNdpDayProfile().getId() != null) {
                        logger.info(">>>RC: SPECIAL dayprofile now waiting to be deleted: ndp.getId()=" + ndp.getId() + "  excludeDeleteDay.getNdpDayProfile().getId()= " + excludeDeleteDay.getNdpDayProfile().getId());
                        if (ndp.getId().equals(excludeDeleteDay.getNdpDayProfile().getId())) {
                            removeTheseNdp.add(ndp);
                        }
                    }
                }
                dayProfilesList.removeAll(removeTheseNdp);
                if (dayProfilesList != null && !dayProfilesList.isEmpty()) {
                    isPresent = true;
                    break;
                }
            }
            logger.info(">>RC: isDayProfilePresentForSchedule: special dayprofilesList is empty!!");
        }
        logger.info(">>RC isPresent=" + isPresent);
        return isPresent;
    }
    
    @Transactional(readOnly=true)
    public List<NdpSeason> getNdpSeasons(Long ndpScheduleId) throws ServiceException, AccessControlException{
        NdpSeasonExample ndpExample = new NdpSeasonExample();
        ndpExample.createCriteria().andNdpScheduleIdEqualTo(ndpScheduleId);
        return ndpSeasonMapper.selectByExample(ndpExample);
    }
    
    @Transactional(readOnly=true)
    public NdpSeason getNdpSeasonByPrimary(Long ndpSeasonId) throws ServiceException, AccessControlException{
        return ndpSeasonMapper.selectByPrimaryKey(ndpSeasonId);
    }
    
    @Transactional(readOnly=true)
    public List<NdpSpecialDay> getNdpSpecialDays(Long ndpScheduleId) throws ServiceException, AccessControlException{
        NdpSpecialDayExample ndpExample = new NdpSpecialDayExample();
        ndpExample.createCriteria().andNdpScheduleIdEqualTo(ndpScheduleId);
        return ndpSpecialDayMapper.selectByExample(ndpExample);
    }
    
    @Transactional(readOnly=true)
    public List<NdpDayProfileData> getNdpSeasonDayProfilesData(Long ndpSeasonId) throws ServiceException, AccessControlException{
        ArrayList<NdpDayProfile> list = (ArrayList<NdpDayProfile>) getNdpSeasonDayProfiles(ndpSeasonId);
        ArrayList<NdpDayProfileData> dayProfileDataList = new ArrayList<NdpDayProfileData>();
        
        for (NdpDayProfile ndp : list) {
            NdpDayProfileData ndpd = new NdpDayProfileData(ndp);
            setTimeInMillis(ndpd);
            dayProfileDataList.add(ndpd);
        }
        
        return dayProfileDataList;
    }
    
    @Transactional(readOnly=true)
    public List<NdpDayProfileData> getNdpSpecialDayProfilesData(Long ndpSpecialDayId) throws ServiceException, AccessControlException{
        ArrayList<NdpDayProfile> list = (ArrayList<NdpDayProfile>) getNdpSpecialDayProfiles(ndpSpecialDayId);
        ArrayList<NdpDayProfileData> dayProfileDataList = new ArrayList<NdpDayProfileData>();
        
        for (NdpDayProfile ndp : list) {
            NdpDayProfileData ndpd = new NdpDayProfileData(ndp);
            setTimeInMillis(ndpd);
            dayProfileDataList.add(ndpd);
        }
        
        return dayProfileDataList;
    }
    
    @Transactional(readOnly=true)
    public List<NdpDayProfile> getNdpSeasonDayProfiles(Long ndpSeasonId) throws ServiceException, AccessControlException{
        NdpDayProfileExample ndpdExample = new NdpDayProfileExample();
        ndpdExample.createCriteria().andNdpSeasonIdEqualTo(ndpSeasonId);
        ndpdExample.setOrderByClause("day_of_week asc");
        return ndpDayProfileMapper.selectByExample(ndpdExample);
    }    
    
    @Transactional(readOnly=true)
    public List<NdpDayProfile> getNdpSpecialDayProfiles(Long ndpSpecialDayId) throws ServiceException, AccessControlException{
        NdpDayProfileExample ndpdExample = new NdpDayProfileExample();
        ndpdExample.createCriteria().andNdpSpecialDayIdEqualTo(ndpSpecialDayId);
        ndpdExample.setOrderByClause("start_hour, start_minute asc");
        return ndpDayProfileMapper.selectByExample(ndpdExample);
    }    
    
    public NdpDayProfileData setTimeInMillis(NdpDayProfileData ndpd) {
        cal.set(2000, 1, 1);
        cal.set(Calendar.SECOND, 0);
        cal.set(Calendar.MILLISECOND, 0);
        
        cal.set(Calendar.HOUR_OF_DAY, ndpd.getNdpDayProfile().getStartHour());
        cal.set(Calendar.MINUTE, ndpd.getNdpDayProfile().getStartMinute());
        ndpd.setStartMs(cal.getTimeInMillis());
        
        cal.set(Calendar.HOUR_OF_DAY, ndpd.getNdpDayProfile().getEndHour());
        cal.set(Calendar.MINUTE, ndpd.getNdpDayProfile().getEndMinute());
        ndpd.setEndMs(cal.getTimeInMillis());
        return ndpd;
    }
 
    @Transactional
    @PreAuthorize("hasAnyRole('mm_ndp_admin', 'mm_ndp_group_admin')")
    public void deleteNdpScheduleByPrimaryKey(Long ndpScheduleId) throws ServiceException {
        //delete NdpSchedule & everything in it!!
        
        //delete daysProfiles & seasons
        NdpSeasonExample ndpSeasonExample = new NdpSeasonExample();
        ndpSeasonExample.createCriteria().andNdpScheduleIdEqualTo(ndpScheduleId);
        ArrayList<NdpSeason> ndpSeasonList = (ArrayList<NdpSeason>) ndpSeasonMapper.selectByExample(ndpSeasonExample);
        for (NdpSeason ss : ndpSeasonList) {
            NdpDayProfileExample ndpExample = new NdpDayProfileExample();
            ndpExample.createCriteria().andNdpSeasonIdEqualTo(ss.getId());
            ndpDayProfileMapper.deleteByExample(ndpExample);
            if (ndpSeasonMapper.deleteByPrimaryKey(ss.getId()) < 1) {
                throw new ServiceException("ndp.error.delete.season");
            }
        }
        
        //delete Special Days
        NdpSpecialDayExample spdExample = new NdpSpecialDayExample();
        spdExample.createCriteria().andNdpScheduleIdEqualTo(ndpScheduleId);
        ArrayList<NdpSpecialDay> ndpSpecialDayList = (ArrayList<NdpSpecialDay>) ndpSpecialDayMapper.selectByExample(spdExample);
        for(NdpSpecialDay spd : ndpSpecialDayList) {
            NdpDayProfileExample ndpExample = new NdpDayProfileExample();
            ndpExample.createCriteria().andNdpSpecialDayIdEqualTo(spd.getId());
            ndpDayProfileMapper.deleteByExample(ndpExample);
            
            ndpSpecialDayMapper.deleteByPrimaryKey(spd.getId());
        }
        
        //delete the Schedule
        if (ndpScheduleMapper.deleteByPrimaryKey(ndpScheduleId) != 1) {
            throw new ServiceException("ndp.error.delete.schedule");
        } 
        
    }
    
    @Transactional
    @PreAuthorize("hasAnyRole('mm_ndp_admin', 'mm_ndp_group_admin')")
    public NdpSeason saveNdpSeasonAndDays(NdpSeason ndpSeason, ArrayList<NdpDayProfileData> ndpDayProfileDataList, ArrayList<NdpDayProfileData> deleteTheseDayProfiles) throws ValidationException, ServiceException, AccessControlException {
        if (ndpSeason.getId() == null) {
            if (ndpSeasonMapper.insert(ndpSeason) != 1) {
                throw new ServiceException("ndp.error.save.season");
            }    
        } else {
            if (ndpSeasonMapper.updateByPrimaryKey(ndpSeason) != 1) {
                throw new ServiceException("ndp.error.save.season");
            }
        }
        
        for (NdpDayProfileData ndpd : ndpDayProfileDataList) {
            NdpDayProfile ndp = ndpd.getNdpDayProfile();
            if (ndp.getNdpSeasonId() == null) {
                ndp.setNdpSeasonId(ndpSeason.getId());
                ndp.setNdpSpecialDayId(null);
                saveNdpDayProfile(ndp);
            }
        }
        
        logger.info("deleteTheseDayProfiles.size()=" + deleteTheseDayProfiles.size());
        for (NdpDayProfileData ndpd : deleteTheseDayProfiles) {
            if (ndpd.getNdpDayProfile().getId() != null) {
                ndpDayProfileMapper.deleteByPrimaryKey(ndpd.getNdpDayProfile().getId());
            }
        }
        
        return ndpSeason;
    }
    
    @Transactional
    @PreAuthorize("hasAnyRole('mm_ndp_admin', 'mm_ndp_group_admin')")
    public NdpSpecialDay saveNdpSpecialDayAndTimes(NdpSpecialDay ndpSpecialDay, ArrayList<NdpDayProfileData> dayProfileList, ArrayList<NdpDayProfileData> deleteTheseDayProfiles) throws  ServiceException, AccessControlException{
        if (ndpSpecialDay.getId() == null) {
            if (ndpSpecialDayMapper.insert(ndpSpecialDay) != 1) {
                throw new ServiceException("ndp.error.save.special.day");
            }    
        } else {
            if (ndpSpecialDayMapper.updateByPrimaryKey(ndpSpecialDay) != 1) {
                throw new ServiceException("ndp.error.save.special.day");
            }
        }
        
        for (NdpDayProfileData ndpd : dayProfileList) {
            NdpDayProfile ndp = ndpd.getNdpDayProfile();
            if (ndp.getNdpSpecialDayId() == null) {
                ndp.setNdpSpecialDayId(ndpSpecialDay.getId());
                ndp.setNdpSeasonId(null);
                ndp.setDayOfWeek(null);
                saveNdpDayProfile(ndp);
            }
        }
        
        for (NdpDayProfileData ndpd : deleteTheseDayProfiles) {
            if (ndpd.getNdpDayProfile().getId() != null) {
                ndpDayProfileMapper.deleteByPrimaryKey(ndpd.getNdpDayProfile().getId());
            }
        }
        
        return ndpSpecialDay;
    }
    
    @Transactional
    @PreAuthorize("hasAnyRole('mm_ndp_admin', 'mm_ndp_group_admin')")
    public NdpDayProfile saveNdpDayProfile(NdpDayProfile ndpDayProfile) throws  ServiceException, AccessControlException{
        if (ndpDayProfileMapper.insert(ndpDayProfile) != 1) {
            throw new ServiceException("ndp.error.save.day");
        }
        
        return ndpDayProfile;
    }
    
    @Transactional
    @PreAuthorize("hasAnyRole('mm_ndp_admin', 'mm_ndp_group_admin')")
    public void deleteSeasonAndDayProfiles(Long ndpSeasonId) throws ServiceException, AccessControlException{
        List<NdpDayProfile> dayProfileList = getNdpSeasonDayProfiles(ndpSeasonId);
        for (NdpDayProfile ndp : dayProfileList) {
            ndpDayProfileMapper.deleteByPrimaryKey(ndp.getId());
        }
        
        ndpSeasonMapper.deleteByPrimaryKey(ndpSeasonId);
    }
    
    @Transactional
    @PreAuthorize("hasAnyRole('mm_ndp_admin', 'mm_ndp_group_admin')")
    public void deleteSpecialDayAndDayProfiles(Long ndpSpecialDayId) throws ServiceException, AccessControlException{
        List<NdpDayProfile> dayProfileList = getNdpSpecialDayProfiles(ndpSpecialDayId);
        for (NdpDayProfile ndp : dayProfileList) {
            ndpDayProfileMapper.deleteByPrimaryKey(ndp.getId());
        }
        
        ndpSpecialDayMapper.deleteByPrimaryKey(ndpSpecialDayId);
    }
}
