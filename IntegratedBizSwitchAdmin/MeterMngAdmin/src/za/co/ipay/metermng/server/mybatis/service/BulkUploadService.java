package za.co.ipay.metermng.server.mybatis.service;

import java.util.ArrayList;
import java.util.List;

import org.apache.log4j.Logger;
import org.springframework.context.support.DefaultMessageSourceResolvable;
import org.springframework.transaction.annotation.Transactional;

import za.co.ipay.gwt.common.server.util.ExposedReloadableResourceBundleMessageSource;
import za.co.ipay.metermng.mybatis.generated.model.AccountTrans;
import za.co.ipay.metermng.shared.AuxAccountData;
import za.co.ipay.metermng.shared.dto.MeterData;
import za.co.ipay.metermng.shared.dto.uploaddata.transuploaddata.AuxTransCsvData;
import za.co.ipay.metermng.shared.dto.uploaddata.transuploaddata.CustomerTransCsvData;

public class BulkUploadService {

    private MeterService meterService;
    private CustomerService customerService;
    private AuxAccountService auxAccountService;
    
    private ExposedReloadableResourceBundleMessageSource importMessageSource;
    
    private static Logger logger = Logger.getLogger(BulkUploadService.class);
    
    public void setMeterService(MeterService meterService) {
        this.meterService = meterService;
    }
    
    public void setCustomerService(CustomerService customerService) {
        this.customerService = customerService;
    }

	public void setAuxAccountService(AuxAccountService auxAccountService) {
		this.auxAccountService = auxAccountService;
	}

	public void setImportMessageSource(ExposedReloadableResourceBundleMessageSource importMessageSource) {
        this.importMessageSource = importMessageSource;
    }
    
    //-------------------------------------------------------------------------------------------------------------------------------
    
	@Transactional
	public String meterBulkUpload(ArrayList<MeterData> meterDataList, int noDupsIgnored) {
		int successUploadsCount = 0;
		boolean probablyHasNewMeters = false;
		for (MeterData meterData : meterDataList) {
			try {
				if(meterData.getId()==null){
					probablyHasNewMeters = true;
				}
				meterService.saveBulkUploadMeter(meterData);
			} catch (Exception e) {
				logger.debug("BulkUploadService: UPLOAD FAILED. meterBulkUpload: successful uploads to this point="
						+ successUploadsCount + "   Exception=" + e);

				boolean uploadsWithProbablyNewMeters = (successUploadsCount > 0 && probablyHasNewMeters);
				meterService.sendAggregationRequest(uploadsWithProbablyNewMeters, MeterService.BATCH_AGGREGATION_TYPE);
				return "BulkException:," + e.getMessage() + " : "+importMessageSource.getMessage(new DefaultMessageSourceResolvable(new String[] { "bulk.upload.process.failed" },
						new String[] { String.valueOf(successUploadsCount) }, ""), null);
			}
			successUploadsCount += 1;
		}
		boolean uploadsWithProbablyNewMeters = (successUploadsCount > 0 && probablyHasNewMeters);
		meterService.sendAggregationRequest(uploadsWithProbablyNewMeters, MeterService.BATCH_AGGREGATION_TYPE);

		String returnMessage = importMessageSource.getMessage(new DefaultMessageSourceResolvable(new String[] { "bulk.upload.successful.meter.upload" },
                new String[] { String.valueOf(meterDataList.size()), String.valueOf(noDupsIgnored)}, ""), null);
		logger.debug("bulkservice returns: " + returnMessage);
		return returnMessage;
	}

	public String customerTransBulkUpload(ArrayList<AccountTrans> accountTransList, List<CustomerTransCsvData> processTransList) {
		try {
			return customerService.inputBulkAccountAdjustments(accountTransList);
		} catch (Exception e) {
			String error = e.getMessage();
			String[] resultArray = e.getMessage().split(",");
			if (resultArray.length > 2) {
				String noSuccessfulTrans = resultArray[3];
				String noDuplicates = resultArray[4];
				int failedOnindex = Integer.parseInt(noSuccessfulTrans) + Integer.parseInt(noDuplicates);
				CustomerTransCsvData ctcd = processTransList.get(failedOnindex);
				error = "BulkException:," + importMessageSource.getMessage("customer.trans.upload.process.failed",
						new String[] { ctcd.getIdentifierType(), ctcd.getIdentifier(), ctcd.getAccountRef() }, null);
			}

			logger.error("**** inputBulkAccountAdjustments Exception:, errormessage, total trans, noSuccessful, noAlreadyProcessed, identifierType, identifier, accountRef");
			logger.error("****                             " + error + "  exception= " + e.getMessage());
			return error;
		}
	}

	public String auxTransBulkUpload(ArrayList<AccountTrans> accountTransList, List<AuxTransCsvData> processTransList) {
		try {
			return customerService.inputBulkAuxAccountAdjustments(accountTransList);
		} catch (Exception e) {
			String error = e.getMessage();
			String[] resultArray = e.getMessage().split(",");
			if (resultArray.length > 2) {
				String noSuccessfulTrans = resultArray[3];
				String noDuplicates = resultArray[4];
				int failedOnindex = Integer.parseInt(noSuccessfulTrans) + Integer.parseInt(noDuplicates);
				AuxTransCsvData ctcd = processTransList.get(failedOnindex);
				error = "BulkException:," + importMessageSource.getMessage("auxaccount.trans.upload.process.failed",
						new String[] { ctcd.getAuxAccountName(), ctcd.getAgreementRef() }, null);
			}

			logger.error("**** inputAuxBulkAccountAdjustments Exception:, errormessage, total trans, noSuccessful, noAlreadyProcessed, auxAccounName, accountRef");
			logger.error("****                             " + error + "  exception= " + e.getMessage());
			return error;
		}
	}

	@Transactional
	public String auxAccountDataBulkUpload(ArrayList<AuxAccountData> auxAccountDataList) {
		int successUploadsCount = 0;

		for (AuxAccountData acd : auxAccountDataList) {
			try {
				auxAccountService.updateAuxAccount(acd);
			} catch (Exception e) {
				logger.debug("BulkUploadService: UPLOAD FAILED. auxAccountBulkUpload: successful uploads to this point=" + successUploadsCount + "   Exception=" + e);
				return "BulkException:," + e.getMessage() + " : " + importMessageSource.getMessage(new DefaultMessageSourceResolvable(new String[] { "bulk.upload.process.failed" },
										new String[] { String.valueOf(successUploadsCount) }, ""), null);
			}
			successUploadsCount += 1;
		}
		logger.debug("bulkservice returns: " + importMessageSource.getMessage(new DefaultMessageSourceResolvable(new String[] { "auxaccount.upload.successful.count" },
						new String[] { String.valueOf(auxAccountDataList.size()) }, ""), null));
		return importMessageSource.getMessage(new DefaultMessageSourceResolvable(new String[] { "auxaccount.upload.successful.count" },
						new String[] { String.valueOf(auxAccountDataList.size()) }, ""), null);
	}
    
}