package za.co.ipay.metermng.server.rpc;

import java.util.ArrayList;

import org.apache.log4j.Logger;

import za.co.ipay.gwt.common.shared.exception.ServiceException;
import za.co.ipay.gwt.common.shared.exception.ValidationException;
import za.co.ipay.metermng.client.rpc.ScheduleRpc;
import za.co.ipay.metermng.datatypes.RecordStatus;
import za.co.ipay.metermng.mybatis.generated.model.TaskSchedule;
import za.co.ipay.metermng.server.mybatis.service.ScheduleService;
import za.co.ipay.metermng.server.mybatis.service.SchedulingService;
import za.co.ipay.metermng.shared.dto.schedule.ScheduledTaskDto;
import za.co.ipay.metermng.shared.dto.schedule.ScheduledTaskScreenData;

public class ScheduleRpcImpl extends BaseMeterMngRpc implements ScheduleRpc {

    private static final long serialVersionUID = 1L;
    
    private ScheduleService scheduleService;
    private SchedulingService schedulingService;
    
    public ScheduleRpcImpl() {
        this.logger = Logger.getLogger(ScheduleRpcImpl.class);
    }

    @Override
    public ScheduledTaskScreenData getScheduledTaskScreenData() throws ServiceException {
        return scheduleService.getTaskScheduleScreenData();
    }

    @Override
    public Integer getTaskScheduleCount() throws ServiceException {
        return scheduleService.getTaskScheduleCount();
    }

    @Override
    public ArrayList<TaskSchedule> getTaskSchedules(int startRow, int pageSize, String sortField, boolean isAscending)
            throws ServiceException {
        return scheduleService.getTaskSchedules(startRow, pageSize, sortField, isAscending);
    }

    @Override
    public void saveTaskSchedule(TaskSchedule taskSchedule) throws ValidationException, ServiceException {
        TaskSchedule existing = null;
        if (taskSchedule.getId() != null) {
            existing = scheduleService.getTaskSchedule(taskSchedule.getId());
        }
        
        scheduleService.saveTaskSchedule(taskSchedule);  
        logger.info("Saved TaskSchedule: "+taskSchedule.getTaskScheduleName());
         
        if (!RecordStatus.ACT.equals(taskSchedule.getRecordStatus())) {
            schedulingService.descheduleTaskSchedule(taskSchedule);
            logger.info("Descheduled taskSchedule: "+taskSchedule.getTaskScheduleName());
        } else if (RecordStatus.ACT.equals(taskSchedule.getRecordStatus())) {
            if (existing == null || !existing.getCronExpression().equals(taskSchedule.getCronExpression())) {
                schedulingService.rescheduleTaskSchedule(taskSchedule);
                logger.info("Rescheduled taskSchedule: "+taskSchedule.getTaskScheduleName());
            } else {
                logger.info("TaskSchedule's cronExp has not changed, not rescheduling: "+taskSchedule.getTaskScheduleName());
            }
        } 
    }

    @Override
    public ArrayList<ScheduledTaskDto> getScheduleTasks(Long taskScheduleId) throws ValidationException, ServiceException {
        return scheduleService.getScheduleTasks(taskScheduleId);
    }

    @Override
    public void saveScheduledTask(ScheduledTaskDto scheduledTaskDto) throws ValidationException, ServiceException {
        scheduleService.saveScheduledTask(scheduledTaskDto);
    }
    
    @Override
    public void deleteScheduledTask(Long scheduledTaskId) throws ValidationException, ServiceException {
        scheduleService.deleteScheduledTask(scheduledTaskId);
    }
    
    public void setScheduleService(ScheduleService scheduleService) {
        this.scheduleService = scheduleService;
    }

    public void setSchedulingService(SchedulingService schedulingService) {
        this.schedulingService = schedulingService;
    }
}
