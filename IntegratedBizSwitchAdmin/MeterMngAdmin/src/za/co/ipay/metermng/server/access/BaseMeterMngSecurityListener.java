package za.co.ipay.metermng.server.access;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;

import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import za.co.ipay.accesscontrol.domain.Permission;
import za.co.ipay.accesscontrol.domain.UserRole;
import za.co.ipay.metermng.shared.dto.user.MeterMngUser;

public class BaseMeterMngSecurityListener {
    
    protected HttpSession getSession(boolean create) {
        HttpServletRequest request = getRequest();
        if (request != null) {
            return request.getSession(create);  
       } else {
           return null;
       }
    }
    
    protected HttpServletRequest getRequest() {
        ServletRequestAttributes requestAttributes = (ServletRequestAttributes) RequestContextHolder.currentRequestAttributes();
        if (requestAttributes != null) {
            return requestAttributes.getRequest();  
       } else {
           return null;
       }
    }

    protected void setRolesPerms(List<UserRole> list, MeterMngUser meterMngUser) {
        //Roles and permissions
        ArrayList<String> roleNames = new ArrayList<String>();
        HashSet<String> permissionSet = new HashSet<String>();        
        for (UserRole userRole : list) {
            roleNames.add(userRole.getRole().getName());
            List<Permission> permissions = userRole.getRole().getPermissions();
            for (Permission permission : permissions) {
                permissionSet.add(permission.getName());
            }
        }
        ArrayList<String> permissionNames = new ArrayList<String>(permissionSet);
        permissionNames.trimToSize();
        roleNames.trimToSize();        
        meterMngUser.setPermissions(permissionNames);
        meterMngUser.setRoles(roleNames);
    }

}
