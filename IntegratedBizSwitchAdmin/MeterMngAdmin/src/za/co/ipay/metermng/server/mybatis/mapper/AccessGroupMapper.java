package za.co.ipay.metermng.server.mybatis.mapper;

import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

public interface AccessGroupMapper {
    
    @Update("update usage_point set access_group_id = null where usage_point_id = #{usagePointId} and access_group_id is not null")
    public int clearGroupForUsagePoint(@Param("usagePointId") Long usagePointId);
    
}
