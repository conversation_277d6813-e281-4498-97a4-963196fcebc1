package za.co.ipay.metermng.server.rpc;

import java.util.ArrayList;
import java.util.List;

import org.apache.log4j.Logger;

import za.co.ipay.gwt.common.shared.exception.AccessControlException;
import za.co.ipay.gwt.common.shared.exception.ServiceException;
import za.co.ipay.gwt.common.shared.exception.ValidationException;
import za.co.ipay.gwt.common.shared.exception.ValidationMessage;
import za.co.ipay.metermng.client.rpc.BillingDetRpc;
import za.co.ipay.metermng.mybatis.generated.model.BillingDet;
import za.co.ipay.metermng.server.mybatis.service.BillingDetService;
import za.co.ipay.metermng.server.validation.ServerValidatorUtil;
import za.co.ipay.metermng.service.tariffdata.RegisterReadingThinDataService;
import za.co.ipay.metermng.shared.dto.BillingDetAppliesDto;
import za.co.ipay.metermng.shared.dto.BillingDetPrimaryDto;

public class BillingDetRpcImpl extends BaseMeterMngRpc implements BillingDetRpc {

    private static final long serialVersionUID = 1L;
    
    private BillingDetService billingDetService;
    
    public BillingDetRpcImpl() {
        this.logger = Logger.getLogger(BillingDetRpcImpl.class);
    }
    
    public void setBillingDetService(BillingDetService billingDetService) {
        this.billingDetService = billingDetService;
    } 
    
    //--------------------------------------------------------------------------------------------------------------
    @Override
    public Integer getBillingDetCount() throws ServiceException {
        return billingDetService.getBillingDetCount();
    }
    
    @Override
    public ArrayList<BillingDet> getBillingDets(int startRow, int pageSize) throws ServiceException {
        return billingDetService.getBillingDets(startRow, pageSize);
    }
    
    @Override
    public ArrayList<BillingDet> getBillingDets() throws ServiceException {
        return billingDetService.getBillingDets();
    }
    
    @Override
    public ArrayList<BillingDetAppliesDto> getBillingDetAppliesDtoList() throws ServiceException {
        return billingDetService.getBillingDetAppliesDtoList();
    }
    
    @Override
    public List<BillingDet> getActiveBillingDets() throws ServiceException {
        return billingDetService.getActiveBillingDets();
    }
    
    @Override
    public ArrayList<BillingDet> getSelectedBillingDets(ArrayList<Long> selectedIds) throws ServiceException{
        return billingDetService.getSelectedBillingDets(selectedIds);
    }
    
    @Override
    public ArrayList<BillingDetAppliesDto> saveBillingDet(BillingDetAppliesDto billingDetDto)
            throws ServiceException, AccessControlException, ValidationException {
        ServerValidatorUtil.getInstance().validateDataForValidationMessages(billingDetDto);
        ArrayList<BillingDet> billingDets = billingDetService.getBillingDets();
        if (billingDetDto.getId() == null) {
            for (BillingDet billingDetTemp : billingDets) {
                if (billingDetDto.getName().equals(billingDetTemp.getName())) {
                    throw new ValidationException(new ValidationMessage("billingdet.error.save.duplicate", true));
                }
            }
        }
        return billingDetService.saveBillingDet(billingDetDto);
    }
    
    public List<BillingDetPrimaryDto> getBillingDeterminantsWithCharges() throws ServiceException {
        return billingDetService.getBillingDeterminants();
    }
}
