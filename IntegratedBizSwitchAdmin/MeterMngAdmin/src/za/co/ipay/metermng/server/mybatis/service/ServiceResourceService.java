package za.co.ipay.metermng.server.mybatis.service;

import java.util.List;

import org.springframework.transaction.annotation.Transactional;

import za.co.ipay.metermng.datatypes.RecordStatus;
import za.co.ipay.metermng.mybatis.generated.mapper.ServiceResourceMapper;
import za.co.ipay.metermng.mybatis.generated.model.ServiceResource;
import za.co.ipay.metermng.mybatis.generated.model.ServiceResourceExample;

public class ServiceResourceService {

    private ServiceResourceMapper serviceResourceMapper;
    
    @Transactional(readOnly=true)
    public ServiceResource getServiceResource(Long id) {
        if (id != null) {
            return serviceResourceMapper.selectByPrimaryKey(id);
        } else {
            return null;
        }
    }

    @Transactional(readOnly=true)
    public List<ServiceResource> getServiceResources(Boolean active) {
        ServiceResourceExample example = new ServiceResourceExample();
        if (active != null) {
            if (active) {
                example.createCriteria().andRecordStatusEqualTo(RecordStatus.ACT);
            } else {
                example.createCriteria().andRecordStatusEqualTo(RecordStatus.DAC);
            }
        } else {
            example.createCriteria().andRecordStatusNotEqualTo(RecordStatus.DEL);
        }
        example.setOrderByClause("resource_name");
        return serviceResourceMapper.selectByExample(example);
    }
    
    
    public void setServiceResourceMapper(ServiceResourceMapper serviceResourceMapper) {
        this.serviceResourceMapper = serviceResourceMapper;
    }
}
