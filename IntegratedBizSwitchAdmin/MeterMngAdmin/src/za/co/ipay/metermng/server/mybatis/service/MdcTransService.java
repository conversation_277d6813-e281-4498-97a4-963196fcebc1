package za.co.ipay.metermng.server.mybatis.service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

import org.springframework.transaction.annotation.Transactional;

import za.co.ipay.metermng.mybatis.generated.mapper.CustomerAgreementMapper;
import za.co.ipay.metermng.mybatis.generated.mapper.CustomerMapper;
import za.co.ipay.metermng.mybatis.generated.mapper.MdcTransMapper;
import za.co.ipay.metermng.mybatis.generated.mapper.MeterMapper;
import za.co.ipay.metermng.mybatis.generated.mapper.UsagePointMapper;
import za.co.ipay.metermng.mybatis.generated.model.MdcTrans;
import za.co.ipay.metermng.mybatis.generated.model.MdcTransExample;
import za.co.ipay.metermng.shared.MdcTransData;
import za.co.ipay.metermng.shared.dto.CustomerData;

public class MdcTransService {

    private MdcTransMapper mdcTransMapper;
    private MeterMapper meterMapper;
    private UsagePointMapper usagePointMapper;
    private CustomerAgreementMapper customerAgreementMapper;
    private CustomerMapper customerMapper;
    
    @Transactional(readOnly=true)
    public ArrayList<MdcTransData> getMdcTransByMeterId(Long meterId) {
        MdcTransExample example = new MdcTransExample();
        example.createCriteria().andMeterIdEqualTo(meterId);
        example.setOrderByClause("req_received desc");
        List<MdcTrans> list = mdcTransMapper.selectByExample(example);
        
        String meterNum = getMeterNum(meterId);
        HashMap<Long, String> usagePointNamesMap = new HashMap<Long, String>();
        HashMap<Long, String> customerNamesMap = new HashMap<Long, String>();    //<customerAgreementId, customerName>
        
        ArrayList<MdcTransData> mdcTransList = new ArrayList<MdcTransData>();
        for (MdcTrans mt : list) {
            MdcTransData mtd = new MdcTransData(mt);
            mtd.setMeterNum(meterNum);
            
            if (mt.getUsagePointId() != null) {
                Long id = mt.getUsagePointId();
                if (usagePointNamesMap.containsKey(id)) {
                    mtd.setUsagePointName(usagePointNamesMap.get(id));
                } else {
                    String nme = getUsagePointName(id);
                    usagePointNamesMap.put(id, nme);
                    mtd.setUsagePointName(nme);
                }
            } else {
                mtd.setUsagePointName("");
            }
            
            if (mt.getCustomerAgreementId() != null) {
                Long id = mt.getCustomerAgreementId();
                if (customerNamesMap.containsKey(id)) {
                    mtd.setCustName(customerNamesMap.get(id));
                } else {
                    String nme = getCustomerName(id);
                    customerNamesMap.put(id, nme);
                    mtd.setCustName(nme);
                }
            } else {
                mtd.setCustName("");
            }
            
            mdcTransList.add(mtd);
        }
        return mdcTransList;
    }

    @Transactional(readOnly=true)
    public ArrayList<MdcTransData> getMdcTransByUsagePoint(Long usagePointId) {
        MdcTransExample example = new MdcTransExample();
        example.createCriteria().andUsagePointIdEqualTo(usagePointId);
        example.setOrderByClause("req_received desc");
        List<MdcTrans> list = mdcTransMapper.selectByExample(example);
        
        String upName = getUsagePointName(usagePointId);
        HashMap<Long, String> meterNumMap = new HashMap<Long, String>();
        HashMap<Long, String> customerNamesMap = new HashMap<Long, String>();    //<customerAgreementId, customerName>
        
        ArrayList<MdcTransData> mdcTransList = new ArrayList<MdcTransData>();
        for (MdcTrans mt : list) {
            MdcTransData mtd = new MdcTransData(mt);
            
            if (mt.getMeterId() != null) {
                Long id = mt.getMeterId();
                if (meterNumMap.containsKey(id)) {
                    mtd.setMeterNum(meterNumMap.get(id));
                } else {
                    String num = getMeterNum(id);
                    meterNumMap.put(id, num);
                    mtd.setMeterNum(num);
                }
            } else {
                mtd.setMeterNum("");
            }
 
            mtd.setUsagePointName(upName);
            
            if (mt.getCustomerAgreementId() != null) {
                Long id = mt.getCustomerAgreementId();
                if (customerNamesMap.containsKey(id)) {
                    mtd.setCustName(customerNamesMap.get(id));
                } else {
                    String nme = getCustomerName(id);
                    customerNamesMap.put(id, nme);
                    mtd.setCustName(nme);
                }
            } else {
                mtd.setCustName("");
            }
            
            mdcTransList.add(mtd);
        }
        return mdcTransList;
    }
    
    private String getUsagePointName(Long usagePointId) {
        return usagePointMapper.selectByPrimaryKey(usagePointId).getName();
    }
    private String getCustomerName(Long customerAgreementId) {
        Long custid = (customerAgreementMapper.selectByPrimaryKey(customerAgreementId)).getCustomerId();
        if (custid != null) {
            return(new CustomerData(customerMapper.selectByPrimaryKey(custid))).getName();
        } else {
            return "";
        }
    }
    private String getMeterNum(long meterId) {
        return meterMapper.selectByPrimaryKey(meterId).getMeterNum();
    }
 
    @Transactional(readOnly=true)
    public MdcTrans getMdcTransByReference(String reference) {
        MdcTransExample example = new MdcTransExample();
        example.createCriteria().andRefReceivedEqualTo(reference);
        List<MdcTrans> list = mdcTransMapper.selectByExample(example);
        if (list.isEmpty()) {
            return null;
        } else {
            return list.get(0);
        }
    }
    
    public void setMdcTransMapper(MdcTransMapper mdcTransMapper) {
        this.mdcTransMapper = mdcTransMapper;
    }

    public void setMeterMapper(MeterMapper meterMapper) {
        this.meterMapper = meterMapper;
    }

    public void setUsagePointMapper(UsagePointMapper usagePointMapper) {
        this.usagePointMapper = usagePointMapper;
    }

    public void setCustomerAgreementMapper(CustomerAgreementMapper customerAgreementMapper) {
        this.customerAgreementMapper = customerAgreementMapper;
    }

    public void setCustomerMapper(CustomerMapper customerMapper) {
        this.customerMapper = customerMapper;
    }
    
}
