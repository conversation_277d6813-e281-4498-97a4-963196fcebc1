package za.co.ipay.metermng.server.bulkupload;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;

import org.apache.commons.beanutils.BeanUtils;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.support.DefaultMessageSourceResolvable;
import org.springframework.stereotype.Controller;

import za.co.ipay.gwt.common.shared.exception.ValidationException;
import za.co.ipay.gwt.common.shared.validation.ValidateUtil;
import za.co.ipay.metermng.cim.MridUtil;
import za.co.ipay.metermng.datatypes.RecordStatus;
import za.co.ipay.metermng.integration.offlinebulkuploads.metercustup.CustomFieldsUtil;
import za.co.ipay.metermng.mybatis.generated.mapper.EndDeviceStoreMapper;
import za.co.ipay.metermng.mybatis.generated.mapper.StsAlgorithmMapper;
import za.co.ipay.metermng.mybatis.generated.mapper.StsSupplyGroupMapper;
import za.co.ipay.metermng.mybatis.generated.mapper.StsTokenTechMapper;
import za.co.ipay.metermng.mybatis.generated.model.AppSetting;
import za.co.ipay.metermng.mybatis.generated.model.EndDeviceStore;
import za.co.ipay.metermng.mybatis.generated.model.EndDeviceStoreExample;
import za.co.ipay.metermng.mybatis.generated.model.FormFields;
import za.co.ipay.metermng.mybatis.generated.model.Meter;
import za.co.ipay.metermng.mybatis.generated.model.MeterModel;
import za.co.ipay.metermng.mybatis.generated.model.MeterType;
import za.co.ipay.metermng.mybatis.generated.model.StsAlgorithm;
import za.co.ipay.metermng.mybatis.generated.model.StsAlgorithmExample;
import za.co.ipay.metermng.mybatis.generated.model.StsMeter;
import za.co.ipay.metermng.mybatis.generated.model.StsSupplyGroup;
import za.co.ipay.metermng.mybatis.generated.model.StsSupplyGroupExample;
import za.co.ipay.metermng.mybatis.generated.model.StsTokenTech;
import za.co.ipay.metermng.mybatis.generated.model.StsTokenTechExample;
import za.co.ipay.metermng.server.mybatis.service.AppSettingService;
import za.co.ipay.metermng.server.mybatis.service.MeterModelService;
import za.co.ipay.metermng.server.mybatis.service.MeterService;
import za.co.ipay.metermng.server.mybatis.service.MeterTypeService;
import za.co.ipay.metermng.server.mybatis.service.UserInterfaceService;
import za.co.ipay.metermng.server.validation.ServerValidatorUtil;
import za.co.ipay.metermng.shared.MeterMngStatics;
import za.co.ipay.metermng.shared.appsettings.AppSettings;
import za.co.ipay.metermng.shared.appsettings.CustomFieldDto;
import za.co.ipay.metermng.shared.appsettings.PowerLimitValue;
import za.co.ipay.metermng.shared.bulkupload.dto.metercustupuploaddata.MeterBulkCsvData;
import za.co.ipay.metermng.shared.dto.MeterData;
import za.co.ipay.metermng.shared.userinterface.UserInterfaceFormFields;
import za.co.ipay.metermng.shared.utils.MeterMngCommonUtil;

/**
 * This is the super class for all uploads that involve Meters (MeterBulkUpload
 * & MeterCustomerUsagePointUpload). It only handles Meter data,(i.e. data
 * validation, meter objects creation & meter util functions). Other func
 * specific to the upload are handled by the relative sub-class.
 */
@Controller
public abstract class MeterBulkUploadCommon extends BulkUploadController {

	@Autowired
	private MeterService meterService;

	@Autowired
	private MeterTypeService meterTypeService;

	@Autowired
	protected MeterModelService meterModelService;

	@Autowired
	private EndDeviceStoreMapper endDeviceStoreMapper;

	@Autowired
	private StsTokenTechMapper stsTokenTechMapper;

	@Autowired
	private StsAlgorithmMapper stsAlgorithmMapper;

	@Autowired
	private StsSupplyGroupMapper stsSupplyGroupMapper;

    @Autowired
    private AppSettingService appSettingService;

    @Autowired
    UserInterfaceService userInterfaceService;

	private Logger logger = Logger.getLogger(MeterBulkUploadCommon.class.getName());
	private DateFormat dateFormatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

	protected ArrayList<String> meterNumList;
	private ArrayList<String> meterMridList;
	private ArrayList<MeterType> meterTypeList;
	private ArrayList<MeterModel> meterModelList;
	private ArrayList<EndDeviceStore> endDeviceStoreList;
	private ArrayList<StsTokenTech> stsTokenTechCodeList;
	private ArrayList<StsAlgorithm> stsAlgorithmCodeList;
	private ArrayList<StsSupplyGroup> stsSupplyGroupCodeList;
	private List<PowerLimitValue> powerLimitList;
	protected List<CustomFieldDto> meterCustomFieldDtoList;
	protected String customFieldStatusRequired;

	private Long currentMetermodelId;
	private boolean isMeterOnlyUpload;
    Map<String, FormFields> formFieldsMap;

	// VALIDATE METER DATA
	public StringBuilder validateMeterData(StringBuilder validationError, MeterBulkCsvData meterBulkCsvData) {
		// **** validationError.append("error-message").append(";"); *****
	    
        // MeterNum - (required, can be dup if ignoreDupsParam, ('char length','luhn check')
        if (!ValidateUtil.isNotNullOrBlank(meterBulkCsvData.getMeterNum())) {
            validationError.append(addValidationError("required", "bulk.upload.meternum"));
        } else {
            if (isDupMeter(meterBulkCsvData.getMeterNum())) {
                if (ignoreDups) {
                    validationError.append("dupIgnore");
                    return validationError;
                } else {
                    validationError.append(addValidationError("duplicate", "bulk.upload.meternum"));
                }
            }
            if (meterBulkCsvData.getMeterType() != null
                    && meterBulkCsvData.getMeterType().toLowerCase().contains("sts")) {
                // Proprietary meter workaround using alg code 89, does not validate them as
                // they do not conform to STS std
                if(! "89".equals(meterBulkCsvData.getStsAlgorithmCode())) {
                    if (!MeterMngCommonUtil.stsLengthCheck(meterBulkCsvData.getMeterNum())) {
                        validationError.append("meter.sts.length").append(";");
                    } else if (!MeterMngCommonUtil.luhnCheck(meterBulkCsvData.getMeterNum())) {
                        validationError.append("meter.luhncheck.failed").append(";");
                    }
                }
            }
            meterNumList.add(meterBulkCsvData.getMeterNum());
        }

		// MeterType - (required, nonexisting)
		Long currentMeterTypeId = null;
		if (!ValidateUtil.isNotNullOrBlank(meterBulkCsvData.getMeterType())) {
			validationError.append(addValidationError("required", "bulk.upload.metertype"));
		} else {
			currentMeterTypeId = getMeterTypeId(meterBulkCsvData.getMeterType());
			if (currentMeterTypeId == null) {
				validationError.append(addValidationError("nonexisting", "bulk.upload.metertype"));
			}
		}

		// MeterModel - (required, nonexisting, 'does not match metertype')
		MeterModel currentMeterModel = null;
		Long currentMeterModelId = null;
		if (!ValidateUtil.isNotNullOrBlank(meterBulkCsvData.getMeterModelName())) {
			validationError.append(addValidationError("required", "bulk.upload.metermodelname"));
		} else {
			currentMeterModel = getMeterModel(meterBulkCsvData.getMeterModelName());
			if (currentMeterModel == null) {
				validationError.append(addValidationError("nonexisting", "bulk.upload.metermodelname"));
			} else if (!currentMeterModel.getMeterTypeId().equals(currentMeterTypeId)) {
				validationError.append("bulk.upload.metertype.incompatible.metermodel").append(";");
			} else {
				currentMeterModelId = currentMeterModel.getId();
			}
		}
		setCurrentMetermodelId(currentMeterModelId);

		// Meter URI fields - (if present for MeterModel)
		if (currentMeterModel != null) {
			if (currentMeterModel.isUriPresent()) {
				if (MeterMngCommonUtil.isNotNullOrBlank(meterBulkCsvData.getMeterUriPort())) {
					if (!MeterMngCommonUtil.isValidPort(meterBulkCsvData.getMeterUriPort())) {
						validationError.append(addValidationError("invalid", "meter.uri.port.error"));
					}
				}
			} else {
				if (MeterMngCommonUtil.isNotNullOrBlank(meterBulkCsvData.getMeterUriAddress())) {
					validationError.append("bulk.upload.meter.uri.not.present.address.error").append(";");
				}
				if (MeterMngCommonUtil.isNotNullOrBlank(meterBulkCsvData.getMeterUriPort())) {
					validationError.append("bulk.upload.meter.uri.not.present.port.error").append(";");
				}
				if (MeterMngCommonUtil.isNotNullOrBlank(meterBulkCsvData.getMeterUriProtocol())) {
					validationError.append("bulk.upload.meter.uri.not.present.protocol.error").append(";");
				}
				if (MeterMngCommonUtil.isNotNullOrBlank(meterBulkCsvData.getMeterUriParams())) {
					validationError.append("bulk.upload.meter.uri.not.present.params.error").append(";");
				}
			}
		}

        if (isMeterOnlyUpload && !formFieldsMap.get(UserInterfaceFormFields.SERIAL_NUMBER).isDisplay()) {
            meterBulkCsvData.setSerialNum(null);
        } else {
            validateOptionalField(validationError, "serialnum", meterBulkCsvData.getSerialNum(),
                    UserInterfaceFormFields.SERIAL_NUMBER);
        }

        // MeterMrid - (noduplicate, ('char length')
        if (ValidateUtil.isNotNullOrBlank(meterBulkCsvData.getMeterMrid())) {
        	if (meterMridList.contains(meterBulkCsvData.getMeterMrid()) || meterService.getMeterByMrid(meterBulkCsvData.getMeterMrid()) != null) {
                validationError.append(addValidationError("duplicate", "bulk.upload.mrid"));
            }
        	meterMridList.add(meterBulkCsvData.getMeterMrid());
        }

		// BreakerId - ('if MeterModel requires it')
		if (currentMeterModel != null && currentMeterModel.isNeedsBreakerId()
				&& !ValidateUtil.isNotNullOrBlank(meterBulkCsvData.getBreakerId())) {
			validationError.append("meter.breakerid.error").append(";");
		}

		// encKey - ('if MeterModel requires it')
		if (currentMeterModel != null && currentMeterModel.isNeedsEncKey()
				&& !ValidateUtil.isNotNullOrBlank(meterBulkCsvData.getEncKey())) {
			validationError.append("meter.enc.key.error").append(";");
		}

		// EndDeviceStore : only for meter-only uploads - (required, nonexistsing)
		Long currentEndDeviceStoreId = null;
		Long endDeviceStoreAccessGroupId = null;
		if (isMeterOnlyUpload) {
			EndDeviceStore currentEndDeviceStore = null;
			if (!ValidateUtil.isNotNullOrBlank(meterBulkCsvData.getEndDeviceStoreName())) {
				validationError.append(addValidationError("required", "bulk.upload.meterupload.enddevicestorename"));
			} else {
				currentEndDeviceStore = getEndDeviceStore(meterBulkCsvData.getEndDeviceStoreName());
				if (currentEndDeviceStore == null) {
					validationError
							.append(addValidationError("nonexisting", "bulk.upload.meterupload.enddevicestorename"));
				} else {
					currentEndDeviceStoreId = currentEndDeviceStore.getId();
					endDeviceStoreAccessGroupId = currentEndDeviceStore.getAccessGroupId();
				}
			}
		}

		//Powerlimit settings
		PowerLimitValue meterPowerLimit = null;
		if (ValidateUtil.isNotNullOrBlank(meterBulkCsvData.getPowerLimit())) {
		    if (powerLimitList.isEmpty()) {
		        validationError.append("meter.powerlimit.key.error.not.configured").append(";");
		    } else {
                boolean valid = true;
		        Integer powerLimit = null;
		        try {
		            powerLimit = Integer.valueOf(meterBulkCsvData.getPowerLimit());
		        } catch (NumberFormatException nfe) {
		            validationError.append("meter.powerlimit.key.error.integer").append(";");
		            valid = false;
		        }

		        if (valid) {
		            valid = false;
		            for (PowerLimitValue plv : powerLimitList) {
		                if (powerLimit.equals(plv.getValue())) {
		                    meterPowerLimit = plv;
		                    valid = true;
		                    break;
		                }
		            }
		            if (!valid) {
		                validationError.append("meter.powerlimit.key.error.invalid").append(";");
		            }
		        }
		    }
		}

		String customFieldsValidation = CustomFieldsUtil.validateMeterCustomFields(meterCustomFieldDtoList, meterBulkCsvData, dateFormatter, customFieldStatusRequired);
		validationError.append(customFieldsValidation);

		// create Meter Object & run it through validator to pick up null & max errors
		Meter meter = createMeter(currentMeterTypeId, currentMeterModelId, meterPowerLimit, currentEndDeviceStoreId, endDeviceStoreAccessGroupId, meterBulkCsvData);
		try {
			ServerValidatorUtil.getInstance().validateDataForValidationMessages(meter);
		} catch (ValidationException e) {
			logger.debug("MeterBulkUpload Meter Exception = " + e);
			for (String key : e.getFieldErrorMessages().keySet()) {
				if (key.equals("meterTypeId") || key.equals("meterModelId") || (key.equals("meterNum")
						&& e.getFieldErrorMessages().get(key).toString().contains("required"))) {
					continue;
				}
				validationError.append("NOTKEY").append(e.getFieldErrorMessages().get(key).getMessage()).append(";");
			}
		}

		// -----------------------------------------------------------------------------------------------------------------------
		// VALIDATIONS - STS METER : STS Specific Fields - REQUIRED for STS
		if (meterBulkCsvData.getMeterType() != null) {

			if (meterBulkCsvData.getMeterType().toLowerCase().contains("sts")) {
				// StsTokenTechCode - (required, nonexistsing)
				if (!ValidateUtil.isNotNullOrBlank(meterBulkCsvData.getStsTokenTechCode())) {
					validationError.append(addValidationError("required", "bulk.upload.ststokentechcode"));
				} else if (getTokenTechCodeId(meterBulkCsvData.getStsTokenTechCode()) == null) {
					validationError.append(addValidationError("nonexisting", "bulk.upload.ststokentechcode"));
				}

				// StsAlgorithmCode - (required, nonexistsing)
				if (!ValidateUtil.isNotNullOrBlank(meterBulkCsvData.getStsAlgorithmCode())) {
					validationError.append(addValidationError("required", "bulk.upload.stsalgorithmcode"));
				} else if (getAlgorithmCodeId(meterBulkCsvData.getStsAlgorithmCode()) == null) {
					validationError.append(addValidationError("nonexisting", "bulk.upload.stsalgorithmcode"));
				}

				// StsSupplyGroupCode - (required)
				if (!ValidateUtil.isNotNullOrBlank(meterBulkCsvData.getStsSupplyGroupCode())) {
					validationError.append(addValidationError("required", "bulk.upload.stssupplygroupcode"));
				}

				// StsKeyRevisionNum - (required, 'Must be Integer')
				Integer stsKeyRevInteger = null;
				if (!ValidateUtil.isNotNullOrBlank(meterBulkCsvData.getStsKeyRevisionNum())) {
					validationError.append(addValidationError("required", "bulk.upload.stskeyrevisionnum"));
				} else {
					try {
						stsKeyRevInteger = Integer.parseInt(meterBulkCsvData.getStsKeyRevisionNum());
					} catch (NumberFormatException n) {
						validationError.append(addValidationError("invalid", "bulk.upload.stskeyrevisionnum"));
					}
				}

				// StsSupplyGroupCode + StsKeyRevisionNum combo
				if (!isValidSupplyGroupAndKeyRevCombo(meterBulkCsvData.getStsSupplyGroupCode(), stsKeyRevInteger)) {
					validationError.append("bulk.upload.invalid.nonexisting.field").append("_")
							.append(importMessageSource.getMessage(
									new DefaultMessageSourceResolvable("bulk.upload.stssupplygroupcode"), null))
							.append(" + ")
							.append(importMessageSource.getMessage(
									new DefaultMessageSourceResolvable("bulk.upload.stskeyrevisionnum"), null))
							.append(";");
				}

				// StsTariffIndex (required,'Must be Integer','Max of 2 chars')
				if (!ValidateUtil.isNotNullOrBlank(meterBulkCsvData.getStsTariffIndex())) {
					validationError.append(addValidationError("required", "bulk.upload.ststariffindex"));
				} else {
					if (meterBulkCsvData.getStsTariffIndex().length() > 2) {
						validationError.append("error.field.ststariffindex.max").append("_2").append(";");
					}
				}
			} else if (meterBulkCsvData.getStsTokenTechCode() != null || meterBulkCsvData.getStsAlgorithmCode() != null
					|| meterBulkCsvData.getStsSupplyGroupCode() != null
					|| meterBulkCsvData.getStsKeyRevisionNum() != null
					|| meterBulkCsvData.getStsTariffIndex() != null) {
				validationError.append("bulk.upload.metertype.incompatible.stsinfo").append(";");
			}
		}
		// not running ServerValidator for STS_Meter... have already checked the salient fields and just one max size check above

		return validationError;
	}
	
	protected boolean isDupMeter(String meterNumber) {
	    if (meterService.getMeterByNumber(meterNumber) != null
                || meterNumList.contains(meterNumber)) {
	        return true;
	    } else {
	        return false;
	    }
	}

	protected MeterData createMeterData(MeterBulkCsvData data) {
		Long meterTypeId = getMeterTypeId(data.getMeterType());
		Long meterModelId = getMeterModel(data.getMeterModelName()).getId();

		PowerLimitValue meterPowerLimit = null;
		if (ValidateUtil.isNotNullOrBlank(data.getPowerLimit())) {
		    Integer powerLimit = null;
		    try {
		        powerLimit = Integer.valueOf(data.getPowerLimit());
		    } catch (NumberFormatException nfe) {
		        logger.warn("Bulk Upload: create MeterData - now powerlimit is invalid after passing validation step. Shouldn't happen!");
		    }

		    if (powerLimit != null) {
		        for (PowerLimitValue plv : powerLimitList) {
                    if (powerLimit.equals(plv.getValue())) {
                        meterPowerLimit = plv;
                        break;
                    }
                }
		    }
		}

		EndDeviceStore endDeviceStore = null;
		if (isMeterOnlyUpload)
		    endDeviceStore = getEndDeviceStore(data.getEndDeviceStoreName());

		MeterData meterData = createMeter(meterTypeId, meterModelId, meterPowerLimit, endDeviceStore.getId(), endDeviceStore.getAccessGroupId(), data);
		// STS
		if (data.getMeterType().toLowerCase().contains("sts")) {
		    StsMeter stsMeter = meterData.getStsMeter();
		    stsMeter.setStsTokenTechCode(make2chars(data.getStsTokenTechCode()));
		    stsMeter.setStsTokenTechId(getTokenTechCodeId(data.getStsTokenTechCode()));

		    stsMeter.setStsAlgorithmCode(make2chars(data.getStsAlgorithmCode()));
		    stsMeter.setStsAlgorithmCodeId(getAlgorithmCodeId(data.getStsAlgorithmCode()));

            String stsSupplyGroupCode = data.getStsSupplyGroupCode();
            stsMeter.setStsCurrSupplyGroupCode(stsSupplyGroupCode);
            int keyRevInteger = Integer.parseInt(data.getStsKeyRevisionNum());
            stsMeter.setStsCurrSupplyGroupId(getStsCurrSupplyGroupId(stsSupplyGroupCode, keyRevInteger));

			stsMeter.setStsCurrKeyRevisionNum(keyRevInteger);
			stsMeter.setStsCurrTariffIndex(make2chars(data.getStsTariffIndex()));
		}
		return meterData;
	}

	// Create Meter: If set the property value to null in the map, BeanUtils
	// seems to give it a default value; so just don't set it if it is null
	// incoming; the the serverValidator will trap the @NotNUll
	private MeterData createMeter(Long meterTypeId, Long meterModelId, PowerLimitValue powerLimitValue, Long currentEndDeviceStoreId, Long endDeviceStoreAccessGroupId,
			MeterBulkCsvData meterBulkCsvData) {

		MeterData meterData = new MeterData();
		HashMap<String, Object> properties = new HashMap<String, Object>();

		if (meterTypeId != null) {
			properties.put("meterTypeId", meterTypeId);
			if (meterBulkCsvData.getMeterType().toLowerCase().contains("sts")) {
				meterData.setStsMeter(new StsMeter());
			}
		}
		String mrid = meterBulkCsvData.getMeterMrid();
		if (ValidateUtil.isNotNullOrBlank(mrid)) {
			properties.put("mrid", mrid);
			properties.put("mridExternal", true);
		} else {
			mrid = MridUtil.getMrid();
			properties.put("mrid", mrid);
			properties.put("mridExternal", false);
		}

		if (ValidateUtil.isNotNullOrBlank(meterBulkCsvData.getSerialNum())) {
			properties.put("serialNum", meterBulkCsvData.getSerialNum());
		}
		if (ValidateUtil.isNotNullOrBlank(meterBulkCsvData.getMeterNum())) {
			properties.put("meterNum", meterBulkCsvData.getMeterNum());
		}
		properties.put("recordStatus", RecordStatus.ACT);
		if (meterModelId != null) {
			properties.put("meterModelId", meterModelId);
		}
		if (ValidateUtil.isNotNullOrBlank(meterBulkCsvData.getBreakerId())) {
			properties.put("breakerId", meterBulkCsvData.getBreakerId());
		}
		if (ValidateUtil.isNotNullOrBlank(meterBulkCsvData.getEncKey())) {
			properties.put("encKey", meterBulkCsvData.getEncKey());
		}
		if (ValidateUtil.isNotNullOrBlank(meterBulkCsvData.getMeterUriAddress())) {
			properties.put("meterUriAddress", meterBulkCsvData.getMeterUriAddress());
		}
		if (ValidateUtil.isNotNullOrBlank(meterBulkCsvData.getMeterUriPort())) {
			properties.put("meterUriPort", meterBulkCsvData.getMeterUriPort());
		}
		if (ValidateUtil.isNotNullOrBlank(meterBulkCsvData.getMeterUriProtocol())) {
			properties.put("meterUriProtocol", meterBulkCsvData.getMeterUriProtocol());
		}
		if (ValidateUtil.isNotNullOrBlank(meterBulkCsvData.getMeterUriParams())) {
			properties.put("meterUriParams", meterBulkCsvData.getMeterUriParams());
		}
        if (powerLimitValue != null) {
            properties.put("powerLimitLabel", powerLimitValue.getLabel());
            properties.put("powerLimit", powerLimitValue.getValue());
        }
		properties.putAll(CustomFieldsUtil.populateCustomMeterData(meterCustomFieldDtoList, meterBulkCsvData, dateFormatter));

		if (isMeterOnlyUpload) {
			properties.put("endDeviceStoreId", currentEndDeviceStoreId);
			if (endDeviceStoreAccessGroupId != null) {
		         properties.put("accessGroupId", endDeviceStoreAccessGroupId);
			}
		}

		try {
			BeanUtils.populate(meterData, properties);
		} catch (Exception e) {
			logger.debug("MeterBulkUploadController: createMeter: Beanutils failed!! exception = " + e.getMessage());
			return null;
		}
		logger.debug("new Meter: " + meterData.getMeterNum() + "   meterTypeId = " + meterData.getMeterTypeId()+ "  mrid=" + mrid + "  STSMeter=" + meterData.getStsMeter());
		return meterData;
	}

	// Load common lists from the DB. This values are re/set per upload.
	public void checkPreLoadedTables(Long currentGroupId) {

		meterNumList = new ArrayList<String>();// Maintains Unique Meter numbers.
		meterMridList = new ArrayList<String>();// Maintains Unique Meter mrid.
		meterTypeList = new ArrayList<MeterType>();
		meterTypeList = (ArrayList<MeterType>) meterTypeService.getMeterTypes(true);
		meterModelList = meterModelService.getAvailableMeterModels();

		if (isMeterOnlyUpload) {
			endDeviceStoreList = new ArrayList<EndDeviceStore>();
			EndDeviceStoreExample endDeviceStoreExample = new EndDeviceStoreExample();
			endDeviceStoreExample.createCriteria().andGenGroupIdEqualTo(currentGroupId)
					.andRecordStatusEqualTo(RecordStatus.ACT);
			endDeviceStoreExample.setOrderByClause("store_name");
			endDeviceStoreList = (ArrayList<EndDeviceStore>) endDeviceStoreMapper
					.selectByExample(endDeviceStoreExample);
		}

		stsTokenTechCodeList = new ArrayList<StsTokenTech>();
		StsTokenTechExample ststtExample = new StsTokenTechExample();
		ststtExample.createCriteria().andRecordStatusEqualTo(RecordStatus.ACT);
		ststtExample.setOrderByClause("sts_token_tech_name");
		stsTokenTechCodeList = (ArrayList<StsTokenTech>) stsTokenTechMapper.selectByExample(ststtExample);

		stsAlgorithmCodeList = new ArrayList<StsAlgorithm>();
		StsAlgorithmExample stsaExample = new StsAlgorithmExample();
		stsaExample.createCriteria().andRecordStatusEqualTo(RecordStatus.ACT);
		stsaExample.setOrderByClause("sts_algorithm_name");
		stsAlgorithmCodeList = (ArrayList<StsAlgorithm>) stsAlgorithmMapper.selectByExample(stsaExample);

		stsSupplyGroupCodeList = new ArrayList<StsSupplyGroup>();
		StsSupplyGroupExample stssgExample = new StsSupplyGroupExample();
		stssgExample.createCriteria().andRecordStatusEqualTo(RecordStatus.ACT);
		stssgExample.setOrderByClause("supply_group_name");
		stsSupplyGroupCodeList = (ArrayList<StsSupplyGroup>) stsSupplyGroupMapper.selectByExample(stssgExample);

		powerLimitList = new ArrayList<PowerLimitValue>();
		AppSetting appSetting = appSettingService.getAppSettingByKey(AppSettings.POWER_LIMIT_SETTINGS);
		if (appSetting != null) {
		    powerLimitList = PowerLimitValue.fromAppSetting(appSetting);
		}
	}

	private Long getMeterTypeId(String meterTypeName) {
		for (MeterType mt : meterTypeList) {
			if (mt.getName().equals(meterTypeName)) {
				return mt.getId();
			}
		}
		return null;
	}

	private MeterModel getMeterModel(String meterModelName) {
		for (MeterModel mm : meterModelList) {
			if (mm.getName().equals(meterModelName)) {
				return mm;
			}
		}
		return null;
	}

	private EndDeviceStore getEndDeviceStore(String endDeviceStoreName) {
		for (EndDeviceStore eds : endDeviceStoreList) {
			if (eds.getName().equals(endDeviceStoreName)) {
				return eds;
			}
		}
		return null;
	}

	private Long getTokenTechCodeId(String ttCode) {
		if (ttCode.length() < 2) {
			ttCode = "0" + ttCode;
		}
		for (StsTokenTech tt : stsTokenTechCodeList) {
			if (tt.getStsTokenTechCode().equals(ttCode)) {
				return tt.getId();
			}
		}
		return null;
	}

	private Long getAlgorithmCodeId(String algCode) {
		if (algCode.length() < 2) {
			algCode = "0" + algCode;
		}
		for (StsAlgorithm sa : stsAlgorithmCodeList) {
			if (sa.getStsAlgorithmCode().equals(algCode)) {
				return sa.getId();
			}
		}
		return null;
	}

	private boolean isValidSupplyGroupAndKeyRevCombo(String supplyGroupCode, Integer keyRevNum) {
		for (StsSupplyGroup sg : stsSupplyGroupCodeList) {
			if (sg.getSupplyGroupCode().equals(supplyGroupCode) && sg.getKeyRevisionNum().equals(keyRevNum)) {
				return true;
			}
		}
		return false;
	}

    private Long getStsCurrSupplyGroupId(String supplyGroupCode, int keyRevInteger) {
		for (StsSupplyGroup ssg : stsSupplyGroupCodeList) {
            if (ssg.getSupplyGroupCode().equals(supplyGroupCode)
                    && ssg.getKeyRevisionNum().intValue() == keyRevInteger) {
				return ssg.getId();
			}
		}
		return null;
	}

	// **********************************************************************************
	// ************** Important global properties **********************************
	// MeterModelId is set per line (.csv file row)
	public Long getCurrentMetermodelId() {
		return currentMetermodelId;
	}

	public void setCurrentMetermodelId(Long currentMetermodelId) {
		this.currentMetermodelId = currentMetermodelId;
	}

	// IsMeterOnlyUpload is set per upload.
	public boolean isMeterOnlyUpload() {
		return isMeterOnlyUpload;
	}

	public void setMeterOnlyUpload(boolean isMeterOnlyUpload) {
		this.isMeterOnlyUpload = isMeterOnlyUpload;
	}
	
    void validateOptionalField(StringBuilder validationError, String fieldName, String fieldValue,
            String formFieldsValue) {
        FormFields formField = formFieldsMap.get(formFieldsValue);
        if (fieldValue == null || fieldValue.isEmpty()) {
            if (formField.isRequired()) {
                validationError.append(addValidationError("required", "bulk.upload." + fieldName));
            }
        } else {
            if (UserInterfaceFormFields.TITLE.equals(formFieldsValue)) {
                String enumeratedValuesText = formField.getEnumeratedValues();
                if (enumeratedValuesText != null) {
                    String enumeratedValues[] = enumeratedValuesText.split(",");
                    boolean invalid = true;
                    for (String enumeratedValue : enumeratedValues) {
                        if (enumeratedValue.equals(fieldValue)) {
                            invalid = false;
                            break;
                        }
                    }
                    if (invalid) {
                        validationError.append(addValidationError("invalid", "bulk.upload." + fieldName));
                    }
                }
            } else {
                String regex = formField.getValidationRegex();
                if (regex != null && !Pattern.matches(regex, fieldValue)) {
                    validationError.append(addValidationError("regex", "bulk.upload." + fieldName));
                }
            }
        }
    }

	// *************************************************************************************
	// ************** Meter Util functions  ***********************************************
	private static String make2chars(String code) {
		if (code.length() < 2) {
			return "0" + code;
		}
		return code;
	}

	// Use luhnCheck from MeterMngCommonUtil
}
