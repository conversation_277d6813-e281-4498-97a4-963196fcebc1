package za.co.ipay.metermng.server.servlet;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;

import javax.servlet.ServletConfig;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.log4j.Logger;

import za.co.ipay.metermng.shared.MeterMngStatics;

/**
 * Servlet implementation class TemplateExportServlet
 */
public class TemplateExportServlet extends SuperExportDataServlet {

    private static Logger logger = Logger.getLogger(TemplateExportServlet.class);

    @Override
    public void init(ServletConfig config) throws ServletException {
        super.init(config);
    }

    public void doGet(HttpServletRequest request, HttpServletResponse response) throws IOException, ServletException {
        doDataExport(request, response);
    }

    public void doPost(HttpServletRequest request, HttpServletResponse response) throws IOException, ServletException {
        doDataExport(request, response);
    }

    private byte[] getTemplateToExport(String fileLocation) {

        ClassLoader classLoader = getClass().getClassLoader();

        byte[] arr = null;
        try {
            File file = new File(classLoader.getResource(fileLocation).getFile());
            FileInputStream fileInputStream = new FileInputStream(file);
            arr = new byte[(int) file.length()];
            fileInputStream.read(arr);
            fileInputStream.close();
        } catch (IOException e) {
            logger.error("Error retrieving template data:", e);
        }

        return arr;
    }

    @Override
    protected void doDataExport(HttpServletRequest request, HttpServletResponse response)
            throws IOException, ServletException {
        String fileLocation = null;
        String filename = null;

        String importType = request.getParameter("import-type");
        if (importType != null) {
            switch (importType) {
            case MeterMngStatics.AUX_TRANSACTION_UPLOAD:
                filename = "TEMPLATE_AuxAccAdjustTrans.csv";
                break;
            case MeterMngStatics.AUX_ACCOUNT_UPLOAD:
                filename = "TEMPLATE_AuxiliaryAccount.csv";
                break;
            case MeterMngStatics.CUST_TRANSACTION_UPLOAD:
                filename = "TEMPLATE_CustomerBalanceAdjustTrans.csv";
                break;
            case MeterMngStatics.METER_UPLOAD:
                filename = "TEMPLATE_MetersOnly.csv";
                break;
            case MeterMngStatics.FILETYPE_REGISTER_READING_IMPORT:
                filename = "TEMPLATE_RegRead.csv";
                break;
            case MeterMngStatics.FILETYPE_PRICING_STRUCTURE_CHANGE_IMPORT:
                filename = "TEMPLATE_PricingStructureChangeImport.csv";
                break;
            case MeterMngStatics.FILETYPE_IPAY_DEBT_IMPORT:
                filename = "TEMPLATE_IPayDebtImport.csv";
                break;
            case MeterMngStatics.FILETYPE_BULK_MDC_IMPORT:
                filename = "TEMPLATE_BulkMDCMessages.csv";
                break;
            case MeterMngStatics.FILETYPE_BULK_FREE_ISSUE:
                filename = "TEMPLATE_BulkFreeIssue.csv";
                break;
            }

            if (filename != null) {
                fileLocation = "/templates/" + filename;
            }
        }

        if (fileLocation != null && filename != null) {
            byte[] output = getTemplateToExport(fileLocation);
            response.addHeader("Content-Type", "application/force-download");
            response.addHeader("Content-Disposition", "attachment; filename=\"" + filename + "\"");
            response.getOutputStream().write(output);
        } else {
            logger.error("File location or file name is null");
        }
    }

    @Override
    protected String getExportFileName(HttpServletRequest request) {
        // TODO Auto-generated method stub
        return null;
    }

}
