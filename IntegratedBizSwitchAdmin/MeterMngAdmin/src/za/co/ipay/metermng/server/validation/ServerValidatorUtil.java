package za.co.ipay.metermng.server.validation;

import javax.validation.Configuration;
import javax.validation.MessageInterpolator;
import javax.validation.Validation;
import javax.validation.ValidatorFactory;

import org.hibernate.validator.messageinterpolation.ResourceBundleMessageInterpolator;
import org.hibernate.validator.resourceloading.PlatformResourceBundleLocator;
import org.hibernate.validator.resourceloading.ResourceBundleLocator;

/**
 *  ServerValidatorUtil is a helper class used to create and control access to an EnhancedValidator that can be used
 *  on the server-side to perform validation of data instances.
 */
public class ServerValidatorUtil {
    
    //The name of the ResourceBundle used to load the i18n validation message. TODO Could pass this into the getInstance()
    private static final String MESSAGE_BUNDLE_NAME = "messages";
    // The created Validator instance which is reused by all controller, etc
    private static ServerEnhancedValidator instance;

    public static ServerEnhancedValidator getInstance() {
        if (instance == null) {
            Configuration<?> config = Validation.byDefaultProvider().configure();
            ResourceBundleLocator locator = new PlatformResourceBundleLocator(MESSAGE_BUNDLE_NAME);
            MessageInterpolator interpolator = new ResourceBundleMessageInterpolator(locator);
            config.messageInterpolator(new CustomLocaleResolverMessageInterpolator(interpolator));
            ValidatorFactory factory = config.buildValidatorFactory();            
            instance = new ServerEnhancedValidator(locator, factory.getValidator());
        }
        return instance;
    }
}
