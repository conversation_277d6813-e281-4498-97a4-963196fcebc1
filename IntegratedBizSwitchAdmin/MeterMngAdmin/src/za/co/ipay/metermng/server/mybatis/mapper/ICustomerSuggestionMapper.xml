<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="za.co.ipay.metermng.server.mybatis.mapper.ICustomerSuggestionMapper">
    <resultMap id="ExtendedResultMap" type="za.co.ipay.metermng.server.mybatis.model.CustomerWithUsagePointId" extends="za.co.ipay.metermng.mybatis.generated.mapper.CustomerMapper.BaseResultMap">
        <result column="usage_point_id" property="usagePointId" />
        <result column="usage_point_name" property="usagePointName" jdbcType="VARCHAR" />
        <result column="customer_agreement_id" property="customerAgreementId" />
        <result column="agreement_ref" property="customerAgreementRef" jdbcType="VARCHAR" />
        <result column="customer_id" property="customerId" />
        <result column="id_number" property="customerIdNumber" jdbcType="VARCHAR" />
    </resultMap>
</mapper>