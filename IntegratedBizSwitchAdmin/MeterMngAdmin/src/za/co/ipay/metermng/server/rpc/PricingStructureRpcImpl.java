package za.co.ipay.metermng.server.rpc;

import java.util.ArrayList;
import java.util.List;

import javax.validation.groups.Default;

import org.apache.log4j.Logger;

import za.co.ipay.gwt.common.shared.exception.AccessControlException;
import za.co.ipay.gwt.common.shared.exception.ServiceException;
import za.co.ipay.gwt.common.shared.exception.ValidationException;
import za.co.ipay.gwt.common.shared.exception.ValidationMessage;
import za.co.ipay.gwt.common.shared.validation.group.ClientGroup;
import za.co.ipay.gwt.common.shared.validation.group.ServerGroup;
import za.co.ipay.gwt.common.shared.validation.group.UpdateGroup;
import za.co.ipay.metermng.client.rpc.PricingStructureRpc;
import za.co.ipay.metermng.mybatis.custom.model.TariffWithCalcClass;
import za.co.ipay.metermng.mybatis.generated.model.PricingStructure;
import za.co.ipay.metermng.mybatis.generated.model.Tariff;
import za.co.ipay.metermng.mybatis.generated.model.TariffClass;
import za.co.ipay.metermng.server.mybatis.service.PricingStructureService;
import za.co.ipay.metermng.server.validation.ServerValidatorUtil;
import za.co.ipay.metermng.shared.ChannelCompatibilityE;
import za.co.ipay.metermng.shared.IpayResponseData;
import za.co.ipay.metermng.shared.dto.PSDto;
import za.co.ipay.metermng.shared.dto.pricing.PricingStructureDto;
import za.co.ipay.metermng.shared.dto.tariff.TariffPanelDto;
import za.co.ipay.metermng.shared.dto.user.MeterMngUser;
import za.co.ipay.metermng.shared.tariff.ITariffInitData;
import za.co.ipay.metermng.shared.tariff.TariffWithData;

public class PricingStructureRpcImpl extends BaseMeterMngRpc implements PricingStructureRpc {

    private static final long serialVersionUID = 1L;

    private PricingStructureService pricingStructureService;

    public PricingStructureRpcImpl() {
        logger = Logger.getLogger(PricingStructureRpcImpl.class);
    }

    public void setPricingStructureService(PricingStructureService pricingStructureService) {
        this.pricingStructureService = pricingStructureService;
    }

    @Override
    public String getAllPricingStructuresWithCurrentTariff(Long genGroupId, List<Long> pricingStructureIds) {
        return pricingStructureService.getAllPricingStructuresWithCurrentTariff(genGroupId, pricingStructureIds);
    }

    @Override
    public ArrayList<TariffClass> getTariffClasses(Long serviceResourceId, Long meterTypeId, Long paymentModeId) {
        return pricingStructureService.getTariffClasses(serviceResourceId, meterTypeId, paymentModeId);
    }

    @Override
    public List<TariffWithData> getTariffsForPricingStructure(PricingStructure pricingStructure) {
        return pricingStructureService.getTariffsForPricingStructure(pricingStructure);
    }

    @Override
    public List<Tariff> getTariffsForPricingStructure(Long pricingStructureId) throws AccessControlException {
        return pricingStructureService.selectTariffsForPricingStructure(pricingStructureId);
    }

    @Override
    public TariffPanelDto getCurrentTariffDtoByPricingStructureId(Long pricingStructureId) throws AccessControlException {
        return pricingStructureService.getCurrentTariffDtoByPricingStructureId(pricingStructureId);
    }

    @Override
    public PricingStructure savePricingStructure(PricingStructure pricingStructure) throws ValidationException, ServiceException, AccessControlException {
        ServerValidatorUtil.getInstance().validateNotNull(pricingStructure, "pricingstructure.title");
        MeterMngUser user = getUser();
        pricingStructure.setGenGroupId(user.getCurrentGroupId());
        if (pricingStructure.getId() == null) {
            ServerValidatorUtil.getInstance().validateDataForValidationMessages(pricingStructure);
        } else {
            ServerValidatorUtil.getInstance().validateDataForValidationMessages(pricingStructure, false, Default.class, UpdateGroup.class, ClientGroup.class, ServerGroup.class);
        }
        if (pricingStructureService.getMridExistence(pricingStructure.getMrid(), pricingStructure.getId())) {
            throw new ValidationException(new ValidationMessage("pricing.structure.mrid.external.unique.validation", true));
        }
        return pricingStructureService.savePricingStructure(pricingStructure);
    }

    @Override
    public ChannelCompatibilityE checkRegReadTariffActiveUpBillingDetAlignment(TariffWithData tariffWithData, String userName) throws ServiceException {
        return pricingStructureService.checkRegReadTariffActiveUpBillingDetAlignment(tariffWithData, userName, getUser().getCurrentGroupId());
    }

    @Override
    public void saveTariff(TariffWithData tariffWithData) throws ValidationException, ServiceException, AccessControlException {
        ServerValidatorUtil.getInstance().validateDataForValidationMessages(tariffWithData.getTariff());
        if (tariffWithData.getTariffData() != null) {
            ServerValidatorUtil.getInstance().validateDataForValidationMessages(tariffWithData.getTariffData());
        }
        pricingStructureService.saveTariff(tariffWithData);
    }

    @Override
    public ITariffInitData getTariffInitData(PricingStructure pricingStructure, Long tariffClassId, Tariff tariff) throws ValidationException, ServiceException, AccessControlException {
        return pricingStructureService.getTariffInitData(pricingStructure, tariffClassId, tariff);
    }

    @Override
    public PricingStructure getPricingStructure(Long pricingStructureId) throws ServiceException, AccessControlException {
        PricingStructure ps = pricingStructureService.getPricingStructure(pricingStructureId);
        return ps;
    }

    @Override
    public IpayResponseData sendTariffCalculationMsg(String mrid)  throws ServiceException {
        try {
            return pricingStructureService.sendTariffCalculation(mrid);
        } catch (Exception e) {
            return null;
        }
    }

    @Override
    public List<Long> getPricingStructuresIdsForFilters(int filterTableColIndx, String filterString) {
        return pricingStructureService.getPricingStructuresIdsForFilters(getUser().getCurrentGroupId(),
                filterTableColIndx, filterString);
    }

    @Override
    public ArrayList<PricingStructureDto> getPricingStructures(int startRow, int pageSize, String sortField, int filterTableColIndx,
			String filterString, boolean isAscending,PricingStructureDto lastAdded) throws ServiceException {
        return pricingStructureService.getPricingStructures(startRow, pageSize,sortField, filterTableColIndx, filterString, isAscending, getUser().getCurrentGroupId(), lastAdded);
    }

    @Override
    public ArrayList<PricingStructure> getPricingStructuresByCalendarId(Long touCalendarId) throws ServiceException {
        return pricingStructureService.getPricingStructuresByTouCalendarId(touCalendarId);
    }

    @Override
    public TariffWithCalcClass getCurrentOrFirstTariffByPricingStructure(Long pricingStructureId) throws AccessControlException {
        return pricingStructureService.getCurrentOrFirstTariffByPricingStructureId(pricingStructureId);
    }

    @Override
    public List<ChannelCompatibilityE> isregReadPsSameBillingDetsAsMeterModel(List<PSDto> pricingStructureDtos, Long mdcId, String userName)  throws ServiceException {
        return pricingStructureService.isregReadPsSameBillingDetsAsMeterModel(pricingStructureDtos, mdcId, userName);
    }

}
