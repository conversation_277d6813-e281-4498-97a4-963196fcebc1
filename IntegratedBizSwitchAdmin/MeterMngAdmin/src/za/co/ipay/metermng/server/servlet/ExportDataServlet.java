package za.co.ipay.metermng.server.servlet;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.security.AccessControlException;
import java.util.Date;

import javax.servlet.ServletConfig;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.log4j.Logger;
import org.springframework.context.ApplicationContext;
import org.springframework.context.support.DefaultMessageSourceResolvable;
import org.springframework.web.context.WebApplicationContext;

import za.co.ipay.gwt.common.shared.exception.ServiceException;
import za.co.ipay.metermng.mybatis.generated.model.MeterReadingType;
import za.co.ipay.metermng.server.mybatis.service.MeterService;
import za.co.ipay.metermng.shared.dto.meter.MeterReadingsDto;
import za.co.ipay.metermng.shared.dto.user.MeterMngUser;
import za.co.ipay.utils.files.CsvWriter;

/**
 * ExportDataServlet is used to export specific data using a file so that the user can save the file for off line use.
 * Currently it only supports CSV file format.
 * 
 * <AUTHOR>
 */
public class ExportDataServlet extends SuperExportDataServlet {

    private static final long serialVersionUID = 1L;

    private MeterService meterService;
    
    private static Logger logger = Logger.getLogger(ExportDataServlet.class);
    
    @Override
    public void init(ServletConfig config) throws ServletException {
       super.init(config);
       ApplicationContext ac = (ApplicationContext) config.getServletContext().getAttribute(WebApplicationContext.ROOT_WEB_APPLICATION_CONTEXT_ATTRIBUTE);
       this.meterService = (MeterService) ac.getBean("meterService");
       if (meterService == null || messageSource == null || formatSource == null) {
           throw new ServletException("Missing beans not set in the ExportDataServlet: "+meterService+" "+messageSource+" "+formatSource);
       }
    }

    public void doPost(HttpServletRequest request, HttpServletResponse response) throws IOException, ServletException {
        doDataExport(request, response);
    }
    
    public void doGet(HttpServletRequest request, HttpServletResponse response) throws IOException, ServletException {
        doDataExport(request, response);
    }
    
    protected void doDataExport(HttpServletRequest request, HttpServletResponse response) throws IOException, ServletException {
        try {
            MeterMngUser user = getCurrentUser(request);
            if (user != null) {            
                logger.info("Exporting request received: "+request.getLocale());
                try {
                CsvWriter csvContents = getExportData(request, user);
                if (csvContents == null) {                
                    writeError(response, messageSource.getMessage(new DefaultMessageSourceResolvable("export.error.nodata"), request.getLocale()));
                } else {
                    String exportFileName = getExportFileName(request);
                    if (exportFileName == null) {
                        writeError(response, messageSource.getMessage(new DefaultMessageSourceResolvable("export.error.nofile"), request.getLocale()));
                    } else { 
                        logger.info("Writing csv content...");
                        InputStream inputStream = new ByteArrayInputStream(csvContents.getCSVContents().getBytes());
                        response.setContentType("text/csv");
                        response.setHeader("Content-Disposition", "attachment; fileName=\""+exportFileName+"\"");
                        response.setHeader("content-Length", String.valueOf(stream(inputStream, response.getOutputStream())));
                        logger.info("Completed export");
                    }
                }          
                } catch (AccessControlException e) {
                    logger.error(e.getMessage());
                    writeError(response, messageSource.getMessage(new DefaultMessageSourceResolvable("export.denied.group"), request.getLocale()));    
                }
            } else {
                logger.error("No session/logged in user found.");
                writeError(response, messageSource.getMessage(new DefaultMessageSourceResolvable("export.denied"), request.getLocale()));    
            }
        } catch (Exception e) {
            logger.error("Error exporting data:", e);
            writeError(response, messageSource.getMessage(new DefaultMessageSourceResolvable("export.error"), request.getLocale()));
        }
    }
    
    private CsvWriter getExportData(HttpServletRequest request, MeterMngUser user) throws AccessControlException {
        String data = request.getParameter("data");
        if (data.equalsIgnoreCase("meterreadings")) {            
            return getMeterReadings(request, user);
        } else {
            logger.error("Unknown data: "+data);
            return null;
        }
    }
    
    private CsvWriter getMeterReadings(HttpServletRequest request, MeterMngUser user) throws AccessControlException {
        try {
            String type = request.getParameter("type");
            Long meterId = Long.valueOf(request.getParameter("meter"));
            Long readingTypeId = Long.valueOf(request.getParameter("readingtype"));
            Date start = new Date(Long.valueOf(request.getParameter("start")));
            Date end = new Date(Long.valueOf(request.getParameter("end")));
            if (type != null && meterId != null && readingTypeId != null && start != null && end != null) {
                try {
                    //TODO check the current user is in the same group as the specified meter
                    if (meterService.isCorrespondingMeterGroup(meterId, user.getCurrentGroupId())) {                    
                        if ("singleMeter".equalsIgnoreCase(type)) {
                            MeterReadingsDto readings = meterService.getMeterReadings(meterId, readingTypeId, start, end);
                            return ExportDataUtil.getMeterReadings(messageSource, formatSource, request.getLocale(), readings);
                        } else if ("energyBalancing".equalsIgnoreCase(type)) {
                            MeterReadingsDto readings = meterService.getMeterBalancingReadings(meterId, readingTypeId, start, end);
                            return ExportDataUtil.getMeterBalancingReadings(messageSource, formatSource, request.getLocale(), readings, null);
                        } else {
                            logger.error("Unknown type: "+type);
                        }
                    } else {
                        throw new AccessControlException("Invalid user group:"+user.getCurrentGroupId()+" for meterId: "+meterId);
                    }
                } catch (ServiceException e) {
                    logger.error("Error getting export data:", e);
                }
            } else {
                logger.error("Invalid parameters type:"+type+" meterId:"+meterId+" readingTypeId:"+readingTypeId+" start:"+start+" end:"+end);
            }
        } catch (NumberFormatException e) {
            logger.error("Error getting parameters: ", e);            
        }
        return null;
    }
    
    public String getExportFileName(HttpServletRequest request) {
        try {
            String data = request.getParameter("data");
            if (data.equalsIgnoreCase("meterreadings")) {
                String type = request.getParameter("type");
                Long meterId = Long.valueOf(request.getParameter("meter"));
                Long readingTypeId = Long.valueOf(request.getParameter("readingtype"));
                String meterNumber = meterService.getMeterNumberForId(meterId);
                MeterReadingType readingType = meterService.getMeterReadingType(readingTypeId);
                if ("singleMeter".equalsIgnoreCase(type)) {    
                    return ExportDataUtil.getMeterReadingsFileName(messageSource, request.getLocale(), meterNumber, readingType, ".csv");
                } else if ("energyBalancing".equalsIgnoreCase(type)) {
                    return ExportDataUtil.getEnergyBalancingFileName(messageSource, request.getLocale(), meterNumber, readingType, ".csv");
                } else {
                    logger.error("Unknown type: "+type);
                }
            } else {
                logger.error("Unknown data: "+data);
            }
        } catch (NumberFormatException e) {
            logger.error("Error getting params for file name:", e);
        } catch (ServiceException e) {
            logger.error("Error getting file name:", e);
        }
        return null;
    }
    
}