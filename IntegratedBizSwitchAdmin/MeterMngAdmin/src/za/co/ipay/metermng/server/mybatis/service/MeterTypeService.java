package za.co.ipay.metermng.server.mybatis.service;

import java.util.List;

import org.springframework.transaction.annotation.Transactional;

import za.co.ipay.metermng.datatypes.RecordStatus;
import za.co.ipay.metermng.mybatis.generated.mapper.MeterTypeMapper;
import za.co.ipay.metermng.mybatis.generated.model.MeterType;
import za.co.ipay.metermng.mybatis.generated.model.MeterTypeExample;

public class MeterTypeService {

    private MeterTypeMapper meterTypeMapper;
    
    public void setMeterTypeMapper(MeterTypeMapper meterTypeMapper) {
        this.meterTypeMapper = meterTypeMapper;
    }
    
    @Transactional(readOnly=true)
    public List<MeterType> getMeterTypes() {
        MeterTypeExample meterTypeExample = new MeterTypeExample();
        meterTypeExample.createCriteria().andRecordStatusNotEqualTo(RecordStatus.DEL);
        return meterTypeMapper.selectByExample(meterTypeExample);
    }    
    
    @Transactional(readOnly=true)
    public List<MeterType> getMeterTypes(Boolean enabled) {
        MeterTypeExample meterTypeExample = new MeterTypeExample();
        if (enabled != null) {
            if (enabled) {
                meterTypeExample.createCriteria().andRecordStatusEqualTo(RecordStatus.ACT);
            } else {
                meterTypeExample.createCriteria().andRecordStatusEqualTo(RecordStatus.DAC);
            }
        } else {
            meterTypeExample.createCriteria().andRecordStatusNotEqualTo(RecordStatus.DEL);
        }
        meterTypeExample.setOrderByClause("meter_type_name");
        return meterTypeMapper.selectByExample(meterTypeExample);
    }    
    
    @Transactional(readOnly=true)
    public MeterType getMeterType(Long meterTypeId) {
        return meterTypeMapper.selectByPrimaryKey(meterTypeId);
    }    
}
