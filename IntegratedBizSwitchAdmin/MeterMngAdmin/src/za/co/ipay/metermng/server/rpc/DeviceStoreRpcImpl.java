package za.co.ipay.metermng.server.rpc;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

import org.apache.log4j.Logger;

import za.co.ipay.gwt.common.shared.exception.AccessControlException;
import za.co.ipay.gwt.common.shared.exception.ServiceException;
import za.co.ipay.gwt.common.shared.exception.ValidationException;
import za.co.ipay.metermng.client.rpc.DeviceStoreRpc;
import za.co.ipay.metermng.mybatis.generated.model.EndDeviceStoreHist;
import za.co.ipay.metermng.server.mybatis.service.DeviceStoreService;
import za.co.ipay.metermng.server.validation.ServerValidatorUtil;
import za.co.ipay.metermng.shared.EndDeviceStoreData;
import za.co.ipay.metermng.shared.EndDeviceStoreHistData;

public class DeviceStoreRpcImpl extends BaseMeterMngRpc implements DeviceStoreRpc {
    
    private static final long serialVersionUID = 5724847633543590810L;
    
    private DeviceStoreService deviceStoreService;
    
    public DeviceStoreRpcImpl() {
        logger = Logger.getLogger(DeviceStoreRpcImpl.class);
    }
    
    public void setDeviceStoreService(DeviceStoreService deviceStoreService) {
        this.deviceStoreService = deviceStoreService;
    }

    @Override
    public List<EndDeviceStoreData> getDeviceStores() throws ValidationException, ServiceException, AccessControlException {
        return deviceStoreService.getDeviceStores(getUser().getCurrentGroupId(), getUser().getSessionGroupId());
    }

    @Override
    public EndDeviceStoreData getDeviceStore(Long deviceStoreId) throws ValidationException, ServiceException, AccessControlException {
        return deviceStoreService.getDeviceStore(deviceStoreId);
    }

    @Override
    public EndDeviceStoreData getDeviceStoreByMeter(String meterNumber) throws ValidationException, ServiceException, AccessControlException {
        return deviceStoreService.getDeviceStoreByMeter(meterNumber);
    }
    
    @Override
    public EndDeviceStoreData addDeviceStore(EndDeviceStoreData deviceStore) throws ValidationException, ServiceException, AccessControlException {
        ServerValidatorUtil.getInstance().validateDataForValidationMessages(deviceStore);
        deviceStore.setGenGroupId(getUser().getCurrentGroupId());
        return deviceStoreService.insert(deviceStore);
    }
    
    @Override
    public EndDeviceStoreData updateDeviceStore(EndDeviceStoreData deviceStore) throws ValidationException, ServiceException, AccessControlException {
        ServerValidatorUtil.getInstance().validateDataForValidationMessages(deviceStore);
        deviceStore.setGenGroupId(getUser().getCurrentGroupId());
        return deviceStoreService.update(deviceStore);
    }

    @Override
    public ArrayList<EndDeviceStoreHistData> getEndDeviceStoreHistory(Long endDeviceStoreId) throws ServiceException {
        ArrayList<EndDeviceStoreHistData> thelist = new ArrayList<EndDeviceStoreHistData>();
        List<EndDeviceStoreHist> listfromdb = deviceStoreService.getEndDeviceStoreHistoryByEndDeviceStoreId(endDeviceStoreId);
        if (listfromdb != null) {
            EndDeviceStoreHistData histData;
            EndDeviceStoreHistData prev_record = null;
            Iterator<EndDeviceStoreHist> listIt = listfromdb.iterator();
            
            while (listIt.hasNext()) {
                histData = new EndDeviceStoreHistData(listIt.next());
                if (prev_record != null) {
                    findChanges(prev_record, histData);
                }
                prev_record = histData;
                thelist.add(histData);
            }
        }
        return thelist;
    }
    
    private void findChanges(EndDeviceStoreHistData obj1, EndDeviceStoreHistData obj2) {
        if (obj1.getId().equals(obj2.getId())) {
//            if (obj1.getMeterNum() != null && obj2.getMeterNum() != null) {
//                obj2.setMeterNumChanged(!obj1.getMeterNum().equals(obj2.getMeterNum()));
//            } else {
//                if ((obj1.getMeterNum() == null && obj2.getMeterNum() != null)
//                        || (obj1.getMeterNum() != null && obj2.getMeterNum() == null)) {
//                    obj2.setMeterNumChanged(true);
//                }
//            }
//
//            if (obj1.getSerialNum() != null && obj2.getSerialNum() != null) {
//                obj2.setSerialNumChanged(!obj1.getSerialNum().equals(obj2.getSerialNum()));
//            } else if ((obj1.getSerialNum() == null && obj2.getSerialNum() != null)
//                    || (obj1.getSerialNum() != null && obj2.getSerialNum() == null)) {
//                obj2.setSerialNumChanged(true);
//            }
//
//            obj2.setRecordStatusChanged(!obj1.getRecordStatus().equals(obj2.getRecordStatus()));
//
//            if (obj1.getStsTokenTechId() != null && obj2.getStsTokenTechId() != null) {
//                obj2.setStsTokenTechCodeChanged(!obj1.getStsTokenTechId().equals(obj2.getStsTokenTechId()));
//            } else {
//                if ((obj1.getStsTokenTechId() == null && obj2.getStsTokenTechId() != null)
//                        || (obj1.getStsTokenTechId() != null && obj2.getStsTokenTechId() == null)) {
//                    obj2.setStsTokenTechCodeChanged(true);
//                }
//            }
//
//            if (obj1.getStsAlgorithmCodeId() != null && obj2.getStsAlgorithmCodeId() != null) {
//                obj2.setStsAlgorithmCodeChanged(!obj1.getStsAlgorithmCodeId().equals(obj2.getStsAlgorithmCodeId()));
//            } else {
//                if ((obj1.getStsAlgorithmCodeId() == null && obj2.getStsAlgorithmCodeId() != null)
//                        || (obj1.getStsAlgorithmCodeId() != null && obj2.getStsAlgorithmCodeId() == null)) {
//                    obj2.setStsAlgorithmCodeChanged(true);
//                }
//            }
//
//            if (obj1.getStsCurrTariffIndex() != null && obj2.getStsCurrTariffIndex() != null) {
//                obj2.setStsCurrTariffIndexChanged(!obj1.getStsCurrTariffIndex().equals(obj2.getStsCurrTariffIndex()));
//            } else {
//                if ((obj1.getStsCurrTariffIndex() == null && obj2.getStsCurrTariffIndex() != null)
//                        || (obj1.getStsCurrTariffIndex() != null && obj2.getStsCurrTariffIndex() == null)) {
//                    obj2.setStsCurrTariffIndexChanged(true);
//                }
//            }
//            if (obj1.getStsCurrSupplyGroupCode() != null && obj2.getStsCurrSupplyGroupCode() != null) {
//                obj2.setStsCurrSupplyGroupCodeChanged(!obj1.getStsCurrSupplyGroupCode().equals(
//                        obj2.getStsCurrSupplyGroupCode()));
//            } else {
//                if ((obj1.getStsCurrSupplyGroupCode() == null && obj2.getStsCurrSupplyGroupCode() != null)
//                        || (obj1.getStsCurrSupplyGroupCode() != null && obj2.getStsCurrSupplyGroupCode() == null)) {
//                    obj2.setStsCurrSupplyGroupCodeChanged(true);
//                }
//            }
//            if (obj1.getStsCurrKeyRevisionNum() != null && obj2.getStsCurrKeyRevisionNum() != null) {
//                obj2.setStsCurrKeyRevisionNumChanged(!obj1.getStsCurrKeyRevisionNum().equals(
//                        obj2.getStsCurrKeyRevisionNum()));
//            } else {
//                if ((obj1.getStsCurrKeyRevisionNum() == null && obj2.getStsCurrKeyRevisionNum() != null)
//                        || (obj1.getStsCurrKeyRevisionNum() != null && obj2.getStsCurrKeyRevisionNum() == null)) {
//                    obj2.setStsCurrKeyRevisionNumChanged(true);
//                }
//            }
//            if (obj1.getEndDeviceStoreId() != null && obj2.getEndDeviceStoreId() != null) {
//                obj2.setEndDeviceStoreChanged(!obj1.getEndDeviceStoreId().equals(
//                        obj2.getEndDeviceStoreId()));
//            } else {
//                if ((obj1.getEndDeviceStoreId() == null && obj2.getEndDeviceStoreId() != null)
//                        || (obj1.getEndDeviceStoreId() != null && obj2.getEndDeviceStoreId() == null)) {
//                    obj2.setEndDeviceStoreChanged(true);
//                }
//            }
        }
    }
}
