package za.co.ipay.metermng.server.mybatis.service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.apache.ibatis.session.RowBounds;
import org.apache.log4j.Logger;

import za.co.ipay.metermng.server.mybatis.mapper.GroupSearchCustomMapper;
import za.co.ipay.metermng.shared.MeterOnlineBulkKernelData;
import za.co.ipay.metermng.shared.dto.UpPricingStructureData;

/**
 * GroupSearchService provides the group search functionality for the application.
 * <AUTHOR>
 */
public class GroupSearchService {
    
    private GroupSearchCustomMapper groupSearchCustomMapper;
    private UsagePointService usagePointService;
    
    private static Logger logger = Logger.getLogger(GroupSearchService.class);    

    public void setGroupSearchCustomMapper(GroupSearchCustomMapper groupSearchCustomMapper) {
        this.groupSearchCustomMapper = groupSearchCustomMapper;
    }

    public void setUsagePointService(UsagePointService usagePointService) {
        this.usagePointService = usagePointService;
    }

    public Integer getMeterOnlineBulkDataFromGenGroupLeafNodesCount(int start, int pageSize, ArrayList<Long> selGrpIdList, String sortColumn, String filterColumn, String filterString, Date filterDate, String order) {
        return groupSearchCustomMapper.getMeterOnlineBulkKernelDataByGenGroupLeafNodesCount(selGrpIdList, sortColumn, filterColumn, filterString, filterDate, order, new Date());  
    }
    
    public List<MeterOnlineBulkKernelData> getMeterOnlineBulkDataFromGenGroupLeafNodes(int start, int pageSize, ArrayList<Long> selGrpIdList, String sortColumn, String filterColumn, String filterString, Date filterDate, String order) {
        RowBounds rowBounds = new RowBounds(start, pageSize);
        List<MeterOnlineBulkKernelData> dataList = groupSearchCustomMapper.selectMeterOnlineBulkKernelDataByGenGroupLeafNodes(selGrpIdList, sortColumn, filterColumn, filterString, filterDate, order, new Date(), rowBounds);
        for (MeterOnlineBulkKernelData data : dataList) {
            UpPricingStructureData uppsData = usagePointService.getCurrentAndFutureUPPricingStructures(data.getUsagePointId());
            if (uppsData != null && uppsData.getPricingStructure() != null) {
                data.setCurrentPricingStructureId(uppsData.getPricingStructure().getId());
                data.setCurrentPricingStructureName(uppsData.getPricingStructure().getName());
                data.setUpPricingStructureData(uppsData);
            }
        }
        return dataList;
    }
    

//    @Transactional(readOnly=true)
//    public void doMeterAdvancedSearch(SearchData data, Long currentGroupId) 
//        throws ValidationException, ServiceException {        
//        if (MeterMngUtil.isMeterSearchCriteria(data)) {
//            //Combine the meter and other criteria if necessary for AND searching
//            
//            //Total rows available
//            if (data.getTotalResults() == null) {
//                int totalCount = groupSearchCustomMapper.getMeterAdvancedSearchCount(data.getCriteria(), currentGroupId);
//                data.setTotalResults(totalCount);
//                logger.info("Set current total search results: "+totalCount);
//            } else {
//                logger.info("Current total search results: "+data.getTotalResults());
//            }
//            
//            //Search for current search results
//            logger.info("Meter search with start:"+data.getStart()+" pageSize:"+data.getPageSize());
//            RowBounds rowBounds = new RowBounds(data.getStart(), data.getPageSize());
//            List<SearchResult> meters = groupSearchCustomMapper.doMeterAdvancedSearch(data.getCriteria(), currentGroupId, rowBounds);            
//            ArrayList<SearchResultData> results = new ArrayList<SearchResultData>();
//            SearchResultData result = null;
//            for(SearchResult sr : meters) {
//                result = new SearchResultData(SearchResultType.METER, sr.getMeterId());
//                result.addDetail(MeterMngStatics.METER_NUMBER_SEARCH, sr.getMeterNumber());
//                if (sr.getMeterModelId() != null) {
//                    result.addDetail(MeterMngStatics.METER_MODEL_ID_SEARCH, sr.getMeterModelId().toString());
//                    result.addDetail(MeterMngStatics.METER_MODEL_NAME, sr.getMeterModelName());
//                }
//                if (sr.getAgreementId() != null) {
//                    result.addId(MeterMngStatics.CUSTOMER_AGREEMENT_ID_SEARCH, sr.getAgreementId());
//                    result.addDetail(MeterMngStatics.CUSTOMER_AGREEMENT_SEARCH, sr.getAgreement());
//                }
//                if (sr.getCustomerId() != null) {
//                    result.addId(MeterMngStatics.CUSTOMER_ID_SEARCH, sr.getCustomerId());
//                    result.addDetail(MeterMngStatics.CUSTOMER_SURNAME_SEARCH, sr.getSurname());
//                    result.addDetail(MeterMngStatics.CUSTOMER_NAME_SEARCH, sr.getName());
//                    result.addDetail(MeterMngStatics.CUSTOMER_TITLE_SEARCH, sr.getTitle());
//                }
//                if (sr.getAccountName() != null) {
//                    result.addDetail(MeterMngStatics.ACCOUNT_NAME_SEARCH, sr.getAccountName());
//                }
//                if (sr.getUsagePointId() != null) {
//                    result.addId(MeterMngStatics.USAGE_POINT_ID_SEARCH, sr.getUsagePointId());
//                    result.addDetail(MeterMngStatics.USAGE_POINT_NAME_SEARCH, sr.getUsagePointName());
//                    result.addDetail(MeterMngStatics.PRICING_STRUCTURE_ID_SEARCH, sr.getPricingStructureId().toString());
//                    result.addDetail(MeterMngStatics.PRICING_STRUCTURE_NAME_SEARCH, sr.getPricingStructureName());
//                    result.addDetail(MeterMngStatics.PAYMENT_MODE_NAME_SEARCH, sr.getPaymentModeName());
//                }
//                results.add(result);
//            }
//            logger.info("Got meters:"+results.size());
//            data.addResults(results);
//        }        
//    }
// 
//    @Transactional(readOnly=true)
//    public void doCustomerAdvancedSearch(SearchData data, Long currentGroupId) 
//        throws ValidationException, ServiceException {        
//        if (MeterMngUtil.isCustomerSearchCriteria(data)) {
//            //Combine the meter and other criteria if necessary for AND searching
//            
//            //Total rows available
//            if (data.getTotalResults() == null) {
//                int totalCount = groupSearchCustomMapper.getCustomerAdvancedSearchCount(data.getCriteria(), currentGroupId);
//                data.setTotalResults(totalCount);
//                logger.info("Set current total search results: "+totalCount);
//            } else {
//                logger.info("Current total search results: "+data.getTotalResults());
//            }
//            
//            //Search for current search results
//            logger.info("Customer search with start:"+data.getStart()+" pageSize:"+data.getPageSize());
//            RowBounds rowBounds = new RowBounds(data.getStart(), data.getPageSize());
//            List<SearchResult> meters = groupSearchCustomMapper.doCustomerAdvancedSearch(data.getCriteria(), currentGroupId, rowBounds);            
//            ArrayList<SearchResultData> results = new ArrayList<SearchResultData>();
//            SearchResultData result = null;
//            for(SearchResult sr : meters) {
//                result = new SearchResultData(SearchResultType.CUSTOMER, sr.getCustomerId());
//                result.addDetail(MeterMngStatics.CUSTOMER_SURNAME_SEARCH, sr.getSurname());
//                result.addDetail(MeterMngStatics.CUSTOMER_NAME_SEARCH, sr.getName());
//                result.addDetail(MeterMngStatics.CUSTOMER_TITLE_SEARCH, sr.getTitle());
//                if (sr.getAgreementId() != null) {
//                    result.addId(MeterMngStatics.CUSTOMER_AGREEMENT_ID_SEARCH, sr.getAgreementId());
//                    result.addDetail(MeterMngStatics.CUSTOMER_AGREEMENT_SEARCH, sr.getAgreement());
//                }
//                if (sr.getAccountName() != null) {
//                    result.addDetail(MeterMngStatics.ACCOUNT_NAME_SEARCH, sr.getAccountName());
//                }
//                if (sr.getMeterId() != null) {
//                    result.addId(MeterMngStatics.METER_ID_SEARCH, sr.getMeterId());
//                    result.addDetail(MeterMngStatics.METER_NUMBER_SEARCH, sr.getMeterNumber());
//                }
//                if (sr.getMeterModelId() != null) {
//                    result.addDetail(MeterMngStatics.METER_MODEL_ID_SEARCH, sr.getMeterModelId().toString());
//                    result.addDetail(MeterMngStatics.METER_MODEL_NAME, sr.getMeterModelName());
//                }
//                if (sr.getUsagePointId() != null) {
//                    result.addId(MeterMngStatics.USAGE_POINT_ID_SEARCH, sr.getUsagePointId());
//                    result.addDetail(MeterMngStatics.USAGE_POINT_NAME_SEARCH, sr.getUsagePointName());
//                    result.addDetail(MeterMngStatics.PRICING_STRUCTURE_ID_SEARCH, sr.getPricingStructureId().toString());
//                    result.addDetail(MeterMngStatics.PRICING_STRUCTURE_NAME_SEARCH, sr.getPricingStructureName());
//                    result.addDetail(MeterMngStatics.PAYMENT_MODE_NAME_SEARCH, sr.getPaymentModeName());
//                }
//                results.add(result);
//            }
//            logger.info("Got customers:"+results.size());
//            data.addResults(results);
//        }        
//    }
//    
//    @Transactional(readOnly=true)
//    public void doUsagePointAdvancedSearch(SearchData data, Long currentGroupId) 
//        throws ValidationException, ServiceException {        
//        if (MeterMngUtil.isUsagePointSearchCriteria(data)) {
//            //Combine the meter and other criteria if necessary for AND searching
//            
//            //Total rows available
//            if (data.getTotalResults() == null) {
//                int totalCount = groupSearchCustomMapper.getUsagePointAdvancedSearchCount(data.getCriteria(), currentGroupId);
//                data.setTotalResults(totalCount);
//                logger.info("Set current total search results: "+totalCount);
//            } else {
//                logger.info("Current total search results: "+data.getTotalResults());
//            }
//            
//            //Search for current search results
//            logger.info("UsagePoint search with start:"+data.getStart()+" pageSize:"+data.getPageSize());
//            RowBounds rowBounds = new RowBounds(data.getStart(), data.getPageSize());
//            List<SearchResult> meters = groupSearchCustomMapper.doUsagePointAdvancedSearch(data.getCriteria(), currentGroupId, rowBounds);            
//            ArrayList<SearchResultData> results = new ArrayList<SearchResultData>();
//            SearchResultData result = null;
//            for(SearchResult sr : meters) {
//                result = new SearchResultData(SearchResultType.USAGE_POINT, sr.getUsagePointId());
//                result.addDetail(MeterMngStatics.USAGE_POINT_NAME_SEARCH, sr.getUsagePointName());
//                result.addDetail(MeterMngStatics.PRICING_STRUCTURE_ID_SEARCH, sr.getPricingStructureId().toString());
//                result.addDetail(MeterMngStatics.PRICING_STRUCTURE_NAME_SEARCH, sr.getPricingStructureName());
//                result.addDetail(MeterMngStatics.PAYMENT_MODE_NAME_SEARCH, sr.getPaymentModeName());
//                if (sr.getCustomerId() != null) {
//                    result.addId(MeterMngStatics.CUSTOMER_ID_SEARCH, sr.getCustomerId());
//                    result.addDetail(MeterMngStatics.CUSTOMER_SURNAME_SEARCH, sr.getSurname());
//                    result.addDetail(MeterMngStatics.CUSTOMER_NAME_SEARCH, sr.getName());
//                    result.addDetail(MeterMngStatics.CUSTOMER_TITLE_SEARCH, sr.getTitle());
//                }
//                if (sr.getAgreementId() != null) {
//                    result.addId(MeterMngStatics.CUSTOMER_AGREEMENT_ID_SEARCH, sr.getAgreementId());
//                    result.addDetail(MeterMngStatics.CUSTOMER_AGREEMENT_SEARCH, sr.getAgreement());
//                }
//                if (sr.getAccountName() != null) {
//                    result.addDetail(MeterMngStatics.ACCOUNT_NAME_SEARCH, sr.getAccountName());
//                }
//                if (sr.getMeterId() != null) {
//                    result.addId(MeterMngStatics.METER_ID_SEARCH, sr.getMeterId());
//                    result.addDetail(MeterMngStatics.METER_NUMBER_SEARCH, sr.getMeterNumber());
//                }
//                if (sr.getMeterModelId() != null) {
//                    result.addDetail(MeterMngStatics.METER_MODEL_ID_SEARCH, sr.getMeterModelId().toString());
//                    result.addDetail(MeterMngStatics.METER_MODEL_NAME, sr.getMeterModelName());
//                }
//                results.add(result);
//            }
//            logger.info("Got usagePoints:"+results.size());
//            data.addResults(results);
//        }        
//    }
}
