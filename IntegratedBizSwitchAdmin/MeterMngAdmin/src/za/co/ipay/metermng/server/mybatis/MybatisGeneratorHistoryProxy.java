package za.co.ipay.metermng.server.mybatis;

import java.lang.reflect.InvocationHandler;
import java.lang.reflect.Method;
import java.util.Date;

import org.apache.log4j.Logger;
import org.springframework.beans.BeanUtils;

import za.co.ipay.dao.history.HistoryConstants;
import za.co.ipay.utils.web.ICurrentUserNameProvider;

public class MybatisGeneratorHistoryProxy implements InvocationHandler {
    private static Logger logger = Logger.getLogger(MybatisGeneratorHistoryProxy.class);
    private static final String USER_METHOD = "setUserRecEntered";
    private static final String ACTION_METHOD = "setUserAction";
    private static final String DATE_METHOD = "setDateRecModified";
    private static final String INSERT_METHOD = "insert";
    private static final String UPDATE_METHOD = "updateByPrimaryKey";
    private static final String DELETE_METHOD = "deleteByPrimaryKey";
    private static final String SELECT_METHOD = "selectByPrimaryKey";
    private Object dao;
    private Object historyDao;
    private ICurrentUserNameProvider userNameProvider;
    @SuppressWarnings("rawtypes")
    private Class historyClass;

    private Method userMethod;
    private Method dateMethod;
    private Method actionMethod;
    private Method historyInsertMethod;
    private Method selectMethod;

    @SuppressWarnings("unchecked")
    public MybatisGeneratorHistoryProxy(Object dao, Object historyDao, ICurrentUserNameProvider userNameProvider) throws Exception {
        this.dao = dao;
        this.historyDao = historyDao;
        this.userNameProvider = userNameProvider;

        Method[] methods = historyDao.getClass().getMethods();
        for (int i = 0; i < methods.length; i++) {
            if (methods[i].getName().equals(INSERT_METHOD)) {
                // there is only one select by primary key method in abator generated dao's
                this.historyInsertMethod = methods[i];
                break;
            }
        }

        // only has single parameter
        this.historyClass = historyInsertMethod.getParameterTypes()[0];

        this.userMethod = historyClass.getMethod(USER_METHOD, String.class);
        this.dateMethod = historyClass.getMethod(DATE_METHOD, Date.class);
        this.actionMethod = historyClass.getMethod(ACTION_METHOD, String.class);

        methods = dao.getClass().getMethods();
        for (int i = 0; i < methods.length; i++) {
            if (methods[i].getName().equals(SELECT_METHOD)) {
                // there is only one select by primary key method in abator generated dao's
                this.selectMethod = methods[i];
                break;
            }
        }
    }

    public Object invoke(Object proxy, Method method, Object[] args) throws Throwable {
        if (logger.isDebugEnabled())
            logger.debug("Checking whether history is needed for this method." + historyClass.getName());

        // need to get history before executing method, since otherwise
        // if it was deleting a record, it would be lost
        Object original = null;
        if (method.getName().equals(DELETE_METHOD))
            original = selectMethod.invoke(dao, args);
        else
            original = args[0];

        Object result = method.invoke(dao, args);

        String action = checkHistoryAction(method.getName());
        if (action != null) {
            if (logger.isDebugEnabled())
                logger.debug("Doing history: action=" + action);
            insertHistory(original, action);
        }
        return result;
    }

    public String checkHistoryAction(String methodName) {
        if (methodName.equals(INSERT_METHOD))
            return HistoryConstants.INSERT_ACTION;
        if (methodName.equals(UPDATE_METHOD))
            return HistoryConstants.UPDATE_ACTION;
        if (methodName.equals(DELETE_METHOD))
            return HistoryConstants.DELETE_ACTION;
        return null;
    }

    private void insertHistory(Object original, String action) throws Throwable {
        Object history = historyClass.newInstance();

        BeanUtils.copyProperties(original, history);
        if (logger.isDebugEnabled())
            logger.debug("after copying properties: dao=" + dao + ", history=" + history);

        if (logger.isDebugEnabled())
            logger.debug("Doing history: history=" + history + ",action=" + action);

        userMethod.invoke(history, userNameProvider.getCurrentUserName());
        actionMethod.invoke(history, action);
        dateMethod.invoke(history, new Date());

        historyInsertMethod.invoke(historyDao, history);

        if (logger.isDebugEnabled())
            logger.debug("history done");
    }

}
