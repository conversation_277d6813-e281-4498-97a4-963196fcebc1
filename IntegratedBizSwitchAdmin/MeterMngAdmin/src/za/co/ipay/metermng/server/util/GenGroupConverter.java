package za.co.ipay.metermng.server.util;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;

import org.apache.log4j.Logger;

import za.co.ipay.metermng.mybatis.generated.model.GenGroup;
import za.co.ipay.metermng.shared.GenGroupData;

public class GenGroupConverter {
    
    private static Logger logger = Logger.getLogger(GenGroupConverter.class);
    
    public static GenGroup convertGenGroupData(GenGroupData data) {
        if (data != null) {
            GenGroup genGroup = new GenGroup();
            genGroup.setGroupEntityId(data.getGroupEntityId());
            genGroup.setCustomerAccThresholdsId(data.getCustomerAccThresholdsId());
            genGroup.setNdpScheduleId(data.getNdpScheduleId());
            genGroup.setGroupHierarchyId(data.getGroupHierarchyId());
            genGroup.setId(data.getId());
            genGroup.setName(data.getName());
            genGroup.setParentId(data.getParentId());
            genGroup.setRecordStatus(data.getRecordStatus());
            genGroup.setMetadata(data.getMetadata());
            genGroup.setMrid(data.getMrid());
            genGroup.setMridExternal(data.isMridExternal());
            genGroup.setAccessGroupId(data.getAccessGroupId());
            genGroup.setCustAccNotifyId(data.getCustAccNotifyId());
            return genGroup;
        } else {
            return null;
        }
    }
    
    public static GenGroupData convertGenGroup(GenGroup g) {
        if (g != null) {
            GenGroupData d = new GenGroupData();
            d.setGroupEntityId(g.getGroupEntityId());
            d.setCustomerAccThresholdsId(g.getCustomerAccThresholdsId());
            d.setNdpScheduleId(g.getNdpScheduleId());
            d.setGroupHierarchyId(g.getGroupHierarchyId());
            d.setId(g.getId());
            d.setName(g.getName());
            d.setParentId(g.getParentId());
            d.setRecordStatus(g.getRecordStatus());
            d.setMetadata(g.getMetadata());
            d.setMrid(g.getMrid());
            d.setMridExternal(g.isMridExternal());
            d.setAccessGroupId(g.getAccessGroupId());
            d.setCustAccNotifyId(g.getCustAccNotifyId());
            return d;
        } else {
            return null;
        }
    }
    
    public static ArrayList<GenGroupData> convertGenGroups(ArrayList<GenGroup> hierarchies) {
        ArrayList<GenGroupData> temp = new ArrayList<GenGroupData>();
        for(GenGroup d : hierarchies) {
            temp.add(convertGenGroup(d));
        }
        return temp;
    }
    
    public static ArrayList<GenGroupData> convertToHierarchy(ArrayList<GenGroup> hierarchies) {
        //Convert the data into a parents --> children hierarchy for easy of use on front end
        ArrayList<GenGroupData> temp = convertGenGroups(hierarchies);
        //Sort by ids
        HashMap<Long, ArrayList<GenGroupData>> ids = groupByIds(temp);
        logger.info("Grouped by parent ids ");          //+ids.toString());
        //For each item, add its children and then recurse through the children
        ArrayList<GenGroupData> parents = ids.get(Long.valueOf(0));
        addChildren(parents, ids);
        //sort the data so its display the same way each time
        sortGenGroups(parents);
        return parents;
    }
    
    private static void sortGenGroups(List<GenGroupData> data) {
        Collections.sort(data, new NameComparator());
        
        for(GenGroupData d : data) {
            sortGenGroups(d.getChildren());
        }
    }
    static class NameComparator implements Comparator<GenGroupData> {
        @Override
        public int compare(GenGroupData o1, GenGroupData o2) {
            if (o1 == o2) {
                return 0;
            }
            if (o1 != null) {
                return (o2 != null) ? o1.getName().toLowerCase().compareTo(o2.getName().toLowerCase()) : 1;
            }
            return -1;
        }
    }
    
    private static HashMap<Long, ArrayList<GenGroupData>> groupByIds(ArrayList<GenGroupData> temp) {
        //parent id, children
        HashMap<Long, ArrayList<GenGroupData>> ids = new HashMap<Long, ArrayList<GenGroupData>>();
        ids.put(Long.valueOf(0), new ArrayList<GenGroupData>());  //for null parents ids
        for(GenGroupData data : temp) {
            if (data.getParentId() == null) {
                ids.get(Long.valueOf(0)).add(data);
            } else {
                if (!ids.containsKey(data.getParentId())) {
                    ids.put(data.getParentId(), new ArrayList<GenGroupData>());
                }
                ids.get(data.getParentId()).add(data);
            }
        }
        return ids;
    }

    private static void addChildren(ArrayList<GenGroupData> current, HashMap<Long, ArrayList<GenGroupData>> all) {
        for(GenGroupData c : current) {
            if (all.containsKey(c.getId())) {
                c.setChildren(all.get(c.getId()));
                for(GenGroupData child : c.getChildren()) {
                    child.setParent(c);
                    logger.debug("Set parent for child: "+child.getId());
                }
                addChildren((ArrayList<GenGroupData>) c.getChildren(), all);
            }
        }
    }

    private GenGroupConverter() {
        //no instances required
    }
}
