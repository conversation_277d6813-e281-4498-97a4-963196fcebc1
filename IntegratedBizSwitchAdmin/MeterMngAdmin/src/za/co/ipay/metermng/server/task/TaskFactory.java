package za.co.ipay.metermng.server.task;

import za.co.ipay.metermng.server.task.balancing.EnergyBalancingExportTask;
import za.co.ipay.metermng.server.task.balancing.EnergyBalancingVarianceTask;
import za.co.ipay.metermng.server.task.reading.MeterReadingsExportTask;

public class TaskFactory {
    
    public static final Long METER_READINGS_REPORT_EXPORT = Long.valueOf(1);
    public static final Long ENERGY_BALANCING_REPORT_EXPORT = Long.valueOf(2);
    public static final Long ENERGY_BALANCING_VARIANCE = Long.valueOf(3);

    public static Task getScheduledTask(Long taskClassId) {
        if (taskClassId != null) {
            if (METER_READINGS_REPORT_EXPORT.equals(taskClassId)) {
                return new MeterReadingsExportTask();
            } else if (ENERGY_BALANCING_REPORT_EXPORT.equals(taskClassId)) {
                return new EnergyBalancingExportTask();
            } else if (ENERGY_BALANCING_VARIANCE.equals(taskClassId)) {
                return new EnergyBalancingVarianceTask();
            } else {
                return null;
            }
        } else {
            return null;
        }
    }
    
}
