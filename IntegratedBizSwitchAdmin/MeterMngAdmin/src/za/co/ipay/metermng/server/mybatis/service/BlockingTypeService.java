package za.co.ipay.metermng.server.mybatis.service;

import java.util.List;

import za.co.ipay.gwt.common.shared.exception.ServiceException;
import za.co.ipay.metermng.mybatis.generated.mapper.BlockingTypeMapper;
import za.co.ipay.metermng.mybatis.generated.model.BlockingType;
import za.co.ipay.metermng.mybatis.generated.model.BlockingTypeExample;

public class BlockingTypeService {

    private BlockingTypeMapper blockingTypeMapper;

    public void setBlockingTypeMapper(BlockingTypeMapper blockingTypeMapper) {
        this.blockingTypeMapper = blockingTypeMapper;
    }

    public List<BlockingType> getAllBlockingTypes() {
        BlockingTypeExample blockingTypeExample = new BlockingTypeExample();
        blockingTypeExample.setOrderByClause("type_name asc");
        return blockingTypeMapper.selectByExample(blockingTypeExample);
    }

    public BlockingType getBlockingTypeById(Long id) {
        return blockingTypeMapper.selectByPrimaryKey(id);
    }

    public BlockingType getBlockingTypeByName(String name) {
        BlockingTypeExample blockingTypeExample = new BlockingTypeExample();
        blockingTypeExample.createCriteria().andTypeNameEqualTo(name);
        List<BlockingType> list = blockingTypeMapper.selectByExample(blockingTypeExample);
        if (list != null && !list.isEmpty()) {
            return list.get(0);
        }
        return null;
    }

    public BlockingType insertBlockingType(BlockingType blockingType) throws ServiceException {
    	if (blockingTypeMapper.insert(blockingType) == 1) {
            return blockingType;
        } else {
            throw new ServiceException("blockingtype.error.save");
        }
    }

    public BlockingType updateBlockingType(BlockingType blockingType) throws ServiceException {
    	if (blockingTypeMapper.updateByPrimaryKey(blockingType) == 1) {
            return blockingType;
        } else {
            throw new ServiceException("blockingtype.error.update");
        }
    }
}
