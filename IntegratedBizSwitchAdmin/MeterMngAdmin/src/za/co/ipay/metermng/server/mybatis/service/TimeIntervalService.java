package za.co.ipay.metermng.server.mybatis.service;

import java.util.List;

import org.springframework.transaction.annotation.Transactional;

import za.co.ipay.metermng.datatypes.RecordStatus;
import za.co.ipay.metermng.mybatis.generated.mapper.TimeIntervalMapper;
import za.co.ipay.metermng.mybatis.generated.model.TimeInterval;
import za.co.ipay.metermng.mybatis.generated.model.TimeIntervalExample;

public class TimeIntervalService {

    private TimeIntervalMapper timeIntervalMapper;
    
    @Transactional(readOnly=true)
    public TimeInterval getTimeInterval(Long id) {
        if (id != null) {
            return timeIntervalMapper.selectByPrimaryKey(id);
        } else {
            return null;
        }
    }

    @Transactional(readOnly=true)
    public List<TimeInterval> getTimeIntervals(Boolean active) {
        TimeIntervalExample example = new TimeIntervalExample();
        if (active != null) {
            if (active) {
                example.createCriteria().andRecordStatusEqualTo(RecordStatus.ACT);
            } else {
                example.createCriteria().andRecordStatusEqualTo(RecordStatus.DAC);
            }
        } else {
            example.createCriteria().andRecordStatusNotEqualTo(RecordStatus.DEL);
        }
        example.setOrderByClause("time_interval_id");
        return timeIntervalMapper.selectByExample(example);
    }
    
    
    public void setTimeIntervalMapper(TimeIntervalMapper timeIntervalMapper) {
        this.timeIntervalMapper = timeIntervalMapper;
    }
}
