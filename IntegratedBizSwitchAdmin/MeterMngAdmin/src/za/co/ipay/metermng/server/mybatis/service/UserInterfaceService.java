package za.co.ipay.metermng.server.mybatis.service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.transaction.annotation.Transactional;

import za.co.ipay.metermng.mybatis.generated.mapper.FormFieldsMapper;
import za.co.ipay.metermng.mybatis.generated.mapper.FormMapper;
import za.co.ipay.metermng.mybatis.generated.model.Form;
import za.co.ipay.metermng.mybatis.generated.model.FormExample;
import za.co.ipay.metermng.mybatis.generated.model.FormFields;
import za.co.ipay.metermng.mybatis.generated.model.FormFieldsExample;

public class UserInterfaceService {

    private FormMapper formMapper;
    private FormFieldsMapper formFieldsMapper;

    public void setFormMapper(FormMapper formMapper) {
        this.formMapper = formMapper;
    }

    public void setFormFieldsMapper(FormFieldsMapper formFieldsMapper) {
        this.formFieldsMapper = formFieldsMapper;
    }

    @Transactional(readOnly = true)
    public List<Form> getForms() {
        FormExample example = new FormExample();
        example.setOrderByClause("form_id");
        return formMapper.selectByExample(example);
    }

    @Transactional(readOnly = true)
    public List<FormFields> getFormFieldsList() {
        FormFieldsExample example = new FormFieldsExample();
        example.setOrderByClause("form_fields_id");
        return formFieldsMapper.selectByExample(example);
    }

    @Transactional
    public void saveFormFields(Map<Long, FormFields> formFields) {
        formFields.values().forEach(formFieldsMapper::updateByPrimaryKey);
    }

    @Transactional(readOnly = true)
    public Map<String, FormFields> getFormFields() {
        HashMap<String, FormFields> fieldsMap = new HashMap<String, FormFields>();
        getFormFieldsList().forEach(field -> {
            fieldsMap.put(field.getValue(), field);
        });
        return fieldsMap;
    }
}
