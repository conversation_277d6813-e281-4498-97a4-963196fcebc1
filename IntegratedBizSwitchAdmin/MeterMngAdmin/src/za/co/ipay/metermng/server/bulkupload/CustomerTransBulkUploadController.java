package za.co.ipay.metermng.server.bulkupload;

import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import za.co.ipay.gwt.common.shared.validation.ValidateUtil;
import za.co.ipay.metermng.datatypes.RecordStatus;
import za.co.ipay.metermng.mybatis.generated.model.AccountTrans;
import za.co.ipay.metermng.mybatis.generated.model.CustomerAccount;
import za.co.ipay.metermng.mybatis.generated.model.Meter;
import za.co.ipay.metermng.mybatis.generated.model.UsagePoint;
import za.co.ipay.metermng.server.mybatis.service.BulkUploadService;
import za.co.ipay.metermng.server.mybatis.service.CustomerAccountService;
import za.co.ipay.metermng.server.mybatis.service.MeterService;
import za.co.ipay.metermng.server.mybatis.service.UsagePointService;
import za.co.ipay.metermng.shared.bulkupload.BulkUploadException;
import za.co.ipay.metermng.shared.dto.uploaddata.transuploaddata.CustomerTransCsvData;
import za.co.ipay.metermng.shared.dto.uploaddata.transuploaddata.TransCsvMapToData;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.Map;

@Controller
@RequestMapping(value = "/**/secure/customertransbulkupload.do")
public class CustomerTransBulkUploadController extends TransBulkUploadCommon {

	@Autowired
	private UsagePointService usagePointService;

	@Autowired
	private MeterService meterService;

	@Autowired
	private CustomerAccountService customerAccountService;

	@Autowired
	private BulkUploadService bulkUploadService;

	private static final boolean IS_CUSTOMER_TRANS_UPLOAD = true;
	private Logger logger = Logger.getLogger(CustomerTransBulkUploadController.class.getName());

	private Map<Long, String> identifiersMap;
	private ArrayList<CustomerTransCsvData> processTransList;
	private TransCsvMapToData csvMapData;

	public void setupDataMapping() {
		csvMapData = new TransCsvMapToData(IS_CUSTOMER_TRANS_UPLOAD);
		csvFieldMap = new HashMap<Integer, String>();
		headingIndicator = "Identifier Type";
		setCustomerTransUpload(IS_CUSTOMER_TRANS_UPLOAD);
	}

	@Override
	void constructCsvFieldMap(String headingLine) throws BulkUploadException {
		csvFieldMap = csvMapData.constructCsvFieldMap(headingLine);
	}

	// *********************************************************************************************
	// ********** VALIDATE UPLOADED FILE (FILE STRUCTURE THEN CONTENT) *****************************
	@Override
	void validationPreProcessing() {
		setupDataMapping();
		identifiersMap = new HashMap<Long, String>();
		dateFormatter.setLenient(false);
	}

	@Override
	String validateTrans(String thisLine) {
		/*
		 * Original Validation Rules: 
		 * FIELDS: identifierType, identifier, amtInclTax, amtTax, transDate, accountRef, comment
		 * COMMON FIELDS: amtInclTax,amtTax,transDate,accountRef,comment
		 * identifierType: usagePointName, accountName, meterNumber
		 * identifier: actual usage point name, account name or meter number.
		 */
		CustomerTransCsvData custTrans = new CustomerTransCsvData(csvFieldMap, thisLine, WITH_ERROR_STRING);
		StringBuilder validationError = new StringBuilder();
		Long customerAgreementId = null;

		if (custTrans.isUnexpectedCommas()) {
			validationError.append("customer.trans.upload.invalid.unexpected.commas");
			return validationError.toString();
		}

		// Identifier Type
		String identifierTypeLc = null;
		if (ValidateUtil.isNullOrBlank(custTrans.getIdentifierType())) {
			validationError.append(addValidationError("required", "customer.trans.upload.identifierType"));
		} else {
			identifierTypeLc = custTrans.getIdentifierType().toLowerCase();
			if (!identifierTypeLc.equals("usagepointname") && !identifierTypeLc.equals("accountname")
					&& !identifierTypeLc.equals("meternumber")) {
				validationError.append("customer.trans.upload.invalid.identifiertype").append(";");
			}
		}

		// Identifier
		String identifierError = null;
		if (ValidateUtil.isNullOrBlank(custTrans.getIdentifier())) {
			validationError.append(addValidationError("required", "customer.trans.upload.identifier"));
		} else if (identifierTypeLc != null) {
			if (identifierTypeLc.equals("usagepointname")) {
				UsagePoint up = usagePointService.getUsagePointByName(custTrans.getIdentifier());
				identifierError = validateUsagePoint(up);
				if (up != null) {
				    customerAgreementId = up.getCustomerAgreementId();
				}
	
			} else if (identifierTypeLc.equals("accountname")) {
				CustomerAccount cacc = customerAccountService.getCustomerAccountByAccountName(custTrans.getIdentifier());
				if (cacc == null) {
					identifierError = "customer.trans.upload.invalid.identifier" + ";";
				} else {
					UsagePoint up = usagePointService.getUsagePointByCustomerAgreementId(
							customerAgreementService.getCustomerAgreementByCustomerId(cacc.getCustomerId()).getId());
					identifierError = validateUsagePoint(up);
	                if (up != null) {
	                    customerAgreementId = up.getCustomerAgreementId();
	                }
				}
	
			} else if (identifierTypeLc.equals("meternumber")) {
				Meter m = meterService.getMeterByNumber(custTrans.getIdentifier());
				if (m == null) {
					identifierError = "customer.trans.upload.invalid.identifier" + ";";
				} else {
					UsagePoint up = usagePointService.getUsagePointByMeterId(m.getId());
					identifierError = validateUsagePoint(up);
	                if (up != null) {
	                    customerAgreementId = up.getCustomerAgreementId();
	                }
				}
			}
			if (identifierError != null && !identifierError.isEmpty()) {
				validationError.append(identifierError);
			}
		}

		// duplicate transaction
		String dupError = checkForDuplicate(custTrans, customerAgreementId);
		if (dupError != null) {
		    validationError.append(dupError).append(";");
		}
		
		// Performs Validation of Common Fields
		validateTransCommonFields(validationError, custTrans);

		String returnString = validationError.toString();
		int lastIndx = returnString.lastIndexOf(";");
		if (lastIndx > 0) {
			returnString = returnString.substring(0, lastIndx);
		}
		return returnString;
	}
	
	private String validateUsagePoint(UsagePoint up) {
	    StringBuilder idErr = new StringBuilder();
	    if (up == null) {
            idErr.append("customer.trans.upload.invalid.identifier").append(";");
        } else {
            if (up.getCustomerAgreementId() == null) {
                idErr.append("customer.trans.upload.invalid.agreement").append(";");
            } 
            if (up.getMeterId() == null) {
                idErr.append("customer.trans.upload.invalid.up.no.meter").append(";");
            } 
            if (up.getRecordStatus() != RecordStatus.ACT) {
                idErr.append("customer.trans.upload.invalid.up.not.active").append(";"); 
            }
        }
	    return idErr.toString();
	}

	private String checkForDuplicate(CustomerTransCsvData custTrans, Long customerAgreementId) {
		if (custTrans.getIdentifierType() == null || custTrans.getIdentifierType().trim().isEmpty() || custTrans.getIdentifier() == null || custTrans.getIdentifier().trim().isEmpty()) {
			return null;
		}
		String transKey = custTrans.getIdentifierType().toLowerCase() + "," + custTrans.getIdentifier().toLowerCase();
		Collection<String> transCustIds = identifiersMap.values();

		StringBuilder sb = new StringBuilder();
		for (String s : transCustIds) {
			sb.append(s).append(" // ");
		}

		if (!transCustIds.contains(transKey)) { // not found in Collection
			if (customerAgreementId != null) {
				String dupTransKey = identifiersMap.get(customerAgreementId);
				if (dupTransKey != null) {
					return constructDupError(dupTransKey);
				}
			}
			identifiersMap.put(customerAgreementId, transKey);
		} else {
			return constructDupError(transKey);
		}

		return null;
	}

	private String constructDupError(String transKey) {
		String[] sa = transKey.split(",");
		return "customer.trans.upload.invalid.duplicate_" + sa[0] + "_" + sa[1];
	}

	// ***********************************************************************************
	// ********** P R O C E S S U P L O A D   ********************************************
	@Override
	public void processTransactionsPreProcessing() {
		setupDataMapping();
		processTransList = new ArrayList<CustomerTransCsvData>();
		accountTransList = new ArrayList<AccountTrans>();
		dateFormatter.setLenient(false);
	}

	@Override
	public void addToProcessList(String thisLine) {
		CustomerTransCsvData custTrans = new CustomerTransCsvData(csvFieldMap, thisLine, WITH_ERROR_STRING);
		processTransList.add(custTrans);
		accountTransList.add(createAccountTrans(custTrans));
	}

	private AccountTrans createAccountTrans(CustomerTransCsvData custTrans) {
		
		Long customerAgreementId = null;
		Long customerAccountId = null;
		UsagePoint up = null;
		String identifierTypeLc = custTrans.getIdentifierType().toLowerCase();
		if (identifierTypeLc.equals("usagepointname")) {
			up = usagePointService.getUsagePointByName(custTrans.getIdentifier());
		}
		if (identifierTypeLc.equals("meternumber")) {
			Meter m = meterService.getMeterByNumber(custTrans.getIdentifier());
			up = usagePointService.getUsagePointByMeterId(m.getId());
		}
		if (identifierTypeLc.equals("usagepointname") || identifierTypeLc.equals("meternumber")) {
			customerAgreementId = up.getCustomerAgreementId();
			customerAccountId = customerAgreementService.getCustomerAgreementById(customerAgreementId).getCustomerAccountId();
		}

		if (identifierTypeLc.equals("accountname")) {
			CustomerAccount cacc = customerAccountService.getCustomerAccountByAccountName(custTrans.getIdentifier());
			customerAccountId = cacc.getId();
			customerAgreementId = customerAgreementService.getCustomerAgreementByCustomerId(cacc.getCustomerId()).getId();
		}

		AccountTrans accountTrans = createAccountTrans(custTrans, customerAgreementId, customerAccountId);
		return accountTrans;
	}

	@Override
	public String processUpload() {
		logger.info("**** Bulk Customer Transaction adjustments input listSize=" + accountTransList.size());
		return bulkUploadService.customerTransBulkUpload(accountTransList, processTransList);
	}

}
