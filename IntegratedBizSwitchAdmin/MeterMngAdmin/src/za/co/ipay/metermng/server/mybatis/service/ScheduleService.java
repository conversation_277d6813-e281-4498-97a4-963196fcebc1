package za.co.ipay.metermng.server.mybatis.service;

import java.util.ArrayList;
import java.util.List;

import org.apache.ibatis.session.RowBounds;
import org.apache.log4j.Logger;
import org.springframework.transaction.annotation.Transactional;

import za.co.ipay.accesscontrol.domain.User;
import za.co.ipay.accesscontrol.service.IAccessControlService;
import za.co.ipay.gwt.common.shared.exception.ServiceException;
import za.co.ipay.gwt.common.shared.exception.ValidationException;
import za.co.ipay.gwt.common.shared.exception.ValidationMessage;
import za.co.ipay.metermng.datatypes.RecordStatus;
import za.co.ipay.metermng.mybatis.custom.model.NotifyCustomer;
import za.co.ipay.metermng.mybatis.generated.mapper.ScheduledTaskMapper;
import za.co.ipay.metermng.mybatis.generated.mapper.ScheduledTaskUserMapper;
import za.co.ipay.metermng.mybatis.generated.mapper.TaskClassMapper;
import za.co.ipay.metermng.mybatis.generated.mapper.TaskScheduleMapper;
import za.co.ipay.metermng.mybatis.generated.model.ScheduledTask;
import za.co.ipay.metermng.mybatis.generated.model.ScheduledTaskExample;
import za.co.ipay.metermng.mybatis.generated.model.ScheduledTaskUser;
import za.co.ipay.metermng.mybatis.generated.model.ScheduledTaskUserExample;
import za.co.ipay.metermng.mybatis.generated.model.TaskClass;
import za.co.ipay.metermng.mybatis.generated.model.TaskClassExample;
import za.co.ipay.metermng.mybatis.generated.model.TaskSchedule;
import za.co.ipay.metermng.mybatis.generated.model.TaskScheduleExample;
import za.co.ipay.metermng.server.validation.ServerValidatorUtil;
import za.co.ipay.metermng.shared.dto.customer.CustomerNotifyData;
import za.co.ipay.metermng.shared.dto.schedule.ScheduledTaskDto;
import za.co.ipay.metermng.shared.dto.schedule.ScheduledTaskScreenData;
import za.co.ipay.metermng.shared.dto.user.UserData;

/**
 * ScheduleService is used for the CRUD functionality for the scheduling related data such as TaskSchedule, 
 * Task, etc.
 * 
 * <AUTHOR>
 */
public class ScheduleService {
    
    private TaskScheduleMapper taskScheduleMapper;
    private ScheduledTaskMapper scheduledTaskMapper;
    private TaskClassMapper taskClassMapper;
    private ScheduledTaskUserMapper scheduledTaskUserMapper;
    /** Service to read/write to the access control database. */
    private IAccessControlService accessControlService;   
    /** Service to access the customers. */
    private CustomerService customerService;

    private static Logger logger = Logger.getLogger(ScheduleService.class);
    
    @Transactional(readOnly=true)
    public ScheduledTaskScreenData getTaskScheduleScreenData() throws ServiceException {
        ScheduledTaskScreenData screenData = new ScheduledTaskScreenData();
        
        TaskClassExample example = new TaskClassExample();
        example.createCriteria().andRecordStatusEqualTo(RecordStatus.ACT);
        screenData.setTaskClasses( new ArrayList<TaskClass>(taskClassMapper.selectByExample(example)) );
        
        return screenData;
    }
    
    @Transactional(readOnly=true)
    public Integer getTaskScheduleCount() throws ServiceException {
        TaskScheduleExample example = new TaskScheduleExample();
        TaskScheduleExample.Criteria criteria = example.createCriteria();
        criteria.andRecordStatusNotEqualTo(RecordStatus.DEL);
        return taskScheduleMapper.countByExample(example);
    }
    
    @Transactional(readOnly=true)
    public List<ScheduledTask> getScheduleTasksForTaskSchedule(Long id) {
        if (id != null) {
            ScheduledTaskExample example = new ScheduledTaskExample();
            example.createCriteria().andTaskScheduleIdEqualTo(id);
            return scheduledTaskMapper.selectByExample(example);
        } else {
            return new ArrayList<ScheduledTask>();
        }
    }
    
    @Transactional(readOnly=true)
    public ArrayList<TaskSchedule> getTaskSchedules(int startRow, int pageSize, String sortField, boolean isAscending)
            throws ServiceException {
        logger.info("Getting taskSchedules: "+startRow+" pageSize:"+pageSize+" sortField:"+sortField+" isAscending:"+isAscending);
        TaskScheduleExample example = new TaskScheduleExample();
        example.createCriteria().andRecordStatusNotEqualTo(RecordStatus.DEL);
        example.setOrderByClause(getOrderColumn(sortField, isAscending));
        RowBounds rowBounds = new RowBounds(startRow, pageSize);
        return new ArrayList<TaskSchedule>(taskScheduleMapper.selectByExampleWithRowbounds(example, rowBounds));
    }
    
    @Transactional(readOnly=true)
    public List<TaskSchedule> getTaskSchedules(Boolean active) {
        TaskScheduleExample example = new TaskScheduleExample();
        if (active != null) {
            if (active) {
                example.createCriteria().andRecordStatusEqualTo(RecordStatus.ACT);
            } else {
                example.createCriteria().andRecordStatusEqualTo(RecordStatus.DAC);
            }
        } else {
            example.createCriteria().andRecordStatusNotEqualTo(RecordStatus.DEL);
        }
        return taskScheduleMapper.selectByExample(example);
    }
    
    @Transactional(readOnly=true)
    public TaskSchedule getTaskSchedule(Long id) throws ServiceException {
        if (id != null) {
            return taskScheduleMapper.selectByPrimaryKey(id);
        } else {
            return null;
        }
    }
    
    @Transactional(readOnly=true)
    public ArrayList<ScheduledTaskDto> getScheduleTasks(Long taskScheduleId) throws ValidationException, ServiceException {    
        if (taskScheduleId == null) {
            throw new ValidationException(new ValidationMessage("taskschedule.error.taskschedule.none", true));
        }
        
        List<ScheduledTaskUser> taskUsers = null;
        ArrayList<UserData> users = null;
        ScheduledTaskUserExample usersExample = null;
        User acUser = null;
        
        List<ScheduledTask> scheduledTasks = getScheduleTasksForTaskSchedule(taskScheduleId);
        ArrayList<ScheduledTaskDto> tasks = new ArrayList<ScheduledTaskDto>();
        ScheduledTaskDto scheduledTaskDto = null;
        TaskClass taskClass = null;
        for(ScheduledTask task : scheduledTasks) {
            //Task and its TaskClass
            scheduledTaskDto = new ScheduledTaskDto();
            scheduledTaskDto.setScheduledTask(task);
            taskClass = taskClassMapper.selectByPrimaryKey(scheduledTaskDto.getScheduledTask().getTaskClassId());
            scheduledTaskDto.setTaskClass((taskClass != null ? taskClass.getTaskClassName() : ""));
            
            //Users
            usersExample = new ScheduledTaskUserExample();
            usersExample.createCriteria().andScheduledTaskIdEqualTo(scheduledTaskDto.getScheduledTask().getId());
            taskUsers = scheduledTaskUserMapper.selectByExample(usersExample);
            users = new ArrayList<UserData>(taskUsers.size());
            for(ScheduledTaskUser taskUser : taskUsers) {
                acUser = accessControlService.findUserById(taskUser.getUserId());
                if (acUser != null) {
                    users.add(new UserData(acUser.getId(), acUser.getUsername(), acUser.getEmail()));
                } else {
                    logger.info("Unknown user for id: "+taskUser.getUserId());
                }
            }
            scheduledTaskDto.setUsers(users);
            
            //Customer
            if (task.getCustomerAccountId() != null) {
                NotifyCustomer customer = customerService.getNotifyCustomer(task.getCustomerAccountId());
                if (customer != null) {
                    CustomerNotifyData data = new CustomerNotifyData();
                    data.setCustomerId(customer.getCustomerId());
                    data.setFirstName(customer.getFirstName());
                    data.setLastName(customer.getLastName());
                    data.setCustomerAccountId(customer.getCustomerAccountId());
                    data.setNotifyEmail(customer.getNotifyEmail());
                    data.setNotifySms(customer.getNotifySms());
                    scheduledTaskDto.setCustomer(data);
                }
            }
            
            tasks.add(scheduledTaskDto);
        }        
        return tasks;
    }
    
    private String getOrderColumn(String sortField, boolean isAscending) {
        String orderColumn = "task_schedule_name";
        if (sortField != null && !sortField.trim().equals("")) {
            if ("name".equals(sortField)) {
                orderColumn = "task_schedule_name";
            } else if ("status".equals(sortField)) {
                orderColumn = "record_status";
            } else if ("task".equals(sortField)) {
                orderColumn = "scheduled_task_id";
            }
                
        }
        return orderColumn + " " + getOrder(isAscending);
    }
    
    private String getOrder(boolean isAscending) {
        if (isAscending) {
            return "asc";
        } else {
            return "desc";
        }
    }
    
    @Transactional(readOnly=false)
    public void saveTaskSchedule(TaskSchedule taskSchedule) throws ValidationException, ServiceException {
        //Validate input
        ServerValidatorUtil.getInstance().validateDataForValidationMessages(taskSchedule);
        //Save Task Schedule
        if (taskSchedule.getId() == null) {
            if (taskScheduleMapper.insert(taskSchedule) != 1) {
                throw new ServiceException("taskschedule.error.taskschedule.save", true);
            }
        } else {
            if (taskScheduleMapper.updateByPrimaryKey(taskSchedule) != 1) {
                throw new ServiceException("taskschedule.error.taskschedule.save", true);
            }
        }
    }

    @Transactional(readOnly=false)
    public void saveScheduledTask(ScheduledTaskDto scheduledTaskDto) throws ValidationException, ServiceException {
        //Validate input
        ServerValidatorUtil.getInstance().validateDataForValidationMessages(scheduledTaskDto.getScheduledTask());        
        //Save Scheduled Task
        logger.info("Saving Task: "+scheduledTaskDto.getScheduledTask().getScheduledTaskName());
        if (scheduledTaskDto.getScheduledTask().getId() == null) {
            if (scheduledTaskMapper.insert(scheduledTaskDto.getScheduledTask()) != 1) {
                throw new ServiceException("taskschedule.error.scheduledtask.save", true);
            } 
        } else {
            if (scheduledTaskMapper.updateByPrimaryKey(scheduledTaskDto.getScheduledTask()) != 1) {
                throw new ServiceException("taskschedule.error.scheduledtask.save", true);
            } 
        }
        
        //Users for the Scheduled Task
        List<ScheduledTaskUser> current = getScheduledTaskUsers(scheduledTaskDto.getScheduledTask().getId());
        List<Long> existingUserIds = new ArrayList<Long>(); 
        boolean found = false;
        for(ScheduledTaskUser user : current) {
            found = false;
            for(UserData u : scheduledTaskDto.getUsers()) {
                if (u.getId().equals(user.getUserId())) {
                    found = true;
                    existingUserIds.add(user.getUserId());
                    break;
                }
            }
            if (!found) {
                if (scheduledTaskUserMapper.deleteByPrimaryKey(user.getId()) != 1) {
                    throw new ServiceException("taskschedule.error.scheduledtaskuser.delete", true);
                }
                logger.info("Deleted existing task user: "+user.getId());
            }
        }
        
        //Save any new ones
        for(UserData u : scheduledTaskDto.getUsers()) {
            if (!existingUserIds.contains(u.getId())) {
                ScheduledTaskUser stu = new ScheduledTaskUser();
                stu.setScheduledTaskId(scheduledTaskDto.getScheduledTask().getId());
                stu.setUserId(u.getId());
                if (scheduledTaskUserMapper.insert(stu) != 1) {
                    throw new ServiceException("taskschedule.error.scheduledtaskuser.save", true);
                }
                logger.info("Saved task user: "+u.getUsername());
            } else {
                logger.info("Skipping existing task user: "+u.getId());
            }
        }
        logger.info("Saved users for Task");
    }
    
    @Transactional(readOnly=true)
    public List<ScheduledTaskUser> getScheduledTaskUsers(Long scheduledTaskId) {
        if (scheduledTaskId != null) {
            ScheduledTaskUserExample example = new ScheduledTaskUserExample();
            example.createCriteria().andScheduledTaskIdEqualTo(scheduledTaskId);
            return scheduledTaskUserMapper.selectByExample(example);
        } else {
            return new ArrayList<ScheduledTaskUser>();
        }
    }
    
    @Transactional(readOnly=false)
    public void deleteScheduledTask(Long scheduledTaskId) throws ValidationException, ServiceException {
        ScheduledTask task = null;
        if (scheduledTaskId != null) {
            task = scheduledTaskMapper.selectByPrimaryKey(scheduledTaskId);           
        } 
        if (task == null) {
            throw new ValidationException(new ValidationMessage("scheduledtask.error.delete.none", true));
        }
        
        ScheduledTaskUserExample example = new ScheduledTaskUserExample();
        example.createCriteria().andScheduledTaskIdEqualTo(scheduledTaskId);
        scheduledTaskUserMapper.deleteByExample(example);
        
        if (scheduledTaskMapper.deleteByPrimaryKey(scheduledTaskId) != 1) {
            throw new ServiceException("scheduledtask.error.delete", true);
        }
    }

    public void setTaskScheduleMapper(TaskScheduleMapper taskScheduleMapper) {
        this.taskScheduleMapper = taskScheduleMapper;
    }

    public void setScheduledTaskMapper(ScheduledTaskMapper scheduledTaskMapper) {
        this.scheduledTaskMapper = scheduledTaskMapper;
    }

    public void setTaskClassMapper(TaskClassMapper taskClassMapper) {
        this.taskClassMapper = taskClassMapper;
    }

    public void setScheduledTaskUserMapper(ScheduledTaskUserMapper scheduledTaskUserMapper) {
        this.scheduledTaskUserMapper = scheduledTaskUserMapper;
    }

    public void setAccessControlService(IAccessControlService accessControlService) {
        this.accessControlService = accessControlService;
    }

    public void setCustomerService(CustomerService customerService) {
        this.customerService = customerService;
    }
}
