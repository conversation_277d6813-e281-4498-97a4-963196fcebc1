package za.co.ipay.metermng.server.servlet;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Locale;

import javax.servlet.ServletConfig;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.log4j.Logger;
import org.springframework.context.ApplicationContext;
import org.springframework.context.support.DefaultMessageSourceResolvable;
import org.springframework.web.context.WebApplicationContext;

import za.co.ipay.gwt.common.shared.exception.ServiceException;
import za.co.ipay.metermng.datatypes.UnitsTransTypeE;
import za.co.ipay.metermng.mybatis.generated.model.UnitsTrans;
import za.co.ipay.metermng.server.mybatis.service.UsagePointService;
import za.co.ipay.utils.files.CsvWriter;

public class UsagePointUnitsAccountTransExportDataServlet extends SuperExportDataServlet {

    private static final long serialVersionUID = 1L;
    private static Logger logger = Logger.getLogger(UsagePointUnitsAccountTransExportDataServlet.class.getName());

    private DateFormat filednameDate = new SimpleDateFormat("yyyyMMddHHmmss");

    private String filterValue;
    private String filterElement;
    private Locale requestLocale;
    private SimpleDateFormat dateTimeFormat;
    private UsagePointService usagePointService;

    @Override
    public void init(ServletConfig config) throws ServletException {
        super.init(config);
        this.usagePointService = (UsagePointService) ((ApplicationContext) config.getServletContext()
                .getAttribute(WebApplicationContext.ROOT_WEB_APPLICATION_CONTEXT_ATTRIBUTE))
                        .getBean("usagePointService");
        if (usagePointService == null || messageSource == null || formatSource == null) {
            throw new ServletException("Missing beans not set in the UsagePointUnitsAccountTransExportDataServlet: "
                    + usagePointService + " " + messageSource + " " + formatSource);
        }
    }

    public void doPost(HttpServletRequest request, HttpServletResponse response) throws IOException, ServletException {
        doDataExport(request, response);
    }

    public void doGet(HttpServletRequest request, HttpServletResponse response) throws IOException, ServletException {
        doDataExport(request, response);
    }

    public void doDataExport(HttpServletRequest request, HttpServletResponse response) throws IOException {
        requestLocale = request.getLocale();
        filterValue = request.getParameter("filtervalue");
        filterElement = request.getParameter("filterelement");
        dateTimeFormat = new SimpleDateFormat(
                formatSource.getMessage(new DefaultMessageSourceResolvable("datetime.pattern"), requestLocale));
        try {
            CsvWriter csvContents = null;
            if (getCurrentUser(request) != null) {
                csvContents = buildData(request.getParameter("unitsaccountid"), isFilterApplied());
                if (csvContents == null) {
                    writeError(response, messageSource
                            .getMessage(new DefaultMessageSourceResolvable("export.error.nodata"), requestLocale));
                } else {
                    logger.info("Writing csv content...");
                    response.setContentType("text/csv");
                    response.setHeader("Content-Disposition",
                            "attachment; fileName=\"" + getExportFileName(request) + "\"");
                    response.setHeader("content-Length",
                            String.valueOf(stream(new ByteArrayInputStream(csvContents.getCSVContents().getBytes()),
                                    response.getOutputStream())));
                    logger.info("Completed export");
                }
            } else {
                logger.error("No session/logged in user found.");
                writeError(response,
                        messageSource.getMessage(new DefaultMessageSourceResolvable("export.denied"), requestLocale));
            }
        } catch (Exception e) {
            logger.error("Error exporting data:", e);
            writeError(response,
                    messageSource.getMessage(new DefaultMessageSourceResolvable("export.error"), requestLocale));
        }
    }

    private CsvWriter buildData(String unitsAccountId, boolean hasFilter) {
        try {
            List<ArrayList<String>> list = new ArrayList<ArrayList<String>>();
            for (UnitsTrans unitsTrans : usagePointService.getUnitsAccountTransactions(new Long(unitsAccountId))) {
                if (!hasFilter || isValid(unitsTrans)) {
                    ArrayList<String> exportDataRow = new ArrayList<String>();
                    exportDataRow.add(dateTimeFormat.format(unitsTrans.getDateEntered()));
                    exportDataRow.add(unitsTrans.getUserRecEntered());
                    exportDataRow.add(messageSource.getMessage(
                            new DefaultMessageSourceResolvable("units.transaction.type."
                                    + UnitsTransTypeE.fromId(unitsTrans.getUnitsTransTypeId()).name().toLowerCase()),
                            requestLocale));
                    exportDataRow.add(dateTimeFormat.format(unitsTrans.getTransDate()));
                    exportDataRow.add(unitsTrans.getComment());
                    exportDataRow.add(unitsTrans.getOurRef());
                    BigDecimal oneThousand = new BigDecimal("1000");
                    exportDataRow.add(
                            unitsTrans.getUnitsAmt().divide(oneThousand).setScale(1, RoundingMode.UP).toPlainString());
                    exportDataRow.add(unitsTrans.getResultantBalance().divide(oneThousand).setScale(1, RoundingMode.UP)
                            .toPlainString());
                    list.add(exportDataRow);
                }
            }
            return ExportDataUtil.getUsagePointUnitsAccountTransactionDetails(messageSource, formatSource,
                    requestLocale, list);
        } catch (ServiceException e) {
            logger.error("Error getting export data:", e);
        }
        return null;
    }

    private boolean isValidDateFilter(String filterDates[], Date date) {
        // will check if the date is within the two filter dates. adding 1 day to the
        // end date to make the date inclusive
        return !((!filterDates[0].isEmpty() && new Date(Long.parseLong(filterDates[0])).after(date))
                || (filterDates.length == 2 && new Date(Long.parseLong(filterDates[1]) + ********).before(date)));
    }

    private boolean isValid(UnitsTrans value) {
        if (filterElement.equals(
                messageSource.getMessage(new DefaultMessageSourceResolvable("customer.txn.trans.date"), requestLocale))
                || filterElement.equals("Transaction Date")) {
            return isValidDateFilter(filterValue.split(","), value.getTransDate());
        } else if (filterElement.equals(
                messageSource.getMessage(new DefaultMessageSourceResolvable("units.transaction.type"), requestLocale))
                || filterElement.equals("Units Transaction Type")) {
            return messageSource
                    .getMessage(
                            new DefaultMessageSourceResolvable("units.transaction.type."
                                    + UnitsTransTypeE.fromId(value.getUnitsTransTypeId()).name().toLowerCase()),
                            requestLocale)
                    .toLowerCase().contains(filterValue.toLowerCase());
        } else if (filterElement.equals(
                messageSource.getMessage(new DefaultMessageSourceResolvable("customer.txn.our.ref"), requestLocale))
                || filterElement.equals("Our Reference")) {
            return value.getOurRef().toLowerCase().contains(filterValue.toLowerCase());
        }
        return false;
    }

    private boolean isFilterApplied() {
        if (filterElement != null && !filterElement.trim().isEmpty()) {
            if (filterValue != null && !filterValue.trim().isEmpty()) {
                return true;
            }
        }
        return false;
    }

    public String getExportFileName(HttpServletRequest request) {
        StringBuilder sb = new StringBuilder();
        sb.append(ExportDataUtil.getFileName(request.getParameter("filenameprefix")));
        sb.append("_");
        sb.append(filednameDate.format(new Date()));
        sb.append(".csv");
        return sb.toString();
    }

}
