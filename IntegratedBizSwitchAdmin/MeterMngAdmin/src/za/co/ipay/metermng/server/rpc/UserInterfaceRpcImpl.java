package za.co.ipay.metermng.server.rpc;

import java.util.List;
import java.util.Map;

import org.apache.log4j.Logger;

import za.co.ipay.metermng.client.rpc.UserInterfaceRpc;
import za.co.ipay.metermng.mybatis.generated.model.Form;
import za.co.ipay.metermng.mybatis.generated.model.FormFields;
import za.co.ipay.metermng.server.mybatis.service.UserInterfaceService;

public class UserInterfaceRpcImpl extends BaseMeterMngRpc implements UserInterfaceRpc {

    private static final long serialVersionUID = 1L;

    private UserInterfaceService userInterfaceService;

    public UserInterfaceRpcImpl() {
        this.logger = Logger.getLogger(UserInterfaceRpcImpl.class);
    }

    @Override
    public List<Form> getForms() {
        return userInterfaceService.getForms();
    }

    @Override
    public List<FormFields> getFormFieldsList() {
        return userInterfaceService.getFormFieldsList();
    }

    @Override
    public void saveFormFields(Map<Long, FormFields> formFields) {
        userInterfaceService.saveFormFields(formFields);
    }

    public void setUserInterfaceService(UserInterfaceService userInterfaceService) {
        this.userInterfaceService = userInterfaceService;
    }

    @Override
    public Map<String, FormFields> getFormFields() {
        return userInterfaceService.getFormFields();
    }
}
