package za.co.ipay.metermng.server.mybatis.service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import org.apache.log4j.Logger;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;
import za.co.ipay.gwt.common.shared.exception.AccessControlException;
import za.co.ipay.gwt.common.shared.exception.ServiceException;
import za.co.ipay.gwt.common.shared.exception.ValidationException;
import za.co.ipay.gwt.common.shared.exception.ValidationMessage;
import za.co.ipay.metermng.datatypes.RecordStatus;
import za.co.ipay.metermng.mybatis.custom.mapper.GenGroupCustomMapper;
import za.co.ipay.metermng.mybatis.custom.mapper.GroupHierarchyCustomMapper;
import za.co.ipay.metermng.mybatis.custom.mapper.IGenGroupMapper;
import za.co.ipay.metermng.mybatis.custom.mapper.IGroupTypeMapper;
import za.co.ipay.metermng.mybatis.custom.model.GenGroupClosureCombo;
import za.co.ipay.metermng.mybatis.custom.model.GenGroupParent;
import za.co.ipay.metermng.mybatis.custom.model.GroupClosureCombo;
import za.co.ipay.metermng.mybatis.custom.model.IdPair;
import za.co.ipay.metermng.mybatis.generated.mapper.CustAccNotifyMapper;
import za.co.ipay.metermng.mybatis.generated.mapper.CustomerAccThresholdsMapper;
import za.co.ipay.metermng.mybatis.generated.mapper.GenGroupMapper;
import za.co.ipay.metermng.mybatis.generated.mapper.GroupEntityMapper;
import za.co.ipay.metermng.mybatis.generated.mapper.GroupFeatureMapper;
import za.co.ipay.metermng.mybatis.generated.mapper.GroupHierarchyMapper;
import za.co.ipay.metermng.mybatis.generated.mapper.GroupTypeFeatureMapper;
import za.co.ipay.metermng.mybatis.generated.mapper.GroupTypeMapper;
import za.co.ipay.metermng.mybatis.generated.model.AppSetting;
import za.co.ipay.metermng.mybatis.generated.model.CustAccNotify;
import za.co.ipay.metermng.mybatis.generated.model.CustomerAccThresholds;
import za.co.ipay.metermng.mybatis.generated.model.GenGroup;
import za.co.ipay.metermng.mybatis.generated.model.GenGroupExample;
import za.co.ipay.metermng.mybatis.generated.model.GenGroupExample.Criteria;
import za.co.ipay.metermng.mybatis.generated.model.GroupEntity;
import za.co.ipay.metermng.mybatis.generated.model.GroupEntityExample;
import za.co.ipay.metermng.mybatis.generated.model.GroupFeature;
import za.co.ipay.metermng.mybatis.generated.model.GroupFeatureExample;
import za.co.ipay.metermng.mybatis.generated.model.GroupHierarchy;
import za.co.ipay.metermng.mybatis.generated.model.GroupHierarchyExample;
import za.co.ipay.metermng.mybatis.generated.model.GroupType;
import za.co.ipay.metermng.mybatis.generated.model.GroupTypeExample;
import za.co.ipay.metermng.mybatis.generated.model.GroupTypeFeature;
import za.co.ipay.metermng.mybatis.generated.model.GroupTypeFeatureExample;
import za.co.ipay.metermng.mybatis.generated.model.NdpSchedule;
import za.co.ipay.metermng.server.validation.ServerValidatorUtil;
import za.co.ipay.metermng.shared.GenGroupData;
import za.co.ipay.metermng.shared.MeterMngStatics;
import za.co.ipay.metermng.shared.NdpScheduleData;
import za.co.ipay.metermng.shared.UpGenGroupLinkDataNames;
import za.co.ipay.metermng.shared.dto.UpGenGroupLinkData;
import za.co.ipay.metermng.shared.dto.uploaddata.metadata.GisMetadata;
import za.co.ipay.metermng.shared.group.GroupTypeData;
import za.co.ipay.utils.validation.ValidationUtils;

public class GroupService {

    private static final String GROUP_TYPE_NAME_COLUMN = "group_type_name";
    private static final String GROUP_TYPE_LAYOUT_ORDER_COLUMN = "layout_order";
    private static final String ID_COLUMN = "group_hierarchy_id";
    private static final String PARENT_ID_COLUMN = "parent_id";
    private static Logger logger = Logger.getLogger(GroupService.class);

    private GroupTypeMapper groupTypeMapper;
    private IGroupTypeMapper iGroupTypeMapper;
    private GroupHierarchyMapper groupHierarchyMapper;
    private GroupHierarchyCustomMapper groupHierarchyCustomMapper;
    private GroupEntityMapper groupEntityMapper;
    private GenGroupMapper genGroupMapper;
    private IGenGroupMapper iGenGroupMapper;
    private GroupFeatureMapper groupFeatureMapper;
    private GroupTypeFeatureMapper groupTypeFeatureMapper;
    private CustomerAccThresholdsMapper customerAccThresholdsMapper;
    private CustAccNotifyMapper custAccNotifyMapper;
    private GenGroupCustomMapper genGroupCustomMapper;
    private UserGroupService userGroupService;
    private CustomerService customerService;
    private UsagePointService usagePointService;
    private AuxChargeScheduleService auxChargeScheduleService;
    private PricingStructureService pricingStructureService;
    private DeviceStoreService deviceStoreService;
    private UpGenGroupLnkService upGenGroupLnkService;
    private NdpService ndpService;
    private AppSettingService appSettingService;

    @Transactional(readOnly = true)
    public Map<String, GroupType> getGroupTypes() {
        GroupTypeExample example = new GroupTypeExample();
        example.createCriteria().andRecordStatusEqualTo(RecordStatus.ACT);
        example.setOrderByClause(GROUP_TYPE_NAME_COLUMN);
        List<GroupType> groupTypeList = groupTypeMapper.selectByExample(example);

        Map<String, GroupType> groupTypeMap = new HashMap<String, GroupType>();
        if (groupTypeList != null && !groupTypeList.isEmpty()) {
            for (GroupType gt : groupTypeList) {
                groupTypeMap.put(gt.getName(), gt);
            }
        }
        return groupTypeMap;
    }

    @Transactional(readOnly = true)
    public List<GroupFeature> getAllGroupFeatures() {
        GroupFeatureExample example = new GroupFeatureExample();
        example.createCriteria().andRecordStatusNotEqualTo(RecordStatus.DEL);
        example.setOrderByClause("group_feature_value asc");
        return groupFeatureMapper.selectByExample(example);
    }

    @Transactional(readOnly = true)
    public ArrayList<GroupTypeData> getGroupTypesData() {
        GroupTypeExample example = new GroupTypeExample();
        example.createCriteria().andRecordStatusNotEqualTo(RecordStatus.DEL);
        example.setOrderByClause(GROUP_TYPE_NAME_COLUMN);
        List<GroupType> groupTypeList = new ArrayList<GroupType>();
        groupTypeList = groupTypeMapper.selectByExample(example);

        return addGroupTypeExtra(groupTypeList);
    }

    //RC note dup in OfflineMeterCustUpImportServices
   @Transactional(readOnly = true)
    private ArrayList<GroupTypeData> addGroupTypeExtra(List<GroupType> groupTypeList) {
        ArrayList<GroupTypeData> groupTypeDataList = new ArrayList<GroupTypeData>();
        for (GroupType gt : groupTypeList) {
            GroupTypeData gtd = new GroupTypeData(gt);

            List<GroupHierarchy> hierarchyList = getGroupHierarchies(gt.getId());
            if (hierarchyList != null && !hierarchyList.isEmpty()) {
                gtd.setHierarchyDefined(true);
                gtd.setHierarchyList(hierarchyList);
            }
            if (gtd.isHierarchyDefined()) {
                for (GroupHierarchy gh : hierarchyList) {
                    GenGroupExample ggExample = new GenGroupExample();
                    ggExample.createCriteria().andGroupHierarchyIdEqualTo(gh.getId());
                    List<GenGroup> genGroups = genGroupMapper.selectByExample(ggExample);
                    if (genGroups.size() > 0) {
                        gtd.setGenGroupDefined(true);
                        break;
                    }
                }
            }

            //for location Group, if Required, get the appsetting for the last level required as well...
            if (gt.isLocationGroup() && gt.isRequired()) {
                AppSetting appSetting = appSettingService.getAppSettingByKey(MeterMngStatics.APP_SETTING_LOCATION_REQUIRED_TO_ALL_LEVELS);
                if (appSetting != null && appSetting.getValue().equalsIgnoreCase("true")) {
                    gtd.setSelectLastLevelLocationGroup(true);
                }
            }

            //get features & create featuresList
            GroupTypeFeatureExample gtfExample = new GroupTypeFeatureExample();
            gtfExample.createCriteria().andGroupTypeIdEqualTo(gt.getId());
            List<GroupTypeFeature> gtfList = groupTypeFeatureMapper.selectByExample(gtfExample);
            ArrayList<GroupFeature> featureList = new ArrayList<GroupFeature>();
            for (GroupTypeFeature gtf : gtfList) {
                GroupFeature gf = groupFeatureMapper.selectByPrimaryKey(gtf.getGroupFeatureId());
                featureList.add(gf);
            }
            gtd.setFeaturesList(featureList);

            groupTypeDataList.add(gtd);
        }
        return groupTypeDataList;
    }

    @Transactional
    public GroupType getGroupTypeByName(String name) {
        if (ValidationUtils.isNotNullOrBlank(name)) {
            GroupTypeExample example = new GroupTypeExample();
            example.createCriteria().andNameEqualTo(name);
            List<GroupType> types = groupTypeMapper.selectByExample(example);
            if (types.size() > 0) {
                return types.get(0);
            } else {
                return null;
            }
        } else {
            return null;
        }
    }

    @Transactional
    public GroupType getGroupTypeById(Long groupTypeId) {
        GroupTypeExample example = new GroupTypeExample();
        example.createCriteria().andIdEqualTo(groupTypeId);
        List<GroupType> types = groupTypeMapper.selectByExample(example);
        if (types.size() > 0) {
            return types.get(0);
        } else {
            return null;
        }
    }

    @Transactional
    public GroupType getGroupTypeForGroupHierarchy(Long groupHierarchyId) {
        if (groupHierarchyId != null) {
            GroupHierarchy hierarchy = groupHierarchyMapper.selectByPrimaryKey(groupHierarchyId);
            if (hierarchy != null) {
                return groupTypeMapper.selectByPrimaryKey(hierarchy.getGroupTypeId());
            }
        }
        return null;
    }

    @Transactional(readOnly = true)
    public List<GroupTypeData> getGroupTypesWithHierarchy() throws ServiceException {
        List<GroupType> groupTypeList = iGroupTypeMapper.getGroupTypesWithHierarchy();
        return addGroupTypeExtra(groupTypeList);
    }

    //RC note dup in OfflineMeterCustUpImportServices
    @Transactional(readOnly = true)
    public List<GroupTypeData> getActiveGroupTypesWithHierarchyExclAccessGrps() throws ServiceException {
        List<GroupType> groupTypeList = iGroupTypeMapper.getActiveGroupTypesWithHierarchyExclAccessGrps();
        return addGroupTypeExtra(groupTypeList);
    }

    @Transactional(readOnly = true)
    public List<GroupTypeData> getUsagepointGroupTypesWithHierarchy() throws ServiceException {
        List<GroupType> groupTypeList = iGroupTypeMapper.getUsagepointGroupTypesWithHierarchy();
        return addGroupTypeExtra(groupTypeList);
    }

    @Transactional(readOnly = true)
    public List<GroupType> getGroupTypesWithNoHierarchy() throws ServiceException {
        return iGroupTypeMapper.getGroupTypesWithNoHierarchy();
    }

    //RC note dup in OfflineMeterCustUpImportServices
    @Transactional(readOnly = true)
    public List<GroupHierarchy> getGroupHierarchies(Long groupTypeId) throws ServiceException {
        GroupHierarchyExample example = new GroupHierarchyExample();
        example.createCriteria().andRecordStatusNotEqualTo(RecordStatus.DEL).andGroupTypeIdEqualTo(groupTypeId);
        example.setOrderByClause(ID_COLUMN);
        return groupHierarchyMapper.selectByExample(example);
    }

    @Transactional(readOnly = true)
    public GroupHierarchy getGroupHierarchy(Long groupHierarchyId) {
        if (groupHierarchyId != null) {
            return groupHierarchyMapper.selectByPrimaryKey(groupHierarchyId);
        } else {
            return null;
        }
    }

    @Transactional(readOnly = true)                 //all three middle ones
    public GroupType getAccessGroupGroupType() {
        GroupTypeExample example = new GroupTypeExample();
        example.createCriteria().andAccessGroupEqualTo(true);
        List<GroupType> types = groupTypeMapper.selectByExample(example);
        if (types.size() == 1) {
            return types.get(0);
        } else if (types.size() > 0) {
            logger.error("Expected only one access group for groupType but found: " + types.size());
        }
        return null;
    }

    @Transactional(readOnly = true)
    public GroupType getLocationGroupGroupType() {
        GroupTypeExample example = new GroupTypeExample();
        example.createCriteria().andLocationGroupEqualTo(true);
        List<GroupType> types = groupTypeMapper.selectByExample(example);
        if (types.size() == 1) {
            return types.get(0);
        } else if (types.size() > 0) {
            logger.error("Expected only one location group for groupType but found: " + types.size());
        }
        return null;
    }

    @Transactional(readOnly = true)
    public GenGroup getDefaultUserGroup() {
        GroupType type = getAccessGroupGroupType();
        if (type != null) {
            GroupHierarchyExample example = new GroupHierarchyExample();
            example.createCriteria().andGroupTypeIdEqualTo(type.getId()).andParentIdIsNull();
            List<GroupHierarchy> hierarchies = groupHierarchyMapper.selectByExample(example);
            if (hierarchies.size() == 1) {
                GroupHierarchy hierarchy = hierarchies.get(0);
                GenGroupExample groupExample = new GenGroupExample();
                groupExample.createCriteria().andGroupHierarchyIdEqualTo(hierarchy.getId()).andParentIdIsNull();
                List<GenGroup> groups = genGroupMapper.selectByExample(groupExample);
                if (groups.size() == 1) {
                    return groups.get(0);
                } else {
                    logger.error("Expected 1 group for groupHierarchy:" + hierarchy.getId() + " " + hierarchy.getName() + " but got: " + groups.size());
                }
            } else {
                logger.error("Expected 1 groupHierarchy for groupType:" + type.getId() + " " + type.getName() + " but got: " + hierarchies.size());
            }
        } else {
            logger.error("No groupType for the access group!");
        }
        return null;
    }

    @Transactional
    @PreAuthorize("hasRole('mm_group_type_admin')")
    public Boolean updateGroupType(GroupTypeData groupTypeData) throws ValidationException, ServiceException {
        ServerValidatorUtil.getInstance().validateDataForValidationMessages(groupTypeData);
        logger.debug("Saving groupType id:" + groupTypeData.getId() + " name:" + groupTypeData.getName() + " isAccessGroup:" + groupTypeData.isAccessGroup() + " recordStatus=" + groupTypeData.getRecordStatus());

        //Unique name?
        GroupType existing = getGroupTypeByName(groupTypeData.getName());
        if (existing != null && !existing.getId().equals(groupTypeData.getId())) {
            throw new ValidationException(new ValidationMessage("grouptype.error.name.duplicate", true));
        }

        //Access group checking
        boolean accessGroupChanging = false;
        GroupType current = getAccessGroupGroupType();
        if (groupTypeData.isAccessGroup() && current != null && !current.getId().equals(groupTypeData.getId())) {
            accessGroupChanging = true;
            logger.info("Becoming new accessGroup: " + groupTypeData.getName());
        }
        if (!groupTypeData.isAccessGroup() && current != null && current.getId().equals(groupTypeData.getId())) {
            accessGroupChanging = true;
            logger.info("Was previously accessGroup: " + groupTypeData.getName());
        }
        if (accessGroupChanging) {
            checkExistingLinkedGroups();
            iGroupTypeMapper.resetAccessGroup(Boolean.FALSE);
        }

        //Check that cannot change required switch if already have gen-groups defined
        if (existing != null && existing.isRequired() != groupTypeData.isRequired()) {
            List<GroupHierarchy> hierarchyList = getGroupHierarchies(groupTypeData.getId());
            if (hierarchyList != null && !hierarchyList.isEmpty()) {
                for (GroupHierarchy gh : hierarchyList) {
                    GenGroupExample ggExample = new GenGroupExample();
                    ggExample.createCriteria().andGroupHierarchyIdEqualTo(gh.getId());
                    List<GenGroup> genGroups = genGroupMapper.selectByExample(ggExample);
                    if (genGroups.size() > 0) {
                        throw new ValidationException(new ValidationMessage("grouptype.error.cannot.change.required", true));
                    }
                }
            }
        }

        //validate GroupFeatures
        //Cannot be set for Access / Location groups
        ArrayList<GroupFeature> featuresList = groupTypeData.getFeaturesList();
        if (featuresList != null && featuresList.size() > 0
                && (groupTypeData.isAccessGroup() || groupTypeData.isLocationGroup())) {
            throw new ValidationException(new ValidationMessage("grouptype.error.cannot.set.feature.for.group", true));
        }

        //get list of EXISTING grouptype features for this id
        List<GroupTypeFeature> existingGtfList = new ArrayList<GroupTypeFeature>();
        if (existing != null) {
            GroupTypeFeatureExample gtfExample = new GroupTypeFeatureExample();
            gtfExample.createCriteria().andGroupTypeIdEqualTo(existing.getId());
            existingGtfList = groupTypeFeatureMapper.selectByExample(gtfExample);
        }
        //build lists of existing and of new features
        ArrayList<Long> existingFeaturesIdList = new ArrayList<Long>();
        for (GroupTypeFeature existingGtf : existingGtfList) {
            existingFeaturesIdList.add(existingGtf.getGroupFeatureId());
        }
        ArrayList<Long> newFeaturesIdList = new ArrayList<Long>();
        if (featuresList != null) {
            for (GroupFeature newGf : featuresList) {
                newFeaturesIdList.add(newGf.getId());
            }
        }

        //Establish if any existing features were unset -->
        for (GroupTypeFeature existingGtf : existingGtfList) {
            Long existingFeatureId = existingGtf.getGroupFeatureId();
            if (!newFeaturesIdList.contains(existingFeatureId)) {
                //has been unset
                //for CustomerAccount Thresholds cannot unset if : there are feature values already in one of the gen_group_entities
                GroupFeature existingGroupFeature = groupFeatureMapper.selectByPrimaryKey(existingGtf.getGroupFeatureId());
                if (existingGroupFeature.getGroupFeatureValue().contains("CUSTOMER_ACC_THRESHOLDS")
                        || existingGroupFeature.getGroupFeatureValue().contains("NDP")) {
                    Long maxHierarchyId = iGenGroupMapper.getGroupTypeMaxGroupHierarchyId(existingGtf.getGroupTypeId());
                    //max Hierarchy gives leaf nodes
                    if (maxHierarchyId != null) {
                        ArrayList<GenGroup> genGroupList = iGenGroupMapper.getGenGroupIdsByGroupHierarchyId(maxHierarchyId);
                        //now have all gen groups defined at leaf node level
                        if (genGroupList != null && !genGroupList.isEmpty()) {
                            //reject if any have a Customer Acc Threshold id
                            boolean fail = false;
                            for (GenGroup ggLeaf : genGroupList) {
                                if (existingGroupFeature.getGroupFeatureValue().contains("CUSTOMER_ACC_THRESHOLDS") && ggLeaf.getCustomerAccThresholdsId() != null) {
                                    fail = true;
                                }
                                if (existingGroupFeature.getGroupFeatureValue().contains("NDP") && ggLeaf.getNdpScheduleId() != null) {
                                    fail = true;
                                }
                            }
                            if (fail) {
                                throw new ValidationException(new ValidationMessage("grouptype.error.cannot.remove.feature", true));
                            }
                        }
                    }  //end max-hierarchy null check
                }  //end check CUSTOMER_ACC_THRESHOLDS
            }   //end if existing not contained in new list
        } //end of looping through existing features

        //Establish if any new features were added, not there before
        //If find a new one and  multiselect, move on 
        //if NOT multiselect, now go see if any other group types have the setting, if so, reject
        if (featuresList != null) {
            for (GroupFeature newGf : featuresList) {
                Long newFeatureId = newGf.getId();
                if (!existingFeaturesIdList.contains(newFeatureId) && !newGf.isSupportsMultiInstance()) {
                    GroupTypeFeatureExample checkGtfExample = new GroupTypeFeatureExample();
                    checkGtfExample.createCriteria().andGroupFeatureIdEqualTo(newFeatureId);
                    List<GroupTypeFeature> groupTypebyFeatureList = groupTypeFeatureMapper
                            .selectByExample(checkGtfExample);
                    if (groupTypebyFeatureList != null && groupTypebyFeatureList.size() > 0) {
                        throw new ValidationException(
                                new ValidationMessage("grouptype.error.feature.not.multi.instance", true));
                    }
                }
            }
        }

        //All VALID now can save
        //Save the group type
        GroupType newGT = saveGroupType(groupTypeData.getGroupType());
        groupTypeData.setId(newGT.getId());

        //update the groupFeatureLink
        for (GroupTypeFeature existingGtf : existingGtfList) {
            Long existingFeatureId = existingGtf.getGroupFeatureId();
            if (!newFeaturesIdList.contains(existingFeatureId)) {
                //delete the existing feature link
                if (groupTypeFeatureMapper.deleteByPrimaryKey(existingGtf.getId()) != 1) {
                    throw new ServiceException("grouptype.error.save.feature");
                }
            }
        }
        if (featuresList != null) {
            for (GroupFeature newGf : featuresList) {
                Long newFeatureId = newGf.getId();
                if (!existingFeaturesIdList.contains(newFeatureId)) {
                    // make a new link
                    GroupTypeFeature record = new GroupTypeFeature();
                    record.setGroupTypeId(groupTypeData.getId());
                    record.setGroupFeatureId(newFeatureId);
                    if (groupTypeFeatureMapper.insert(record) != 1) {
                        throw new ServiceException("grouptype.error.save.feature");
                    }
                }
            }
        }
        return accessGroupChanging;
    }

    private void checkExistingLinkedGroups() throws ValidationException {
        logger.info("Checking for linked data for the current accessGroup...");
        if (userGroupService.isExistingUserGroups()) {
            logger.info("Existing UserGroups");
            throw new ValidationException(new ValidationMessage("grouptype.error.accessgroup.users", true));
        } else {
            if (customerService.isExistingCustomersWithGroup()) {
                logger.info("Existing customers");
                throw new ValidationException(new ValidationMessage("grouptype.error.accessgroup.customers", true));
            }
            if (usagePointService.isExistingUsagePointsWithGroup()) {
                logger.info("Existing usage points");
                throw new ValidationException(new ValidationMessage("grouptype.error.accessgroup.up", true));
            }
            if (pricingStructureService.isExistingPricingStructuresWithGroup()) {
                logger.info("Existing pricing");
                throw new ValidationException(new ValidationMessage("grouptype.error.accessgroup.pricing", true));
            }
            if (auxChargeScheduleService.isExistingAuxChargeSchedulesWithGroup()) {
                logger.info("Existing aux changes");
                throw new ValidationException(new ValidationMessage("grouptype.error.accessgroup.aux", true));
            }
            if (deviceStoreService.isExistingDeviceStoresWithGroup()) {
                logger.info("Existing device stores");
                throw new ValidationException(new ValidationMessage("grouptype.error.accessgroup.stores", true));
            }
        }
    }

    @Transactional
    @PreAuthorize("hasRole('mm_group_type_admin')")
    public GroupType saveGroupType(GroupType groupType) throws ValidationException, ServiceException {
        if (groupType.getId() == null) {
            if (groupTypeMapper.insert(groupType) != 1) {
                throw new ServiceException("grouptype.error.save");
            }
        } else {
            if (groupTypeMapper.updateByPrimaryKey(groupType) != 1) {
                throw new ServiceException("grouptype.error.save");
            }
        }
        return groupType;
    }

    @Transactional
    @PreAuthorize("hasRole('mm_group_type_admin')")
    public GroupHierarchy updateGroupHierarchy(GroupHierarchy groupHierarchy) throws ValidationException, ServiceException {
        //Check this is not the parent hierarchy root for access group as must not be inserted/updated/deleted as its static data
        checkRootAccessGroupHierarchy(groupHierarchy);

        if (groupHierarchy.getId() == null) {
            //if not the first level and already data existing for upper level (has gen_group) do not allow add a new level,
            // unless it's adding to the access group hierarchy which needs this ability on initial setup
            ArrayList<GenGroupParent> genGroupParents = getGenGroupParent(groupHierarchy.getGroupTypeId());
            boolean isAddingToInitialAccessGroup = false;
            if (groupHierarchy.getParentId() != null) {
                GroupHierarchy parent = getGroupHierarchy(groupHierarchy.getParentId());
                if ((getGroupTypeById(groupHierarchy.getGroupTypeId()).isAccessGroup() && parent.getParentId() == null || getHierarchyChildrenCount(groupHierarchy.getParentId()) == 0)) {
                    isAddingToInitialAccessGroup = true;
                }
            }
            if (groupHierarchy.getParentId() != null && genGroupParents != null && !genGroupParents.isEmpty() && !isAddingToInitialAccessGroup) {
                throw new ValidationException(new ValidationMessage("grouphierarchy.error.update", true));
            }

            if (groupHierarchyMapper.insert(groupHierarchy) != 1) {
                throw new ServiceException("grouphierarchy.error.save");
            }
        } else {
            if (groupHierarchyMapper.updateByPrimaryKey(groupHierarchy) != 1) {
                throw new ServiceException("grouphierarchy.error.save");
            }
        }
        return groupHierarchy;
    }

    @Transactional
    @PreAuthorize("hasRole('mm_group_type_admin')")
    public void deleteGroupHierarchy(Long groupHierarchyId) throws ValidationException, ServiceException {
        if (groupHierarchyId == null) {
            throw new ValidationException(new ValidationMessage("grouphierarchy.error.unknown", true));
        }
        try {
            GroupHierarchy current = groupHierarchyMapper.selectByPrimaryKey(groupHierarchyId);
            if (current == null) {
                throw new ValidationException(new ValidationMessage("grouphierarchy.error.unknown", true));
            }

            //Check this is not the parent hierarchy root for access group as must not be inserted/updated/deleted as its static data
            checkRootAccessGroupHierarchy(current);

            //If there are any linked children, their parent_id needs to be set to appropriate parent id
            GroupHierarchyExample example = new GroupHierarchyExample();
            example.createCriteria().andParentIdEqualTo(groupHierarchyId);
            List<GroupHierarchy> children = groupHierarchyMapper.selectByExample(example);
            for (GroupHierarchy child : children) {
                groupHierarchyCustomMapper.setParentId(current.getParentId(), child.getId());
                logger.info("Updating the child hierarchy's parent_id:" + child.getName() + " newParentId:" + current.getParentId());
            }

            //Attempt to Delete the group hierarchy - if delete fails its because of foreign key defined to gen_group
            if (groupHierarchyMapper.deleteByPrimaryKey(groupHierarchyId) != 1) {
                throw new ServiceException("grouphierarchy.error.delete");
            }
        } catch (Exception e) {
            logger.info("Unable to delete GroupHierarchy as it is in use:", e);
            throw new ValidationException(new ValidationMessage("grouphierarchy.error.delete.linked", true));
        }
    }

    @Transactional(readOnly = true)
    private void checkRootAccessGroupHierarchy(GroupHierarchy groupHierarchy) throws ValidationException {
        if (groupHierarchy != null) {
            //top level hierarchy
            if (groupHierarchy.getParentId() == null) {
                GroupType accessGroupType = getAccessGroupGroupType();
                if (groupHierarchy.getGroupTypeId().equals(accessGroupType.getId())) {
                    throw new ValidationException(new ValidationMessage("grouphierarchy.error.access.root", true));
                }
            }
        } else {
            logger.error("Unable to check null groupHierarchy data");
        }
    }

    @Transactional(readOnly = true)
    public GroupEntity getEntity(Long id) throws ValidationException, ServiceException {
        GroupEntityExample example = new GroupEntityExample();
        example.createCriteria().andRecordStatusNotEqualTo(RecordStatus.DEL).andIdEqualTo(id);
        List<GroupEntity> entities = groupEntityMapper.selectByExample(example);
        if (entities.size() == 1) {
            return entities.get(0);
        } else {
            return null;
        }
    }

    @Transactional(readOnly = true)
    public GroupEntity getEntityFromGenGroupId(Long genGroupId) throws ServiceException {
        return genGroupCustomMapper.getEntityFromGenGroupId(genGroupId);
    }

    @Transactional
    @PreAuthorize("hasAnyRole('mm_group_admin', 'mm_location_group_admin', 'mm_access_group_admin')")
    public void saveGroupEntity(GroupEntity entity, Long groupId) throws ValidationException, ServiceException {
        int result = -1;
        if (entity.getId() == null) {
            result = groupEntityMapper.insert(entity);
            if (result == 1) {
                saveGenGroupEntity(groupId, entity.getId());
            }
        } else {
            result = groupEntityMapper.updateByPrimaryKey(entity);
        }
        if (result != 1) {
            throw new ServiceException("groupentity.error.save");
        }
    }

    @Transactional
    @PreAuthorize("hasAnyRole('mm_group_admin', 'mm_location_group_admin', 'mm_access_group_admin')")
    private void saveGenGroupEntity(Long genGroupId, Long entityId) throws ServiceException {
        GenGroup genGroup = genGroupMapper.selectByPrimaryKey(genGroupId);
        if (genGroup != null) {
            genGroup.setGroupEntityId(entityId);
            if (genGroupMapper.updateByPrimaryKey(genGroup) != 1) {
                throw new ServiceException("group.error.entity.save");
            }
        }
    }

    @Transactional(readOnly = true)
    public GroupHierarchy getNextInHierarchy(Long parentHierarchyId) {
        GroupHierarchyExample example = new GroupHierarchyExample();
        example.createCriteria().andParentIdEqualTo(parentHierarchyId);
        List<GroupHierarchy> thelist = groupHierarchyMapper.selectByExample(example);
        if (thelist != null && !thelist.isEmpty()) {
            return thelist.get(0);
        }
        return null;
    }

    @Transactional(readOnly = true)
    public GenGroup getGenGroup(Long id) {
        GenGroupExample example = new GenGroupExample();
        example.createCriteria().andIdEqualTo(id);
        List<GenGroup> genGroups = genGroupMapper.selectByExample(example);
        if (genGroups.size() == 1) {
            return genGroups.get(0);
        } else {
            return null;
        }
    }

    @Transactional(readOnly = true)
    public Long getParentGenGroupForAccessGroup(Long accessGroupId) {
        GenGroupExample example = new GenGroupExample();
        example.createCriteria().andAccessGroupIdEqualTo(accessGroupId);
        List<GenGroup> genGroups = genGroupMapper.selectByExample(example);
        if (genGroups.size() == 1) {
            GenGroup accessGroupGG = genGroups.get(0);
            if (accessGroupGG.getParentId() != null) {
                // The Parent Group
                accessGroupGG = getGenGroup(accessGroupGG.getParentId());
                if (accessGroupGG != null) {
                    return accessGroupGG.getId();
                }
            } else {
                return accessGroupGG.getId();
            }
        }
        return null;
    }

    @Transactional(readOnly = true)
    public List<GenGroup> getAllGenGroups() {
        GenGroupExample example = new GenGroupExample();
        example.createCriteria().andRecordStatusNotEqualTo(RecordStatus.DEL);
        example.setOrderByClause(PARENT_ID_COLUMN);
        return genGroupMapper.selectByExample(example);
    }

    @Transactional(readOnly = true)
    public ArrayList<GenGroup> getAllGenGroups(Long groupTypeId) {
        List<GenGroup> groups = iGenGroupMapper.getGenGroupsByGroupTypeId(groupTypeId);
        if (groups instanceof ArrayList<?>) {
            return (ArrayList<GenGroup>) groups;
        } else {
            return new ArrayList<GenGroup>(groups);
        }
    }

    @Transactional
    @PreAuthorize("hasAnyRole('mm_group_admin', 'mm_location_group_admin', 'mm_access_group_admin')")
    public GenGroup updateGenGroup(GenGroup genGroup, boolean lastLevelChild) throws ServiceException {
        if (genGroup.getId() == null) {
            if (genGroupMapper.insert(genGroup) != 1) {
                throw new ServiceException("group.error.save");
            }
        } else {
            if (genGroupMapper.updateByPrimaryKey(genGroup) != 1) {
                throw new ServiceException("group.error.save");
            }
        }

        if (lastLevelChild) {
            logger.info("Updating parents status for child: " + genGroup.getName());
            if (RecordStatus.ACT.equals(genGroup.getRecordStatus())) {
                activateParents(genGroup);
            } else {
                deactivateParents(genGroup);
            }
        }

        return genGroup;
    }

    @Transactional
    @PreAuthorize("hasAnyRole('mm_group_admin', 'mm_location_group_admin', 'mm_access_group_admin')")
    public GenGroup updateGenGroupMetadata(GenGroup genGroup, GisMetadata newMetadata) throws ServiceException {
        if (genGroup.getId() == null) {
            throw new ServiceException("group.error.save");
        } else {
            String metadata = genGroup.getMetadata();
            if (metadata != null && !metadata.trim().isEmpty()) {
                Pattern pattern = Pattern.compile(GisMetadata.GIS_REGEXP);
                Matcher matcher = pattern.matcher(metadata);
                if (matcher.find()) {
                    genGroup.setMetadata(metadata.replaceFirst(pattern.pattern(), newMetadata.toJson()));
                } else {
                    genGroup.setMetadata(metadata + "," + newMetadata.toJson());
                }
            } else {
                genGroup.setMetadata(newMetadata.toJson());
            }
            if (genGroupMapper.updateByPrimaryKey(genGroup) != 1) {
                throw new ServiceException("group.error.save");
            }
        }
        return genGroup;
    }

    /**
     * Method to go up the hierarchy from child to parent, activating the parent if it is not active.
     *
     * @param genGroup The last level child.
     */
    @Transactional
    private void activateParents(GenGroup genGroup) {
        Long parentId = genGroup.getParentId();
        GenGroup parent = null;
        while (parentId != null) {
            parent = getGenGroup(parentId);
            if (parent != null) {
                if (!RecordStatus.ACT.equals(parent.getRecordStatus())) {
                    parent.setRecordStatus(RecordStatus.ACT);
                    genGroupMapper.updateByPrimaryKey(parent);
                    logger.info("Updated parent's status: " + parent.getName());
                }
                parentId = parent.getParentId();
            } else {
                logger.error("No matching parent for id: " + parentId);
                parentId = null;
            }
        }
    }

    /**
     * Method to check if the parents has any active children left and if not then make the parent deactivated.
     *
     * @param genGroup The last level child.
     */
    @Transactional
    private void deactivateParents(GenGroup genGroup) {
        logger.info("Deleting parents: " + genGroup.getName());
        Long parentId = genGroup.getParentId();
        while (parentId != null) {
            int activeChildren = getActiveChildrenCount(parentId);
            logger.info("Active children: " + activeChildren);
            GenGroup parent = getGenGroup(parentId);
            if (parent != null) {
                if (activeChildren == 0) {
                    if (!RecordStatus.DAC.equals(parent.getRecordStatus())) {
                        parent.setRecordStatus(RecordStatus.DAC);
                        genGroupMapper.updateByPrimaryKey(parent);
                        logger.info("Deactivated parent: " + parent.getName());
                    }
                }
                parentId = parent.getParentId();
            } else {
                logger.error("No matching parent for id: " + parentId);
                parentId = null;
            }
        }
    }

    @Transactional(readOnly = true)
    private int getActiveChildrenCount(Long parentId) {
        GenGroupExample example = new GenGroupExample();
        example.createCriteria().andRecordStatusEqualTo(RecordStatus.ACT).andParentIdEqualTo(parentId);
        return genGroupMapper.countByExample(example);
    }

    @Transactional(readOnly = true)
    private int getHierarchyChildrenCount(Long hierarchyId) {
        GenGroupExample example = new GenGroupExample();
        example.createCriteria().andRecordStatusEqualTo(RecordStatus.ACT).andGroupHierarchyIdEqualTo(hierarchyId);
        return genGroupMapper.countByExample(example);
    }

    @Transactional(readOnly = true)
    public List<GroupType> getGroupTypes(boolean includeAccessGroup, boolean includeLocationGroup) {
        GroupTypeExample example = new GroupTypeExample();
        za.co.ipay.metermng.mybatis.generated.model.GroupTypeExample.Criteria criteria = example.createCriteria().andRecordStatusEqualTo(RecordStatus.ACT);
        if(!includeAccessGroup) {
            criteria.andAccessGroupEqualTo(false);
        }
        if(!includeLocationGroup) {
            criteria.andLocationGroupEqualTo(false);
        }
        example.setOrderByClause(GROUP_TYPE_LAYOUT_ORDER_COLUMN);
        List<GroupType> groupTypeList = groupTypeMapper.selectByExample(example);
        return groupTypeList;
    }

    @Transactional(readOnly = true)
    public ArrayList<GenGroupParent> getLocationGroupParents() {
        List<GenGroupParent> parents;
        parents = iGenGroupMapper.getLocationGroupParents();
        if (parents instanceof ArrayList<?>) {
            return (ArrayList<GenGroupParent>) parents;
        } else {
            return new ArrayList<GenGroupParent>(parents);
        }
    }

    @Transactional(readOnly = true)
    public ArrayList<GenGroupParent> getGenGroupParent(Long groupTypeId) {
        List<GenGroupParent> parents = iGenGroupMapper.getGenGroupParent(groupTypeId);
        if (parents instanceof ArrayList<?>) {
            return (ArrayList<GenGroupParent>) parents;
        } else {
            return new ArrayList<GenGroupParent>(parents);
        }
    }

    @Transactional(readOnly = true)
    public ArrayList<Long> getGenGroupParentIds(Long groupTypeId) {
        List<Long> ids = iGenGroupMapper.getGenGroupParentIds(groupTypeId);
        if (ids instanceof ArrayList<?>) {
            return (ArrayList<Long>) ids;
        } else {
            return new ArrayList<Long>(ids);
        }
    }

    @Transactional(readOnly = true)
    public ArrayList<GroupClosureCombo> getGroupTypeClosures(Long groupTypeChildId) {
        List<GroupClosureCombo> combos = iGenGroupMapper.getGroupTypeClosuresForChildId(groupTypeChildId);
        if (combos instanceof ArrayList<?>) {
            return (ArrayList<GroupClosureCombo>) combos;
        } else {
            return new ArrayList<GroupClosureCombo>(combos);
        }
    }

    @Transactional(readOnly = true)
    public List<IdPair> getGroupHierarchyLastLevels(List<Long> ids) {
        return iGenGroupMapper.getLastGroupHierarchyIdForGroupTypes(ids);
    }

    @Transactional(readOnly = true)
    public List<GenGroupClosureCombo> getGenGroupClosuresForGroupHierarchy(Long parentId) {
        return iGenGroupMapper.getGenGroupClosuresForParentId(parentId);
    }

    @Transactional(readOnly = true)
    public GenGroupClosureCombo getGenGroupClosureForGroupHierarchy(Long genGroupId) {
        return iGenGroupMapper.getGenGroupClosure(genGroupId);
    }

    @Transactional(readOnly = true)
    public List<GenGroupClosureCombo> getParentGenGroupClosuresForGroupType(Long groupTypeId) {
        return iGenGroupMapper.getTopLevelGenGroupClosures(groupTypeId);
    }

    @Transactional(readOnly = true)
    public List<GenGroupClosureCombo> getClosureChildren(Long parentId) {
        return iGenGroupMapper.getClosureChildren(parentId);
    }

    @Transactional(readOnly = true)
    public Long getMaxGroupHierarchyId(Long genGroupId) {
        GenGroup genGroup = genGroupMapper.selectByPrimaryKey(genGroupId);
        if (genGroup != null) {
            return iGenGroupMapper.getMaxGroupHierarchyId(genGroup.getGroupHierarchyId());
        } else {
            logger.error("No matching GenGroup for genGroupId: " + genGroupId);
            return Long.valueOf(-1);
        }
    }

    @Transactional(readOnly = true)
    public Long getGroupTypeMaxGroupHierarchyId(Long groupTypeId) {
        return iGenGroupMapper.getGroupTypeMaxGroupHierarchyId(groupTypeId);
    }

    @Transactional
    @PreAuthorize("hasAnyRole('mm_group_admin', 'mm_location_group_admin', 'mm_access_group_admin')")
    public void deleteGenGroup(GenGroup genGroup) throws ServiceException {
        try {
            if (genGroupMapper.deleteByPrimaryKey(genGroup.getId()) != 1) {
                throw new ServiceException("group.error.delete");
            }
        } catch (DataIntegrityViolationException dive) {
            throw new ServiceException("group.error.delete");
        } catch (Exception sqle) {
            throw new ServiceException("group.error.delete");
        }

        //When delete walk up parents and make them DAC
        deactivateParents(genGroup);
    }

    @Transactional(readOnly = true)
    public ArrayList<Long> getPath(Long groupId) {
        return upGenGroupLnkService.getPath(groupId);
    }

    @Transactional(readOnly = true)
    public Boolean isGenGroupUnique(GenGroup genGroup) {
        GenGroupExample example = new GenGroupExample();
        GenGroupExample.Criteria criteria = example.createCriteria();
        if (genGroup.getParentId() != null) {
            criteria.andParentIdEqualTo(genGroup.getParentId());
        } else {
            criteria.andParentIdIsNull();
        }
        criteria.andGroupHierarchyIdEqualTo(genGroup.getGroupHierarchyId())
                .andNameLikeInsensitive(genGroup.getName().trim());
        return (genGroupMapper.countByExample(example) == 0);
    }

    @Transactional(readOnly = true)
    public Boolean isGroupAssignedToUP(GenGroup genGroup) {
        return upGenGroupLnkService.isGroupAssigned(genGroup.getId());
    }

    //-----------------------------------------Thresholds --------------------------------------------------------------------------
    @Transactional
    @PreAuthorize("hasRole('mm_cust_acc_thresholds_admin')")
    public CustomerAccThresholds updateThresholds(GenGroupData genGroupData, CustomerAccThresholds thresholds) throws ServiceException {
        logger.info("updateThresholds: " + genGroupData.getName());
        int result = -1;

        Long existingThresholdsId = null;

        //will only update if values have changed.... 
        //but note --> if this node's threshold Id is same as that of parent; then this branch is changing: 
        //                insert a new threshold & update all children also with new thresholdId
        if (genGroupData.getCustomerAccThresholdsId() != null) {
            existingThresholdsId = genGroupData.getCustomerAccThresholdsId();
        } else if (genGroupData.getParent() != null && genGroupData.getParent().getCustomerAccThresholdsId() != null) {
            existingThresholdsId = genGroupData.getParent().getCustomerAccThresholdsId();
        }

        if (thresholds.getId() == null) {
            result = insertNewThreshold(genGroupData, existingThresholdsId, thresholds);
        } else {
            result = customerAccThresholdsMapper.updateByPrimaryKey(thresholds);
        }

        if (result != 1) {
            throw new ServiceException("groupthreshold.error.save.thresholds");
        }

        return thresholds;
    }

    @Transactional
    private int insertNewThreshold(GenGroupData genGroupData, Long existingThresholdId, CustomerAccThresholds thresholds) {
        int result = -1;
        result = customerAccThresholdsMapper.insert(thresholds);
        if (result == 1) {
            //update this genGroup with its new thresholds id
            logger.info(genGroupData.getName() + " insert new thresholdid=" + thresholds.getId());
            saveGenGroupThreshold(genGroupData.getId(), thresholds.getId());

            updateChildrenWithThresholdId(genGroupData.getChildren(), existingThresholdId, thresholds.getId());
        }
        return result;
    }

    @Transactional
    private void updateChildrenWithThresholdId(List<GenGroupData> genGroupChildren, Long existingThresholdId, Long newThresholdId) {
        //existingThresholdId can be null --> i.e. insert new
        if (genGroupChildren != null) {
            for (GenGroupData ggdChild : genGroupChildren) {
                if ((ggdChild.getCustomerAccThresholdsId() == null && existingThresholdId == null)
                        || (ggdChild.getCustomerAccThresholdsId() != null && existingThresholdId != null
                        && ggdChild.getCustomerAccThresholdsId().equals(existingThresholdId))) {
                    logger.info("Updating children: " + ggdChild.getName() + " thresholdId=" + newThresholdId);
                    saveGenGroupThreshold(ggdChild.getId(), newThresholdId);
                }
                updateChildrenWithThresholdId(ggdChild.getChildren(), existingThresholdId, newThresholdId);
            }
        }
    }

    @Transactional
    public CustomerAccThresholds getCustomerAccThreshold(Long thresholdId) throws ServiceException {
        return customerAccThresholdsMapper.selectByPrimaryKey(thresholdId);
    }

    @Transactional
    private void saveGenGroupThreshold(Long genGroupId, Long thresholdId) throws ServiceException {
        GenGroup genGroup = genGroupMapper.selectByPrimaryKey(genGroupId);
        if (genGroup != null) {
            genGroup.setCustomerAccThresholdsId(thresholdId);
            if (genGroupMapper.updateByPrimaryKey(genGroup) != 1) {
                throw new ServiceException("group.error.threshold.save");
            }
        }
    }

    @Transactional
    @PreAuthorize("hasRole('mm_cust_acc_thresholds_admin')")
    public CustomerAccThresholds deleteThresholds(GenGroupData genGroupData, Long revertToThresholdId) throws ServiceException {
        Long existingThresholdId = genGroupData.getCustomerAccThresholdsId();
        saveGenGroupComplete(genGroupData.getId(), revertToThresholdId);
        deleteThresholdIdFromChildren(genGroupData.getChildren(), existingThresholdId, revertToThresholdId);

        if (existingThresholdId != null) {
            //if no gen-groups are using the deleted thresholdId, delete the threshold record
            GenGroupExample groupExample = new GenGroupExample();
            groupExample.createCriteria().andCustomerAccThresholdsIdEqualTo(existingThresholdId);
            ArrayList<GenGroup> genGroupsStillUseOldThresholdList = (ArrayList<GenGroup>) genGroupMapper.selectByExample(groupExample);
            logger.info("DELETING THRESHOLD ID =" + existingThresholdId + " BUT STILL IN USE BY LIST.SIZE()=" + genGroupsStillUseOldThresholdList.size());
            if (genGroupsStillUseOldThresholdList == null || genGroupsStillUseOldThresholdList.isEmpty()) {
                logger.info("DELETING THRESHOLD ID =" + existingThresholdId);
                customerAccThresholdsMapper.deleteByPrimaryKey(existingThresholdId);
            }
        }

        if (revertToThresholdId != null) {
            return getCustomerAccThreshold(revertToThresholdId);
        } else {
            return null;
        }
    }

    @Transactional
    private void saveGenGroupComplete(Long genGroupId, Long thresholdId) throws ServiceException {
        GenGroup genGroup = genGroupMapper.selectByPrimaryKey(genGroupId);
        if (genGroup != null) {
            genGroup.setCustomerAccThresholdsId(thresholdId);
            if (genGroupMapper.updateByPrimaryKey(genGroup) != 1) {
                throw new ServiceException("group.error.threshold.save");
            }
        }
    }

    @Transactional
    private void deleteThresholdIdFromChildren(List<GenGroupData> genGroupChildren, Long existingThresholdId, Long newThresholdId) {
        //existingThresholdId can be null --> revert to global (thresholdId = null)
        if (genGroupChildren != null) {
            for (GenGroupData ggdChild : genGroupChildren) {
                if (ggdChild.getCustomerAccThresholdsId().equals(existingThresholdId)) {
                    logger.info("Updating children: " + ggdChild.getName() + " thresholdId=" + newThresholdId);
                    saveGenGroupComplete(ggdChild.getId(), newThresholdId);
                }
                deleteThresholdIdFromChildren(ggdChild.getChildren(), existingThresholdId, newThresholdId);
            }
        }
    }


    //-----------------------------------------NDP --------------------------------------------------------------------------
    @Transactional
    @PreAuthorize("hasAnyRole('mm_ndp_admin', 'mm_ndp_group_admin')")
    public NdpSchedule addNewNdpScheduleToGenGroup(GenGroupData genGroupData) throws ServiceException, AccessControlException {
        logger.info("createNdpSchedule for genGroup: " + genGroupData.getName());
        NdpSchedule ndpSchedule = ndpService.addNdpSchedule(Boolean.FALSE);
        Long existingNdpScheduleId = null;

        //will only update if values have changed.... 
        //but note --> if this node's NdpScheduleId is same as that of parent; then this branch is changing: 
        //                insert a new NdpSchedule & update all children also with new ndpScheduleId
        if (genGroupData.getParent() != null
                && ((genGroupData.getNdpScheduleId() == null && genGroupData.getParent().getNdpScheduleId() == null)
                || (genGroupData.getNdpScheduleId() != null && genGroupData.getParent().getNdpScheduleId() != null
                && genGroupData.getNdpScheduleId().equals(genGroupData.getParent().getNdpScheduleId())))) {
            existingNdpScheduleId = genGroupData.getNdpScheduleId();
        }

        //result = 
        insertNewNdpSchedule(genGroupData, existingNdpScheduleId, ndpSchedule.getId());

        return ndpSchedule;
    }

    @Transactional
    private void insertNewNdpSchedule(GenGroupData genGroupData, Long existingNdpScheduleId, Long ndpScheduleId) {
        //update this genGroup with its new ndpSchedule id
        logger.info(genGroupData.getName() + " insert new ndpScheduleid=" + ndpScheduleId.toString());
        saveGenGroupNdpSchedule(genGroupData.getId(), ndpScheduleId);

        updateChildrenWithNdpScheduleId(genGroupData.getChildren(), existingNdpScheduleId, ndpScheduleId);
    }

    @Transactional
    private void updateChildrenWithNdpScheduleId(List<GenGroupData> genGroupChildren, Long existingNdpScheduleId, Long newNdpScheduleId) {
        //existingNdpScheduleId can be null --> i.e. insert new
        if (genGroupChildren != null) {
            for (GenGroupData ggdChild : genGroupChildren) {
                if ((ggdChild.getNdpScheduleId() == null && existingNdpScheduleId == null)
                        || (ggdChild.getNdpScheduleId() != null && existingNdpScheduleId != null
                        && ggdChild.getNdpScheduleId().equals(existingNdpScheduleId))) {
                    logger.info("Updating children: " + ggdChild.getName() + " ndpScheduleId=" + newNdpScheduleId);
                    saveGenGroupNdpSchedule(ggdChild.getId(), newNdpScheduleId);
                }
                updateChildrenWithNdpScheduleId(ggdChild.getChildren(), existingNdpScheduleId, newNdpScheduleId);
            }
        }
    }

    @Transactional
    private void saveGenGroupNdpSchedule(Long genGroupId, Long ndpScheduleId) throws ServiceException {
        GenGroup genGroup = genGroupMapper.selectByPrimaryKey(genGroupId);
        if (genGroup != null) {
            genGroup.setNdpScheduleId(ndpScheduleId);
            if (genGroupMapper.updateByPrimaryKey(genGroup) != 1) {
                throw new ServiceException("group.error.ndp.schedule.save");
            }
        }
    }

    @Transactional
    @PreAuthorize("hasAnyRole('mm_ndp_admin', 'mm_ndp_group_admin')")
    public NdpScheduleData deleteNdpSchedule(GenGroupData genGroupData, Long revertToNdpScheduleId) throws ServiceException, AccessControlException {
        Long existingNdpScheduleId = genGroupData.getNdpScheduleId();
        saveGenGroupNdpSchedule(genGroupData.getId(), revertToNdpScheduleId);
        deleteNdpScheduleIdFromChildren(genGroupData.getChildren(), existingNdpScheduleId, revertToNdpScheduleId);

        if (existingNdpScheduleId != null) {
            //if no gen-groups are using the deleted thresholdId, delete the threshold record
            GenGroupExample groupExample = new GenGroupExample();
            groupExample.createCriteria().andNdpScheduleIdEqualTo(existingNdpScheduleId);
            ArrayList<GenGroup> genGroupsStillUseOldNdpScheduleList = (ArrayList<GenGroup>) genGroupMapper.selectByExample(groupExample);
            logger.info("ATTEMPT TO DELETE NDP SCHEDULE ID =" + existingNdpScheduleId + " BUT STILL IN USE BY LIST.SIZE()=" + genGroupsStillUseOldNdpScheduleList.size());
            if (genGroupsStillUseOldNdpScheduleList == null || genGroupsStillUseOldNdpScheduleList.isEmpty()) {
                logger.info("DELETING NDP SCHEDULE ID =" + existingNdpScheduleId);
                ndpService.deleteNdpScheduleByPrimaryKey(existingNdpScheduleId);
            }
        }

        if (revertToNdpScheduleId != null) {
            return ndpService.getNdpScheduleData(revertToNdpScheduleId);
        } else {
            return null;
        }
    }

    @Transactional
    private void deleteNdpScheduleIdFromChildren(List<GenGroupData> genGroupChildren, Long existingNdpScheduleId, Long newNdpScheduleId) {
        //existingNdpScheduleId can be null --> revert to global (thresholdId = null)
        if (genGroupChildren != null) {
            for (GenGroupData ggdChild : genGroupChildren) {
                if (ggdChild.getNdpScheduleId().equals(existingNdpScheduleId)) {
                    logger.info("Updating children: " + ggdChild.getName() + " ndpScheduleId=" + newNdpScheduleId);
                    saveGenGroupNdpSchedule(ggdChild.getId(), newNdpScheduleId);
                }
                deleteNdpScheduleIdFromChildren(ggdChild.getChildren(), existingNdpScheduleId, newNdpScheduleId);
            }
        }
    }

    @Transactional
    public Boolean getChooseAccessGroupDecision(Long currentGroupId) {
        //Returns true if Assigned Group NOT at Highest Level and need to choose a leaf node 
        GroupType groupType = getAccessGroupGroupType();
        List<GroupHierarchy> hierarchyList = getGroupHierarchies(groupType.getId());           //ordered by "group_hierarchy_id"
        int accessGroupHighestLevel = hierarchyList.size();

        if (accessGroupHighestLevel == 1) {
            return false;
        }

        // more than 1 Access Group Level - check assigned Group level & accessGroup leaf nodes
        int AssignedGrpLevel = getPath(currentGroupId).size();      //List<Long> depthIdList
        if (AssignedGrpLevel < accessGroupHighestLevel) {
            return true;
        }

        //if assigned grp level is = highest assume it has the one and only
        return false;
    }

    @Transactional(readOnly = true)
    public ArrayList<UpGenGroupLinkDataNames> getUpGenGroupNamesList(ArrayList<UpGenGroupLinkData> genGroupIdList) throws ServiceException, AccessControlException {
        ArrayList<UpGenGroupLinkDataNames> returnData = new ArrayList<UpGenGroupLinkDataNames>(genGroupIdList.size());
        UpGenGroupLinkDataNames upGenGroupLinkDataNames;
        ArrayList<String> hierarchyNamesList;
        ArrayList<String> depthNamesList;

        for (UpGenGroupLinkData lnkIdData : genGroupIdList) {
            upGenGroupLinkDataNames = new UpGenGroupLinkDataNames();
            // add group type Name
            upGenGroupLinkDataNames.setGroupTypeName(getGroupTypeById(lnkIdData.getGroupTypeId()).getName());
            upGenGroupLinkDataNames.setGroupTypeId(lnkIdData.getGroupTypeId());
            hierarchyNamesList = new ArrayList<String>();
            depthNamesList = new ArrayList<String>();

            for (Long ggId : lnkIdData.getDepthList()) {
                GenGroup gg = getGenGroup(ggId);
                hierarchyNamesList.add(getGroupHierarchy(gg.getGroupHierarchyId()).getName());
                depthNamesList.add(gg.getName());
            }

            upGenGroupLinkDataNames.setHierarchyNamesList(hierarchyNamesList);
            upGenGroupLinkDataNames.setDepthNamesList(depthNamesList);
            returnData.add(upGenGroupLinkDataNames);
        }

        return returnData;
    }


    public List<Long> isGroupEntityCustomFieldUsed(int customFieldNumber) {
        GroupEntityExample groupEntityExample = new GroupEntityExample();
        GroupEntityExample.Criteria groupEntityCriteria = groupEntityExample.createCriteria();
        groupEntityCriteria.andRecordStatusEqualTo(RecordStatus.ACT);

        switch (customFieldNumber) {
            case 1:
                groupEntityCriteria.andCustomField1IsNotNull();
                break;
            case 2:
                groupEntityCriteria.andCustomField2IsNotNull();
                break;
            case 3:
                groupEntityCriteria.andCustomField3IsNotNull();
                break;
            case 4:
                groupEntityCriteria.andCustomField4IsNotNull();
                break;
            case 5:
                groupEntityCriteria.andCustomField5IsNotNull();
                break;
            case 6:
                groupEntityCriteria.andCustomField6IsNotNull();
                break;
            case 7:
                groupEntityCriteria.andCustomField7IsNotNull();
                break;
            case 8:
                groupEntityCriteria.andCustomField8IsNotNull();
                break;
            case 9:
                groupEntityCriteria.andCustomField9IsNotNull();
                break;
            case 10:
                groupEntityCriteria.andCustomField10IsNotNull();
                break;
        }
        List<GroupEntity> using = groupEntityMapper.selectByExample(groupEntityExample);
        List<Long> ids = using.stream().map(x -> x.getId()).collect(Collectors.toList());
        return ids;
    }

    public List<Long> getUsedAccessGroupIdsForHierachy(long groupHierarchyId) {
        GenGroupExample ex = new GenGroupExample();
        ex.createCriteria().andGroupHierarchyIdEqualTo(groupHierarchyId);
        List<GenGroup> groups = genGroupMapper.selectByExample(ex);
        ArrayList<Long> accessGroupIds = new ArrayList<>(groups.size());
        for (GenGroup genGroup : groups) {
            if(genGroup.getAccessGroupId() != null) {
                accessGroupIds.add(genGroup.getAccessGroupId());
            }
        }
        return accessGroupIds;
    }

    public boolean getMridExistence(GenGroup genGroup) {
        GenGroupExample example = new GenGroupExample();
        Criteria criteria = example.createCriteria().andMridEqualTo(genGroup.getMrid()).andGroupHierarchyIdEqualTo(genGroup.getGroupHierarchyId());
        if(genGroup.getId() != null) {
            criteria.andIdNotEqualTo(genGroup.getId());
        }
        return !genGroupMapper.selectByExample(example).isEmpty();
    }
    
    //-----------------------------------------Notifications --------------------------------------------------------------------------
    @Transactional
    public CustAccNotify updateNotifications(GenGroupData genGroupData, CustAccNotify notifyAccount) throws ServiceException {
        int result = -1;
        Long existingNotifyId = null;
        if (genGroupData.getCustAccNotifyId() != null) {
            existingNotifyId = genGroupData.getCustAccNotifyId();
        } else if (genGroupData.getParent() != null && genGroupData.getParent().getCustAccNotifyId() != null) {
            existingNotifyId = genGroupData.getParent().getCustAccNotifyId();
        }
        if (notifyAccount.getId() == null) {
            result = insertNewNotifyAccount(genGroupData, notifyAccount, existingNotifyId);
        } else {
            result = custAccNotifyMapper.updateByPrimaryKey(notifyAccount);
        }
        if (result != 1) {
            throw new ServiceException("group.error.notification.save");
        }
        return notifyAccount;
    }
    
    @Transactional
    public CustAccNotify convertNotifyAccountToInherit(GenGroupData genGroupData, Long oldId) {
        CustAccNotify custAccNotify = new CustAccNotify();
        if(genGroupData.getParent() != null && genGroupData.getParent().getCustAccNotifyId() != null) 
            custAccNotify.setId(genGroupData.getParent().getCustAccNotifyId());
        saveGenGroupNotifyId(genGroupData.getId(), custAccNotify.getId());
        updateChildrenWithNotifyId(genGroupData.getChildren(), oldId, custAccNotify.getId());
        return custAccNotify;
    }
    
    @Transactional
    private int insertNewNotifyAccount(GenGroupData genGroupData, CustAccNotify notifyAccount, Long existingNotifyId) {
        int result = -1;
        result = custAccNotifyMapper.insert(notifyAccount);
        if (result == 1) {
            saveGenGroupNotifyId(genGroupData.getId(), notifyAccount.getId());
            updateChildrenWithNotifyId(genGroupData.getChildren(), existingNotifyId, notifyAccount.getId());
        }
        return result;
    }
    
    @Transactional
    private void saveGenGroupNotifyId(Long genGroupId, Long custAccNotifyId) throws ServiceException {
        GenGroup genGroup = genGroupMapper.selectByPrimaryKey(genGroupId);
        if (genGroup != null) {
            genGroup.setCustAccNotifyId(custAccNotifyId);
            if (genGroupMapper.updateByPrimaryKey(genGroup) != 1) {
                throw new ServiceException("group.error.notification.save");
            }
        }
    }

    @Transactional
    private void updateChildrenWithNotifyId(List<GenGroupData> genGroupChildren, Long existingNotifyId, Long newNotifyId) {
    //existingNotifyId can be null --> i.e. insert new
        if (genGroupChildren != null) {
            for (GenGroupData ggdChild : genGroupChildren) {
                if ((ggdChild.getCustAccNotifyId() == null && existingNotifyId == null)
                        || (ggdChild.getCustAccNotifyId() != null && existingNotifyId != null
                        && ggdChild.getCustAccNotifyId().equals(existingNotifyId))) {
                    logger.info("Updating children: " + ggdChild.getName() + " notifyId=" + newNotifyId);
                    saveGenGroupNotifyId(ggdChild.getId(), newNotifyId);
                }
                updateChildrenWithNotifyId(ggdChild.getChildren(), existingNotifyId, newNotifyId);
            }
        }
    }
    
    @Transactional
    public CustAccNotify getGenGroupNotifications(Long custAccNotifyId) throws ServiceException {
        return custAccNotifyMapper.selectByPrimaryKey(custAccNotifyId);
    }
    
    /**
     * Setter methods ----------------------------------------------------------------------------------------------------
     */
    public void setGroupTypeMapper(GroupTypeMapper groupTypeMapper) {
        this.groupTypeMapper = groupTypeMapper;
    }

    public void setGroupHierarchyMapper(GroupHierarchyMapper groupHierarchyMapper) {
        this.groupHierarchyMapper = groupHierarchyMapper;
    }

    public void setIGroupTypeMapper(IGroupTypeMapper iGroupTypeMapper) {
        this.iGroupTypeMapper = iGroupTypeMapper;
    }

    public void setGroupEntityMapper(GroupEntityMapper groupEntityMapper) {
        this.groupEntityMapper = groupEntityMapper;
    }

    public void setiGroupTypeMapper(IGroupTypeMapper iGroupTypeMapper) {
        this.iGroupTypeMapper = iGroupTypeMapper;
    }

    public void setGenGroupMapper(GenGroupMapper genGroupMapper) {
        this.genGroupMapper = genGroupMapper;
    }

    public void setiGenGroupMapper(IGenGroupMapper iGenGroupMapper) {
        this.iGenGroupMapper = iGenGroupMapper;
    }

    public void setUserGroupService(UserGroupService userGroupService) {
        this.userGroupService = userGroupService;
    }

    public void setCustomerService(CustomerService customerService) {
        this.customerService = customerService;
    }

    public void setUsagePointService(UsagePointService usagePointService) {
        this.usagePointService = usagePointService;
    }

    public void setAuxChargeScheduleService(AuxChargeScheduleService auxChargeScheduleService) {
        this.auxChargeScheduleService = auxChargeScheduleService;
    }

    public void setPricingStructureService(PricingStructureService pricingStructureService) {
        this.pricingStructureService = pricingStructureService;
    }

    public void setDeviceStoreService(DeviceStoreService deviceStoreService) {
        this.deviceStoreService = deviceStoreService;
    }

    public void setUpGenGroupLnkService(UpGenGroupLnkService upGenGroupLnkService) {
        this.upGenGroupLnkService = upGenGroupLnkService;
    }

    public void setGroupHierarchyCustomMapper(GroupHierarchyCustomMapper groupHierarchyCustomMapper) {
        this.groupHierarchyCustomMapper = groupHierarchyCustomMapper;
    }

    public void setGroupFeatureMapper(GroupFeatureMapper groupFeatureMapper) {
        this.groupFeatureMapper = groupFeatureMapper;
    }

    public void setGroupTypeFeatureMapper(GroupTypeFeatureMapper groupTypeFeatureMapper) {
        this.groupTypeFeatureMapper = groupTypeFeatureMapper;
    }

    public void setCustomerAccThresholdsMapper(CustomerAccThresholdsMapper customerAccThresholdsMapper) {
        this.customerAccThresholdsMapper = customerAccThresholdsMapper;
    }

    public void setNdpService(NdpService ndpService) {
        this.ndpService = ndpService;
    }

    public void setGenGroupCustomMapper(GenGroupCustomMapper genGroupCustomMapper) {
        this.genGroupCustomMapper = genGroupCustomMapper;
    }

    public void setAppSettingService(AppSettingService appSettingService) {
        this.appSettingService = appSettingService;
    }
    
    public void setCustAccNotifyMapper(CustAccNotifyMapper custAccNotifyMapper) {
        this.custAccNotifyMapper = custAccNotifyMapper;
    }
}
