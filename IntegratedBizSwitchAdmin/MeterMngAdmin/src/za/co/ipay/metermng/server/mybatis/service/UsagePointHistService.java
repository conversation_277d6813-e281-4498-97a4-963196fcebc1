package za.co.ipay.metermng.server.mybatis.service;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.apache.ibatis.session.RowBounds;
import org.springframework.transaction.annotation.Transactional;

import za.co.ipay.metermng.mybatis.generated.mapper.UpMeterInstallHistMapper;
import za.co.ipay.metermng.mybatis.generated.mapper.UpPricingStructureHistMapper;
import za.co.ipay.metermng.mybatis.generated.model.Meter;
import za.co.ipay.metermng.mybatis.generated.model.PricingStructure;
import za.co.ipay.metermng.mybatis.generated.model.UpMeterInstallHistExample;
import za.co.ipay.metermng.mybatis.generated.model.UpMeterInstallHistExample.Criteria;
import za.co.ipay.metermng.mybatis.generated.model.UpPricingStructureHist;
import za.co.ipay.metermng.mybatis.generated.model.UpPricingStructureHistExample;
import za.co.ipay.metermng.mybatis.generated.model.UsagePoint;
import za.co.ipay.metermng.server.mybatis.mapper.IUsagePointHistoryMapper;
import za.co.ipay.metermng.shared.CustomerUsagePointMiscInfo;
import za.co.ipay.metermng.shared.MeterMngStatics;
import za.co.ipay.metermng.shared.UsagePointHistData;
import za.co.ipay.metermng.shared.dto.UpMeterInstallHistData;
import za.co.ipay.metermng.shared.dto.UpPricingStructureHistData;

public class UsagePointHistService {
	
    private IUsagePointHistoryMapper iUsagePointHistoryMapperImpl;
    private UpMeterInstallHistMapper upMeterInstallHistMapper;
    private UpPricingStructureHistMapper upPricingStructureHistMapper;
    private MeterService meterService;
    private UsagePointService usagePointService;
    private PricingStructureService pricingStructureService;
    private SpecialActionsService specialActionsService;
    
    public void setUsagePointHistoryMapper(IUsagePointHistoryMapper iUsagePointHistoryMapper) {
        this.iUsagePointHistoryMapperImpl = iUsagePointHistoryMapper;
    }
    
    public void setUpMeterInstallHistMapper(UpMeterInstallHistMapper upMeterInstallHistMapper) {
        this.upMeterInstallHistMapper = upMeterInstallHistMapper;
    }
    
    public void setUpPricingStructureHistMapper(UpPricingStructureHistMapper upPricingStructureHistMapper) {
        this.upPricingStructureHistMapper = upPricingStructureHistMapper;
    }
    
    public void setMeterService(MeterService meterService) {
        this.meterService = meterService;
    }
    
    public void setUsagePointService(UsagePointService usagePointService) {
        this.usagePointService = usagePointService;
    }
    
    public void setPricingStructureService(PricingStructureService pricingStructureService) {
        this.pricingStructureService = pricingStructureService;
    }
    
    public void setSpecialActionsService(SpecialActionsService specialActionsService) {
        this.specialActionsService = specialActionsService;
    }
    
    @Transactional(readOnly=true)
    public List<UsagePointHistData> getUsagePointHistoryByUsagePointId(Long upId) {
        return iUsagePointHistoryMapperImpl.getUsagePointHistoryByUsagePointId(upId);
    }
    
    @Transactional(readOnly=true)
    public Long getLatestUsagePointHistoryIdByUsagePointId(Long upId) {
        List<UsagePointHistData> upHistData = iUsagePointHistoryMapperImpl.getLatestUsagePointHistoryIdByUsagePointId(upId, new RowBounds(0, 1));
        if (upHistData != null && !upHistData.isEmpty()) {
            return iUsagePointHistoryMapperImpl.getLatestUsagePointHistoryIdByUsagePointId(upId, new RowBounds(0, 1)).get(0).getUsagePointHistId();    
        } else {
            return null;
        }
    }

    @Transactional(readOnly = true)
    public ArrayList<UpMeterInstallHistData> getUpMeterInstallHistDataList(Long usagePointId, Long meterId, boolean usingAccessGroup) {
        UpMeterInstallHistExample upMeterInstallHistExample = new UpMeterInstallHistExample();
        Criteria criteria = upMeterInstallHistExample.createCriteria();
        if (meterId == null) {
            criteria.andUsagePointIdEqualTo(usagePointId);
        } else {
            criteria.andMeterIdEqualTo(meterId);
        }
        upMeterInstallHistExample.setOrderByClause("up_meter_install_hist_id desc");
        ArrayList<UpMeterInstallHistData> upMeterInstallHistDataList = new ArrayList<UpMeterInstallHistData>();
        upMeterInstallHistMapper.selectByExample(upMeterInstallHistExample).forEach(upMeterInstallHist -> {
            UpMeterInstallHistData upMeterInstallHistData = new UpMeterInstallHistData();
            upMeterInstallHistData.setUpMeterInstallHist(upMeterInstallHist);
            if (meterId == null) {
                Long tempMeterId = upMeterInstallHist.getMeterId();
                if (tempMeterId != null) {
                    Meter meter = meterService.getMeterById(tempMeterId);
                    if (meter == null && usingAccessGroup) {
                        upMeterInstallHistData.setMeterNumber(MeterMngStatics.ACCESSGROUP_RLS_HIDDEN_DATA);
                    } else if (meter != null) {
                        upMeterInstallHistData.setMeterNumber(meter.getMeterNum());
                    }
                }
            } else {
                Long tempUsagePointId = upMeterInstallHist.getUsagePointId();
                if (tempUsagePointId != null) {
                    UsagePoint up = usagePointService.getUsagePointById(tempUsagePointId);
                    if (up == null  && usingAccessGroup) {
                        upMeterInstallHistData.setUsagePointName(MeterMngStatics.ACCESSGROUP_RLS_HIDDEN_DATA);
                    } else if (up != null) {
                        upMeterInstallHistData.setUsagePointName(up.getName());
                    }   
                }
            }
            upMeterInstallHistDataList.add(upMeterInstallHistData);
        });
        return upMeterInstallHistDataList;
    }
    
    @Transactional(readOnly = true)
    public List<UpPricingStructureHistData> getUpPricingStructureHistDataList(Long usagePointId, boolean usingAccessGroup) {
        UpPricingStructureHistExample example = new UpPricingStructureHistExample();
        example.createCriteria().andUsagePointIdEqualTo(usagePointId);
        example.setOrderByClause("date_rec_modified desc");
        List<UpPricingStructureHist> upPSHistList = upPricingStructureHistMapper.selectByExample(example);
        
        List<UpPricingStructureHistData> upPSHistDataList = new ArrayList<UpPricingStructureHistData>();
        UpPricingStructureHistData histData = null;
        PricingStructure pricingStructure = null;
        for (UpPricingStructureHist hist : upPSHistList) {
            histData = new UpPricingStructureHistData(hist);
            pricingStructure = pricingStructureService.getPricingStructure(hist.getPricingStructureId());
            if (pricingStructure == null && usingAccessGroup) {
                histData.setPricingStructureName(MeterMngStatics.ACCESSGROUP_RLS_HIDDEN_DATA);
            } else {
                histData.setPricingStructureName(pricingStructure.getName());
            }
            if (hist.getChangeReasonLogId() != null) {
                histData.setPsChangeReasonLogText(specialActionsService.getSpecialActionReasonsLog(hist.getChangeReasonLogId()).getReasonText());
            }
            upPSHistDataList.add(histData);
        }
        return upPSHistDataList;
    }

    @Transactional(readOnly = true)
    public CustomerUsagePointMiscInfo getLatestCustomerUsagePointMiscInfoByUsagePointId(long usagePointId, Date date) {
        List<CustomerUsagePointMiscInfo> customerUsagePointMiscInfoList = iUsagePointHistoryMapperImpl
                .getLatestCustomerUsagePointMiscInfoByUsagePointId(usagePointId, new Timestamp(date.getTime()));
        if (customerUsagePointMiscInfoList.isEmpty()) {
            return null;
        }
        return customerUsagePointMiscInfoList.get(0);
    }
}
