package za.co.ipay.metermng.server.rpc;

import java.util.ArrayList;

import org.apache.log4j.Logger;

import za.co.ipay.gwt.common.shared.exception.AccessControlException;
import za.co.ipay.gwt.common.shared.exception.ServiceException;
import za.co.ipay.metermng.client.rpc.NdpRpc;
import za.co.ipay.metermng.mybatis.generated.model.NdpSchedule;
import za.co.ipay.metermng.mybatis.generated.model.NdpSeason;
import za.co.ipay.metermng.mybatis.generated.model.NdpSpecialDay;
import za.co.ipay.metermng.server.mybatis.service.NdpService;
import za.co.ipay.metermng.shared.NdpDayProfileData;
import za.co.ipay.metermng.shared.NdpScheduleData;

public class NdpRpcImpl extends BaseMeterMngRpc implements NdpRpc {

    private static final long serialVersionUID = 2399014789877255533L;
    
    private NdpService ndpService;
    
    public NdpRpcImpl() {
        this.logger = Logger.getLogger(NdpRpcImpl.class);
    }
    
    public void setNdpService(NdpService ndpService) {
        this.ndpService = ndpService;
    }

    
    @Override
    public NdpScheduleData getGlobalNdpScheduleData() throws ServiceException, AccessControlException{
        return ndpService.getGlobalNdpScheduleData();
    }
    
    @Override
    public NdpScheduleData getNdpScheduleData(Long ndpScheduleId) throws ServiceException, AccessControlException{
        return ndpService.getNdpScheduleData(ndpScheduleId);
    }
    
    @Override
    public ArrayList<NdpDayProfileData> getNdpSeasonDayProfilesData(Long ndpSeasonId) throws ServiceException, AccessControlException{
        return (ArrayList<NdpDayProfileData>) ndpService.getNdpSeasonDayProfilesData(ndpSeasonId);
    }
    
    @Override
    public ArrayList<NdpDayProfileData> getNdpSpecialDayProfilesData(Long ndpSpecialDayId) throws ServiceException, AccessControlException{
        return (ArrayList<NdpDayProfileData>) ndpService.getNdpSpecialDayProfilesData(ndpSpecialDayId);
    }
    
    @Override
    public Boolean isDayProfilePresentForSchedule(ArrayList<NdpSeason> checkTheseSeasons, ArrayList<NdpSpecialDay> checkTheseSpecialDays, NdpDayProfileData excludeDeleteDay, ArrayList<NdpDayProfileData> deleteTheseDayProfiles) throws ServiceException, AccessControlException {
        return ndpService.isDayProfilePresentForSchedule(checkTheseSeasons, checkTheseSpecialDays, excludeDeleteDay, deleteTheseDayProfiles);
    }
    
    
    
    @Override 
    public NdpSchedule addNdpSchedule(Boolean isGlobal)  throws ServiceException, AccessControlException{
        return ndpService.addNdpSchedule(isGlobal);
    }
    
    @Override 
    public NdpSchedule saveNdpSchedule(NdpSchedule ndpSchedule) throws  ServiceException, AccessControlException {
        return ndpService.saveNdpSchedule(ndpSchedule);
    }
    
    @Override
    public NdpSeason saveNdpSeasonAndDays(NdpSeason ndpSeason, ArrayList<NdpDayProfileData> ndpDayProfileDataList, ArrayList<NdpDayProfileData> deleteTheseDayProfiles) throws ServiceException, AccessControlException {
        return ndpService.saveNdpSeasonAndDays(ndpSeason, ndpDayProfileDataList, deleteTheseDayProfiles);
    }
    
    @Override
    public NdpSpecialDay saveNdpSpecialDayAndTimes(NdpSpecialDay ndpSpecialDay, ArrayList<NdpDayProfileData> dayProfileList, ArrayList<NdpDayProfileData> deleteTheseDayProfiles) throws  ServiceException, AccessControlException{
        return ndpService.saveNdpSpecialDayAndTimes(ndpSpecialDay, dayProfileList, deleteTheseDayProfiles);
    }
    
    @Override
    public NdpSeason getNdpSeasonByPrimary(Long ndpSeasonId) throws ServiceException, AccessControlException{
        return ndpService.getNdpSeasonByPrimary(ndpSeasonId);
    }

    @Override
    public NdpDayProfileData setTimeInMillis(NdpDayProfileData ndpd) {
        return ndpService.setTimeInMillis(ndpd);
    }
    
    @Override
    public void deleteNdpScheduleByPrimaryKey(Long ndpScheduleId) throws ServiceException {
        ndpService.deleteNdpScheduleByPrimaryKey(ndpScheduleId);
    }
    
    @Override
    public void deleteSeasonAndDayProfiles(Long ndpSeasonId) throws ServiceException, AccessControlException{
        ndpService.deleteSeasonAndDayProfiles(ndpSeasonId);
    }
    
    @Override
    public void deleteSpecialDayAndDayProfiles(Long ndpSpecialDayId) throws ServiceException, AccessControlException{
        ndpService.deleteSpecialDayAndDayProfiles(ndpSpecialDayId);
    }
}
