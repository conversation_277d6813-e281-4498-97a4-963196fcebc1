package za.co.ipay.metermng.server.mybatis.service;

import java.util.List;

import org.apache.log4j.Logger;
import org.quartz.Job;
import org.quartz.JobDataMap;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.quartz.JobKey;

import za.co.ipay.gwt.common.server.util.ExposedReloadableResourceBundleMessageSource;
import za.co.ipay.gwt.common.shared.exception.ServiceException;
import za.co.ipay.gwt.common.shared.exception.ValidationException;
import za.co.ipay.metermng.mybatis.generated.model.TaskSchedule;
import za.co.ipay.metermng.server.task.Task;
import za.co.ipay.metermng.server.task.TaskFactory;
import za.co.ipay.metermng.shared.dto.schedule.ScheduledTaskDto;

public class SchedulingTaskScheduleJob implements Job {
    
    private static Logger logger = Logger.getLogger(SchedulingTaskScheduleJob.class);
    
    public SchedulingTaskScheduleJob() {
        
    }

    @Override
    public void execute(JobExecutionContext context) throws JobExecutionException {
        JobKey key = context.getJobDetail().getKey();            
        JobDataMap dataMap = context.getJobDetail().getJobDataMap();
        Long taskScheduleId = dataMap.getLong(SchedulingService.TASK_SCHEDULE_ID);        
        ScheduleService scheduleService = (ScheduleService) dataMap.get(SchedulingService.SCHEDULE_SERVICE);
        MeterService meterService = (MeterService) dataMap.get(SchedulingService.METER_SERVICE);
        AppSettingService appSettingService = (AppSettingService) dataMap.get(SchedulingService.APPSETTING_SERVICE);
        ExposedReloadableResourceBundleMessageSource messageSource = (ExposedReloadableResourceBundleMessageSource) dataMap.get(SchedulingService.MESSAGE_SOURCE);
        ExposedReloadableResourceBundleMessageSource formatSource = (ExposedReloadableResourceBundleMessageSource) dataMap.get(SchedulingService.FORMAT_SOURCE);
        EmailService emailService = (EmailService) dataMap.get(SchedulingService.EMAIL_SERVICE);
        boolean useDefaultLocale = dataMap.getBoolean(SchedulingService.USE_DEFAULT_LOCALE);
        String defaultLocaleName = dataMap.getString(SchedulingService.DEFAULT_LOCALE_NAME);
        if (taskScheduleId != null && scheduleService != null) {                 
            logger.info("Executing job: "+key+" for taskScheduleId: "+taskScheduleId);
            try {
                TaskSchedule taskSchedule = scheduleService.getTaskSchedule(taskScheduleId);
                List<ScheduledTaskDto> scheduledTasks = scheduleService.getScheduleTasks(taskScheduleId);
                logger.debug("ScheduledTasks: "+scheduledTasks.size());
                for(ScheduledTaskDto scheduledTask : scheduledTasks) {
                    executeTask(scheduleService, meterService, messageSource, formatSource, emailService, taskSchedule, scheduledTask, 
                            useDefaultLocale, defaultLocaleName, appSettingService);
                }
                logger.info("Executed job: "+key+" for taskScheduleId: "+taskScheduleId);                
            } catch (ValidationException e) {
                logger.error("Invalid job input:", e);
            } catch (ServiceException e) {
                logger.error("Error execuing job:", e);
            }
        } else {
            logger.error("TaskScheduleJob: "+key+" has invalid input id:"+taskScheduleId+" scheduleService:"+scheduleService);
        }
    }  
    
    private void executeTask(ScheduleService scheduleService, 
                               MeterService meterService, 
                               ExposedReloadableResourceBundleMessageSource messageSource,
                               ExposedReloadableResourceBundleMessageSource formatSource,
                               EmailService emailService,
                               TaskSchedule taskSchedule,
                               ScheduledTaskDto scheduledTaskDto,
                               boolean useDefaultLocale, String defaultLocaleName,
                               AppSettingService appSettingService) throws ServiceException {
        Task task = TaskFactory.getScheduledTask(scheduledTaskDto.getScheduledTask().getTaskClassId());
        if (task != null) {
            logger.info("Executing task:"+task.getClass().getSimpleName()+" for scheduledTask: "+taskSchedule.getTaskScheduleName());
            task.setScheduleService(scheduleService);
            task.setMeterService(meterService);
            task.setAppSettingService(appSettingService);
            task.setMessageSource(messageSource);
            task.setFormatSource(formatSource);
            task.setEmailService(emailService);
            task.setUseDefaultLocale(useDefaultLocale);
            task.setDefaultLocaleName(defaultLocaleName);
            task.execute(taskSchedule, scheduledTaskDto);
        } else {
            throw new ServiceException("Unknown Task for "+scheduledTaskDto);
        }
    }
}
