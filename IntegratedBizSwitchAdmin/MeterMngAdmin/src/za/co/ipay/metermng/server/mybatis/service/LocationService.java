package za.co.ipay.metermng.server.mybatis.service;

import java.util.ArrayList;
import java.util.List;

import org.springframework.transaction.annotation.Transactional;

import za.co.ipay.gwt.common.shared.exception.ServiceException;
import za.co.ipay.gwt.common.shared.exception.ValidationException;
import za.co.ipay.metermng.mybatis.custom.mapper.IGenGroupMapper;
import za.co.ipay.metermng.mybatis.custom.model.GenGroupDepth;
import za.co.ipay.metermng.mybatis.generated.mapper.LocationHistMapper;
import za.co.ipay.metermng.mybatis.generated.mapper.LocationMapper;
import za.co.ipay.metermng.mybatis.generated.model.Location;
import za.co.ipay.metermng.mybatis.generated.model.LocationHist;
import za.co.ipay.metermng.mybatis.generated.model.LocationHistExample;
import za.co.ipay.metermng.server.validation.ServerValidatorUtil;
import za.co.ipay.metermng.shared.dto.LocationData;

public class LocationService {
    
    private LocationMapper locationMapper;
    private IGenGroupMapper iGenGroupMapper;
    private LocationHistMapper locationHistMapperImpl;
    
    public void setLocationMapper(LocationMapper locationMapper) {
        this.locationMapper = locationMapper;
    }
    
    public void setIGenGroupMapper(IGenGroupMapper iGenGroupMapper) {
        this.iGenGroupMapper = iGenGroupMapper;
    }
    
    public void setLocationHistMapper(LocationHistMapper locationHistMapper) {
        this.locationHistMapperImpl = locationHistMapper;
    }
    
    @Transactional(readOnly=true)
    public LocationData getLocationDataById(Long locationId) {
        LocationData locationData = null;
        if (locationId != null) {
            Location loc = locationMapper.selectByPrimaryKey(locationId);
            if (loc != null) {
                locationData = new LocationData(loc);
                
                locationData.setGroupTypeId(iGenGroupMapper.getGroupTypeIdByGenGroupId(locationData.getLocGroupId()));
                if (locationData.getLocGroupId() != null) {
                    locationData.setDepthList(getPath(locationData.getLocGroupId()));
                }
            }
        } 
        return locationData;
    }
    
    @Transactional(readOnly=true)
    public Location getLocationById(Long locationId) {
        return locationMapper.selectByPrimaryKey(locationId);
    }
    
    @Transactional
    public LocationData saveLocation(Location location) throws ValidationException, ServiceException {
        ServerValidatorUtil.getInstance().validateDataForValidationMessages(location);
        if (location.getId() == null) {
            if (locationMapper.insert(location) != 1) {
                throw new ServiceException("location.error.save");
            }
        } else {
            updateLocation(location);
        }
        return getLocationDataById(location.getId());
    }
    
    @Transactional
    public int updateLocation(Location location) {
        int updated = 0;
        updated = locationMapper.updateByPrimaryKey(location);
        if (updated != 1) {
            throw new ServiceException("location.error.save");
        }
        return updated;
    }
    
    @Transactional(readOnly=true)
    public ArrayList<Long> getPath(Long groupId) {
        ArrayList<Long> depthList = new ArrayList<Long>();
        List<GenGroupDepth> serverList = iGenGroupMapper.getPathToRootGroup(groupId);        
        Long genGroupId = null;
        for (int j=0; j< serverList.size(); j++) {
            genGroupId = serverList.get(j).getGenGroupId();
            depthList.add(genGroupId);
        }
        return depthList;
    }
    
    
    @Transactional(readOnly=true)
    public List<LocationHist> getLocationHistoryByLocationId(Long locationId) {
        LocationHistExample locationHistExample = new LocationHistExample();
        locationHistExample.createCriteria().andIdEqualTo(locationId);
        locationHistExample.setOrderByClause("date_rec_modified desc");
        return locationHistMapperImpl.selectByExample(locationHistExample);
    }
    
}
