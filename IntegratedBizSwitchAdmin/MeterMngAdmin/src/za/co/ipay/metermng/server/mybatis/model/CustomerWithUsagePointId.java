package za.co.ipay.metermng.server.mybatis.model;

import java.io.Serializable;

import za.co.ipay.metermng.mybatis.generated.model.Customer;

public class CustomerWithUsagePointId extends Customer implements Serializable {

	private static final long serialVersionUID = 2109971708327857463L;

	private String usagePointName;
	private Long usagePointId;
	private Long customerAgreementId;
	private String customerAgreementRef;
	private Long customerId;
	private String customerIdNumber;
	
	public String getUsagePointName() {
		return usagePointName;
	}
	
	public void setUsagePointName(String usagePointName) {
		this.usagePointName = usagePointName;
	}
	
	public Long getUsagePointId() {
		return usagePointId;
	}
	
	public void setUsagePointId(Long usagePointId) {
		this.usagePointId = usagePointId;
	}

    public Long getCustomerAgreementId() {
        return customerAgreementId;
    }

    public void setCustomerAgreementId(Long customerAgreementId) {
        this.customerAgreementId = customerAgreementId;
    }

    public String getCustomerAgreementRef() {
        return customerAgreementRef;
    }

    public void setCustomerAgreementRef(String customerAgreementRef) {
        this.customerAgreementRef = customerAgreementRef;
    }

    public Long getCustomerId() {
        return customerId;
    }

    public void setCustomerId(Long customerId) {
        this.customerId = customerId;
    }

    public String getCustomerIdNumber() {
        return customerIdNumber;
    }

    public void setCustomerIdNumber(String customerIdNumber) {
        this.customerIdNumber = customerIdNumber;
    }
}
