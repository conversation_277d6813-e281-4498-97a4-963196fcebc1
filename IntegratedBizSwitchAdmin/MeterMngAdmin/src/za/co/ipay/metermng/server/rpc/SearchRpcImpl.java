package za.co.ipay.metermng.server.rpc;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Optional;

import org.apache.log4j.Logger;

import com.google.gwt.user.client.ui.SuggestOracle.Request;
import com.google.gwt.user.client.ui.SuggestOracle.Response;

import za.co.ipay.gwt.common.shared.exception.ServiceException;
import za.co.ipay.gwt.common.shared.exception.ValidationException;
import za.co.ipay.gwt.common.shared.exception.ValidationMessage;
import za.co.ipay.metermng.client.rpc.SearchRpc;
import za.co.ipay.metermng.datatypes.MeterTypeE;
import za.co.ipay.metermng.datatypes.RecordStatus;
import za.co.ipay.metermng.mybatis.custom.model.MeterDto;
import za.co.ipay.metermng.mybatis.generated.model.Customer;
import za.co.ipay.metermng.mybatis.generated.model.CustomerAccount;
import za.co.ipay.metermng.mybatis.generated.model.CustomerAgreement;
import za.co.ipay.metermng.mybatis.generated.model.EndDeviceStore;
import za.co.ipay.metermng.mybatis.generated.model.GenGroup;
import za.co.ipay.metermng.mybatis.generated.model.Meter;
import za.co.ipay.metermng.mybatis.generated.model.MeterHist;
import za.co.ipay.metermng.mybatis.generated.model.UpMeterInstall;
import za.co.ipay.metermng.mybatis.generated.model.UsagePoint;
import za.co.ipay.metermng.server.mybatis.model.CustomerWithUsagePointId;
import za.co.ipay.metermng.server.mybatis.service.BlockingTypeService;
import za.co.ipay.metermng.server.mybatis.service.CustomerAccountService;
import za.co.ipay.metermng.server.mybatis.service.CustomerAgreementService;
import za.co.ipay.metermng.server.mybatis.service.CustomerHistService;
import za.co.ipay.metermng.server.mybatis.service.CustomerService;
import za.co.ipay.metermng.server.mybatis.service.DeviceStoreService;
import za.co.ipay.metermng.server.mybatis.service.GroupService;
import za.co.ipay.metermng.server.mybatis.service.LocationService;
import za.co.ipay.metermng.server.mybatis.service.MeterHistService;
import za.co.ipay.metermng.server.mybatis.service.MeterModelService;
import za.co.ipay.metermng.server.mybatis.service.MeterService;
import za.co.ipay.metermng.server.mybatis.service.MeterTypeService;
import za.co.ipay.metermng.server.mybatis.service.OnlineBulkMeterService;
import za.co.ipay.metermng.server.mybatis.service.STSMeterHistService;
import za.co.ipay.metermng.server.mybatis.service.STSMeterService;
import za.co.ipay.metermng.server.mybatis.service.SearchService;
import za.co.ipay.metermng.server.mybatis.service.SpecialActionsService;
import za.co.ipay.metermng.server.mybatis.service.UnitTransService;
import za.co.ipay.metermng.server.mybatis.service.UpGenGroupLnkService;
import za.co.ipay.metermng.server.mybatis.service.UsagePointHistService;
import za.co.ipay.metermng.server.mybatis.service.UsagePointService;
import za.co.ipay.metermng.server.mybatis.service.AuxAccountHistService;
import za.co.ipay.metermng.server.util.MeterMngUtil;
import za.co.ipay.metermng.shared.CustomerAccountSuggestion;
import za.co.ipay.metermng.shared.CustomerAgreementSuggestion;
import za.co.ipay.metermng.shared.CustomerSuggestion;
import za.co.ipay.metermng.shared.MeterMngStatics;
import za.co.ipay.metermng.shared.MeterOnlineBulkData;
import za.co.ipay.metermng.shared.UsagePointSuggestion;
import za.co.ipay.metermng.shared.dto.CustomerAgreementData;
import za.co.ipay.metermng.shared.dto.CustomerData;
import za.co.ipay.metermng.shared.dto.HistoryData;
import za.co.ipay.metermng.shared.dto.MdcChannelReadingsDto;
import za.co.ipay.metermng.shared.dto.MeterData;
import za.co.ipay.metermng.shared.dto.MeterOnlineBulkDto;
import za.co.ipay.metermng.shared.dto.UpGenGroupLinkData;
import za.co.ipay.metermng.shared.dto.UsagePointData;
import za.co.ipay.metermng.shared.dto.meter.MeterSearchDto;
import za.co.ipay.metermng.shared.dto.search.SearchData;
import za.co.ipay.metermng.shared.dto.usagepoint.UsagePointFetchDto;
import za.co.ipay.metermng.shared.dto.user.MeterMngUser;

public class SearchRpcImpl extends BaseMeterMngRpc implements SearchRpc {

    private static final long serialVersionUID = -7598457805750779770L;

    private STSMeterService stsMeterService;
    private CustomerService customerService;
    private CustomerAgreementService customerAgreementService;
    private CustomerAccountService customerAccountService;
    private UsagePointService usagePointService;
    private LocationService locationService;
    private UpGenGroupLnkService upGenGroupLnkService;
    private DeviceStoreService deviceStoreService;
    private SearchService searchService;
    private MeterService meterService;
    private MeterTypeService meterTypeService;
    private MeterModelService meterModelService;
    private SpecialActionsService specialActionsService;
    private OnlineBulkMeterService onlineBulkMeterService;
    private BlockingTypeService blockingTypeService;

    private MeterHistService meterHistService;
    private STSMeterHistService stsMeterHistService;
    private CustomerHistService customerHistService;
    private UsagePointHistService usagePointHistService;
    private UnitTransService unitTransService;
    private GroupService groupService;
    private AuxAccountHistService auxAccountHistService;



    public SearchRpcImpl() {
        logger = Logger.getLogger(SearchRpcImpl.class);
    }

    @Override
    public UsagePointData getUsagePointDataByMeterNum(String meterNumber) throws ServiceException {
        Meter meter = meterService.getMeterByNumber(meterNumber);
        if (meter == null) {
            return null;
        }
        return loadUsagePointData(meter);
    }

    private UsagePointData refreshUsagePointDataByMeter(UsagePointData usagePointData) {
        Optional<Meter> meter = Optional.ofNullable(meterService.getMeterById(usagePointData.getMeterId()));
        if(!meter.isPresent()){
            return getUsagePointDataByMeterNum(usagePointData.getMeterData().getMeterNum());
        }
        return loadUsagePointData(meter.get());
    }

    private UsagePointData loadUsagePointData(Meter meter) {

        MeterMngUser user = getUser();
        UsagePointData usagePointData = new UsagePointData();
        Optional<MeterData> optMeterData = getMeterData(meter);
        if (!optMeterData.isPresent()) {
            return null;
        }
        MeterData meterData = optMeterData.get();
        if (user.getCurrentGroupId() != null) {
            if (meterData.getEndDeviceStoreId() != null) {
                // check enddevicestore group = users group
                EndDeviceStore endDeviceStore = deviceStoreService.getDeviceStore(meterData.getEndDeviceStoreId());
                if (endDeviceStore.getGenGroupId() != null
                    && !endDeviceStore.getGenGroupId().equals(getUser().getCurrentGroupId())) {
                    usagePointData.setErrorMessage("meter.found.incorrect.group");
                    return usagePointData;
                }
            }
        }
        usagePointData.setMeterData(meterData);
        // Get Usage Point Data
        UsagePoint theUsagePoint = usagePointService.getUsagePointByMeterId(meterData.getId());
        if (theUsagePoint != null) {
            // check the usage point is in the same group as the user
            if (user.getCurrentGroupId() != null && theUsagePoint.getGenGroupId() != null
                && !theUsagePoint.getGenGroupId().equals(user.getCurrentGroupId())) {
                usagePointData.setErrorMessage("meter.found.incorrect.group");
                usagePointData.setMeterData(null);
                return usagePointData;
            }
            usagePointData.setUsagePoint(theUsagePoint);
        }

        Long usagePointId = usagePointData.getId();
        if (usagePointId != null) {
            // Set CustomerAgreementData
            getCustomerAgreementDataById(usagePointData.getCustomerAgreementId())
                .ifPresent(usagePointData::setCustomerAgreementData);
            setCommonUsagePointFields(usagePointData, theUsagePoint);
        } else {
            setHistoryIds(usagePointData);
        }
        return usagePointData;
    }

    private Optional<MeterData> getMeterData(Meter meter) {
        MeterData meterData = new MeterData(meter);
        meterData.setMeterType(meterTypeService.getMeterType(meterData.getMeterTypeId()));
        if (meterData.getMeterTypeId().equals(MeterTypeE.STS.getId())) {
            meterData.setStsMeter(stsMeterService.getMeterByMeterNumber(meter.getMeterNum()));
            if (meterData.getStsMeter() == null) {
                return Optional.empty();
            }
        }

        meterData.setMeterModelData(meterModelService.getMeterModelById(meterData.getMeterModelId()));
        return Optional.of(meterData);
    }

    private Optional<CustomerAgreementData> getCustomerAgreementDataById(Long customerAgreementId) {
        if (customerAgreementId != null) {
            CustomerAgreementData agreementData = new CustomerAgreementData(
                    customerAgreementService.getCustomerAgreementById(customerAgreementId));
            agreementData.setCustomerData(fetchCustomerDataById(agreementData.getCustomerId()));
            agreementData.setCustomerAccount(fetchCustomerAccountById(agreementData.getCustomerAccountId()));
            return Optional.of(agreementData);
        }
        return Optional.empty();
    }

    @Override
    public boolean checkUsagePointDataIntegrity(UsagePointData clientUsagePointData) throws ServiceException {

        Long clientMeterId = clientUsagePointData.getMeterId();
        Long clientUpId = clientUsagePointData.getId();
        Long clientCustAgreeId = clientUsagePointData.getCustomerAgreementId();
        Long clientCustAccTransHistId = clientUsagePointData.getHistoryData().getLatestCustomerAccountTransHistId();
        Long clientAuxAccountId = clientUsagePointData.getHistoryData().getLatestAuxAccountHistId();
        Long clientAuxAccountAdjustmentId = clientUsagePointData.getHistoryData().getLatestAuxAccountAdjustmentId();
        Long clientUnitTransid = clientUsagePointData.getHistoryData().getLatestUnitTransId();


        UsagePointData serverUsagePointData = null;
        if (clientUpId != null) {
            serverUsagePointData = refreshUsagePointDataByClientUsagePointData(clientUsagePointData);
        }
        if (clientMeterId != null && serverUsagePointData == null) {
            serverUsagePointData = refreshUsagePointDataByMeter(clientUsagePointData);
        }
        if (clientCustAgreeId != null) {
            CustomerAgreementData agreement = clientUsagePointData.getCustomerAgreementData();
            if (serverUsagePointData == null) {
                serverUsagePointData = getUsagePointByCustomerAgreement(agreement, agreement.getCustomerAccount());
            } else if (serverUsagePointData.getCustomerAgreementId() == null) {
                // Customer Agreement Id may not have been bound to client UsagePoint, and may have been fetched
                // therefore, bind customer agreement with UP (only for multi-UP)
                UsagePointData upData = getUsagePointByCustomerAgreement(agreement, agreement.getCustomerAccount());
                if (((upData.getMeterId() != null || upData.getMeterData() != null || upData.getId() != null)
                        && clientUsagePointData.isMultiUsagePointEnabled())
                        || (upData.getMeterData() == null && upData.getMeterId() == null && upData.getId() == null)) {
                    serverUsagePointData.setCustomerAgreementData(upData.getCustomerAgreementData());
                    serverUsagePointData.setCustomerAgreementId(upData.getCustomerAgreementId());
                }
            }
        }

        if(serverUsagePointData == null){
            serverUsagePointData = new UsagePointData();
        }
        setHistoryIds(serverUsagePointData);

        if(clientUnitTransid != null){
            serverUsagePointData.setHistoryData(setLatestUnitTransId(serverUsagePointData));
        }
        if(clientCustAccTransHistId != null){
            serverUsagePointData.setHistoryData(setLatestCustAccTransId(serverUsagePointData));
        }
        if(clientAuxAccountId != null){
            serverUsagePointData.setHistoryData(setLatestAuxAccountId(serverUsagePointData));
        }
        if(clientAuxAccountAdjustmentId != null){
            serverUsagePointData.setHistoryData(setLatestAuxAdjustmentId(serverUsagePointData));
        }

        return UsagePointData.isDataValid(serverUsagePointData, clientUsagePointData);
    }
    @Override
    public HistoryData setHistoryIds(UsagePointData usagePointData) {
        Long meterHistId = null;
        Long stsMeterHistId = null;
        Long custHistId = null;
        Long custAgreeHistId = null;
        Long custAccountHistId = null;
        Long upHistId = null;

        if(usagePointData.getMeterId() != null) {
            MeterHist meterHistoryData = meterHistService.getLatestMeterHistoryByMeterId(usagePointData.getMeterId());
            if (meterHistoryData != null) {
                meterHistId = meterHistoryData.getMeterHistId();
                if (meterHistoryData.getMeterTypeId() == MeterTypeE.STS.getId()) {
                    stsMeterHistId = stsMeterHistService.getLatestMeterHistoryIdByMeterId(usagePointData.getMeterId());
                }
            }
        }
        
        if(usagePointData.getCustomerAgreementData() != null) {
            custHistId =customerHistService.getLatestCustomerHistoryIdByCustomerId(usagePointData.getCustomerAgreementData().getCustomerId());
            custAgreeHistId = customerHistService.getLatestCustomerAgreementHistoryIdById(usagePointData.getCustomerAgreementId());
            custAccountHistId = customerHistService.getLatestCustomerAccountHistoryIdById(usagePointData.getCustomerAgreementData().getCustomerAccountId());
        }
        
        if(usagePointData.getId() != null) {
            upHistId = usagePointHistService.getLatestUsagePointHistoryIdByUsagePointId(usagePointData.getId());
        }

        HistoryData historyData = new HistoryData(meterHistId, stsMeterHistId, custHistId, custAgreeHistId, custAccountHistId, upHistId);
        historyData.setMultiUsagePointEnabled(usagePointData.isMultiUsagePointEnabled());
        usagePointData.setHistoryData(historyData);
        return historyData;
    }

    @Override
    public HistoryData setLatestCustAccTransId(UsagePointData usagePointData) {
        Long custAccTransIdList = null;

        if (usagePointData.getCustomerAgreementData() != null) {
            custAccTransIdList = customerHistService.getLatestCustomerAccountTransHistIdByCustomerId(usagePointData.getCustomerAgreementData().getCustomerAccountId());
        }
        HistoryData historyData = usagePointData.getHistoryData();
        historyData.setLatestCustomerAccountTransHistId(custAccTransIdList);
        return historyData;
    }

    @Override
    public HistoryData setLatestAuxAccountId(UsagePointData usagePointData) {
        Long aaHistId = null;

        if (usagePointData.getCustomerAgreementData() != null) {
            aaHistId = auxAccountHistService.getLatestAuxAccountHistoryIdByCustomerAgreementId(usagePointData.getCustomerAgreementId());
        }
        HistoryData historyData = usagePointData.getHistoryData();
        historyData.setLatestAuxAccountHistId(aaHistId);
        return historyData;
    }

    @Override
    public HistoryData setLatestAuxAdjustmentId(UsagePointData usagePointData) {
        Long auxAccountAdjustmentId = null;

        if (usagePointData.getCustomerAgreementId() != null) {
            auxAccountAdjustmentId = customerHistService.getLatestAccountTransIdByCustomerAgreementId(usagePointData.getCustomerAgreementId());
        }
        HistoryData historyData = usagePointData.getHistoryData();
        historyData.setLatestAuxAccountAdjustmentId(auxAccountAdjustmentId);
        return historyData;
    }
    @Override
    public HistoryData setLatestUnitTransId(UsagePointData usagePointData) {
        Long unitTransId = null;

        if (usagePointData.getCustomerAgreementId() != null) {
            unitTransId = unitTransService.getLatestUnitsTransIdByUsagePoint(usagePointData.getId());
        }
        HistoryData historyData = usagePointData.getHistoryData();
        historyData.setLatestUnitTransId(unitTransId);
        return historyData;
    }




    // usagePointData is the new data building up, usagePoint is the current usagePoint on DB
    private void setCommonUsagePointFields(UsagePointData usagePointData, UsagePoint usagePoint) {
        // Get Service location
        Long serviceLocationId = usagePointData.getServiceLocationId();
        if (serviceLocationId != null) {
            usagePointData.setServiceLocation(locationService.getLocationDataById(serviceLocationId));
        }
        // Get UsagePoint location
        Long upLocationId = usagePointData.getUpLocationId();
        if (upLocationId != null) {
            usagePointData.setUsagepointLocation(locationService.getLocationDataById(upLocationId));
        }
        
        Long usagePointId = usagePoint.getId();
        // Get usagepoint groups
        ArrayList<UpGenGroupLinkData> groups = upGenGroupLnkService.getUpGenGroupListByUsagePointId(usagePointId);
        HashMap<Long, UpGenGroupLinkData> groupsMap = new HashMap<>();
        if (groups != null) {
            groups.forEach(group -> groupsMap.put(group.getGroupTypeId(), group));
        }
        usagePointData.setUpgengroups(groupsMap);

        // Set PricingStructure
        usagePointData.setUpPricingStructureData(usagePointService.getCurrentAndFutureUPPricingStructures(usagePoint.getId()));
        usagePointData.setFirstTariffStartDate(usagePointService.getUPFirstTariffStartDate(usagePoint.getId()));

        // get UpMeterInstall
        usagePointData.setUpMeterInstall(usagePointService.getLastUpMeterInstallRemoveDateIsNull(usagePointId));
        //is there an earlier upMeterInstall WITH a remove date
        //on fetch after a remove Meter when construct popup can know whether to ask about activation date
        //if there were any other meters attached to this UP ever, don't want to change activation date cos they might still get readings etal
        UpMeterInstall lastRemoveUpMeterInstall = usagePointService.getLastUpMeterInstallRemoveDate(usagePointId);
        if (lastRemoveUpMeterInstall != null) {
            usagePointData.setLastRemoveDate(lastRemoveUpMeterInstall.getRemoveDate());
        }

        // Get Special Action deactivation
        if (!usagePointData.getRecordStatus().equals(RecordStatus.ACT)) {
            Long activeStatusReasonLogId = usagePointData.getActiveStatusReasonLogId();
            if (activeStatusReasonLogId != null) {
                usagePointData.setActiveStatusUsagePointReasonsLog(
                        specialActionsService.getSpecialActionReasonsLog(activeStatusReasonLogId));
            }
        }

        // Set BlockingType
        Long blockingTypeId = usagePointData.getBlockingTypeId();
        if (blockingTypeId != null) {
            usagePointData.setBlockingType(blockingTypeService.getBlockingTypeById(blockingTypeId));
        }

        // Set BlockReasonLog
        Long blockReasonLogId = usagePointData.getBlockReasonLogId();
        if (blockReasonLogId != null) {
            usagePointData.setBlockingUsagePointReasonsLog(
                    specialActionsService.getSpecialActionReasonsLog(blockReasonLogId));
        }

        Long numReadings = usagePointService.getCountReadingsForMeterAndUP(usagePoint.getMeterId(), usagePointId);
        usagePointData.setNoTransactions(usagePoint.getLastPurchaseDate() == null
                && usagePoint.getLastCyclicChargeDate() == null
                && numReadings.compareTo(0L) == 0);

        Long unitsAccountId = usagePoint.getUnitsAccountId();
        if (unitsAccountId != null) {
            usagePointData.setUnitsAccount(usagePointService.getUnitsAccount(unitsAccountId));
        }

        setHistoryIds(usagePointData);
    }

    @Override
    public UsagePointData getUsagePointDataforFetchUsagePoint(UsagePointFetchDto usagePointFetchDto, List<MdcChannelReadingsDto> channelReadingsList) throws ServiceException {
        return getUsagePointDataByUsagePointName(usagePointService.updateUsagePointFromNewFetch(usagePointFetchDto, channelReadingsList));
    }

    // A seperate GetUsagePoint Method that will also poll tariff history to see whether there are potential outstanding BILLING cyclic charges 
    @Override
    public Boolean isBillingCyclicChargesInTariffHistSinceLastChargeDate(UsagePointData usagePointData) throws ServiceException {
        Boolean isBillingCyclicCharges = Boolean.FALSE;
        try {
            isBillingCyclicCharges = usagePointService.isBillingCyclicChargesInTariffHistSinceLastChargeDate(usagePointData);
        } catch (Exception e) {
            //Exception is thrown when can't find any tariffs for a PS - this can happen when the tariff list is truncated because users changed payment mode PS
            //OR there is a database error!
            //At any rate - there will be NO billingCharges for such a tariff history
            //this is not the place to trap a genuine database error. Will be trapped when the PS is assigned to the UP.
        }
        return isBillingCyclicCharges;
    }

    @Override
    public UsagePointData getUsagePointDataByUsagePointName(String usagePointName) throws ServiceException {
        UsagePoint usagePoint = usagePointService.getUsagePointByName(usagePointName);
        if (usagePoint == null) {
            return null;
        }
        return loadUsagePointData(usagePoint);
    }

    private UsagePointData refreshUsagePointDataByClientUsagePointData(UsagePointData upData) {
        Optional<UsagePoint> usagePoint = Optional.ofNullable(usagePointService.getUsagePointById(upData.getId()));
        if (!usagePoint.isPresent()) {
            return getUsagePointDataByUsagePointName(upData.getName());
        }
        return loadUsagePointData(usagePoint.get());
    }

    private UsagePointData loadUsagePointData(UsagePoint usagePoint) {
        MeterMngUser user = getUser();
        UsagePointData usagePointData = new UsagePointData();
        if (user.getCurrentGroupId() != null && usagePoint.getGenGroupId() != null
                && !usagePoint.getGenGroupId().equals(user.getCurrentGroupId())) {
            usagePointData.setErrorMessage("usagepoint.found.incorrect.group");
            return null;
        }
        usagePointData.setUsagePoint(usagePoint);

        if (usagePointData.getMeterId() != null) {
            // Get meter data
            Meter meter = meterService.getMeterById(usagePointData.getMeterId());
            if (meter == null) {
                return null;
            }
            Optional<MeterData> optMeterData = getMeterData(meter);
            if (!optMeterData.isPresent()){
                return null;
            }
            MeterData meterData = optMeterData.get();
            if (user.getCurrentGroupId() != null) {
                if (meterData.getEndDeviceStoreId() != null) {
                    // check enddevicestore group = users group
                    EndDeviceStore endDeviceStore = deviceStoreService.getDeviceStore(meterData.getEndDeviceStoreId());
                    if (endDeviceStore.getGenGroupId() != null
                            && !endDeviceStore.getGenGroupId().equals(getUser().getCurrentGroupId())) {
                        usagePointData.setErrorMessage("usagepoint.found.incorrect.group");
                        return usagePointData;
                    }
                }
            }
            usagePointData.setMeterData(meterData);
        } else {
            usagePointData.setMeterData(null);
        }

        // Set CustomerAgreementData
        getCustomerAgreementDataById(usagePointData.getCustomerAgreementId())
                .ifPresent(usagePointData::setCustomerAgreementData);
        setCommonUsagePointFields(usagePointData, usagePoint);

        return usagePointData;
    }

    @Override
    public Integer getOnlineBulkUpDataFromUpGroupSelectionCount(int start, int pageSize, ArrayList<UpGenGroupLinkData> selectedGroupsList, String sortColumn, String filterColumn, String filterString, Date filterDate, String order) throws ServiceException {
        return onlineBulkMeterService.getOnlineBulkUpDataFromUpGroupSelectionCount(start, pageSize, selectedGroupsList, sortColumn, filterColumn, filterString, filterDate, order);
    }


    @Override
    public MeterOnlineBulkDto getOnlineBulkUpDataFromUpGroupSelection(int start, int pageSize, ArrayList<UpGenGroupLinkData> selectedGroupsList, String sortColumn, String filterColumn, String filterString, Date filterDate, String order) throws ServiceException {
        return onlineBulkMeterService.getOnlineBulkUpDataFromUpGroupSelection(start, pageSize, selectedGroupsList, sortColumn, filterColumn, filterString, filterDate, order);
    }

    @Override
    public MeterOnlineBulkData getMeterOnlineBulkForMeterNum(String meterNum, Long deviceStoreId) throws ServiceException {
        return onlineBulkMeterService.getMeterOnlineBulkForMeterNum(meterNum, deviceStoreId);
    }

    @Override
    public MeterOnlineBulkData saveMeterOnlineBulkData(MeterOnlineBulkData mobData) throws ServiceException {
        return onlineBulkMeterService.saveMeterOnlineBulk(mobData, getUser());
    }

    @Override
    public MeterOnlineBulkData updateMeterOnlineBulkData(MeterOnlineBulkData mobData) throws ServiceException {
        return onlineBulkMeterService.updateMeterOnlineBulk(mobData, getUser());
    }

    @Override
    public CustomerAgreementData getCustomerAgreementDatabyCustId(Long customerId) throws ServiceException {
        CustomerAgreementData customerAgreementData = new CustomerAgreementData(
                customerAgreementService.getCustomerAgreementByCustomerId(customerId));

        // get customerData
        customerAgreementData.setCustomerData(fetchCustomerDataById(customerId));
        customerAgreementData.setCustomerAccount(fetchCustomerAccountById(customerAgreementData.getCustomerAccountId()));
        return customerAgreementData;
    }

    @Override
    public UsagePointData getUsagePointByCustId(String custId) throws ServiceException {
        Long customerId = Long.valueOf(custId);
        return getUsagePointByLongCustId(customerId);
    }

    private UsagePointData getUsagePointByLongCustId(Long customerId) throws ServiceException {
        CustomerAgreementData customerAgreementData = new CustomerAgreementData(
                customerAgreementService.getCustomerAgreementByCustomerId(customerId));
        return getUsagePointByCustomerAgreement(customerAgreementData, null);
    }

    @Override
    public UsagePointData getUsagePointByAgreementRef(String agreementRef) throws ServiceException {
        CustomerAgreementData customerAgreementData = new CustomerAgreementData(
                customerAgreementService.getCustomerAgreementByAgreementRef(agreementRef));
        if (customerAgreementData.getId() != null) {
            return getUsagePointByCustomerAgreement(customerAgreementData, null);
        }
        return null;
    }

    @Override
    public UsagePointData getUsagePointByAccountName(String accountName) throws ServiceException {
        CustomerAccount customerAccount = customerAccountService.getCustomerAccountByAccountName(accountName);
        if (customerAccount == null) {
            return null;
        }
        CustomerAgreementData customerAgreementData = new CustomerAgreementData(
                customerAgreementService.getCustomerAgreementByCustomerId(customerAccount.getCustomerId()));
        if (customerAgreementData.getId() != null) {
            return getUsagePointByCustomerAgreement(customerAgreementData, customerAccount);
        }
        return null;
    }

    @Override
    public UsagePointData getUsagePointByCustomerIdNumber(String idNumber) throws ServiceException {
        Customer customer = customerService.getCustomerByIdNumber(idNumber);
        if (customer == null) {
            return null;
        }
        return getUsagePointByLongCustId(customer.getId());
    }

    @Override
    public Response getCustomerSuggestions(Request req, boolean withUsagePointId, boolean isIdNumberSearch, boolean isAgreementRefSearch, int limit) throws ServiceException {
        Response response = new Response();
        ArrayList<CustomerSuggestion> suggestions = new ArrayList<>();
        String thequery = (req.getQuery() + "%").toLowerCase();
        if (withUsagePointId) {
            List<CustomerWithUsagePointId> list;
            if (isAgreementRefSearch) {
                list = customerService.getCustomerWithUpSearchByAgreementRefSuggestions(thequery, getUser().getCurrentGroupId(), limit);
            } else if (isIdNumberSearch) {
                list = customerService.getCustomerWithUpSearchByIdNumberSuggestions(thequery, getUser().getCurrentGroupId(), limit);
            } else {
                list = customerService.getCustomerWithUpSearchSuggestions(thequery, getUser().getCurrentGroupId(), limit);
            }
            Iterator<CustomerWithUsagePointId> listIt = list.iterator();
            String suggestion;
            CustomerSuggestion customerSuggestion;
            CustomerWithUsagePointId customer;
            while (listIt.hasNext()) {
                customer = listIt.next();
                suggestion = "";
                customerSuggestion = new CustomerSuggestion();
                customerSuggestion.setOriginalQuery(req.getQuery());
                suggestion += (customer.getSurname() + ", ");
                if (customer.getTitle() == null)
                    customer.setTitle("");
                if (customer.getFirstnames() == null)
                    customer.setFirstnames(" - ");
                if (customer.getInitials() == null)
                    customer.setInitials("");
                suggestion += (customer.getTitle() + " " + customer.getFirstnames() + " " + customer.getInitials()).trim();
                customerSuggestion.setCustomerName(suggestion);
                suggestion += " - ";

                if(isAgreementRefSearch){
                    suggestion += "<div>Agr Ref: "+ customer.getCustomerAgreementRef() + "</div>";
                }else if(isIdNumberSearch){
                    suggestion += "<div>ID Num: "+ customer.getCustomerIdNumber() + "</div>";
                }

                if (customer.getUsagePointName() == null) {
                    suggestion += "<span class='success'>Unassigned</span>";
                    customerSuggestion.setUnassigned(true);
                } else {
                    suggestion += ("<span class='error'>" + customer.getUsagePointName() + "</span>");
                    customerSuggestion.setUnassigned(false);
                }
                customerSuggestion.setSuggestion(suggestion.trim());
                customerSuggestion.setCustomerId(customer.getId().toString());
                suggestions.add(customerSuggestion);
            }
        } else {
            List<Customer> list;
            if (isIdNumberSearch) {
                list = customerService.getCustomerByIdNumberSearchSuggestions(thequery, getUser().getCurrentGroupId(), limit);
            } else {
                list = customerService.getCustomerSearchSuggestions(thequery, getUser().getCurrentGroupId(), limit);
            }
            Iterator<Customer> listIt = list.iterator();
            String suggestion;
            CustomerSuggestion customerSuggestion;
            Customer customer;
            while (listIt.hasNext()) {
                customer = listIt.next();
                suggestion = "";
                customerSuggestion = new CustomerSuggestion();
                customerSuggestion.setOriginalQuery(req.getQuery());
                if (isIdNumberSearch) {
                    suggestion += (customer.getIdNumber() + ", ");
                }
                suggestion += (customer.getSurname() + ", ");
                if (customer.getTitle() == null)
                    customer.setTitle("");
                if (customer.getFirstnames() == null)
                    customer.setFirstnames(" - ");
                if (customer.getInitials() == null)
                    customer.setInitials("");
                suggestion += (customer.getTitle() + " " + customer.getFirstnames() + " " + customer.getInitials());
                customerSuggestion.setSuggestion(suggestion.trim());
                customerSuggestion.setCustomerId(customer.getId().toString());
                suggestions.add(customerSuggestion);
            }
        }
        response.setSuggestions(suggestions);
        return response;
    }

    @Override
    public Response getCustomerAgreementSuggestions(Request req) throws ServiceException {
        Response response = new Response();
        ArrayList<CustomerAgreementSuggestion> suggestions = new ArrayList<>();
        String thequery = (req.getQuery() + "%").toLowerCase();
        List<CustomerAgreement> list = customerAgreementService.getCustomerAgreementSearchSuggestions(thequery, getUser().getCurrentGroupId());
        Iterator<CustomerAgreement> listIt = list.iterator();
        String suggestion;
        CustomerAgreementSuggestion customerAgreementSuggestion;
        CustomerAgreement customerAgreement;
        while (listIt.hasNext()) {
            customerAgreement = listIt.next();
            suggestion = "";
            customerAgreementSuggestion = new CustomerAgreementSuggestion();
            customerAgreementSuggestion.setOriginalQuery(req.getQuery());
            suggestion += (customerAgreement.getAgreementRef());
            customerAgreementSuggestion.setSuggestion(suggestion.trim());
            customerAgreementSuggestion.setCustAgrRef(customerAgreement.getAgreementRef());
            suggestions.add(customerAgreementSuggestion);
        }
        response.setSuggestions(suggestions);
        return response;
    }

    @Override
    public Response getCustomerAccountSuggestions(Request req) throws ServiceException {
        Response response = new Response();
        ArrayList<CustomerAccountSuggestion> suggestions = new ArrayList<>();
        String thequery = (req.getQuery() + "%").toLowerCase();
        List<CustomerAccount> list = customerAccountService.getCustomerAccountSearchSuggestions(thequery, getUser().getCurrentGroupId());
        Iterator<CustomerAccount> listIt = list.iterator();
        String suggestion;
        CustomerAccountSuggestion customerAccountSuggestion;
        CustomerAccount customerAccount;
        while (listIt.hasNext()) {
            customerAccount = listIt.next();
            suggestion = "";
            customerAccountSuggestion = new CustomerAccountSuggestion();
            customerAccountSuggestion.setOriginalQuery(req.getQuery());
            suggestion += (customerAccount.getAccountName());
            customerAccountSuggestion.setSuggestion(suggestion.trim());
            customerAccountSuggestion.setAccountName(customerAccount.getAccountName());
            suggestions.add(customerAccountSuggestion);
        }
        response.setSuggestions(suggestions);
        return response;
    }

    @Override
    public Response getUsagePointSuggestions(Request req, boolean withPricingStructureInfo, int limit) throws ServiceException {
        MeterMngUser user = getUser();
        Response response = new Response();
        ArrayList<UsagePointSuggestion> suggestions = new ArrayList<>();
        String thequery = (req.getQuery() + "%").toLowerCase();

        List<UsagePoint> list = usagePointService.getUsagePointSearchSuggestions(thequery, user.getCurrentGroupId(), limit);
        Iterator<UsagePoint> listIt = list.iterator();
        String suggestion;
        UsagePointSuggestion usagePointSuggestion;
        UsagePoint usagePoint;
        while (listIt.hasNext()) {
            usagePoint = listIt.next();
            usagePointSuggestion = new UsagePointSuggestion(usagePoint.getId(), usagePoint.getName(), usagePoint.getLastCyclicChargeDate());
            usagePointSuggestion.setMeterId(usagePoint.getMeterId());
            usagePointSuggestion.setCustomerAgreementId(usagePoint.getCustomerAgreementId());
            suggestion = usagePoint.getName();
            if (withPricingStructureInfo) {
                usagePointSuggestion.setUpPricingStructureData(usagePointService.getCurrentAndFutureUPPricingStructures(usagePoint.getId()));
                if (usagePoint.getMeterId() != null || usagePoint.getCustomerAgreementId() != null) {
                    String custName = "";
                    String meterNum = "";
                    if (usagePoint.getMeterId() != null) {
                        Meter meter = meterService.getMeterById(usagePoint.getMeterId());
                        meterNum = meter.getMeterNum();
                    }
                    if (usagePoint.getCustomerAgreementId() != null) {
                        Customer customer = customerService.getCustomerByAgreementId(usagePoint.getCustomerAgreementId());
                        custName = (customer.getSurname() + ", ");
                        if (customer.getTitle() == null)
                            customer.setTitle("");
                        if (customer.getFirstnames() == null)
                            customer.setFirstnames(" - ");
                        if (customer.getInitials() == null)
                            customer.setInitials("");
                        custName += (customer.getTitle() + " " + customer.getFirstnames() + " " + customer.getInitials());
                    }

                    if (usagePoint.getMeterId() != null && usagePoint.getCustomerAgreementId() != null) {
                        suggestion += ("<span class='error'>" + "Meter: " + meterNum + " ; Cust= " + custName + "</span>");
                    } else if (usagePoint.getMeterId() != null) {
                        suggestion += ("<span class='error'>" + "Meter: " + meterNum + "</span>");
                    } else if (usagePoint.getCustomerAgreementId() != null) {
                        suggestion += ("<span class='error'>" + "Cust= " + custName + "</span>");
                    }
                } else {
                    suggestion += "<span class='success'>Unassigned</span>";
                }
            }
            usagePointSuggestion.setSuggestion(suggestion);
            suggestions.add(usagePointSuggestion);
        }
        response.setSuggestions(suggestions);
        return response;
    }


    public CustomerData fetchCustomerDataById(Long customerId) {
        CustomerData customerData = new CustomerData(customerService.getCustomerById(customerId));
        // get locations
        if (customerData.getPhysicalLocationId() != null) {
            customerData.setPhysicalLocation(locationService.getLocationDataById(customerData.getPhysicalLocationId()));
        }
        return customerData;
    }

    public CustomerAccount fetchCustomerAccountById(Long customerAccountId) {
        return customerService.getCustomerAccountById(customerAccountId);
    }

    @Override
    public UsagePoint fetchUsagePointByMeterDto(MeterDto meterDto) {
        Long meterId = meterDto.getId();
        if (meterId == null) {
            Meter meter = meterService.getMeterByNumber(meterDto.getNumber());
            if (meter == null) {
                return null;
            }
            meterId = meter.getId();
        }
        return usagePointService.getUsagePointByMeterId(meterId);
    }

    public UsagePointData getUsagePointByCustomerAgreement(CustomerAgreementData customerAgreementData, CustomerAccount customerAccount) throws ServiceException {
        UsagePointData usagePointData = new UsagePointData();

        if (customerAgreementData != null) {
            // get customerData
            customerAgreementData.setCustomerData(fetchCustomerDataById(customerAgreementData.getCustomerId()));
            if (customerAccount == null) {
                customerAgreementData.setCustomerAccount(fetchCustomerAccountById(customerAgreementData.getCustomerAccountId()));
            } else {
                customerAgreementData.setCustomerAccount(customerAccount);
            }
            usagePointData.setCustomerAgreementData(customerAgreementData);
            // Get Usage Point
            UsagePoint theUsagePoint = null;
            if (customerAgreementData.getId() != null) {
                theUsagePoint = usagePointService.getUsagePointByCustomerAgreementId(customerAgreementData.getId());
            }
            if (theUsagePoint != null) {
                usagePointData.setUsagePoint(theUsagePoint);
            }
            Long usagePointId = usagePointData.getId();
            if (usagePointId != null) {
                // Get MeterData
                if (usagePointData.getMeterId() != null) {
                    Meter meter = meterService.getMeterById(usagePointData.getMeterId());
                    MeterData meterData = new MeterData(meter);
                    meterData.setMeterType(meterTypeService.getMeterType(meterData.getMeterTypeId()));
                    meterData.setStsMeter(stsMeterService.getMeterById(usagePointData.getMeterId()));
                    meterData.setMeterModelData(meterModelService.getMeterModelById(meterData.getMeterModelId()));
                    usagePointData.setMeterData(meterData);
                }

                setCommonUsagePointFields(usagePointData, theUsagePoint);
            } else {
                setHistoryIds(usagePointData);
            }
        }
        return usagePointData;
    }

    @Override
	public SearchData doAdvancedSearchTotalCount(SearchData data) throws ValidationException, ServiceException {
		if (data != null) {
			HashMap<String, Object> criteria = data.getCriteria();
			if (!criteria.isEmpty()) {
				MeterMngUser user = getUser();
                preprocessLocationGroupSearch(criteria);
				// AND search for relevant meters, customers, usage points combination
				// Using either meter, customer or usagePoint as main table to select from
				if (MeterMngUtil.isMeterSearchCriteria(data)) {
					searchService.doMeterAdvancedSearchCount(data, user.getCurrentGroupId());
				} else if (MeterMngUtil.isCustomerSearchCriteria(data)) {
					searchService.doCustomerAdvancedSearchCount(data, user.getCurrentGroupId());
				} else if (MeterMngUtil.isUsagePointSearchCriteria(data)) {
					searchService.doUsagePointAdvancedSearchCount(data, user.getCurrentGroupId());
				} else if (MeterMngUtil.isLocationSearchCriteria(data)) {
					searchService.doLocationAdvancedSearchCount(data, user.getCurrentGroupId());
				} else {
					logger.error("Unrecognised criteria: " + data);
				}
				logger.info("Got searchResults: " + data.getResults().size());
				return data;
			}
		}
		throw new ValidationException(new ValidationMessage("search.error.no.criteria", true));
	}
    
    @Override
	public SearchData doAdvancedSearch(SearchData data) throws ValidationException, ServiceException {
		if (data != null) {
			HashMap<String, Object> criteria = data.getCriteria();
			if (!criteria.isEmpty()) {
				MeterMngUser user = getUser();
                preprocessLocationGroupSearch(criteria);
				// AND search for relevant meters, customers, usage points combination
				// Using either meter, customer or usagePoint as main table to select from
				if (MeterMngUtil.isMeterSearchCriteria(data)) {
					searchService.doMeterAdvancedSearch(data, user.getCurrentGroupId());
				} else if (MeterMngUtil.isCustomerSearchCriteria(data)) {
					searchService.doCustomerAdvancedSearch(data, user.getCurrentGroupId());
				} else if (MeterMngUtil.isUsagePointSearchCriteria(data)) {
					searchService.doUsagePointAdvancedSearch(data, user.getCurrentGroupId());
				} else if (MeterMngUtil.isLocationSearchCriteria(data)) {
					searchService.doLocationAdvancedSearch(data, user.getCurrentGroupId());
				} else {
					logger.error("Unrecognised criteria: " + data);
				}
				logger.info("Got searchResults: " + data.getResults().size());
				return data;
			}
		}
		throw new ValidationException(new ValidationMessage("search.error.no.criteria", true));
	}

    private void preprocessLocationGroupSearch(HashMap<String, Object> criteria) {
        Object locationGroupId = criteria.get(MeterMngStatics.LOCATION_GROUP_SEARCH);
        if (locationGroupId != null) {
            List<GenGroup> allGenGroups = groupService
                    .getAllGenGroups(groupService.getLocationGroupGroupType().getId());
            allGenGroups.removeIf(genGroup -> genGroup.getParentId() == null);
            List<String> genGroupIds = new ArrayList<>();
            genGroupIds.addAll(Arrays.asList(locationGroupId.toString()));
            boolean keepGoing = true;
            while (keepGoing) {
                keepGoing = false;
                for (GenGroup genGroup : allGenGroups) {
                    if (genGroupIds.contains(genGroup.getParentId().toString())) {
                        genGroupIds.add(String.valueOf(genGroup.getId()));
                        allGenGroups.remove(genGroup);
                        keepGoing = true;
                        break;
                    }
                }
            }
            criteria.put(MeterMngStatics.LOCATION_GROUP_SEARCH,
                    genGroupIds.toString().replace('[', '(').replace(']', ')'));
        }
    }

    @Override
    public List<MeterDto> getMeterSuggestions(MeterSearchDto searchDto, int limit) throws ServiceException {
        MeterMngUser user = getUser();
        return meterService.getMeterSuggestions(user.getCurrentGroupId(), searchDto, limit);
    }

    @Override
    public List<MeterDto> getMetersInDeviceStoresSuggestions(MeterSearchDto searchDto, int limit) throws ServiceException {
        return meterService.getMetersInDeviceStoresSuggestions(searchDto, getUser().getCurrentGroupId(), limit);
    }

    @Override
    public List<MeterDto> getMetersAssignedToUpSuggestions(MeterSearchDto meterSearchDto, int limit) throws ServiceException {
        return meterService.getMetersAssignedToUpSuggestions(meterSearchDto, getUser().getCurrentGroupId(), limit);
    }

    //************************************************************************************************************************
    public void setStsMeterService(STSMeterService stsMeterServe) {
        this.stsMeterService = stsMeterServe;
    }

    public void setCustomerService(CustomerService customerServe) {
        this.customerService = customerServe;
    }

    public void setCustomerAgreementService(CustomerAgreementService customerAgreementServe) {
        this.customerAgreementService = customerAgreementServe;
    }

    public void setCustomerAccountService(CustomerAccountService customerAccountService) {
        this.customerAccountService = customerAccountService;
    }

    public void setUsagePointService(UsagePointService usagePointService) {
        this.usagePointService = usagePointService;
    }

    public void setLocationService(LocationService locationService) {
        this.locationService = locationService;
    }

    public void setUpGenGroupLnkService(UpGenGroupLnkService usagePointGroupLnkService) {
        this.upGenGroupLnkService = usagePointGroupLnkService;
    }

    public void setDeviceStoreService(DeviceStoreService deviceStoreService) {
        this.deviceStoreService = deviceStoreService;
    }

    public void setSearchService(SearchService searchService) {
        this.searchService = searchService;
    }

    public void setMeterTypeService(MeterTypeService meterTypeService) {
        this.meterTypeService = meterTypeService;
    }

    public void setMeterService(MeterService meterService) {
        this.meterService = meterService;
    }

    public void setMeterModelService(MeterModelService meterModelService) {
        this.meterModelService = meterModelService;
    }

    public void setSpecialActionsService(SpecialActionsService specialActionsService) {
        this.specialActionsService = specialActionsService;
    }

    public void setOnlineBulkMeterService(OnlineBulkMeterService onlineBulkMeterService) {
        this.onlineBulkMeterService = onlineBulkMeterService;
    }

    public void setBlockingTypeService(BlockingTypeService blockingTypeService) {
        this.blockingTypeService = blockingTypeService;
    }

    public void setMeterHistService(MeterHistService meterHistService) {
        this.meterHistService = meterHistService;
    }

    public void setStsMeterHistService(STSMeterHistService stsMeterHistService) {
        this.stsMeterHistService = stsMeterHistService;
    }

    public void setCustomerHistService(CustomerHistService customerHistService) {
        this.customerHistService = customerHistService;
    }

    public void setUsagePointHistService(UsagePointHistService usagePointHistService) {
        this.usagePointHistService = usagePointHistService;
    }

	public void setGroupService(GroupService groupService) {
		this.groupService = groupService;
	}

    public void setAuxAccountHistService(AuxAccountHistService auxAccountHistService) {
        this.auxAccountHistService = auxAccountHistService;
    }

    public AuxAccountHistService getAuxAccountHistService() {
        return auxAccountHistService;
    }

    public void setUnitTransService(UnitTransService unitTransService) {
        this.unitTransService =unitTransService;
    }
}
