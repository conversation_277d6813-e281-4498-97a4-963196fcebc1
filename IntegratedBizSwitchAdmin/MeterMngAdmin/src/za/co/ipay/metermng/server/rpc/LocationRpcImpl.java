package za.co.ipay.metermng.server.rpc;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

import org.apache.log4j.Logger;

import za.co.ipay.metermng.client.rpc.LocationRpc;
import za.co.ipay.metermng.mybatis.generated.model.GenGroup;
import za.co.ipay.metermng.mybatis.generated.model.LocationHist;
import za.co.ipay.metermng.server.mybatis.service.GroupService;
import za.co.ipay.metermng.server.mybatis.service.LocationService;
import za.co.ipay.metermng.server.util.MeterMngUtil;
import za.co.ipay.metermng.shared.LocationHistData;

public class LocationRpcImpl extends BaseMeterMngRpc implements LocationRpc {

    private static final long serialVersionUID = -6640500745683694497L;

    private LocationService locationService;
    private GroupService groupService;
	
	public LocationRpcImpl() {
	    logger = Logger.getLogger(LocationRpcImpl.class);
	}

	public void setLocationService(LocationService locationService) {
        this.locationService = locationService;
    }

    public void setGroupService(GroupService groupService) {
        this.groupService = groupService;
    }

    @Override
    public ArrayList<LocationHistData> getLocationHistory(Long locationId) {
        ArrayList<LocationHistData> thelist = new ArrayList<LocationHistData>();
        List<LocationHist> listfromdb = locationService.getLocationHistoryByLocationId(locationId);
        if (listfromdb != null) {
            LocationHistData locationHistData;
            LocationHistData prev_record = null;
            Iterator<LocationHist> listIt = listfromdb.iterator();
            GenGroup gengroup;
            while (listIt.hasNext()) {
                locationHistData = new LocationHistData(listIt.next());
                if (locationHistData.getLocGroupId() != null) {
                    gengroup = groupService.getGenGroup(locationHistData.getLocGroupId());
                    if (gengroup != null) {
                        locationHistData.setLocationGroup(gengroup.getName());
                    }
                }
                if (prev_record != null) {
                    findChanges(prev_record, locationHistData);
                }
                prev_record = locationHistData;
                thelist.add(locationHistData);
            }
        }
        return thelist;
    }
    
    private void findChanges(LocationHistData obj1, LocationHistData obj2) {

        if (obj1.getId().equals(obj2.getId())) {
            obj1.setRecordStatusChanged(
                    MeterMngUtil.areValuesDifferent(obj1.getRecordStatus(), obj2.getRecordStatus()));
            obj1.setAddressLine1Changed(
                    MeterMngUtil.areValuesDifferent(obj1.getAddressLine1(), obj2.getAddressLine1()));
            obj1.setAddressLine2Changed(
                    MeterMngUtil.areValuesDifferent(obj1.getAddressLine2(), obj2.getAddressLine2()));
            obj1.setAddressLine3Changed(
                    MeterMngUtil.areValuesDifferent(obj1.getAddressLine3(), obj2.getAddressLine3()));
            obj1.setErfNumberChanged(MeterMngUtil.areValuesDifferent(obj1.getErfNumber(), obj2.getErfNumber()));
            obj1.setLatitudeChanged(MeterMngUtil.areValuesDifferent(obj1.getLatitude(), obj2.getLatitude()));
            obj1.setLongitudeChanged(MeterMngUtil.areValuesDifferent(obj1.getLongitude(), obj2.getLongitude()));
            obj1.setLocGroupIdChanged(MeterMngUtil.areValuesDifferent(obj1.getLocGroupId(), obj2.getLocGroupId()));
            obj1.setStreetNumChanged(MeterMngUtil.areValuesDifferent(obj1.getStreetNum(), obj2.getStreetNum()));
            obj1.setBuildingNameChanged(
                    MeterMngUtil.areValuesDifferent(obj1.getBuildingName(), obj2.getBuildingName()));
            obj1.setSuiteNumChanged(MeterMngUtil.areValuesDifferent(obj1.getSuiteNum(), obj2.getSuiteNum()));
        }
    }
}
