package za.co.ipay.metermng.server.mybatis.service;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

import org.springframework.transaction.annotation.Transactional;

import za.co.ipay.metermng.datatypes.StsEngineeringTokenTypeE;
import za.co.ipay.metermng.mybatis.generated.mapper.StsEngTokenTypeMapper;
import za.co.ipay.metermng.mybatis.generated.mapper.StsEngineeringTokenMapper;
import za.co.ipay.metermng.mybatis.generated.mapper.StsMeterMapper;
import za.co.ipay.metermng.mybatis.generated.mapper.StsSupplyGroupMapper;
import za.co.ipay.metermng.mybatis.generated.mapper.UsagePointMapper;
import za.co.ipay.metermng.mybatis.generated.model.StsEngTokenType;
import za.co.ipay.metermng.mybatis.generated.model.StsEngineeringToken;
import za.co.ipay.metermng.mybatis.generated.model.StsEngineeringTokenExample;
import za.co.ipay.metermng.mybatis.generated.model.StsMeter;
import za.co.ipay.metermng.mybatis.generated.model.StsSupplyGroup;
import za.co.ipay.metermng.mybatis.generated.model.StsSupplyGroupExample;
import za.co.ipay.metermng.mybatis.generated.model.UsagePoint;
import za.co.ipay.metermng.shared.MeterMngStatics;
import za.co.ipay.metermng.shared.StsEngineeringTokenData;
import za.co.ipay.metermng.shared.dto.importfile.KeyChangeDto;


public class EngineeringTokensService {
	
	private UsagePointMapper usagePointMapper;
	private StsEngTokenTypeMapper stsEngTokenTypeMapper;
	private StsEngineeringTokenMapper stsEngineeringTokenMapper;
	private StsMeterMapper stsMeterMapper;
	private StsSupplyGroupMapper stsSupplyGroupMapper;
	private ImportFileDataService importFileDataService;
	
	public void setUsagePointMapper(UsagePointMapper usagePointMapper) {
	    this.usagePointMapper = usagePointMapper;
	}
	public void setStsEngTokenTypeMapper(StsEngTokenTypeMapper stsEngTokenTypeMapper) {
	    this.stsEngTokenTypeMapper = stsEngTokenTypeMapper;
	}
	public void setStsEngineeringTokenMapper(
	        StsEngineeringTokenMapper stsEngineeringTokenMapper) {
	    this.stsEngineeringTokenMapper = stsEngineeringTokenMapper;
	}
	public void setStsMeterMapper(StsMeterMapper stsMeterMapper) {
	    this.stsMeterMapper = stsMeterMapper;
	}
	
	public void setStsSupplyGroupMapper(StsSupplyGroupMapper stsSupplyGroupMapper) {
	    this.stsSupplyGroupMapper = stsSupplyGroupMapper;
	}
	
	public void setImportFileDataService(ImportFileDataService importFileDataService) {
	    this.importFileDataService = importFileDataService;
	}
	
	@Transactional(readOnly=true)
	public ArrayList<StsEngineeringTokenData> getEngineeringTokensByMeterId(Long meterId, boolean usingAccessGroup) {
		StsEngineeringTokenExample engTransExample = new StsEngineeringTokenExample();
    	engTransExample.createCriteria().andMeterIdEqualTo(meterId).andStsEngTokenTypeIdNotEqualTo(9L)
                .andStsEngTokenTypeIdNotEqualTo(10L); //exclude std and bsst tokens
    	engTransExample.setOrderByClause("trans_date desc");
        List<StsEngineeringToken> list = stsEngineeringTokenMapper.selectByExample(engTransExample);
        ArrayList<StsEngineeringTokenData> returnList = new ArrayList<StsEngineeringTokenData>(list.size());
        if (list != null && !list.isEmpty()) {
            Iterator<StsEngineeringToken> listit = list.iterator();
            StsMeter meter;
            UsagePoint upData;
            StsEngTokenType stsEngTokenType;
            StsEngineeringTokenData stsEngineeringTokenData;
            
            while (listit.hasNext()) {
            	stsEngineeringTokenData = new StsEngineeringTokenData(listit.next());
            	if (stsEngineeringTokenData.getMeterId() != null) {
                    meter = stsMeterMapper.selectByPrimaryKey(stsEngineeringTokenData.getMeterId());
                    stsEngineeringTokenData.setMeterNumber(meter.getMeterNum());
                    stsEngineeringTokenData.setStsAlgorithmCode(meter.getStsAlgorithmCode());
                }
            	if (stsEngineeringTokenData.getUsagePointId() != null) {
            		upData = usagePointMapper.selectByPrimaryKey(stsEngineeringTokenData.getUsagePointId());
            		if (upData == null && usingAccessGroup) {
            		    stsEngineeringTokenData.setUsagePointName(MeterMngStatics.ACCESSGROUP_RLS_HIDDEN_DATA);
            		} else {
            		    stsEngineeringTokenData.setUsagePointName(upData.getName());
            		}
            	}
            	if (stsEngineeringTokenData.getStsEngTokenTypeId() != null) {
            		stsEngTokenType = stsEngTokenTypeMapper.selectByPrimaryKey(stsEngineeringTokenData.getStsEngTokenTypeId());
            		stsEngineeringTokenData.setTokenTypeName(stsEngTokenType.getName());
            	}
            	if (stsEngineeringTokenData.getBulkRef() != null) {
            	    stsEngineeringTokenData.setImportFileName(importFileDataService.getImportFileNameFromBulkRef(stsEngineeringTokenData.getBulkRef()));
            	}
        		returnList.add(stsEngineeringTokenData);
            }
        }
        return returnList;
	}

    @Transactional(readOnly = true)
    public StsEngineeringToken getFirstEngineeringTokenByCustomerTrans(long customerTransId) {
        StsEngineeringTokenExample stsEngineeringTokenExample = new StsEngineeringTokenExample();
        stsEngineeringTokenExample.createCriteria().andCustomerTransIdEqualTo(customerTransId);
        List<StsEngineeringToken> list = stsEngineeringTokenMapper.selectByExample(stsEngineeringTokenExample);
        if (list.isEmpty()) {
            return null;
        }
        return list.stream()
                .filter(item -> item.getStsEngTokenTypeId().equals(StsEngineeringTokenTypeE.KEY_CHANGE.getId()))
                .findAny().orElse(list.get(0));
    }

    @Transactional(readOnly=true)
    public ArrayList<StsEngineeringTokenData> getStdBsstEngineeringTokensByCustomerTrans(Long customerTransId, String meterNumber) {
        StsEngineeringTokenExample engTransExample = new StsEngineeringTokenExample();
        engTransExample.createCriteria().andCustomerTransIdEqualTo(customerTransId).andStsEngTokenTypeIdBetween(9L,10L); //only std and bsst tokens
        List<StsEngineeringToken> list = stsEngineeringTokenMapper.selectByExample(engTransExample);
        ArrayList<StsEngineeringTokenData> returnList = new ArrayList<StsEngineeringTokenData>(list.size());
        if (list != null && !list.isEmpty()) {
            Iterator<StsEngineeringToken> listit = list.iterator();
            StsEngTokenType stsEngTokenType;
            StsEngineeringTokenData stsEngineeringTokenData;
            while (listit.hasNext()) {
                stsEngineeringTokenData = new StsEngineeringTokenData(listit.next());
                if (stsEngineeringTokenData.getStsEngTokenTypeId() != null) {
                    stsEngTokenType = stsEngTokenTypeMapper.selectByPrimaryKey(stsEngineeringTokenData.getStsEngTokenTypeId());
                    stsEngineeringTokenData.setTokenTypeName(stsEngTokenType.getName());
                }
                stsEngineeringTokenData.setMeterNumber(meterNumber);
                returnList.add(stsEngineeringTokenData);
            }
        }
        return returnList;
    }
    
    @Transactional(readOnly=true)
    public List<KeyChangeDto> getKeychangeTokensForBulkRef(String bulkRef) {
        StsEngineeringTokenExample ex = new StsEngineeringTokenExample();
        ex.createCriteria().andBulkRefEqualTo(bulkRef)
                           .andStsEngTokenTypeIdEqualTo(StsEngineeringTokenTypeE.KEY_CHANGE.getId());
        List<StsEngineeringToken> stsEngTokens = stsEngineeringTokenMapper.selectByExample(ex);
        if (stsEngTokens == null || stsEngTokens.isEmpty()) {
            return null;
        } 
        
        List<KeyChangeDto> kctList = new ArrayList<>();
        for (StsEngineeringToken t : stsEngTokens) {
            KeyChangeDto kct = new KeyChangeDto();
            kct.setMeterNum(stsMeterMapper.selectByPrimaryKey(t.getMeterId()).getMeterNum());
            kct.setUserRef(t.getUserRef());
            
            kct.setToken1(t.getToken1());
            kct.setToken2(t.getToken2());
            kct.setToken3(t.getToken3());
            kct.setToken4(t.getToken4());
            
            kct.setFromSupGroup(t.getOldSupGroup());
            kct.setFromKeyRev(t.getOldKeyRev());
            kct.setFromTariffIdx(t.getOldTariffIdx());
            kct.setFromBaseDate(getSupplyGroupBaseDate(t.getOldSupGroup(), t.getOldKeyRev()));
            
            kct.setToSupGroup(t.getNewSupGroup());
            kct.setToKeyRev(t.getNewKeyRev());
            kct.setToTariffIdx(t.getNewTariffIdx());
            kct.setToBaseDate(getSupplyGroupBaseDate(t.getNewSupGroup(), t.getNewKeyRev()));
            
            kct.setTransDate(t.getTransDate());
            kct.setUserRecEntered(t.getUserRecEntered());
            kct.setBulkRef(t.getBulkRef());
            
            kctList.add(kct);
        }
        return kctList;
    }
    
    @Transactional(readOnly=true)
    private Short getSupplyGroupBaseDate(String supplyGroupCode, Integer keyRevNum) {
        StsSupplyGroupExample ex = new StsSupplyGroupExample();
        ex.createCriteria().andSupplyGroupCodeEqualTo(supplyGroupCode)
                           .andKeyRevisionNumEqualTo(keyRevNum);
        List<StsSupplyGroup> ssgList = stsSupplyGroupMapper.selectByExample(ex);
        if (ssgList == null || ssgList.isEmpty()) {
            return null;
        }
        return ssgList.get(0).getBaseDate();
    }

    @Transactional(readOnly = true)
    public StsEngineeringToken getKeyChangeTokensForCustomerTrans(long customerTransId) {
        StsEngineeringTokenExample stsEngineeringTokenExample = new StsEngineeringTokenExample();
        stsEngineeringTokenExample.createCriteria().andCustomerTransIdEqualTo(customerTransId)
                .andStsEngTokenTypeIdEqualTo(StsEngineeringTokenTypeE.KEY_CHANGE.getId());
        List<StsEngineeringToken> stsEngineeringTokens = stsEngineeringTokenMapper
                .selectByExample(stsEngineeringTokenExample);
        if (stsEngineeringTokens.isEmpty()) {
            return null;
        }
        return stsEngineeringTokens.get(0);
    }
}
