package za.co.ipay.metermng.server.rpc;

import java.util.List;

import org.apache.log4j.Logger;

import za.co.ipay.gwt.common.shared.exception.AccessControlException;
import za.co.ipay.gwt.common.shared.exception.ServiceException;
import za.co.ipay.gwt.common.shared.exception.ValidationException;
import za.co.ipay.gwt.common.shared.exception.ValidationMessage;
import za.co.ipay.metermng.client.rpc.AuxTypeRpc;
import za.co.ipay.metermng.datatypes.RecordStatus;
import za.co.ipay.metermng.mybatis.generated.model.AuxType;
import za.co.ipay.metermng.server.mybatis.service.AuxAccountService;
import za.co.ipay.metermng.server.mybatis.service.AuxTypeService;
import za.co.ipay.metermng.server.validation.ServerValidatorUtil;

public class AuxTypeRpcImpl extends BaseMeterMngRpc implements AuxTypeRpc {

    private static final long serialVersionUID = 2399014789877255533L;
    
    private AuxTypeService auxTypeService;
    private AuxAccountService auxAccountService;
    
    public AuxTypeRpcImpl() {
        this.logger = Logger.getLogger(AuxTypeRpcImpl.class);
    }
    
    public void setAuxTypeService(AuxTypeService auxTypeService) {
        this.auxTypeService = auxTypeService;
    }

    public void setAuxAccountService(AuxAccountService auxAccountService) {
        this.auxAccountService = auxAccountService;
    }

    @Override
    public List<AuxType> getAuxTypes() throws ValidationException, ServiceException, AccessControlException {
        return auxTypeService.getAuxTypes();
    }
    
    @Override
    public AuxType addAuxType(AuxType auxType) throws ValidationException, ServiceException, AccessControlException {
        ServerValidatorUtil.getInstance().validateDataForValidationMessages(auxType);
        if (auxTypeService.getAuxTypeByName(auxType.getName()) != null) {
            throw new ValidationException(new ValidationMessage("auxtype.error.save.duplicate", true));
        }
        
        if (auxTypeService.getMridExistence(auxType.getMrid(), auxType.getId())) {
            throw new ValidationException(new ValidationMessage("aux.type.mrid.external.unique.validation", true));
        }
        
        return auxTypeService.insert(auxType);
    }
    
    @Override
    public AuxType updateAuxType(AuxType auxType) throws ValidationException, ServiceException, AccessControlException {
        ServerValidatorUtil.getInstance().validateDataForValidationMessages(auxType);
        AuxType tempAuxType = auxTypeService.getAuxTypeByName(auxType.getName());
        if ((tempAuxType != null) && (!tempAuxType.getId().equals(auxType.getId()))) {
            throw new ValidationException(new ValidationMessage("auxtype.error.update.duplicate", true));
        } else if (auxType.getRecordStatus() != RecordStatus.ACT && auxAccountService.isAuxTypeInUse(auxType.getId())) {
            throw new ValidationException(new ValidationMessage("auxtype.error.update.in.use", true));
        }
        
        if (auxTypeService.getMridExistence(auxType.getMrid(), auxType.getId())) {
            throw new ValidationException(new ValidationMessage("aux.type.mrid.external.unique.validation", true));
        }
        
        return auxTypeService.update(auxType);
    }
}
