package za.co.ipay.metermng.server.mybatis.mapper;

import org.apache.ibatis.annotations.Select;

public interface ReadingsCustomMapper {
    
    @Select("select count(*) from meter_reading where meter_id = #{0}")
    public Long getMeterReadingsCountForMeter(Long meterId);
            
    @Select("select count(*) from meter_reading where usage_point_id =  #{0}")
    public Long getMeterReadingsCountForUP(Long usagePointId);
    
    @Select("select count(*) from register_reading where meter_id = #{0}")
    public Long getRegReadCountForMeter(Long meterId);
        
    @Select("select count(*) from register_reading where usage_point_id = #{0}")
    public Long getRegReadCountForUP(Long usagePointId);

}