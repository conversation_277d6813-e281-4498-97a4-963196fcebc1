package za.co.ipay.metermng.server.mybatis.service;

import org.apache.ibatis.session.RowBounds;
import org.springframework.transaction.annotation.Transactional;
import za.co.ipay.metermng.mybatis.generated.mapper.UnitsTransMapper;
import za.co.ipay.metermng.mybatis.generated.model.UnitsTrans;
import za.co.ipay.metermng.mybatis.generated.model.UnitsTransExample;

import java.util.List;

public class UnitTransService {
    private UnitsTransMapper unitsTransMapper;

    @Transactional(readOnly = true)
    public UnitsTrans getUnitsTransById(Long unitsTransId) {
        return unitsTransMapper.selectByPrimaryKey(unitsTransId);
    }

    @Transactional(readOnly = true)
    public List<UnitsTrans> getUnitsTransByAccountId(Long unitsAccountId) {
        UnitsTransExample example = new UnitsTransExample();
        example.createCriteria().andUnitsAccountIdEqualTo(unitsAccountId);
        example.setOrderByClause("trans_date desc");
        return unitsTransMapper.selectByExample(example);
    }

    @Transactional(readOnly = true)
    public UnitsTrans getLatestUnitsTransByAccountId(Long unitsAccountId) {
        UnitsTransExample example = new UnitsTransExample();
        example.createCriteria().andUnitsAccountIdEqualTo(unitsAccountId);
        example.setOrderByClause("trans_date desc");
        List<UnitsTrans> list = unitsTransMapper.selectByExampleWithRowbounds(example, new RowBounds(0, 1));

        return list != null && !list.isEmpty() ? list.get(0) : null;
    }

    @Transactional
    public int insertUnitsTrans(UnitsTrans unitsTrans) {
        return unitsTransMapper.insertSelective(unitsTrans);
    }

    @Transactional
    public int updateUnitsTrans(UnitsTrans unitsTrans) {
        return unitsTransMapper.updateByPrimaryKeySelective(unitsTrans);
    }

    @Transactional(readOnly = true)
    public Long getLatestUnitsTransIdByUsagePoint(Long usagePointId) {
        UnitsTransExample example = new UnitsTransExample();
        example.createCriteria().andUsagePointIdEqualTo(usagePointId);
        example.setOrderByClause("trans_date desc");
        List<UnitsTrans> unitsList = unitsTransMapper.selectByExampleWithRowbounds(example, new RowBounds(0, 1));

        return unitsList != null && !unitsList.isEmpty() ? unitsList.get(0).getId() : 0L;
    }


    // Setter for unitsTransMapper
    public void setUnitsTransMapper(UnitsTransMapper unitsTransMapper) {
        this.unitsTransMapper = unitsTransMapper;
    }
}