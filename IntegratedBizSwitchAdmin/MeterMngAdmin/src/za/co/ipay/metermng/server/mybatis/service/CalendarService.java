package za.co.ipay.metermng.server.mybatis.service;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.GregorianCalendar;
import java.util.HashSet;
import java.util.List;

import org.apache.ibatis.session.RowBounds;
import org.springframework.transaction.annotation.Transactional;

import za.co.ipay.gwt.common.shared.exception.ServiceException;
import za.co.ipay.gwt.common.shared.exception.ValidationException;
import za.co.ipay.gwt.common.shared.exception.ValidationMessage;
import za.co.ipay.metermng.datatypes.RecordStatus;
import za.co.ipay.metermng.mybatis.custom.mapper.CalendarCustomMapper;
import za.co.ipay.metermng.mybatis.generated.mapper.TouCalendarMapper;
import za.co.ipay.metermng.mybatis.generated.mapper.TouCalendarSeasonMapper;
import za.co.ipay.metermng.mybatis.generated.mapper.TouDayProfileMapper;
import za.co.ipay.metermng.mybatis.generated.mapper.TouDayProfileTimeMapper;
import za.co.ipay.metermng.mybatis.generated.mapper.TouPeriodMapper;
import za.co.ipay.metermng.mybatis.generated.mapper.TouSeasonDateMapper;
import za.co.ipay.metermng.mybatis.generated.mapper.TouSeasonMapper;
import za.co.ipay.metermng.mybatis.generated.mapper.TouSpecialDayMapper;
import za.co.ipay.metermng.mybatis.generated.model.PricingStructure;
import za.co.ipay.metermng.mybatis.generated.model.TouCalendar;
import za.co.ipay.metermng.mybatis.generated.model.TouCalendarExample;
import za.co.ipay.metermng.mybatis.generated.model.TouCalendarSeason;
import za.co.ipay.metermng.mybatis.generated.model.TouCalendarSeasonExample;
import za.co.ipay.metermng.mybatis.generated.model.TouDayProfile;
import za.co.ipay.metermng.mybatis.generated.model.TouDayProfileExample;
import za.co.ipay.metermng.mybatis.generated.model.TouDayProfileTime;
import za.co.ipay.metermng.mybatis.generated.model.TouDayProfileTimeExample;
import za.co.ipay.metermng.mybatis.generated.model.TouPeriod;
import za.co.ipay.metermng.mybatis.generated.model.TouPeriodExample;
import za.co.ipay.metermng.mybatis.generated.model.TouSeason;
import za.co.ipay.metermng.mybatis.generated.model.TouSeasonDate;
import za.co.ipay.metermng.mybatis.generated.model.TouSeasonDateExample;
import za.co.ipay.metermng.mybatis.generated.model.TouSeasonExample;
import za.co.ipay.metermng.mybatis.generated.model.TouSpecialDay;
import za.co.ipay.metermng.mybatis.generated.model.TouSpecialDayExample;
import za.co.ipay.metermng.server.mybatis.mapper.ICalendarSuggestionMapper;
import za.co.ipay.metermng.shared.TouCalendarData;
import za.co.ipay.metermng.shared.TouDayProfileData;
import za.co.ipay.metermng.shared.TouDayProfileTimeData;
import za.co.ipay.metermng.shared.TouSeasonDateData;
import za.co.ipay.metermng.shared.TouSpecialDayData;
import za.co.ipay.metermng.shared.dto.pricing.SpecialDayProfile;
import za.co.ipay.metermng.shared.dto.pricing.TouCalendarSeasonsPeriodsSpecialDaysData;

public class CalendarService {

    private TouSeasonMapper touSeasonMapper;
    private TouPeriodMapper touPeriodMapper;
    private TouCalendarMapper touCalendarMapper;
    private TouSpecialDayMapper touSpecialDayMapper;
    private TouDayProfileMapper touDayProfileMapper;
    private TouDayProfileTimeMapper touDayProfileTimeMapper;
    private ICalendarSuggestionMapper calendarSuggestionMapper;
    private TouCalendarSeasonMapper touCalendarSeasonMapper;
    private TouSeasonDateMapper touSeasonDateMapper;
    private CalendarCustomMapper calendarCustomMapper;
    private PricingStructureService pricingStructureService;
    
    private RowBounds rowBounds = new RowBounds(0, 20);
    
    public void setTouSeasonMapper(TouSeasonMapper touSeasonMapper) {
        this.touSeasonMapper = touSeasonMapper;
    }
    
    public void setTouPeriodMapper(TouPeriodMapper touPeriodMapper) {
        this.touPeriodMapper = touPeriodMapper;
    }
    
    public void setTouCalendarMapper(TouCalendarMapper touCalendarMapper) {
        this.touCalendarMapper = touCalendarMapper;
    }
    
    public void setCalendarSuggestionMapper(ICalendarSuggestionMapper calendarSuggestionMapper) {
        this.calendarSuggestionMapper = calendarSuggestionMapper;
    }
    
    public void setTouSpecialDayMapper(TouSpecialDayMapper touSpecialDayMapper) {
        this.touSpecialDayMapper = touSpecialDayMapper;
    }
    
    public void setTouDayProfileMapper(TouDayProfileMapper touDayProfileMapper) {
        this.touDayProfileMapper = touDayProfileMapper;
    }
    
    public void setTouDayProfileTimeMapper(TouDayProfileTimeMapper touDayProfileTimeMapper) {
        this.touDayProfileTimeMapper = touDayProfileTimeMapper;
    }

    public void setTouCalendarSeasonMapper(TouCalendarSeasonMapper touCalendarSeasonMapper) {
        this.touCalendarSeasonMapper = touCalendarSeasonMapper;
    }
    
    public void setTouSeasonDateMapper(TouSeasonDateMapper touSeasonDateMapper) {
        this.touSeasonDateMapper = touSeasonDateMapper;
    }
    
    public void setCalendarCustomMapper(CalendarCustomMapper calendarCustomMapper) {
        this.calendarCustomMapper = calendarCustomMapper;
    }
    
    public void setPricingStructureService(PricingStructureService pricingStructureService) {
        this.pricingStructureService = pricingStructureService;
    }

    
    
    @Transactional
    public TouSeason insertSeason(TouSeason touSeason) throws ServiceException {
        if (touSeasonMapper.insert(touSeason) == 1) {
            return touSeason;
        } else {
            throw new ServiceException("season.error.save");
        }
    }
    
    @Transactional
    public TouSeason updateSeason(TouSeason touSeason) throws ServiceException {
        if (touSeasonMapper.updateByPrimaryKey(touSeason) == 1) {
            return touSeason;
        } else {
            throw new ServiceException("season.error.update");
        }
    }
    
    @Transactional
    public TouSeason deleteSeason(TouSeason del) throws ServiceException {
        try {
            if (touSeasonMapper.deleteByPrimaryKey(del.getId()) == 1) {
                return del;
            } else {
                throw new ServiceException("season.error.delete");
            }
        } catch (Exception e) {
            throw new ServiceException("season.error.delete");
        }
    }
    
    @Transactional(readOnly=true)
    public ArrayList<TouSeason> getTouSeasons() {
        TouSeasonExample touSeasonExample = new TouSeasonExample();
        touSeasonExample.createCriteria().andRecordStatusNotEqualTo(RecordStatus.DEL);
        ArrayList<TouSeason> returnlist = new ArrayList<TouSeason>(touSeasonMapper.selectByExample(touSeasonExample));
        return returnlist;
    }    
    
    @Transactional
    public TouPeriod insertPeriod(TouPeriod touPeriod) throws ServiceException {
        if (touPeriodMapper.insert(touPeriod) == 1) {
            return touPeriod;
        } else {
            throw new ServiceException("period.error.save");
        }
    }
    
    @Transactional
    public TouPeriod updatePeriod(TouPeriod touPeriod) throws ServiceException {
        if (touPeriodMapper.updateByPrimaryKey(touPeriod) == 1) {
            return touPeriod;
        } else {
            throw new ServiceException("period.error.update");
        }
    }
    
    @Transactional
    public TouPeriod deletePeriod(TouPeriod touPeriod) throws ServiceException {
        try {
            if (touPeriodMapper.deleteByPrimaryKey(touPeriod.getId()) == 1) {
                return touPeriod;
            } else {
                throw new ServiceException("period.error.delete");
            }
        } catch (Exception e) {
            throw new ServiceException("period.error.delete");
        }
    }
    
    @Transactional(readOnly=true)
    public ArrayList<TouPeriod> getTouPeriods() {
        TouPeriodExample touPeriodExample = new TouPeriodExample();
        touPeriodExample.createCriteria().andRecordStatusNotEqualTo(RecordStatus.DEL);
        ArrayList<TouPeriod> returnlist = new ArrayList<TouPeriod>(touPeriodMapper.selectByExample(touPeriodExample));
        return returnlist;
    }

    @Transactional(readOnly=true)
    public ArrayList<TouCalendar> getTouCalendars() {
        TouCalendarExample touCalendarExample = new TouCalendarExample();
        touCalendarExample.createCriteria().andRecordStatusNotEqualTo(RecordStatus.DEL);
        ArrayList<TouCalendar> returnlist = new ArrayList<TouCalendar>(touCalendarMapper.selectByExample(touCalendarExample));
        return returnlist;
    }

    @Transactional(readOnly=true)
    public List<TouSeason> getSeasonSearchSuggestions(String thequery) {
        ArrayList<TouSeason> returnlist = new ArrayList<TouSeason>(calendarSuggestionMapper.findSeasonByLikeLowerName(thequery, rowBounds));
        return returnlist;
    }
    
    @Transactional(readOnly=true)
    public List<TouPeriod> getPeriodSearchSuggestions(String thequery) {
        ArrayList<TouPeriod> returnlist = new ArrayList<TouPeriod>(calendarSuggestionMapper.findPeriodByLikeLowerName(thequery, rowBounds));
        return returnlist;
    }
    
    @Transactional(readOnly=true)
    public TouCalendarData getTouCalendarData(Long calendarId) {
        TouCalendar cal = touCalendarMapper.selectByPrimaryKey(calendarId);
        TouCalendarData data = new TouCalendarData();
        if (cal != null) {
            data = new TouCalendarData(cal);
            data.setCalendarSeasons(getTouCalendarSeasons(calendarId));
            data.setAssignedSeasons(getTouSeasonDates(calendarId));
            data.setPricingStructures(getPricingStructuresByCalendarId(calendarId));
        }
        
        //return ALL the calendar Data
        data.setAssignedSeasons(getTouSeasonDates(calendarId));
        data.setAssignedDayProfiles(getTouDayProfiles(calendarId));
        
        return data;
    }

    @Transactional(readOnly=true)
    private ArrayList<PricingStructure> getPricingStructuresByCalendarId(Long calendarId) {
        return pricingStructureService.getPricingStructuresByTouCalendarId(calendarId);
    }

    @Transactional(readOnly=true)
    public ArrayList<TouDayProfileData> getTouDayProfiles(Long calendarId) {
        TouDayProfileExample touDayProfileExample = new TouDayProfileExample();
        touDayProfileExample.createCriteria().andTouCalendarIdEqualTo(calendarId);
        ArrayList<TouDayProfile> tdplist = new ArrayList<TouDayProfile>(touDayProfileMapper.selectByExample(touDayProfileExample));
        ArrayList<TouDayProfileData> returnlist = new ArrayList<TouDayProfileData>(tdplist.size()); 
        TouDayProfileData tdpd;
        for (TouDayProfile dp : tdplist) {
            tdpd = new TouDayProfileData(dp);
            tdpd.setDayProfileTimesList(getTimesByTouDayProfile(dp.getId()));
            returnlist.add(tdpd);
        }
        return returnlist;
    }

    @Transactional(readOnly=true)
    public List<TouDayProfile> getDayProfileSearchSuggestions(Long calendarId, String thequery) {
        ArrayList<TouDayProfile> returnlist = new ArrayList<TouDayProfile>(calendarSuggestionMapper.findDayProfileByCalendarIdLikeLowerName(calendarId, thequery, rowBounds));
        return returnlist;
    }

    @Transactional(readOnly=true)
    public ArrayList<TouCalendarSeason> getTouCalendarSeasons(Long calendarId) {
        TouCalendarSeasonExample touCalendarSeasonExample = new TouCalendarSeasonExample();
        touCalendarSeasonExample.createCriteria().andTouCalendarIdEqualTo(calendarId);
        ArrayList<TouCalendarSeason> returnlist = new ArrayList<TouCalendarSeason>(touCalendarSeasonMapper.selectByExample(touCalendarSeasonExample));
        return returnlist;
    }

    @Transactional
    public TouCalendarData insertCalendar(TouCalendarData calendar) throws ValidationException, ServiceException {
        //Check for duplicate calendar name
        TouCalendarExample touCalendarExample = new TouCalendarExample();
        touCalendarExample.createCriteria().andNameEqualTo(calendar.getName());
        ArrayList<TouCalendar> calendarlist = new ArrayList<TouCalendar>(touCalendarMapper.selectByExample(touCalendarExample));
        if (calendarlist != null && !calendarlist.isEmpty()) {
            throw new ValidationException(new ValidationMessage("calendar.duplicate", true));
        }
        
        if (touCalendarMapper.insert(calendar) == 1) {
            for (TouCalendarSeason tcs : calendar.getCalendarSeasons()) {
                if (tcs.getId() == null) {
                    touCalendarSeasonMapper.insert(tcs);
                } else {
                    touCalendarSeasonMapper.updateByPrimaryKey(tcs);
                }
            }
            return calendar;
        } else {
            throw new ServiceException("calendar.error.save");
        }
    }
    
    private void checkPricingStructures(Long calendarId) throws ValidationException {
        if (calendarId != null) {
            ArrayList<PricingStructure> pss = getPricingStructuresByCalendarId(calendarId);
            String pricingstructures = "";
            if (pss != null && !(pss.isEmpty())) {
                for (PricingStructure ps : pss) {
                    if (pricingstructures.isEmpty()) {
                        pricingstructures = ps.getName().trim();
                    } else {
                        pricingstructures += (", "+ps.getName().trim());
                    }
                }
                throw new ValidationException(new ValidationMessage("calendar.readOnly", new String[] {pricingstructures}, true));
            }
        }
    }
    
    @Transactional
    public TouCalendarData updateCalendar(TouCalendarData calendar) throws ServiceException, ValidationException {
        checkPricingStructures(calendar.getId());
        if (touCalendarMapper.updateByPrimaryKey(calendar) == 1) {
            for (TouCalendarSeason tcs : calendar.getCalendarSeasons()) {
                if (tcs.getId() == null) {
                    touCalendarSeasonMapper.insert(tcs);
                } else {
                    touCalendarSeasonMapper.updateByPrimaryKey(tcs);
                }
            }
            updateCalendarSeasons(calendar.getCalendarSeasons());
            return calendar;
        } else {
            throw new ServiceException("calendar.error.save");
        }
    }
    
    
    @Transactional
    public void updateCalendarSeasons(ArrayList<TouCalendarSeason> calendarseasons) throws ServiceException, ValidationException {
        for (TouCalendarSeason tcs : calendarseasons) {
            checkPricingStructures(tcs.getTouCalendarId());
            if (tcs.getId() == null) {
                 if (touCalendarSeasonMapper.insert(tcs) !=1) {
                     throw new ServiceException("calendar.season.error.save");
                 }
            } else {
                if (touCalendarSeasonMapper.updateByPrimaryKey(tcs) !=1) {
                    throw new ServiceException("calendar.season.error.save");
                }
            }
        }
    }

    @Transactional
    public TouDayProfileData updateDayProfile(TouDayProfileData dayProfile) throws ServiceException, ValidationException {
        checkPricingStructures(dayProfile.getTouCalendarId());
        if (touDayProfileMapper.updateByPrimaryKey(dayProfile) == 1) {
            dayProfile.setDayProfileTimesList(getTimesByTouDayProfile(dayProfile.getId()));
            return dayProfile;
        } else {
            throw new ServiceException("dayprofile.error.save");
        }
    }
    @Transactional
    public TouDayProfileData insertDayProfile(TouDayProfileData dayProfile) throws ServiceException, ValidationException {
        checkPricingStructures(dayProfile.getTouCalendarId());
        if (touDayProfileMapper.insert(dayProfile) == 1) {
            dayProfile.setDayProfileTimesList(getTimesByTouDayProfile(dayProfile.getId()));
            return dayProfile;
        } else {
            throw new ServiceException("dayprofile.error.save");
        }
    }

    @Transactional
    public boolean deleteDayProfile(TouDayProfileData dayProfile) throws ServiceException, ValidationException {
        checkPricingStructures(dayProfile.getTouCalendarId());
        TouDayProfileTimeExample timeExample = new TouDayProfileTimeExample();
        timeExample.createCriteria().andTouDayProfileIdEqualTo(dayProfile.getId());
        touDayProfileTimeMapper.deleteByExample(timeExample);
        return (touDayProfileMapper.deleteByPrimaryKey(dayProfile.getId()) == 1);
    }
    
    @Transactional(readOnly=true)
    public ArrayList<TouSpecialDay> getSpecialDaysForCalendar(Long calendarId) {
        TouSpecialDayExample touSpecialDayExample = new TouSpecialDayExample();
        touSpecialDayExample.createCriteria().andTouCalendarIdEqualTo(calendarId);
        return new ArrayList<TouSpecialDay>(touSpecialDayMapper.selectByExample(touSpecialDayExample));        
    }
    
    @Transactional(readOnly=true)
    public ArrayList<TouSpecialDayData> getSpecialDays(Long calendarId) {
        ArrayList<TouSpecialDay> spdays = getSpecialDaysForCalendar(calendarId);
        ArrayList<TouSpecialDayData> returnList = new ArrayList<TouSpecialDayData>(spdays.size());
        TouSpecialDayData dta;
        for (TouSpecialDay dpt : spdays) {
            dta = new TouSpecialDayData(dpt);
            dta.setDayProfile(touDayProfileMapper.selectByPrimaryKey(dta.getTouDayProfileId()));
            returnList.add(dta);
        }
        return returnList;
    }

    @Transactional(readOnly=true)
    public ArrayList<TouSeasonDateData> getTouSeasonDates(Long calendarId) {
        TouSeasonDateExample tsdExample = new TouSeasonDateExample();
        tsdExample.createCriteria().andTouCalendarIdEqualTo(calendarId);
        ArrayList<TouSeasonDate> sdlist = new ArrayList<TouSeasonDate>(touSeasonDateMapper.selectByExample(tsdExample));
        ArrayList<TouSeasonDateData> returnList = new ArrayList<TouSeasonDateData>(sdlist.size());
        TouSeasonDateData dta;
        
        for (TouSeasonDate sd : sdlist) {
            dta = new TouSeasonDateData(sd);
            dta.setSeason(touSeasonMapper.selectByPrimaryKey(dta.getTouSeasonId()));
            returnList.add(dta);
        }
        return returnList;
    }

    @Transactional
    public TouSeasonDateData insertSeasonDate(TouSeasonDateData seasonDate) throws ServiceException, ValidationException {
        checkPricingStructures(seasonDate.getTouCalendarId());
        if (touSeasonDateMapper.insert(seasonDate) == 1) {
            seasonDate.setSeason(touSeasonMapper.selectByPrimaryKey(seasonDate.getTouSeasonId()));
            seasonDate.setCompleteMonths();
            return seasonDate;
        } else {
            throw new ServiceException("assign.season.error.save");
        }
    }

    @Transactional
    public TouSeasonDateData updateSeasonDate(TouSeasonDateData seasonDate) throws ServiceException, ValidationException {
        checkPricingStructures(seasonDate.getTouCalendarId());
        if (touSeasonDateMapper.updateByPrimaryKey(seasonDate) == 1) {
            seasonDate.setSeason(touSeasonMapper.selectByPrimaryKey(seasonDate.getTouSeasonId()));
            seasonDate.setCompleteMonths();
            return seasonDate;
        } else {
            throw new ServiceException("assign.season.error.update");
        }
    }

    @Transactional
    public Boolean deleteSeasonDate(TouSeasonDateData seasonDateData, boolean deleteCalendarSeason) throws ValidationException {
        checkPricingStructures(seasonDateData.getTouCalendarId());
        boolean deletedall = false;
        int done =  touSeasonDateMapper.deleteByPrimaryKey(seasonDateData.getId());
        if (done == 1) {
            if (deleteCalendarSeason) {
                TouCalendarSeasonExample touCalendarSeasonExample = new TouCalendarSeasonExample();
                touCalendarSeasonExample.createCriteria().andTouCalendarIdEqualTo(seasonDateData.getTouCalendarId()).andTouSeasonIdEqualTo(seasonDateData.getTouSeasonId());
                touCalendarSeasonMapper.deleteByExample(touCalendarSeasonExample);
                deletedall = true;
            } else {
                deletedall = true;
            }
        }
        return deletedall;
    }

    @Transactional(readOnly=true)
    public ArrayList<TouDayProfileTimeData> getTimesByTouDayProfile(Long dayProfileId) {
        TouDayProfileTimeExample tdptExample = new TouDayProfileTimeExample();
        tdptExample.createCriteria().andTouDayProfileIdEqualTo(dayProfileId);
        ArrayList<TouDayProfileTime> dptlist = new ArrayList<TouDayProfileTime>(touDayProfileTimeMapper.selectByExample(tdptExample));
        ArrayList<TouDayProfileTimeData> returnList = new ArrayList<TouDayProfileTimeData>(dptlist.size());
        TouDayProfileTimeData dta;
        Calendar cal = GregorianCalendar.getInstance();
        cal.set(2000, 1, 1);
        cal.set(Calendar.SECOND, 0);
        cal.set(Calendar.MILLISECOND, 0);
        for (TouDayProfileTime dpt : dptlist) {
            dta = new TouDayProfileTimeData(dpt);
            dta.setPeriod(touPeriodMapper.selectByPrimaryKey(dta.getTouPeriodId()));
            cal.set(Calendar.HOUR_OF_DAY, dpt.getStartHour());
            cal.set(Calendar.MINUTE, dpt.getStartMinute());
            dta.setStartMs(cal.getTimeInMillis());
            cal.set(Calendar.HOUR_OF_DAY, dpt.getEndHour());
            cal.set(Calendar.MINUTE, dpt.getEndMinute());
            dta.setEndMs(cal.getTimeInMillis());
            returnList.add(dta);
        }
        return returnList;
    }

    @Transactional
    public TouDayProfileTimeData updateDayProfileTime(TouDayProfileTimeData dayProfileTime, Long calendarId) throws ServiceException, ValidationException {
        checkPricingStructures(calendarId);
        if (touDayProfileTimeMapper.updateByPrimaryKey(dayProfileTime) == 1) {
            dayProfileTime.setPeriod(touPeriodMapper.selectByPrimaryKey(dayProfileTime.getTouPeriodId()));
            dayProfileTime.setCompleteHours();
            return dayProfileTime;
        } else {
            throw new ServiceException("assign.dayprofiletime.error.update");
        }
    }
    
    @Transactional
    public TouDayProfileTimeData insertDayProfileTime(TouDayProfileTimeData dayProfileTime, Long calendarId) throws ServiceException, ValidationException {
        checkPricingStructures(calendarId);
        if (touDayProfileTimeMapper.insert(dayProfileTime) == 1) {
            dayProfileTime.setPeriod(touPeriodMapper.selectByPrimaryKey(dayProfileTime.getTouPeriodId()));
            dayProfileTime.setCompleteHours();
            return dayProfileTime;
        } else {
            throw new ServiceException("assign.dayprofiletime.error.update");
        }
    }

    public TouSpecialDayData insertSpecialDay(TouSpecialDayData specialDay) throws ServiceException, ValidationException {
        checkPricingStructures(specialDay.getTouCalendarId());
        if (touSpecialDayMapper.insert(specialDay) == 1) {
            specialDay.setDayProfile(touDayProfileMapper.selectByPrimaryKey(specialDay.getTouDayProfileId()));
            return specialDay;
        } else {
            throw new ServiceException("calendar.specialday.error.update");
        }
    }

    public TouSpecialDayData updateSpecialDay(TouSpecialDayData specialDay) throws ServiceException, ValidationException {
        checkPricingStructures(specialDay.getTouCalendarId());
        if (touSpecialDayMapper.updateByPrimaryKey(specialDay) == 1) {
            specialDay.setDayProfile(touDayProfileMapper.selectByPrimaryKey(specialDay.getTouDayProfileId()));
            return specialDay;
        } else {
            throw new ServiceException("calendar.specialday.error.update");
        }
    }

    public Boolean deleteSpecialDay(Long specialDayId, Long calendarId) throws ValidationException {
        checkPricingStructures(calendarId);
        return (touSpecialDayMapper.deleteByPrimaryKey(specialDayId)==1);
    }

    public boolean deleteDayProfileTime(Long id, Long calendarId) throws ValidationException {
        checkPricingStructures(calendarId);
        return (touDayProfileTimeMapper.deleteByPrimaryKey(id)==1);
    }
    
    @Transactional(readOnly=true)
    public TouCalendarSeasonsPeriodsSpecialDaysData getCalendarData(Long calendarId) throws ServiceException {
        TouCalendarSeasonsPeriodsSpecialDaysData data = new TouCalendarSeasonsPeriodsSpecialDaysData();
        data.setTouCalendarId(calendarId);
        data.setSeasons(calendarCustomMapper.getTouSeasonsForCalendar(calendarId));
        List<TouPeriod> touPeriodsForCalendar = calendarCustomMapper.getTouPeriodsForCalendar(calendarId);
        data.setPeriods(touPeriodsForCalendar);
        ArrayList<SpecialDayProfile> specialDays = getSpecialDayProfilesForCalendar(calendarId, touPeriodsForCalendar);
        data.setSpecialDays(specialDays);
        return data;
    }

    private ArrayList<SpecialDayProfile> getSpecialDayProfilesForCalendar(Long calendarId,
            List<TouPeriod> touPeriodsForCalendar) {
        ArrayList<TouSpecialDay> specialDaysForCalendar = getSpecialDaysForCalendar(calendarId);
        ArrayList<SpecialDayProfile> specialDays = new ArrayList<SpecialDayProfile>(specialDaysForCalendar.size());
        for (TouSpecialDay touSpecialDay : specialDaysForCalendar) {
            SpecialDayProfile profile = new SpecialDayProfile();
            profile.setSpecialDay(touSpecialDay);
            // Use hashset so we only get unique periods for the day
            // because the day profile times could have periods repeat at different times
            HashSet<TouPeriod> touPeriods = new HashSet<TouPeriod>();
            profile.setPeriods(touPeriods);
            
            TouDayProfileTimeExample example = new TouDayProfileTimeExample();
            example.createCriteria().andTouDayProfileIdEqualTo(touSpecialDay.getTouDayProfileId());
            List<TouDayProfileTime> times = touDayProfileTimeMapper.selectByExample(example);
            for (TouDayProfileTime touDayProfileTime : times) {
                Long touDayProfileId = touDayProfileTime.getTouDayProfileId();
                touPeriods.add(getTouPeriodById(touPeriodsForCalendar, touDayProfileId));
            }
            
            specialDays.add(profile);
        }
        return specialDays;
    }

    private TouPeriod getTouPeriodById(List<TouPeriod> touPeriodsForCalendar, Long touDayProfileId) {
        for (TouPeriod touPeriod : touPeriodsForCalendar) {
            if(touPeriod.getId().equals(touDayProfileId)) {
                return touPeriod;
            }
        }
        return null;
    }

    public ArrayList<TouCalendar> getActiveTouCalendars() {
        TouCalendarExample touCalendarExample = new TouCalendarExample();
        touCalendarExample.createCriteria().andRecordStatusEqualTo(RecordStatus.ACT);
        ArrayList<TouCalendar> returnlist = new ArrayList<TouCalendar>(touCalendarMapper.selectByExample(touCalendarExample));
        return returnlist;
    }
}
