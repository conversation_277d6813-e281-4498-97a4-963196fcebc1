package za.co.ipay.metermng.server.mybatis.mapper;

import java.util.ArrayList;

import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Select;
import za.co.ipay.metermng.mybatis.generated.model.PricingStructure;

public interface ICalendarPricingStructureMapper {

    @Select(" select distinct p.*"
           +" from pricing_structure p left join tariff t on p.pricing_structure_id=t.pricing_structure_id "
           +" left join tou_tariff_calendar tc on tc.tariff_id = t.tariff_id "
           +" where tc.tou_calendar_id = #{touCalendarId}")
     @ResultMap("za.co.ipay.metermng.server.mybatis.mapper.ICalendarPricingStructureMapper.ExtendedResultMap")
     public ArrayList<PricingStructure> getPricingStructuresByAssignedCalendars(@Param("touCalendarId") long touCalendarId);
     
}
