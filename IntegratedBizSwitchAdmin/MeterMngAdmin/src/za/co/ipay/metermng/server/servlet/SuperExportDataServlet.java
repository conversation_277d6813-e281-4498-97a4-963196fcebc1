package za.co.ipay.metermng.server.servlet;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.nio.ByteBuffer;
import java.nio.channels.Channels;
import java.nio.channels.ReadableByteChannel;
import java.nio.channels.WritableByteChannel;

import javax.servlet.ServletConfig;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import org.apache.log4j.Logger;
import org.springframework.context.ApplicationContext;
import org.springframework.web.context.WebApplicationContext;

import za.co.ipay.gwt.common.server.util.ExposedReloadableResourceBundleMessageSource;
import za.co.ipay.metermng.shared.MeterMngStatics;
import za.co.ipay.metermng.shared.dto.user.MeterMngUser;

/**
 * SuperExportDataServlet defines common methods and variables used in the data export classes. 
 * The subclasses are used to export specific data using a file so that the user can save the file for offline use.
 */
public abstract class SuperExportDataServlet extends HttpServlet {

    private static final long serialVersionUID = 1L;
    private static Logger logger = Logger.getLogger(SuperExportDataServlet.class.getName());

    protected ExposedReloadableResourceBundleMessageSource messageSource;
    protected ExposedReloadableResourceBundleMessageSource formatSource;

    @Override
    public void init(ServletConfig config) throws ServletException {
        super.init(config);
        ApplicationContext ac = (ApplicationContext) config.getServletContext().getAttribute(WebApplicationContext.ROOT_WEB_APPLICATION_CONTEXT_ATTRIBUTE);
        this.messageSource = (ExposedReloadableResourceBundleMessageSource) ac.getBean("messageSource");
        this.formatSource = (ExposedReloadableResourceBundleMessageSource) ac.getBean("formatSource");
        if (messageSource == null || formatSource == null) {
            throw new ServletException("Missing beans not set in the ExportDataServlet: " + messageSource + " " + formatSource);
        }
    }

    protected MeterMngUser getCurrentUser(HttpServletRequest request) {
        HttpSession session = request.getSession(false);
        if (session != null) {
            MeterMngUser user = (MeterMngUser) session.getAttribute(MeterMngStatics.METER_MNG_USER_ATTR);
            if (user != null) {
                logger.info("User found in session: " + user.getUserName());
                return user;
            }
        }
        return null;
    }

    protected void writeError(HttpServletResponse response, String errorMessage) throws IOException {
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        baos.write(errorMessage.getBytes());
        response.setContentLength(baos.size());
        baos.writeTo(response.getOutputStream());
        response.getOutputStream().flush();
        logger.info("Wrote error: " + errorMessage);
    }

    protected long stream(InputStream input, OutputStream output) throws IOException {
        try (ReadableByteChannel inputChannel = Channels.newChannel(input);
                WritableByteChannel outputChannel = Channels.newChannel(output)) {
            ByteBuffer buffer = ByteBuffer.allocate(10240);
            long size = 0;
            while (inputChannel.read(buffer) != -1) {
                buffer.flip();
                size += outputChannel.write(buffer);
                buffer.clear();
            }
            return size;
        }
    }

    protected abstract void doDataExport(HttpServletRequest request, HttpServletResponse response) throws IOException, ServletException;

    protected abstract String getExportFileName(HttpServletRequest request);

}
