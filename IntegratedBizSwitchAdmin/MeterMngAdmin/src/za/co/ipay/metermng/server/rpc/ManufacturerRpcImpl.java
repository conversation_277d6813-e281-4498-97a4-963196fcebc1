package za.co.ipay.metermng.server.rpc;

import java.util.ArrayList;

import org.apache.log4j.Logger;

import za.co.ipay.gwt.common.shared.exception.ServiceException;
import za.co.ipay.gwt.common.shared.exception.ValidationException;
import za.co.ipay.metermng.client.rpc.ManufacturerRpc;
import za.co.ipay.metermng.mybatis.generated.model.Manufacturer;
import za.co.ipay.metermng.server.mybatis.service.ManufacturerService;

public class ManufacturerRpcImpl extends BaseMeterMngRpc implements ManufacturerRpc {

    private static final long serialVersionUID = 1L;
    
    private ManufacturerService manufacturerService;
    
    public ManufacturerRpcImpl() {
        this.logger = Logger.getLogger(ManufacturerRpcImpl.class);
    }

    @Override
    public Integer getManufacturersCount() throws ServiceException {
        return manufacturerService.getManufacturersCount();
    }

    @Override
    public ArrayList<Manufacturer> getManufacturers(int startRow, int pageSize, String sortField, boolean isAscending)
            throws ServiceException {
        return manufacturerService.getManufacturers(startRow, pageSize, sortField, isAscending);
    }

    @Override
    public void saveManufacturer(Manufacturer manufacturer) throws ValidationException, ServiceException {
        manufacturerService.saveManufacturer(manufacturer);
    }

    public void setManufacturerService(ManufacturerService manufacturerService) {
        this.manufacturerService = manufacturerService;
    }    
}
