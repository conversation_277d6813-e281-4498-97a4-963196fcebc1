package za.co.ipay.metermng.server.mybatis.service;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.apache.ibatis.session.RowBounds;
import org.apache.log4j.Logger;

import za.co.ipay.gwt.common.shared.exception.ServiceException;
import za.co.ipay.gwt.common.shared.exception.ValidationException;
import za.co.ipay.gwt.common.shared.exception.ValidationMessage;
import za.co.ipay.metermng.datatypes.KeyChangeInstructionE;
import za.co.ipay.metermng.fileimport.DefaultFileImportService;
import za.co.ipay.metermng.fileimport.FileImportHandler;
import za.co.ipay.metermng.fileimport.ImportItemDataConverter;
import za.co.ipay.metermng.fileimport.ImportParamRecordConverter;
import za.co.ipay.metermng.fileimport.exceptions.FileImportException;
import za.co.ipay.metermng.integration.offlinebulkuploads.metercustup.MeterCustUpBulkImportHandler;
import za.co.ipay.metermng.ipayxml.IpayXmlMessageService;
import za.co.ipay.metermng.mybatis.generated.mapper.ImportFileItemImportMapper;
import za.co.ipay.metermng.mybatis.generated.mapper.ImportFileItemMapper;
import za.co.ipay.metermng.mybatis.generated.mapper.ImportFileMapper;
import za.co.ipay.metermng.mybatis.generated.mapper.ImportFileTypeMapper;
import za.co.ipay.metermng.mybatis.generated.mapper.ProductInfoMapper;
import za.co.ipay.metermng.mybatis.generated.model.ImportFile;
import za.co.ipay.metermng.mybatis.generated.model.ImportFileExample;
import za.co.ipay.metermng.mybatis.generated.model.ImportFileItem;
import za.co.ipay.metermng.mybatis.generated.model.ImportFileItemExample;
import za.co.ipay.metermng.mybatis.generated.model.ImportFileItemImport;
import za.co.ipay.metermng.mybatis.generated.model.ImportFileItemImportExample;
import za.co.ipay.metermng.mybatis.generated.model.ImportFileType;
import za.co.ipay.metermng.mybatis.generated.model.ImportFileTypeExample;
import za.co.ipay.metermng.mybatis.generated.model.ProductInfo;
import za.co.ipay.metermng.mybatis.generated.model.ProductInfoExample;
import za.co.ipay.metermng.server.mybatis.mapper.ImportFileCustomMapper;
import za.co.ipay.metermng.shared.MeterMngStatics;
import za.co.ipay.metermng.shared.dto.BulkParamRecord;
import za.co.ipay.metermng.shared.dto.importfile.ImportFileActionParamsDto;
import za.co.ipay.metermng.shared.dto.importfile.ImportFileDto;
import za.co.ipay.metermng.shared.dto.importfile.ImportFileItemDto;
import za.co.ipay.metermng.shared.dto.importfile.ImportFileItemListDto;
import za.co.ipay.metermng.shared.dto.importfile.ImportFileListDto;
import za.co.ipay.metermng.shared.dto.importfile.ImportFileResultDto;
import za.co.ipay.metermng.shared.dto.importfile.KeyChangeDto;
import za.co.ipay.metermng.shared.integration.GenericImportRecord;
import za.co.ipay.metermng.shared.integration.bulkkeychange.BulkKeyChangeParamRecord;
import za.co.ipay.metermng.shared.integration.tariffexport.TariffPsImportRecord;

public class ImportFileDataService {
    private static Logger logger = Logger.getLogger(ImportFileDataService.class);
    private DateFormat filenameDateFormat = new SimpleDateFormat("yyyyMMdd_HHmmss");

    private ImportFileMapper importFileMapper;
    private ImportFileTypeMapper importFileTypeMapper;
    private ImportFileItemMapper importFileItemMapper;
    private ImportFileItemImportMapper importFileItemImportMapper;
    private DefaultFileImportService defaultFileImportService;
    private ImportFileCustomMapper importFileCustomMapper;

    private ProductInfoMapper productInfoMapper;
    
    private PricingStructureService pricingStructureService;
    private IpayXmlMessageService ipayXmlMessageService;
    private EngineeringTokensService engineeringTokensService;

    public void setImportFileMapper(ImportFileMapper importFileMapper) {
        this.importFileMapper = importFileMapper;
    }

    public void setImportFileTypeMapper(ImportFileTypeMapper importFileTypeMapper) {
        this.importFileTypeMapper = importFileTypeMapper;
    }

    public void setImportFileItemMapper(ImportFileItemMapper importFileItemMapper) {
        this.importFileItemMapper = importFileItemMapper;
    }

    public void setImportFileItemImportMapper(ImportFileItemImportMapper importFileItemImportMapper) {
        this.importFileItemImportMapper = importFileItemImportMapper;
    }
    
    public void setDefaultFileImportService(DefaultFileImportService defaultFileImportService) {
        this.defaultFileImportService = defaultFileImportService;
    }

    public void setImportFileCustomMapper(ImportFileCustomMapper importFileCustomMapper) {
        this.importFileCustomMapper = importFileCustomMapper;
    }
    
    public void setPricingStructureService(PricingStructureService pricingStructureService) {
        this.pricingStructureService = pricingStructureService;
    }
    
    public void setIpayXmlMessageService(IpayXmlMessageService ipayXmlMessageService) {
        this.ipayXmlMessageService = ipayXmlMessageService;
    }
    
    public void setEngineeringTokensService(EngineeringTokensService engineeringTokensService) {
        this.engineeringTokensService = engineeringTokensService;
    }

    public void setProductInfoMapper(ProductInfoMapper productInfoMapper) {
        this.productInfoMapper = productInfoMapper;
    }

    //-----------------------------------------------------------------------------------------------------------------------
    public static void setLogger(Logger logger) {
        ImportFileDataService.logger = logger;
    }

    public void setFilenameDateFormat(DateFormat filenameDateFormat) {
        this.filenameDateFormat = filenameDateFormat;
    }

    public List<ImportFileType> getActiveImportFileTypes() {
        return defaultFileImportService.listFileTypes();
    }
    
    public ImportFileType getImportFileTypeByName(String importFileTypeValue) {
        ImportFileTypeExample ex = new ImportFileTypeExample();
        ex.createCriteria().andNameEqualTo(importFileTypeValue);
        List<ImportFileType> importFileTypeList = importFileTypeMapper.selectByExample(ex);
        if (importFileTypeList.size() < 1) {
            logger.error("**** INVALID importFileTypeValue: " + importFileTypeValue);
            return null;
        }
        return importFileTypeList.get(0);
    }
    
    public ImportFileType getImportFileTypeById(Long importFileTypeId) {
        return importFileTypeMapper.selectByPrimaryKey(importFileTypeId);
    }
    
    public ImportFileDto getImportFileDtoById(Long importFileId) {
        ImportFile importFile = importFileMapper.selectByPrimaryKey(importFileId);
        ImportFileDto dto = new ImportFileDto(importFile);
        
        ImportFileType importFileType = importFileTypeMapper.selectByPrimaryKey(importFile.getImportFileTypeId());
        dto.setImportFileTypeName(importFileType.getName());
        dto.setImportFileTypeClass(importFileType.getTypeClass());
        dto.setAllowItemsMultipleImport(importFileType.getAllowItemsMultipleImport());
        dto.setHasActionParams(importFileType.isHasActionParams());
        
        dto.setNumFailedUploads(getNumUploadUnSuccessful(dto.getId()));
        dto.setNumSuccessfulImports(importFileCustomMapper.getCountSuccessfulImports(importFileId));

        if (importFileType.getTypeClass().equalsIgnoreCase(MeterCustUpBulkImportHandler.class.getName())) {
            ProductInfoExample productInfoExample = new ProductInfoExample();
            List<ProductInfo> productInfoList = productInfoMapper.selectByExample(productInfoExample);
            dto.setLicenceCount(productInfoList.get(0).getLicenseCount());
            dto.setCurrentCount(productInfoList.get(0).getCurrentCount());
        }
        return dto;
    }
    
    public ImportFileActionParamsDto getImportFileParamData(String userName, String typeClass, String importFileName) {
        String shortenedFileTypeClass = typeClass.substring(typeClass.lastIndexOf(".") + 1);
        //get ImportFileType from the typeClass
        ImportFileTypeExample exIft = new ImportFileTypeExample();
        exIft.createCriteria().andTypeClassEqualTo(typeClass);
        List<ImportFileType> listFileTypes = importFileTypeMapper.selectByExample(exIft);
        if (listFileTypes.size() < 1) {
            logger.error("**** ERROR - no import File Type found = " + typeClass);
            return null;
        }
        ImportFileType importFileType = listFileTypes.get(0);
        
        ImportFile importFile = null;
        if (importFileName == null) {
            //fileName will be null if this is a bulk change that only needs params and no data
            //generate a dummy filename & insert the ImportFile record
            Date now = new Date();
            //NB NB importFileItemView.setImportFileDto(..) : filename.startsWith("dummy") is used to establish whether to show import buttons or not
            //and when execute LoadParams whether to show "from params" for instruction only, no input file (see when construct column in table to viewParams.
            importFileName = "dummy_" + shortenedFileTypeClass + "_" + filenameDateFormat.format(now) + "_" + userName + ".txt";
            importFile = new ImportFile();
            importFile.setUploadStart(now);
            importFile.setUploadEnd(now);
            importFile.setImportFolder("no_data_only_params");
            importFile.setImportFilename(importFileName);
            importFile.setImportFileTypeId(importFileType.getId());
            importFile.setUploadUsername(userName);
            importFileMapper.insertSelective(importFile);
        }
        
        ImportFileExample ex = new ImportFileExample();
        ex.createCriteria().andImportFilenameEqualTo(importFileName);
        List<ImportFile> importFileList = importFileMapper.selectByExample(ex);
        if (importFileList.size() < 1) {
            logger.error("**** ERROR - no import File found with name = " + importFileName);
            return null;
        }
        importFile = importFileList.get(0);
        if (!importFile.getImportFileTypeId().equals(importFileType.getId())) {
            logger.error(("**** ERROR - importFileType of import File name : " + importFileName + " not of the selected filetype = " + typeClass));
            return null;
        }
        
        ImportFileActionParamsDto dto =  new ImportFileActionParamsDto(); 
        dto.setImportFile(importFile);
        dto.setImportFileTypeClass(typeClass);
        
        String actionParams = importFile.getActionParams();
        if (actionParams != null) {
            dto.setBulkParamRecord((BulkParamRecord)(getParamRecordConverter(typeClass).convertToObject(actionParams)));
        }
        return dto;
    }
    
    private ImportParamRecordConverter getParamRecordConverter(String importFileTypeClass) {
        FileImportHandler fileTypeHandler = defaultFileImportService.loadFileImportHandler(importFileTypeClass);
        ImportParamRecordConverter importParamRecordConverter = fileTypeHandler.getImportParamRecordConverter();
        if (importParamRecordConverter == null) {
            throw new ServiceException("import.file.no.params.converter.error");
        }
        return importParamRecordConverter;
    }
    
    public String getImportFileNameFromBulkRef(String bulkRef) {
        ImportFileExample ex = new ImportFileExample();
        ex.createCriteria().andBulkRefEqualTo(bulkRef);
        List<ImportFile> listImportFiles = importFileMapper.selectByExample(ex);
        if (listImportFiles == null || listImportFiles.isEmpty()) {
            return null;
        } else {
            return listImportFiles.get(0).getImportFilename();
        }
    }
    
    public ImportFileListDto selectImportFiles(int start, int pageSize, String sortColumn, String filterColumn,
            String filterString, Date filterDate, String order, String[] importfileTypeIds,
            Boolean blockingPermission) {
        ImportFileListDto dto = new ImportFileListDto();
        boolean sortByUploadFailures = "upload_failures".equals(sortColumn);
        if (sortByUploadFailures) {
            sortColumn = "upload_start";
        }
        Integer totalResultCount = importFileCustomMapper.getImportFilesCount(sortColumn, filterColumn, filterString,
                filterDate, order, importfileTypeIds, blockingPermission);
        dto.setResultCount(totalResultCount);

        RowBounds rowBounds = new RowBounds(start, pageSize);
        List<ImportFileDto> listImportFileDto = importFileCustomMapper.selectImportFiles(sortColumn, filterColumn,
                filterString, filterDate, order, rowBounds, importfileTypeIds, blockingPermission);

        // update with count of unsuccessful uploads in the file
        ProductInfo productInfo = null;
        for (ImportFileDto importFileDto : listImportFileDto) {
            ImportFileType fileType = importFileTypeMapper.selectByPrimaryKey(importFileDto.getImportFileTypeId());
            FileImportHandler fileTypeHandler = defaultFileImportService.loadFileImportHandler(fileType);
            importFileDto.setAllowExportFailedItems(fileTypeHandler.allowExportFailedItems());
            importFileDto.setNumFailedUploads(getNumUploadUnSuccessful(importFileDto.getId()));
            if (importFileDto.getImportFileTypeClass().equalsIgnoreCase(MeterCustUpBulkImportHandler.class.getName())) {
                ProductInfoExample productInfoExample = new ProductInfoExample();
                List<ProductInfo> productInfoList = productInfoMapper.selectByExample(productInfoExample);
                importFileDto.setLicenceCount(productInfoList.get(0).getLicenseCount());
                importFileDto.setCurrentCount(productInfoList.get(0).getCurrentCount());
            }

            //Check if will need export button for bulk keychange - show only if bulk instruction = generate now
            String fileTypeClass = fileType.getTypeClass();
            if (MeterMngStatics.FILETYPE_BULK_KEY_CHANGE_GENERATOR.equals(fileTypeClass)) {
                String actionParams = importFileDto.getActionParams();
                //on first pass, action params may not have been input yet
                if (actionParams != null && !actionParams.isEmpty()) {
                    BulkKeyChangeParamRecord paramRec = ((BulkKeyChangeParamRecord)(getParamRecordConverter(fileTypeClass).convertToObject(actionParams)));
                    if (KeyChangeInstructionE.GENERATENOW.equals(paramRec.getBulkInstruction())) {
                        importFileDto.setExtractAvailable(true);
                    } else {
                        importFileDto.setExtractAvailable(false);
                    }
                }
            }
            importFileDto.setNumSuccessfulImports(importFileCustomMapper.getCountSuccessfulImports(importFileDto.getId()));

            if (MeterMngStatics.FILETYPE_METERCUSTUP_BULK_IMPORT.equals(fileTypeClass)) {
                if (productInfo==null) {
                    ProductInfoExample productInfoExample = new ProductInfoExample();
                    List<ProductInfo> productInfoList = productInfoMapper.selectByExample(productInfoExample);
                    productInfo = productInfoList.get(0);
                }
                importFileDto.setLicenceCount(productInfo.getLicenseCount());
                importFileDto.setCurrentCount(productInfo.getCurrentCount());
            }       
        }
        if (sortByUploadFailures) {
            listImportFileDto.sort((o1, o2) -> {
                int comparison = o1.getNumFailedUploads().compareTo(o2.getNumFailedUploads());
                if ("DESC".equals(order)) {
                    comparison *= -1;
                }
                return comparison;
            });
        }

        dto.setListImportFileDto(listImportFileDto);
        return dto;
    }
    
    public ImportFileItemListDto selectImportFileItems(Long importFileId, int start, int pageSize, String sortColumn, String filterColumn, String filterString, Date filterDate, String order) {
        ImportFileItemListDto dto = new ImportFileItemListDto();
        Integer totalResultCount = importFileCustomMapper.getImportFileItemsCount(importFileId, sortColumn, filterColumn, filterString, filterDate, order);
        dto.setResultCount(totalResultCount);
        
        RowBounds rowBounds = new RowBounds(start, pageSize);
        List<ImportFileItem> importFileItemList = importFileCustomMapper.selectImportFileItems(importFileId, sortColumn, filterColumn, filterString, filterDate, order, rowBounds);
        List<ImportFileItemDto> listOfImportFileItemDto = new ArrayList<ImportFileItemDto>();
        ImportFileDto importFileDto = getImportFileDtoById(importFileId);
        String importFileTypeClass = importFileDto.getImportFileTypeClass();
        
        for (ImportFileItem importFileitem : importFileItemList) {
            ImportFileItemDto itemDto = new ImportFileItemDto(importFileitem);
            String itemData = importFileitem.getItemData();
            FileImportHandler fileTypeHandler = defaultFileImportService.loadFileImportHandler(importFileTypeClass);
            //for most import filetypes, set both the record and the string. 
            //the record allows custom display of columns, whereas the string allows use of generic detail popup
            //meterCustUp is the exception - here we don't have a record format - because want to maintain the columns that WERE imported for re-export of failures in the same format
            itemDto.setImportFileRecordStr(itemData);
            if (!MeterMngStatics.FILETYPE_METERCUSTUP_BULK_IMPORT.equals(importFileTypeClass)) {
                itemDto.setGenericImportRecord((GenericImportRecord)fileTypeHandler.getImportItemDataConverter().convertToObject(itemData));
            }
            ImportFileItemImportExample ex = new ImportFileItemImportExample();
            ex.createCriteria().andImportFileItemIdEqualTo(importFileitem.getId());
            ex.setOrderByClause("import_date desc");
            List<ImportFileItemImport> importFileItemImportList = importFileItemImportMapper.selectByExample(ex);
            if (importFileItemImportList != null && !importFileItemImportList.isEmpty()) {
                String comment = importFileItemImportList.get(0).getComment();
                if (comment != null && !comment.isEmpty()) {
                    itemDto.setImportFileItemImportComment(comment);
                }
            }
            listOfImportFileItemDto.add(itemDto);
        }
        
        dto.setListOfImportFileItemDto(listOfImportFileItemDto);
        return dto;
    }

    public String updateImportFileItem(ImportFileItemDto importFileItemDto) throws ValidationException {
        String itemData = importFileItemDto.getImportFileRecordStr();
        if (itemData == null) {
            //string was converted to an object for a custom dialogueBox
            ImportFileDto importFileDto = getImportFileDtoById(importFileItemDto.getImportFileItem().getImportFileId());
            String importFileTypeClass = importFileDto.getImportFileTypeClass();
            FileImportHandler fileTypeHandler = defaultFileImportService.loadFileImportHandler(importFileTypeClass);
            ImportItemDataConverter itemDataConverter = fileTypeHandler.getImportItemDataConverter();
            itemData = itemDataConverter.convertToString(importFileItemDto.getGenericImportRecord());
            if (importFileItemDto.getGenericImportRecord() instanceof TariffPsImportRecord) {
                //check calcContents after editing if Json format still intact
                TariffPsImportRecord tariffPsImportRecord = (TariffPsImportRecord) importFileItemDto.getGenericImportRecord();
                try {
                    pricingStructureService.checkCalcContents(tariffPsImportRecord.getCalcClassDONOTCHANGE(), tariffPsImportRecord.getCalcContents());
                } catch (Exception e) {
                    String err = e.getMessage();
                    int errIndx = e.getMessage().lastIndexOf("Exception: ");
                    if (errIndx > -1) {
                        err = err.substring(errIndx);
                    }
                    throw new ValidationException(new ValidationMessage("import.tariff.edit.resave.error", new String[] {err}, true));
                }
            }

        }
        defaultFileImportService.updateImportFileItem(importFileItemDto.getImportFileItem().getId(), itemData, importFileItemDto.getImportFileItem().getComment());
        return itemData;
    }
    
    public void updateImportFileParams(ImportFileActionParamsDto dto) throws ServiceException {
        String actionParams = null;
        actionParams = getParamRecordConverter(dto.getImportFileTypeClass()).convertToString(dto.getBulkParamRecord());

        ImportFile importFile = dto.getImportFile();
        importFile.setActionParams(actionParams);
        if (importFileMapper.updateByPrimaryKey(importFile) != 1) {
            throw new ServiceException("import.file.params.save.error");
        }
    }
    
    public ImportFileResultDto importSelectedItems(String username, Long userGenGroupId, Long userAccessGroupId, ImportFile importFile, List<Long> selectedItemsList, boolean isAccessGroupsEnabled) {
        logger.info("FILE IMPORT: importSelectedItems() username=" + username + " importFile=" + importFile.getImportFilename() + " list size=" + selectedItemsList.size());
        //get the items
        ImportFileItemExample ex = new ImportFileItemExample();
        ex.createCriteria().andIdIn(selectedItemsList);
        List<ImportFileItem> itemsList = importFileItemMapper.selectByExample(ex);

        defaultFileImportService.setIpayXmlMessageService(ipayXmlMessageService);
        try {
            defaultFileImportService.importItems(username, userGenGroupId, userAccessGroupId, importFile, itemsList, isAccessGroupsEnabled);
        } catch (FileImportException e) {
            return new ImportFileResultDto(Boolean.FALSE, e.getMessage(), getNumUploadUnSuccessful(importFile.getId()));
        }
        
        //check all imports successful --> get count of failed imports in the list of item-ids
        ex = new ImportFileItemExample();
        ex.createCriteria().andIdIn(selectedItemsList)
                           .andLastImportSuccessfulEqualTo(false);
        int failedImports = (importFileItemMapper.selectByExample(ex)).size();
        
        return new ImportFileResultDto(failedImports == 0 ? Boolean.TRUE : Boolean.FALSE, 
                null, getNumUploadUnSuccessful(importFile.getId()));
    }

    public ImportFileResultDto importAllItems(String username, Long userGenGroupId, Long userAccessGroupId, ImportFile importFile, boolean isAccessGroupsEnabled) {
       logger.info("FILE IMPORT: importAllItems() username=" + username + " importFile=" + importFile.getImportFilename());
       defaultFileImportService.setIpayXmlMessageService(ipayXmlMessageService);
       Boolean stoppedImport = false;
       
       try {
           defaultFileImportService.importAll(username, userGenGroupId, userAccessGroupId, importFile, isAccessGroupsEnabled);       
       if (importFile.getStopImport()) {
           stoppedImport = true;
           importFile.setStopImport(false);
           importFileMapper.updateByPrimaryKeySelective(importFile);
       }

       } catch (FileImportException e) {
           return new ImportFileResultDto(Boolean.FALSE, e.getMessage(), getNumUploadUnSuccessful(importFile.getId()), stoppedImport);
       }
       
       //check for success
       int failedImports = extractFailedItems(importFile).size();
       return new ImportFileResultDto(failedImports == 0 ? Boolean.TRUE : Boolean.FALSE, 
               null, getNumUploadUnSuccessful(importFile.getId()), stoppedImport);
    }
    
    public List<ImportFileItem> extractFailedItems(ImportFile importFile) {
        logger.info("FILE IMPORT: extractFailedItems() importFile=" + importFile.getImportFilename());
        //get the items that have failed last import
        ImportFileItemExample ex = new ImportFileItemExample();
        ex.createCriteria().andImportFileIdEqualTo(importFile.getId())
                           .andLastImportSuccessfulEqualTo(false);
        ex.setOrderByClause("upload_successful desc");
        List<ImportFileItem> list = importFileItemMapper.selectByExample(ex);
        logger.info("FILE IMPORT: extractItems() importFile=" + importFile.getImportFilename() + " extracted list size=" + list.size());

        return list;
    }
    
    public List<KeyChangeDto> getKeyChangeTokensforBulkRef(String bulkRef) {
        return engineeringTokensService.getKeychangeTokensForBulkRef(bulkRef);
    }
    
    public void stopImportAll(String username, ImportFile importFile) {
        logger.info("FILE IMPORT: stopImportAll() issued by username=" + username + " importFile=" + importFile.getImportFilename());
        importFile = importFileMapper.selectByPrimaryKey(importFile.getId());
        importFile.setStopImport(true);
        importFileMapper.updateByPrimaryKeySelective(importFile);
    }
    
    public int getNumUploadUnSuccessful(Long importFileId) {
        ImportFileItemExample ex = new ImportFileItemExample();
        ex.createCriteria().andImportFileIdEqualTo(importFileId)
                           .andUploadSuccessfulEqualTo(Boolean.FALSE);
        List<ImportFileItem> lst = importFileItemMapper.selectByExample(ex);
        if (lst != null && !lst.isEmpty()) {
            return lst.size();
        } else {
            return 0;
        }
    }
}
