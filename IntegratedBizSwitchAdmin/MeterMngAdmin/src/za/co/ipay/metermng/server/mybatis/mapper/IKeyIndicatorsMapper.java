package za.co.ipay.metermng.server.mybatis.mapper;

import java.math.BigDecimal;
import java.sql.Date;

import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

public interface IKeyIndicatorsMapper {

	// ***
	// Total Sales
	@Select("SELECT COALESCE(SUM(CAST(amt_incl_tax AS numeric(19,2))),0.00) "
			+ "FROM customer_trans " 
			+ "INNER JOIN usage_point "
			+ "ON customer_trans.usage_point_id = usage_point.usage_point_id "
			+ "WHERE usage_point.gen_group_id = #{genGroupId} "
			+ "AND trans_date > #{todaysDate} "
	        + "AND customer_trans_type_id != 2 "
            + "AND reversed = biz_false")
	public BigDecimal getTotalSalesToday(@Param("genGroupId") Long genGroupId,
			@Param("todaysDate") Date todaysDate);

	@Select("SELECT COALESCE(SUM(CAST(amt_incl_tax AS numeric(19,2))),0.00) "
			+ "FROM customer_trans " 
			+ "INNER JOIN usage_point "
			+ "ON customer_trans.usage_point_id = usage_point.usage_point_id "
			+ "WHERE usage_point.gen_group_id = #{genGroupId} "
			+ "AND trans_date > #{monthStartDate} "
	        + "AND customer_trans_type_id != 2 "
            + "AND reversed = biz_false")
	public BigDecimal getTotalSalesMonthToDate(@Param("genGroupId") Long genGroupId,
			@Param("monthStartDate") Date monthStartDate);

	@Select("SELECT COALESCE(SUM(CAST(amt_incl_tax AS numeric(19,2))),0.00) "
			+ "FROM customer_trans "
			+ "INNER JOIN usage_point "
			+ "ON customer_trans.usage_point_id = usage_point.usage_point_id "
			+ "WHERE usage_point.gen_group_id = #{genGroupId} "
			+ "AND trans_date > #{lastMonthStartDate} "
			+ "AND trans_date < #{monthStartDate} "
	        + "AND customer_trans_type_id != 2 "
            + "AND reversed = biz_false")
	public BigDecimal getTotalSalesLastMonth(@Param("genGroupId") Long genGroupId,
			@Param("monthStartDate") Date monthStartDate,
			@Param("lastMonthStartDate") Date lastMonthStartDate);

	// ***
	// Transactions
	@Select("SELECT count(customer_trans_id) "
			+ "FROM customer_trans " 
			+ "INNER JOIN usage_point "
			+ "ON customer_trans.usage_point_id = usage_point.usage_point_id "
			+ "WHERE usage_point.gen_group_id = #{genGroupId} "
			+ "AND trans_date > #{todaysDate} "
	        + "AND customer_trans_type_id != 2 "
            + "AND reversed = biz_false")
	public String getTransactionsToday(@Param("genGroupId") Long genGroupId,
			@Param("todaysDate") Date todaysDate);

	@Select("SELECT count(customer_trans_id) "
			+ "FROM customer_trans "
			+ "INNER JOIN usage_point "
			+ "ON customer_trans.usage_point_id = usage_point.usage_point_id "
			+ "WHERE usage_point.gen_group_id = #{genGroupId} "
			+ "AND trans_date > #{monthStartDate} "
            + "AND customer_trans_type_id != 2 "
			+ "AND reversed = biz_false")
	public String getTransactionsMonthToDate(@Param("genGroupId") Long genGroupId,
			@Param("monthStartDate") Date monthStartDate);

	@Select("SELECT count(customer_trans_id) "
			+ "FROM customer_trans "
			+ "INNER JOIN usage_point "
			+ "ON customer_trans.usage_point_id = usage_point.usage_point_id "
			+ "WHERE usage_point.gen_group_id = #{genGroupId} "
			+ "AND trans_date > #{lastMonthStartDate} "
			+ "AND trans_date < #{monthStartDate} "
	        + "AND customer_trans_type_id != 2 "
            + "AND reversed = biz_false")
	public String getTransactionsLastMonth(@Param("genGroupId") Long genGroupId,
			@Param("monthStartDate") Date monthStartDate,
			@Param("lastMonthStartDate") Date lastMonthStartDate);

	// ***
	// Transacting Meters
	@Select("SELECT count(DISTINCT customer_trans.meter_id) "
			+ "FROM customer_trans " 
			+ "INNER JOIN usage_point "
			+ "ON customer_trans.usage_point_id = usage_point.usage_point_id "
			+ "WHERE usage_point.gen_group_id = #{genGroupId} "
			+ "AND trans_date > #{todaysDate} "
	        + "AND customer_trans_type_id != 2 "
            + "AND reversed = biz_false")
	public String getTransactingMetersToday(@Param("genGroupId") Long genGroupId,
			@Param("todaysDate") Date todaysDate);

	@Select("SELECT count(DISTINCT customer_trans.meter_id) "
			+ "FROM customer_trans " 
			+ "INNER JOIN usage_point "
			+ "ON customer_trans.usage_point_id = usage_point.usage_point_id "
			+ "WHERE usage_point.gen_group_id = #{genGroupId} "
			+ "AND trans_date > #{monthStartDate} "
	        + "AND customer_trans_type_id != 2 "
            + "AND reversed = biz_false")
	public String getTransactingMetersMonthToDate(@Param("genGroupId") Long genGroupId,
			@Param("monthStartDate") Date monthStartDate);

	@Select("SELECT count(DISTINCT customer_trans.meter_id) "
			+ "FROM customer_trans "
			+ "INNER JOIN usage_point "
			+ "ON customer_trans.usage_point_id = usage_point.usage_point_id "
			+ "WHERE usage_point.gen_group_id = #{genGroupId} "
			+ "AND trans_date > #{lastMonthStartDate} "
			+ "AND trans_date < #{monthStartDate} "
	        + "AND customer_trans_type_id != 2 "
            + "AND reversed = biz_false")
	public String getTransactingMetersLastMonth(@Param("genGroupId") Long genGroupId,
			@Param("monthStartDate") Date monthStartDate,
			@Param("lastMonthStartDate") Date lastMonthStartDate);

	// ***
	// New Meters Installed
	@Select("SELECT count(DISTINCT up_meter_install.meter_id) "
			+ "FROM up_meter_install "
			+ "INNER JOIN usage_point "
			+ "ON up_meter_install.usage_point_id = usage_point.usage_point_id "
			+ "WHERE usage_point.gen_group_id = #{genGroupId} "
			+ "AND remove_date is null " 
			+ "AND install_date > #{todaysDate}")
	public String getNewMetersInstalledToday(@Param("genGroupId") Long genGroupId,
			@Param("todaysDate") Date todaysDate);

	@Select("SELECT count(DISTINCT up_meter_install.meter_id) "
			+ "FROM up_meter_install "
			+ "INNER JOIN usage_point "
			+ "ON up_meter_install.usage_point_id = usage_point.usage_point_id "
			+ "WHERE usage_point.gen_group_id = #{genGroupId} "
			+ "AND remove_date is null "
			+ "AND install_date > #{monthStartDate}")
	public String getNewMetersInstalledMonthToDate(	@Param("genGroupId") Long genGroupId,
			@Param("monthStartDate") Date monthStartDate);

	@Select("SELECT count(DISTINCT up_meter_install.meter_id) "
			+ "FROM up_meter_install "
			+ "INNER JOIN usage_point "
			+ "ON (up_meter_install.usage_point_id = usage_point.usage_point_id) "
			+ "WHERE usage_point.gen_group_id = #{genGroupId} "
			+ "AND remove_date is null "
			+ "AND install_date > #{lastMonthStartDate} "
			+ "AND install_date < #{monthStartDate}")
	public String getNewMetersInstalledLastMonth(@Param("genGroupId") Long genGroupId,
			@Param("monthStartDate") Date monthStartDate,
			@Param("lastMonthStartDate") Date lastMonthStartDate);

	// ***
	// New Active Meters Installed
	@Select("SELECT count(DISTINCT up_meter_install.meter_id) "
			+ "FROM up_meter_install "
			+ "INNER JOIN usage_point "
			+ "ON up_meter_install.usage_point_id = usage_point.usage_point_id "
			+ "WHERE usage_point.gen_group_id = #{genGroupId} "
			+ "AND remove_date is null " 
			+ "AND record_status = 'ACT'"
			+ "AND install_date > #{todaysDate}")
	public String getNewActiveMetersInstalledToday(	@Param("genGroupId") Long genGroupId,
			@Param("todaysDate") Date todaysDate);

	@Select("SELECT count(DISTINCT up_meter_install.meter_id) "
			+ "FROM up_meter_install "
			+ "INNER JOIN usage_point "
			+ "ON up_meter_install.usage_point_id = usage_point.usage_point_id "
			+ "WHERE usage_point.gen_group_id = #{genGroupId} "
			+ "AND remove_date is null " 
			+ "AND record_status = 'ACT'"
			+ "AND install_date > #{monthStartDate}")
	public String getNewActiveMetersInstalledMonthToDate(@Param("genGroupId") Long genGroupId,
			@Param("monthStartDate") Date monthStartDate);

	@Select("SELECT count(DISTINCT up_meter_install.meter_id) "
			+ "FROM up_meter_install "
			+ "INNER JOIN usage_point "
			+ "ON (up_meter_install.usage_point_id = usage_point.usage_point_id) "
			+ "WHERE usage_point.gen_group_id = #{genGroupId} "
			+ "AND remove_date is null " 
			+ "AND record_status = 'ACT'"
			+ "AND install_date > #{lastMonthStartDate} "
			+ "AND install_date < #{monthStartDate}")
	public String getNewActiveMetersInstalledLastMonth(	@Param("genGroupId") Long genGroupId,
			@Param("monthStartDate") Date monthStartDate,
			@Param("lastMonthStartDate") Date lastMonthStartDate);

	// ***
	// Usage Point Meter Total
	@Select("SELECT count(*) "
			+ "FROM usage_point "
			+ "WHERE meter_id is not null "
			+ "AND usage_point.gen_group_id = #{genGroupId} ")
	public String getUsagePointMeterTotalToday(	@Param("genGroupId") Long genGroupId);

	@Select("SELECT count(*) "
			+ "FROM usage_point "
			+ "WHERE meter_id is not null "
			+ "AND usage_point.gen_group_id = #{genGroupId} ")
	public String getUsagePointMeterTotalMonthToDate(@Param("genGroupId") Long genGroupId);

	@Select("SELECT count(DISTINCT up_meter_install.meter_id) "
			+ "FROM up_meter_install "
			+ "INNER JOIN usage_point "
			+ "ON (up_meter_install.usage_point_id = usage_point.usage_point_id) "
			+ "WHERE usage_point.gen_group_id = #{genGroupId} "
			+ "AND remove_date is null "
			+ "AND install_date < #{monthStartDate}")
	public String getUsagePointMeterTotalLastMonth(	@Param("genGroupId") Long genGroupId,
			@Param("monthStartDate") Date monthStartDate);

	// ***
	// Active Usage Point Meter Total
	@Select("SELECT count(*) "
			+ "FROM usage_point "
			+ "WHERE meter_id IS NOT null "
			+ "AND gen_group_id = #{genGroupId} "
			+ "AND record_status = 'ACT'")
	public String getActiveUsagePointMeterTotalToday(@Param("genGroupId") Long genGroupId);

	@Select("SELECT count(*) "
			+ "FROM usage_point "
			+ "WHERE meter_id IS NOT null "
			+ "AND gen_group_id = #{genGroupId} "
			+ "AND record_status = 'ACT'")
	public String getActiveUsagePointMeterTotalMonthToDate(	@Param("genGroupId") Long genGroupId);

	@Select("SELECT count(up_meter_install.meter_id) "
			+ "FROM up_meter_install "
			+ "INNER JOIN usage_point "
			+ "ON (up_meter_install.usage_point_id = usage_point.usage_point_id) "
			+ "WHERE usage_point.gen_group_id = #{genGroupId} "
			+ "AND record_status = 'ACT'" 
			+ "AND remove_date is null "
			+ "AND install_date < #{monthStartDate}")
	public String getActiveUsagePointMeterTotalLastMonth(@Param("genGroupId") Long genGroupId,
			@Param("monthStartDate") Date monthStartDate);

	// ***
	// Device Store Meters Total
	@Select("SELECT count(m.meter_id) "
			+ "FROM meter m " 
			+ "INNER JOIN end_device_store eds "
			+ "ON m.end_device_store_id = eds.end_device_store_id "
			+ "WHERE eds.gen_group_id = #{genGroupId} ")
	public String getDeviceStoreMetersTotalToday(@Param("genGroupId") Long genGroupId);

	@Select("SELECT count(m.meter_id) "
			+ "FROM meter m " 
			+ "INNER JOIN end_device_store eds "
			+ "ON m.end_device_store_id = eds.end_device_store_id "
			+ "WHERE eds.gen_group_id = #{genGroupId} ")
	public String getDeviceStoreMetersTotalMonthToDate(	@Param("genGroupId") Long genGroupId);

	// ***
	// Device Store Meters Total Last Month is calculated subtracting last
	// months usage point meters from last months total.
	// Total meter inserts since last month. Used for working out Device Store
	// Meters Total Last Month.
	@Select("SELECT count(*) "
			+ "FROM (SELECT m.meter_id "
			+ "FROM meter m "
			+ "LEFT JOIN  usage_point up "
			+ "ON m.meter_id = up.meter_id "
			+ "LEFT JOIN end_device_store eds "
			+ "ON m.end_device_store_id = eds.end_device_store_id "
			+ "WHERE up.gen_group_id = #{genGroupId} "
			+ "OR eds.gen_group_id =#{genGroupId}) temp "
			+ "INNER JOIN meter_hist mh "
			+ "ON mh.meter_id = temp.meter_id "
			+ "WHERE user_action = 'insert' "
			+ "AND mh.date_rec_modified > #{monthStartDate}")
	public Long getMetersAddedMonthToDate(@Param("genGroupId") Long genGroupId,
			@Param("monthStartDate") Date monthStartDate);

}
