package za.co.ipay.metermng.server.mybatis.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.session.RowBounds;

import za.co.ipay.metermng.mybatis.generated.model.Customer;
import za.co.ipay.metermng.mybatis.generated.model.CustomerAccount;
import za.co.ipay.metermng.mybatis.generated.model.CustomerAgreement;
import za.co.ipay.metermng.server.mybatis.model.CustomerWithUsagePointId;


public interface ICustomerSuggestionMapper {

    @Select("select * from customer where lower(surname) like #{surname}")
    @ResultMap("za.co.ipay.metermng.mybatis.generated.mapper.CustomerMapper.BaseResultMap")
    public List<Customer> findByLikeLowerSurname(@Param("surname") String surname, RowBounds rowbounds);

    @Select("select * from customer where lower(id_number) like #{idnumber}")
    @ResultMap("za.co.ipay.metermng.mybatis.generated.mapper.CustomerMapper.BaseResultMap")
    public List<Customer> findByLikeLowerIdNumber(@Param("idnumber") String idNumber, RowBounds rowbounds);

    @Select("select * from customer_agreement ca, customer c where ca.customer_id = c.customer_id and lower(ca.agreement_ref) like #{ref}")
    @ResultMap("za.co.ipay.metermng.mybatis.generated.mapper.CustomerAgreementMapper.BaseResultMap")
    public List<CustomerAgreement> findAgreementByLikeRef(@Param("ref") String agreementRef, RowBounds rowbounds);

    @Select("select * from customer_agreement ca, customer c where ca.customer_id = c.customer_id and c.gen_group_id = #{groupid} and lower(ca.agreement_ref) like #{ref}")
    @ResultMap("za.co.ipay.metermng.mybatis.generated.mapper.CustomerAgreementMapper.BaseResultMap")
    public List<CustomerAgreement> findAgreementByLikeRefAndGroup(@Param("ref") String agreementRef, @Param("groupid") Long genGroupId, RowBounds rowbounds);

    @Select("select c.*, u.usage_point_id, u.usage_point_name from customer c left join customer_agreement ca on c.customer_id = ca.customer_id left join usage_point u on ca.customer_agreement_id = u.customer_agreement_id where lower(c.surname) like #{surname}")
    @ResultMap("za.co.ipay.metermng.server.mybatis.mapper.ICustomerSuggestionMapper.ExtendedResultMap")
    public List<CustomerWithUsagePointId> findWithUPByLikeLowerSurname(@Param("surname") String surname, RowBounds rowbounds);

    @Select("select * from customer where gen_group_id = #{groupid} and lower(surname) like #{surname}")
    @ResultMap("za.co.ipay.metermng.mybatis.generated.mapper.CustomerMapper.BaseResultMap")
    public List<Customer> findByLikeLowerSurnameAndGroup(@Param("surname") String surname, @Param("groupid") Long genGroupId, RowBounds rowbounds);

    @Select("select * from customer where gen_group_id = #{groupid} and lower(id_number) like #{idnumber}")
    @ResultMap("za.co.ipay.metermng.mybatis.generated.mapper.CustomerMapper.BaseResultMap")
    public List<Customer> findByLikeLowerIdNumberAndGroup(@Param("idnumber") String idNumber, @Param("groupid") Long genGroupId, RowBounds rowbounds);

    @Select("select c.*, u.usage_point_id, u.usage_point_name from customer c left join customer_agreement ca on c.customer_id = ca.customer_id left join usage_point u on ca.customer_agreement_id = u.customer_agreement_id where c.gen_group_id = #{groupid} and lower(c.surname) like #{surname}")
    @ResultMap("za.co.ipay.metermng.server.mybatis.mapper.ICustomerSuggestionMapper.ExtendedResultMap")
    public List<CustomerWithUsagePointId> findWithUPByLikeLowerSurnameAndGroup(@Param("surname") String surname, @Param("groupid") Long genGroupId, RowBounds rowbounds);

    @Select("select * from customer_account ca, customer c where ca.customer_id = c.customer_id and lower(ca.account_name) like #{ref}")
    @ResultMap("za.co.ipay.metermng.mybatis.generated.mapper.CustomerAccountMapper.BaseResultMap")
    public List<CustomerAccount> findAccountByLikeName(@Param("ref") String accountName, RowBounds rowbounds);

    @Select("select * from customer_account ca, customer c where ca.customer_id = c.customer_id and c.gen_group_id = #{groupid} and lower(ca.account_name) like #{ref}")
    @ResultMap("za.co.ipay.metermng.mybatis.generated.mapper.CustomerAccountMapper.BaseResultMap")
    public List<CustomerAccount> findAccountByLikeNameAndGroup(@Param("ref") String accountName, @Param("groupid") Long genGroupId, RowBounds rowbounds);


    @Select("SELECT c.*, u.usage_point_id, u.usage_point_name, ca.customer_agreement_id, ca.agreement_ref FROM customer c" +
            " INNER JOIN customer_agreement ca ON ca.customer_id=c.customer_id" +
            " LEFT JOIN usage_point u ON ca.customer_agreement_id = u.customer_agreement_id" +
            " WHERE lower(c.id_number) LIKE #{idNumber}")
    @ResultMap("za.co.ipay.metermng.server.mybatis.mapper.ICustomerSuggestionMapper.ExtendedResultMap")
    List<CustomerWithUsagePointId> findWithUpByLikeLowerIdNumber(@Param("idNumber") String idNumber, RowBounds rowbounds);

    @Select("SELECT c.*, u.usage_point_id, u.usage_point_name, ca.customer_agreement_id, ca.agreement_ref FROM customer c" +
            " INNER JOIN customer_agreement ca ON ca.customer_id=c.customer_id" +
            " LEFT JOIN usage_point u ON ca.customer_agreement_id = u.customer_agreement_id" +
            " WHERE c.gen_group_id = #{groupId} AND lower(c.id_number) LIKE #{idNumber}")
    @ResultMap("za.co.ipay.metermng.server.mybatis.mapper.ICustomerSuggestionMapper.ExtendedResultMap")
    List<CustomerWithUsagePointId> findWithUpByLikeLowerIdNumberAndGroup(@Param("idNumber") String idNumber, @Param("groupId") Long genGroupId, RowBounds rowbounds);

    @Select("SELECT c.*, u.usage_point_id, u.usage_point_name, ca.customer_agreement_id, ca.agreement_ref FROM customer c" +
            " INNER JOIN customer_agreement ca ON ca.customer_id=c.customer_id" +
            " LEFT JOIN usage_point u ON ca.customer_agreement_id = u.customer_agreement_id" +
            " WHERE lower(ca.agreement_ref) LIKE #{agreementRef}")
    @ResultMap("za.co.ipay.metermng.server.mybatis.mapper.ICustomerSuggestionMapper.ExtendedResultMap")
    List<CustomerWithUsagePointId> findByLikeLowerAgreementRef(@Param("agreementRef") String agreementRef, RowBounds rowbounds);

    @Select("SELECT c.*, u.usage_point_id, u.usage_point_name, ca.customer_agreement_id, ca.agreement_ref FROM customer c" +
            " INNER JOIN customer_agreement ca ON ca.customer_id=c.customer_id" +
            " LEFT JOIN usage_point u ON ca.customer_agreement_id = u.customer_agreement_id" +
            " WHERE c.gen_group_id = #{groupId} AND lower(ca.agreement_ref) LIKE #{agreementRef}")
    @ResultMap("za.co.ipay.metermng.server.mybatis.mapper.ICustomerSuggestionMapper.ExtendedResultMap")
    List<CustomerWithUsagePointId> findByLikeLowerAgreementRefAndGroup(@Param("agreementRef") String agreementRef, @Param("groupId") Long genGroupId, RowBounds rowbounds);
}
