package za.co.ipay.metermng.server.mybatis.service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.ibatis.session.RowBounds;
import org.apache.log4j.Logger;
import org.springframework.context.support.DefaultMessageSourceResolvable;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;

import za.co.ipay.gwt.common.server.util.ExposedReloadableResourceBundleMessageSource;
import za.co.ipay.gwt.common.shared.exception.ServiceException;
import za.co.ipay.gwt.common.shared.exception.ValidationException;
import za.co.ipay.gwt.common.shared.exception.ValidationMessage;
import za.co.ipay.metermng.mybatis.custom.mapper.GroupEntityCustomMapper;
import za.co.ipay.metermng.mybatis.generated.mapper.AppSettingHistMapper;
import za.co.ipay.metermng.mybatis.generated.mapper.AppSettingMapper;
import za.co.ipay.metermng.mybatis.generated.model.AppSetting;
import za.co.ipay.metermng.mybatis.generated.model.AppSettingExample;
import za.co.ipay.metermng.mybatis.generated.model.AppSettingExample.Criteria;
import za.co.ipay.metermng.mybatis.generated.model.AppSettingHist;
import za.co.ipay.metermng.mybatis.generated.model.AppSettingHistExample;
import za.co.ipay.metermng.shared.MeterMngStatics;
import za.co.ipay.metermng.shared.appsettings.AppSettings;
import za.co.ipay.metermng.shared.appsettings.CustomFieldDto;
import za.co.ipay.metermng.shared.dto.FilterCriteria;
import za.co.ipay.metermng.shared.dto.FilterCriteria.Pair;
import za.co.ipay.metermng.shared.dto.FilterCriteria.Value;

public class AppSettingService {

    private AppSettingMapper appSettingMapper;
    private AppSettingHistMapper appSettingHistMapper;
    private GroupEntityCustomMapper groupEntityCustomMapper;
    private ExposedReloadableResourceBundleMessageSource messageSource;

    private static Logger logger = Logger.getLogger(AppSettingService.class);
    
    
    public void setAppSettingMapper(AppSettingMapper appSettingMapper) {
        this.appSettingMapper = appSettingMapper;
    }
    
    public void setAppSettingHistMapper(AppSettingHistMapper appSettingHistMapper) {
        this.appSettingHistMapper = appSettingHistMapper;
    }

    public void setMessageSource(ExposedReloadableResourceBundleMessageSource messageSource) {
        this.messageSource = messageSource;
    }

    public void setGroupEntityCustomMapper(GroupEntityCustomMapper groupEntityCustomMapper) {
        this.groupEntityCustomMapper = groupEntityCustomMapper;
    }
    //---------------------------------------------------------------------------------------------------
    @Transactional(readOnly=true)
    public AppSetting getAppSetting(Long appSettingId) {
        if (appSettingId != null) {
            return appSettingMapper.selectByPrimaryKey(appSettingId);
        } else {
            return null;
        }
    }
    
    @Transactional(readOnly = true)
    public Integer getAppSettingCount(FilterCriteria filterCriteria, String searchText) {
        Pair<String, Value> filterCriterion = null;
        if (filterCriteria != null) {
            filterCriterion = filterCriteria.getColumnValuesFilter();
        }
        return appSettingMapper.countByExample(createAppSettingExampleForSearch(searchText, filterCriterion));
    }

    private AppSettingExample createAppSettingExampleForSearch(String searchText, Pair<String, Value> filterCriterion) {
        AppSettingExample example = new AppSettingExample();
        Criteria initialCriteria = createAppSettingCriteria(example, filterCriterion);
        if (!searchText.isEmpty()) {
            searchText = "%" + searchText + "%";
            initialCriteria.andValueLikeInsensitive(searchText);
            example.or(createAppSettingCriteria(example, filterCriterion).andNameLikeInsensitive(searchText));
            example.or(createAppSettingCriteria(example, filterCriterion).andDescriptionLikeInsensitive(searchText));
        }
        return example;
    }

    private Criteria createAppSettingCriteria(AppSettingExample example, Pair<String, Value> filterCriterion) {
        Criteria criteria = example.createCriteria();
        setAndKeyCriteria(criteria, filterCriterion);
        return criteria;
    }

    @Transactional(readOnly = true)
    public ArrayList<AppSetting> getAppSettings(FilterCriteria filterCriteria, String searchText) {
        if (filterCriteria == null) {
            filterCriteria = FilterCriteria.createDefaultCriteria();
        }
        AppSettingExample example = createAppSettingExampleForSearch(searchText,
                filterCriteria.getColumnValuesFilter());
        example.setOrderByClause(getOrderColumn(filterCriteria.getSortField(), filterCriteria.isSortAscending()));
        return new ArrayList<>(appSettingMapper.selectByExampleWithRowbounds(example,
                new RowBounds(filterCriteria.getStartRow(), filterCriteria.getPageSize())));
    }

    private void setAndKeyCriteria(AppSettingExample.Criteria criteria, FilterCriteria.Pair<String, FilterCriteria.Value> filterCriterion){
        if(filterCriterion==null){
            return;
        }
        String column = filterCriterion.getKey();
        FilterCriteria.Value filterValue = filterCriterion.getValue();
        List<String> values = filterValue.getValues();
        String singleValue = filterValue.getValues().get(0);
        if (column.equals("key")) {
            switch (filterValue.getValueType()) {
                case IN:
                    criteria.andKeyIn(values);
                    break;
                case LIKE:
                    criteria.andKeyLike(singleValue);
                    break;
                case NOT_LIKE:
                    criteria.andKeyNotLike(singleValue);
                    break;
                case EQUAL:
                    criteria.andKeyEqualTo(singleValue);
                    break;
                case NOT_EQUAL:
                    criteria.andKeyNotEqualTo(singleValue);
                    break;
                case NOT_IN:
                    criteria.andKeyNotIn(values);
                    break;
            }
        }

    }
    
    @Transactional(readOnly=true)
    public ArrayList<AppSetting> getAllAppSettings() throws ServiceException {
        AppSettingExample example = new AppSettingExample();
        example.createCriteria().andIdIsNotNull();
        return new ArrayList<AppSetting>(appSettingMapper.selectByExample(example));
    }
    
    @Transactional(readOnly=true)
    public ArrayList<AppSetting> getAppSettingsForCustomFields() throws ServiceException {
        AppSettingExample example = new AppSettingExample();
        example.createCriteria().andKeyLike("%" + ".custom_" + "%");
        ArrayList<AppSetting> list = new ArrayList<AppSetting>(appSettingMapper.selectByExample(example));
        logger.info("AppSettingService: andKeyLike(%.custom_%): listSize=" + list.size());
        return list;
    }

	@Transactional(readOnly = true)
	public ArrayList<AppSetting> getAppSettingsForUPAndMeterAndCustomerCustomFields() throws ServiceException {
		AppSettingExample example = new AppSettingExample();
		example.createCriteria().andKeyLike("usagepoint.custom_" + "%");
		example.or(example.createCriteria().andKeyLike("customer.custom_" + "%"));
		example.or(example.createCriteria().andKeyLike("meter.custom_" + "%"));
		ArrayList<AppSetting> list = new ArrayList<AppSetting>(appSettingMapper.selectByExample(example));
		logger.info("AppSettingService: UsagePointAndCustomer: listSize=" + list.size());
		return list;
	}
    
    @Transactional(readOnly=true)
    public ArrayList<AppSetting> getAppSettingsForUsagePointGroupCustomFields() throws ServiceException {
        AppSettingExample example = new AppSettingExample();
        example.createCriteria().andKeyLike("%" + "usagepointgroup.custom_" + "%");
        ArrayList<AppSetting> list = new ArrayList<AppSetting>(appSettingMapper.selectByExample(example));
        logger.info("AppSettingService: andKeyLike(%usagepointgroup.custom_%): listSize=" + list.size());
        return list;
    }
    
    private String getOrderColumn(String sortField, boolean isAscending) {
        String orderColumn = "app_setting_name";
        if (sortField != null && !sortField.trim().equals("")) {
            if ("name".equals(sortField)) {
                orderColumn = "app_setting_name";
            } else if ("description".equals(sortField)) {
                orderColumn = "app_setting_description";
            } else if ("value".equals(sortField)) {
                orderColumn = "app_setting_value";
            }
                
        }
        return orderColumn + " " + getOrder(isAscending);
    }
    
    private String getOrder(boolean isAscending) {
        if (isAscending) {
            return "asc";
        } else {
            return "desc";
        }
    }
    
    @Transactional(readOnly=true)
    public AppSetting getAppSettingByName(String name) throws ValidationException, ServiceException {
        if (name != null && !name.trim().equals("")) {
            AppSettingExample example = new AppSettingExample();
            example.createCriteria().andNameEqualTo(name);
            List<AppSetting> appSettings = appSettingMapper.selectByExample(example);
            if (appSettings.isEmpty()) {
                return null;
            } else {
                return appSettings.get(0);
            }
        } else {
            return null;
        }
    }    
    
    @Transactional(readOnly=true)
    public AppSetting getAppSettingByKey(String key) throws ValidationException, ServiceException {
        if (key != null && !key.trim().equals("")) {
            AppSettingExample example = new AppSettingExample();
            example.createCriteria().andKeyEqualTo(key);
            List<AppSetting> appSettings = appSettingMapper.selectByExample(example);
            if (appSettings.isEmpty()) {
                return null;
            } else {
                return appSettings.get(0);
            }
        } else {
            return null;
        }
    }

    @Transactional
    @PreAuthorize("hasRole('mm_app_settings_admin')")
    public void saveAppSetting(AppSetting appSetting) throws ValidationException, ServiceException {
        AppSetting existing = (AppSetting) getAppSettingByName(appSetting.getName());
        if (existing != null && !existing.getId().equals(appSetting.getId())) {
            throw new ValidationException(new ValidationMessage("appsetting.name.duplicate", new String[]{appSetting.getName()}, true));
        }
        
        if (appSetting.getId() == null) {
                throw new ValidationException(new ValidationMessage("appsetting.error.new", new String[]{appSetting.getName()}, true));
        } else {
            String errorMsg = null;
            if (appSetting.getKey().equals(AppSettings.CUSTOMER_ACCOUNT_DISCONNECT_THRESHOLD) 
                    || appSetting.getKey().equals(AppSettings.CUSTOMER_ACCOUNT_RECONNECT_THRESHOLD)
                    || appSetting.getKey().equals(AppSettings.CUSTOMER_ACCOUNT_EMERGENCY_CREDIT_THRESHOLD)
                    || appSetting.getKey().equals(AppSettings.CUSTOMER_ACCOUNT_LOW_BALANCE_THRESHOLD)) {
                errorMsg = crossValidateConnectionSettings(appSetting);
            } 
            
            if (appSetting.getKey().equals(AppSettings.VENDING_MAX_VEND_AMT) || appSetting.getKey().equals(AppSettings.VENDING_MIN_VEND_AMT)) {
                errorMsg = crossValidateMinMaxSettings(appSetting);
            }
            
            if (appSetting.getKey().toLowerCase().contains(".custom_") && appSetting.getKey().toLowerCase().contains(".status")) {
                if (appSetting.getValue().toLowerCase().equals(messageSource.getMessage(new DefaultMessageSourceResolvable("user.custom.field.status.optional"), null).toLowerCase()) 
                        || appSetting.getValue().toLowerCase().equals(messageSource.getMessage(new DefaultMessageSourceResolvable("user.custom.field.status.required"), null).toLowerCase())
                        || appSetting.getValue().toLowerCase().equals(messageSource.getMessage(new DefaultMessageSourceResolvable("user.custom.field.status.unavailable"), null).toLowerCase())) {
                    appSetting.setValue(appSetting.getValue().toUpperCase());
                } else {
                    errorMsg = "appsetting.error.invalid.custom.status";
                }
            }
            
            if (errorMsg == null || errorMsg.trim().isEmpty()) {
                if (appSettingMapper.updateByPrimaryKey(appSetting) != 1) {
                    throw new ValidationException(new ValidationMessage("error.save", new String[]{"appsetting.name"}, true));
                }
            } else {
                throw new ValidationException(new ValidationMessage(errorMsg, true));
            }
        }
    }

    @Transactional
    @PreAuthorize("hasRole('mm_app_settings_admin')")
    public void saveAppSettingAndClearCustomField(AppSetting appSetting, int groupEntityCustomFieldNumber) throws ValidationException, ServiceException {
        saveAppSetting(appSetting);
        switch (groupEntityCustomFieldNumber) {
            case 1:
                groupEntityCustomMapper.clearCustomField1();
                break;
            case 2:
                groupEntityCustomMapper.clearCustomField2();
                break;
            case 3:
                groupEntityCustomMapper.clearCustomField3();
                break;
            case 4:
                groupEntityCustomMapper.clearCustomField4();
                break;
            case 5:
                groupEntityCustomMapper.clearCustomField5();
                break;
            case 6:
                groupEntityCustomMapper.clearCustomField6();
                break;
            case 7:
                groupEntityCustomMapper.clearCustomField7();
                break;
            case 8:
                groupEntityCustomMapper.clearCustomField8();
                break;
            case 9:
                groupEntityCustomMapper.clearCustomField9();
                break;
            case 10:
                groupEntityCustomMapper.clearCustomField10();
                break;
        }
    }

    @Transactional
    public String crossValidateConnectionSettings(AppSetting appSetting) throws ValidationException, ServiceException {
        String errorMsg = null;
        
        AppSetting disconnectSetting = null;
        AppSetting reconnectSetting = null;
        AppSetting emergencyCreditSetting = null;
        AppSetting lowBalanceSetting = null;
        
        if (appSetting.getKey().equals(AppSettings.CUSTOMER_ACCOUNT_DISCONNECT_THRESHOLD)) {
            disconnectSetting = appSetting;
        } else if (appSetting.getKey().equals(AppSettings.CUSTOMER_ACCOUNT_RECONNECT_THRESHOLD)) {
            reconnectSetting = appSetting;
        } else if (appSetting.getKey().equals(AppSettings.CUSTOMER_ACCOUNT_EMERGENCY_CREDIT_THRESHOLD)) {
            emergencyCreditSetting = appSetting;
        } else if (appSetting.getKey().equals(AppSettings.CUSTOMER_ACCOUNT_LOW_BALANCE_THRESHOLD)) {
            lowBalanceSetting = appSetting;
        }
        
        if (disconnectSetting != null) {
            reconnectSetting = getReconnectSetting();
            errorMsg = crossCheckReconnect(disconnectSetting, reconnectSetting);
            emergencyCreditSetting = getEmergencyCreditSetting();
            errorMsg = crossCheckEmergencyCredit(errorMsg, disconnectSetting, emergencyCreditSetting, null);
        } else if (reconnectSetting != null) {
            disconnectSetting= getDisconnectSetting();
            errorMsg = crossCheckReconnect(disconnectSetting, reconnectSetting);
        } else if (emergencyCreditSetting != null) {
            disconnectSetting= getDisconnectSetting();
            lowBalanceSetting = getLowBalanceSetting();
            errorMsg = crossCheckEmergencyCredit(null, disconnectSetting, emergencyCreditSetting, lowBalanceSetting);
        } else if (lowBalanceSetting != null) {
            emergencyCreditSetting = getEmergencyCreditSetting();
            errorMsg = crossCheckLowBalance(null, lowBalanceSetting, emergencyCreditSetting);
        }

        return errorMsg;
    }
    @Transactional
    public AppSetting getDisconnectSetting() throws ValidationException, ServiceException {
        AppSetting disconnectSetting = getAppSettingByKey(AppSettings.CUSTOMER_ACCOUNT_DISCONNECT_THRESHOLD);
        if (disconnectSetting == null) {
            throw new ValidationException(new ValidationMessage("appsetting.error.new", new String[]{AppSettings.CUSTOMER_ACCOUNT_DISCONNECT_THRESHOLD}, true));
        }    
        return disconnectSetting;    
    }
    @Transactional
    public AppSetting getReconnectSetting() throws ValidationException, ServiceException {
        AppSetting reconnectSetting = getAppSettingByKey(AppSettings.CUSTOMER_ACCOUNT_RECONNECT_THRESHOLD);
        if (reconnectSetting == null) {
            throw new ValidationException(new ValidationMessage("appsetting.error.new", new String[]{AppSettings.CUSTOMER_ACCOUNT_RECONNECT_THRESHOLD}, true));
        }
        return reconnectSetting;
    }
    @Transactional
    public AppSetting getEmergencyCreditSetting() throws ValidationException, ServiceException {
        AppSetting emergencyCreditSetting = getAppSettingByKey(AppSettings.CUSTOMER_ACCOUNT_EMERGENCY_CREDIT_THRESHOLD);
        if (emergencyCreditSetting == null) {
            throw new ValidationException(new ValidationMessage("appsetting.error.new", new String[]{AppSettings.CUSTOMER_ACCOUNT_EMERGENCY_CREDIT_THRESHOLD}, true));
        }
        return emergencyCreditSetting;
    }
    @Transactional
    public AppSetting getLowBalanceSetting() throws ValidationException, ServiceException {
        AppSetting lowBalanceSetting = getAppSettingByKey(AppSettings.CUSTOMER_ACCOUNT_LOW_BALANCE_THRESHOLD);
        if (lowBalanceSetting == null) {
            throw new ValidationException(new ValidationMessage("appsetting.error.new", new String[]{AppSettings.CUSTOMER_ACCOUNT_LOW_BALANCE_THRESHOLD}, true));
        }
        return lowBalanceSetting;
    }
    @Transactional
    public String crossCheckReconnect(AppSetting disconnectSetting, AppSetting reconnectSetting){
        if ((new BigDecimal(disconnectSetting.getValue())).compareTo(new BigDecimal(reconnectSetting.getValue())) > 0)  {
            return "appsetting.error.disconnect.greater.reconnect";
        }
        return null;
    }
    @Transactional
    public String crossCheckEmergencyCredit(String errorMsg, AppSetting disconnectSetting,
                                                AppSetting emergencyCreditSetting, AppSetting lowBalanceSetting) {
        if ((new BigDecimal(disconnectSetting.getValue())).compareTo(new BigDecimal(emergencyCreditSetting.getValue())) > 0)  {
            if (errorMsg == null) {
                errorMsg = "appsetting.error.disconnect.greater.emergency.credit";
            } else {
                errorMsg = "appsetting.error.disconnect.greater.both";
            }
        }
        if (lowBalanceSetting != null) {
            errorMsg = crossCheckLowBalance(errorMsg, lowBalanceSetting, emergencyCreditSetting); 
        }
        return errorMsg;
    }
    @Transactional
    public String crossCheckLowBalance(String errorMsg, AppSetting lowBalanceSetting, AppSetting emergencyCreditSetting) {
        if ((new BigDecimal(emergencyCreditSetting.getValue())).compareTo(new BigDecimal(lowBalanceSetting.getValue())) > 0)  {
            if (errorMsg == null) {
                errorMsg = "appsetting.error.emergency.credit.greater.low.balance";
            } else {
                errorMsg = "appsetting.error.emergency.credit.errors";      // this should never happen!
            }
        }
        return errorMsg;
    }
    
    @Transactional
    public String crossValidateMinMaxSettings(AppSetting appSetting) throws ValidationException, ServiceException {
        AppSetting maxVendSetting = null;
        AppSetting minVendSetting = null;

        if (appSetting.getKey().equals(AppSettings.VENDING_MAX_VEND_AMT)) {
            maxVendSetting = appSetting;
        } else if (appSetting.getKey().equals(AppSettings.VENDING_MIN_VEND_AMT)) {
            minVendSetting = appSetting;
        }

        if (maxVendSetting != null) {
            minVendSetting = getMinVendSetting();
        } else if (minVendSetting != null) {
            maxVendSetting = getMaxVendSetting();
        }

        if (maxVendSetting.getValue() != null && minVendSetting.getValue() != null &&
                !maxVendSetting.getValue().isEmpty() && !minVendSetting.getValue().isEmpty()) {
            if ((new BigDecimal(maxVendSetting.getValue())).compareTo(new BigDecimal(minVendSetting.getValue())) == -1)   {
                return "appsetting.error.maxvend.smaller.minvend";
            }
        }
        return null;
    }
    @Transactional
    public AppSetting getMaxVendSetting() throws ValidationException, ServiceException {
        AppSetting maxVendSetting = getAppSettingByKey(AppSettings.VENDING_MAX_VEND_AMT);
        if (maxVendSetting == null) {
            throw new ValidationException(new ValidationMessage("appsetting.error.new", new String[]{AppSettings.VENDING_MAX_VEND_AMT}, true));
        }    
        return maxVendSetting;    
    }
    @Transactional
    public AppSetting getMinVendSetting() throws ValidationException, ServiceException {
        AppSetting minVendSetting = getAppSettingByKey(AppSettings.VENDING_MIN_VEND_AMT);
        if (minVendSetting == null) {
            throw new ValidationException(new ValidationMessage("appsetting.error.new", new String[]{AppSettings.VENDING_MIN_VEND_AMT}, true));
        }
        return minVendSetting;
    }
 
    @Transactional
    public Map<String, CustomFieldDto> getAppSettingsCustomFieldsHistoryVisibility(Map<String, CustomFieldDto> customFieldsHistoryRequiredMap) throws ServiceException {
        
        for (CustomFieldDto dto : customFieldsHistoryRequiredMap.values()) {
            if (!dto.isVisible()) {
                AppSettingHistExample ex = new AppSettingHistExample();
                ex.createCriteria().andIdEqualTo(dto.getStatusId());
                List<AppSettingHist> appSettingHistList = appSettingHistMapper.selectByExample(ex);
                if (!appSettingHistList.isEmpty()) {
                    dto.setVisible(true);
                } 
            }
        }
        return customFieldsHistoryRequiredMap;
    }
    
    @Transactional(readOnly = true)
    public Map<String, AppSetting> getAppSettingsByKeys(List<String> keys) {
        if (keys != null && !keys.isEmpty()) {
            AppSettingExample example = new AppSettingExample();
            example.createCriteria().andKeyIn(keys);
            List<AppSetting> appSettings = appSettingMapper.selectByExample(example);
            if (appSettings.isEmpty()) {
                return null;
            } else {
                Map<String, AppSetting> appSettingsMap = new HashMap<String, AppSetting>();
                for (AppSetting appSetting : appSettings) {
                    appSettingsMap.put(appSetting.getKey(), appSetting);
                }
                return appSettingsMap;
            }
        } else {
            return null;
        }
    }
    
    @Transactional(readOnly = true)    
    public Map<String, String> getFromEmailAppSettingDetails() {
        Map<String, String> fromEmailDetailsMap = new HashMap<>();
        AppSettingExample appSettingExample = new AppSettingExample();
        appSettingExample.createCriteria().andKeyIn(Arrays.asList(MeterMngStatics.APP_SETTING_FROM_NAME_KEY, 
                MeterMngStatics.APP_SETTING_FROM_EMAIL_KEY));
        appSettingMapper.selectByExample(appSettingExample).forEach(appSetting -> {
            String value = appSetting.getValue();
            switch (appSetting.getKey()) {
            case MeterMngStatics.APP_SETTING_FROM_NAME_KEY:
                fromEmailDetailsMap.put("fromName", value);
                break;
            case MeterMngStatics.APP_SETTING_FROM_EMAIL_KEY:
                fromEmailDetailsMap.put("fromEmail", value);
                break;    
             }
         });

        if (fromEmailDetailsMap.get("fromName") == null) {
            fromEmailDetailsMap.put("fromName", "iPay (Pty) Ltd");
        }
        if (fromEmailDetailsMap.get("fromEmail") == null) {
            fromEmailDetailsMap.put("fromEmail", "<EMAIL>");
        }
        return fromEmailDetailsMap;
    }
}
