package za.co.ipay.metermng.server.rpc;

import java.util.ArrayList;

import org.apache.log4j.Logger;

import za.co.ipay.gwt.common.shared.exception.ServiceException;
import za.co.ipay.gwt.common.shared.exception.ValidationException;
import za.co.ipay.metermng.client.rpc.SalesPerResourceRpc;
import za.co.ipay.metermng.server.mybatis.service.SalesPerResourceService;
import za.co.ipay.metermng.shared.dto.dashboard.SalesPerResourceContainerDto;
import za.co.ipay.metermng.shared.dto.dashboard.SalesPerResourceDto;

public class SalesPerResourceRpcImpl extends BaseMeterMngRpc implements SalesPerResourceRpc {

    private static final long serialVersionUID = 1L;

    private SalesPerResourceService salesPerResourceService;

    public SalesPerResourceRpcImpl() {
        logger = Logger.getLogger(SalesPerResourceRpcImpl.class);
    }

    public void setSalesPerResourceService(SalesPerResourceService salesPerResourceService) {
        this.salesPerResourceService = salesPerResourceService;
    }

    @Override
    public ArrayList<SalesPerResourceContainerDto> getSalesPerResource()
            throws ValidationException, ServiceException {

        return  salesPerResourceService.getSalesPerResource(getUser().getCurrentGroupId());
     }
}
