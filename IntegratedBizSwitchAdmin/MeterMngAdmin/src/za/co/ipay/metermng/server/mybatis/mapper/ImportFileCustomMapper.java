package za.co.ipay.metermng.server.mybatis.mapper;

import java.util.Date;
import java.util.List;

import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.session.RowBounds;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.Select;

import za.co.ipay.metermng.mybatis.generated.model.ImportFileItem;
import za.co.ipay.metermng.shared.dto.importfile.ImportFileDto;

/**
 * ImportFileCustomMapper provides custom select methods to retrieve corresponding search results for the specified search criteria.
 * <AUTHOR>
 */
public interface ImportFileCustomMapper {
    
    //ImportFile
    @SelectProvider(type=ImportFileQueryBuilder.class, method="getImportFilesCount")
    public int getImportFilesCount(@Param("sortColumn") String sortColumn, @Param("filterColumn") String filterColumn,
            @Param("filterString") String filterString, @Param("filterDate") Date filterDate,
            @Param("order") String order, @Param("importfileTypeIds") String[] importfileTypeIds,
            @Param("blockingPermission") Boolean blockingPermission);
                                                                
    @SelectProvider(type=ImportFileQueryBuilder.class, method="selectImportFiles")
    @Results(value = {
            @Result(column = "import_file_id", property="id"),
            @Result(column = "import_file_type_id", property="importFileTypeId"),
            @Result(column = "import_folder", property="importFolder"),
            @Result(column = "import_filename", property="importFilename"),
            @Result(column = "upload_username", property="uploadUsername"),
            @Result(column = "upload_start", property="uploadStart"),
            @Result(column = "upload_end", property="uploadEnd"),
            @Result(column = "num_items", property="numItems"),
            @Result(column = "last_import_username", property="lastImportUsername"),
            @Result(column = "last_import_start", property="lastImportStart"),
            @Result(column = "last_import_end", property="lastImportEnd"),
            @Result(column = "last_import_type_all", property="lastImportTypeAll"),
            @Result(column = "stop_import", property="stopImport"),
            @Result(column = "last_stop_import", property="lastStopImport"),
            @Result(column = "action_params", property="actionParams"),
            @Result(column = "file_type_name", property="importFileTypeName"),
            @Result(column = "file_type_class", property="importFileTypeClass"),
            @Result(column = "allow_items_multiple_import", property="allowItemsMultipleImport"),
            @Result(column = "has_action_params", property="hasActionParams"),
            @Result(column = "bulk_ref", property="bulkRef"),
            @Result(column = "access_group_id", property="accessGroupId")
    })
    public List<ImportFileDto> selectImportFiles(@Param("sortColumn") String sortColumn,
            @Param("filterColumn") String filterColumn, @Param("filterString") String filterString,
            @Param("filterDate") Date filterDate, @Param("order") String order, RowBounds rowBounds,
            @Param("importfileTypeIds") String[] importfileTypeIds,
            @Param("blockingPermission") Boolean blockingPermission);
                            
    //------------------------------------------------------------------------------------------------------------
    //ImportFileItems
    @SelectProvider(type=ImportFileQueryBuilder.class, method="getImportFileItemsCount")
    public int getImportFileItemsCount(@Param("importFileId") Long importFileId,
                                       @Param("sortColumn") String sortColumn, 
                                       @Param("filterColumn") String filterColumn,
                                       @Param("filterString") String filterString, 
                                       @Param("filterDate") Date filterDate,
                                       @Param("order") String order);
                                                                
    @SelectProvider(type=ImportFileQueryBuilder.class, method="selectImportFileItems")
    @ResultMap("za.co.ipay.metermng.mybatis.generated.mapper.ImportFileItemMapper.BaseResultMap")
    public List<ImportFileItem> selectImportFileItems(@Param("importFileId") Long importFileId,
                                                  @Param("sortColumn") String sortColumn, 
                                                  @Param("filterColumn") String filterColumn,
                                                  @Param("filterString") String filterString, 
                                                  @Param("filterDate") Date filterDate,
                                                  @Param("order") String order, 
                                                  RowBounds rowBounds);
    
    // Count successfully imported for the file
    @Select("select count(*) from import_file_item where import_file_id = #{0} and last_import_successful = biz_true")
    public Long getCountSuccessfulImports(Long importFileId);

  //------------------------------------------------------------------------------------------------------------
}
