package za.co.ipay.metermng.server.rpc;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.ArrayList;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Properties;
import java.util.TimeZone;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import org.apache.log4j.Logger;
import org.springframework.context.MessageSource;
import org.springframework.context.support.DelegatingMessageSource;
import za.co.ipay.gwt.common.server.rpc.TranslationRpcImpl;
import za.co.ipay.gwt.common.server.util.ExposedReloadableResourceBundleMessageSource;
import za.co.ipay.gwt.common.shared.exception.ServiceException;
import za.co.ipay.gwt.common.shared.message.MessageBundleData;
import za.co.ipay.metermng.client.rpc.MeterMngAppRpc;
import za.co.ipay.metermng.mybatis.generated.model.AppSetting;
import za.co.ipay.metermng.server.mybatis.service.AppSettingService;
import za.co.ipay.metermng.shared.appsettings.AppSettings;
import za.co.ipay.metermng.shared.dto.MeterMngAppData;
import za.co.ipay.metermng.shared.dto.message.MessagesDto;
import za.co.ipay.metermng.shared.dto.time.TimeZoneData;
import za.co.ipay.metermng.shared.util.MeterMngConfig;
import za.co.ipay.utils.manifest.ManifestUtils;

public class MeterMngAppRpcImpl extends TranslationRpcImpl implements MeterMngAppRpc {

    private static final long serialVersionUID = 1L;
    public static final String RELEASE = ManifestUtils.getImplementationVersion(MeterMngAppRpcImpl.class);

    private MessageSource formatSource;
    private ExposedReloadableResourceBundleMessageSource formatsMs;
    
    private MeterMngConfig meterMngConfig;    
    
    private MessageSource timezoneSource;
    private ExposedReloadableResourceBundleMessageSource timezonesMs;
    private AppSettingService appSettingService;
    
    private static Logger logger = Logger.getLogger(MeterMngAppRpcImpl.class);
    
    @Override
    public MeterMngAppData getConfigAndMessages(String localeName) throws ServiceException {
        MeterMngAppData appData = new MeterMngAppData();
        appData.setMeterMngConfig(meterMngConfig);
        appData.setMessages(getMessagesAndFormats(localeName));
        appData.setVersion(RELEASE);
        return appData;
    }
    
    private MessagesDto getMessagesAndFormats(String localeName) throws ServiceException {
        MessagesDto messagesDto = new MessagesDto();        
        messagesDto.setMessages(loadMessages(localeName));
        messagesDto.setFormats(loadFormats(localeName));
        return messagesDto;
    }
    
    private MessageBundleData loadFormats(String localeName) throws ServiceException {
        Map<String, String> messages = new HashMap<String, String>();
        Locale locale = null;
        if (useDefaultLocaleOnly) {
            locale = parseLocale(defaultLocaleName);
            logger.info("Got useDefaultLocaleOnly locale using defaultLocaleName:"+defaultLocaleName);   
        } else {
            locale = parseLocale(localeName);
            logger.info("Got locale using localeName:"+localeName);
        }
        
        logger.info("Using locale: "+locale.toString()+" to load formats...");
                
        if (formatsMs != null) {            
            Properties props = formatsMs.getProperties(locale);
            Enumeration<?> names = props.propertyNames();
            String name = "";
            String value = "";
            while(names.hasMoreElements()) {
                name = (String) names.nextElement();
                value = props.getProperty(name);
                messages.put(name, value);
                logger.debug("Loaded format: "+name+"="+value);
            }            
        } else {
            logger.error("No current messageSource set to load formats from: "+formatSource);
        }
        
        return new MessageBundleData(locale.toString(), messages);
    }
    
    @Override
    public List<TimeZoneData> getTimeZones(String localeName) throws ServiceException {
        List<TimeZoneData> timezones = new ArrayList<TimeZoneData>();
        Locale locale = null;
        if (useDefaultLocaleOnly) {
            locale = parseLocale(defaultLocaleName);
            logger.info("Got useDefaultLocaleOnly locale using defaultLocaleName:"+defaultLocaleName);   
        } else {
            locale = parseLocale(localeName);
            logger.info("Got locale using localeName:"+localeName);
        }
        
        logger.info("Using locale: "+locale.toString()+" to load timezones...");
                
        if (timezonesMs != null) {            
            Properties props = timezonesMs.getProperties(locale);
            String namesString = props.getProperty("timezone.names");
            if (namesString != null && !namesString.trim().equals("")) {
                String[] names = namesString.split("\\,");
                if (names != null && names.length > 0) {
                    for(String name : names) {
                        name = name.trim();
                        String displayName = props.getProperty(name+".name");
                        String json = props.getProperty(name+".json");
                        if (displayName != null && !displayName.trim().equals("")
                                && json != null && !json.trim().equals("")) {
                            timezones.add(new TimeZoneData(name, displayName, json));
                        }
                    }
                }
            }
        } else {
            logger.error("No current messageSource set to load timezones from: "+timezoneSource);
        }
        
        return timezones;
    }

    @Override
    public String getServerTimeZoneJson() throws ServiceException {
        AppSetting appSetting = appSettingService.getAppSettingByKey(AppSettings.ENABLE_SERVER_TIMEZONE);
        if (appSetting != null && appSetting.getValue().equalsIgnoreCase("true")) {
            InputStream inputStream = null;
            InputStreamReader inputStreamReader = null;
            BufferedReader bufferedReader = null;
            try {
                inputStream = getClass().getResourceAsStream("/TimeZoneConstants.properties");
                inputStreamReader = new InputStreamReader(inputStream);
                bufferedReader = new BufferedReader(inputStreamReader);
                for (String s; (s = bufferedReader.readLine()) != null; ) {
                    Pattern pattern = Pattern.compile("^[A-Za-z]+ = (.*\"id\": \"([A-Za-z_/]+)\".*)$");
                    Matcher matcher = pattern.matcher(s);
                    if (matcher.matches()) {
                        String id = matcher.group(2);
                        if (id.equals(TimeZone.getDefault().getID())) {
                            return matcher.group(1);
                        }
                    }
                }
            } catch (Exception e) {
                throw new ServiceException(e.getMessage());
            } finally {
                try {
                    if (bufferedReader != null)
                        bufferedReader.close();
                    if (inputStreamReader != null)
                        inputStreamReader.close();
                    if (inputStream != null)
                        inputStream.close();
                } catch (IOException ioException) {
                    logger.error("Exception closing streams");
                }
            }
        }
        return null;
    }

    public void setFormatSource(MessageSource formatSource) {
        this.formatSource = formatSource;
        if (formatSource instanceof ExposedReloadableResourceBundleMessageSource) {
            formatsMs = (ExposedReloadableResourceBundleMessageSource) formatSource;
        } else if (formatSource instanceof DelegatingMessageSource) {
            DelegatingMessageSource dms = (DelegatingMessageSource) formatSource;
            logger.info("Got delegating messagesource:" + dms.getParentMessageSource());
            if (dms.getParentMessageSource() instanceof ExposedReloadableResourceBundleMessageSource) {
                formatsMs = (ExposedReloadableResourceBundleMessageSource) dms.getParentMessageSource();
            } else {
                logger.error("No expected MessageSource instance was found for the format messages");
            }
        } 
    }
    
    public void setTimezoneSource(MessageSource timezoneSource) {
        this.timezoneSource = timezoneSource;
        if (timezoneSource instanceof ExposedReloadableResourceBundleMessageSource) {
            timezonesMs = (ExposedReloadableResourceBundleMessageSource) timezoneSource;
        } else if (timezoneSource instanceof DelegatingMessageSource) {
            DelegatingMessageSource dms = (DelegatingMessageSource) timezoneSource;
            if (dms.getParentMessageSource() instanceof ExposedReloadableResourceBundleMessageSource) {
                timezonesMs = (ExposedReloadableResourceBundleMessageSource) dms.getParentMessageSource();
            } else {
                logger.error("No expected MessageSource instance was found for the timezone messages");
            }
        } 
    }
    
    public void setMeterMngConfig(MeterMngConfig meterMngConfig) {
        this.meterMngConfig = meterMngConfig;
    }

    public void setAppSettingService(AppSettingService appSettingService) {
        this.appSettingService = appSettingService;
    }
}