package za.co.ipay.metermng.server.servlet;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;

import javax.servlet.ServletConfig;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.ibatis.session.RowBounds;
import org.apache.log4j.Logger;

import org.springframework.context.ApplicationContext;
import org.springframework.context.support.DefaultMessageSourceResolvable;
import org.springframework.web.context.WebApplicationContext;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;

import za.co.ipay.metermng.client.rpc.SearchRpc;
import za.co.ipay.metermng.server.rpc.SearchRpcImpl;
import za.co.ipay.metermng.shared.MeterMngStatics;
import za.co.ipay.metermng.shared.dto.search.SearchData;
import za.co.ipay.metermng.shared.dto.search.SearchResultData;
import za.co.ipay.metermng.shared.dto.user.MeterMngUser;
import za.co.ipay.utils.files.CsvWriter;

/**
 * AdvancedSearchExportDataServlet is used to export advanced search results.
 * The currently supported file format is CSV. 
 */
public class AdvancedSearchExportDataServlet extends SuperExportDataServlet {

    private static final long serialVersionUID = 1L;
    private static Logger logger = Logger.getLogger(AdvancedSearchExportDataServlet.class.getName());

    private DateFormat filednameDate = new SimpleDateFormat("yyMMdd_HHmmss");



    private String criteria;
    private Locale requestLocale;
    private SearchRpc searchRpc;
    SearchData searchCriteria;

    @Override
    public void init(ServletConfig config) throws ServletException {
        super.init(config);
        ApplicationContext ac = (ApplicationContext) config.getServletContext().getAttribute(WebApplicationContext.ROOT_WEB_APPLICATION_CONTEXT_ATTRIBUTE);
        this.searchRpc = (SearchRpcImpl) ac.getBean("searchRpc");
        if (messageSource == null || formatSource == null || searchRpc ==null) {
            throw new ServletException("Missing beans not set in the AdvancedSearchExportDataServlet: "
                    + messageSource + " " + formatSource);
        }
    }

    public void doPost(HttpServletRequest request, HttpServletResponse response) throws IOException, ServletException {
        doDataExport(request, response);
    }

    public void doGet(HttpServletRequest request, HttpServletResponse response) throws IOException, ServletException {
        doDataExport(request, response);
    }

    public void doDataExport(HttpServletRequest request, HttpServletResponse response) throws IOException {
        requestLocale = request.getLocale();
        criteria = request.getParameter(MeterMngStatics.EXPORT_FILTER_VALUE);
        try {
            CsvWriter csvContents = null;
            MeterMngUser user = getCurrentUser(request);
            if (user != null) {
                String type = request.getParameter(MeterMngStatics.EXPORT_TYPE);
                String filename = getExportFileName(request);

                if (type.equals(MeterMngStatics.EXPORT_ADVANCED_SEARCH)) {
                    csvContents = buildAdvancedSearchData();
                }

                if (csvContents == null) {
                    writeError(response, messageSource.getMessage(new DefaultMessageSourceResolvable("export.error.nodata"), requestLocale));
                } else {
                    logger.info("Writing csv content...");
                    InputStream inputStream = new ByteArrayInputStream(csvContents.getCSVContents().getBytes());
                    response.setContentType("text/csv");
                    response.setHeader("Content-Disposition", "attachment; fileName=\"" + filename + "\"");
                    response.setHeader("content-Length", String.valueOf(stream(inputStream, response.getOutputStream())));
                    logger.info("Completed export");
                }
            } else {
                logger.error("No session/logged in user found.");
                writeError(response, messageSource.getMessage(new DefaultMessageSourceResolvable("export.denied"), requestLocale));
            }
        } catch (Exception e) {
            logger.error("Error exporting data:", e);
            writeError(response, messageSource.getMessage(new DefaultMessageSourceResolvable("export.error"), requestLocale));
        }
    }
    
    private CsvWriter buildAdvancedSearchData(){
        try {
            List<ArrayList<String>> finalList = new ArrayList<ArrayList<String>>();
            SearchData searchData= new SearchData();
            ObjectMapper objectMapper = new ObjectMapper();
            HashMap<String, Object> mapFromJson = objectMapper.readValue(criteria, new TypeReference<HashMap<String,Object>>(){});
            
            for (Map.Entry<String,Object> entry : mapFromJson.entrySet()) {
                String k = entry.getKey();
                Object v =  entry.getValue();
                searchData.addCriteria(k, v);
            }
            
            searchData.setPageSize(RowBounds.NO_ROW_LIMIT);
            SearchData searchDataResults = searchRpc.doAdvancedSearch(searchData);
            
            for(SearchResultData result : searchDataResults.getResults()) {
                ArrayList<String> exportDataRow = new ArrayList<String>();
                if(result.getDetails().get("meter.number") != null) {
                    exportDataRow.add((String) result.getDetails().get("meter.number"));
                    finalList.add(exportDataRow);
                }
            }
            return ExportDataUtil.getAdvancedSearchCsv(messageSource, formatSource, requestLocale, finalList);
        } catch (IOException e) {
            logger.error("Error getting export data:", e);
            return null;
        }
    }

    public String getExportFileName(HttpServletRequest request) {
        StringBuilder sb = new StringBuilder();
        sb.append(ExportDataUtil.getFileName(request.getParameter(MeterMngStatics.EXPORT_FILE_NAME_PREFIX)));
        sb.append("_");
        sb.append(filednameDate.format(new Date()));
        sb.append(".csv");
        return sb.toString();
    }

}
