package za.co.ipay.metermng.server.mybatis.service;

import org.apache.log4j.Logger;
import org.springframework.transaction.annotation.Transactional;
import za.co.ipay.metermng.server.mybatis.mapper.IVendingActivityMapper;
import za.co.ipay.metermng.shared.dto.dashboard.TsDataCountTableDto;

import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Map;
import java.util.TreeMap;

public class VendingActivityService {

    // How far back in months to fetch data
    private static final long GRAPH_HISTORY = 2L;

    private static Logger logger = Logger.getLogger(VendingActivityService.class);

    private IVendingActivityMapper vendingActivityMapper;

    public VendingActivityService() {}

    public void setiVendingActivityMapper(IVendingActivityMapper vendingActivityMapper) {
        this.vendingActivityMapper = vendingActivityMapper;
    }

    @Transactional(readOnly = true)
    public ArrayList<TsDataCountTableDto> getVendingActivity(Long genGroupId) {

        ArrayList<TsDataCountTableDto> vendingActivityDataSet = this.getDataSet(genGroupId);

        return vendingActivityDataSet;
    }

    private ArrayList<TsDataCountTableDto> getDataSet(Long genGroupId) {

        LocalDateTime endDate = LocalDateTime.now();
        LocalDateTime startDate = endDate.minusDays(GRAPH_HISTORY)
                .withHour(0)
                .withMinute(0)
                .truncatedTo(ChronoUnit.MINUTES);

        // Use new method of getting dataset
        Timestamp sqlStartDate = Timestamp.valueOf(startDate);
        Timestamp sqlEndDate = Timestamp.valueOf(endDate);
        ArrayList<Timestamp> sqlDateArray = vendingActivityMapper.getVendingData(genGroupId,sqlStartDate, sqlEndDate);

        LocalDateTime entryDate = startDate;
        TreeMap<LocalDateTime,Integer> graphMapping = new TreeMap<>();

        // Create mapping of yyyy-MM-dd daily keys from start date to end date and set their values to 0.
        while (entryDate.isBefore(endDate.plusMinutes(15))) {
            graphMapping.put(entryDate, 0);
            entryDate = entryDate.plusMinutes(15);
        }

        for (Timestamp date : sqlDateArray) {
            LocalDateTime workingDate = date.toLocalDateTime();
            int workingDateMinutes = workingDate.getMinute();

            if    ((workingDateMinutes >= 0) && (workingDateMinutes < 15)) {
                workingDate = workingDate.withMinute(15)
                        .truncatedTo(ChronoUnit.MINUTES);

            } else if ((workingDateMinutes >= 15) && (workingDateMinutes < 30)) {
                workingDate = workingDate.withMinute(30)
                        .truncatedTo(ChronoUnit.MINUTES);

            } else if ((workingDateMinutes >= 30) && (workingDateMinutes < 45)) {
                workingDate = workingDate.withMinute(45)
                        .truncatedTo(ChronoUnit.MINUTES);

            } else {
            	// We need to add an hour to working date. Example: Times between 10:45 and 10:59 need be saved against 11:00. 
            	// If hour is not added they will be saved against 10:00.
                workingDate = workingDate.withMinute(0).plusHours(1)
                        .truncatedTo(ChronoUnit.MINUTES);
            }

            graphMapping.put(workingDate, graphMapping.get(workingDate) + 1);
        }

        ArrayList<TsDataCountTableDto> theData = new ArrayList<>();

        for (Map.Entry<LocalDateTime, Integer> entry : graphMapping.entrySet()) {
            theData.add(new TsDataCountTableDto(entry.getKey().toString().replace('T',' '),entry.getValue()));
        }

        return theData;
    }
}