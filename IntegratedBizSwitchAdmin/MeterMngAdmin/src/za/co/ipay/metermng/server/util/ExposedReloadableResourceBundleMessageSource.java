package za.co.ipay.metermng.server.util;

import java.util.Locale;
import java.util.Properties;

import org.springframework.context.support.ReloadableResourceBundleMessageSource;

public class ExposedReloadableResourceBundleMessageSource extends ReloadableResourceBundleMessageSource {

    public Properties getProperties(Locale locale) {
        PropertiesHolder propertiesHolder = getMergedProperties(locale);
        if (propertiesHolder != null) {
            return propertiesHolder.getProperties();
        } else {
            return null;
        }
    }
}
