package za.co.ipay.metermng.server.mybatis.mapper;

import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import za.co.ipay.metermng.shared.dto.dashboard.SalesPerResourceDto;

import java.sql.Date;
import java.util.ArrayList;

public interface ISalesPerResourceMapper {

    // ***
    // Daily totals of sales for resource with the id serviceId
    @Select("SELECT to_char(trans_date, 'YYYY-mm-DD') as tsData, sum(amt_incl_tax) as salesPerResourceTotal "
            + "FROM customer_trans ct "
            + "INNER JOIN usage_point up "
            + "ON ct.usage_point_id = up.usage_point_id "
            + "WHERE ct.service_resource_id= #{serviceId} "
            + "AND up.gen_group_id = #{genGroupId} "
            + "AND trans_date > #{startDate} "
            + "AND trans_date < #{endDate} "
            + "AND customer_trans_type_id = 1 "
            + "group by to_char(trans_date, 'YYYY-mm-DD') "
            + "order by 1")
    public ArrayList<SalesPerResourceDto> getDailySalesPerResource(
            @Param("genGroupId") Long genGroupId,
            @Param("serviceId") Long serviceId, 
            @Param("startDate") Date startDate,
            @Param("endDate") Date endDate);
}
