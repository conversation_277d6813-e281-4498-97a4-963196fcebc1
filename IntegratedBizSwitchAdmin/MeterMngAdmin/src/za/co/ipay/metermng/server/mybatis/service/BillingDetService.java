package za.co.ipay.metermng.server.mybatis.service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;

import org.apache.ibatis.session.RowBounds;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;

import za.co.ipay.gwt.common.shared.exception.AccessControlException;
import za.co.ipay.gwt.common.shared.exception.ServiceException;
import za.co.ipay.metermng.mybatis.custom.mapper.BillingDetAppliesLnkSupportMapper;
import za.co.ipay.metermng.mybatis.custom.mapper.TariffSupportMapper;
import za.co.ipay.metermng.mybatis.generated.mapper.BillingDetAppliesLnkMapper;
import za.co.ipay.metermng.mybatis.generated.mapper.BillingDetMapper;
import za.co.ipay.metermng.mybatis.generated.model.BillingDet;
import za.co.ipay.metermng.mybatis.generated.model.BillingDetAppliesLnk;
import za.co.ipay.metermng.mybatis.generated.model.BillingDetAppliesLnkExample;
import za.co.ipay.metermng.mybatis.generated.model.BillingDetExample;
import za.co.ipay.metermng.shared.dto.BillingDetAppliesDto;
import za.co.ipay.metermng.shared.dto.BillingDetPrimaryDto;
import za.co.ipay.metermng.shared.dto.IdNameDto;

public class BillingDetService {

    private BillingDetMapper billingDetMapper;
    private BillingDetAppliesLnkMapper billingDetAppliesLnkMapper;
    private BillingDetAppliesLnkSupportMapper billingDetAppliesLnkSupportMapper;
    private TariffSupportMapper tariffSupportMapper;
    
    @Transactional(readOnly=true)
    public Integer getBillingDetCount() throws ServiceException {
        BillingDetExample example = new BillingDetExample();
        example.createCriteria().andIdIsNotNull();
        int count = billingDetMapper.countByExample(example);
        return count;
    }
    
    @Transactional(readOnly=true)
    public ArrayList<BillingDet> getBillingDets(int startRow, int pageSize) throws ServiceException {
        BillingDetExample example = new BillingDetExample();
        example.createCriteria().andIdIsNotNull();
        example.setOrderByClause("billing_det_name asc");
        RowBounds rowBounds = new RowBounds(startRow, pageSize);
        return new ArrayList<BillingDet>(billingDetMapper.selectByExampleWithRowbounds(example, rowBounds));
    }
    
    @Transactional(readOnly=true)
    public ArrayList<BillingDet> getBillingDets() throws ServiceException {
        BillingDetExample example = new BillingDetExample();
        example.createCriteria().andIdIsNotNull();
        example.setOrderByClause("billing_det_name asc");
        return new ArrayList<BillingDet>(billingDetMapper.selectByExample(example));
    }
    
    public List<BillingDetPrimaryDto> getBillingDeterminants() {
        List<BillingDet> billingDeterminants = billingDetAppliesLnkSupportMapper.getBaseBillingDetsOnly();
        List<BillingDetPrimaryDto> billingDetMasters = new ArrayList<>();
        for (BillingDet bd: billingDeterminants) {
            List<BillingDet> appliesBdList = billingDetAppliesLnkSupportMapper.getAppliesBillingDetsForBase(bd.getId());
            if (appliesBdList.isEmpty()) {
                appliesBdList = null;
            }
            billingDetMasters.add(new BillingDetPrimaryDto(bd,appliesBdList));
        }
        return billingDetMasters;
    }
    
    @Transactional(readOnly=true)
    public ArrayList<BillingDetAppliesDto> getBillingDetAppliesDtoList() throws ServiceException {
        ArrayList<BillingDetAppliesDto> billingDetDtoList = new ArrayList<BillingDetAppliesDto>();
        for (BillingDet bd : getBillingDets()) {
            BillingDetAppliesDto bdDto = new BillingDetAppliesDto(bd);
            bdDto.setAppliesToBillingDets(billingDetAppliesLnkSupportMapper.getAppliesToBillingDets(bd.getId()));
            billingDetDtoList.add(bdDto);
        }
        return billingDetDtoList;
    }
    
    @Transactional(readOnly=true)
    public List<BillingDet> getActiveBillingDets() throws ServiceException {
        return billingDetAppliesLnkSupportMapper.getBaseBillingDetsOnly();
    }
    
    @Transactional(readOnly=true)
    public ArrayList<BillingDet> getSelectedBillingDets(ArrayList<Long> selectedIds) throws ServiceException{
        BillingDetExample example = new BillingDetExample();
        example.createCriteria().andIdIn(selectedIds);
        example.setOrderByClause("billing_det_name asc");
        return new ArrayList<BillingDet>(billingDetMapper.selectByExample(example));
    }

    @Transactional(readOnly=false)
    @PreAuthorize("hasRole('mm_billing_det_admin')")
    public ArrayList<BillingDetAppliesDto> saveBillingDet(BillingDetAppliesDto billingDetAppliesDto) throws ServiceException, AccessControlException {
        //ensure that neither the master billingDets nor the percentage BD is yet in use
        //If already in use, can still change the name and description, but not the record status or the booleans, nor the applies to list
        List<Long> checkBdIdsInUse = new ArrayList<>();
        int billingDetUsedInMDC = 0;
        if (billingDetAppliesDto.getId() != null) {
            billingDetUsedInMDC = billingDetAppliesLnkSupportMapper.getCountMdcBillingDetInUse(billingDetAppliesDto.getId());
            checkBdIdsInUse.add(billingDetAppliesDto.getId());
        }
        
        //if this bilingDet is already in use by an MDC it cannot be a sub billing det that applies to others
        if (billingDetUsedInMDC > 0 && 
                billingDetAppliesDto.getAppliesToBillingDets() != null && !billingDetAppliesDto.getAppliesToBillingDets().isEmpty()) {
            throw new ServiceException("billingdet.applies.to.already.in.use.on.mdc");
        }
        
        //get all the current links saved for this billing det
        List<IdNameDto> newBdLnks = (ArrayList<IdNameDto>) billingDetAppliesDto.getAppliesToBillingDets();
        if (newBdLnks != null) {
            for (IdNameDto idName : newBdLnks) {
                checkBdIdsInUse.add(idName.getId());
            }
        }
        
        boolean alreadyInUse = false;
        String billingDetJsonSearchString = null;
        for (Long checkBdId : checkBdIdsInUse) {
            //using a simplistic search through the JSON because of the complexity of arrays within arrays and 
            //here we don't need actual data, only a string search to see if is in use 
            billingDetJsonSearchString = "%\"billingDetId\":" + checkBdId + "%";
            int countInUse = tariffSupportMapper.getCountTariffBillingDetInUse(billingDetJsonSearchString) + 
                    tariffSupportMapper.getCountTariffClassTemplatesBillingDetInUse(billingDetJsonSearchString);
            if (countInUse > 0) {
                alreadyInUse = true;
                break;
            }
        }
        
        if (billingDetAppliesDto.getId() == null) {
            //New BD. Check the appliesTo BD's not already in use
            if (alreadyInUse) {
                throw new ServiceException("billingdet.applies.to.already.in.use");
            }
            if (billingDetMapper.insert(billingDetAppliesDto) == 1) {
                for (IdNameDto idName : billingDetAppliesDto.getAppliesToBillingDets()) {
                    BillingDetAppliesLnk bdLnk = new BillingDetAppliesLnk();
                    bdLnk.setBillingDetAppliesId(billingDetAppliesDto.getId());
                    bdLnk.setBillingDetBaseId(idName.getId());
                    if (billingDetAppliesLnkMapper.insert(bdLnk) == 1) {
                        continue;
                    } else {
                        throw new ServiceException("billingdet.perclnk.error.save");
                    }
                }
                return getBillingDetAppliesDtoList();
            } else {
                throw new ServiceException("billingDet.error.save");
            }
        } else {
            //get existing BD record
            BillingDet currentBdOnDb = billingDetMapper.selectByPrimaryKey(billingDetAppliesDto.getId());
            ArrayList<IdNameDto> oldBdLnks = (ArrayList<IdNameDto>) billingDetAppliesLnkSupportMapper.getAppliesToBillingDets(billingDetAppliesDto.getId());
            //if any field other than name or description is changed, if BD already in use in active PS or on MDC, reject
            //can't change record_status if already in use
            if (alreadyInUse) { 
                if (!currentBdOnDb.getRecordStatus().equals(billingDetAppliesDto.getRecordStatus())
                        || currentBdOnDb.isDiscount() != billingDetAppliesDto.isDiscount() 
                        || currentBdOnDb.isPercentage() != billingDetAppliesDto.isPercentage()
                        || checkChangeInFlatRate(currentBdOnDb, billingDetAppliesDto)
                        || currentBdOnDb.isTaxable() != billingDetAppliesDto.isTaxable()) {
                    throw new ServiceException("billingDet.in.use.cannot.change.settings");
                }
                //sort newBdlnks again to make sure it still maintains the same name order
                Collections.sort(newBdLnks, new Comparator<IdNameDto>() {
                            @Override
                            public int compare(IdNameDto o1, IdNameDto o2) {
                                if (o1 == o2) {
                                    return 0;
                                }
                                if (o1 != null) {
                                    return (o2 != null) ? o1.getName().toLowerCase().compareTo(o2.getName().toLowerCase()) : 1;
                                }
                                return -1;
                            }
                });
                if (!oldBdLnks.equals(newBdLnks)) {
                    throw new ServiceException("billingDet.in.use.cannot.change.applies.to");
                }
            }
        
            if (billingDetMapper.updateByPrimaryKey(billingDetAppliesDto) == 1) {
                //add any new links
                for (IdNameDto idName : billingDetAppliesDto.getAppliesToBillingDets()) {
                    if (billingDetAppliesLnkSupportMapper.selectExistingBillingDetAppliesLnk(billingDetAppliesDto.getId(), idName.getId()) == null) {
                        BillingDetAppliesLnk bdLnk = new BillingDetAppliesLnk();
                        bdLnk.setBillingDetAppliesId(billingDetAppliesDto.getId());
                        bdLnk.setBillingDetBaseId(idName.getId());
                        if (billingDetAppliesLnkMapper.insert(bdLnk) == 1) {
                            continue;
                        } else {
                            throw new ServiceException("billingdet.perclnk.error.save");
                        }
                    }    
                }
                //remove links no longer connected
                for (IdNameDto idNameOld : oldBdLnks) {
                    if (!newBdLnks.contains(idNameOld)) {
                        BillingDetAppliesLnkExample lnkex = new BillingDetAppliesLnkExample();
                        lnkex.createCriteria().andBillingDetAppliesIdEqualTo(billingDetAppliesDto.getId())
                                              .andBillingDetBaseIdEqualTo(idNameOld.getId());
                        List<BillingDetAppliesLnk> deleteThisLinkLst = billingDetAppliesLnkMapper.selectByExample(lnkex);
                        if (!deleteThisLinkLst.isEmpty()) {    //someone could have deleted it inbetween
                            billingDetAppliesLnkMapper.deleteByPrimaryKey(deleteThisLinkLst.get(0).getId());
                        }
                    }
                }
                return getBillingDetAppliesDtoList();
            } else {
                throw new ServiceException("billingDet.error.update");
            }
        }
    }
    
    private boolean checkChangeInFlatRate(BillingDet currentBdOnDb, BillingDetAppliesDto billingDetAppliesDto) {
        //we know that the billingDetAppliesDto is already in use
        //have already confirmed that isPercentage setting is the same, so no need to check flatrate if already isPercentage true 
        //cannot be both percentage and flatrate
        if (currentBdOnDb.isPercentage()) {
            return false;  //no change
        }
        //at this point, if not a percentage then,
        //if the bd being saved used to appear in the link table as an appliesTO bd and no longer does, then it used to be a flatrate but is no more
        //or vice versa
        if (billingDetAppliesLnkSupportMapper.selectCountAllBillingDetAppliesLnkforAppliesToId(currentBdOnDb.getId()) 
                != billingDetAppliesLnkSupportMapper.selectCountAllBillingDetAppliesLnkforAppliesToId(billingDetAppliesDto.getId())) {
            return true;  //has changed
        }
        return false; //no change
    }
    
    public void setBillingDetMapper(BillingDetMapper billingDetMapper) {
        this.billingDetMapper = billingDetMapper;
    }

    public void setBillingDetAppliesLnkMapper(
            BillingDetAppliesLnkMapper billingDetAppliesLnkMapper) {
        this.billingDetAppliesLnkMapper = billingDetAppliesLnkMapper;
    }

    public void setBillingDetAppliesLnkSupportMapper(
            BillingDetAppliesLnkSupportMapper billingDetAppliesLnkSupportMapper) {
        this.billingDetAppliesLnkSupportMapper = billingDetAppliesLnkSupportMapper;
    }
    
    public void setTariffSupportMapper(TariffSupportMapper tariffSupportMapper) {
        this.tariffSupportMapper = tariffSupportMapper;
    }
}
