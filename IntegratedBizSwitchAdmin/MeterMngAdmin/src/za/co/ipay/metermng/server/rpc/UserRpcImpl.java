package za.co.ipay.metermng.server.rpc;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;

import org.apache.log4j.Logger;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import za.co.ipay.accesscontrol.domain.Group;
import za.co.ipay.accesscontrol.domain.User;
import za.co.ipay.accesscontrol.password.IPasswordValidationPolicy;
import za.co.ipay.accesscontrol.password.PasswordValidationCriteria;
import za.co.ipay.accesscontrol.password.PasswordValidationResult;
import za.co.ipay.accesscontrol.service.IAccessControlService;
import za.co.ipay.gwt.common.shared.exception.AccessControlException;
import za.co.ipay.gwt.common.shared.exception.ServiceException;
import za.co.ipay.gwt.common.shared.exception.ValidationException;
import za.co.ipay.gwt.common.shared.exception.ValidationMessage;
import za.co.ipay.metermng.client.rpc.UserRpc;
import za.co.ipay.metermng.mybatis.generated.model.GenGroup;
import za.co.ipay.metermng.mybatis.generated.model.UserGroup;
import za.co.ipay.metermng.server.mybatis.service.GroupService;
import za.co.ipay.metermng.server.mybatis.service.UserGroupService;
import za.co.ipay.metermng.shared.MeterMngStatics;
import za.co.ipay.metermng.shared.dto.user.MeterMngUser;
import za.co.ipay.metermng.shared.dto.user.MeterMngUserGroup;
import za.co.ipay.metermng.shared.dto.user.UserData;
import za.co.ipay.utils.StringUtils;

@SuppressWarnings("deprecation") //PasswordEncoder is deprecated
public class UserRpcImpl extends BaseMeterMngRpc implements UserRpc {

    private static final long serialVersionUID = 1L;
    
    /** The service to access UserGroup data. */
    private UserGroupService userGroupService;
    /** The service to access groups. */
    private GroupService groupService;
    
    /** The service to get user data from access control. */
    private IAccessControlService accessControlService;
    /** The password validation policy used to validate new passwords. */
    private IPasswordValidationPolicy passwordValidationPolicy;    
    /** The encoder for passwords. */
    private PasswordEncoder passwordEncoder;
        
    public UserRpcImpl() {
        logger = Logger.getLogger(UserRpcImpl.class);
    }
        
    @Override
    public MeterMngUser getCurrentUser() throws ServiceException {         
        return getUser();  
    }
    

    @Override
    public GenGroup updateUserCurrentGroup(Boolean fromUserGroupWorkspace, Long groupId) throws ValidationException, ServiceException, AccessControlException {
        if (groupId == null || groupService.getChooseAccessGroupDecision(groupId)) {
            setUserGroup(getUser().getAssignedGroup().getId());
            return null;
        }
        return updateUserCurrentGroup(groupId);
    }


    @Override
    public GenGroup updateUserCurrentGroup(Long groupId) throws ValidationException, ServiceException, AccessControlException {
        if (getCurrentUser().hasPermission("mm_session_group_change")) {
            if (groupId == null) {
                return clearUserGroup();
            } else {
                return setUserGroup(groupId);
            }
        } else {
            throw new AccessControlException();
        }
    }
    
    
    private GenGroup clearUserGroup() throws ValidationException, ServiceException {
        MeterMngUser user = getUser();
        if (user.getAssignedGroup() != null) {
            user.setCurrentGroup(user.getAssignedGroup());
            boolean isMustChooseGroup = groupService.getChooseAccessGroupDecision(user.getCurrentGroupId());
            user.setChooseCurrentGroupleafNode(isMustChooseGroup);
            if (isMustChooseGroup) {
                return null;
            } 
            return user.getAssignedGroup();
        } else {
            user.setCurrentGroup(null);
            user.setChooseCurrentGroupleafNode(true);
            return null;
        }
    }
    
    private GenGroup setUserGroup(Long groupId) throws ValidationException, ServiceException {
        logger.info("Setting user's group for groupId:"+groupId);
        GenGroup group = groupService.getGenGroup(groupId);
        if (group == null) {
            throw new ValidationException(new ValidationMessage("error.current.group", true));
        }
        
        MeterMngUser user = getUser();
        
        //If the user is assigned to a group, check that the new group is a child of their assigned group
        if (user.getAssignedGroup() != null) {
            //Want the hierarchy of the user's new group from parent to child
            boolean isParent = false;
            ArrayList<Long> pathIds = groupService.getPath(groupId);
            logger.info("Got new group's path: "+pathIds.toString());
            for(int i=0;i<pathIds.size();i++) {
                if (pathIds.get(i).equals(user.getAssignedGroup().getId())) {
                    isParent = true;
                    break;
                }
            }
            if (!isParent) {
                logger.error("Current group is not a child of the user's assigned group: "+user.getAssignedGroup().getName());
                throw new ValidationException(new ValidationMessage("error.current.group", true));
            }
        }
        
        //Set the user's current group
        user.setCurrentGroup(group);
        //set the choose AccessGroup to false, because have now chosen
        user.setChooseCurrentGroupleafNode(false);
        
        //set httpsession var
        ServletRequestAttributes requestAttributes = (ServletRequestAttributes) RequestContextHolder.currentRequestAttributes();
        HttpServletRequest request = null;
        if (requestAttributes != null) {
            request = requestAttributes.getRequest();
            if (request != null) {
                HttpSession httpSession = request.getSession(false);
                if (httpSession != null) {
                    httpSession.setAttribute(MeterMngStatics.METER_MNG_USER_ATTR, user);
                    logger.info("Saved user in session: " + user.getUserName());
                } 
            }
        }
        //Return the set group
        return group;
    }

    @Override
    public GenGroup getUserGroup(Long userId) throws ValidationException, ServiceException {
        return userGroupService.getUserAssignedGroup(userId);
    }
    
    @Override
    public List<MeterMngUserGroup> getUsersWithGroup() throws ServiceException, AccessControlException {
        return userGroupService.getAllUserGroups();
    }
    
    @Override
    public MeterMngUserGroup getCurrentUserWithGroup() throws ServiceException {
        MeterMngUser user = getUser();
        if (user.getAssignedGroup() != null) {  
            MeterMngUserGroup meterMngUserGroup = new MeterMngUserGroup();
            meterMngUserGroup.setId(user.getId());
            meterMngUserGroup.setUserName(user.getUserName());
            meterMngUserGroup.setAssignedGroup(user.getAssignedGroup());
            meterMngUserGroup.setCurrentGroup(user.getCurrentGroup());
            meterMngUserGroup.setPermissions(user.getPermissions());
            meterMngUserGroup.setRoles(user.getRoles());
            meterMngUserGroup.setGroupPath(groupService.getPath(user.getAssignedGroup().getId()));       
            meterMngUserGroup.setAssignedGroupHierarchy(groupService.getGroupHierarchy(user.getAssignedGroup().getGroupHierarchyId()));
            return meterMngUserGroup;
        } else {
            throw new ServiceException("error.no.user.assignedgroup");
        }
    }
    
    @Override
    public List<UserData> getUserSuggestions(String query, int limit) {
        return userGroupService.getUserSuggestions(query, limit, getOrganisation());
    } 
    
    @Override
    public UserGroup saveUserGroup(Long userId, Long groupId) throws ValidationException, ServiceException, AccessControlException {             
        UserGroup userGroup = userGroupService.saveUserGroup(userId, groupId);
        //Update the user's assigned group
        MeterMngUser user = getUser();
        if (user.getId() == userId.longValue()) {
            GenGroup assigned = groupService.getGenGroup(groupId);
            user.setAssignedGroup(assigned);
            logger.info("Updated the current user's assigned group: "+user.getUserName()+" to "+assigned.getName());
        }
        return userGroup;
    }
    
    @Override
    public void deleteUserGroup(Long userId) throws ValidationException, ServiceException {        
        userGroupService.deleteUserGroup(userId);       
        //Clear the user's assigned group
        MeterMngUser user = getUser();
        if (user.getId() == userId.longValue()) {
            user.setAssignedGroup(null);          
            logger.info("Cleared the current user's assigned group: "+user.getUserName());
        }
    } 
    
    @Override
    public MeterMngUser updateCurrentUserPassword(Long userId, String currentPassword, String newPassword) 
            throws ValidationException, ServiceException {
        User user = accessControlService.findUserById(userId);
        
        //Current password correct?
        if (!passwordEncoder.matches(currentPassword, user.getPassword())) {
            accessControlService.incorrectPasswordAttempt(user);
            if (user.isLocked()) {
                throw new ValidationException(new ValidationMessage("password.locked", true));
            } else {
                throw new ValidationException(new ValidationMessage("password.validate.current", true));
            }
        }
        
        //Password policy validation
        user.setPassword(newPassword);
        validatePasswordWithPolicy(user);
        
        //Save the password
        accessControlService.changeUserPassword(user, passwordEncoder.encode(newPassword));
        //Update the session's version of the User
        MeterMngUser u = getUser();
        u.setPasswordExpired(false);
        u.setPasswordRequiresReset(false);
        u.setPasswordWarningMsg(null);        
        logger.info("Updated user's password: "+user.getUsername());
        return u;
    }
    
    private void validatePasswordWithPolicy(User user) throws ValidationException {
        if (!user.isLdapAuthenticated() && !StringUtils.isNullOrEmpty(user.getPassword())) {
            if (passwordValidationPolicy != null) {
                PasswordValidationResult result = passwordValidationPolicy.validate(accessControlService, user, user.getPassword());
                if (!result.isValid()) {
                    Map<String, ValidationMessage> fieldErrorMessages = new HashMap<String, ValidationMessage>();                    
                    PasswordValidationCriteria criteria = passwordValidationPolicy.getCriteria(accessControlService, user);
                    if (!result.isPassedMinLength()) {
                        fieldErrorMessages.put("password1", new ValidationMessage(IPasswordValidationPolicy.MIN_LENGTH_KEY, new String[]{Integer.toString(criteria.getMinLength())}, true));
                    }
                    if (!result.isPassedMaxLength()) {
                        fieldErrorMessages.put("password2", new ValidationMessage(IPasswordValidationPolicy.MAX_LENGTH_KEY, new String[]{Integer.toString(criteria.getMaxLength())}, true));
                    }
                    if (!result.isPassedMinUpperCase()) {
                        fieldErrorMessages.put("password3", new ValidationMessage(IPasswordValidationPolicy.MIN_UPPER_KEY, new String[]{Integer.toString(criteria.getMinUpperCase())}, true));
                    }
                    if (!result.isPassedMinDigits()) {
                        fieldErrorMessages.put("password4", new ValidationMessage(IPasswordValidationPolicy.MIN_DIGITS_KEY, new String[]{Integer.toString(criteria.getMinDigits())}, true));
                    }
                    if (!result.isPassedNumHistoryPasswords()) {
                        fieldErrorMessages.put("password5", new ValidationMessage(IPasswordValidationPolicy.NUM_HISTORY_KEY, new String[]{Integer.toString(criteria.getNumHistoryPasswords())}, true));
                    }
                    logger.info("Password policy validation errors: "+fieldErrorMessages.values());
                    throw new ValidationException(fieldErrorMessages);
                }
            } else {
                logger.debug("No password validation policy set!");
            }
        }
    }
    
    @Override
    public String getAccessGroupNameById(Long accessGroupId) throws ServiceException {
        if (accessGroupId != null) {
            return accessControlService.findGroupById(accessGroupId).getName();
        }
        return null;
    }
    
    @Override
    public List<Group> getAccessGroupsByOrgId(String orgShortName) throws ServiceException {
        Long orgId = accessControlService.findManagementCompanyByShortName(orgShortName).getId();
        if (orgId != null) {
            return accessControlService.findGroups(orgId);
        }
        return null;
    }
    
    /** Setters **/
    
    public void setUserGroupService(UserGroupService userGroupService) {
        this.userGroupService = userGroupService;
    }
    
    public void setGroupService(GroupService groupService) {
        this.groupService = groupService;
    }

    public void setAccessControlService(IAccessControlService accessControlService) {
        this.accessControlService = accessControlService;
    }

    public void setPasswordValidationPolicy(IPasswordValidationPolicy passwordValidationPolicy) {
        this.passwordValidationPolicy = passwordValidationPolicy;
    }

    public void setPasswordEncoder(PasswordEncoder passwordEncoder) {
        this.passwordEncoder = passwordEncoder;
    }
}
