package za.co.ipay.metermng.server.mybatis.service;

import org.apache.log4j.Logger;

import za.co.ipay.ipayxml.metermng.StsEngTokenReqMessage;
import za.co.ipay.ipayxml.metermng.StsEngTokenResMessage;
import za.co.ipay.ipayxml.metermng.domain.Meter;
import za.co.ipay.metermng.datatypes.StsEngineeringTokenTypeE;
import za.co.ipay.metermng.ipayxml.IpayXmlMessageService;
import za.co.ipay.metermng.mybatis.generated.mapper.SpecialActionReasonsMapper;
import za.co.ipay.metermng.mybatis.generated.model.SpecialActionReasonsExample;
import za.co.ipay.metermng.mybatis.generated.model.SpecialActionReasonsLog;
import za.co.ipay.utils.web.ICurrentUserNameProvider;

public class TokenGenerationService {
    private IpayXmlMessageService ipayXmlMessageService;
    private ICurrentUserNameProvider currentUserNameProvider;
    private SpecialActionReasonsMapper specialActionReasonsMapper;

    private static final Logger logger = Logger.getLogger(TokenGenerationService.class);

    public void setIpayXmlMessageService(IpayXmlMessageService ipayXmlMessageService) {
        this.ipayXmlMessageService = ipayXmlMessageService;
    }

    public void setCurrentUserNameProvider(ICurrentUserNameProvider currentUserNameProvider) {
        this.currentUserNameProvider = currentUserNameProvider;
    }

    public void setSpecialActionReasonsMapper(SpecialActionReasonsMapper specialActionReasonsMapper) {
        this.specialActionReasonsMapper = specialActionReasonsMapper;
    }

    public StsEngTokenResMessage sendStsEngTokenReqMessage(String description, String meterNumber, String userReference,
            StsEngineeringTokenTypeE tokenType, Integer units, SpecialActionReasonsLog specialActionReasonsLog)
            throws Exception {
        StsEngTokenResMessage stsEngTokenResMessage = null;
        try {
            String reason = null;
            String customReason = null;
            if (specialActionReasonsLog != null) {
                customReason = specialActionReasonsLog.getReasonText();
                Long specialActionReasonsId = specialActionReasonsLog.getSpecialActionReasonsId();
                if (specialActionReasonsId != null) {
                    SpecialActionReasonsExample example = new SpecialActionReasonsExample();
                    example.createCriteria().andIdEqualTo(specialActionReasonsId);
                    reason = specialActionReasonsMapper.selectByExample(example).get(0).getReasonName();
                }
            }
            stsEngTokenResMessage = (StsEngTokenResMessage) ipayXmlMessageService
                    .sendIpayXml(new StsEngTokenReqMessage(ipayXmlMessageService.getClient(),
                            ipayXmlMessageService.getTerm(), new Meter(meterNumber, null, null), tokenType.getValue(),
                            currentUserNameProvider.getCurrentUserName(), units, userReference, reason, description,
                            customReason));
        } catch (Exception e) {
            logger.info("SENDMESSAGE sendIpayXml FAILURE: exception.toString()=" + e.toString());
            if (!e.toString().contains("ConnectException")) {
                throw new Exception(e);
            }
        }
        return stsEngTokenResMessage;
    }
}
