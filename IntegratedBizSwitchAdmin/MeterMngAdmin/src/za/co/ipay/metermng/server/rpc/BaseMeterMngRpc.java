package za.co.ipay.metermng.server.rpc;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;

import org.apache.log4j.Logger;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import za.co.ipay.gwt.common.server.Organisation;
import za.co.ipay.gwt.common.shared.exception.NoCurrentUserException;
import za.co.ipay.gwt.common.shared.exception.ServiceException;
import za.co.ipay.metermng.shared.MeterMngStatics;
import za.co.ipay.metermng.shared.dto.user.MeterMngUser;

import com.google.gwt.user.server.rpc.RemoteServiceServlet;

/**
 * BaseMeterMngRpc provides common functionality for all meter management RPC classes on the server-side.
 * <AUTHOR>
 */
public abstract class BaseMeterMngRpc extends RemoteServiceServlet implements Organisation {

    private static final long serialVersionUID = 1L;
        
    /** The name of the organisation that Meter Management web site is installed for currently. It can be null if no
     *  organisation has been set for the installation. */
    private String organisation;
    
    protected Logger logger; //init in each subclass or else it will be null!

    /**
     * Method to set the current organisation.
     * @param organisation
     */
    public void setOrganisation(String organisation) {
        this.organisation = organisation;
        logger.debug("Set current organisation:"+organisation);
    }

    /**
     * Method to get the organisation.
     * @return The organisation or null if none is set.
     */
    public String getOrganisation() {
        return organisation;
    }

    /**
     * Method to access the current user from their session. 
     * @return The user or null if no user is logged in or their session has timed out.
     */
    protected MeterMngUser getUser() throws ServiceException {
        HttpSession httpSession = getSession();
        if (httpSession != null) {
            MeterMngUser user = (MeterMngUser) httpSession.getAttribute(MeterMngStatics.METER_MNG_USER_ATTR);
            if (user == null) {
                logger.error("No current user in session");
                throw new NoCurrentUserException("error.session.user.none");
            } else {
                return user;
            }
        } else {
            logger.error("No current session");
            throw new NoCurrentUserException("error.session.none");
        }
    }
    
    /**
     * Method to get the current session.
     * @return The session or null if there is none.
     */
    protected HttpSession getSession() {
        return getSession(false);
    }
    
    /**
     * Method to get the current session.
     * @param createNew Whether a new session should be created if there is none.
     * @return The session or null if the session could not be accessed successfully.
     */
    protected HttpSession getSession(boolean createNew) {
        HttpServletRequest request = null; 
        ServletRequestAttributes requestAttributes = (ServletRequestAttributes) RequestContextHolder.currentRequestAttributes();
        if (requestAttributes != null) {
             request = requestAttributes.getRequest();
             return request.getSession(createNew);  
        } else {
            return null;
        }
    }
}
