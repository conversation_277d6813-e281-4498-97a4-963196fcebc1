package za.co.ipay.metermng.server.mybatis.service;

import org.springframework.transaction.annotation.Transactional;
import za.co.ipay.metermng.server.mybatis.mapper.IBuyingIndexMapper;
import za.co.ipay.metermng.server.mybatis.model.StringBigDecPair;
import za.co.ipay.metermng.shared.dto.dashboard.TsDataBigDecimalDto;

import java.math.BigDecimal;
import java.sql.Date;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class DashBoardService {

    // How far back in months to fetch data
    private static final int BUYING_INDEX_GRAPH_HISTORY = 12;

    private IBuyingIndexMapper buyingIndexMapper;

    public DashBoardService() {
    }

    public void setiBuyingIndexMapper(IBuyingIndexMapper buyingIndexMapper) {
        this.buyingIndexMapper = buyingIndexMapper;
    }

    @Transactional(readOnly = true)
    public ArrayList<TsDataBigDecimalDto> getBuyingIndexData(Long genGroupId) {

        LocalDate lTime = LocalDate.now().withDayOfMonth(1);
        List<StringBigDecPair> uniqueTransactingMetersPerMonth = buyingIndexMapper.getCountOfUniqueTransactingMetersPerMonth(genGroupId, Date.valueOf(lTime.minusMonths(BUYING_INDEX_GRAPH_HISTORY)));

        //Convert to map. Mybatis would perform similar step if returning map directly.
        Map<String, BigDecimal> uniqueTransatingMetersPerMonthMap = new HashMap<>();
        for (StringBigDecPair sBDP : uniqueTransactingMetersPerMonth) {
            uniqueTransatingMetersPerMonthMap.put(sBDP.getStringValue(), sBDP.getBigDecValue());
        }

        ArrayList<TsDataBigDecimalDto> theData = new ArrayList<>();

        //Date format for comparing date value strings returned from DB (example 2018-3).
        DateTimeFormatter formatFromDB = DateTimeFormatter.ofPattern("yyyy-M");

        //Date format for returning to client for graph labels (example Mar 2018).
        DateTimeFormatter formatForGraph = DateTimeFormatter.ofPattern("MMM yyyy");

        String dateStringDB;
        String dateStringGraph;
        for (int i = 0; i < BUYING_INDEX_GRAPH_HISTORY; i++) {
            dateStringDB = lTime.format(formatFromDB);
            dateStringGraph = lTime.format(formatForGraph);
            if (uniqueTransatingMetersPerMonthMap.containsKey(dateStringDB)) {
                BigDecimal lValue = buyingIndexMapper.getActiveUsagePointCountForTimePeriod(genGroupId, Date.valueOf(lTime), Date.valueOf(lTime.plusMonths(1)));
                BigDecimal buyingIndex = BigDecimal.ZERO;
                if (lValue.compareTo(BigDecimal.ZERO) != 0) {
                    buyingIndex = uniqueTransatingMetersPerMonthMap.get(dateStringDB)
                            .divide(lValue, 2, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal("100"));
                }
                theData.add(0, new TsDataBigDecimalDto(dateStringGraph, buyingIndex));
            } else {
                theData.add(0, new TsDataBigDecimalDto(dateStringGraph, BigDecimal.ZERO));
            }
            lTime = lTime.minusMonths(1);
        }


        return theData;
    }
}
