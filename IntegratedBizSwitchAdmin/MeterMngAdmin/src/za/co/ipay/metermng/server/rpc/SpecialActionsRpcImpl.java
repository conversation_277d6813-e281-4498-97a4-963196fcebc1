package za.co.ipay.metermng.server.rpc;

import java.util.ArrayList;

import org.apache.log4j.Logger;

import za.co.ipay.gwt.common.shared.exception.AccessControlException;
import za.co.ipay.gwt.common.shared.exception.ServiceException;
import za.co.ipay.gwt.common.shared.exception.ValidationException;
import za.co.ipay.gwt.common.shared.exception.ValidationMessage;
import za.co.ipay.metermng.client.rpc.SpecialActionsRpc;
import za.co.ipay.metermng.mybatis.generated.model.AuxType;
import za.co.ipay.metermng.mybatis.generated.model.SpecialActionReasons;
import za.co.ipay.metermng.mybatis.generated.model.SpecialActions;
import za.co.ipay.metermng.server.mybatis.service.SpecialActionsService;
import za.co.ipay.metermng.server.validation.ServerValidatorUtil;

public class SpecialActionsRpcImpl extends BaseMeterMngRpc implements SpecialActionsRpc {

	private static final long serialVersionUID = -2922619173447417184L;
	
	private SpecialActionsService specialActionsService;
    
    public SpecialActionsRpcImpl() {
        this.logger = Logger.getLogger(SpecialActionsRpcImpl.class);
    }

	@Override
	public ArrayList<SpecialActions> getSpecialActions() throws ServiceException {
		return specialActionsService.getSpecialActions();
	}

	@Override
	public SpecialActions saveSpecialActions(SpecialActions specialAction) throws ValidationException, ServiceException, AccessControlException {
		return specialActionsService.saveSpecialAction(specialAction);
	}    

	@Override
	public ArrayList<SpecialActionReasons> getSpecialActionReasons(long specialActionId) throws ServiceException {
		return specialActionsService.getSpecialActionReasons(specialActionId, false);
	}

	@Override
	public SpecialActionReasons addSpecialActionReason(SpecialActionReasons specialActionReason) throws ValidationException, ServiceException, AccessControlException {
		ServerValidatorUtil.getInstance().validateDataForValidationMessages(specialActionReason);
		if (specialActionsService.getSpecialActionReasonsByName(specialActionReason.getReasonName()) != null) {
            throw new ValidationException(new ValidationMessage("special.action.reason.error.save.duplicate", true));
        }
        if (specialActionsService.getMridExistence(specialActionReason.getMrid(), specialActionReason.getId())) {
            throw new ValidationException(new ValidationMessage("special.action.reason.mrid.external.unique.validation", true));
        }
		return specialActionsService.insertSpecialActionReason(specialActionReason);
	}

	@Override
	public SpecialActionReasons updateSpecialActionReason(SpecialActionReasons specialActionReason) throws ValidationException, ServiceException, AccessControlException {
		ServerValidatorUtil.getInstance().validateDataForValidationMessages(specialActionReason);
		SpecialActionReasons tempReason = specialActionsService.getSpecialActionReasonsByName(specialActionReason.getReasonName());
		if ((tempReason != null) && (!tempReason.getId().equals(specialActionReason.getId()))) {
            throw new ValidationException(new ValidationMessage("special.action.reason.error.update.duplicate", true));
        }
        if (specialActionsService.getMridExistence(specialActionReason.getMrid(), specialActionReason.getId())) {
            throw new ValidationException(new ValidationMessage("special.action.reason.mrid.external.unique.validation", true));
        }
		return specialActionsService.updateSpecialActionReason(specialActionReason);
	}
	
	@Override
	public SpecialActions getSpecialActionsByValue(String specialActionValue) throws ValidationException, ServiceException, AccessControlException {
		return specialActionsService.getSpecialActionByValue(specialActionValue);
	}
	
	public void setSpecialActionsService(SpecialActionsService specialActionsService) {
        this.specialActionsService = specialActionsService;
    }

	@Override
	public ArrayList<SpecialActionReasons> getActiveSpecialActionReasons(long specialActionId) throws ServiceException {
		return specialActionsService.getSpecialActionReasons(specialActionId, true);
	}


	
	
	
}
