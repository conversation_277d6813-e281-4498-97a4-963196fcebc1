package za.co.ipay.metermng.server.mybatis.service;

import java.util.ArrayList;
import java.util.List;

import org.apache.ibatis.session.RowBounds;
import org.springframework.transaction.annotation.Transactional;

import za.co.ipay.gwt.common.server.validation.ServerValidatorUtil;
import za.co.ipay.gwt.common.shared.exception.ServiceException;
import za.co.ipay.gwt.common.shared.exception.ValidationException;
import za.co.ipay.gwt.common.shared.exception.ValidationMessage;
import za.co.ipay.metermng.datatypes.RecordStatus;
import za.co.ipay.metermng.mybatis.generated.mapper.ManufacturerMapper;
import za.co.ipay.metermng.mybatis.generated.model.Manufacturer;
import za.co.ipay.metermng.mybatis.generated.model.ManufacturerExample;

public class ManufacturerService {

    private ManufacturerMapper manufacturerMapper;
    
    @Transactional(readOnly=true)
    public Manufacturer getManufacturer(Long manufacturerId) {
        if (manufacturerId != null) {
            return manufacturerMapper.selectByPrimaryKey(manufacturerId);
        } else {
            return null;
        }
    }
    
    @Transactional(readOnly=true)
    public List<Manufacturer> getManufacturers(Boolean enabled) {
        ManufacturerExample example = new ManufacturerExample();
        if (enabled != null) {
            if (enabled) {
                example.createCriteria().andRecordStatusEqualTo(RecordStatus.ACT);
            } else {
                example.createCriteria().andRecordStatusEqualTo(RecordStatus.DAC);
            }
        } else {
            example.createCriteria().andRecordStatusNotEqualTo(RecordStatus.DEL);
        }
        example.setOrderByClause("manufacturer_name");
        return manufacturerMapper.selectByExample(example);
    }

    @Transactional(readOnly=true)
    public Integer getManufacturersCount() throws ServiceException {
        ManufacturerExample example = new ManufacturerExample();
        example.createCriteria().andRecordStatusNotEqualTo(RecordStatus.DEL);
        int count = manufacturerMapper.countByExample(example);
        return count;
    }

    @Transactional(readOnly=true)
    public ArrayList<Manufacturer> getManufacturers(int startRow, int pageSize, String sortField, boolean isAscending)
            throws ServiceException {
        ManufacturerExample example = new ManufacturerExample();
        example.createCriteria().andRecordStatusNotEqualTo(RecordStatus.DEL);
        example.setOrderByClause(getOrderColumn(sortField, isAscending));
        RowBounds rowBounds = new RowBounds(startRow, pageSize);
        return new ArrayList<Manufacturer>(manufacturerMapper.selectByExampleWithRowbounds(example, rowBounds));
    }
    
    private String getOrderColumn(String sortField, boolean isAscending) {
        String orderColumn = "manufacturer_name";
        if (sortField != null && !sortField.trim().equals("")) {
            if ("name".equals(sortField)) {
                orderColumn = "manufacturer_name";
            } else if ("description".equals(sortField)) {
                orderColumn = "manufacturer_description";
            } else if ("status".equals(sortField)) {
                orderColumn = "record_status";
            }
                
        }
        return orderColumn + " " + getOrder(isAscending);
    }
    
    private String getOrder(boolean isAscending) {
        if (isAscending) {
            return "asc";
        } else {
            return "desc";
        }
    }
    
    @Transactional(readOnly=true)
    public Manufacturer getManufacturerByName(String name) throws ValidationException, ServiceException {
        if (name != null && !name.trim().equals("")) {
            ManufacturerExample example = new ManufacturerExample();
            example.createCriteria().andNameEqualTo(name);
            List<Manufacturer> mans = manufacturerMapper.selectByExample(example);
            if (mans.isEmpty()) {
                return null;
            } else {
                return mans.get(0);
            }
        } else {
            return null;
        }
    }    

    @Transactional(readOnly=false)
    public void saveManufacturer(Manufacturer manufacturer) throws ValidationException, ServiceException {
        ServerValidatorUtil.getInstance().validateDataForValidationMessages(manufacturer);
        
        Manufacturer existing = (Manufacturer) getManufacturerByName(manufacturer.getName());
        if (existing != null && !existing.getId().equals(manufacturer.getId())) {
            throw new ValidationException(new ValidationMessage("meter.manufacturer.name.duplicate", new String[]{manufacturer.getName()}, true));
        }
        
        if (manufacturer.getId() == null) {
            if (manufacturerMapper.insert(manufacturer) != 1) {
                throw new ValidationException(new ValidationMessage("error.save", new String[]{"meter.manufacturer.name"}, true));
            }
        } else {
            if (manufacturerMapper.updateByPrimaryKey(manufacturer) != 1) {
                throw new ValidationException(new ValidationMessage("error.save", new String[]{"meter.manufacturer.name"}, true));
            }
        }
    }
    
    public void setManufacturerMapper(ManufacturerMapper manufacturerMapper) {
        this.manufacturerMapper = manufacturerMapper;
    }
}
