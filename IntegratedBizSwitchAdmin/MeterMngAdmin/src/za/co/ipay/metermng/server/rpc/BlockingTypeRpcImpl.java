package za.co.ipay.metermng.server.rpc;

import java.security.AccessControlException;
import java.util.List;

import org.apache.log4j.Logger;

import za.co.ipay.gwt.common.shared.exception.ServiceException;
import za.co.ipay.gwt.common.shared.exception.ValidationException;
import za.co.ipay.gwt.common.shared.exception.ValidationMessage;
import za.co.ipay.metermng.client.rpc.BlockingTypeRpc;
import za.co.ipay.metermng.mybatis.generated.model.BlockingType;
import za.co.ipay.metermng.server.mybatis.service.BlockingTypeService;
import za.co.ipay.metermng.server.validation.ServerValidatorUtil;

public class BlockingTypeRpcImpl extends BaseMeterMngRpc implements BlockingTypeRpc {

    private static final long serialVersionUID = 1L;

    private BlockingTypeService blockingTypeService;

    public BlockingTypeRpcImpl() {
        this.logger = Logger.getLogger(BlockingTypeRpcImpl.class);
    }

    public void setBlockingTypeService(BlockingTypeService blockingTypeService) {
        this.blockingTypeService = blockingTypeService;
    }

    @Override
    public List<BlockingType> getAllBlockingTypes() throws ServiceException {
        return blockingTypeService.getAllBlockingTypes();
    }

    @Override
    public BlockingType getBlockingType(Long id) throws ServiceException, AccessControlException {
        return blockingTypeService.getBlockingTypeById(id);
    }

    @Override
    public BlockingType addBlockingType(BlockingType blockingType) throws ValidationException, ServiceException {
        ServerValidatorUtil.getInstance().validateDataForValidationMessages(blockingType);
        if (blockingTypeService.getBlockingTypeByName(blockingType.getTypeName()) != null) {
            throw new ValidationException(new ValidationMessage("blockingtype.error.save.duplicate", true));
        }
        return blockingTypeService.insertBlockingType(blockingType);
    }

    @Override
    public BlockingType updateBlockingType(BlockingType blockingType) throws ValidationException, ServiceException {
        ServerValidatorUtil.getInstance().validateDataForValidationMessages(blockingType);
        BlockingType tempBlockingType = blockingTypeService.getBlockingTypeByName(blockingType.getTypeName());
        if ((tempBlockingType != null) && (!tempBlockingType.getId().equals(blockingType.getId()))) {
            throw new ValidationException(new ValidationMessage("blockingtype.error.update.duplicate", true));
        }
        return blockingTypeService.updateBlockingType(blockingType);
    }
}
