package za.co.ipay.metermng.server.mybatis.service;

import java.util.ArrayList;
import java.util.List;

import org.apache.ibatis.session.RowBounds;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;

import za.co.ipay.gwt.common.server.validation.ServerValidatorUtil;
import za.co.ipay.gwt.common.shared.exception.ServiceException;
import za.co.ipay.gwt.common.shared.exception.ValidationException;
import za.co.ipay.gwt.common.shared.exception.ValidationMessage;
import za.co.ipay.metermng.datatypes.RecordStatus;
import za.co.ipay.metermng.mybatis.custom.mapper.MdcChannelCustomMapper;
import za.co.ipay.metermng.mybatis.generated.mapper.MdcMapper;
import za.co.ipay.metermng.mybatis.generated.model.Mdc;
import za.co.ipay.metermng.mybatis.generated.model.MdcExample;

public class MdcService {

    private MdcMapper mdcMapper;
    private MdcChannelCustomMapper mdcChannelCustomMapper;
    
    @Transactional(readOnly=true)
    public Mdc getMdc(Long mdcId) {
        if (mdcId != null) {
            return mdcMapper.selectByPrimaryKey(mdcId);
        } else {
            return null;
        }
    }
    
    @Transactional(readOnly=true)
    public List<Mdc> getMdcs(Boolean enabled) {
        MdcExample example = new MdcExample();
        if (enabled != null) {
            if (enabled) {
                example.createCriteria().andRecordStatusEqualTo(RecordStatus.ACT);
            } else {
                example.createCriteria().andRecordStatusEqualTo(RecordStatus.DAC);
            }
        } else {
            example.createCriteria().andRecordStatusNotEqualTo(RecordStatus.DEL);
        }
        example.setOrderByClause("mdc_name");
        return mdcMapper.selectByExample(example);
    }

    @Transactional(readOnly=true)
    public Integer getMdcCount() throws ServiceException {
        MdcExample example = new MdcExample();
        example.createCriteria().andRecordStatusNotEqualTo(RecordStatus.DEL);
        int count = mdcMapper.countByExample(example);
        return count;
    }

    @Transactional(readOnly=true)
    public ArrayList<Mdc> getMdcs(int startRow, int pageSize, String sortField, boolean isAscending)
            throws ServiceException {
        MdcExample example = new MdcExample();
        example.createCriteria().andRecordStatusNotEqualTo(RecordStatus.DEL);
        example.setOrderByClause(getOrderColumn(sortField, isAscending));
        RowBounds rowBounds = new RowBounds(startRow, pageSize);
        return new ArrayList<Mdc>(mdcMapper.selectByExampleWithRowbounds(example, rowBounds));
    }
    
    private String getOrderColumn(String sortField, boolean isAscending) {
        String orderColumn = "mdc_name";
        if (sortField != null && !sortField.trim().equals("")) {
            if ("name".equals(sortField)) {
                orderColumn = "mdc_name";
            } else if ("description".equals(sortField)) {
                orderColumn = "mdc_description";
            } else if ("status".equals(sortField)) {
                orderColumn = "record_status";
            } else if ("value".equals(sortField)) {
                orderColumn = "mdc_value";
            }
                
        }
        return orderColumn + " " + getOrder(isAscending);
    }
    
    private String getOrder(boolean isAscending) {
        if (isAscending) {
            return "asc";
        } else {
            return "desc";
        }
    }
    
    @Transactional(readOnly=true)
    public Mdc getMdcByName(String name) throws ValidationException, ServiceException {
        if (name != null && !name.trim().equals("")) {
            MdcExample example = new MdcExample();
            example.createCriteria().andNameEqualTo(name);
            List<Mdc> mdcs = mdcMapper.selectByExample(example);
            if (mdcs.isEmpty()) {
                return null;
            } else {
                return mdcs.get(0);
            }
        } else {
            return null;
        }
    }    
    
    
    @Transactional(readOnly=true)
    public Mdc getMdcByValue(String value) throws ValidationException, ServiceException {
        if (value != null && !value.trim().equals("")) {
            MdcExample example = new MdcExample();
            example.createCriteria().andValueEqualTo(value);
            List<Mdc> mdcs = mdcMapper.selectByExample(example);
            if (mdcs.isEmpty()) {
                return null;
            } else {
                return mdcs.get(0);
            }
        } else {
            return null;
        }
    }    
    
    @Transactional(readOnly=true)
    public boolean isMdcActiveChannels(Long mdcId) {
        return mdcChannelCustomMapper.getActiveMdcChannelCountByMdcId(mdcId) > 0;
    }

    @Transactional(readOnly=false)
    @PreAuthorize("hasRole('mm_mdc_admin')")
    public void saveMdc(Mdc mdc) throws ValidationException, ServiceException {
        ServerValidatorUtil.getInstance().validateDataForValidationMessages(mdc);
        
        //Check for duplicate name
        Mdc existing = (Mdc) getMdcByName(mdc.getName());
        if (existing != null && !existing.getId().equals(mdc.getId())) {
            throw new ValidationException(new ValidationMessage("meter.mdc.name.duplicate", new String[]{mdc.getName()}, true));
        }
        
        //check for duplicate value
        existing = (Mdc) getMdcByValue(mdc.getValue());
        if (existing != null && !existing.getId().equals(mdc.getId())) {
            throw new ValidationException(new ValidationMessage("meter.mdc.value.duplicate", new String[]{mdc.getValue()}, true));
        }
         
        if (mdc.getId() == null) {
            if (mdcMapper.insert(mdc) != 1) {
                throw new ValidationException(new ValidationMessage("error.save", new String[]{"meter.mdc.name"}, true));
            }
        } else {
            if (mdcMapper.updateByPrimaryKey(mdc) != 1) {
                throw new ValidationException(new ValidationMessage("error.save", new String[]{"meter.mdc.name"}, true));
            }
        }
    }
    
    public void setMdcMapper(MdcMapper mdcMapper) {
        this.mdcMapper = mdcMapper;
    }
    
    public void setMdcChannelCustomMapper(MdcChannelCustomMapper mdcChannelCustomMapper) {
        this.mdcChannelCustomMapper = mdcChannelCustomMapper;
    }
}
