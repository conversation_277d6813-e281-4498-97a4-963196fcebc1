package za.co.ipay.metermng.server.importfile;

import java.io.File;

import javax.servlet.ServletContext;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;

import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.multipart.MultipartFile;

import za.co.ipay.gwt.common.server.util.ExposedReloadableResourceBundleMessageSource;
import za.co.ipay.gwt.common.shared.exception.NoCurrentUserException;
import za.co.ipay.gwt.common.shared.exception.ServiceException;
import za.co.ipay.metermng.fileimport.DefaultFileImportService;
import za.co.ipay.metermng.fileimport.exceptions.AlreadyUploadedException;
import za.co.ipay.metermng.mybatis.generated.model.ImportFile;
import za.co.ipay.metermng.mybatis.generated.model.ImportFileType;
import za.co.ipay.metermng.server.mybatis.service.ImportFileDataService;
import za.co.ipay.metermng.shared.MeterMngStatics;
import za.co.ipay.metermng.shared.dto.user.MeterMngUser;

@Controller
@RequestMapping(value = "/**/secure/importfileservlet.do")
public class ImportFileController {

    @Autowired
    public ExposedReloadableResourceBundleMessageSource importMessageSource;
    @Autowired
    private ServletContext servletContext;
    @Autowired
    private DefaultFileImportService defaultFileImportService;
    @Autowired
    private ImportFileDataService importFileDataService;
    private Logger logger = Logger.getLogger(ImportFileController.class.getName());

    public void setImportMessageSource(ExposedReloadableResourceBundleMessageSource importMessageSource) {
        this.importMessageSource = importMessageSource;
    }

    @RequestMapping(method = RequestMethod.POST)
    public @ResponseBody String handleFileUpload(@RequestParam("fileTypeName") String fileTypeName, 
                            @RequestParam("enableAccessGroups") boolean enableAccessGroups,
                            @RequestPart("file") MultipartFile file) {

        String fn = file.getOriginalFilename();
        logger.info("**** Uploading file: " + fn + " of filetype value=" + fileTypeName);


        if (!file.isEmpty()) {
            try {

                //RC temp start copy filename so can get to folder ... sort out later whether can get folder from server through the browser
                String filename = file.getOriginalFilename().replaceAll("/", "\\/");
                filename = filename.substring(filename.lastIndexOf("\\") + 1);
                String folder = copyFileGetFolder(file, filename);
                //RC temp end   copy filename so can get to folder ... sort out later whether can get folder from server through the browser  

                ImportFileType importFileType = importFileDataService.getImportFileTypeByName(fileTypeName);
                if (importFileType == null) {
                    return "EXCEPTION:import.upload.filetype.error";
                }

                ImportFile importFile = defaultFileImportService.uploadFile(getUser().getUserName(), getUser().getSessionGroupId(), importFileType, folder, filename);
                //NOTE: If importfile has a first line with actionParams, the necessary implementation of the upload will store this on the import_file table
                //      Also if this is a upload with only params and no data, the implementation will handle that 

                int numFailedUpload = importFileDataService.getNumUploadUnSuccessful(importFile.getId());
                String hasActionParams = importFileType.isHasActionParams() ? "true" : "false";
                return "success;" + numFailedUpload + ";hasActionParams=;" + hasActionParams;

            } catch (AlreadyUploadedException e) {
                if (enableAccessGroups) {
                    return "EXCEPTION:import.upload.file.already.uploaded.group";
                } else {
                    return "EXCEPTION:import.upload.file.already.uploaded";
                }
            } catch (Exception e) {
                logger.error("**** Error uploading file:", e);
                return "EXCEPTION:import.upload.file.error" + ";" + e.getMessage();
            }
        } else {
            return "EXCEPTION:import.upload.file.none";
        }
    }

    protected MeterMngUser getUser() throws ServiceException {
        HttpSession httpSession = getSession();
        if (httpSession != null) {
            MeterMngUser user = (MeterMngUser) httpSession.getAttribute(MeterMngStatics.METER_MNG_USER_ATTR);
            if (user == null) {
                logger.error("**** No current user in session");
                throw new NoCurrentUserException("error.session.user.none");
            } else {
                return user;
            }
        } else {
            logger.error("**** No current session");
            throw new NoCurrentUserException("error.session.none");
        }
    }

    private HttpSession getSession() {
        HttpServletRequest request = null;
        ServletRequestAttributes requestAttributes = (ServletRequestAttributes) RequestContextHolder.currentRequestAttributes();
        if (requestAttributes != null) {
            request = requestAttributes.getRequest();
            return request.getSession(false);
        } else {
            return null;
        }

    }

    //RC temp method to get a dir other than /fakepath
    private String copyFileGetFolder(MultipartFile file, String filename) throws Exception {

        File dir = new File((String) servletContext.getInitParameter("baseFolder"));
        try {
            if (!dir.mkdirs()) {
                if (!dir.isDirectory()) {
                    return null;
                }
            }
        } catch (Exception e) {
            logger.debug("**** EXCEPTION creating directory=" + e.getMessage());
            return null;
        }

        File copyFile = new File(dir, filename);
        file.transferTo(copyFile);
        logger.info("**** Filename copyFile.getPath()=" + copyFile.getPath());
        //return copyFile.getPath();
        return dir.getPath();
    }
}



