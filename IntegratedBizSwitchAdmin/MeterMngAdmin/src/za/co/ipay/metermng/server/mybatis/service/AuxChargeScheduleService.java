package za.co.ipay.metermng.server.mybatis.service;

import java.util.List;

import org.apache.ibatis.session.RowBounds;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;

import za.co.ipay.gwt.common.shared.exception.ServiceException;
import za.co.ipay.metermng.datatypes.RecordStatus;
import za.co.ipay.metermng.mybatis.generated.mapper.AuxAccountMapper;
import za.co.ipay.metermng.mybatis.generated.mapper.AuxChargeScheduleMapper;
import za.co.ipay.metermng.mybatis.generated.model.AuxAccount;
import za.co.ipay.metermng.mybatis.generated.model.AuxAccountExample;
import za.co.ipay.metermng.mybatis.generated.model.AuxChargeSchedule;
import za.co.ipay.metermng.mybatis.generated.model.AuxChargeScheduleExample;

public class AuxChargeScheduleService {
    
    private AuxChargeScheduleMapper auxChargeScheduleMapper;
    private AuxAccountMapper auxAccountMapper;

    @Transactional(readOnly=true)
    public List<AuxChargeSchedule> getAllAuxChargeSchedules(Long currentGroupId) {
        AuxChargeScheduleExample example = new AuxChargeScheduleExample();
        AuxChargeScheduleExample.Criteria criteria = example.createCriteria();
        criteria.andRecordStatusNotEqualTo(RecordStatus.DEL)
                .andAccountSpecificEqualTo(Boolean.FALSE);
        if (currentGroupId != null) {
            criteria.andGenGroupIdEqualTo(currentGroupId);
        } else {
            criteria.andGenGroupIdIsNull();
        }
        List<AuxChargeSchedule> auxChargeScheduleList = auxChargeScheduleMapper.selectByExample(example);
        return auxChargeScheduleList;
    }
    
	@Transactional(readOnly = true)
	public List<AuxChargeSchedule> getAllActiveAuxChargeSchedules(Long currentGroupId) {
		AuxChargeScheduleExample example = new AuxChargeScheduleExample();
		AuxChargeScheduleExample.Criteria criteria = example.createCriteria();
		criteria.andRecordStatusEqualTo(RecordStatus.ACT);
		criteria.andGenGroupIdEqualTo(currentGroupId);
		List<AuxChargeSchedule> auxChargeScheduleList = auxChargeScheduleMapper.selectByExample(example);
		return auxChargeScheduleList;
	}

    @Transactional(readOnly=true)
    public List<AuxAccount> getAllAuxAccounts() {
        AuxAccountExample example = new AuxAccountExample();
        example.createCriteria().andRecordStatusNotEqualTo(RecordStatus.DEL);
        List<AuxAccount> auxAccountList = auxAccountMapper.selectByExample(example);
        return auxAccountList;
    }
    
    @Transactional
    public AuxChargeSchedule getAuxChargeScheduleByName(String name) {
        AuxChargeScheduleExample example = new AuxChargeScheduleExample();
        example.createCriteria().andScheduleNameEqualTo(name);
        List<AuxChargeSchedule> auxChargeScheduleList = auxChargeScheduleMapper.selectByExample(example);
        if (auxChargeScheduleList.isEmpty()) {
            return null;
        }
        return auxChargeScheduleList.get(0);
    }
    
    @Transactional
    @PreAuthorize("hasRole('mm_aux_schedule_admin')")
    public AuxChargeSchedule updateAuxChargeSchedule(AuxChargeSchedule auxChargeSchedule) throws ServiceException {
        if(auxChargeSchedule.getId() == null) {
            if (auxChargeScheduleMapper.insert(auxChargeSchedule) != 1) {
                throw new ServiceException("auxchargeschedule.error.save");
            }
        } else {
            if (auxChargeScheduleMapper.updateByPrimaryKey(auxChargeSchedule) != 1) {
                throw new ServiceException("auxchargeschedule.error.save");
            }
        }
        return auxChargeSchedule;
    }

    @Transactional(readOnly=true)
    public boolean isExistingAuxChargeSchedulesWithGroup() {
        AuxChargeScheduleExample example = new AuxChargeScheduleExample();
        example.createCriteria().andGenGroupIdIsNotNull();
        RowBounds rowBounds = new RowBounds(RowBounds.NO_ROW_OFFSET, 1);
        List<AuxChargeSchedule> schedules = auxChargeScheduleMapper.selectByExampleWithRowbounds(example, rowBounds);
        if (schedules.size() == 1) {
            return true;
        } else {
            return false;
        }
    }
    
    public void setAuxAccountMapper(AuxAccountMapper auxAccountMapper) {
        this.auxAccountMapper = auxAccountMapper;
    }
    
    public void setAuxChargeScheduleMapper(AuxChargeScheduleMapper auxChargeScheduleMapper) {
        this.auxChargeScheduleMapper = auxChargeScheduleMapper;
    }
}