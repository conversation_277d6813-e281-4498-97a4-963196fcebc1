package za.co.ipay.metermng.server.task.balancing;

import java.io.File;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Locale;

import org.apache.log4j.Logger;
import org.springframework.context.support.AbstractMessageSource;
import org.springframework.context.support.DefaultMessageSourceResolvable;

import za.co.ipay.gwt.common.shared.exception.ServiceException;
import za.co.ipay.gwt.common.shared.validation.ValidateUtil;
import za.co.ipay.metermng.mybatis.custom.model.MeterDto;
import za.co.ipay.metermng.mybatis.generated.model.MeterReadingType;
import za.co.ipay.metermng.mybatis.generated.model.TaskSchedule;
import za.co.ipay.metermng.server.servlet.ExportDataUtil;
import za.co.ipay.metermng.server.task.BaseTask;
import za.co.ipay.metermng.shared.MeterMngStatics;
import za.co.ipay.metermng.shared.dto.ItemValuePairDto;
import za.co.ipay.metermng.shared.dto.meter.EnergyBalancingDto;
import za.co.ipay.metermng.shared.dto.meter.MeterReadingsDto;
import za.co.ipay.metermng.shared.dto.schedule.ScheduledTaskDto;
import za.co.ipay.metermng.shared.dto.user.UserData;
import za.co.ipay.metermng.shared.schedule.EnergyBalancingVarianceTaskData;
import za.co.ipay.metermng.shared.schedule.EnergyBalancingVarianceTaskUtil;
import za.co.ipay.utils.files.CsvWriter;

/**
 * EnergyBalancingVarianceTask checks the variance between a super meter and its sub-meters, over a specific time period
 * and if the variance is greater than the task's specified variance, the meter readings are sent to the task's users.
 * 
 * <AUTHOR>
 */
public class EnergyBalancingVarianceTask extends BaseTask {

    // Note: don't change this class' package and name unless you also update the corresponding TaskClass row in the db
    // and the db static data scripts - thank you

    private static Logger logger = Logger.getLogger(EnergyBalancingVarianceTask.class);

    @Override
    public void execute(TaskSchedule taskSchedule, ScheduledTaskDto scheduledTaskDto) throws ServiceException {
        EnergyBalancingVarianceTaskData data = EnergyBalancingVarianceTaskUtil.getTaskClassContents(scheduledTaskDto
                .getScheduledTask().getTaskContents());
        String superMeterNumber = data.getSuperMeterNumber();
        if (superMeterNumber.equals("*")) {
            List<MeterDto> superMeters = meterService.getSuperMeters();
            for (MeterDto meter : superMeters) {
                checkSuperMeter(taskSchedule, scheduledTaskDto, meter, data);
            }
        } else {
            checkSuperMeter(taskSchedule, scheduledTaskDto, superMeterNumber, data);
        }
    }

    private void checkSuperMeter(TaskSchedule taskSchedule, ScheduledTaskDto scheduledTaskDto, String superMeterNumber,
            EnergyBalancingVarianceTaskData data) throws ServiceException {
        MeterDto superMeter = meterService.getMeterDto(superMeterNumber);
        if (superMeter != null) {
            checkSuperMeter(taskSchedule, scheduledTaskDto, superMeter, data);
        } else {
            logger.error("TaskSchedule: " + scheduledTaskDto.getScheduledTask().getScheduledTaskName()
                    + " no meter found for: " + superMeterNumber);
        }
    }

    private void checkSuperMeter(TaskSchedule taskSchedule, ScheduledTaskDto scheduledTaskDto, MeterDto superMeter,
            EnergyBalancingVarianceTaskData data) throws ServiceException {
        MeterReadingType type = meterService.getMeterReadingTypeByValue(data.getMeterReadingType());
        if (type != null) {
            Date[] startAndEnd = getDateRange(data.getNumber(), data.getMinsOrHours());
            if (startAndEnd != null) {
                Date start = startAndEnd[0];
                Date end = startAndEnd[1];
                Double variance = getVariance(data.getVariance());
                logger.info("Checking energyBalancingVariance for superMeter:" + superMeter.getNumber()
                          + " meterReadingType:" + type.getName()
                          + " numbers:" + data.getNumber()+" units: "+data.getMinsOrHours() 
                          + " start:" + start + " end:" + end + " variance: " + variance);
                EnergyBalancingDto dto = meterService.checkEnergyBalancingMeter(superMeter, type.getId(), start, end, variance);
                if (dto != null) {
                    // Get the super meter and sub meters meter readings
                    MeterReadingsDto meterReadings = meterService.getMeterBalancingReadings(superMeter.getId(), type.getId(), start, end);

                    // locale
                    Locale locale = getLocale();

                    // Export as CSV file
                    CsvWriter writer = ExportDataUtil.getMeterBalancingReadings(messageSource, formatSource, locale, meterReadings, dto.getVariation());
                    File csvFile = writeCsvFile(superMeter.getNumber(), type, writer, locale);
                    logger.info("Wrote CSV file: "+csvFile.getAbsolutePath());

                    // Email to task users and/or customer
                    ItemValuePairDto fromNameAndEmail = getFromEmailDets();
                    String fromName = fromNameAndEmail.getItem();
                    String fromEmail = fromNameAndEmail.getValue();
                    String subject = getSubject(taskSchedule.getTaskScheduleName(), scheduledTaskDto.getScheduledTask().getScheduledTaskName(), locale);
                    String message = getMessage(taskSchedule, scheduledTaskDto, superMeter.getNumber(), type, start, end, locale);
                    List<UserData> users = scheduledTaskDto.getUsers();
                    if (scheduledTaskDto.getCustomer() != null 
                            && ValidateUtil.isNotNullOrBlank(scheduledTaskDto.getCustomer().getNotifyEmail())) {
                        users.add(new UserData(scheduledTaskDto.getCustomer().getNotifyEmail()));
                        logger.debug("Added customer for task notify: "+scheduledTaskDto.getCustomer().getNotifyEmail());
                    }
                    try {
                        emailService.sendEmail(fromName, fromEmail, scheduledTaskDto.getUsers(), subject, message, new File[] { csvFile });
                    } catch (Exception e) {
                        logger.error("Error sending email: ", e);
                        throw new ServiceException("Unable to send scheduledTask email: "
                                + scheduledTaskDto.getScheduledTask().getScheduledTaskName());
                    }
                } else {
                    logger.info("No energyBalancingVariance found for superMeter: " + superMeter.getNumber() + " variance: "
                            + data.getVariance());
                }
            } else {
                logger.error("TaskSchedule: " + scheduledTaskDto.getScheduledTask().getScheduledTaskName()
                        + " unknown times to use for numbers: " + data.getNumber()+" units: "+data.getMinsOrHours());
            }
        } else {
            logger.error("TaskSchedule: " + scheduledTaskDto.getScheduledTask().getScheduledTaskName()
                    + " no meterReadingType found for: " + data.getMeterReadingType());
        }
    }

    private Double getVariance(String value) throws ServiceException {
        try {
            return Double.valueOf(value);
        } catch (NumberFormatException e) {
            throw new ServiceException("Invalid variance: " + value);
        }
    }

    private Date[] getDateRange(String numbers, String units) {
        try {
            Calendar now = Calendar.getInstance();
            Date end = now.getTime();
            if (MeterMngStatics.HOURS_VALUE.equals(units)) {
                now.add(Calendar.HOUR_OF_DAY, Integer.valueOf(numbers) * -1);
            } else {
                now.add(Calendar.MINUTE, Integer.valueOf(numbers) * -1);
            }
            Date start = now.getTime();
            return new Date[] { start, end };
        } catch (NumberFormatException e) {
            logger.error("Invalid numbers: " + numbers+" units: "+units);
        }
        return null;
    }

    private String getSubject(String taskScheduleName, String scheduledTaskName, Locale locale) {
        StringBuilder sb = new StringBuilder();
        sb.append(messageSource.getMessage(new DefaultMessageSourceResolvable("scheduledtask.email.subject.taskschedule"), locale));
        sb.append(taskScheduleName);
        sb.append(messageSource.getMessage(new DefaultMessageSourceResolvable("scheduledtask.email.subject.scheduledtask"), locale));
        sb.append(scheduledTaskName);
        return sb.toString();
    }

    private String getMessage(TaskSchedule taskSchedule, ScheduledTaskDto scheduledTaskDto, String meterNumber,
            MeterReadingType readingType, Date start, Date end, Locale locale) {
        StringBuilder sb = new StringBuilder();

        sb.append(messageSource.getMessage(new DefaultMessageSourceResolvable("scheduledtask.email.message.taskschedule"), locale));
        sb.append(taskSchedule.getTaskScheduleName());
        sb.append(LINE_SEPARATOR);

        sb.append(messageSource.getMessage(new DefaultMessageSourceResolvable("scheduledtask.email.message.scheduledtask"), locale));
        sb.append(scheduledTaskDto.getScheduledTask().getScheduledTaskName());
        sb.append(LINE_SEPARATOR);

        sb.append(messageSource.getMessage(new DefaultMessageSourceResolvable("scheduledtask.email.message.supermeter"), locale));
        sb.append(meterNumber);
        sb.append(LINE_SEPARATOR);

        SimpleDateFormat sdf = new SimpleDateFormat(formatSource.getMessage(new DefaultMessageSourceResolvable("datetime.pattern"), locale));

        sb.append(messageSource.getMessage(new DefaultMessageSourceResolvable("scheduledtask.email.message.start"), locale));
        sb.append(sdf.format(start));
        sb.append(LINE_SEPARATOR);

        sb.append(messageSource.getMessage(new DefaultMessageSourceResolvable("scheduledtask.email.message.end"), locale));
        sb.append(sdf.format(end));
        sb.append(LINE_SEPARATOR);

        sb.append(messageSource.getMessage(new DefaultMessageSourceResolvable("scheduledtask.email.message"), locale));
        return sb.toString();
    }

    @Override
    public String getFileName(AbstractMessageSource messageSource, Locale locale, String meterNumber,
            MeterReadingType readingType, String fileExtension) {
        return ExportDataUtil.getEnergyBalancingFileName(messageSource, locale, meterNumber, readingType, fileExtension);
    }
}
