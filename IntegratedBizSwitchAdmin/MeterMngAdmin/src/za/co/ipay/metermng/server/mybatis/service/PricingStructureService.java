package za.co.ipay.metermng.server.mybatis.service;

import java.beans.Introspector;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import org.apache.ibatis.session.RowBounds;
import org.apache.log4j.Logger;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;

import za.co.ipay.gwt.common.server.validation.ServerValidatorUtil;
import za.co.ipay.gwt.common.shared.exception.ServiceException;
import za.co.ipay.gwt.common.shared.exception.ValidationException;
import za.co.ipay.gwt.common.shared.exception.ValidationMessage;
import za.co.ipay.ipayxml.metermng.TariffCalcReqMessage;
import za.co.ipay.ipayxml.metermng.TariffCalcResMessage;
import za.co.ipay.metermng.datatypes.CycleE;
import za.co.ipay.metermng.datatypes.CyclicChargeCalcAtE;
import za.co.ipay.metermng.datatypes.RecordStatus;
import za.co.ipay.metermng.integration.TariffBulkUpload.TariffPsJsonRecordConverter;
import za.co.ipay.metermng.ipayxml.IpayXmlMessageService;
import za.co.ipay.metermng.mybatis.custom.mapper.TariffSupportMapper;
import za.co.ipay.metermng.mybatis.custom.model.TariffWithCalcClass;
import za.co.ipay.metermng.mybatis.custom.model.TariffWithCalcClassAndPsInfo;
import za.co.ipay.metermng.mybatis.generated.mapper.PricingStructureMapper;
import za.co.ipay.metermng.mybatis.generated.mapper.TariffClassMapper;
import za.co.ipay.metermng.mybatis.generated.mapper.TariffMapper;
import za.co.ipay.metermng.mybatis.generated.mapper.UpPricingStructureMapper;
import za.co.ipay.metermng.mybatis.generated.model.PricingStructure;
import za.co.ipay.metermng.mybatis.generated.model.PricingStructureExample;
import za.co.ipay.metermng.mybatis.generated.model.Tariff;
import za.co.ipay.metermng.mybatis.generated.model.TariffClass;
import za.co.ipay.metermng.mybatis.generated.model.TariffClassExample;
import za.co.ipay.metermng.mybatis.generated.model.TariffExample;
import za.co.ipay.metermng.mybatis.generated.model.UpPricingStructure;
import za.co.ipay.metermng.mybatis.generated.model.UpPricingStructureExample;
import za.co.ipay.metermng.server.mybatis.mapper.ICalendarPricingStructureMapper;
import za.co.ipay.metermng.service.pricingstructure.PricingStructureCustomService;
import za.co.ipay.metermng.shared.ChannelCompatibilityE;
import za.co.ipay.metermng.shared.IpayResponseData;
import za.co.ipay.metermng.shared.dto.PSDto;
import za.co.ipay.metermng.shared.dto.pricing.PricingStructureDto;
import za.co.ipay.metermng.shared.dto.tariff.TariffPanelDto;
import za.co.ipay.metermng.shared.integration.tariffexport.TariffExportObj;
import za.co.ipay.metermng.shared.integration.tariffexport.TariffPsImportRecord;
import za.co.ipay.metermng.shared.tariff.ITariffData;
import za.co.ipay.metermng.shared.tariff.ITariffInitData;
import za.co.ipay.metermng.shared.tariff.TariffWithData;
import za.co.ipay.metermng.shared.tariff.cyclic.CyclicChargeData;
import za.co.ipay.metermng.shared.tariff.register.BillingDetBlockData;
import za.co.ipay.metermng.shared.tariff.register.RegisterReadingCalcContentsParser;
import za.co.ipay.metermng.shared.tariff.register.RegisterReadingThinData;
import za.co.ipay.metermng.tariff.ITariffCalculator;
import za.co.ipay.metermng.tariff.ITariffDataService;
import za.co.ipay.utils.CalendarUtils;

public class PricingStructureService implements ApplicationContextAware {

    private static final Logger logger = Logger.getLogger(PricingStructureService.class);
    private static DateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    private PricingStructureMapper pricingStructureMapper;
    private UpPricingStructureMapper upPricingStructureMapper;
    private TariffMapper tariffMapper;
    private TariffClassMapper tariffClassMapper;
    private ApplicationContext context;
    private IpayXmlMessageService ipayXmlMessageService;
    private ICalendarPricingStructureMapper icalendarPricingStructureMapper;
    private TariffSupportMapper tariffSupportMapper;

    private MdcChannelService mdcChannelService;
    private PricingStructureCustomService pricingStructureCustomService;

    private PricingStructureExample generateFilteredPricingStructureExample(Long currentGroupId,int filterTableColIndx, String filterString) {
        PricingStructureExample example = new PricingStructureExample();
        PricingStructureExample.Criteria criteria = example.createCriteria();
        criteria.andRecordStatusNotEqualTo(RecordStatus.DEL);
        criteria.andGenGroupIdEqualTo(currentGroupId);

        if ((filterTableColIndx != -1) && (filterString != null) && !filterString.equals("")) {

            switch (filterTableColIndx) {
            case 0: //name

                criteria.andNameLikeInsensitive("%" + filterString + "%");
                break;
            case 1: //description
                criteria.andDescriptionLikeInsensitive("%" + filterString + "%");
                break;
            case 2: //record status
                if (filterString.equals(RecordStatus.ACT.toString())) {
                    criteria.andRecordStatusEqualTo(RecordStatus.ACT);
                } else if (filterString.equals(RecordStatus.DAC.toString())) {
                    criteria.andRecordStatusEqualTo(RecordStatus.DAC);
                }
                break;
            case 4: //service resource
            	criteria.andServiceResourceIdEqualTo(Long.valueOf(filterString));
            	break;
            case 5: //meter type
            	criteria.andMeterTypeIdEqualTo(Long.valueOf(filterString));
            	break;
            case 6: //payment mode
            	criteria.andPaymentModeIdEqualTo(Long.valueOf(filterString));
            	break;
            default:
                break;
            }
        }
        return example;
    }

    /**
     * If UP activation date is in the future then the PS will also be starting in the future.
     * Hence the UP does not have a PS that is currently running.
     * @param usagePointId
     */
    @Transactional(readOnly=true)
    public UpPricingStructure getCurrentOrFirstUsagePointUpPricingStructure(Long usagePointId) {
        List<UpPricingStructure> uppsList = tariffSupportMapper.selectCurrentAndFutureUPPricingStructures(usagePointId, new Date());
        if (!uppsList.isEmpty()) {
            return uppsList.get(0);
        }
        return null;
    }

    /**
     * If UP activation date is in the future then the PS will also be starting in the future.
     * Hence the UP does not have a PS that is currently running.
     * @param usagePointId
     */
    @Transactional(readOnly = true)
    public PricingStructure getCurrentOrFirstUsagePointPricingStructure(Long upId) {
        UpPricingStructure upPricingStructure = getCurrentOrFirstUsagePointUpPricingStructure(upId);
        return pricingStructureMapper.selectByPrimaryKey(upPricingStructure.getPricingStructureId());
    }

    @Transactional(readOnly = true)
    public Map<String, UpPricingStructure> getCurrentAndFutureUPPricingStructures(Long usagePointId) {
        Map<String, UpPricingStructure> upps = new HashMap<>();
        List<UpPricingStructure> uppsList = tariffSupportMapper.selectCurrentAndFutureUPPricingStructures(usagePointId, new Date());
        if (!uppsList.isEmpty()) {
            upps.put(UsagePointService.CURRENT_PS, uppsList.get(0));
            if (uppsList.size() > 1) {
                upps.put(UsagePointService.FUTURE_PS, uppsList.get(1));
            }
        }
        return upps;
    }

    @Transactional(readOnly = true)
    public List<UpPricingStructure> selectAllUPPricingStructures(Long upId) {
        UpPricingStructureExample example = new UpPricingStructureExample();
        example.createCriteria().andUsagePointIdEqualTo(upId);
        example.setOrderByClause("start_date asc");
        return upPricingStructureMapper.selectByExample(example);
    }

    @Transactional(readOnly = true)
    public List<PricingStructure> getPSsWithRunningTariff(Long currentGroupId) {
        return getPSsWithRunningTariffForDate(currentGroupId, new Date());
    }

    @Transactional(readOnly = true)
    public List<PricingStructure> getPSsWithRunningTariffForDate(Long currentGroupId, Date startDate) {
        return tariffSupportMapper.selectPSsWithRunningTariff(currentGroupId, startDate);
    }

    @Transactional(readOnly = true)
    public List<PricingStructure> getPSsWithRunningTariffByMeterModel(Long currentGroupId, Long serviceResourceId, Long meterTypeId, ArrayList<Long> paymentModeIds) {
        return tariffSupportMapper.selectPSsWithRunningTariffByMeterModel(currentGroupId, serviceResourceId, meterTypeId, paymentModeIds, new Date());
    }

    @Transactional(readOnly=true)
    public Date getPricingStructureStartDate(Long pricingStructureId) {
        return selectTariffsForPricingStructure(pricingStructureId, "asc").get(0).getStartDate();
    }

    @Transactional(readOnly = true)
    public List<Long> getPricingStructuresIdsForFilters(Long currentGroupId, int filterTableColIndx,
            String filterString) {
        return pricingStructureMapper
                .selectByExample(
                        generateFilteredPricingStructureExample(currentGroupId, filterTableColIndx, filterString))
                .stream().mapToLong(pricingStructure -> pricingStructure.getId()).boxed().collect(Collectors.toList());
    }

    @Transactional(readOnly=true)
    public ArrayList<PricingStructureDto> getPricingStructures(int startRow, int pageSize, String sortField, int filterTableColIndx,
            String filterString, boolean isAscending, Long currentGroupId, PricingStructureDto lastAdded) throws ServiceException {

        String fText = filterString;
        if (lastAdded != null) {
            fText = "";
        }
        PricingStructureExample example = generateFilteredPricingStructureExample(currentGroupId, filterTableColIndx, fText);
        example.setOrderByClause(getOrderColumn(sortField, isAscending));

        RowBounds rowBounds = new RowBounds(startRow, pageSize);
        List<PricingStructure> ps = pricingStructureMapper.selectByExampleWithRowbounds(example, rowBounds);

        int newStartRow = startRow;

        if (lastAdded != null) {
            newStartRow = 0;
            int dataSize = pricingStructureMapper.countByExample(example);
            foundMatch:
            while (newStartRow < dataSize) {
                rowBounds = new RowBounds(newStartRow, pageSize);
                ps = pricingStructureMapper.selectByExampleWithRowbounds(example, rowBounds);

                for (PricingStructure psDto : ps) {
                    if (psDto.getName().equals(lastAdded.getPricingStructure().getName())) {
                        break foundMatch;
                    }
                }
                newStartRow += pageSize;
            }

        }

        TariffExample tariffExample = null;
        ArrayList<PricingStructureDto> dtos = new ArrayList<PricingStructureDto>(ps.size());
        PricingStructureDto dto = null;
        for(PricingStructure p : ps) {
            dto = new PricingStructureDto();
            dto.setPricingStructure(p);
            dto.setPricingStructurePagerIndex(newStartRow++);
            tariffExample = new TariffExample();
            tariffExample.createCriteria()
                         .andPricingStructureIdEqualTo(p.getId())
                         .andRecordStatusNotEqualTo(RecordStatus.DEL);
            int count = tariffMapper.countByExample(tariffExample);
            dto.setTariffCount(count);

            dtos.add(dto);
        }
        return dtos;
    }

    private String getOrderColumn(String sortField, boolean isAscending) {
        String orderColumn = "pricing_structure_name";
        if (sortField != null && !sortField.trim().equals("")) {
            if ("name".equals(sortField)) {
                orderColumn = "pricing_structure_name";
            } else if ("status".equals(sortField)) {
                orderColumn = "record_status";
            } else if ("description".equals(sortField)) {
                orderColumn = "pricing_structure_description";
            } else if ("serviceResource".equals(sortField)) {
                orderColumn = "service_resource_id";
            } else if ("meterType".equals(sortField)) {
                orderColumn = "meter_type_id";
            } else if ("paymentMode".equals(sortField)) {
                orderColumn = "payment_mode_id";
            }
        }
        return orderColumn + " " + getOrder(isAscending);
    }

    private String getOrder(boolean isAscending) {
        if (isAscending) {
            return "asc";
        } else {
            return "desc";
        }
    }

    @Transactional(readOnly=true)
    public PricingStructure getPricingStructure(Long pricingStructureId) {
        return pricingStructureMapper.selectByPrimaryKey(pricingStructureId);
    }

    @Transactional(readOnly=true)
    public PricingStructure getPricingStructureByName(String name, Long genGroupId) {
        PricingStructureExample example = new PricingStructureExample();
        example.createCriteria().andNameEqualTo(name).andGenGroupIdEqualTo(genGroupId);
        List<PricingStructure> ps = pricingStructureMapper.selectByExample(example);
        if (ps.isEmpty()) {
            return null;
        } else {
            return ps.get(0);
        }
    }

    @Transactional(readOnly=true)
    public ArrayList<TariffClass> getTariffClasses(Long serviceResourceId, Long meterTypeId, Long paymentModeId) {
        TariffClassExample example = new TariffClassExample();
        example.createCriteria().andServiceResourceIdEqualTo(serviceResourceId)
                                .andMeterTypeIdEqualTo(meterTypeId)
                                .andPaymentModeIdEqualTo(paymentModeId);
        return new ArrayList<TariffClass>(tariffClassMapper.selectByExample(example));
    }

    @Transactional(readOnly=true)
    public List<TariffWithData> getTariffsForPricingStructure(PricingStructure pricingStructure) {
        List<Tariff> tariffList = selectTariffsForPricingStructure(pricingStructure.getId());
        List<TariffWithData> tariffWithDataList = new ArrayList<TariffWithData>(tariffList.size());
        for (Tariff t : tariffList) {
            ITariffData tariffData = null;
            ITariffDataService dataService;
            try {
                dataService = getTariffDataService(t);
                if (dataService != null) {
                    logger.info("Getting tariffData for id:"+t.getId());
                    tariffData = dataService.getTariffData(pricingStructure, t);
                }
            } catch (Exception e) {
                logger.error("Could not get tariffDataService", e);
            }
            tariffWithDataList.add(new TariffWithData(t, tariffData));
        }

        return tariffWithDataList;
    }

    public List<Tariff> selectTariffsForPricingStructure(long pricingStructureId) {
        return selectTariffsForPricingStructure(pricingStructureId, "desc");
    }

    public List<Tariff> selectTariffsForPricingStructure(long pricingStructureId, String sortOrder) {
        TariffExample example = new TariffExample();
        example.createCriteria().andRecordStatusNotEqualTo(RecordStatus.DEL).andPricingStructureIdEqualTo(pricingStructureId);
        example.setOrderByClause("start_date " + sortOrder);
        List<Tariff> tariffList = tariffMapper.selectByExample(example);
        return tariffList;
    }

    public TariffPanelDto getCurrentTariffDtoByPricingStructureId(Long pricingStructureId) {
        TariffWithCalcClass t = tariffSupportMapper.findCurrentTariffByPricingStructureId(pricingStructureId, new Date());
        PricingStructure pricingStructure = getPricingStructure(pricingStructureId);

        ITariffData tariffData = null;
        ITariffDataService dataService;
        try {
            dataService = getTariffDataService(t);
            if (dataService != null) {
                logger.info("Getting tariffData for id:"+t.getId());
                tariffData = dataService.getTariffData(pricingStructure, t);
            } else {
                logger.info("dataService IS null");
            }
        } catch (Exception e) {
            logger.error("Could not get tariffDataService", e);
        }

        TariffPanelDto dto = new TariffPanelDto();
        dto.setPricingStructure(pricingStructure);
        dto.setTariffClass(getTariffClass(t.getTariffClassId()));
        dto.setTariffWithData(new TariffWithData(t, tariffData));
        return dto;
    }


    @Transactional
    @PreAuthorize("hasRole('mm_pricing_struct_admin')")
    public PricingStructure savePricingStructure(PricingStructure pricingStructure) throws ValidationException, ServiceException {

        ServerValidatorUtil.getInstance().validateDataForValidationMessages(pricingStructure);

        PricingStructure existing = getPricingStructureByName(pricingStructure.getName(), pricingStructure.getGenGroupId());
        if (existing != null && !existing.getId().equals(pricingStructure.getId())) {
            throw new ValidationException(new ValidationMessage("pricingstructure.name.duplicate", new String[]{pricingStructure.getName()}, true));
        }

        if (pricingStructure.getId() == null) {
            if (pricingStructureMapper.insert(pricingStructure) != 1) {
                throw new ServiceException("pricingstructure.error.save");
            }
        } else {
            if (pricingStructureMapper.updateByPrimaryKey(pricingStructure) != 1) {
                throw new ServiceException("pricingstructure.error.save");
            }
        }
        return pricingStructure;
    }

    @Transactional
    @PreAuthorize("hasRole('mm_pricing_struct_admin')")
    public ChannelCompatibilityE checkRegReadTariffActiveUpBillingDetAlignment(TariffWithData tariffWithData, String userName, Long userGenGroupId) throws ServiceException {
        //only use this method if already know its a register Reading tariff from the TariffView saveTariff()
        //check if usagePoints have MDC with Channels with unaligned billing dets

        Tariff tariff =  tariffWithData.getTariff();
        ChannelCompatibilityE match = ChannelCompatibilityE.NO_DATA;
        PricingStructure ps = getPricingStructure(tariff.getPricingStructureId());
        if (ps.getRecordStatus().equals(RecordStatus.ACT)) {
            //get billing Det ids covered by this latest tariff
            List<Long> regReadTariffBillingDetIds = getBillingDetIdsFromTariffWithData(tariffWithData);

            //get billing dets in the mdc attached to ACTive UP using this PS
            Set<Long> mdcBillingDetIds = new HashSet<Long>();
            List<Long> bdIds = mdcChannelService.getBillingDetIdsUsedByPricingStructureIdOnActiveUPs(tariff.getPricingStructureId(), userGenGroupId);
            if (bdIds == null || bdIds.isEmpty()) {
                logger.info("checkRegReadTariffActiveUpBillingDetAlignment : NO Active Usage Points with this pricing_structure");
                //return default NO_DATA
            } else {
                mdcBillingDetIds.addAll(bdIds);
                match = checkChannelCompatibility(regReadTariffBillingDetIds, mdcBillingDetIds);
                logger.info("checkRegReadTariffActiveUpBillingDetAlignment returns " + match.toString() + " for tariffId=" + tariff.getId()+ " :" + tariff.getName() + ", Warning displayed to user: " + userName);
            }
        }
        return match;
    }

    public List<Long> getBillingDetIdsFromTariffWithData(TariffWithData tariffWithData) {
        RegisterReadingThinData data = (RegisterReadingThinData) tariffWithData.getTariffData();
        List<Long> regReadTariffBillingDetIds = new ArrayList<>();
        for (BillingDetBlockData billingDetBlockData : data.getBillingDetBlockData()) {
            regReadTariffBillingDetIds.add(billingDetBlockData.getBillingDetPrimaryDto().getId());
        }
        return regReadTariffBillingDetIds;
    }

    @Transactional
    @PreAuthorize("hasRole('mm_pricing_struct_admin')")
    public void saveTariff(TariffWithData tariffWithData) throws ServiceException {
        Tariff tariff = tariffWithData.getTariff();
        boolean isUpdate = tariff.getId() != null;
        ITariffData tariffData = tariffWithData.getTariffData();
        ITariffDataService dataService = null;
        if (tariffData != null) {
            try {
                dataService = getTariffDataService(tariff);
            } catch (Exception e) {
                logger.warn("tariffDataService could not be found", e);
                throw new ServiceException("tariff.error.save");
            }
        }
    
        if(dataService != null) {
            // update calc contents from ITariffData if applicable
            // shifted code to here because need calc_contents for tariff being saved now already (is still "placeholder")   
            dataService.updateCalcContents(tariffWithData);
        }
        //note dataService CAN still be null for much older tariff classes that were not converted to JSON design
        
        boolean midMonthStart = false;
        Date firstOfMonth = CalendarUtils.startOfMonth(tariff.getStartDate());
        if (tariff.getStartDate().compareTo(firstOfMonth) != 0) {
            midMonthStart = true;
        }
        //only RegisterReading can carry Billing cyclic charges
        boolean mayHaveBillingCyclicCharges = false;
        if (getTariffClass(tariff.getTariffClassId()).getCalcClass().contains("RegisterReadingThinTariff")) {
            mayHaveBillingCyclicCharges = true;
        }
        
        //check this new tariff - should not have MONTHLY Billing charges if not starting on a month boundary
        if (midMonthStart && mayHaveBillingCyclicCharges) {
            List<Tariff> tariffList = selectTariffByName(tariff.getName(), tariff.getPricingStructureId());
            Tariff theTariff = tariffList.isEmpty() ? null : tariffList.get(0);
            //check if haven't changed BillingCycle makeup from what was before on this tariff for a midMonth startDate 
            validateMidMonthTariffBillingCyclicChange(theTariff, tariff);
        }

        // Get and Sort tariffs based on startDate then Update the affected endDates
        List<Tariff> existingTariffs = selectTariffsForPricingStructure(tariff.getPricingStructureId());
        if (existingTariffs != null && existingTariffs.size() > 0) {
            Tariff oldestTariff = existingTariffs.get(existingTariffs.size() - 1);
            List<Tariff> allTariffs = new ArrayList<Tariff>();
            for (Tariff currentTariff : existingTariffs) {
                if (!currentTariff.getId().equals(tariff.getId())
                        && (currentTariff.getEndDate() == null || currentTariff.getEndDate().after(new Date()))) {
                    allTariffs.add(currentTariff);
                }
            }
            allTariffs.add(tariff);
            //sort in ascending startdate order
            Collections.sort(allTariffs, new Comparator<Tariff>() {
                public int compare(Tariff o1, Tariff o2) {
                    return o1.getStartDate().compareTo(o2.getStartDate());
                }
            });

            if (isUpdate && oldestTariff.getId().equals(tariff.getId())) {
                Date earliestPSStartDate = null;
                UpPricingStructureExample example = new UpPricingStructureExample();
                example.createCriteria().andPricingStructureIdEqualTo(tariff.getPricingStructureId());
                example.setOrderByClause("start_date asc");
                List<UpPricingStructure> upPSList = upPricingStructureMapper.selectByExample(example);
                if (upPSList != null && !upPSList.isEmpty()) {
                    earliestPSStartDate = upPSList.get(0).getStartDate();
                }
                Date earliestTariffStartDate = allTariffs.get(0).getStartDate();
                if (earliestPSStartDate != null && earliestPSStartDate.before(earliestTariffStartDate)) {
                    throw new ServiceException("tariff.error.save.up.ps.start.date.conflict", new String[]{(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(earliestPSStartDate))});
                }
            }

            // Update the affected tariffs endDate
            for (int i = 0; i < allTariffs.size(); i++) {
                boolean updateTariff = false;
                Tariff topTarriff = allTariffs.get(i);
                Tariff belowTarriff = null;
                if (i != allTariffs.size() - 1) {
                    belowTarriff = allTariffs.get(i + 1);
                    if (topTarriff.getEndDate() == null || topTarriff.getEndDate().compareTo(belowTarriff.getStartDate()) != 0) {
                        updateTariff = true;
                    }
                } else {
                    updateTariff = true;
                }

                if (updateTariff) {
                    if (topTarriff.getId() != null) {
                        if (belowTarriff != null) {
                            topTarriff.setEndDate(belowTarriff.getStartDate());
                            if (midMonthStart && mayHaveBillingCyclicCharges) {
                                //check billing cyclic change changes if midmonth
                                validateMidMonthTariffBillingCyclicChange(topTarriff, belowTarriff);
                            }
                        } else {
                            topTarriff.setEndDate(null); // Topmost tariff always has endDate null
                        }
                        if (tariffMapper.updateByPrimaryKey(topTarriff) != 1) {
                            throw new ServiceException("tariff.error.save");
                        }
                    } else {
                        if (belowTarriff != null) {
                            tariff.setEndDate(belowTarriff.getStartDate());
                            if (midMonthStart && mayHaveBillingCyclicCharges) {
                                //check billing cyclic change changes if midmonth
                                validateMidMonthTariffBillingCyclicChange(topTarriff, belowTarriff);
                            }
                        } else {
                            tariff.setEndDate(null); // Topmost tariff always has endDate null
                        }
                    }
                }
            }
        }
    
        if (tariff.getName().trim().isEmpty()) {
            logger.debug("Tariff name can't only be a space! >" + tariff.getName() + "<");
            throw new ServiceException("error.field.name.range", new String[] {"1", "100"});
        }
        if (tariff.getId() == null) {
            if (!selectTariffByName(tariff.getName(), tariff.getPricingStructureId()).isEmpty()) {
                logger.debug("Tariff with same name already exists. " + tariff.getName());
                throw new ServiceException("tariff.error.save.duplicate");
            }
            if (tariffMapper.insert(tariff) != 1) {
                throw new ServiceException("tariff.error.save");
            }
        } else {
            List<Tariff> tariffList = selectTariffByName(tariff.getName(), tariff.getPricingStructureId());
            Tariff theTariff = tariffList.isEmpty() ? null : tariffList.get(0);
            if (theTariff != null && !theTariff.getId().equals(tariff.getId())) {
                logger.debug("Tariff with same name already exists. " + tariff.getName());
                throw new ServiceException("tariff.error.tariff.error.save.duplicate");
            }
            if (tariffMapper.updateByPrimaryKey(tariff) != 1) {
                throw new ServiceException("tariff.error.save");
            }
        }
        if (tariffData != null) {
            // save tariff data for a tariff class which has it's own tables
            try {
                dataService.updateTariffData(tariff.getId(), tariffData);
                //TODO: Check if tariff need to be updated.
                if(dataService != null) {
                    // update calc contents from ITariffData if applicable
                    dataService.updateCalcContents(tariffWithData);
                    if (tariffMapper.updateByPrimaryKey(tariff) != 1) {
                        throw new ServiceException("tariff.error.save");
                    }
                }
            } catch (Exception e) {
                logger.warn("Could not update tariff data", e);
                throw new ServiceException("tariff.error.save");
            }
        }
    }
    
    //private void validateMidMonthTariffBillingCyclicChange(Date startDate, Tariff prevTariff, Tariff newTariff,
    private void validateMidMonthTariffBillingCyclicChange(Tariff prevTariff, Tariff newTariff) {
        //If want to add / change MONTHLY Billing Cyclic charge/s can only do it in a tariff that starts on the 1st at zero hours
        
        List<CyclicChargeData> prevBillingCyclicChargeDataList = new ArrayList<>();
        if (prevTariff != null) {
            prevBillingCyclicChargeDataList = RegisterReadingCalcContentsParser.parseCalcContentsToObject(prevTariff.getCalcContents()).getCyclicCharges() 
                .stream().filter(cc -> cc.getCyclicChargeCalcAtE().equals(CyclicChargeCalcAtE.B) && cc.getCycle().equals(CycleE.MONTHLY))
                .collect(Collectors.toList());
        }        
        
        List<CyclicChargeData> newBillingCyclicChargeDataList = RegisterReadingCalcContentsParser.parseCalcContentsToObject(newTariff.getCalcContents()).getCyclicCharges() 
                .stream().filter(cc -> cc.getCyclicChargeCalcAtE().equals(CyclicChargeCalcAtE.B) && cc.getCycle().equals(CycleE.MONTHLY))
                .collect(Collectors.toList());
        
        if (prevBillingCyclicChargeDataList.size() > 0 || newBillingCyclicChargeDataList.size() > 0) {
            throw new ServiceException("error.billing.cyclic.change.midmonth.monthly");
        }
    }

    private List<Tariff> selectTariffByName(String name, Long pricingStructureId) {
        TariffExample example = new TariffExample();
        example.createCriteria().andNameEqualTo(name).andPricingStructureIdEqualTo(pricingStructureId);
        return tariffMapper.selectByExample(example);
    }

    public ITariffDataService getTariffDataService(Tariff tariff) throws Exception {
        TariffClass tc = tariffClassMapper.selectByPrimaryKey(tariff.getTariffClassId());
        return getTariffDataService(tc.getCalcClass());
    }

    public ITariffDataService getTariffDataService(String calcClass) throws ClassNotFoundException, InstantiationException, IllegalAccessException {
        logger.info("Getting dataService for calcClass: "+calcClass);
        Class<?> clazz = Class.forName(calcClass);
        ITariffCalculator calc = (ITariffCalculator) clazz.newInstance();
        Class<? extends ITariffDataService> tariffDataServiceClass = calc.getTariffDataServiceClass();
        if (tariffDataServiceClass != null) {
            ITariffDataService dataService = (ITariffDataService) context.getBean(Introspector.decapitalize(tariffDataServiceClass.getSimpleName()));
            logger.info("dataService= " + dataService.getClass().getSimpleName());
            if (dataService != null) {
                return dataService;
            }
        }
        return null;
    }

    @Transactional
    public boolean isExistingPricingStructuresWithGroup() {
        PricingStructureExample example = new PricingStructureExample();
        example.createCriteria().andGenGroupIdIsNotNull();
        RowBounds rowBounds = new RowBounds(RowBounds.NO_ROW_OFFSET, 1);
        List<PricingStructure> ps = pricingStructureMapper.selectByExampleWithRowbounds(example, rowBounds);
        if (ps.size() == 1) {
            return true;
        } else {
            return false;
        }
    }

    public ITariffInitData getTariffInitData(PricingStructure pricingStructure, Long tariffClassId, Tariff tariff) throws ServiceException {
        logger.info("PricingStructureService:getTariffInitData() for pricingStructure= " + pricingStructure.getName() + " tariffClassId=" + tariffClassId);
        ITariffInitData data = null;
        try {
            TariffClass tariffClass = getTariffClass(tariffClassId);
            ITariffDataService dataService = getTariffDataService(tariffClass.getCalcClass());
            if (dataService != null) {
                data = dataService.getTariffInitData(pricingStructure, tariffClass, tariff);
                logger.info("Got tariffInitData: " + data);
            } else {
                logger.info("PricingStructureService:getTariffInitData() dataService IS null");
            }
        } catch (Exception e) {
            logger.error("Could either not get the tariff data service or the init data from it:", e);
            throw new ServiceException("pricingstructure.error.tariff.load", true);
        }
        return data;
    }

    public TariffClass getTariffClass(Long tariffClassId) throws ServiceException {
        return tariffClassMapper.selectByPrimaryKey(tariffClassId);
    }

    public void checkCalcContents(String calcClass, String calcContentsStr) throws Exception {
        //Basically checking if user has changed JSON structure on editing of Bulk Tariff Updater field
        Tariff dummyTariff = new Tariff();
        dummyTariff.setCalcContents(calcContentsStr);
        try {
            ITariffDataService dataService = getTariffDataService(calcClass);
            if (dataService != null) {
                dataService.getTariffData(new PricingStructure(), dummyTariff);
            } else {
                throw new Exception("TariffDataService is null, invalid calcClass.");
            }
        } catch (Exception e) {
            logger.error("Could either not get the tariff data service or the init data from it: calcClass= " + calcClass
                    + ", calcContentsStr = " + calcContentsStr, e);
            throw new Exception(e);
        }
    }

    public IpayResponseData sendTariffCalculation(String mrid) throws Exception {
        TariffCalcReqMessage tariffCalcReq = new TariffCalcReqMessage(
                ipayXmlMessageService.getClient(), ipayXmlMessageService.getTerm(),
                mrid);
        logger.info("tariffCalcReq=" + tariffCalcReq.toString());
        TariffCalcResMessage tariffCalcRes = null;
        try {
            tariffCalcRes = (TariffCalcResMessage) ipayXmlMessageService.sendIpayXml(tariffCalcReq);
        } catch (Exception e) {
            logger.info("SENDMESSAGE TariffCalcResMessage: sendIpayXml FAILURE: exception.toString()=" + e.toString());
            if (e.toString().contains("ConnectException")) {
                tariffCalcRes = null;
            } else {
                throw new Exception(e);
            }
        }
        if(tariffCalcRes == null) {
            logger.info("PricingStructureService: sendTariffCalculation: tariffCalcRes=null");
            return null;
        }
        logger.info("tariffCalcRes=" + tariffCalcRes.toString());
        IpayResponseData data = new IpayResponseData();
        data.setResCode(tariffCalcRes.getResCode());
        data.setResMsg(tariffCalcRes.getRes());
        return data;
    }

    @Transactional(readOnly=true)
    public String getAllPricingStructuresWithCurrentTariff(Long genGroupId, List<Long> pricingStructureIds) {
        Date now = new Date();
        List<TariffWithCalcClassAndPsInfo> tariffPsListExDb =  tariffSupportMapper.getAllPricingStructuresWithCurrentTariff(genGroupId, now);
        tariffPsListExDb.removeIf(tariff -> !pricingStructureIds.contains(tariff.getPricingStructureId()));
        List<String> tariffList = new ArrayList<>();
        TariffPsJsonRecordConverter tariffPsJsonRecordConverter = new TariffPsJsonRecordConverter();

        for (TariffWithCalcClassAndPsInfo tariffPs : tariffPsListExDb) {
            try {
                if (!tariffPs.getCalcContents().trim().startsWith("{")) {
                    //build object & rebuild to String
                    //if now still doesn't start with "{" then its 'jsonify' component hasn't yet been built
                    //log & ignore
                    ITariffData tariffData = null;
                    ITariffDataService dataService = null;
                    try {
                        dataService = getTariffDataService(tariffPs);
                        if (dataService != null) {
                            tariffData = dataService.getTariffData(null, tariffPs);
                        } else {
                            logger.debug("Export pricingstructures. dataService is null for PS: " + tariffPs.getPricingStructureName() + " tariffId=" + tariffPs.getId() + " calc class=" + tariffPs.getCalcClass());
                            continue;
                        }
                    } catch (Exception e) {
                        logger.debug("Export pricingstructures. Could not get tariffDataService: ", e);
                        continue;
                    }
                    TariffWithData twd = new TariffWithData(tariffPs, tariffData);
                    dataService.updateCalcContents(twd);
                    if (!twd.getTariff().getCalcContents().trim().startsWith("{")) {
                        //this tariff has not yet converted calc_contents from csv to Json, log it and skip it entirely
                        logger.info("FAILED & IGNORED: Export current tariff for pricing structure. Tariff Calc_Contents has no JSON formatter. Tariff_calc_class = " + tariffPs.getCalcClass() + " Pricing_structure = " + tariffPs.getPricingStructureName() + "(id=" + tariffPs.getPricingStructureId() + ")");
                        continue;
                    }
                }
            } catch (Exception e) {
                logger.debug("ERROR ON DB: Export pricingstructures. Exception=" + e.getMessage()
                + " PricingStructureName=" + tariffPs.getPricingStructureName() + "; tariffName=" + tariffPs.getName() + " calc class=" + tariffPs.getCalcClass());
                continue;
            }

            //Build the exportformat
            TariffPsImportRecord tpi = new TariffPsImportRecord();
            tpi.setPricingStructureNameDONOTCHANGE(tariffPs.getPricingStructureName());
            String calcClass = tariffPs.getCalcClass();
            tpi.setCalcClassDONOTCHANGE(calcClass.substring(calcClass.lastIndexOf(":") + 1));
            tpi.setPsRecordStatus(tariffPs.getPsRecordStatus());
            tpi.setTariffName(tariffPs.getName());
            tpi.setStartDate(df.format(tariffPs.getStartDate()));
            tpi.setDescription(tariffPs.getDescription());
            tpi.setCustomerDescription(tariffPs.getCustomerDescription());
            tpi.setCalcContents(tariffPs.getCalcContents());

            tariffList.add(tariffPsJsonRecordConverter.convertToString(tpi));
        }

        //return buffer.toString();

        TariffExportObj obj = new TariffExportObj();
        obj.setTariffs(tariffList);
        return TariffPsJsonRecordConverter.parseExportObjectToJsonString(obj);
    }

    @Transactional(readOnly=true)
    public ArrayList<PricingStructure> getPricingStructuresByTouCalendarId(Long touCalendarId) {
        return icalendarPricingStructureMapper.getPricingStructuresByAssignedCalendars(touCalendarId);
    }

    /**
     * PS may be active but the Tariff start date is in the future.
     * Hence the PS does not have a Tariff that is currently running.
     * @param pricingStructureId
     */
    @Transactional(readOnly=true)
    public TariffWithCalcClass getCurrentOrFirstTariffByPricingStructureId(Long pricingStructureId) {
        TariffWithCalcClass tariff = tariffSupportMapper.findCurrentTariffByPricingStructureId(pricingStructureId, new Date());
        if (tariff == null) {
            // PS has no running Tariff atm.
            tariff = getAllTariffByPricingStructureId(pricingStructureId).get(0);
        }
        return tariff;
    }

    @Transactional(readOnly=true)
    public List<TariffWithCalcClass> getAllTariffByPricingStructureId(Long pricingStructureId) {
        return tariffSupportMapper.findAllTariffByPricingStructureId(pricingStructureId);
    }

    @Transactional(readOnly=true)
    public List<Long> getPricingStructureIdsforUpWithMdc(Long mdcId, Long userGenGroupId) {
        return tariffSupportMapper.getPricingStructureIdsforUpWithMdc(mdcId, userGenGroupId, new Date());
    }

    @Transactional(readOnly=true)
    public List<Long> getPricingStructureIdsforUpWithMeterModel(Long meterModelId, Long userGenGroupId) {
        return tariffSupportMapper.getPricingStructureIdsforUpWithMeterModel(meterModelId, userGenGroupId, new Date());
    }

    @Transactional(readOnly = true)
    public List<ChannelCompatibilityE> isregReadPsSameBillingDetsAsMeterModel(List<PSDto> pricingStructureDtos, Long mdcId, String userName) {
        return pricingStructureCustomService.isregReadPsSameBillingDetsAsMeterModel(pricingStructureDtos, mdcId, userName);
    }

    public ChannelCompatibilityE checkChannelCompatibility(Collection<Long> firstList, Collection<Long> secondList) {
        return pricingStructureCustomService.checkChannelCompatibility(firstList, secondList);
    }

    public boolean getMridExistence(String mrid, Long id) {
        PricingStructureExample example = new PricingStructureExample();
        PricingStructureExample.Criteria criteria = example.createCriteria().andMridEqualTo(mrid);
        if(id != null) {
            criteria.andIdNotEqualTo(id);
        }
        return !pricingStructureMapper.selectByExample(example).isEmpty();
    }

    //-------------------------------------------------------------------------------------------------------------------------------
    public void setPricingStructureMapper(PricingStructureMapper pricingStructureMapper) {
        this.pricingStructureMapper = pricingStructureMapper;
    }

    public void setTariffMapper(TariffMapper tariffMapper) {
        this.tariffMapper = tariffMapper;
    }

    public void setTariffClassMapper(TariffClassMapper tariffClassMapper) {
        this.tariffClassMapper = tariffClassMapper;
    }

    public void setTariffSupportMapper(TariffSupportMapper tariffSupportMapper) {
        this.tariffSupportMapper = tariffSupportMapper;
    }

    public void setUpPricingStructureMapper(UpPricingStructureMapper upPricingStructureMapper) {
        this.upPricingStructureMapper = upPricingStructureMapper;
    }

    public void setMdcChannelService(MdcChannelService mdcChannelService) {
        this.mdcChannelService = mdcChannelService;
    }

    public void setIpayXmlMessageService(IpayXmlMessageService ipayXmlMessageService) {
        this.ipayXmlMessageService = ipayXmlMessageService;
    }

    public void setICalendarPricingStructureMapper(ICalendarPricingStructureMapper icalendarPricingStructureMapper) {
        this.icalendarPricingStructureMapper = icalendarPricingStructureMapper;
    }

    public void setPricingStructureCustomService(PricingStructureCustomService pricingStructureCustomService) {
        this.pricingStructureCustomService = pricingStructureCustomService;
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.context = applicationContext;
    }
}
