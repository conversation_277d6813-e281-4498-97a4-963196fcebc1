package za.co.ipay.metermng.server.rpc;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

import org.apache.log4j.Logger;
import org.springframework.transaction.annotation.Transactional;

import za.co.ipay.gwt.common.shared.exception.ServiceException;
import za.co.ipay.gwt.common.shared.exception.ValidationException;
import za.co.ipay.gwt.common.shared.exception.ValidationMessage;
import za.co.ipay.metermng.client.rpc.CustomerRpc;
import za.co.ipay.metermng.mybatis.custom.model.NotifyCustomer;
import za.co.ipay.metermng.mybatis.generated.model.AuxAccount;
import za.co.ipay.metermng.mybatis.generated.model.BillPayTrans;
import za.co.ipay.metermng.mybatis.generated.model.Customer;
import za.co.ipay.metermng.mybatis.generated.model.CustomerAccount;
import za.co.ipay.metermng.mybatis.generated.model.CustomerAgreement;
import za.co.ipay.metermng.mybatis.generated.model.CustomerAgreementHist;
import za.co.ipay.metermng.mybatis.generated.model.CustomerHist;
import za.co.ipay.metermng.mybatis.generated.model.SpecialActionReasonsLog;
import za.co.ipay.metermng.mybatis.generated.model.UsagePoint;
import za.co.ipay.metermng.server.mybatis.service.AuxAccountService;
import za.co.ipay.metermng.server.mybatis.service.CustomerHistService;
import za.co.ipay.metermng.server.mybatis.service.CustomerService;
import za.co.ipay.metermng.server.mybatis.service.UsagePointService;
import za.co.ipay.metermng.server.util.MeterMngUtil;
import za.co.ipay.metermng.server.validation.ServerValidatorUtil;
import za.co.ipay.metermng.shared.AccountTransData;
import za.co.ipay.metermng.shared.AuxAccountData;
import za.co.ipay.metermng.shared.BillPayTransData;
import za.co.ipay.metermng.shared.CustomerAccountTransData;
import za.co.ipay.metermng.shared.CustomerAgreementHistData;
import za.co.ipay.metermng.shared.CustomerHistData;
import za.co.ipay.metermng.shared.IpayResponseData;
import za.co.ipay.metermng.shared.MeterMngStatics;
import za.co.ipay.metermng.shared.auxaccounthist.CustAuxAccountHistData;
import za.co.ipay.metermng.shared.dto.CustomerAgreementData;
import za.co.ipay.metermng.shared.dto.CustomerData;
import za.co.ipay.metermng.shared.dto.MeterData;
import za.co.ipay.metermng.shared.dto.UsagePointData;
import za.co.ipay.metermng.shared.dto.customer.CustomerNotifyData;
import za.co.ipay.metermng.shared.dto.user.MeterMngUser;
import za.co.ipay.metermng.tariff.thin.AccountAdjustmentResult;

public class CustomerRpcImpl extends BaseMeterMngRpc implements CustomerRpc {

    private static final long serialVersionUID = -8969292725137935019L;

    private CustomerService customerService;
    private CustomerHistService customerHistService;
    private AuxAccountService auxAccountService;  
    private UsagePointService usagePointService;

    public CustomerRpcImpl() {
        logger = Logger.getLogger(CustomerRpcImpl.class);
    }

    @Override
    public CustomerAgreementData updateCustomer(CustomerAgreementData customerAgreementData, boolean updateCustomer,
                                                boolean updateCustomerAgreement, boolean updateCustomerAccount) throws ValidationException,
            ServiceException {
        logger.info("customerservice: " + customerService + " updateCustomer:" + customerAgreementData);
        logger.info("UPDATECUSTOMER: updateCustomer=" + updateCustomer + " updateCustomerAgreement=" + updateCustomerAgreement + " updateCustomerAccount=" + updateCustomerAccount);   //grrrrr!! why wasn't this there!!
        // TODO validation for the CustomerAgreementData - nested model classes?
        // ServerValidatorUtil.getInstance().validateDataForValidationMessages(customerAgreementData);
        customerAgreementData.getCustomerData().setGenGroupId(getUser().getCurrentGroupId());
        
        if (customerService.getCustRefExistence(customerAgreementData.getCustomerData().getCustomerReference(), customerAgreementData.getCustomerId())) {
            throw new ValidationException(new ValidationMessage("cust.ref.external.unique.validation", true));
        }
        
        if (customerService.getMridExistence(customerAgreementData.getCustomerData().getMrid(), customerAgreementData.getCustomerId())) {
            throw new ValidationException(new ValidationMessage("cust.mrid.external.unique.validation", true));
        }
        
        return customerService.updateCustomer(customerAgreementData, customerAgreementData.getCustomerData()
                .getPhysicalLocation(), updateCustomer, updateCustomerAgreement, updateCustomerAccount, getUser().getUserName());
    }

    @Override
    public ArrayList<AuxAccountData> fetchAuxAccounts(CustomerAgreement customerAgreement) throws ValidationException,
            ServiceException {
        ServerValidatorUtil.getInstance().validateNotNull(customerAgreement, "customeragreement.title");
        return auxAccountService.getAuxAccountsByCustomerAgr(customerAgreement);
    }

    @Override
    public List<CustAuxAccountHistData> fetchAuxAccountHistory(AuxAccount auxAccount) throws ValidationException, ServiceException {
        List<CustAuxAccountHistData> auxAccountHistory = auxAccountService.getAuxAccountHistory(auxAccount);
        return auxAccountHistory;
    }

    @Override
    public ArrayList<CustomerHistData> fetchCustomerHistory(Long customerId) {
        ArrayList<CustomerHistData> thelist = new ArrayList<CustomerHistData>();
        List<CustomerHist> listfromdb = customerHistService.getCustomerHistoryByCustomerId(customerId);
        if (listfromdb != null) {
            CustomerHistData customerHistData;
            CustomerHistData prev_record = null;
            Iterator<CustomerHist> listIt = listfromdb.iterator();
            while (listIt.hasNext()) {
                customerHistData = new CustomerHistData(listIt.next());
                if (prev_record != null) {
                    findChanges(prev_record, customerHistData);
                }
                prev_record = customerHistData;
                thelist.add(customerHistData);
            }
        }
        return thelist;
    }

    @Override
    public ArrayList<CustomerAgreementHistData> fetchCustomerAgreementHistory(Long customerAgreementId) {
        ArrayList<CustomerAgreementHistData> thelist = new ArrayList<CustomerAgreementHistData>();
        List<CustomerAgreementHist> listfromdb = customerHistService
                .getCustomerAgreementHistoryById(customerAgreementId);
        if (listfromdb != null) {
            CustomerAgreementHistData customerAgreementHistData;
            CustomerAgreementHistData prev_record = null;
            Iterator<CustomerAgreementHist> listIt = listfromdb.iterator();
            Customer customer;
            while (listIt.hasNext()) {
                customerAgreementHistData = new CustomerAgreementHistData(listIt.next());
                customer = null;
                if (customerAgreementHistData.getCustomerId() != null) {
                    customer = customerService.getCustomerById(customerAgreementHistData.getCustomerId());
                    if (customer != null)
                        customerAgreementHistData.setCustomerName((customer.getFirstnames()==null?"":customer.getFirstnames()) + " "
                                + customer.getSurname());
                }
                if (prev_record != null) {
                    findChanges(prev_record, customerAgreementHistData);
                }
                prev_record = customerAgreementHistData;
                thelist.add(customerAgreementHistData);
            }
        }
        return thelist;
    }

    private void findChanges(CustomerHistData obj1, CustomerHistData obj2) {
        if (obj1.getId().equals(obj2.getId())) {
            obj1.setRecordStatusChanged(!obj1.getRecordStatus().equals(obj2.getRecordStatus()));
            obj1.setTitleChanged(MeterMngUtil.areValuesDifferent(obj1.getTitle(), obj2.getTitle()));
            obj1.setInitialsChanged(MeterMngUtil.areValuesDifferent(obj1.getInitials(), obj2.getInitials()));
            obj1.setFirstnamesChanged(MeterMngUtil.areValuesDifferent(obj1.getFirstnames(), obj2.getFirstnames()));
            obj1.setSurnameChanged(MeterMngUtil.areValuesDifferent(obj1.getSurname(), obj2.getSurname()));
            obj1.setIdNumberChanged(MeterMngUtil.areValuesDifferent(obj1.getIdNumber(), obj2.getIdNumber()));
            obj1.setCompanyNameChanged(MeterMngUtil.areValuesDifferent(obj1.getCompanyName(), obj2.getCompanyName()));
            obj1.setEmail1Changed(MeterMngUtil.areValuesDifferent(obj1.getEmail1(), obj2.getEmail1()));
            obj1.setEmail2Changed(MeterMngUtil.areValuesDifferent(obj1.getEmail2(), obj2.getEmail2()));
            obj1.setPhone1Changed(MeterMngUtil.areValuesDifferent(obj1.getPhone1(), obj2.getPhone1()));
            obj1.setPhone2Changed(MeterMngUtil.areValuesDifferent(obj1.getPhone2(), obj2.getPhone2()));
        }
    }

    private void findChanges(CustomerAgreementHistData obj1, CustomerAgreementHistData obj2) {
        if (obj1.getId().equals(obj2.getId())) {
            obj2.setRecordStatusChanged(!obj1.getRecordStatus().equals(obj2.getRecordStatus()));
            obj1.setCustomerIdChanged(MeterMngUtil.areValuesDifferent(obj1.getCustomerId(), obj2.getCustomerId()));
            obj1.setAgreementRefChanged(
                    MeterMngUtil.areValuesDifferent(obj1.getAgreementRef(), obj2.getAgreementRef()));
            obj1.setStartDateChanged(MeterMngUtil.areValuesDifferent(obj1.getStartDate(), obj2.getStartDate()));
        }
    }

    @Transactional(readOnly=true)
    @Override
    public List<CustomerNotifyData> getCustomerNotifySuggestions(String query, int limit, String notifyType) throws ServiceException {
        List<CustomerNotifyData> dtos = new ArrayList<CustomerNotifyData>();
        if (query != null && !query.trim().equals("")) {
            List<NotifyCustomer> customers = null;
            if (MeterMngStatics.NOTIFY_EMAIL_TYPE.equals(notifyType)) {
                logger.info("Searching for emailCustomerNotify: "+query+" limit:"+limit);
                MeterMngUser user = getUser();
                customers = customerService.getEmailNotifyCustomerSuggestions(query, user.getCurrentGroupId(), limit);
            } else if (MeterMngStatics.NOTIFY_SMS_TYPE.equals(notifyType)) {
                logger.info("Searching for smsCustomerNotify: "+query+" limit:"+limit);
                MeterMngUser user = getUser();
                customers = customerService.getSmsNotifyCustomerSuggestions(query, user.getCurrentGroupId(), limit);
            } else {
                logger.info("Searching for customerNotify: "+query+" limit:"+limit);
                MeterMngUser user = getUser();
                customers = customerService.getNotifyCustomerSuggestions(query, user.getCurrentGroupId(), limit);
            }
            if (customers != null) {
                CustomerNotifyData customer = null;
                logger.info("Retrieved notifyCustomers: "+customers.size());
                for(NotifyCustomer c : customers) {
                    customer = new CustomerNotifyData();
                    customer.setCustomerAccountId(c.getCustomerAccountId());
                    customer.setCustomerId(c.getCustomerId());
                    customer.setFirstName(c.getFirstName());
                    customer.setLastName(c.getLastName());
                    customer.setNotifyEmail(c.getNotifyEmail());
                    customer.setNotifySms(c.getNotifySms());
                    dtos.add(customer);
                }
            }
        } else {
            logger.debug("Ignoring invalid query: "+query);
        }
        return dtos;
    }   
 
    @Override
    public IpayResponseData sendSyncAccountBalanceMsg(MeterData meterData, Long customerAccountId)  throws ServiceException {
        try {
            return customerService.sendSyncAccountBalance(meterData, customerAccountId);
        } catch (Exception e) {
            logger.error("CustomerRpcImpl sendSynchmsg Exception = " + e.getMessage() + " " + e);
            return null;
        }
    }

    @Override
    public List<BillPayTransData> fetchBillPayments(Long customerAgreementId) {
        return customerService.fetchBillPayments(customerAgreementId);
    }

    public void setCustomerService(CustomerService customerService) {
        this.customerService = customerService;
    }

    public void setAuxAccountService(AuxAccountService auxAccountService) {
        this.auxAccountService = auxAccountService;
    }

    public void setCustomerHistService(CustomerHistService customerHistService) {
        this.customerHistService = customerHistService;
    }

    public void setUsagePointService(UsagePointService usagePointService) {
        this.usagePointService = usagePointService;
    }

    @Override
    public ArrayList<CustomerAccountTransData> fetchCustomerAccountTransactions(CustomerAgreementData customerAgreementData) throws ServiceException {
        return customerService.fetchCustomerAccountTransactions(customerAgreementData);
    }

    @Override
    public ArrayList<CustomerAccountTransData> fetchAuxiliaryAccountTransactions(Long auxAccountId) throws ServiceException {
        return customerService.fetchAuxiliaryAccountTransactions(auxAccountId);
    }

    @Override
    public BigDecimal inputAccountAdjustment(AccountTransData accountTrans) throws ServiceException {
        AccountAdjustmentResult accountAdjustmentResult = customerService.inputAccountAdjustment(accountTrans);
        customerService.sendAccountAdjustmentMessages(accountAdjustmentResult);
        return accountAdjustmentResult.getCurrentAccountBalance();
    }

    @Override
    public BigDecimal inputAuxAccountAdjustment(AccountTransData accountTrans) throws ServiceException {
        return customerService.inputAuxAccountAdjustment(accountTrans);
    }

    /**
     * GROUP to GLOBAL: Clears contractual entities from their group. 
     * - PricingStructure must change to GLOBAL PS. All future PSs are deleted.
     * - UsagePoint and Customer location groups are cleared.
     */
    @Override
    @Transactional
    public void clearAccessGroup(UsagePointData usagePointData) throws ValidationException, ServiceException {
        int updated = 0;
        try {
            boolean clearGroupedUPLocation = false;
            boolean clearGroupedCustomerLocation = false;
            ArrayList<UsagePointData> upDataList = new ArrayList<>(10);
            if (usagePointData.getId() != null) {
                upDataList.add(usagePointData);
            }

            CustomerData customer = null;
            CustomerAgreementData customerAgreementData = null;
            if (usagePointData.getCustomerAgreementData() != null) {
                customerAgreementData = usagePointData.getCustomerAgreementData();
                customer = customerAgreementData.getCustomerData();
            }
            if (customer != null && customer.getId() != null) {
                ArrayList<UsagePoint> upList = usagePointService.getUsagePointsByCustomerAgreementId(customerAgreementData.getId());
                UsagePointData up1 = null;
                for (UsagePoint up : upList) {
                    if (up.getId().equals(usagePointData.getId())) {
                        continue;
                    }
                    up1 = new UsagePointData();
                    up1.setUsagePoint(up);
                    upDataList.add(up1);
                }
                clearGroupedUPLocation = usagePointService.clearGroupedLocation(customer.getPhysicalLocationId());
                if (clearGroupedCustomerLocation) {
                    updated += 1;
                    customer.setPhysicalLocationId(null);
                    customer.setPhysicalLocation(null);
                }
                updated += customerService.clearAccessGroupForCustomer(customer.getId());
            }

            for (UsagePointData upData : upDataList) {
                usagePointService.deleteAllFutureUpPs(upData.getId());
                clearGroupedUPLocation = usagePointService.clearGroupedLocation(upData.getServiceLocationId());
                if (clearGroupedUPLocation) {
                    updated += 1;
                    upData.setServiceLocationId(null);
                    upData.setServiceLocation(null);
                }
                updated += usagePointService.clearAccessGroupForUsagePoint(upData.getId());
                if (upData.getMeterId() != null) {
                    updated += usagePointService.clearAccessGroupForMeter(upData.getMeterId());
                }
            }
            logger.info("updated=" + updated);
            if (updated == 0) {
                // Ideally this should never happen.
                throw new ServiceException("access_group.error.group_already_cleared");
            }
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            logger.error("Error while clearing org group for contract entities", e);
            throw new ServiceException("error.general");
        }
    }

    /**
     * Claims contractual entities for the user's session group 
     * 1. From GLOBAL to GROUP. 
     * 2. From GROUP to GROUP. 
     * - PricingStructure must change to GLOBAL/NEW GROUP PS. 
     * - UsagePoint and Customer location groups are cleared.
     */
    @Override
    @Transactional
    public void claimAccessGroup(Long accessGroupId, UsagePointData usagePointData) throws ValidationException, ServiceException {
        try {
            int updated = 0;
            boolean clearGroupedUPLocation = false;
            boolean clearGroupedCustomerLocation = false;
            ArrayList<UsagePointData> upDataList = new ArrayList<>(10);
            if (usagePointData.getId() != null) {
                upDataList.add(usagePointData);
            }

            CustomerData customer = null;
            CustomerAgreementData customerAgreementData = null;
            if (usagePointData.getCustomerAgreementData() != null) {
                customerAgreementData = usagePointData.getCustomerAgreementData();
                customer = customerAgreementData.getCustomerData();
            }
            if (customer != null && customer.getId() != null) {
                ArrayList<UsagePoint> upList = usagePointService.getUsagePointsByCustomerAgreementId(customerAgreementData.getId());
                UsagePointData up1 = null;
                for (UsagePoint up : upList) {
                    if (up.getId().equals(usagePointData.getId())) {
                        continue;
                    }
                    up1 = new UsagePointData();
                    up1.setUsagePoint(up);
                    upDataList.add(up1);
                }
                clearGroupedUPLocation = usagePointService.clearGroupedLocation(customer.getPhysicalLocationId());
                if (clearGroupedCustomerLocation) {
                    updated += 1;
                    customer.setPhysicalLocationId(null);
                    customer.setPhysicalLocation(null);
                }
                updated += customerService.updateAccessGroupForCustomer(customer.getId(), accessGroupId);
            }

            for (UsagePointData upData : upDataList) {
                clearGroupedUPLocation = usagePointService.clearGroupedLocation(upData.getServiceLocationId());
                if (clearGroupedUPLocation) {
                    updated += 1;
                    upData.setServiceLocationId(null);
                    upData.setServiceLocation(null);
                }
                updated += usagePointService.updateAccessGroupForUsagePoint(upData.getId(), accessGroupId);
                if (upData.getMeterId() != null) {
                    updated += usagePointService.updateAccessGroupForMeter(upData.getMeterId(), accessGroupId);
                }
                usagePointService.deleteAllFutureUpPs(upData.getId());
            }
            logger.info("updated=" + updated);
            if (updated == 0) {
                // Ideally this should never happen.
                throw new ServiceException("access_group.error.group_already_cleared");
            }
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            logger.error("Error while updating org group for contract entities", e);
            throw new ServiceException("error.general");
        }
    }

    @Override
    public IpayResponseData sendBillPaymentReversalMsg(BillPayTrans billPayTrans, String userName,
            SpecialActionReasonsLog specialActionReasonsLog, String comment) {
        return customerService.sendBillPaymentReversalMsg(billPayTrans, userName, specialActionReasonsLog, comment);
    }
    
    public int updateNotifyPreferences(CustomerAccount customerAccount) throws ServiceException {
        return customerService.updateNotifyPreferences(customerAccount);
    }

}
