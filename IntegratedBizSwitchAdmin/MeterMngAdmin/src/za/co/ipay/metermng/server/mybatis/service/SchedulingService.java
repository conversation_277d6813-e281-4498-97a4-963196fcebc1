package za.co.ipay.metermng.server.mybatis.service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.log4j.Logger;
import org.quartz.CronScheduleBuilder;
import org.quartz.JobBuilder;
import org.quartz.JobDataMap;
import org.quartz.JobDetail;
import org.quartz.JobKey;
import org.quartz.Scheduler;
import org.quartz.SchedulerException;
import org.quartz.Trigger;
import org.quartz.TriggerBuilder;
import org.springframework.transaction.annotation.Transactional;

import za.co.ipay.gwt.common.server.util.ExposedReloadableResourceBundleMessageSource;
import za.co.ipay.gwt.common.shared.exception.ServiceException;
import za.co.ipay.metermng.mybatis.generated.model.TaskSchedule;

/**
 * SchedulingService is used by the application to set up and manage the currently task schedules using a Quartz
 * scheduler. The task schedules are stored in the database as TaskSchedule instances. Each TaskSchedule has a
 * corresponding Quartz job scheduled for its corresponding cron expression.
 * 
 * <AUTHOR>
 */
public class SchedulingService {
    
    public static final String JOB_PREFIX = "Job:";
    public static final String TRIGGER_PREFIX = "Trigger:";
    public static final String GROUP = "MeterMngAdmin";
    public static final String TASK_SCHEDULE_ID = "taskScheduleId";
    public static final String SCHEDULE_SERVICE = "scheduleService";
    public static final String METER_SERVICE = "meterService";
    public static final String APPSETTING_SERVICE = "appSettingService";
    public static final String MESSAGE_SOURCE = "messageSource";
    public static final String FORMAT_SOURCE = "formatSource";
    public static final String EMAIL_SERVICE = "emailService";
    public static final String USE_DEFAULT_LOCALE = "useDefaultLocale";
    public static final String DEFAULT_LOCALE_NAME = "defaultLocaleName";
    
    /** The default locale's name. */
    private String defaultLocaleName; 
    /** Whether the default locale should be used or not. */
    private boolean useDefaultLocaleOnly = false;
    /** The Quartz scheduled which is used to set up scheduled tasks. */
    private Scheduler scheduler;    
    /** The service used to access the schedule data. */
    private ScheduleService scheduleService;
    /** The meter service used to access meters, meter readings, etc. */
    private MeterService meterService;
    /** The appSetting service used to access email from details */
    private AppSettingService appSettingService;
    /** The current message bundle. */
    private ExposedReloadableResourceBundleMessageSource messageSource;
    /** The current format bundle. */
    private ExposedReloadableResourceBundleMessageSource formatSource;
    /** The service used to send task emails. */
    private EmailService emailService;
    
    private static Logger logger = Logger.getLogger(SchedulingService.class);
    
    //Method to initialise the active TaskSchedule instances as scheduled jobs. 
    @Transactional(readOnly=true)
    public void startScheduling() {  
        logger.info("Starting scheduling using useDefaultLocaleOnly:"+useDefaultLocaleOnly+" defaultLocaleName:"+defaultLocaleName);
        List<TaskSchedule> taskSchedules = scheduleService.getTaskSchedules(true);
        for(TaskSchedule taskSchedule : taskSchedules) {            
            try {
                schedule(taskSchedule);
            } catch (SchedulerException se) {
                logger.error("Error scheduling TaskSchedule:"+taskSchedule.getId()+" "+taskSchedule.getTaskScheduleName(), se);
            }
        }
    }
    
    private void schedule(TaskSchedule taskSchedule) throws SchedulerException {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put(SCHEDULE_SERVICE, scheduleService);
        map.put(METER_SERVICE, meterService);
        map.put(APPSETTING_SERVICE, appSettingService);
        map.put(TASK_SCHEDULE_ID, taskSchedule.getId());
        map.put(MESSAGE_SOURCE, messageSource);
        map.put(FORMAT_SOURCE, formatSource);
        map.put(EMAIL_SERVICE, emailService);
        map.put(USE_DEFAULT_LOCALE, useDefaultLocaleOnly);
        map.put(DEFAULT_LOCALE_NAME, defaultLocaleName);
        JobDetail jobDetail = JobBuilder.newJob(SchedulingTaskScheduleJob.class)
                                        .withIdentity(JOB_PREFIX + taskSchedule.getId(), GROUP)
                                        .usingJobData(new JobDataMap(map))
                                        .build();
        Trigger trigger = TriggerBuilder.newTrigger()
                                        .withIdentity(TRIGGER_PREFIX + taskSchedule.getId(), GROUP)
                                        .withSchedule(CronScheduleBuilder.cronSchedule(taskSchedule.getCronExpression()))
                                        .build();
        scheduler.scheduleJob(jobDetail, trigger);
        logger.info("Scheduled TaskSchedule: id:"+ taskSchedule.getId()+" "+taskSchedule.getTaskScheduleName()+" cronExp:"+taskSchedule.getCronExpression());
    }
    
    public void rescheduleTaskSchedule(TaskSchedule taskSchedule) throws ServiceException {        
        if (taskSchedule != null && taskSchedule.getId() != null) {
            try {
                deschedule(taskSchedule);
                schedule(taskSchedule);
            } catch (SchedulerException se) {
              logger.error("Error rescheduling TaskSchedule: "+taskSchedule.getTaskScheduleName()+ " id:"+ taskSchedule.getId(), se);
                throw new ServiceException("taskschedule.error.scheduling", true);
            }
        }
    }
    
    private void deschedule(TaskSchedule taskSchedule) throws SchedulerException, ServiceException {
        JobKey jobKey = new JobKey(JOB_PREFIX+taskSchedule.getId(), GROUP);
        if (scheduler.checkExists(jobKey)) {
            if (!scheduler.deleteJob(jobKey)) {
                logger.error("Unable to deschedule existing version of TaskSchedule: "+taskSchedule.getId()+" "+taskSchedule.getTaskScheduleName());
                throw new ServiceException("taskschedule.error.descheduling", true);
            }
        } else {
            logger.info("No current instance of TaskSchedule:"+taskSchedule.getTaskScheduleName()+" is currently scheduled");
        }
    }
    
    public void descheduleTaskSchedule(TaskSchedule taskSchedule) throws ServiceException {        
        if (taskSchedule != null && taskSchedule.getId() != null) {
            try {
                deschedule(taskSchedule);
            } catch (SchedulerException se) {
              logger.error("Error descheduling TaskSchedule: "+taskSchedule.getTaskScheduleName(), se);
                throw new ServiceException("taskschedule.error.descheduling", true);
            }
        }
    }
    
    //Method to shut down the current scheduler. After this is called, the scheduler can not be re-used. This should
    //be called when the web app is shutting down.
    public void shutDownScheduling() {
        if (scheduler != null) {
            try {
                scheduler.shutdown(true);
                logger.info("Scheduler is shut down");
            } catch (SchedulerException se) {
                logger.error("Error shutting down the scheduler:", se);
            }
        }
    }

    public void setScheduleService(ScheduleService scheduleService) {
        this.scheduleService = scheduleService;
    }

    public void setScheduler(Scheduler scheduler) {
        this.scheduler = scheduler;
    }

    public void setMeterService(MeterService meterService) {
        this.meterService = meterService;
    }
    
    public void setAppSettingService(AppSettingService appSettingService) {
        this.appSettingService = appSettingService;
    }

    public void setMessageSource(ExposedReloadableResourceBundleMessageSource messageSource) {
        this.messageSource = messageSource;
    }

    public void setFormatSource(ExposedReloadableResourceBundleMessageSource formatSource) {
        this.formatSource = formatSource;
    }

    public void setEmailService(EmailService emailService) {
        this.emailService = emailService;
    }

    public void setDefaultLocaleName(String defaultLocaleName) {
        this.defaultLocaleName = defaultLocaleName;
    }

    public void setUseDefaultLocaleOnly(boolean useDefaultLocaleOnly) {
        this.useDefaultLocaleOnly = useDefaultLocaleOnly;
    }   
}
