package za.co.ipay.metermng.server.mybatis.service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.ibatis.session.RowBounds;
import org.mybatis.spring.mapper.MapperFactoryBean;
import org.springframework.transaction.annotation.Transactional;

import za.co.ipay.metermng.mybatis.generated.mapper.CustomerAccountHistMapper;
import za.co.ipay.metermng.mybatis.generated.mapper.CustomerAgreementHistMapper;
import za.co.ipay.metermng.mybatis.generated.mapper.CustomerHistMapper;
import za.co.ipay.metermng.mybatis.generated.model.CustomerAccountHist;
import za.co.ipay.metermng.mybatis.generated.model.CustomerAccountHistExample;
import za.co.ipay.metermng.mybatis.generated.model.CustomerAgreementHist;
import za.co.ipay.metermng.mybatis.generated.model.CustomerAgreementHistExample;
import za.co.ipay.metermng.mybatis.generated.model.CustomerHist;
import za.co.ipay.metermng.mybatis.generated.model.CustomerHistExample;
import za.co.ipay.metermng.mybatis.generated.model.AuxAccountExample;
import za.co.ipay.metermng.mybatis.generated.model.AccountTransExample;
import za.co.ipay.metermng.mybatis.generated.model.AccountTrans;
import za.co.ipay.metermng.mybatis.generated.mapper.AccountTransMapper;
import za.co.ipay.metermng.mybatis.generated.model.AuxAccount;
import za.co.ipay.metermng.mybatis.generated.mapper.AuxAccountMapper;

public class CustomerHistService {

    private CustomerHistMapper customerHistMapperImpl;
    private CustomerAgreementHistMapper customerAgreementHistMapperImpl;
    private CustomerAccountHistMapper customerAccountHistMapperImpl;
    private AccountTransMapper accountTransMapper;
    private AuxAccountMapper auxAccountMapper;

    
    public void setCustomerHistMapperImpl(CustomerHistMapper customerHistMapper) {
        this.customerHistMapperImpl = customerHistMapper;
    }
    
    public void setCustomerAgreementHistMapperImpl(CustomerAgreementHistMapper customerAgreementHistMapper) {
        this.customerAgreementHistMapperImpl = customerAgreementHistMapper;
    }
    
    public void setCustomerAccountHistMapperImpl(CustomerAccountHistMapper customerAccountHistMapper) {
        this.customerAccountHistMapperImpl = customerAccountHistMapper;
    }

    public void setAuxAccountMapper(AuxAccountMapper auxAccountMapper) {
        this.auxAccountMapper = auxAccountMapper;
    }

    
    @Transactional(readOnly=true)
    public List<CustomerHist> getCustomerHistoryByCustomerId(Long custId) {
        CustomerHistExample customerHistExample = new CustomerHistExample();
        customerHistExample.createCriteria().andIdEqualTo(custId);
        customerHistExample.setOrderByClause("date_rec_modified desc");
        return customerHistMapperImpl.selectByExample(customerHistExample);
    }
    
    @Transactional(readOnly=true)
    public Long getLatestCustomerHistoryIdByCustomerId(Long custId) {
        CustomerHistExample customerHistExample = new CustomerHistExample();
        customerHistExample.createCriteria().andIdEqualTo(custId);
        customerHistExample.setOrderByClause("date_rec_modified desc");
        List<CustomerHist> list = customerHistMapperImpl.selectByExampleWithRowbounds(customerHistExample, new RowBounds(0, 1));
        if(list.isEmpty()) {
            return null;
        }
        return list.get(0).getCustomerHistId();
    }
    
    @Transactional(readOnly=true)
    public List<CustomerAgreementHist> getCustomerAgreementHistoryById(Long customerAgreementId) {
        CustomerAgreementHistExample customerAgreementHistExample = new CustomerAgreementHistExample();
        customerAgreementHistExample.createCriteria().andIdEqualTo(customerAgreementId);
        customerAgreementHistExample.setOrderByClause("date_rec_modified desc");
        return customerAgreementHistMapperImpl.selectByExample(customerAgreementHistExample);
    }
    
    @Transactional(readOnly=true)
    public Long getLatestCustomerAgreementHistoryIdById(Long customerAgreementId) {
        CustomerAgreementHistExample customerAgreementHistExample = new CustomerAgreementHistExample();
        customerAgreementHistExample.createCriteria().andIdEqualTo(customerAgreementId);
        customerAgreementHistExample.setOrderByClause("date_rec_modified desc");
        List<CustomerAgreementHist> list = customerAgreementHistMapperImpl.selectByExampleWithRowbounds(customerAgreementHistExample, new RowBounds(0, 1));
        if(list.isEmpty()) {
            return null;
        }
        return list.get(0).getCustomerAgreementHistId();
    }
    
    @Transactional(readOnly=true)
    public List<CustomerAccountHist> getCustomerAccountHistoryById(Long customerAccountId) {
        CustomerAccountHistExample customerAccountHistExample = new CustomerAccountHistExample();
        customerAccountHistExample.createCriteria().andIdEqualTo(customerAccountId);
        customerAccountHistExample.setOrderByClause("date_rec_modified desc");
        return customerAccountHistMapperImpl.selectByExample(customerAccountHistExample);
    }
    @Transactional(readOnly = true)
    public Long getLatestCustomerAccountTransHistIdByCustomerId(Long customerAccountId) {
        AccountTransExample example = new AccountTransExample();
        example.createCriteria().andCustomerAccountIdEqualTo(customerAccountId);
        example.setOrderByClause("trans_date desc");
        List<AccountTrans> list = accountTransMapper.selectByExampleWithRowbounds(example, new RowBounds(0, 1));

        if (list != null && !list.isEmpty()) {
            return list.get(0).getId();
        }
        return 0L;
    }
    @Transactional(readOnly = true)
    public Long getLatestAccountTransIdByCustomerAgreementId(Long customerAgreementId) {
        // First, get all aux accounts for this customer agreement
        AuxAccountExample auxAccountExample = new AuxAccountExample();
        auxAccountExample.createCriteria().andCustomerAgreementIdEqualTo(customerAgreementId);
        List<AuxAccount> auxAccounts = auxAccountMapper.selectByExample(auxAccountExample);
        if (auxAccounts.isEmpty()) {
            return 0L;
        }
        // Extract all aux account IDs
        List<Long> auxAccountIds = auxAccounts.stream()
                .map(AuxAccount::getId)
                .collect(Collectors.toList());

        // Create a single query for all aux accounts
        AccountTransExample example = new AccountTransExample();
        example.createCriteria()
                .andCustomerAgreementIdEqualTo(customerAgreementId)
                .andAuxAccountIdIn(auxAccountIds);
        example.setOrderByClause("trans_date desc");

        // Get only the single latest transaction
        List<AccountTrans> transactions = accountTransMapper.selectByExampleWithRowbounds(
                example,
                new RowBounds(0, 1)
        );

        return transactions.isEmpty() ? 0L : transactions.get(0).getId();
    }

    // Setter for customerAccountHistMapper
    public void setAccountTransMapper(AccountTransMapper accountTransMapper) {
        this.accountTransMapper = accountTransMapper;
    }
    
    @Transactional(readOnly=true)
    public Long getLatestCustomerAccountHistoryIdById(Long customerAccountId) {
        CustomerAccountHistExample customerAccountHistExample = new CustomerAccountHistExample();
        customerAccountHistExample.createCriteria().andIdEqualTo(customerAccountId);
        customerAccountHistExample.setOrderByClause("date_rec_modified desc");
        List<CustomerAccountHist> list = customerAccountHistMapperImpl.selectByExampleWithRowbounds(customerAccountHistExample, new RowBounds(0, 1));
        if(list.isEmpty()) {
            return null;
        }
        return list.get(0).getCustomerAccountHistId();
    }

}
