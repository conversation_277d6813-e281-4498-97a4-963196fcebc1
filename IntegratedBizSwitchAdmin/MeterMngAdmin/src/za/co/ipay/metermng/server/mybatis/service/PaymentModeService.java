package za.co.ipay.metermng.server.mybatis.service;

import java.util.List;

import org.springframework.transaction.annotation.Transactional;

import za.co.ipay.metermng.datatypes.RecordStatus;
import za.co.ipay.metermng.mybatis.generated.mapper.PaymentModeMapper;
import za.co.ipay.metermng.mybatis.generated.model.PaymentMode;
import za.co.ipay.metermng.mybatis.generated.model.PaymentModeExample;

public class PaymentModeService {

    private PaymentModeMapper paymentModeMapper;
    
    @Transactional(readOnly=true)
    public List<PaymentMode> getPaymentModes(Boolean active) {
        PaymentModeExample example = new PaymentModeExample();
        if (active != null) {
            if (active) {
                example.createCriteria().andRecordStatusEqualTo(RecordStatus.ACT);
            } else {
                example.createCriteria().andRecordStatusEqualTo(RecordStatus.DAC);
            }
        } else {
            example.createCriteria().andRecordStatusNotEqualTo(RecordStatus.DEL);
        }
        example.setOrderByClause("payment_mode_name");
        return paymentModeMapper.selectByExample(example);
    }
    
    public void setPaymentModeMapper(PaymentModeMapper paymentModeMapper) {
        this.paymentModeMapper = paymentModeMapper;
    }    
}
