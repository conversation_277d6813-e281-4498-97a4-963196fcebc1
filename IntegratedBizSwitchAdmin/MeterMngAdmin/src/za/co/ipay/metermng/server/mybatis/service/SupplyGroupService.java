package za.co.ipay.metermng.server.mybatis.service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.apache.ibatis.session.RowBounds;
import org.apache.log4j.Logger;
import org.springframework.transaction.annotation.Transactional;

import za.co.ipay.gwt.common.shared.exception.ServiceException;
import za.co.ipay.gwt.common.shared.exception.ValidationException;
import za.co.ipay.gwt.common.shared.exception.ValidationMessage;
import za.co.ipay.metermng.datatypes.RecordStatus;
import za.co.ipay.metermng.mybatis.generated.mapper.StsMeterMapper;
import za.co.ipay.metermng.mybatis.generated.mapper.StsSupplyGroupMapper;
import za.co.ipay.metermng.mybatis.generated.model.StsMeterExample;
import za.co.ipay.metermng.mybatis.generated.model.StsSupplyGroup;
import za.co.ipay.metermng.mybatis.generated.model.StsSupplyGroupExample;
import za.co.ipay.metermng.shared.dto.StsSupplyGroupDto;

public class SupplyGroupService {

    private StsSupplyGroupMapper supplyGroupMapper;
    private StsMeterMapper stsMeterMapper;
    
    private static Logger logger = Logger.getLogger(SupplyGroupService.class);


    @Transactional(readOnly=true)
    public StsSupplyGroup getSupplyGroupByTargetId(Long supplyGroupId) {
        StsSupplyGroupExample example = new StsSupplyGroupExample();
        example.createCriteria().andTargetStsSupplyGroupIdEqualTo(supplyGroupId);
        List<StsSupplyGroup> supplyGroupList = supplyGroupMapper.selectByExample(example);
        if (supplyGroupList == null || supplyGroupList.isEmpty()) {
            return null;
        } else {
            return supplyGroupList.get(0);
        }
    }
    
    @Transactional(readOnly=true)
    public StsSupplyGroup getSupplyGroupBySgcAndKrn(String supplyGroupCode, Integer krn) {
        StsSupplyGroupExample example = new StsSupplyGroupExample();
        example.createCriteria().andSupplyGroupCodeEqualTo(supplyGroupCode).andKeyRevisionNumEqualTo(krn);
        List<StsSupplyGroup> supplyGroupList = supplyGroupMapper.selectByExample(example);
        if (supplyGroupList == null || supplyGroupList.isEmpty()) {
            return null;
        } else {
            return supplyGroupList.get(0);
        }
    }
    
    @Transactional(readOnly=true)
    public int getSupplyGroupCount() {
        logger.debug("Gettting Supply Groups count...");
        StsSupplyGroupExample example = new StsSupplyGroupExample();
        example.createCriteria().andRecordStatusNotEqualTo(RecordStatus.DEL);
        int count = supplyGroupMapper.countByExample(example);
        logger.debug("Supply Groups count: "+count);
        return count;
    }
    
    @Transactional(readOnly=true)
    public List<StsSupplyGroupDto> getSupplyGroups(int startRow, int pageSize, String sortField, boolean isAscending) {
        logger.debug("Gettting Supply Groups for startRow: "+startRow+" pageSize: "+pageSize+" sortField:"+sortField+" isAscending: "+isAscending);
        StsSupplyGroupExample example = new StsSupplyGroupExample();
        example.createCriteria().andRecordStatusNotEqualTo(RecordStatus.DEL);
        example.setOrderByClause(getOrderColumn(sortField, isAscending));
        RowBounds rowBounds = new RowBounds(startRow, pageSize);
        
        ArrayList<StsSupplyGroup> supplyGroupList = (ArrayList<StsSupplyGroup>) supplyGroupMapper.selectByExampleWithRowbounds(example, rowBounds);
        List<StsSupplyGroupDto> sgcKrnList = new ArrayList<>();
        
        for (StsSupplyGroup sg : supplyGroupList) {
            StsSupplyGroupDto dto = new StsSupplyGroupDto(sg);
            dto.setInUseByMeter(isInUseByMeter(sg.getId()));
            Long targetSgcId = sg.getTargetStsSupplyGroupId();
            if (targetSgcId != null) {  
                StsSupplyGroup targetSg = supplyGroupMapper.selectByPrimaryKey(targetSgcId);
                dto.setTargetSupGrKrnString(targetSg.getName() + " - SGC: " + targetSg.getSupplyGroupCode() 
                + " - KRN: " + targetSg.getKeyRevisionNum());
            }
            sgcKrnList.add(dto);
        }
        
        return sgcKrnList;
    }

    @Transactional(readOnly=true)
    public boolean isInUseByMeter(Long supplyGroupId) {
        StsMeterExample example = new StsMeterExample();
        example.createCriteria().andStsCurrSupplyGroupIdEqualTo(supplyGroupId);
        example.or(example.createCriteria().andStsNewSupplyGroupIdEqualTo(supplyGroupId));
        return stsMeterMapper.selectByExample(example).size() > 0;
    }

    private String getOrderColumn(String sortField, boolean isAscending) {
        String orderColumn = "supply_group_name";
        if (sortField != null && !sortField.trim().equals("")) {
            if ("name".equals(sortField)) {
                orderColumn = "supply_group_name";
            } 
            if ("code".equals(sortField)) {
                orderColumn = "supply_group_code";
            }
            if ("revisionNumber".equals(sortField)) {
                orderColumn = "key_revision_num";
            }
            if ("baseDate".equals(sortField)) {
                orderColumn = "base_date";
            }
        }
        return orderColumn + " " + getOrder(isAscending);
    }
    
    private String getOrder(boolean isAscending) {
        if (isAscending) {
            return "asc";
        } else {
            return "desc";
        }
    }
    
    @Transactional
    public StsSupplyGroupDto updateSupplyGroup(StsSupplyGroupDto supplyGroupDto) throws ValidationException, ServiceException {
        if (supplyGroupDto.getId() != null && isInUseByMeter(supplyGroupDto.getId())) {
        	//check to make sure that the GUI data and the service data is in sync
        	//in case a user updated a meter's SG and the SG GUI does not know this yet
        	if (!supplyGroupDto.isInUseByMeter()) {
        		throw new ServiceException("supply.group.in.use.error.service");
        	}
        }
        
        // Validate target against current
        //TODO RC: establish at what point HSM updated these fields!
        Long targetSgcId = supplyGroupDto.getTargetStsSupplyGroupId();
        if (targetSgcId != null) {    
            StsSupplyGroup targetSg = supplyGroupMapper.selectByPrimaryKey(supplyGroupDto.getTargetStsSupplyGroupId());
            if (supplyGroupDto.getBaseDate() == null || targetSg.getBaseDate() == null
                    || supplyGroupDto.getIssuedUntilDate() == null || targetSg.getIssuedUntilDate() == null
                    || supplyGroupDto.getExpiryDate() == null || targetSg.getExpiryDate() == null) {
                supplyGroupDto.setNullsInValidation(true);
            } else { 
                if (targetSg.getBaseDate() < supplyGroupDto.getBaseDate()) {
                    throw new ValidationException(new ValidationMessage("supply.group.target.validation.error", true));
                }

                if (targetSg.getIssuedUntilDate().after(new Date()) 
                        && targetSg.getExpiryDate().after(new Date())) {
                    //all good
                } else {
                    //can't be less or equal
                    throw new ValidationException(new ValidationMessage("supply.group.target.validation.error.expired", true));
                }                
            }
        }       
        
        if (supplyGroupDto.getId() == null) {
            if (supplyGroupMapper.insert(supplyGroupDto) != 1) {
                throw new ServiceException("supplygroup.error.save");
            }
        } else {
            if (supplyGroupMapper.updateByPrimaryKey(supplyGroupDto) != 1) {
                throw new ServiceException("supplygroup.error.save");
            }
        }

        if (supplyGroupDto.isDefaultSupplyGroup()) {
            StsSupplyGroup sg = new StsSupplyGroup();
            sg.setDefaultSupplyGroup(false);
            sg.setRecordStatus(null);
            StsSupplyGroupExample sge = new StsSupplyGroupExample();
            sge.createCriteria().andIdNotEqualTo(supplyGroupDto.getId())
                .andDefaultSupplyGroupEqualTo(true);
            supplyGroupMapper.updateByExampleSelective(sg, sge);
        }
        return supplyGroupDto;
    }
    
    public void setStsSupplyGroupMapper(StsSupplyGroupMapper stsSupplyGroupMapper) {
        this.supplyGroupMapper = stsSupplyGroupMapper;
    }
    
    public void setStsMeterMapper(StsMeterMapper stsMeterMapper) {
        this.stsMeterMapper = stsMeterMapper;
    }
}
