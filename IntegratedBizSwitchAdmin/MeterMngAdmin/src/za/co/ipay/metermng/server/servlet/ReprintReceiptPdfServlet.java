package za.co.ipay.metermng.server.servlet;

import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;

import javax.servlet.ServletConfig;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.io.output.ByteArrayOutputStream;
import org.apache.log4j.Logger;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.pdmodel.PDPage;
import org.apache.pdfbox.pdmodel.font.PDType1Font;
import org.springframework.context.ApplicationContext;
import org.springframework.context.support.DefaultMessageSourceResolvable;
import org.springframework.web.context.WebApplicationContext;

import be.quodlibet.boxable.BaseTable;
import be.quodlibet.boxable.Cell;
import be.quodlibet.boxable.HorizontalAlignment;
import be.quodlibet.boxable.Row;
import be.quodlibet.boxable.VerticalAlignment;
import za.co.ipay.metermng.client.rpc.SearchRpc;
import za.co.ipay.metermng.client.rpc.UsagePointRpc;
import za.co.ipay.metermng.datatypes.ServiceResourceE;
import za.co.ipay.metermng.datatypes.StsEngineeringTokenTypeE;
import za.co.ipay.metermng.datatypes.TransItemTypeE;
import za.co.ipay.metermng.mybatis.custom.model.CustomerTransAlphaData;
import za.co.ipay.metermng.mybatis.generated.model.StsEngineeringToken;
import za.co.ipay.metermng.mybatis.generated.model.StsMeter;
import za.co.ipay.metermng.mybatis.generated.model.UnitsTrans;
import za.co.ipay.metermng.mybatis.generated.model.UpPricingStructure;
import za.co.ipay.metermng.mybatis.generated.model.PricingStructure;
import za.co.ipay.metermng.server.mybatis.service.AppSettingService;
import za.co.ipay.metermng.server.mybatis.service.CustomerTransService;
import za.co.ipay.metermng.server.mybatis.service.EngineeringTokensService;
import za.co.ipay.metermng.server.mybatis.service.MeterModelService;
import za.co.ipay.metermng.server.mybatis.service.MeterService;
import za.co.ipay.metermng.server.mybatis.service.PricingStructureService;
import za.co.ipay.metermng.server.mybatis.service.STSMeterService;
import za.co.ipay.metermng.server.mybatis.service.UsagePointService;
import za.co.ipay.metermng.server.rpc.SearchRpcImpl;
import za.co.ipay.metermng.server.rpc.UsagePointRpcImpl;
import za.co.ipay.metermng.shared.CustomerTransItemData;
import za.co.ipay.metermng.shared.CustomerUsagePointMiscInfo;
import za.co.ipay.metermng.shared.MeterMngStatics;
import za.co.ipay.metermng.shared.StsEngineeringTokenData;
import za.co.ipay.metermng.shared.dto.user.MeterMngUser;
import za.co.ipay.utils.StringUtils;

import static za.co.ipay.metermng.datatypes.TransItemTypeE.aux;
import static za.co.ipay.metermng.datatypes.TransItemTypeE.bsstRepeat;
import static za.co.ipay.metermng.datatypes.TransItemTypeE.bsstToken;
import static za.co.ipay.metermng.datatypes.TransItemTypeE.dep;
import static za.co.ipay.metermng.datatypes.TransItemTypeE.fixed;
import static za.co.ipay.metermng.datatypes.TransItemTypeE.fromId;
import static za.co.ipay.metermng.datatypes.TransItemTypeE.refund;
import static za.co.ipay.metermng.datatypes.TransItemTypeE.stdToken;

/**
 * ReprintReceiptPdfServlet is used to generate a reprinted receipt as a pdf
 * file so that the user can save the file for offline use.
 */
@SuppressWarnings("serial")
public class ReprintReceiptPdfServlet extends SuperExportDataServlet {

    private static Logger logger = Logger.getLogger(ReprintReceiptPdfServlet.class);

    private SearchRpc searchRpc;
    private EngineeringTokensService engineeringTokensService;
    private CustomerTransService customerTransService;
    private STSMeterService stsMeterService;
    private PricingStructureService pricingStructureService;
    private AppSettingService appSettingService;
    private MeterService meterService;
    private MeterModelService meterModelService;
    private UsagePointService usagePointService;
    private UsagePointRpc usagePointRpc;
    private SimpleDateFormat dateTimeFormat;
    private SimpleDateFormat dateFormat;
    DecimalFormat currencyFormat;
    private Locale locale = null;
    private MeterMngUser user = null;
    private BaseTable table = null;
    private PDDocument mainDocument = null;
    private String tokenType = null;

    @Override
    public void init(ServletConfig config) throws ServletException {
        super.init(config);
        ApplicationContext ac = (ApplicationContext) config.getServletContext()
                .getAttribute(WebApplicationContext.ROOT_WEB_APPLICATION_CONTEXT_ATTRIBUTE);
        this.engineeringTokensService = (EngineeringTokensService) ac.getBean("engineeringTokensService");
        this.customerTransService = (CustomerTransService) ac.getBean("customerTransService");
        this.stsMeterService = (STSMeterService) ac.getBean("stsMeterService");
        this.pricingStructureService = (PricingStructureService) ac.getBean("pricingStructureService");
        this.appSettingService = (AppSettingService) ac.getBean("appSettingService");
        this.meterService = (MeterService) ac.getBean("meterService");
        this.meterModelService = (MeterModelService) ac.getBean("meterModelService");
        this.usagePointService = (UsagePointService) ac.getBean("usagePointService");
        this.searchRpc = (SearchRpcImpl) ac.getBean("searchRpc");
        this.usagePointRpc = (UsagePointRpcImpl) ac.getBean("usagePointRpc");
        if (customerTransService == null || pricingStructureService == null || messageSource == null
                || formatSource == null || engineeringTokensService == null || stsMeterService == null
                || appSettingService == null || meterService == null || meterModelService == null
                || usagePointService == null || searchRpc == null || usagePointRpc == null) {
            throw new ServletException(
                    "Missing beans not set in the ReprintReceiptPdfServlet: " + engineeringTokensService + " "
                            + stsMeterService + " " + customerTransService + " " + pricingStructureService + " "
                            + appSettingService + " " + meterService + " " + meterModelService + " " + usagePointService
                            + " " + searchRpc + " " + usagePointRpc + " " + messageSource + " " + formatSource);
        }
    }

    public void doPost(HttpServletRequest request, HttpServletResponse response) throws IOException, ServletException {
        doDataExport(request, response);
    }

    public void doGet(HttpServletRequest request, HttpServletResponse response) throws IOException, ServletException {
        doDataExport(request, response);
    }

    protected void doDataExport(HttpServletRequest request, HttpServletResponse response) throws IOException {
        String localeParameter = request.getParameter("locale");
        if (localeParameter == null) {
            locale = request.getLocale();
        } else {
            locale = new Locale(localeParameter);
        }
        String datePattern = formatSource.getMessage(new DefaultMessageSourceResolvable("date.pattern"), locale);
        dateTimeFormat = new SimpleDateFormat(datePattern + " "
                + formatSource.getMessage(new DefaultMessageSourceResolvable("time.pattern"), locale));
        dateFormat = new SimpleDateFormat(datePattern);
        currencyFormat = new DecimalFormat(
                formatSource.getMessage(new DefaultMessageSourceResolvable("currency.pattern"), locale));
        try {
            user = getCurrentUser(request);
            if (user != null) {
                Long meterId = Long.valueOf(request.getParameter("meterid"));
                String customerTransId = request.getParameter("customertransid");
                boolean isEnableAccessGroups = Boolean.valueOf(request.getParameter("usingaccessgroups"));

                mainDocument = new PDDocument();
                PDPage page = new PDPage();
                table = new BaseTable(0, page.getMediaBox().getHeight() - 100, 0, 150, 50, mainDocument, page, false,
                        true);

                byte output[] = null;
                if (customerTransId == null) {
                    Long stsEngineeringTokenId = Long.valueOf(request.getParameter("stsengineeringtokenid"));
                    output = createEngineeringTokenReceipt(meterId, stsEngineeringTokenId, isEnableAccessGroups).toByteArray();
                } else {
                    output = createCreditVendReceipt(meterId, Long.valueOf(customerTransId)).toByteArray();
                }
                response.getOutputStream().write(output);

                if ("save".equals(request.getParameter("action"))) {
                    response.addHeader("Content-Type", "application/force-download");
                    response.addHeader("Content-Disposition",
                            "attachment; filename=\"" + tokenType.replace(' ', '_') + "_receipt_" + meterId + "_"
                                    + GregorianCalendar.getInstance().get(GregorianCalendar.YEAR) + ".pdf\"");
                }
            } else {
                logger.error("No session/logged in user found.");
                writeError(response, getMessage("export.denied"));
            }
        } catch (Exception e) {
            logger.error("Error exporting data:", e);
            writeError(response, getMessage("export.error"));
        }
    }

    @Override
    protected String getExportFileName(HttpServletRequest request) {
        return null;
    }

    private ByteArrayOutputStream createCreditVendReceipt(Long meterId, Long customerTransId) {
        tokenType = "Credit_Vend";
        List<CustomerTransItemData> customerTransItemData = customerTransService.getCustomerTransItem(customerTransId);
        CustomerTransAlphaData customerTransAlphaData = null;
        List<CustomerTransAlphaData> customerTransList = customerTransService.getCustomerTransByMeterId(meterId);
        for (CustomerTransAlphaData item : customerTransList) {
            if (item.getId().equals(customerTransId)) {
                customerTransAlphaData = item;
                break;
            }
        }

        ByteArrayOutputStream output = new ByteArrayOutputStream();
        try {
            Date transDate = customerTransAlphaData.getTransDate();
            addWarningLines(dateFormat.format(transDate));

            addHeader("---------------------------------------------------");
            addHeader(getMessage("reprint.credit.vend.tax.invoice"));
            addHeader("---------------------------------------------------");
            if (customerTransAlphaData.isHasEngineeringTokens()) {
                StsEngineeringToken stsEngineeringToken = engineeringTokensService
                        .getKeyChangeTokensForCustomerTrans(customerTransId);
                if (stsEngineeringToken != null) {
                    addHeader(getMessage("reprint.key.change.notice.line.1"));
                    addHeader(getMessage("reprint.key.change.notice.line.2"));
                    printTokenCodes(true, stsEngineeringToken);
                    printKeyChangeLines(stsEngineeringToken);
                    addHeader("---------------------------------------------------");
                }
            }
            addLine("reprint.util.name",
                    appSettingService.getAppSettingByKey(MeterMngStatics.APP_SETTING_REPRINT_UTILITY_NAME).getValue());
            addLine("reprint.util.dist.id", appSettingService
                    .getAppSettingByKey(MeterMngStatics.APP_SETTING_REPRINT_UTILITY_DIST_ID).getValue());
            addLine("reprint.util.vat.no", appSettingService
                    .getAppSettingByKey(MeterMngStatics.APP_SETTING_REPRINT_UTILITY_TAX_REF).getValue());
            addLine("reprint.util.address", appSettingService
                    .getAppSettingByKey(MeterMngStatics.APP_SETTING_REPRINT_UTILITY_ADDRESS).getValue());
            addLine("reprint.issued", dateTimeFormat.format(transDate), false, 30);
            addLine("search.customer.agreement.ref", customerTransAlphaData.getVendRefReceived());

            CustomerUsagePointMiscInfo customerUsagePointMiscInfo = usagePointRpc
                    .getLatestCustomerUsagePointMiscInfoByUsagePointId(customerTransAlphaData.getUsagePointId(),
                            transDate);
            if (customerUsagePointMiscInfo != null) {
                String firstnames = customerUsagePointMiscInfo.getFirstnames();
                if (firstnames == null) {
                    firstnames = "";
                }
                addLine("reprint.customer", firstnames + " " + customerUsagePointMiscInfo.getSurname(), false, 40);
                addLine("customer.agreementref", customerUsagePointMiscInfo.getAgreementRef(), false, 40);
                addLine("usagepoint.field.name", customerUsagePointMiscInfo.getUsagePointName(), false, 40);
                addHeader(getFormattedAddress(customerUsagePointMiscInfo));
            }

            addLine("meter.number", customerTransAlphaData.getMeterNumber(), true, 50);
            StsMeter stsMeter = stsMeterService.getMeterById(meterId);
            if (stsMeter != null) {
                addLine("reprint.token.tech", stsMeter.getStsTokenTechCode());
                addLine("reprint.alg", stsMeter.getStsAlgorithmCode());
                StsEngineeringToken stsEngineeringToken = engineeringTokensService
                        .getFirstEngineeringTokenByCustomerTrans(customerTransId);
                if (stsEngineeringToken == null) {
                    addLine("reprint.sgc", stsMeter.getStsCurrSupplyGroupCode());
                    addLine("meter.tariffindex.column", stsMeter.getStsCurrTariffIndex());
                    addLine("reprint.krn", stsMeter.getStsCurrKeyRevisionNum().toString());
                } else {
                    addLine("reprint.sgc", stsEngineeringToken.getNewSupGroup());
                    addLine("meter.tariffindex.column", stsEngineeringToken.getNewTariffIdx());
                    addLine("reprint.krn", stsEngineeringToken.getNewKeyRev().toString());
                }
            }

            addLine("tariff.title", pricingStructureService.getPricingStructure(customerTransAlphaData.getPricingStructureId()).getName());

            addHeader("---------------------------------------------------");

            HashMap<TransItemTypeE, Integer> receiptOrder = new HashMap<TransItemTypeE, Integer>() {
                {
                    put(stdToken, 0);
                    put(bsstToken, 1);
                    put(bsstRepeat, 2);
                    put(dep, 3);
                    put(refund, 4);
                    put(aux, 5);
                    put(fixed, 6);
                }
            };
            customerTransItemData.sort((o1, o2) -> receiptOrder.get(fromId(o1.getTransItemType()))
                    .compareTo(receiptOrder.get(fromId(o2.getTransItemType()))));

            String symbol = formatSource.getMessage(new DefaultMessageSourceResolvable("currency.symbol"), locale);
            ArrayList<String> uniqueHeadings = new ArrayList<String>();
            String resourceMessageKey = null;
            String resourceUnitKey = null;
            switch (ServiceResourceE.fromId(meterModelService
                    .getMeterModelById(meterService.getMeterById(meterId).getMeterModelId()).getServiceResourceId())) {
            case ELEC:
                resourceMessageKey = "reprint.electricity";
                resourceUnitKey = "unit.kilowatthour.symbol";
                break;
            case GAS:
                resourceMessageKey = "reprint.gas";
                resourceUnitKey = "unit.cubicmeter.symbol";
                break;
            case WATER:
                resourceMessageKey = "reprint.water";
                resourceUnitKey = "unit.kiloliter.symbol";
                break;
            }
            for (CustomerTransItemData item : customerTransItemData) {
                switch (fromId(item.getTransItemType())) {
                case stdToken:
                    addUniqueLine("reprint.your.resource.token", getMessage(resourceMessageKey), uniqueHeadings);
                    addHeader(getMessage("reprint.standard.token"));
                    addHeader(getFormattedToken(item.getToken()), true, 10);
                    addLine("reprint.receipt.nr", item.getReceiptNum(), false, 40);
                    addLine("meter.units", getMessage(resourceUnitKey), item.getUnits().toPlainString(), false, 50);
                    UnitsTrans unitsTrans = usagePointService.getUnitsTransactionFromCustomerTransId(customerTransId);
                    if (unitsTrans != null) {
                        addLine("unitsacc.balance.with.symbol", getMessage(resourceUnitKey),
                                unitsTrans.getResultantBalance().setScale(1, RoundingMode.HALF_UP).toPlainString(),
                                false, 60);
                    }
                    BigDecimal amtTax = item.getAmtTax();
                    addLine("usagepoint.txn.amt",
                            symbol + currencyFormat.format(item.getAmtInclTax().subtract(amtTax)));
                    addLine("customer.txn.tax", symbol + currencyFormat.format(amtTax));
                    if (item.getTariff() != null) {
                        addMultiLineValue("tariff.title", item.getTariff().split(":"),0);
                    }

                    addHeader("---------------------------------------------------");
                    break;
                case fixed:
                    handleMultipleItemsCreation(item, "reprint.fixed.items", uniqueHeadings);
                    break;
                case bsstToken:
                case bsstRepeat:
                    addUniqueLine("reprint.your.resource.token", getMessage(resourceMessageKey), uniqueHeadings);
                    addHeader(getMessage("reprint.free.basic.resource", getMessage(resourceMessageKey)));
                    addHeader(getFormattedToken(item.getToken()), true, 10);
                    addLine("reprint.receipt.nr", item.getReceiptNum(), false, 40);
                    addLine("meter.txn.date", dateTimeFormat.format(transDate), false, 30);
                    addLine("meter.units", getMessage(resourceUnitKey), item.getUnits().toPlainString(), false, 50);
                    addHeader("---------------------------------------------------");
                    break;
                case aux:
                    handleMultipleItemsCreation(item, "reprint.debt.items", uniqueHeadings);
                    break;
                case dep:
                    handleMultipleItemsCreation(item, "reprint.deposit", uniqueHeadings);
                    break;
                case refund:
                    handleMultipleItemsCreation(item, "customer.auxaccount.balance.type.refund", uniqueHeadings);
                    break;
                default:
                }
            }

            BigDecimal amtInclTax = customerTransAlphaData.getAmtInclTax();
            addLine("reprint.total.tax",
                    symbol + currencyFormat.format(customerTransAlphaData.getAmtTax()));
            addLine("reprint.total.tax-inclusive", symbol + currencyFormat.format(amtInclTax));
            addHeader("---------------------------------------------------");

            table.draw();
            mainDocument.save(output);
            mainDocument.close();
        } catch (IOException ioEx) {
            System.out.println("Exception while trying to create blank document - " + ioEx);
        } finally {
            if (mainDocument != null) {
                try {
                    mainDocument.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return output;
    }

    private ByteArrayOutputStream createEngineeringTokenReceipt(Long meterId, Long stsEngineeringTokenId, boolean isEnableAccessGroups) {
        StsEngineeringTokenData stsEngineeringTokenData = null;
        ArrayList<StsEngineeringTokenData> tokens = engineeringTokensService.getEngineeringTokensByMeterId(meterId, isEnableAccessGroups);
        // TODO: replace true with isEnableAccessGroups
        for (StsEngineeringTokenData item : tokens) {
            if (item.getId().equals(stsEngineeringTokenId)) {
                stsEngineeringTokenData = item;
                break;
            }
        }

        ByteArrayOutputStream output = new ByteArrayOutputStream();
        try {
            Date transDate = stsEngineeringTokenData.getTransDate();
            addWarningLines(dateFormat.format(transDate));
            addHeader(appSettingService.getAppSettingByKey(MeterMngStatics.APP_SETTING_REPRINT_UTILITY_NAME).getValue(),
                    true, 10);
            addHeader(appSettingService.getAppSettingByKey(MeterMngStatics.APP_SETTING_REPRINT_UTILITY_ADDRESS)
                    .getValue(), true, 10);
            tokenType = stsEngineeringTokenData.getTokenTypeName();
            addHeader(tokenType, true, 12);
            addLine("meter.txn.date", dateTimeFormat.format(transDate), false, 40);
            addLine("meter.txn.usagepoint", stsEngineeringTokenData.getUsagePointName(), false, 40);
            addLine("meter.number", stsEngineeringTokenData.getMeterNumber(), true, 50);

            boolean isKeyChange = false;
            switch (StsEngineeringTokenTypeE.fromName(tokenType)) {
            case FREE_ISSUE:
                String resourceUnitKey = null;
                switch (ServiceResourceE.fromId(
                        meterModelService.getMeterModelById(meterService.getMeterById(meterId).getMeterModelId())
                                .getServiceResourceId())) {
                case ELEC:
                    resourceUnitKey = "unit.kilowatthour.symbol";
                    break;
                case GAS:
                    resourceUnitKey = "unit.cubicmeter.symbol";
                    break;
                case WATER:
                    resourceUnitKey = "unit.kiloliter.symbol";
                    break;
                }
                addLine("meter.units", getMessage(resourceUnitKey), stsEngineeringTokenData.getUnits().toPlainString(),
                        false, 50);
                break;
            case SET_PHASE:
                addLine("meter.units", getMessage("unit.watts.symbol"),
                        stsEngineeringTokenData.getUnits().toPlainString(), false, 50);
                printStsDetailLines(stsEngineeringTokenData);
                break;
            case POWER_LIMIT:
                addLine("meter.powerlimit.units.w", stsEngineeringTokenData.getUnits().toPlainString());
                printStsDetailLines(stsEngineeringTokenData);
                break;
            case CLEAR_TAMPER:
            case CLEAR_CREDIT:
                printStsDetailLines(stsEngineeringTokenData);
                break;
            case KEY_CHANGE:
                printKeyChangeLines(stsEngineeringTokenData);
                isKeyChange = true;
                break;
            default:
            }
            addLine("meter.description", stsEngineeringTokenData.getDescription(), false, 40);
            addLine("meter.txn.user", stsEngineeringTokenData.getUserRecEntered());
            printTokenCodes(isKeyChange, stsEngineeringTokenData);
            table.draw();

            mainDocument.save(output);
            mainDocument.close();
        } catch (IOException ioEx) {
            System.out.println("Exception while trying to create blank document - " + ioEx);
        } finally {
            if (mainDocument != null) {
                try {
                    mainDocument.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return output;
    }

    private void printKeyChangeLines(StsEngineeringToken stsEngineeringToken) {
        addLine("meter.old.supplygroupcode", stsEngineeringToken.getOldSupGroup(), false, 70);
        addLine("meter.new.supplygroupcode", stsEngineeringToken.getNewSupGroup(), false, 70);
        addLine("meter.old.keyrevisionnum", stsEngineeringToken.getOldKeyRev().toString(), false, 80);
        addLine("meter.new.keyrevisionnum", stsEngineeringToken.getNewKeyRev().toString(), false, 80);
        addLine("meter.old.tariffindex", stsEngineeringToken.getOldTariffIdx());
        addLine("meter.new.tariffindex", stsEngineeringToken.getNewTariffIdx());
    }

    private void printStsDetailLines(StsEngineeringToken stsEngineeringToken) {
        addLine("reprint.sgc", stsEngineeringToken.getNewSupGroup(), false, 70);
        addLine("reprint.krn", stsEngineeringToken.getNewKeyRev().toString(), false, 70);
        addLine("meter.tariffindex.column", stsEngineeringToken.getNewTariffIdx());

    }

    private void printTokenCodes(boolean isKeyChange, StsEngineeringToken stsEngineeringToken) {
        String tokenCodeName = "meter.token.code";
        if (isKeyChange) {
            tokenCodeName = "meter.token.code1";
        }
        addHeader(getMessage(tokenCodeName) + ":", true, 0);
        addHeader(getFormattedToken(stsEngineeringToken.getToken1()), true, 10);
        if (isKeyChange) {
            addHeader(getMessage("meter.token.code2") + ":", true, 0);
            addHeader(getFormattedToken(stsEngineeringToken.getToken2()), true, 10);
            if (stsEngineeringToken.getToken3() != null && !stsEngineeringToken.getToken3().isEmpty()) {
                addHeader(getMessage("meter.token.code3") + ":", true, 0);
                addHeader(getFormattedToken(stsEngineeringToken.getToken3()), true, 10);
            }
            if (stsEngineeringToken.getToken4() != null && !stsEngineeringToken.getToken4().isEmpty()) {
                addHeader(getMessage("meter.token.code4") + ":", true, 0);
                addHeader(getFormattedToken(stsEngineeringToken.getToken4()), true, 10);
            }
        }
    }

    private Cell<PDPage> addHeader(String text) {
        return addHeader(text, false, 0);
    }

    private Cell<PDPage> addHeader(String text, boolean isBold, int fontSize) {
        Cell<PDPage> cell = table.createRow(0).createCell(100, text, HorizontalAlignment.CENTER,
                VerticalAlignment.MIDDLE);
        cell.setBottomPadding(0);
        cell.setTopPadding(0);
        if (isBold) {
            cell.setFont(PDType1Font.HELVETICA_BOLD);
        }
        if (fontSize != 0) {
            cell.setFontSize(fontSize);
        }
        return cell;
    }

    private void addMultiLineValue(String name, String[] values, int leftSize) {
        for (int i=0; i<values.length; i++) {
            addLine(name, values[i], false, leftSize);
            name = null;
        }
    }

    private void addLine(String name, String value) {
        addLine(name, value, false, 50);
    }

    private void addLine(String name, String value, boolean isBold, int leftSize) {
        addLine(name, null, value, isBold, leftSize);
    }

    private void addLine(String name, String parameter, String value, boolean isBold, int leftSize) {
        Row<PDPage> row = table.createRow(0);
        String label = StringUtils.isNullOrEmpty(name)?"":getMessage(name, parameter) + ":";
        Cell<PDPage> cell = row.createCell(leftSize, label, HorizontalAlignment.LEFT,
                VerticalAlignment.TOP);
        cell.setBottomPadding(0);
        cell.setTopPadding(0);
        if (isBold) {
            cell.setFont(PDType1Font.HELVETICA_BOLD);
        }
        cell = row.createCell(100 - leftSize, value, HorizontalAlignment.RIGHT, VerticalAlignment.MIDDLE);
        cell.setBottomPadding(0);
        cell.setTopPadding(0);
        if (isBold) {
            cell.setFont(PDType1Font.HELVETICA_BOLD);
        }
    }

    private String getMessage(String key) {
        return messageSource.getMessage(new DefaultMessageSourceResolvable(key), locale);
    }

    private String getMessage(String key, String parameter) {
        return messageSource.getMessage(
                new DefaultMessageSourceResolvable(new String[] { key }, new String[] { parameter }), locale);
    }

    private Cell<PDPage> addUniqueLine(String key, String parameter, ArrayList<String> uniqueHeadings) {
        if (!uniqueHeadings.contains(key)) {
            uniqueHeadings.add(key);
            return addHeader(getMessage(key, parameter));
        }
        return null;
    }

    private void handleMultipleItemsCreation(CustomerTransItemData item, String key, ArrayList<String> uniqueHeadings) {
        Cell<PDPage> cell = addUniqueLine(key, null, uniqueHeadings);
        if (cell != null) {
            cell.setFont(PDType1Font.HELVETICA_BOLD);
        }
        addHeader(item.getToken(), true, 0);
        BigDecimal amtTax = item.getAmtTax();
        String symbol = formatSource.getMessage(new DefaultMessageSourceResolvable("currency.symbol"), locale);
        if (!StringUtils.isNullOrEmpty(item.getDescription())) {
            addLine("reprint.desc", item.getDescription(), false, 40);
        }
        if (!StringUtils.isNullOrEmpty(item.getReceiptNum())) {
            addLine("reprint.receipt.nr", item.getReceiptNum(), false, 40);
        }
        addLine("usagepoint.txn.amt", symbol + currencyFormat.format(item.getAmtInclTax().subtract(amtTax)));
        addLine("customer.txn.tax", symbol + currencyFormat.format(amtTax));
        if (item.getRemBalance() != null) {
            addLine("reprint.remaining.balance", symbol + currencyFormat.format(item.getRemBalance()));
        }
        addHeader("---------------------------------------------------");
    }

    private void addWarningLines(String transDate) {
        addHeader(getMessage("reprint.warning.line.1"));
        addHeader(getMessage("reprint.warning.line.2", transDate));
        addHeader(getMessage("reprint.warning.line.3"));
        addHeader(getMessage("reprint.warning.line.4", dateTimeFormat.format(new Date())));
        addHeader(getMessage("reprint.warning.line.5", user.getUserName()));
    }

    private String getFormattedToken(String key) {
        int keyLength = key.length();
        int a = keyLength / 4;
        String token = "";
        for (int i = 0; i < a; i++) {
            token += key.substring(i * 4, (i + 1) * 4).concat(" ");
        }
        return token += key.substring(a * 4, keyLength);
    }

    private String getFormattedAddress(CustomerUsagePointMiscInfo customerUsagePointMiscInfo) {
        String addressLine1 = customerUsagePointMiscInfo.getAddressLine1();
        String addressLine2 = customerUsagePointMiscInfo.getAddressLine2();
        String addressLine3 = customerUsagePointMiscInfo.getAddressLine3();
        StringBuilder sb = new StringBuilder();
        if (addressLine1 != null && !addressLine1.isEmpty()) {
            sb.append(addressLine1).append(", ");
        }
        if (addressLine2 != null && !addressLine2.isEmpty()) {
            sb.append(addressLine2).append(", ");
        }
        if (addressLine3 != null && !addressLine3.isEmpty()) {
            sb.append(addressLine3).append(", ");
        }

        if (sb.length() > 2) {
            return sb.substring(0, sb.length() - 2);
        }
        return "";
    }
}
