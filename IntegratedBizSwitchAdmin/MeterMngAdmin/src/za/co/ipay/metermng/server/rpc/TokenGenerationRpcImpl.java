package za.co.ipay.metermng.server.rpc;

import com.google.gwt.user.server.rpc.RemoteServiceServlet;
import za.co.ipay.ipayxml.elec.VendResMessage;
import za.co.ipay.ipayxml.elec.VendResMessage.StdToken;
import za.co.ipay.ipayxml.metermng.StsEngTokenResMessage;
import za.co.ipay.metermng.client.rpc.TokenGenerationRpc;
import za.co.ipay.metermng.datatypes.StsEngineeringTokenTypeE;
import za.co.ipay.metermng.engineering.AuxFreeIssue;
import za.co.ipay.metermng.engineering.AuxFreeIssue.AuxFreeIssueResult;
import za.co.ipay.metermng.engineering.DisplayTokens;
import za.co.ipay.metermng.engineering.DisplayTokens.DisplayTokensResult;
import za.co.ipay.metermng.mybatis.generated.model.SpecialActionReasonsLog;
import za.co.ipay.metermng.mybatis.generated.model.StsMeter;
import za.co.ipay.metermng.server.mybatis.service.TokenGenerationService;
import za.co.ipay.metermng.shared.TokenData;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;

public class TokenGenerationRpcImpl extends RemoteServiceServlet implements TokenGenerationRpc {

    private static final long serialVersionUID = -6302703275560897722L;

    private AuxFreeIssue auxFreeIssue;
    private DisplayTokens displayTokens;
    private TokenGenerationService tokenGenerationService;

    public void setAuxFreeIssue(AuxFreeIssue auxFreeIssue) {
        this.auxFreeIssue = auxFreeIssue;
    }

    public void setDisplayTokens(DisplayTokens displayTokens) {
        this.displayTokens = displayTokens;
    }

    public void setTokenGenerationService(TokenGenerationService tokenGenerationService) {
        this.tokenGenerationService = tokenGenerationService;
    }

    @Override
    public TokenData requestFreeTokenUnits(String description, Long usagePointId, StsMeter stsMeter, BigDecimal kwh,
            SpecialActionReasonsLog specialActionReasonsLog, String userReference) {
        return processStsEngTokenMessage(description, stsMeter.getMeterNum(), userReference,
                StsEngineeringTokenTypeE.FREE_ISSUE, new Integer(kwh.movePointRight(1).toString()),
                specialActionReasonsLog);
    }

    @Override
    public TokenData requestFreeTokenCurrency(String description, Long usagePointId, StsMeter stsMeter,
            BigDecimal currencyAmount, SpecialActionReasonsLog specialActionReasonsLog, String userReference) {
        TokenData tokenData = null;
        AuxFreeIssueResult res = null;
        try {
            res = auxFreeIssue.requestFreeToken(description, usagePointId, stsMeter.getMeterNum(), currencyAmount);
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (res != null) {
            tokenData = new TokenData();
            try {
                VendResMessage resp = res.freeIssueRes.getVendRes();
                if (resp == null) {
                    tokenData.setErrorMsg(res.freeIssueRes.getRes());
                } else {
                    if (resp.getResCode().endsWith("000")) {
                        StdToken[] tokenz = resp.getStdTokens();
                        if (tokenz == null || tokenz.length == 0) {
                            if (resp.getCustomerMsg() != null && !resp.getCustomerMsg().isEmpty()) {
                                tokenData.setCustomerMsg(resp.getCustomerMsg());
                            }
                        } else {
                            ArrayList<String> codez = new ArrayList<String>(tokenz.length);
                            for (int i = 0; i < tokenz.length; i++) {
                                codez.add(tokenz[i].getToken());
                            }
                            tokenData.setStandardTokens(codez);
                        }
                    } else {
                        tokenData.setErrorMsg(resp.getRes());
                    }
                }
            } catch (Exception e) {
                tokenData = null;
            }
        }
        return tokenData;
    }

    @Override
    public TokenData requestClearTamperToken(String description, Long usagePointId, StsMeter stsMeter,
            String userReference) {
        return processStsEngTokenMessage(description, stsMeter.getMeterNum(), userReference,
                StsEngineeringTokenTypeE.CLEAR_TAMPER, null, null);
    }

    @Override
    public TokenData requestPowerLimitToken(String description, Long usagePointId, StsMeter stsMeter, int unitsInWatts,
            String userReference) {
        return processStsEngTokenMessage(description, stsMeter.getMeterNum(), userReference,
                StsEngineeringTokenTypeE.POWER_LIMIT, unitsInWatts, null);
    }

    @Override
    public TokenData requestClearCreditToken(String description, Long usagePointId, StsMeter stsMeter, int register,
            String userReference) {
        return processStsEngTokenMessage(description, stsMeter.getMeterNum(), userReference,
                StsEngineeringTokenTypeE.CLEAR_CREDIT, register, null);
    }

    @Override
    public TokenData requestKeyChangeTokens(String description, Long usagePointId, StsMeter stsMeter,
            String userReference) {
        return processStsEngTokenMessage(description, stsMeter.getMeterNum(), userReference,
                StsEngineeringTokenTypeE.KEY_CHANGE, null, null);
    }

    @Override
    public TokenData requestSetPhaseToken(String description, Long usagePointId, StsMeter stsMeter, int unitsInWatts,
            String userReference) {
        return processStsEngTokenMessage(description, stsMeter.getMeterNum(), userReference,
                StsEngineeringTokenTypeE.SET_PHASE, unitsInWatts, null);
    }

    @Override
    public TokenData requestClearReverseFlagToken(String description, StsMeter stsMeter, String userReference) {
        return processStsEngTokenMessage(description, stsMeter.getMeterNum(), userReference,
                StsEngineeringTokenTypeE.CLEAR_REVERSE_FLAG, null, null);
    }

    @Override
    public TokenData requestDisableTripLimitToken(String description, StsMeter stsMeter, String userReference) {
        return processStsEngTokenMessage(description, stsMeter.getMeterNum(), userReference,
                StsEngineeringTokenTypeE.DISABLE_TRIP_LIMIT, null, null);
    }

    @Override
    public TokenData requestInitiateMeterTestDisplayToken(int displayControlField, boolean manufacturerCode4digits) {
        TokenData tokenData = null;
        DisplayTokensResult res = null;
        try {
            res = displayTokens.requestDisplayToken(displayControlField, manufacturerCode4digits);
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (res != null) {
            tokenData = new TokenData();
            tokenData.setResCode(res.getResCode());
            if (res.getErrorMsg() != null) {
                tokenData.setErrorMsg(res.getErrorMsg());
            } else {
                tokenData.addEngineeringTokenCode(res.getToken());
            }
        }
        return tokenData;
    }

    private TokenData processStsEngTokenMessage(String description, String meterNumber, String userReference,
            StsEngineeringTokenTypeE tokenType, Integer units, SpecialActionReasonsLog specialActionReasonsLog) {
        TokenData tokenData = null;
        StsEngTokenResMessage res = null;
        try {
            res = tokenGenerationService.sendStsEngTokenReqMessage(description, meterNumber, userReference, tokenType,
                    units, specialActionReasonsLog);
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (res != null) {
            tokenData = new TokenData();
            tokenData.setResCode(res.getResCode());
            if (res.isSuccessful()) {
                tokenData.setEngineeringTokenCodes(new ArrayList<String>(Arrays.asList(res.getTokenCodes())));
            } else {
                tokenData.setErrorMsg(res.getRes());
            }
        }
        return tokenData;
    }
}
