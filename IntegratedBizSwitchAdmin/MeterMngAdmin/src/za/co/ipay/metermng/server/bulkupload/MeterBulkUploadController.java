package za.co.ipay.metermng.server.bulkupload;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.support.DefaultMessageSourceResolvable;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;

import za.co.ipay.metermng.mybatis.generated.model.AppSetting;
import za.co.ipay.metermng.server.mybatis.service.AppSettingService;
import za.co.ipay.metermng.server.mybatis.service.BulkUploadService;
import za.co.ipay.metermng.shared.bulkupload.BulkUploadException;
import za.co.ipay.metermng.shared.bulkupload.dto.metercustupuploaddata.MeterBulkCsvData;
import za.co.ipay.metermng.shared.dto.MeterData;
import za.co.ipay.metermng.shared.dto.uploaddata.metercustupuploaddata.MeterBulkCsvMapToData;

@Controller
@RequestMapping(value = "/**/secure/meterbulkupload.do")
public class MeterBulkUploadController extends MeterBulkUploadCommon {

    static final boolean ISMETERUPLOADONLY = true;

    @Autowired
    private BulkUploadService bulkUploadService;
    @Autowired
    private AppSettingService appSettingService;

    private Logger logger = Logger.getLogger(MeterBulkUploadController.class.getName());

    private MeterBulkCsvMapToData csvMapData;

    private ArrayList<MeterBulkCsvData> meterBulkCsvDataList;
    private ArrayList<MeterData> meterDataList;
    private int noDupsIgnored = 0;

    public void setupDataMapping() {
        List<AppSetting> appSettings = appSettingService.getAppSettingsForUPAndMeterAndCustomerCustomFields();
        customFieldStatusRequired = importMessageSource.getMessage(new DefaultMessageSourceResolvable("user.custom.field.status.required"), null).toLowerCase();
        String customFieldStatusUnavailable = importMessageSource.getMessage(new DefaultMessageSourceResolvable("user.custom.field.status.unavailable"), null).toLowerCase();
        csvMapData = new MeterBulkCsvMapToData(appSettings, customFieldStatusUnavailable);
        meterCustomFieldDtoList = csvMapData.getMeterCustomFieldDtoList();
        csvFieldMap = new HashMap<Integer, String>();
        headingIndicator = "Meter Num";
        setMeterOnlyUpload(ISMETERUPLOADONLY);
        formFieldsMap = userInterfaceService.getFormFields();
    }

    @Override
    void constructCsvFieldMap(String headingLine) throws BulkUploadException {
        csvFieldMap = csvMapData.constructCsvFieldMap(headingLine);
    }

    //*********************************************************************************************
    //********** VALIDATE UPLOADED FILE (FILE STRUCTURE THEN CONTENT ******************************
    @Override
    void validationPreProcessing() {
        setupDataMapping();
        checkPreLoadedTables(getUser().getCurrentGroupId());
        noDupsIgnored = 0;
    }

    @Override
    String validateTrans(String currentRow) {
        MeterBulkCsvData meterBulkCsvData = new MeterBulkCsvData(csvFieldMap, currentRow, false);
        StringBuilder validationError = new StringBuilder();

        if (meterBulkCsvData.isUnexpectedCommas()) {
            validationError.append("bulk.upload.invalid.unexpected.commas");
            return validationError.toString();
        }

        //-----------------------------------------------------------------------------------------------------------------------
        //VALIDATIONS - METER (handles all meter & stsmeter data validation)
        validationError = validateMeterData(validationError, meterBulkCsvData);

        String returnString = validationError.toString();
        int lastIndx = returnString.lastIndexOf(";");
        if (lastIndx > 0) {
            returnString = returnString.substring(0, lastIndx);
        }
        return returnString;
    }

    @Override
    void processTransactionsPreProcessing() {
        setupDataMapping();
        meterBulkCsvDataList = new ArrayList<MeterBulkCsvData>(); //TODO : remove this guy
        meterDataList = new ArrayList<MeterData>();
        meterNumList = new ArrayList<String>();
        checkPreLoadedTables(getUser().getCurrentGroupId());
        noDupsIgnored = 0;
    }

    @Override
    void addToProcessList(String thisLine) {
        MeterBulkCsvData meterBulkCsvData = new MeterBulkCsvData(csvFieldMap, thisLine, false);
        
        if (ignoreDups) {
            String meterNum = meterBulkCsvData.getMeterNum();
            if (isDupMeter(meterNum)) {
                logger.info("MeterOnly Bulk Upload: " + fileName + " Ignored meter: " + meterBulkCsvData.toString());
                noDupsIgnored++;
                return;
            } else {
                meterNumList.add(meterNum);
            }
        }
        
        meterBulkCsvDataList.add(meterBulkCsvData);
        meterDataList.add(createMeterData(meterBulkCsvData));
    }

    @Override
    String processUpload() {
        logger.info("**** Bulk Meter upload input listSize=" + meterDataList.size());
        return bulkUploadService.meterBulkUpload(meterDataList, noDupsIgnored);
    }
}
