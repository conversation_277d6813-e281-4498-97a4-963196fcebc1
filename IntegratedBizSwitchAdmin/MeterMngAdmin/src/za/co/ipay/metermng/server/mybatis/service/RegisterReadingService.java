package za.co.ipay.metermng.server.mybatis.service;

import java.util.Date;
import java.util.List;

import org.springframework.transaction.annotation.Transactional;

import za.co.ipay.metermng.mybatis.generated.mapper.RegisterReadingMapper;
import za.co.ipay.metermng.mybatis.generated.model.RegisterReading;
import za.co.ipay.metermng.mybatis.generated.model.RegisterReadingExample;
import za.co.ipay.metermng.shared.dto.MdcChannelReadingsDto;
import za.co.ipay.metermng.shared.dto.usagepoint.MeterUpMdcChannelInfo;

public class RegisterReadingService {

    private RegisterReadingMapper registerReadingMapper;
    
    public void setRegisterReadingMapper(RegisterReadingMapper registerReadingMapper) {
        this.registerReadingMapper = registerReadingMapper;
    }

    //---------------------------------------------------------------------------------------------------------------------------------
    @Transactional
    public void updateRegisterReadingsAfterDate(Long meterId, Long usagePointId, Date installationDate) {
        RegisterReadingExample registerReadingExample = new RegisterReadingExample();
        registerReadingExample.createCriteria().andMeterIdEqualTo(meterId).andReadingTimestampGreaterThanOrEqualTo(installationDate);
        RegisterReading registerReading = new RegisterReading();
        registerReading.setUsagePointId(usagePointId);
        registerReading.setDateCreated(null);
        registerReadingMapper.updateByExampleSelective(registerReading, registerReadingExample);
    }
    
    @Transactional
    public int countRegisterReadingsForMeterFromDate(Long meterId, Date date) { 
        RegisterReadingExample registerReadingExample = new RegisterReadingExample();
        registerReadingExample.createCriteria().andMeterIdEqualTo(meterId).andReadingTimestampGreaterThanOrEqualTo(date);
        return registerReadingMapper.countByExample(registerReadingExample);
    }
    
    @Transactional
    public void generateInitialRegisterReadingsforChannels(List<MdcChannelReadingsDto> channelReadingsList, Long usagePointId, Long meterId, Date installDate, Long upMeterInstallId) {
        for (MdcChannelReadingsDto mcr : channelReadingsList) {
            RegisterReading rr = new RegisterReading();
            rr.setMeterId(meterId);
            rr.setDateCreated(new Date());
            rr.setReadingTimestamp(mcr.getReadingTimestamp());
            rr.setReadingValue(mcr.getInitialReading());
            rr.setReadingType(mcr.getReadingTypeValue());
            rr.setCustomerTransId(null);
            rr.setMeterReadingTypeId(mcr.getMeterReadingTypeId());
            rr.setUsagePointId(usagePointId);
            rr.setMdcChannelId(mcr.getId());
            rr.setUpMeterInstallId(upMeterInstallId);
            registerReadingMapper.insert(rr);
        }
    }
    
    @Transactional
    public List<RegisterReading> fetchRegisterReadingsForMeterUsagepointUpmeterinstall(Long meterId, Long usagePointId, Long upMeterInstallId) { 
        RegisterReadingExample registerReadingExample = new RegisterReadingExample();
        registerReadingExample.createCriteria().andMeterIdEqualTo(meterId)
                                                .andUsagePointIdEqualTo(usagePointId)
                                                .andUpMeterInstallIdEqualTo(upMeterInstallId);
        registerReadingExample.setOrderByClause("mdc_channel_id asc");
        return registerReadingMapper.selectByExample(registerReadingExample);
    }
    
    @Transactional
    public RegisterReading fetchRegisterReadingsForMeterUsagepointUpmeterinstallChannel(MeterUpMdcChannelInfo meterUpMdcChannelInfo, Long channelId) { 
        RegisterReadingExample registerReadingExample = new RegisterReadingExample();
        registerReadingExample.createCriteria().andMeterIdEqualTo(meterUpMdcChannelInfo.getMeterId())
                                                .andUsagePointIdEqualTo(meterUpMdcChannelInfo.getUsagePointId())
                                                .andUpMeterInstallIdEqualTo(meterUpMdcChannelInfo.getUpMeterInstallId())
                                                .andMdcChannelIdEqualTo(channelId);
        registerReadingExample.setOrderByClause("reading_timestamp desc");
        List<RegisterReading> regReadList = registerReadingMapper.selectByExample(registerReadingExample);
        if (regReadList != null && ! regReadList.isEmpty()) {
            return regReadList.get(0);
        } else {
            return null;
        }
    }
    
    
    @Transactional
    public RegisterReading fetchRegisterReadingsForMeterUsagepointUpmeterinstallOldChannels(MeterUpMdcChannelInfo meterUpMdcChannelInfo, List<Long> currentChannels) { 
        RegisterReadingExample registerReadingExample = new RegisterReadingExample();
        registerReadingExample.createCriteria().andMeterIdEqualTo(meterUpMdcChannelInfo.getMeterId())
                                                .andUsagePointIdEqualTo(meterUpMdcChannelInfo.getUsagePointId())
                                                .andUpMeterInstallIdEqualTo(meterUpMdcChannelInfo.getUpMeterInstallId())
                                                .andMdcChannelIdNotIn(currentChannels);
        registerReadingExample.setOrderByClause("reading_timestamp desc");
        List<RegisterReading> regReadList = registerReadingMapper.selectByExample(registerReadingExample);
        if (regReadList != null && ! regReadList.isEmpty()) {
            return regReadList.get(0);
        } else {
            return null;
        }
    }
}
