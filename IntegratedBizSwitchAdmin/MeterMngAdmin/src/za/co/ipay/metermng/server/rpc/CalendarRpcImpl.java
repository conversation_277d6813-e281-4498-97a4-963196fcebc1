package za.co.ipay.metermng.server.rpc;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.GregorianCalendar;
import java.util.Iterator;
import java.util.List;

import org.apache.log4j.Logger;

import za.co.ipay.gwt.common.shared.exception.AccessControlException;
import za.co.ipay.gwt.common.shared.exception.ServiceException;
import za.co.ipay.gwt.common.shared.exception.ValidationException;
import za.co.ipay.metermng.client.rpc.CalendarRpc;
import za.co.ipay.metermng.datatypes.RecordStatus;
import za.co.ipay.metermng.mybatis.generated.model.TouCalendar;
import za.co.ipay.metermng.mybatis.generated.model.TouCalendarSeason;
import za.co.ipay.metermng.mybatis.generated.model.TouDayProfile;
import za.co.ipay.metermng.mybatis.generated.model.TouPeriod;
import za.co.ipay.metermng.mybatis.generated.model.TouSeason;
import za.co.ipay.metermng.server.mybatis.service.CalendarService;
import za.co.ipay.metermng.server.validation.ServerValidatorUtil;
import za.co.ipay.metermng.shared.DayProfileSuggestion;
import za.co.ipay.metermng.shared.PeriodSuggestion;
import za.co.ipay.metermng.shared.SeasonSuggestion;
import za.co.ipay.metermng.shared.TouCalendarData;
import za.co.ipay.metermng.shared.TouDayProfileData;
import za.co.ipay.metermng.shared.TouDayProfileTimeData;
import za.co.ipay.metermng.shared.TouSeasonDateData;
import za.co.ipay.metermng.shared.TouSpecialDayData;
import za.co.ipay.metermng.shared.dto.pricing.TouCalendarSeasonsPeriodsSpecialDaysData;

import com.google.gwt.user.client.ui.SuggestOracle;
import com.google.gwt.user.client.ui.SuggestOracle.Request;
import com.google.gwt.user.client.ui.SuggestOracle.Response;


public class CalendarRpcImpl extends BaseMeterMngRpc implements CalendarRpc {

    private static final long serialVersionUID = 4633307421034665424L;
    
    private CalendarService calendarService;
    
    public CalendarRpcImpl() {
        logger = Logger.getLogger(CalendarRpcImpl.class);
    }
    
    public void setCalendarService(CalendarService calendarService) {
        this.calendarService = calendarService;
    }

    @Override
    public ArrayList<TouSeason> getSeasons() throws ValidationException, ServiceException, AccessControlException {
        return calendarService.getTouSeasons();
    }
    
    @Override
    public Response getSeasonSuggestions(Request req) throws ServiceException {
        SuggestOracle.Response response = new SuggestOracle.Response();
        ArrayList<SeasonSuggestion> suggestions = new ArrayList<SeasonSuggestion>();
        String thequery = (req.getQuery() + "%").toLowerCase();
        List<TouSeason> list = calendarService.getSeasonSearchSuggestions(thequery);
        Iterator<TouSeason> listIt = list.iterator();
        SeasonSuggestion seasonSuggestion;
        TouSeason season;
        while (listIt.hasNext()) {
            season = listIt.next();
            seasonSuggestion = new SeasonSuggestion();
            seasonSuggestion.setOriginalQuery(req.getQuery());
            seasonSuggestion.setSuggestion(season.getName());
            seasonSuggestion.setId(season.getId());
            suggestions.add(seasonSuggestion);
        }
        response.setSuggestions(suggestions);
        return response;
    }
    
    @Override
    public Response getPeriodSuggestions(Request req) throws ServiceException {
        SuggestOracle.Response response = new SuggestOracle.Response();
        ArrayList<PeriodSuggestion> suggestions = new ArrayList<PeriodSuggestion>();
        String thequery = (req.getQuery() + "%").toLowerCase();
        List<TouPeriod> list = calendarService.getPeriodSearchSuggestions(thequery);
        Iterator<TouPeriod> listIt = list.iterator();
        PeriodSuggestion periodSuggestion;
        TouPeriod period;
        while (listIt.hasNext()) {
            period = listIt.next();
            periodSuggestion = new PeriodSuggestion();
            periodSuggestion.setOriginalQuery(req.getQuery());
            periodSuggestion.setSuggestion(period.getCode()+"-"+period.getName());
            periodSuggestion.setId(period.getId());
            periodSuggestion.setPeriodCode(period.getCode());
            suggestions.add(periodSuggestion);
        }
        response.setSuggestions(suggestions);
        return response;
    }

    @Override
    public TouSeason addSeason(TouSeason add) throws ValidationException, ServiceException, AccessControlException {
        ServerValidatorUtil.getInstance().validateDataForValidationMessages(add);
        return calendarService.insertSeason(add);
    }

    @Override
    public TouSeason updateSeason(TouSeason updated) throws ValidationException, ServiceException, AccessControlException {
        ServerValidatorUtil.getInstance().validateDataForValidationMessages(updated);
        return calendarService.updateSeason(updated);
    }

    @Override
    public TouSeason deleteSeason(TouSeason del) throws ServiceException, AccessControlException {
        return calendarService.deleteSeason(del);
    }

    @Override
    public ArrayList<TouPeriod> getPeriods() throws ValidationException, ServiceException, AccessControlException {
        return calendarService.getTouPeriods();
    }

    @Override
    public TouPeriod addPeriod(TouPeriod add) throws ValidationException, ServiceException, AccessControlException {
        ServerValidatorUtil.getInstance().validateDataForValidationMessages(add);
        return calendarService.insertPeriod(add);
    }

    @Override
    public TouPeriod updatePeriod(TouPeriod updated) throws ValidationException, ServiceException, AccessControlException {
        ServerValidatorUtil.getInstance().validateDataForValidationMessages(updated);
        return calendarService.updatePeriod(updated);
    }

    @Override
    public TouPeriod deletePeriod(TouPeriod updated) throws ServiceException, AccessControlException {
        return calendarService.deletePeriod(updated);
    }

    @Override
    public ArrayList<TouCalendar> getCalendars() throws ValidationException, ServiceException, AccessControlException {
        return calendarService.getTouCalendars();
    }

    @Override
    public TouCalendarData getCalendar(Long theCalendarId) throws ValidationException, ServiceException, AccessControlException {
        return calendarService.getTouCalendarData(theCalendarId);
    }

    @Override
    public ArrayList<TouDayProfileData> getDayProfilesByCalendar(Long calendarId) {
        return calendarService.getTouDayProfiles(calendarId);
    }

    @Override
    public Response getDayProfileSuggestions(Long calendarId, Long seasonId, Request request) {
        SuggestOracle.Response response = new SuggestOracle.Response();
        ArrayList<DayProfileSuggestion> suggestions = new ArrayList<DayProfileSuggestion>();
        String thequery = (request.getQuery() + "%").toLowerCase();
        List<TouDayProfile> list = calendarService.getDayProfileSearchSuggestions(calendarId, thequery);
        Iterator<TouDayProfile> listIt = list.iterator();
        DayProfileSuggestion suggestion;
        TouDayProfile profile;
        while (listIt.hasNext()) {
            profile = listIt.next();
            suggestion = new DayProfileSuggestion(calendarId, profile.getCode()+"-"+profile.getName(), profile.getId(), profile.getName(), profile.getCode(), true);
            suggestion.setOriginalQuery(request.getQuery());
            suggestion.setSeasonId(seasonId);
            suggestions.add(suggestion);
        }
        response.setSuggestions(suggestions);
        return response;
    }

    @Override
    public ArrayList<TouCalendarSeason> getCalendarSeasons(Long calendarId) {
        return calendarService.getTouCalendarSeasons(calendarId);
    }

    @Override
    public boolean updateCalendarSeasons(ArrayList<TouCalendarSeason> calendarseasons) throws ValidationException, ServiceException, AccessControlException {
        try {
            calendarService.updateCalendarSeasons(calendarseasons);
            return true;
        } catch(ServiceException se) {
            return false;
        }
    }
    
    @Override
    public TouCalendarData updateCalendar(TouCalendarData calendar) throws ValidationException, ServiceException, AccessControlException {
        ServerValidatorUtil.getInstance().validateDataForValidationMessages(calendar);
        if (calendar.getId() == null) {
            return calendarService.insertCalendar(calendar);
        } else {
            return calendarService.updateCalendar(calendar);
        }
        
    }

    @Override
    public TouDayProfileData updateDayProfiles(TouDayProfileData dayProfile) throws ValidationException, ServiceException, AccessControlException {
        ServerValidatorUtil.getInstance().validateDataForValidationMessages(dayProfile);
        if (dayProfile.getId() == null) {
            return calendarService.insertDayProfile(dayProfile);
        } else {
            return calendarService.updateDayProfile(dayProfile);
        }
        
    }
    
    @Override
    public boolean deleteDayProfile(TouDayProfileData dayProfile) throws ValidationException, ServiceException, AccessControlException {
        return calendarService.deleteDayProfile(dayProfile);
    }


    @Override
    public ArrayList<TouSpecialDayData> getSpecialDays(Long calendarId) {
        return calendarService.getSpecialDays(calendarId);
    }

    @Override
    public ArrayList<TouSeasonDateData> getSeasonDates(Long calendarId) {
        return calendarService.getTouSeasonDates(calendarId);
    }

    @Override
    public TouSeasonDateData addSeasonDate(TouSeasonDateData seasonDateData) throws ValidationException, ServiceException {
        ServerValidatorUtil.getInstance().validateDataForValidationMessages(seasonDateData);
        return calendarService.insertSeasonDate(seasonDateData);
    }

    @Override
    public TouSeasonDateData updateSeasonDate(TouSeasonDateData seasonDate) throws ValidationException, ServiceException {
        ServerValidatorUtil.getInstance().validateDataForValidationMessages(seasonDate);
        return calendarService.updateSeasonDate(seasonDate);
    }

    @Override
    public boolean deleteSeasonDate(TouSeasonDateData seasondate, boolean  deleteCalendarSeason) throws ValidationException {
        return calendarService.deleteSeasonDate(seasondate, deleteCalendarSeason);
    }

    @Override
    public ArrayList<TouDayProfileTimeData> getTimesByTouDayProfile(Long dayProfileId) {
        return calendarService.getTimesByTouDayProfile(dayProfileId);
    }

    @Override
    public TouDayProfileTimeData updateDayProfileTimes(TouDayProfileTimeData dayProfileTime, Long calendarId) throws ValidationException,  ServiceException {
        ServerValidatorUtil.getInstance().validateDataForValidationMessages(dayProfileTime);
        if (dayProfileTime.getId()==null) {
            return calendarService.insertDayProfileTime(dayProfileTime, calendarId);
        }
        return calendarService.updateDayProfileTime(dayProfileTime, calendarId);
    }

    @Override
    public TouSpecialDayData updateSpecialDay(TouSpecialDayData specialDay) throws ValidationException, ServiceException {
        ServerValidatorUtil.getInstance().validateDataForValidationMessages(specialDay);
        if (specialDay.getId()==null) {
            return calendarService.insertSpecialDay(specialDay);
        }
        return calendarService.updateSpecialDay(specialDay);
    }
    
    @Override
    public Boolean deleteSpecialDay(Long specialDayPK, Long calendarId) throws ValidationException {
        return calendarService.deleteSpecialDay(specialDayPK, calendarId);
    }

    @Override
    public boolean deleteDayProfileTime(Long id, Long calendarId) throws ServiceException, ValidationException {
        return calendarService.deleteDayProfileTime(id, calendarId);
    }

    @Override
    public TouDayProfileTimeData getTimeInMilliseconds(TouDayProfileTimeData timeData) {
        Calendar cal = GregorianCalendar.getInstance();
        cal.set(2000, 1, 1);
        cal.set(Calendar.SECOND, 0);
        cal.set(Calendar.MILLISECOND, 0);
        cal.set(Calendar.HOUR_OF_DAY, timeData.getStartHour());
        cal.set(Calendar.MINUTE, timeData.getStartMinute());
        timeData.setStartMs(cal.getTimeInMillis());
        cal.set(Calendar.HOUR_OF_DAY, timeData.getEndHour());
        cal.set(Calendar.MINUTE, timeData.getEndMinute());
        timeData.setEndMs(cal.getTimeInMillis());
        return timeData;
    }
    
    @Override
    public TouCalendarSeasonsPeriodsSpecialDaysData getTouCalendarData(Long calendarId) throws ServiceException {        
        return calendarService.getCalendarData(calendarId);
    }

    @Override
    public ArrayList<TouCalendar> getActiveCalendars() throws ValidationException, ServiceException, AccessControlException {
        return calendarService.getActiveTouCalendars();
    }

    @Override
    public ArrayList<TouCalendar> getCalendars(Long theCalendarId) throws ValidationException, ServiceException, AccessControlException {
        ArrayList<TouCalendar> touCalendars = getActiveCalendars();
        if(theCalendarId != null) {
            TouCalendar tc = calendarService.getTouCalendarData(theCalendarId);
            if(!tc.getRecordStatus().equals(RecordStatus.ACT)) {
                touCalendars.add(tc);
            }
        }
        return touCalendars;
    }
}