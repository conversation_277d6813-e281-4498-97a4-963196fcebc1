package za.co.ipay.metermng.server.mybatis.service;

import org.apache.log4j.Logger;
import org.springframework.transaction.annotation.Transactional;
import za.co.ipay.gwt.common.shared.exception.ServiceException;
import za.co.ipay.metermng.datatypes.RecordStatus;
import za.co.ipay.metermng.mybatis.generated.mapper.ServiceResourceMapper;
import za.co.ipay.metermng.mybatis.generated.model.ServiceResource;
import za.co.ipay.metermng.mybatis.generated.model.ServiceResourceExample;
import za.co.ipay.metermng.server.mybatis.mapper.ISalesPerResourceMapper;
import za.co.ipay.metermng.shared.dto.dashboard.SalesPerResourceContainerDto;
import za.co.ipay.metermng.shared.dto.dashboard.SalesPerResourceDto;

import java.math.BigDecimal;
import java.sql.Date;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Collections;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map.Entry;

public class SalesPerResourceService {

    // How far back in months to fetch data
    private static final long GRAPH_HISTORY = 2L;

    private ISalesPerResourceMapper salesPerResourceMapper;
    private ServiceResourceMapper serviceResourceMapper;

    private static Logger logger = Logger.getLogger(SalesPerResourceService.class);

    public SalesPerResourceService() {}

    public void setiSalesPerResourceMapper(ISalesPerResourceMapper salesPerResourceMapper) {
        this.salesPerResourceMapper = salesPerResourceMapper;
    }    
    
    public void setServiceResourceMapper(ServiceResourceMapper serviceResourceMapper) {
        this.serviceResourceMapper = serviceResourceMapper;
    }

    @Transactional(readOnly = true)
    public ArrayList<SalesPerResourceContainerDto> getSalesPerResource(Long genGroupId) {

        ArrayList<SalesPerResourceContainerDto> salesPerResourceContainers = new ArrayList<>();

        List<ServiceResource> serviceResources = getServiceResources();

        for (ServiceResource serviceResource: serviceResources) {
            SalesPerResourceContainerDto salesPerResourceContainerDto = new SalesPerResourceContainerDto();
            salesPerResourceContainerDto.setResourceName(serviceResource.getName());
            salesPerResourceContainerDto.setDailySalesPerResourceDtos(getDailySalesPerResource(genGroupId, serviceResource.getId()));
            salesPerResourceContainerDto.setMonthlySalesPerResourceDtos(getMonthlyDataSet(salesPerResourceContainerDto.getDailySalesPerResourceDtos()));
            salesPerResourceContainers.add(salesPerResourceContainerDto);
        }
        return salesPerResourceContainers;
    }

    private List<ServiceResource> getServiceResources() throws ServiceException {
    	ServiceResourceExample example = new ServiceResourceExample();
        example.createCriteria().andRecordStatusEqualTo(RecordStatus.ACT);
        return serviceResourceMapper.selectByExample(example);
    }

    private ArrayList<SalesPerResourceDto> getDailySalesPerResource(
            Long genGroupId, Long id) {

        LocalDate endDate = LocalDate.now();
        LocalDate startDate = endDate.minusMonths(GRAPH_HISTORY).withDayOfMonth(1);

        // Use new method of getting dataset
        Date sqlStartDate = Date.valueOf(startDate);
        Date sqlEndDate = Date.valueOf(endDate);
        ArrayList<SalesPerResourceDto> tableData = salesPerResourceMapper.getDailySalesPerResource(genGroupId, id, sqlStartDate, sqlEndDate);

        return tableData;
    }

    private ArrayList<SalesPerResourceDto> getMonthlyDataSet(ArrayList<SalesPerResourceDto> dailyDataSet) {
        //Configure X axis range.
        ArrayList<String> xPoints = new ArrayList<>();

        Date today = Date.valueOf(LocalDate.now());
        SimpleDateFormat outputFormat = new SimpleDateFormat("yyyy-MM");

        String dateToday = outputFormat.format(today);
        String[] splitDate = dateToday.split("-");

        int year = Integer.parseInt(splitDate[0]);
        int month = Integer.parseInt(splitDate[1]);

        // Working backwards from today, add dates to X axis points rolling over year at month zero.
        for (long i = GRAPH_HISTORY; i >= 0; i--) {
            try {
                xPoints.add(outputFormat.format(outputFormat.parse(year + "-" + month)));
            } catch (ParseException e) {
                // TODO Auto-generated catch block
                e.printStackTrace();
            }
            month--;

            if (month == 0) {
                // roll back to previous year and set month to 12
                month = 12;
                year--;
            }
        }

        Collections.reverse(xPoints);

        //Create keys corresponding to months and set totals to zero.
        LinkedHashMap<String,BigDecimal> lMap = new LinkedHashMap<>();
        for (String dateString : xPoints) {
            lMap.put(dateString, new BigDecimal("0.00"));
        }

        // Create a mapping of totals for month to year-month key.
        for (SalesPerResourceDto salesPerResourceDto : dailyDataSet) {
                String[] splitString = salesPerResourceDto.getTsData().split("-");
                int yearDto = Integer.parseInt(splitString[0]);
                int monthDto = Integer.parseInt(splitString[1]);
                String dtoDate = String.format("%d-%02d", yearDto,monthDto);
                if (lMap.containsKey(dtoDate)){
                    BigDecimal newTotal = lMap.get(dtoDate).add(salesPerResourceDto.getSalesPerResourceTotal());
                    lMap.put(dtoDate, newTotal);
                }
        }

        // Add mapped points to SalesPerResourceDto's
        ArrayList<SalesPerResourceDto> monthlySalesPerResourceDtos = new ArrayList<>();

        for (Entry<String, BigDecimal> entry : lMap.entrySet()) {
            SalesPerResourceDto dtoToSet = new SalesPerResourceDto();
            dtoToSet.setTsData(entry.getKey());
            dtoToSet.setSalesPerResourceTotal(entry.getValue());
            monthlySalesPerResourceDtos.add(dtoToSet);
        }
        return monthlySalesPerResourceDtos;
    }
}