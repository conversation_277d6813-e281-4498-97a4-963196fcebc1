package za.co.ipay.metermng.integration.bulkfreeissue;

import com.fasterxml.jackson.databind.ObjectMapper;

import za.co.ipay.metermng.fileimport.ImportParamRecordConverter;
import za.co.ipay.metermng.shared.dto.BulkParamRecord;
import za.co.ipay.metermng.shared.integration.bulkfreeissue.BulkFreeIssueParamRecord;

/**
 * Converter for BulkFreeIssueParamRecord to/from JSON string format.
 */
public  class BulkFreeIssueParamRecordConverter implements ImportParamRecordConverter {

    private final ObjectMapper objectMapper = new ObjectMapper();

    @Override
    public BulkParamRecord convertToObject(String jsonData) {
        if (jsonData == null || jsonData.trim().isEmpty()) {
            return new BulkFreeIssueParamRecord();
        }
        
        try {
            return objectMapper.readValue(jsonData, BulkFreeIssueParamRecord.class);
        } catch (Exception e) {
            // If JSON parsing fails, return empty record
            return new BulkFreeIssueParamRecord();
        }
    }

    @Override
    public String convertToString(BulkParamRecord record) {
        if (!(record instanceof BulkFreeIssueParamRecord)) {
            return "{}";
        }
        
        try {
            return objectMapper.writeValueAsString(record);
        } catch (Exception e) {
            return "{}";
        }
    }
}
