package za.co.ipay.metermng.integration.bulkfreeissue;

import za.co.ipay.metermng.fileimport.ImportItemDataConverter;
import za.co.ipay.metermng.shared.integration.GenericImportRecord;
import za.co.ipay.metermng.shared.integration.bulkfreeissue.BulkFreeIssueImportRecord;

/**
 * Converter for BulkFreeIssueImportRecord to/from CSV string format.
 */
public class BulkFreeIssueItemDataConverter implements ImportItemDataConverter {

    @Override
    public GenericImportRecord convertToObject(String csvData) {
        if (csvData == null || csvData.trim().isEmpty()) {
            return new BulkFreeIssueImportRecord();
        }
        
        String[] parts = csvData.split(",", -1);
        BulkFreeIssueImportRecord record = new BulkFreeIssueImportRecord();
        
        if (parts.length > 0) {
            record.setMeterNum(parts[0].trim());
        }
        if (parts.length > 1 && !parts[1].trim().isEmpty()) {
            try {
                record.setUnits(Integer.parseInt(parts[1].trim()));
            } catch (NumberFormatException e) {
                // Invalid number format - leave as null
            }
        }
        if (parts.length > 2) {
            record.setDescription(parts[2].trim());
        }
        if (parts.length > 3) {
            record.setUserReference(parts[3].trim());
        }
        if (parts.length > 4) {
            record.setReason(parts[4].trim());
        }
        if (parts.length > 5) {
            record.setCustomReason(parts[5].trim());
        }
        
        return record;
    }

    @Override
    public String convertToString(GenericImportRecord record) {
        if (!(record instanceof BulkFreeIssueImportRecord)) {
            return "";
        }
        
        BulkFreeIssueImportRecord freeIssueRecord = (BulkFreeIssueImportRecord) record;
        StringBuilder sb = new StringBuilder();
        
        sb.append(freeIssueRecord.getMeterNum() != null ? freeIssueRecord.getMeterNum() : "").append(",");
        sb.append(freeIssueRecord.getUnits() != null ? freeIssueRecord.getUnits() : "").append(",");
        sb.append(freeIssueRecord.getDescription() != null ? freeIssueRecord.getDescription() : "").append(",");
        sb.append(freeIssueRecord.getUserReference() != null ? freeIssueRecord.getUserReference() : "").append(",");
        sb.append(freeIssueRecord.getReason() != null ? freeIssueRecord.getReason() : "").append(",");
        sb.append(freeIssueRecord.getCustomReason() != null ? freeIssueRecord.getCustomReason() : "");
        
        return sb.toString();
    }
}
