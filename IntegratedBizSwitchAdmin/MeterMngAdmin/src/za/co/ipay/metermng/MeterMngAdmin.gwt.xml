<?xml version="1.0" encoding="UTF-8"?>
<module rename-to='metermanagement'>

    <inherits name='com.google.gwt.user.User'/>
    <inherits name="com.google.gwt.activity.Activity"/>
    <inherits name="com.google.gwt.logging.Logging"/>
    <inherits name='com.google.gwt.maps.Maps'/>
    <inherits name='org.hibernate.validator.HibernateValidator'/>
    <inherits name='za.co.ipay.metermng.MeterMngCommon'/>
    <inherits name='za.co.ipay.accesscontrol.gwt.AccessControlService'/>
    <inherits name='za.co.ipay.accesscontrol.AccessControlCommon'/>
    <inherits name='za.co.ipay.gwt.common.GWTCommon'/>
    <inherits name="org.moxieapps.gwt.highcharts.Highcharts"/>
    <inherits name="com.google.gwt.i18n.I18N"/>
    <inherits name="com.google.gwt.user.Debug"/>
    <inherits name='za.co.ipay.IpayUtils'/>

    <set-property name="gwt.logging.consoleHandler" value="ENABLED"/>
    <set-property name="gwt.logging.enabled" value="TRUE"/>
    <set-property name="gwt.logging.logLevel" value="ALL"/>

    <!-- set-property name="gwt.logging.popupHandler" value="DISABLED" / -->
    <!-- set-property name="gwt.logging.firebugHandler" value="ENABLED" / -->

    <set-configuration-property name="CssResource.style" value="pretty"/>

    <entry-point class='za.co.ipay.metermng.client.MeterMngAdmin'/>

    <source path='client'/>
    <source path='shared'/>

    <!-- i18n UiMessages generator class -->
    <generate-with class="za.co.ipay.metermng.rebind.UiMessagesGenerator">
        <when-type-is class="za.co.ipay.metermng.client.i18n.UiMessages"/>
    </generate-with>

    <!-- Specify the ValidatorFactory for the Validation bootstrap to use -->
    <replace-with class="za.co.ipay.metermng.client.validation.ValidatorFactory">
        <when-type-is class="javax.validation.ValidatorFactory"/>
    </replace-with>
    <!--  Specify the ValidationMessageResolver to use for your custom validation messages -->
    <replace-with class="za.co.ipay.gwt.common.client.validation.MessagesResolver">
        <when-type-is class="com.google.gwt.validation.client.UserValidationMessagesResolver"/>
    </replace-with>

    <!-- allow Super Dev Mode commented out for GWT2.7 automatically added. breaks if inserted here!! -->
    <!-- add-linker name="xsiframe"/ -->

</module>
