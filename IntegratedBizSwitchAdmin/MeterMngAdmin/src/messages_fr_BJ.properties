### !!! CAUTION !!!
### Before adding keys, ensure you have no duplicates. Duplicates could lead to unexpected behaviour.

# zachv: 2025-05-16 | Planio #30009
calendar.specialday.field.date=Date
calendar.specialday.field.date.help=Enter a date for the special day
calendar.specialday.field.year=Year

# jacciedt: 2025-04-25 | Planio #33761
multiUsagePointAccountAdjustmentProcessor.notification.disconnect.email.subject=Account balance has run out for your account
multiUsagePointAccountAdjustmentProcessor.notification.disconnect.email.message=Dear Customer,\n\nYour meters will be disconnected.\n\nYour account status is: \n  Account balance: {7,number,currency}\n  Low balance notification threshold: {8,number,currency}\n\nRegards,\nSupport Team
multiUsagePointAccountAdjustmentProcessor.notification.disconnect.sms.message=Balance for your account has run out and will be disconnected. Balance is {7,number,currency} Regards, Support Team
multiUsagePointAccountAdjustmentProcessor.notification.emergencyCredit.email.subject=Emergency credit threshold for your account
multiUsagePointAccountAdjustmentProcessor.notification.emergencyCredit.email.message=Dear Customer,\n\nYour account status is: \n Account balance: {7,number,currency}\n  Low balance notification threshold: {8,number}\n  Emergency credit threshold: {9,number,currency}\n\nRegards,\nSupport Team
multiUsagePointAccountAdjustmentProcessor.notification.lowBalance.email.subject=Account balance low for your account
multiUsagePointAccountAdjustmentProcessor.notification.lowBalance.email.message=Dear Customer,\n\nYour account status is: \n  Account balance: {7,number,currency}\n  Low balance notification threshold: {8,number,currency}\n\nRegards,\nSupport Team
multiUsagePointAccountAdjustmentProcessor.notification.emergencyCredit.sms.message=Balance for your account is running low and below emergency credit threshold. Balance is {7,number,currency} Regards, Support Team
multiUsagePointAccountAdjustmentProcessor.notification.lowBalance.sms.message=Balance for your account is running low. Balance is {7,number,currency} Regards, Support Team


# michalv: 2025-05-09 | Planio #32735
export.error.too.many.records=Error: Too many records to export (over {0}). Please filter on a smaller date range.

# joelc: 2025-02-06 | Planio #28349
#power.limit.edit.label.prompt=Name
#power.limit.edit.label.help=Name of the power limit value. This is text display
power.limit.edit.value.prompt=Valeur en watts
power.limit.edit.value.help=Valeur de la limite de puissance. Cette valeur doit Ãªtre numÃ©rique
error.field.powerlimit.value.already=This power limit already exists.

# joelc: 2025-02-13 | Planio #31090
pricingstructure.suggestbox.placeholder = Start typing to filter list


#rodgersn: 2025-02-06 | Planio 29656
engineering.token.display.sgc=Supply Group Code:
engineering.token.display.krn=Key Revision Number:
engineering.token.display.ti=Tariff Index:

#renciac: 2024-08-31 | Planio 17062: MMA: get email from name & address from appsetting not from messages.properties
error.email.invalid=Invalid email address.

# christoe: 2024-07-29 | Planio #28161
import.edit.bulk.mdc.item.update.success=MDC message successfully updated.

# michalv: 2024-10-09 | Planio 25922
meter.models.mdc.required.for.thin.payment=Thin payment mode requires MDC

# christoe: 2024-10-21 | Planio #30157
online.bulk.panel.error.regex.validation1=Regex validation has been configured for UP Name, Agreement Reference or Account Name.
online.bulk.panel.error.regex.validation2=Auto-generation of these fields is disabled and requires manual entry on the Meter tab.

# Michalv: 2024-09-12 | Planio #25404
error.tab.duplicate.auxaccount=AuxAccount open in another tab. Tab: ({0})
error.tab.duplicate.custaccounttrans=Customer Transaction open in another tab. Tab: ({0})

#thomasn: 2023-12-06 | Planio 12164
access_group.lbl=Access Group
access_group.update.header=Update Access Group
access_group.update.group.error=New group has not been selected.
access_group.update.pricing.error=Customer UsagePoint(s) must have GLOBAL Pricing Structure.
access_group.update.future.pricing.clear=This will also clear any future pricing structures.
access_group.update.location.groups.clear=This will clear the usage point and customer location groups.
access_group.update.confirm.lbl=Proceed?
access_group.update.button=Update Access Group
insert.unique.error.meter=The meter details must be unique.
# Used by MMC MapperStaticProxy. e.g. CustomerMapperStaticProxy
insert.unique.error.customer=The customer details must be unique.
insert.unique.error.usagepoint=The usage point details must be unique.
insert.unique.error.devicestore=The device store details must be unique.
insert.unique.error.pricingstructure=The pricing structure details must be unique.
insert.unique.error.auxchargeschedule=The Aux charge schedule details must be unique.

#thomasn: 2023-10-11 | Planio 12164
session_auth.form.submit=Submit
session_auth.form.logout=Logout
session_auth.form.instructions=Choose session authorization details
session_auth.form.role=Role:
session_auth.form.group=Group:
session_auth.form.group_and_role=Group and Role:
session_auth.form.invalid=Login Error. Invalid Form Mode
access_group.error.group_already_cleared=The group has already been cleared on these entities.
access_group.success.updated_group=Successfully updated group
workspace.usagepoint.overview=Overview
workspace.usagepoint.actions=Actions
grouphierarchy.field.is_access_group=Access Group
grouphierarchy.field.is_access_group.help=Whether to connect this group to an access control organisation access group. This will show a list of access control groups on creating groups for this hierarchy level
grouphierarchy.field.is_access_group.error.already_assigned=An organisation access group can only be linked to one hierarchy level.
grouptype.field.location.group=Location Group
groupnode.field.access_group=Access Group
groupnode.field.access_group.help=Connects an access control organisation access group to this group which will restrict access to group users. A group user in a different group would not see this group in selections.
groupnode.field.access_group.error.no_groups=This hierarchy level has enabled organisation access groups but none have been defined.
changegroup.org_group.confirm=Changing your group and/or role selection will reload the application. Would you like to continue?
login.session.reload=You will be redirected to the login screen and the application will be reloaded.
mrid.ui=Unique ID
mrid.ui.help=Enter the unique ID
mrid.ui.external=External Unique ID?
mrid.ui.external.help=Whether unique id is from an external system

# christoe: 2024-06-21 | Planio #28160, #28158
mdc.txn.relay.title=Relay
mdc.txn.relay.help=Select which relay to connect or disconnect.
mdc.txn.relay.main=Main
mdc.txn.relay.aux.one=Auxiliary 1
mdc.txn.relay.aux.two=Auxiliary 2
mdc.txn.power.limit=Power_Limit

# michalv: 2024-06-15 | Planio #29332
error.field.email3=One or multiple email addresses are invalid

# michalv: 2024-07-18 | Planio 28459
online.bulk.panel.encryptionKey.help = Enter the encryption key for this meter. This field is required for activation. To edit an already existing meter, use the UsagePoint Page.

# christoe: 2024-02-06 | Planio #23257 [i-Switch] Send an SMS when an aux charge is loaded
notify.selection.inherit=Inherit ({0})
error.notify.selection.null=It is required to set both preferences.
group.notify.children.change.alert=Children with matching or undefined notification IDs will be updated with these changes. Continue?
group.error.notification.save=Unable to link the notification information to the group.
customer.adjust.aux.accounts=Auxiliary Account Adjustments
customer.new.aux.accounts=New Auxiliary Accounts
customer.notification.types=Notification Types
customer.manage.notification.types=Manage Notification Types

#renciac: 2024-05-20 | Planio 27858: TariffStartDate not on the first of a month at midnight
warning.tariff.start.date.not.on.month.boundary=WARNING: tariff start date is not on a month boundary (the 1st at zero hours).</br>\
Bizswitch must be configured properly for this, as mid-month tariff changes MAY cause billing problems if not handled correctly!</br>\
Please be aware that MONTHLY BILLING Cyclic charges may only start or end on a month boundary.</br>\
Continue?
error.billing.cyclic.change.midmonth.monthly=MONTHLY BILLING Cyclic charges may only be initiated / changed for tariff start ON a month boundary (the 1st of a month at zero hours).

# michalv: 2024-05-22 | Planio 28023
token.reversal.reprinted.error=You do not have permission to reverse a reprinted token.

# rodgersn: 2024-02-20 | Planio #26293
meter.uri.remove.question=Changing the meter model will clear the Meter URI Fields (if captured). Continue?
meter.models.field.uri.present=URI Present
meter.models.field.uri.present.help=This indicates if the meter has URI.
meter.uri.address=Address
meter.uri.address.help=Enter the URI address of the meter.
meter.uri.port=Port
meter.uri.port.help=Enter the URI port number of the meter.
meter.uri.protocol=Protocol
meter.uri.protocol.help=Enter the URI protocol used to communicate with the meter.
meter.uri.params=Parameters
meter.uri.params.help=Enter the URI parameters used when communicating with the meter.
meter.uri.fields=Meter URI Fields
meter.uri.fields.list=Meter URI Fields (if captured): Address, Port, Protocol, Parameters.
meter.uri.port.error=Port must be a number from 0 to 65535.
bulk.upload.meter.uri.address=Meter URI Address
bulk.upload.meter.uri.port=Meter URI Port
bulk.upload.meter.uri.protocol=Meter URI Protocol
bulk.upload.meter.uri.params=Meter URI Parameters
bulk.upload.meter.uri.not.present.address.error=The meter Model can not have a meter URI address.
bulk.upload.meter.uri.not.present.port.error=The meter Model can not have a meter URI port.
bulk.upload.meter.uri.not.present.protocol.error=The meter Model can not have a meter URI protocol.
bulk.upload.meter.uri.not.present.params.error=The meter Model can not have a meter URI parameters.
error.field.meteruriprotocol.max=Meter URI Protocol must not exceed 255 characters.
error.field.meteruriaddress.max=Meter URI Address must not exceed 100 characters.
error.field.meteruriparams.max=Meter URI Parameters value is too long.

# zachv: 2024-05-07 | Planio 28174
tariff.field.bsst.charge_name.title=Charge Name
tariff.field.bsst.charge_name.title.help=Name of the charge that will be displayed on the receipt. 

# joelc: 2024-04-26 | planio-27792
group.type.for.cape.verde.contract = -

# michalv: 2024-04-05 | planio-26839
meter.txn.tokencode1=Token Code/s

# christoe: 2023-12-28 | Planio #25194 Reason Entry for Writing Off Charges in MMA
usagepoint.charge.writeoff.enter.reason=Enter a reason for writing off charges
usagepoint.charge.writeoff.select.reason=Select a reason for writing off charges

# thomasn: 2024-02-21 | planio-25347 [Cape Verde] Analyze and create the Cape Verde tariffs
tariff.blocks.thresholdCharge.error.empty=Where Block Price is set all Threshold Charges must be set or all must be empty.
tariff.blocks.thresholdCharge.error.incomplete=Threshold Charges can be set only where block price exists.

# zachv: 2023-11-25 | Planio #25498
grouptree.show_more = Show more
grouptree.empty = Empty
grouptree.search.help = Type at least the first two letters of an item. The item could be at any level of the hierarchy of data.
suggestbox.placeholder = Type to search...

# renciac: 2023-11-20 | Planio 25211: [TANESCO UAT] Staff tariff as "FBE" with monthly cyclic charge
tariff.field.subsidised.units.title=Monthly Subsidised Units
tariff.field.subsidised.units.descrip=Description
tariff.field.subsidised.units=Units
tariff.field.subsidised.units.help=Units that are issued at a subsidised charge.
tariff.field.bsst.charge.title=Charge
tariff.field.bsst.charge.title.help=Enter the amount to charge for subsidised units 
tariff.error.bsst.charge.positive=Must be a positive value.

# renciac: 2023-11-15 | Planio 25151 [TANESCO] [MMA] Automatically loading debt for a newly installed meter
tariff.field.meter_debt.title=Meter Debt
tariff.field.meter_debt.singlephase.label=Single Phase
tariff.field.meter_debt.singlephase.label.help=Enter Amount of Debt to consumer for pre-loaded single phase units on a new meter.
tariff.field.meter_debt.threephase.label=Three Phase
tariff.field.meter_debt.threephase.label.help=Enter Amount of Debt to consumer for pre-loaded three phase units on a new meter.

# marcod: 2023-08-29| planio 13587
import.upload.file.already.uploaded.group=Duplicate Filename. This file was already uploaded by another group

# marcod: 2023-06-21 | Planio 12175 Bulk device store movements
bulk.device.store.movement.help=Select the end device store the meters should be transferred to
bulk.device.store.movement=Select TO Device Store
bulk.device.store.movement.header=Bulk Device Store Movement
bulk.device.store.movement.without.input.file.not.implemented.yet=Bulk Device Store Movement needs input file of meters. Database setting has_input_file only implemented as 'y' for this filetype yet.
bulk.device.store.movement.param=TO Device Store

# renciac: 2023-09-20 | Planio 21358 View & writeoff also billing cyclic charges
usagepoint.last.cyclic.date.info=Last Cyclic Charge Date
usagepoint.last.cyclic.dates.info=Last Cyclic Charge Dates
usagepoint.last.cyclic.vend.date=At Vend/Topup
usagepoint.last.cyclic.billing.date=At Billing
usagepoint.charge.view.writeoff.date.help=Last cyclic date is the date when periodic charges like daily and monthly charges were last paid by a consumer. Charges after this date have yet to be recovered and may be written off here by selecting a new 'End Date' which becomes the new 'Last Cyclic Date'. Depending on the tariff and payment mode, there are potentially two sets of cyclic charges, one at Vend/Topup time, one at billing time.
usagepoint.charge.view.dialog.invalid.date.both=Date selected cannot be before BOTH last cyclic dates displayed
usagepoint.charge.view.dialog.warning.last.vend.cyclic.date=Last Vend Cyclic Charge already calculated on {0}. Nothing to calculate for VEND cyclic charges. Continue?
usagepoint.charge.view.dialog.warning.last.vend.billing.date=Last Billing Cyclic Charge already calculated on {0}. Nothing to calculate for Billing cyclic charges. Continue?
usagepoint.charge.view.filter.date.help=Outstanding charges will be calculated from the above Last Cyclic dates to this new selected date. It must be AFTER the previous last Cyclic Date/s. 
usagepoint.charge.view.dialog.date=Select End Date for charges
usagepoint.charge.writeoff.vend.heading=VEND/TOPUP CYCLIC CHARGES OUTSTANDING
usagepoint.charge.writeoff.billing.heading=BILLING CYCLIC CHARGES OUTSTANDING
usagepoint.charge.writeoff.vend.total=The total vend charges amount including tax is
usagepoint.charge.writeoff.billing.total=The total billing charges amount including tax is
usagepoint.charge.writeoff.both.total=The total charges amount including tax is

# christoe: 2023-10-04 | planio 22545
special.action.reason.no.reason=No reason given.

# rodgersn: 2023-06-07 | Planio 21001
register.reading.txn.meter=Meter
meterreadings.table.date=Date Created
usage.point.register.reading.txn.description=Register Readings for all meters on this usage point for the time period selected

# christoe: 2023-06-27 | planio 22796
reprint.default.email.message=Dear Customer\n\nPlease find your receipt details below:\n{0}\nKind regards\n{1}

# rodgersn: 2023-02-28 | Planio 15160
meter.assign.from.units.warn=Changing the pricing structure from Thin Units will not migrate the Units balance. A manual adjustment will have to be done. Continue?
ps.paymentmode.change.warn=This change in pricing structure changes the payment mode. Please ensure all the charges and/or billings are up to date. Continue?

# joelc: 2022-12-09 | planio 7589
usagepoint.save.license.error=  Maximum usage points allowed has been reached. Please notify your System Administrator.
bulk.import.license.waring = Importing {0} active Usage Points will exceed the maximum active usage points allowed. <br/>Note that once the limit is reached, only usage points marked as Inactive will be imported.

# marcod: 2023-04-06 | Planio 21668 [ZESCO] Debt collection methods
auxchargeschedule.specific.list.item=ACCOUNT SPECIFIC
customer.auxaccount.principle.amt=Principle Amount

# renciac: 2023-04-01 | Planio 18524 New requirements for RegisterReadingThinTariff
billingdet.appliesto.group.label=Only for Billing dets that apply to others
billingdet.discount.label=Discount
billingdet.discount.help=Check to indicate that this billing determinant signifies a discount, eg. solar rebate.
billingdet.charge.type.help=This billing determinant is either a percentage charge of another billing determinant's charge or a flat rate.
billingdet.applies.to.label=Applies To
billingdet.applies.to.help=Select the master billing determinant to which the percentage value of THIS billing determinant is applied.
billingdet.lnk.error.save=Unable to save the Billing Determinant appliesTo link.
billingdet.lnk.error.both=AppliesTo and Charge_type must both be captured or neither.
billingdet.applies.to.already.in.use=Billing det/s to which this billing det applies are already in use on a tariff or template.
billingdet.applies.to.already.in.use.on.mdc=This billing Det is in use on an MDC, cannot be used as a sub billing det that applies to others. 
billingDet.in.use.cannot.change.settings=This billing det is already in use on a tariff or template, cannot change settings or record status.
billingDet.in.use.cannot.change.applies.to=This billing det or those it applies to is already in use on a tariff or template, cannot change billing dets it applies to.
billingDet.change.regread.panel.open=You currently have a RegisterReadingThinTariff under construction. Saving this billing_det change will cause that to clear. if you need to save changes there first, do not confirm this message, save that tariff first then return here to save this billing_det. Continue with this save now?
billingdet.taxable=Is AppliesTo Taxable? 
billingdet.taxable.help=Unselect if tax is not applicable to this Billing Det - ONLY for billingDets that apply to others
billingdet.lnk.error.taxable=Taxable can only be unselected when this is an appliesTo BillingDet
cyclic.charge.apply.at.lbl=Apply At:    
cyclic.charge.apply.at.vend.lbl=Vend
cyclic.charge.apply.at.billing.lbl=Billing
cyclic.charge.apply.at.error.required=You must select one.
tariff.blocks.unitcharge.negative=Billing Det Unit Charge percentage or discount must not be negative
tariff.save.failed.billingDets.changed.on.database=Save tariff Failed. Percentage / Discount BillingDets have changed on database. Incompatible with input. Refresh the page.
unitcharge.choose.charge.type=Choose type of Charge
unitcharge.type.none=None
unitcharge.type.percentage=Percentage
unitcharge.type.flatrate=Flat Rate
unitcharge.discount.type.charge.error=Discount must be either a percentage or a flat rate 

# thomasn: 2023-03-21 | Planio 17726
supply.group.in.use.error.service=Supply Group in use by one or more meters. Press cancel to load latest.
supply.group.in.use.error.lbl=Supply Group in use by one or more meters. Some fields are read-only while in this state.

# rodgersn: 2023-02-09 | Planio 20655
register.readings.total.consumption=Total consumption:

# thomasn: 2023-01-10 | Planio 18459
error.positive.value=Value must be positive.

# jacciedt: 2022-12-14 | Planio 19775
export.field.receiptnum=Receipt Number

# patrickm: 2022-11-08 | Planio #19650
reprint.total.tax=Total Tax
reprint.total.tax-inclusive=Total (Tax Incl.)

# thomasn: 2022-11-08 | Planio 17785
aux.account.mrid.external.unique.validation=The Unique ID of this aux account is already in use

# marcod: 2022-10-07 | Planio 19234
error.field.powerlimit.name.range=Name must be between 1 and 255 characters.
error.field.powerlimit.name.required=Name is required.
error.field.powerlimit.value.required=Value is required.
error.field.powerlimit.value.type=Value must be an integer and greater than zero.

# rodgersn: 2022-09-29 | Planio 16034
default.template.bulk.uploads=Download Template

# jacciedt: 2022-05-05 | Planio 15256
bulk.blocking.header=Bulk Blocking : Filename: {0}
bulk.blocking.without.input.file.not.implemented.yet=Bulk Blocking generation needs input file of meters. Database setting has_input_file only implemented as 'y' for this filetype yet.
import.upload.blocking.permission.needed=This user does not have permission to bulk upload Blocking instructions.
bulk.blocking.blocking.type=Blocking Type
bulk.blocking.blocking.type.required.field.error=Blocking Type is a required field
bulk.blocking.not.blocked=Not blocked

# thomasn: 2022-08-31 | Planio 17172
usagepoint.ps.unitstocurrency=Changing Pricing structure from thin-units will not migrate the units balance. A manual adjustment will have to be done. Continue?

# rodgersn: 2022-08-03 | Planio 17931
warning.pricingStructure.billingDets.notsame.asmetermodel.mdc=WARNING: The Billing Determinant/s covered by the pricing structure(s) current tariff(s) are a PARTIAL match to those used by the MDC Channels connected to the Meter Model. Continue?
warning.pricingStructure.billingDets.notsame.asmetermodel.mdc.save.UP=WARNING ON SAVE: The Billing Determinant/s covered by the pricing structure(s) current tariff(s) are a PARTIAL match to those used by the MDC Channels connected to the Meter Model. Continue?
warning.pricingStructure.billingDets.notsame.asmetermodel.mdc.activate.UP=WARNING ON ACTIVATE: The Billing Determinant/s covered by the pricing structure(s) current tariff(s) are a PARTIAL match to those used by the MDC Channels connected to the Meter Model. Continue?
error.pricingStructure.billingDets.notsame.asmetermodel.mdc=ERROR: The Billing Determinant/s covered by the pricing structure(s) current tariff(s) have NO MATCHES to those used by the MDC Channels connected to the Meter Model. If not a exact match, there should be at least one matching.
error.pricingStructure.billingDets.notsame.asmetermodel.mdc.save.UP=ERROR ON SAVE: The Billing Determinant/s covered by the pricing structure(s) current tariff(s) have NO MATCHES to those used by the MDC Channels connected to the Meter Model. If not a exact match, there should be at least one matching.
error.pricingStructure.billingDets.notsame.asmetermodel.mdc.activate.UP=ERROR ON ACTIVATE: The Billing Determinant/s covered by the pricing structure(s) current tariff(s) have NO MATCHES to those used by the MDC Channels connected to the Meter Model. If not a exact match, there should be at least one matching.

# rodgersn:2022-07-28 | Planio 18092 Missing message on some translation files
customer.txn.error.amount.incl.tax.is.zero=Amount incl tax cannot be zero

# thomasn: 2022-06-07 | Planio 16665
mdc.txn.pandisplay=Pan Display(Clear Balance)
remove.meter.pandisplay=*Sends MDC Message to clear meter balance.

# jacciedt: 2022-02-28 | Planio 12375
bulk.pricing.structure.change.header=Bulk Pricing Structure Change : Filename: {0}
bulk.pricing.structure.change.without.input.file.not.implemented.yet=Bulk Pricing Structure Change generation needs input file of meters. Database setting has_input_file only implemented as 'y' for this filetype yet.
import.upload.pricing.structure.change.permission.needed=This user does not have permission to bulk upload pricing structure change instructions.
bulk.pricing.structure.change.ps.start.date.error=Start Date must be in the future and greater than the start date of currently active pricing structure and first tariff start date.
bulk.pricing.structure.change.start.date=Start Date
bulk.pricing.structure.change.start.date.help=This is the date that the selected Pricing Structure will start on. Must be future dated based on when the import is done.
bulk.pricing.structure.change.pricing.structure=Pricing Structure
bulk.pricing.structure.change.pricing.structure.help=Pricing Structures that have active tariffs based on selected Start Date. Changing from thin-units will not migrate the units balance. A manual adjustment will have to be done.
import.items.abort=Import Failed:  

# renciac: 2022-05-05 | Planio 15937 curb validation between PS and billing dets esp for TOU
error.pricingStructure.future.ps.no.tariff.at.date=The future pricing structure has no running tariff at the start date selected.

# rodgersn: 2022-05-17 | Planio #16807
customer.txn.error.tax.less.than.amount=The Tax amount cannot be less than the Amount Including Tax for negative values
customer.txn.error.tax.and.amount.different.sign=If both Amount incl Tax and Tax amount are entered, they should be both positive or both negative

# patrickm: 2022-03-24 | Planio #15899
error.tab.duplicate.customer=Customer open in another tab. Tab: ({0})
error.tab.duplicate.meter=Meter open in another tab. Tab: ({0})
error.tab.duplicate.usagepoint=Usage Point open in another tab. Tab: ({0})

# jacciedt: 2022-02-10 | Planio 14296
confirm.bill.payment.reversal=Confirm Bill Payment reversal?
bill.payment.reversal.success=Successful Bill Payment Reversal. Original Ref = {0}, Reversal Ref = {1}
reverse.payment=Reverse Payment

# renciac: 2022-01-25 | Planio 12812 New pricing_structure history
usagepoint.hist.pricing.structure=Pricing Structure
usagepoint.hist.pricing.start=Start Date
usagepoint.hist.pricing.end=End Date
usagepoint.hist.ps.change.reason=Change Reason
up_pricing_structure.header=Pricing Structure History on the Usage Point
up_pricing_structure.sub.header=Previous Pricing Structure changes made to this Usage Point
usagepoint.current.pricing.change.enter.reason=Entrez une raison pour modifier la structure de prix actuelle
usagepoint.current.pricing.change.select.reason=SÃ©lectionnez une raison pour modifier la structure de prix actuelle
usagepoint.future.pricing.change.enter.reason=Entrez une raison pour modifier la future structure de prix
usagepoint.future.pricing.change.select.reason=SÃ©lectionnez une raison pour modifier la future structure de prix
usagepoint.field.pricingstructure=Structure de prix Actuelle
usagepoint.field.future.pricingstructure=Structure de prix future
usagepoint.ps.date.modified.hd=Last Date Modified
usagepoint.ps.user.modified.hd=User 
usagepoint.ps.change.reason.hd=Change Reason

# jacciedt: 2022-01-19 | Planio 14121
reprint.key.change.notice.line.1=Your resource token is below, but your meter requires a key change before you enter it.
reprint.key.change.notice.line.2=To change your meter's key, enter the tokens listed below:

# thomasn: 2022-01-31 | planio-15259 [BizSwitch & MeterMngCommon] Add support for codes in BlockTariff
tariff.blocks.unitcharge.error.empty=Where Block Price is set all Unit Charges must be set or all must be empty.
tariff.blocks.unitcharge.error.incomplete=Unit Charges can be set only where block price exists.

# marcod: 2021-11-18 | Planio 14768 Indra integration UI changes
bulk.upload.cust.ref=Cust Reference
bulk.upload.external.unique.id=Meter External UniqueId
bulk.upload.external.cust.unique.id=Cust External UniqueId
bulk.upload.external.up.unique.id=UP External UniqueId
mrid.component.error=Unique ID is a required field
group.edit=Edit
customer.ref.label=Customer Reference
customer.ref.help=Enter a unique reference number for the customer. This reference will refer to this particular customer with in the Meter Management System
error.field.customerreference.null=Customer Reference is a required field
error.field.customerreference.range=Customer Reference must be between {min} and {max} characters.
cust.mrid.external.unique.validation=The Unique ID of this customer is already in use
cust.ref.external.unique.validation=The customer reference of this customer is already in use
up.mrid.external.unique.validation=The Unique ID of this usage point is already in use
gen.group.mrid.external.unique.validation=The Unique ID of this group is already in use
meter.model.mrid.external.unique.validation=The Unique ID of this meter model is already in use
gen.group.mrid.external.length.validation=The Unique ID must be between 1 and 100 characters
aux.type.mrid.external.unique.validation=The Unique ID of this aux type is already in use
special.action.reason.mrid.external.unique.validation=The Unique ID of this special action reason is already in use
pricing.structure.mrid.external.unique.validation=The Unique ID of this pricing structure is already in use

# renciac: 2021-12-30 | Planio 15152 Cater for changing payment mode in Pricing Structure
usagepoint.error.new.installdate.before.last.sts.vend.date=Installation date cannot be BEFORE last Vend / Topup Date: {0}
usagepoint.error.new.installdate.before.current.ps.start.date=Installation date cannot be BEFORE the start date of the current Pricing Structure on this usage point: {0}.

# renciac: 2021-12-06 | Planio 14852 [EPC] Zero value in block tariff
tariff.blocks.zero.error=Only the first Block may have a unit price = 0

# renciac: 2021-10-18 | Planio 14521 Aux Account Specific charge schedule
auxspecchargeschedule.title=Aux Account Specific Charge Schedule
auxspecchargeschedule.title.add=Add Aux Specific Charge Schedule
auxspecchargeschedule.title.update=Update Aux Specific Charge Schedule
customer.debt.status.lbl=Debt Status
customer.debt.status.help=DEBT STATUS:<br/><b>Active:</b> balance is not zero and positive<br/><b>Settled:</b> balance is zero <br/><b>Overcollected:</b> balance is negative (i.e Refund)<br/><b>Suspended:</b> suspend-until date is in the future<br/><b>Written Off:</b> LAST transaction for Aux account is type WRITE_OFF and balance = 0
customer.chargeschedule.cycle=Charge Cycle
customer.chargeschedule.chamt=Montant du cycle de charge

# renciac: 2021-09-05 | Planio 11634, 11636, 13969 ViewOutstandingCharges bugs
usagepoint.charge.view.activation.in.future=La date d'activation du point d'utilisation est dans le futur, il n'y a pas encore de frais cycliques. 
usagepoint.charge.writeoff.dialog.heading=Liste des frais impayÃ©es au {0}

# patrickm: 2021-08-20 | Planio 9834
meter.model.in.use=Le modÃ¨le de compteur a un ou plusieurs compteurs attachÃ©s. Certains champs sont en lecture seule dans cet Ã©tat.
meter.models.paymentmodes.preselected=Les modes de paiement actifs ne peuvent pas Ãªtre supprimÃ©s tant que le modÃ¨le de compteur a des compteurs attachÃ©s.

# renciac: 2021-06-01 | Planio 11646 Bulk Keychange
supply.group.target.label=Groupe d'approvisionnement cible / RÃ©vision de la clÃ©
supply.group.target.label.help=Le prochain groupe d'approvisionnement / la prochaine rÃ©vision clÃ© vers laquelle ce groupe d'approvisionnement passera.  
supply.group.target.error.same=Le groupe d'approvisionnement cible ne peut pas Ãªtre le mÃªme que le groupe actuel
supply.group.target.validation.error=La date de base cible est infÃ©rieure au Groupe d'approvisionnement actuel.
supply.group.target.validation.error.expired=Date d'expiration Groupe d'approvisionnement cible ou date d'Ã©missionUntil est expirÃ©
supply.group.target.validation.nulls= REMARQUE : la validation entre la Groupe d'approvisionnement et la Groupe d'approvisionnement cible n'a pas Ã©tÃ© effectuÃ©e parce qu'une ou plusieurs des dates de base, d'expiration ou d'Ã©mission jusqu'Ã  la date sont encore nulles, en attente de mise Ã  jour par HSM. Une vÃ©rification manuelle est nÃ©cessaire.
supplygroup.field.issued.until.date.label=Emis jusqu'Ã 
supplygroup.field.expiry.date.label=Date d'expiration
supplygroup.field.target=Cible code groupe d'approvisionnement/NumÃ©ro de rÃ©vision clÃ© 
supplygroup.base.date.label=Date de base
supplygroup.base.date.label.help=Date de base utilisÃ©e pour la gÃ©nÃ©ration des jetons STS6
supplygroup.target.deactivate.question=Ce Code groupe d'approvisionnement/NumÃ©ro de rÃ©vision clÃ© est actuellement dÃ©sactivÃ©, mais il est toujours utilisÃ© comme Code groupe d'approvisionnement/NumÃ©ro de rÃ©vision clÃ© cible pour d'autres. Continuer ? 
supplygroup.base.dates.no.check=Une cible code groupe d'approvisionnement est en jeu, mais l'une ou l'autre ou les deux dates de base n'ont pas encore Ã©tÃ© mises Ã  jour par le HSM, et ne peuvent donc pas Ãªtre validÃ©es l'une par rapport Ã  l'autre. Continuer ?
supplygroup.target.base.date.smaller.question=Date de base code groupe d'approvisionnement > date de base code groupe d'approvisionnement cible. La cible est un code groupe d'approvisionnement plus ancien. Continuer? 
bulk.Keychange.extract.label.meterNum=NumÃ©ro du compteur 
bulk.Keychange.extract.label.userRef=RÃ©f. utilisateur 
bulk.Keychange.extract.label.token1=Jeton 1
bulk.Keychange.extract.label.token2=Jeton 2
bulk.Keychange.extract.label.token3=Jeton 3
bulk.Keychange.extract.label.token4=Jeton 4
bulk.Keychange.extract.label.fromSupGroup=Du Groupe d'approvisionnement 
bulk.Keychange.extract.label.fromKeyRev=De NumÃ©ro de RÃ©vision ClÃ©
bulk.Keychange.extract.label.fromTariffIdx=De l' Indice Tarifaire
bulk.Keychange.extract.label.fromBaseDate=De la date de base
bulk.Keychange.extract.label.toSupGroup=Au Groupe d'approvisionnement 
bulk.Keychange.extract.label.toKeyRev=Au NumÃ©ro de RÃ©vision ClÃ©
bulk.Keychange.extract.label.toTariffIdx=Vers l'index tarifaire 
bulk.Keychange.extract.label.toBaseDate=Ã la date de base
bulk.Keychange.extract.label.transDate=Date de la transaction  
bulk.Keychange.extract.label.userRecEntered=GÃ©nÃ©rÃ© par l'utilisateur
bulk.Keychange.extract.label.importFileName=importer le nom du fichier
bulk.Keychange.extract.label.bulkRef=RÃ©f. en vrac 
bulk.Keychange.extract.none=Aucun changement de clÃ© trouvÃ© pour ce fichier d'importation
button.submit=Soumettre
action.params.header.label=ParamÃ¨tres
import.file.explanation=TÃ©lÃ©charger un fichier contenant des donnÃ©es et / ou des paramÃ¨tres pour l'action
bulk.keychange.header=Changement de clÃ© en bloc  : Nom de fichier : {0}
bulk.keychange.to.header=Changement de clÃ© vers :
bulk.keychange.use.target=Utiliser le code groupe d'approvisionnement/NumÃ©ro de rÃ©vision clÃ© cible prÃ©-capturÃ© 
bulk.keychange.use.targetHelp=Les code groupe d'approvisionnement/NumÃ©ro de rÃ©vision clÃ© cibles sont saisis sur la page Groupe d'approvisionnement du menu Compteurs. S'il est sÃ©lectionnÃ©, le code groupe d'approvisionnement/NumÃ©ro de rÃ©vision clÃ© de chaque compteur sera mis en correspondance avec sa cible. S'il n'y a pas de cible pour un code groupe d'approvisionnement/NumÃ©ro de rÃ©vision clÃ© particulier, une erreur apparaÃ®tra, Ã  moins que vous n'entriez Ã©galement un code groupe d'approvisionnement/NumÃ©ro de rÃ©vision clÃ© spÃ©cifique dans l'OT, qui sera alors utilisÃ© en l'absence de cible. 
bulk.keychange.use.target.selected.message=On peut sÃ©lectionner "Utiliser la cible" ainsi que les valeurs du groupe d'approvisionnement. S'il n'y a pas de valeurs et qu'un code groupe d'approvisionnement/NumÃ©ro de rÃ©vision clÃ© n'a pas de cible, il y aura une erreur. Si vous saisissez Ã©galement des valeurs, celles-ci seront utilisÃ©es pour ceux qui n'ont pas de cible. 
bulk.keychange.supplygroupcode=Nouveau code groupe d'approvisionnement
bulk.keychange.supplygroupcode.help=SÃ©lectionnez le nouveau code de groupe de fournitures. 
bulk.keychange.supplygroupcode.error.required.no.target=Au moins, le code du groupe d'approvisionnement doit Ãªtre sÃ©lectionnÃ©.
bulk.keychange.tariffindex.required=L'indice tarifaire est requis
bulk.keychange.tariffindex=Nouvel indice tarifaire
bulk.keychange.tariffindex.help=Saisissez l'indice tarifaire Ã  modifier. Obligatoire. Si un compteur a actuellement un indice tarifaire diffÃ©rent, un changement de clÃ© sera gÃ©nÃ©rÃ©.
bulk.keychange.instruction.label=Instructions pour la gÃ©nÃ©ration de jetons de changement de clÃ© 
bulk.keychange.instruction.help=Donner des instructions sur le moment oÃ¹ les jetons de changement de clÃ© doivent Ãªtre gÃ©nÃ©rÃ©s.
bulk.keychange.instruction.generate.keychanges.now=GÃ©nÃ©rer et mettre Ã  jour les jetons de clÃ© maintenant
bulk.keychange.instruction.generate.keychanges.next.vend=DÃ©fini pour gÃ©nÃ©rer des jetons de clÃ© avec la prochaine vente
bulk.keychange.instruction.generate.keychanges.next.vend.after.date=DÃ©fini pour gÃ©nÃ©rer des jetons de clÃ© avec la prochaine vente APRÃS une date....
bulk.keychange.generate.keychanges.next.vend.after.date=Changement de clÃ© avec vente aprÃ¨s la date
bulk.keychange.generate.keychanges.next.vend.after.date.help=Les changements de clÃ©s seront gÃ©nÃ©rÃ©s au moment de la vente, mais seulement APRÃS cette date.
bulk.keychange.after.date.error=La date aprÃ¨s ne peut pas Ãªtre dans le passÃ©. Code vÃ©rifiÃ© pour que now() soit {0}
bulk.keychange.after.date.required=La date aprÃ¨s est requise pour l'instruction en vrac sÃ©lectionnÃ©e
bulk.keychange.overwrite.existing=Ãcraser les nouveaux dÃ©tails code groupe d'approvisionnement/NumÃ©ro de rÃ©vision clÃ© existants ?
bulk.keychange.overwrite.existing.help=Doit sÃ©lectionner le bouton radio pour gÃ©rer le cas oÃ¹ les nouveaux dÃ©tails du groupe d'approvisionnement existent dÃ©jÃ  sur un compteur : Ecraser et continuer, ou laisser les dÃ©tails existants et interrompre l'instruction de changement de clÃ© pour ces compteurs.
bulk.keychange.overwrite.existing.error=Il faut en choisir un, Ã©craser ou non.
import.upload.keychange.permission.needed=Cet utilisateur n'a pas le droit de tÃ©lÃ©charger en masse des instructions de changement de clÃ©. 
import.upload.cannot.change.action.params.now=Une importation a Ã©tÃ© enregistrÃ©e, les paramÃ¨tres d'action ne peuvent plus Ãªtre Ã©ditÃ©s, seulement visualisÃ©s. Pour refaire des Ã©lÃ©ments avec des paramÃ¨tres d'action diffÃ©rents, il faudra rÃ©importer dans un nouveau fichier.
import.upload.view.params.label=ParamÃ¨tres
button.process.selected=Processus sÃ©lectionnÃ©
button.process.all=Tout traiter 
button.import.extract.keychanges=Extraire les changements de clÃ©s gÃ©nÃ©rÃ©s
bulk.keychange.without.input.file.not.implemented.yet=La gÃ©nÃ©ration des changements de clÃ©s en masse nÃ©cessite un fichier d'entrÃ©e des compteurs. Le paramÃ¨tre de base de donnÃ©es has_input_file n'est encore implÃ©mentÃ© que comme 'y' pour ce type de fichier.
import.items.selected.success=Les Ã©lÃ©ments sÃ©lectionnÃ©s ont Ã©tÃ© traitÃ©s avec succÃ¨s.
import.items.errors= Tous les articles n'ont pas Ã©tÃ© traitÃ©s avec succÃ¨s.
import.items.all.success=Tous les Ã©lÃ©ments du fichier d'importation ont Ã©tÃ© traitÃ©s avec succÃ¨s.
import.extract.items.non=Aucun Ã©lÃ©ment Ã  extraire
import.extract.items.exception=L'extraction a Ã©chouÃ©. Veuillez contacter le support.
import.upload.twirly.waiting.text.keychange=Les demandes de changement de clÃ© peuvent prendre beaucoup de temps
import.upload.keychange.bulkref.label=Changement de clÃ© RÃ©f. en vrac
meter.txn.bulk.import.file.name=Nom du fichier importÃ©

# renciac: 2021-06-01 | Planio 12963 Extension to Generic upload framework for action params
import.file.parameters.needed=Veuillez confirmer et soumettre les paramÃ¨tres pour l'importation de ces donnÃ©es. 
import.file.parameters.needed.no.data=Veuillez confirmer et soumettre les paramÃ¨tres pour les changements en masse.\nUn nom de fichier factice sera gÃ©nÃ©rÃ©.\nlfSi les donnÃ©es sont nÃ©cessaires ainsi que les paramÃ¨tres, l'importation sera rejetÃ©e.
import.file.param.no.data.dummy.filename= Le nom de fichier gÃ©nÃ©rÃ© est {0}
import.file.no.parameters=Ce type de fichier {0} n'a pas d'interface utilisateur dÃ©finie pour les paramÃ¨tres d'action. Contactez le support. Fichier factice crÃ©Ã©.
import.file.no.parameters.or.setting=Ce type de fichier {0} n'a pas d'interface utilisateur dÃ©finie pour les paramÃ¨tres d'action ou le paramÃ¨tre de base de donnÃ©es pour has_input_file devrait Ãªtre {1}. Contactez le support technique.
import.file.params.save.error=Impossible d'enregistrer le fichier d'importation avec les paramÃ¨tres d'action.
import.file.no.params.converter.error=Il n'y a pas de convertisseur de paramÃ¨tres JSON pour les paramÃ¨tres d'action de ce fichier d'importation.
import.file.get.params.fail=La configuration du panneau de paramÃ¨tres a Ã©chouÃ©. VÃ©rifiez votre nom de fichier et le type de fichier.
import.file.parameters.updated=Les paramÃ¨tres ont Ã©tÃ© mis Ã  jour pour le nom de fichier : {0}
import.upload.file.settings.conflict=Aucun fichier n'a Ã©tÃ© sÃ©lectionnÃ© pour Ãªtre tÃ©lÃ©chargÃ© et il y a un conflit dans les paramÃ¨tres du type de fichier en vrac : les donnÃ©es d'entrÃ©e sont n ou b, mais les paramÃ¨tres d'action sont faux.
import.upload.file.no.input.data=Le paramÃ¨tre de type de fichier en vrac has_input_file = 'n', aucun nom de fichier n'est nÃ©cessaire.
import.upload.file.needs.action.params=Ce type de fichier nÃ©cessite des paramÃ¨tres d'action. Veuillez les saisir sur la page de tÃ©lÃ©chargement.

# thomasn: 2021-08-16 | planio-10426
usagepoint.ps.start.date.lbl=Date de commencement
usagepoint.ps.name.lbl=Structure de prix
usagepoint.ps.start.date.help=La date Ã  laquelle cette structure de prix sera activÃ©e. Cette date de dÃ©but est unique.
usagepoint.ps.start.date.error=La date de dÃ©but doit Ãªtre dans le futur et supÃ©rieure Ã  la date de dÃ©but de la structure de prix actuellement active et Ã  la date de dÃ©but du premier tarif.
usagepoint.ps.start.date.error.unique=La date de dÃ©but doit Ãªtre unique. Une structure de prix existe dÃ©jÃ  avec cette date de dÃ©but.
usagepoint.ps.view.all.btn=Afficher tout
usagepoint.ps.delete.btn=Effacer 
usagepoint.ps.delete.btn.confirm=Ãtes-vous sÃ»r de vouloir supprimer la future structure de prix ?
usagepoint.ps.save.error=La sauvegarde de la structure de prix des points d'utilisation a Ã©chouÃ©.
usagepoint.ps.delete.error=La suppression de la structure de prix des points d'utilisation a Ã©chouÃ©.
usagepoint.ps.delete.success=Suppression rÃ©ussie de la structure de tarification par points d'utilisation.
usagepoint.ps.required=Structure de prix requise.
usagepoint.ps.future.required=La future structure de prix ne devrait pas Ãªtre la mÃªme que l'actuelle ci-dessus.
usagepoint.ps.future.list.help=SÃ©lectionnez la structure de prix puis fixez une date de dÃ©but pour celle-ci.
usagepoint.ps.future.date.help=Date de dÃ©but de la structure de prix future sÃ©lectionnÃ©e.
usagepoint.ps.future.lbl=Future structure de prix
usagepoint.ps.future.start.date.lbl=Date de dÃ©but de la future structure de prix
usagepoint.ps.historic.error=Le point d'utilisation a une structure de prix historique qui n'est pas compatible avec le modÃ¨le de compteur. Utilisez un nouveau point d'utilisation.
meter.new.current.pricingstructure.required=Le compteur ({0}) ne supporte pas la structure de prix actuelle - {1}.
meter.new.current.pricingstructure.select=Structure de prix actuelle\n(si elle est modifiÃ©e, toutes les structures futures sont supprimÃ©es).
tariff.error.save.up.ps.start.date.conflict=Impossible de mettre Ã  jour la date de dÃ©but du tarif, la structure de prix est dÃ©jÃ  ajoutÃ©e au point d'utilisation avec la date de dÃ©but {0}.
usagepointworkspace.error.meter.unsupported.model.current=Le modÃ¨le de compteur {0} ne supporte pas la structure de prix actuelle du point d'utilisation.
question.confirm.installation.date.3=Le changement de date d'activation entre en conflit avec les structures de tarification. Les PS actuels seront supprimÃ©s et les PS futurs seront actualisÃ©s.

# marcod: 2021-07-27 | Planio 12179
supplygroup.field.kmc.expirydate=KMC Expiry Date
supplygroup.panel.kmc.expirydate.help=The system will automatically send an email warning every day for vending keys that are about to expire.

# renciac: 2021-07-21 | Planio 13124 Debt Instalments
aux.charge.sched.cycle.label=Cycle de frais
aux.charge.sched.cycle.label.help=Pour Ad-hoc, les frais sont facturÃ©s Ã  chaque vente.  Pour Quotidien / Mensuel, dÃ©termine le cycle de prÃ©lÃ¨vement des frais.
aux.charge.sched.cycle.amount.label=Montant du cycle
aux.charge.sched.cycle.amount.label.help=Un montant ad hoc est facturÃ© sur chaque vente. Les montants des cycles quotidiens ou mensuels seront facturÃ©s une fois par jour ou une fois par mois, selon le cycle, jusqu'Ã  ce que la dette soit remboursÃ©e.
aux.charge.sched.cycle.select.error=La sÃ©lection du cycle de facturation doit Ãªtre accompagnÃ©e de la saisie du montant du cycle de facturation. 
aux.charge.sched.cycle.instalment.label=Les versements pÃ©riodiques quotidiens ou mensuels sont des entrÃ©es de montants autonomes.
customer.auxaccount.start.date.lbl=Date de dÃ©part
customer.auxaccount.start.date.help=SÃ©lectionnez la date de dÃ©but du compte auxiliaire
customer.auxaccount.suspend.until.help=Suspendre temporairement un compte auxiliaire jusqu'Ã  la date dÃ©finie dans ce champ. Pour les calendriers de paiement par acomptes, tenez compte du paramÃ¨tre de l'application "Accumuler les acomptes auxiliaires pendant la suspension".
customer.auxaccount.last.charge.date.lbl=Date de la derniÃ¨re facturation
customer.auxaccount.last.charge.date.help=DerniÃ¨re date Ã  laquelle une transaction de vente a Ã©tÃ© effectuÃ©e  et payÃ©e pour ce compte. Ne reflÃ¨te pas les paiements manuels ou les ajustements de compte, mais seulement les paiements rÃ©els via les ventes/recharges. Notez que pour les programmes de facturation qui ne sont PAS des versements, il peut s'agir d'un paiement partiel et non d'un paiement complet.
error.field.value.monthly.charge=La valeur doit Ãªtre l'une des suivantes : PRO_RATA, LIBRE, COMPLÃTE. 
customer.auxaccount.suspend.until.smlr.start=La date de suspension ne doit pas Ãªtre antÃ©rieure Ã  la date de dÃ©but. 
customer.auxaccount.install.suspend.info=Le calendrier de frais Ã  l'aide des versements suit un comportement spÃ©cifique aprÃ¨s la suspension. \r\nVoir appSetting 'accumulate_aux_during_suspension'
vend.older.trans.info=Notez que lors de l'annulation d'une transaction qui n'est PAS la derniÃ¨re transaction pour l'accord du client, les dÃ©tails du dernier achat ne sont pas rÃ©initialisÃ©s et une intervention manuelle est nÃ©cessaire pour les futures transactions du mÃªme mois. La date du dernier paiement sur les comptes auxiliaires n'est pas non plus rÃ©initialisÃ©e.
vend.reversal.last.with.older=Notez qu'il y a eu plusieurs annulations ce mois-ci, les dÃ©tails du dernier achat ne sont pas rÃ©initialisÃ©s et une intervention manuelle est nÃ©cessaire pour les futures transactions du mÃªme mois. La date du dernier paiement sur les comptes auxiliaires n'est pas non plus rÃ©initialisÃ©e.
auxaccount.upload.startDate=Date de commencement
auxaccount.upload.startDate.greater=La date de dÃ©but ne peut pas Ãªtre supÃ©rieure Ã  la date de suspension jusqu'Ã ...
auxaccount.upload.startDate.format=La date de dÃ©but doit Ãªtre soit vide, soit correctement formatÃ©e.
auxaccount.upload.startDate.invalid.date=La date de commencement est une date non valide
auxaccount.upload.suspendUntil.invalid.date=Suspendre jusqu'Ã  est une date non valide
trans.bulk.upload.format.error.trans.date=La date de la transaction doit Ãªtre soit vide (par dÃ©faut, la date du traitement), soit correctement formatÃ©e (aaaa-MM-jj HH:mm:ss).
trans.bulk.upload.invalid.trans.date=La date de la transaction n'est pas valide

# marcod: 2021-07-08 | Planio 12735
error.usagepoint.outdated=ERREUR : Les donnÃ©es de cet onglet sont pÃ©rimÃ©es en raison d'une autre mise Ã  jour. Veuillez cliquer sur recharger pour rafraÃ®chir les donnÃ©es.
button.reload=Rechargez

# jacciedt: 2021-04-15 | Planio 12792
sts.unit.generation.limit.error=La quantitÃ© d'unitÃ©s Ã  Ã©mettre ne peut pas Ãªtre supÃ©rieure Ã  la quantitÃ© configurÃ©e de {0} unitÃ©s.

# thomasn: 2021-07-22 | Planio 5812
usagepoint.meter.inspection.request.btn=Envoyer une demande d'inspection de compteur
usagepoint.meter.inspection.request.setup.error=Le contrat n'est pas complet, confirmez que les donnÃ©es de l'adresse physique sont prÃ©sentes.
usagepoint.meter.inspection.request.processing.error=Une erreur s'est produite lors du traitement de la demande.
usagepoint.meter.inspection.request.meterMng000=La demande d'inspection du compteur a Ã©tÃ© traitÃ©e correctement. RÃ©fÃ©rence={0}
usagepoint.meter.inspection.request.meterMng001=Demande d'inspection du compteur - erreur gÃ©nÃ©rale. RÃ©fÃ©rence={0}
usagepoint.meter.inspection.request.meterMng011=Erreur de demande d'inspection de compteur - donnÃ©es du client incomplÃ¨tes ou invalides. RÃ©fÃ©rence={0}
usagepoint.meter.inspection.request.txt.comment=Entrez un commentaire
usagepoint.meter.inspection.request.txt.comment.help=Une description de la raison.

# jacciedt: 2021-04-15 | Planio 9695
no.aux.charge.schedule.defined=Aucun barÃ¨me de redevances auxiliaires dÃ©fini

# jacciedt: 2021-03-05 | Planio 12340
customer.txn.error.tax.more.than.amount=Le montant de la taxe ne peut pas Ãªtre supÃ©rieur au montant TTC.
customer.auxaccount.error.refund=Le nouveau solde aprÃ¨s l'ajustement sera de {0}. Vous n'Ãªtes pas autorisÃ© Ã  transformer ce compte en remboursement.
customer.auxaccount.error.debt=Le nouveau solde aprÃ¨s l'ajustement sera {0}. Vous n'Ãªtes pas autorisÃ© Ã  transformer ce compte en une dette.

# marcod: 2021-07-15 | Planio 13708
reprint.customer=Client

# thomasn: 2021-08-18 | Planio 13381
meter.txn.engineeringtokens.column=A les jetons d'ingÃ©nierie

# patrickm: 2021-07-02 | Planio 13126
supplygroup.field.code.default=Par dÃ©faut

# jacciedt: 2021-06-30 | Planio 12839
up_meter_install.remove.date=Date de suppression
up_meter_install.install.ref=Date dâinstallation
up_meter_install.remove.ref=RÃ©fÃ©rence de la suppression
up_meter_install.header=Installation des points dâutilisation
up_meter_install.sub.header=Installations de compteurs au point d'utilisation prÃ©cÃ©dentes effectuÃ©es Ã  ce point d'utilisation

# renciac: 2021-06-28 | Planio 13515 Writeoff charges duplicates
usagepoint.charge.button.close.writeoff.and.unassign.customer=Fermer et dÃ©sassigner le client

# marcod: 2021-05-25 | Planio 12620
search.meter.sgc.label1=Groupe d'approvisionnement / RÃ©vision clÃ©
search.meter.sgc.label2=(Actuel ou Nouveau)
search.meter.sgc.help=La recherche trouvera les compteurs dont le code groupe d'approvisionnement actuel ou nouveau est Ã©gal au Code du groupe d'approvisionnement/RÃ©vision de la clÃ© sÃ©lectionnÃ©. Un nouveau Code du groupe d'approvisionnement/RÃ©vision de la clÃ© sur un compteur STS n'est rempli que lorsqu'un changement de clÃ© est effectuÃ©. Ã partir de la prochaine vente, il devient alors le code actuel

# jacciedt: 2021-02-16 | Planio 11622
defaultAccountAdjustmentProcessor.notification.lowBalanceNoUsagePoint.email.subject=Solde du compte bas pour le compte {12} sur la convention {5}
defaultAccountAdjustmentProcessor.notification.lowBalanceNoUsagePoint.email.message=Cher client,\n\nVotre compte a le statut suivant : \n Solde du compte : {7,number, currency}\nLe seuil de notification de solde bas : {8,number, currency}\n\nCordialement,\nL'Ã©quipe du support technique
defaultAccountAdjustmentProcessor.notification.lowBalanceNoUsagePoint.sms.message=Le solde du compte {12} de la convention {5} est faible. Le solde est de {7,number,currency} Cordialement, Ã©quipe de support
bill.payments=Paiement des factures
bill.payments.provider=Fournisseur
bill.payments.reversal.request.received=Demande d'annulation reÃ§ue
bill.payments.pay.type=Type de paie
bill.payments.pay.type.details=DÃ©tails du type de paie
bill.payments.vote.name=Nom du vote 
bill.payments.description=Une liste des paiements de factures effectuÃ©s par ce client
bill.payments.transaction.type=Type de transaction de paiement de factures

# renciac: 2021-05-25 | Planio 13153 PS date validation vs installDate
usagepoint.installation.date.before.tariff.start1=La date d'installation est antÃ©rieure Ã  la date de dÃ©but du tarif {0}. <br/>Il en rÃ©sulte que les frais potentiels du Tarif qui sont dus avant la date de dÃ©but du tarif ne seront pas calculÃ©s.
usagepoint.installation.date.before.tariff.start2=<br/>La derniÃ¨re date Ã  laquelle des frais cycliques ont Ã©tÃ© calculÃ©es Ã©tait {0}.
usagepoint.installation.date.before.tariff.start3=<br/>Non DerniÃ¨re date de calcul de des frais cyclique dans le dossier.
usagepoint.installation.date.before.tariff.start4=<br/>Continuer ?

# renciac: 2021-04-20 | Planio 7918
error.field.customerdescription.max=La description du client doit comporter moins de {max} caractÃ¨res.
button.export.ps.title=Exporter TOUTES les structure de prix des tarifs actuels 
export.ps.failed.exception=Ãchec exportation. Contacter lâÃ©quipe Support.
export.ps.failed.non=Aucune structure de prix avec tarifs actuels Ã  exporterÂ ???
import.edit.item.update.bulk.tariff.success=Les donnÃ©es du tarif {0} ont Ã©tÃ© mises Ã  jour.
import.ps.name.label=Structure de prix
import.tariff.name.label=Tarif
import.tariff.edit.resave.error=ErreurÂ : ContenucalcÂ : {0}
import.upload.file.already.uploaded=ErreurÂ : Le fichier a dÃ©jÃ  Ã©tÃ© tÃ©lÃ©chargÃ©. VÃ©rifier le tableau.
import.upload.tariff.permission.needed=Cet utilisateur nâa pas la permission dâimporter les tarifs

# patrickm: 2021-03-18 | Planio 11152
defaultAccountAdjustmentProcessor.notification.emergencyCredit.email.subject=Seuil de crÃ©dit d'urgence pour {6}
defaultAccountAdjustmentProcessor.notification.emergencyCredit.email.message=Cher client,\n\nLe statut de votre compte estÂ : \n Solde du compteÂ : {7,nombre,devise}\n Seuil de notification de solde faibleÂ : {8,number}\n Seuil de crÃ©dit d'urgenceÂ : {9,Nombre,devise}\n\nSincÃ¨res salutations,\nÃquipe Support
defaultAccountAdjustmentProcessor.notification.emergencyCredit.sms.message=Le solde de {6} est bas et se situe en dessous du seuil de crÃ©dit d'urgence. Le solde est de {7,number,currency} SincÃ¨res salutations, Ãquipe Support

# jacciedt: 2021-03-12 | Planio 12494
error.field.validity.message.content=Le contenu du message ne peut Ãªtre vide.

# jacciedt: 2021-03-15 | Planio 11902
customer.account.does.not.exist=Le compte client nâexiste pas encore

# renciac: 2021-03-04 | Planio 12582
bulk.ignore.dup.meters=Ignorer doublons de compteur
bulk.ignore.dup.meters.help=Si le numÃ©ro du compteur existe dÃ©jÃ  dans la base de donnÃ©es, l'ignorer dans le fichier de tÃ©lÃ©chargement. En cas de doublons dans le fichier de tÃ©lÃ©chargement, utiliser le premier.
bulk.upload.ignore.meter.dups.changed=Le rÃ©glage Ignorer doublons de compteur a Ã©tÃ© modifiÃ© entre les Ã©tapesÂ ! Ãtait {0}Â ; a Ã©tÃ© remplacÃ© par {1}
bulk.upload.successful.meter.upload=Total de {0} tÃ©lÃ©chargement du compteur traitÃ© avec succÃ¨s, {1} doublons ignorÃ©s.

# marcod: 2021-02-01 | Planio 12168
email.password.reset.message=Nous avons reÃ§u une demande de rÃ©initialisation du mot de passe pour {0}.<br> Pour rÃ©initialiser votre mot de passe, cliquez sur le lien ci-dessous.<br> {1}.
password.link.expired=Le lien de rÃ©initialisation du mot de passe a expirÃ©. Vous pouvez en demander un autre.
password.link.used=Le lien de rÃ©initialisation du mot de passe a Ã©tÃ© dÃ©sactivÃ©.
password.change.now= Changer le mot de passe maintenant
password.reset.success=Votre mot de passe a Ã©tÃ© modifiÃ©.

# jacciedt: 2021-02-12 | Planio 12340
unitsacc.balance.with.symbol=Solde d'unitÃ©s ({0})

# jacciedt: 2021-02-04 | Planio 12330
bulk.upload.heading.metercustup=GÃ©nÃ©rer modÃ¨le pour tÃ©lÃ©chargement de masse CompteurClientUp
bulk.upload.metercustup.notice=Pour les tÃ©lÃ©chargements de masse Compteur/Client/PointUtilisation, utiliser l'option de menu -> Configuration -> TÃ©lÃ©charger et importer les fichiers

# jacciedt: 2021-02-05 | Planio 11950
demo.addmeterreadings.tariffCalc.failed={0} : {1} a Ã©tÃ© ajoutÃ© mais le calcul de tarif a Ã©chouÃ©.
demo.addmeterreadings.success={0} : {1} a Ã©tÃ© ajoutÃ©.
demo.addmeterreadings.tariffCalc.success={0} : {1} a Ã©tÃ© ajoutÃ© et le calcul de tarif a Ã©tÃ© effectuÃ©.

# joelc 20 November 2018, Planio 4328
question.custom.field.used= Notez que tout enregistrement d'entitÃ© de groupe utilisant ce champ ne sera PAS mis Ã  jour, \
seule la liste des options disponibles sera mise Ã  jour pour une utilisation ultÃ©rieure.
question.custom.field.used.option.yes=Mettre Ã  jour la liste
question.custom.field.used.option.no=Annuler

# jacciedt: 2021-01-08 | Planio 12082
bulk.upload.unitsaccountname=Nom de compte dâunitÃ©s
bulk.upload.unitslowbalancethreshold=Seuil de solde bas unitÃ©s
bulk.upload.unitsnotificationemail=E-mail de notification dâunitÃ©s
bulk.upload.unitsnotificationphone=NumÃ©ro de tÃ©lÃ©phone de notification dâunitÃ©s

# jacciedt: 2020-12-22 | Planio 11146
error.date.field.invalid=La valeur saisie nâest pas une date valide. Format = {0}

# renciac: 2020-12-10 | Planio 11365
### Units Account ###
unitsacc.title=Compte dâunitÃ©s
unitsacc.name.help=Saisir un nom pour ce compte
unitsacc.name=Nom de compte dâunitÃ©s
unitsacc.balance.help=Solde actuel d'unitÃ©s
unitsacc.balance=Solde d'unitÃ©s
unitsacc.sync.help=Synchroniser le solde d'unitÃ©s avec le solde d'unitÃ©s du compteur
unitsacc.sync=Synchroniser le solde
unitsacc.low.balance.threshold.help=Lorsque le solde d'unitÃ©s atteint ce seuil, un message est envoyÃ© au client.
unitsacc.low.balance.threshold=Seuil de solde bas unitÃ©s
unitsacc.notification.email.help=Liste sÃ©parÃ©e par des virgules des adresses e-mail auxquelles les notifications liÃ©es aux unitÃ©s peuvent Ãªtre envoyÃ©es (par exemple, lorsque le seuil de solde bas est atteint)
unitsacc.notification.email=Adresses e-mail de notification
unitsacc.notification.phone.help=Liste sÃ©parÃ©e par des virgules des numÃ©ros de tÃ©lÃ©phone auxquels les notifications liÃ©es aux unitÃ©s peuvent Ãªtre envoyÃ©es (par exemple, lorsque le seuil de solde bas est atteint)
unitsacc.notification.phone=NumÃ©ros de tÃ©lÃ©phone de notification
unitsacc.note=Un compte d'unitÃ©s est uniquement nÃ©cessaire si le modÃ¨le de compteur et la structure de prix lâexigent.
unitsacc.required=* \= Obligatoire
unitsacc.changes.cleared=Les modifications ont Ã©tÃ© effacÃ©es.
units.account.error.save=Impossible de sauvegarder ce compte dâunitÃ©s.

# jacciedt: 2020-11-27 | Planio 11366
units.account=Compte dâunitÃ©s
units.account.transaction.history=Historique du compte dâunitÃ©s
units.account.transaction.description=PrÃ©cÃ©dentes transactions de compte pour ce compte dâunitÃ©s
amount.cannot.be.zero=Le montant ne peut Ãªtre Ã©gal Ã  zÃ©ro
units.transaction.type=Type de transaction d'unitÃ©s
units.transaction.type.sale=Vente
units.transaction.type.consumption=Consommation
units.transaction.type.manualadj=RÃ©glage manuel
units.transaction.type.reversal=Annulation

# renciac: 2020-11-25 | Planio 11363
# The defaultUnitsAdjustmentProcessor.notification messages has the same arguments as the defaultAccountAdjustmentProcessor, except units instead of currency
defaultUnitsAdjustmentProcessor.notification.disconnect.email.subject=Le solde du compte est Ã©puisÃ© pour {6}
defaultUnitsAdjustmentProcessor.notification.disconnect.email.message=Cher client,\n\nVotre compteur va Ãªtre dÃ©connectÃ©.\n\nLe statut de votre compte estÂ : \n Solde du compteÂ : {7,nombre}\n Seuil de notification de solde faibleÂ : {8,nombre}\n\nSincÃ¨res salutations,\nÃquipe Support
defaultUnitsAdjustmentProcessor.notification.disconnect.sms.message=Le solde de {6} est Ã©puisÃ© et sera dÃ©connectÃ©. Le solde est de {7,nombre} SincÃ¨res salutations, Ãquipe Support
defaultUnitsAdjustmentProcessor.notification.lowBalance.email.subject=Solde de compte faible pour {6}
defaultUnitsAdjustmentProcessor.notification.lowBalance.email.message=Cher client,\n\nLe statut de votre compte estÂ : \n Solde du compteÂ : {7,nombre}\n Seuil de notification de solde faibleÂ : {8,nombre}\n\nSincÃ¨res salutations,\nÃquipe Support
defaultUnitsAdjustmentProcessor.notification.lowBalance.sms.message=Le solde de {6} est faible. Le solde est de {7,nombre} SincÃ¨res salutations, Ãquipe Support

# thomasn: 2020-10-16 | planio-9668
meter.models.battery.capacity=CapacitÃ©
meter.models.battery.capacity.help=Saisir la valeur de la capacitÃ© totale de la batterie. Soit en %, mois, tension, etc.
meter.models.battery.capacity.error=La valeur doit Ãªtre positive
meter.models.battery.threshold=Seuil faible
meter.models.battery.threshold.help=Saisir le pourcentage du seuil de batterie faible. Lorsqu'il est franchi, un Ã©vÃ©nement de batterie faible est dÃ©clenchÃ©.
meter.models.battery.threshold.error=La valeur doit Ãªtre comprise entre 0 et 100.
meter.models.battery.event.lbl=ÃvÃ©nement de batterie

# renciac: 2020-10-16 | Planio 11204
usagepoint.installdate.change.error.readings=Impossible de modifier la date d'installation, nouveaux relevÃ©s sur le compteur/point d'utilisation, veuillez rafraÃ®chir la page.

meter.replace.installation.date.error.trans=Impossible de remplacer le compteur avec une date d'installation ultÃ©rieure, nouvelles transactions sur le point d'utilisation ou nouveaux relevÃ©s sur le compteurÂ ; veuillez rafraÃ®chir la page et rÃ©essayer.

# renciac: 2020-06-09 | Planio 9616
meter.change.activation.date=Modifier la date d'activation = nouvelle date d'installation ?
meter.change.activation.date.required=Veuillez cocher une des cases pour Date d'activation
meter.change.activation.date.error.trans=Impossible de modifier date dâactivation, transactions sur le point d'utilisation
meter.change.installation.date.error.trans=Impossible de modifier la date d'installation, transactions sur le point d'utilisation, veuillez rafraÃ®chir la page.
error.field.installdate.future.trans.or.possible.gap=La date d'installation ne peut pas Ãªtre ultÃ©rieure. Elle ne peut Ãªtre ultÃ©rieure que s'il nây a pas de transactions sur l'UP et si vous avez choisi de modifier la date d'activation = date d'installation.

# marcod : 2020-09-09 | Planio 10498
meter.units.help=Saisir le nombre d'unitÃ©s {0} avec une seule dÃ©cimale.

# marcod : 2020-10-08 | Planio 9723
meter.number.suggestion.help=Commencer Ã  taper le numÃ©ro du compteur, les compteurs qui commencent par ces chiffres apparaÃ®tront dans une liste dÃ©roulante. Cliquer sur lâun des compteurs pour le sÃ©lectionner.

# thomasn: 2020-09-01 | Planio 8404
auxaccount.upload.suspendUntil=Suspendre jusquâÃ 
auxaccount.upload.suspendUntil.in.past=L'option Suspendre jusqu'Ã  ne peut pas Ãªtre antÃ©rieure
auxaccount.upload.suspendUntil.format=L'option Suspendre jusqu'Ã  doit Ãªtre vide ou correctement formatÃ©e
customer.auxaccount.suspend.until.lbl=Suspendre jusquâÃ 
customer.auxaccount.suspend.until.error=La date doit Ãªtre situÃ©e dans le futur.
customer.auxaccount.suspend.until=Suspendu jusquâÃ 
customer.auxaccount.txn.history.suspend.until=Suspendu jusquâÃ Â : {0}

# jacciedt: 2020-08-13 | Planio 10007
auxtype.error.update.in.use=Impossible de dÃ©sactiver le type auxiliaire, il est dÃ©jÃ  utilisÃ©.

# jacciedt: 2019-01-29 | Planio 8575
bulk.upload.invalid.regex={0} ne correspond pas Ã  son schÃ©ma regex

# jacciedt: 2020-06-17 | Planio 9605
demo.addmeterreadings.weekly=Hebdomadaire

# jacciedt: 2020-04-09 | Planio 6150
customer.unassign.unassign.customer=DÃ©saffecter le client
usagepoint.charge.button.writeoff.and.unassign.customer=Supprimer les frais et dÃ©saffecter le client

# joelc: 2020-07-10 | Planio 9609
reprint.remaining.balance=Solde
reprint.desc=Description

# renciac: 2019-12-03 | Planio 5311
file.item.panel.reg.read.reminder=RAPPELÂ : Un relevÃ© initial peut Ãªtre nÃ©cessaire pour tous les compteurs avec des tarifs de lecture de registre.
channel.readings.header.up=Point d'utilisationÂ : 
channel.readings.timestamp.label=Horodatage du relevÃ©
channel.readings.timestamp.help=L'horodatage du relevÃ© doit Ãªtre Ã©gal Ã  la date d'installation OU ultÃ©rieur Ã  l'horodatage du relevÃ© existant pour cette installation de compteur. 
channel.readings.table.error.heading=Erreur
channel.readings.partial.entry=Les lectures initiales du registre pour les canaux de ce compteur ne sont pas ou seulement partiellement rÃ©alisÃ©es. Voulez-vous sauvegarder en lâÃ©tat et complÃ©ter le reste plus tardÂ ?
channel.readings.preExisting.note=REMARQUEÂ : Il existe des relevÃ©s prÃ©existants pour cette installation de compteur et de point d'utilisation. 
channel.readings.preExisting.same.mdc.channels=Ils proviennent des mÃªmes canaux MDC, tel quâindiquÃ© dans le tableau ci-dessous. \nAppuyer sur ANNULER pour les conserver tels quels. Si vous souhaitez modifier les relevÃ©s initiaux, saisir de nouvelles valeurs. \nRemarqueÂ : Lâhorodatage du relevÃ© doit Ãªtre > antÃ©rieur.
channel.readings.preExisting.diff.mdc.channels=Ils semblent provenir de chaÃ®nes MDC antÃ©rieures. La derniÃ¨re date de lecture trouvÃ©e Ã©tait : {0}.\nSaisir de nouvelles valeurs pour les nouveaux canaux MDC. RemarqueÂ : L'horodatage du relevÃ© doit Ãªtre > antÃ©rieur Ã  la derniÃ¨re date de relevÃ©.
channel.readings.preExisting.note.end=\nIf Si aucune de ces options ne convient, veuillez contacter le Support SystÃ¨me. 
channel.readings.timestamp.install.date=Lâhorodatage du relevÃ© doit Ãªtre Ã©gal Ã  la date dâinstallation.
channel.readings.timestamp.previous.date=Lâhorodatage du relevÃ© doit Ãªtre ultÃ©rieur Ã  la date de relevÃ© prÃ©cÃ©denteÂ : {0} 
button.ok=OK

warning.change.mdc.on.meter.NO.DATA=AVERTISSEMENTÂ : La modification du MDC sur le ModÃ¨lecompteur peut affecter {0} points d'utilisation actifs et {1} points d'utilisation inactifs qui n'ont pas de tarifs de lecture de registre. ContinuerÂ ?
warning.change.mdc.on.meter.PARTIAL.or.TOTAL.active.up=AVERTISSEMENTÂ : La modification du MDC sur le ModÃ¨lecompteur peut affecter {0} points d'utilisation actifs et {1} points d'utilisation inactifs qui ont des tarifs de lecture de registre et une correspondance PARTIELLE ou EXACTE Ã  un facteur de facturation. Il est Ã©galement possible de modifier ici l'option PAS de correspondance de points d'utilisation inactifs (lorsqu'ils seront activÃ©s, PAS de correspondance sera rejetÃ©). ContinuerÂ ?
error.change.mdc.on.meter.NONE.MATCH.active.up=ERREURÂ : La modification du MDC sur ce ModÃ¨lecompteur a {0} points d'utilisation ACTIFS avec des tarifs de lecture de registre et AUCUNE CORRESPONDANCE aux facteurs de facturation de certains. Impossible de modifier le mdc sur le modÃ¨le de compteur pour lâinstant.

warning.change.mdc.channel.NO.DATA=AVERTISSEMENTÂ : La modification/(affectation d'un nouveau) canal sur ce MDC peut affecter {0} points d'utilisation actifs et {1} points d'utilisation inactifs qui n'ont pas de tarifs de lecture de registre. ContinuerÂ ?
warning.change.mdc.channel.PARTIAL.or.TOTAL.with.regreadPS=AVERTISSEMENTÂ : La modification/(affectation d'un nouveau) canal sur ce MDC peut affecter {0} points d'utilisation actifs et {1} points d'utilisation inactifs qui ont des tarifs de lecture de registre et une correspondance PARTIELLE ou EXACTE Ã  un facteur de facturation. ContinuerÂ ?
error.change.mdc.channel.NONE.MATCH.active.up=ERREURÂ : La modification/(affectation d'un nouveau) canal sur ce MDC a {0} points d'utilisation ACTIFS avec des tarifs de lecture de registre et AUCUNE CORRESPONDANCE aux facteurs de facturation de certains. Impossible de modifier les canaux/facteurs de facturation pour l'instant.

warning.change.mdc.NO.DATA=AVERTISSEMENTÂ : La modification du MDC affectera {0} points d'utilisation actifs et {1} points d'utilisation inactifs qui n'ont pas de tarifs de lecture de registre. ContinuerÂ ?
warning.change.mdc.inactive.with.regreadPS=AVERTISSEMENTÂ : La modification du MDC peut affecter {0} points d'utilisation actifs et {1} points d'utilisation inactifs qui ont des tarifs de lecture de registre. ContinuerÂ ?
error.deactivated.mdc.active.up=ERREURÂ : Impossible de dÃ©sactiver ce MDC - Il compte {0} points d'utilisation actifs et {1} points d'utilisation inactifs qui ont des tarifs de lecture de registre. 

warning.change.or.new.tariff.has.diff.billing.dets.to.active.up.with.ps=AVERTISSEMENTÂ : La modification/crÃ©ation d'un nouveau tarif de lecture de registre sur cette structure de tarif a une correspondance PARTIELLE de facteurs de facturation Ã  certains des points d'utilisation actifs utilisant la structure de tarif. ContinuerÂ ?
error.change.or.new.tariff.has.diff.billing.dets.to.active.up.with.ps=ERREURÂ : La modification/crÃ©ation d'un nouveau tarif de lecture de registre sur cette structure de tarif nâa AUCUNE CORRESPONDANCE aux facteurs de facturation de certains des points d'utilisation actifs utilisant la structure de tarif. Il doit au moins y avoir une correspondance dans TOUS les cas.
warning.change.status.billing.det.but.in.use=La modification du statut de ce facteur de facturation peut affecter {0} points d'utilisationÂ actifs et {1} points d'utilisation inactifs dont les compteurs sont affectÃ©s Ã  une modÃ¨le de compteur utilisant un MDC avec des canaux affectÃ©s Ã  ce facteur de facturation. ContinuerÂ ?
error.cannot.activate.up.model.not.channels.with.regread.ps=ERREURÂ ! Impossible dâactiver le point d'utilisation. Le modÃ¨le de compteur et la structure de tarif doivent avoir en commun au moins un canal ayant le mÃªme facteur de facturation.
error.incompatible.model.with.ps=ERREURÂ ! Le modÃ¨le de compteur et la structure de prix ne sont pas compatibles. \nSoit le modÃ¨le de compteur dispose de canaux mais pas de structure de tarif de lecture de registre, soit l'inverse.

# renciac: 2019-11-07 | Planio 7951
channel.field.maxsize.help=RelevÃ© maximum sur le registre du compteur avant le retour Ã  zÃ©ro.
channel.field.time.interval.help=Intervalle de temps des lectures de registre
channel.field.time.interval=Intervalle de temps
channel.config.header=Contournement des canaux
channel.config.overrides.button=Contournement des canaux MDC
channel.config.mdc.titlename=Mdc
channel.config.field.titlename=Contournement canal compteur
channel.config.title.add=Ajouter contournement canal compteur
channel.config.title.update=Mettre Ã  jour contournement canal compteur
channel.field.titlename.help=N'affiche que les canaux MDC qui n'ont pas encore Ã©tÃ© contournÃ©sÂ âÂ il faut sÃ©lectionner un canal MDC Ã  contourner
channel.config.field.billingdetnames=Facteur(s) de facturation
channel.config.error.delete=Impossible de supprimer lâoption Contournement canal compteur. Contacter lâÃ©quipe Support
channel.config.delete.confirm=Ãtes-vous sÃ»r(e) de vouloir supprimer l'option Contournement canal compteurÂ ?
channel.config.deleted=L'option Contournement canal compteur a Ã©tÃ© supprimÃ©e.
mdc.channel.override.metermodels=Contournement depuis modÃ¨les(s) de compteur
mdc.channel.override.metermodels.none=Pas de contournement
channel.config.already.override=Le canal fait est dÃ©jÃ  contournÃ©. SÃ©lectionner un dans la liste ci-dessus. 
channel.field.meter.reading.type.title=Type de relevÃ©
meter.model.change.mdc.confirm.delete.configs=Le modÃ¨le de compteur avait des contournements de canaux pour le MDC prÃ©cÃ©dent sur ce modÃ¨le. Ils seront supprimÃ©s si vous modifiez le MDC. ContinuerÂ ?
meter.model.channel.configs.error.delete=Impossible de supprimer lâoption Contournement canal compteur pour le MDC prÃ©cÃ©dent. Contacter lâÃ©quipe Support
channel.config.no.channels.to.override=Aucun canal nâest dÃ©fini pour ce MDC/ 
mdc.channel.status.change.warn.overrides=RemarqueÂ : ce canal de mdc comporte des contournements. ProcÃ©der Ã  la modification du statutÂ ?
mdc.channel.field.time.interval=Intervalle de temps canalmdc
channel.override.field.time.interval=Intervalle de temps contournement
mdc.channel.field.maxsize=Taille maximum relevÃ© canalmdc
channel.override.field.maxsize=Taille maximum relevÃ© contournement
mdc.channel.field.reading_multiplier=Multiplicateur relevÃ© canalmdc
channel.override.field.reading_multiplier=Multiplicateur relevÃ© contournement
import.edit.reg.Read.item.update.success=Les donnÃ©es du compteur {0}, l'horodatage du relevÃ©  {1} ont Ã©tÃ© mises Ã  jour avec succÃ¨s.
import.reg.Read.channel.value.label=Valeur du canal
import.reg.Read.timestamp.label=Horodatage du relevÃ©
reg.read.init.readings.cancel.confirm=En cas dâannulation, aucun autre relevÃ© initial pour les canaux (registres) de ce compteur n'est enregistrÃ© et que les calculs de tarif utiliseront les premiers relevÃ©s ou les relevÃ©s existants en tant que relevÃ©s initiaux. ContinuerÂ ?
channel.readings.import.note=RemarqueÂ : Les relevÃ©s de registre ne peuvent Ãªtre importÃ©s que manuellement.

# barryc: 2020-05-12 | Planio 8211
tariff.error.monthlycost.name=Saisir un nom pour les frais.
tariff.error.monthlycost=Un nombre valide doit Ãªtre fourni
tariff.error.monthlycost.positive=La valeur doit Ãªtre positive ou Ã©gale Ã  zÃ©ro

# barryc: 2020-04-21 | Planio 7780
error.field.specialactionsdescription.max=La description doit comporter entre {min} et {max} caractÃ¨res.

# jacciedt: 2020-04-14 | Planio 8139
meter.model.deactivate.in.use.error=DÃ©sactivation impossibleÂ âÂ certains compteurs utilisent dÃ©jÃ  ce modÃ¨le de compteur.

# jacciedt: 2020-04-01 | Planio 7873
question.confirm.installation.date.future.date=date ultÃ©rieure
usagepoint.field.meter.activation.date=Date dâactivation initiale

# barryc: 2020-04-22 | Planio 8209
tariff.error.percent_charge=Saisir le montant des frais.
tariff.error.unit_charge=Saisir une date.

# patrickm: 2020-04-03 | Planio 8675
register.reading.txn.create_date=Date crÃ©Ã©e

#joelc: 2020-02-19 | Planio 8592
meter.models.field.data.decoder=DÃ©codeur des donnÃ©es de compteur
meter.models.field.data.decoder.help=Certains modÃ¨les de compteur nÃ©cessitent des dÃ©codeurs spÃ©cifiques pour traiter leurs relevÃ©s.  

# patrickm: 2020-01-14 | Planio 7768
blockingtype.form.dailyamount=Montant par jour
blockingtype.form.dailyamount.help=Montant maximum autorisÃ© par jour
blockingtype.msg.error.dailyamount=Vous devez inclure le modÃ¨le de montant maximum par jour, {max_amount_per_day}, dans votre message.

# jacciedt: 2019-01-20 | Planio 7356
usagepoint.hist.device.move.ref=RÃ©fÃ©rence de mouvement de lâappareil
usagepoint.device.move.ref.lbl=NumÃ©ro de rÃ©fÃ©rence de mouvement de lâappareil
error.field.devicemoveref.max=La rÃ©fÃ©rence de mouvement de lâappareil doit comporter moins de {max} caractÃ¨res.

# jacciedt: 2019-12-31 | Planio 7950
configure.user.interface=Configurer interface utilisateur
configure.user.interface.field.name=Nom du champ
configure.user.interface.display=AffichageÂ ?
configure.user.interface.validation.regex=Validation Regex
configure.user.interface.regex.failed.message=Message d'Ã©chec de Regex/Valeurs Ã©numÃ©rÃ©es
changes.saved=Les modifications ont Ã©tÃ© sauvegardÃ©es.
configure.user.interface.invalid.regex=Un ou plusieurs champs contiennent des schÃ©mas regex non valides.
error.required.field={0} est un champ obligatoire â s'il est vide, le fichier ne peut pas Ãªtre sauvegardÃ©.
error.regex.invalid=La valeur de saisie ne correspond pas au schÃ©ma requis deÂ : {0}
configure.user.interface.enumerated.values.label=Saisir la liste des valeurs possibles sÃ©parÃ©es par des virgules :
enumerated.field=champ Ã©numÃ©rÃ©

# jacciedt: 2019-11-18 | Planio 7264
meter.select.meter.phase=Type compteur
meter.select.meter.phase.help=SÃ©lectionner la bonne phase de compteur pour le modÃ¨le de compteur.

# jacciedt: 2019-12-04 | Planio 7772
meter.txn.reversal.reason=Motif de lâannulation
meter.txn.reversed.by=AnnulÃ© par
vend.reversal.exceeds.time.limit=Ãchec de lâannulation. DÃ©lai dâannulation dÃ©passÃ©.

# joelc: 2019-11-25 | Planio 7487
sts.tokens.header = Codes compteur STS
verify.token = VÃ©rifier le code compteur
verification.error.timeout = Erreur lors de la vÃ©rification des codes compteur. Aucune rÃ©ponse reÃ§ue du service.
verification.error.general=Impossible de vÃ©rifier le code compteur
verify.token.class=CatÃ©gorie de code compteur
verify.token.subclass=Sous-catÃ©gorie de code compteur
verify.token.id=ID de code compteur
verify.token.units=UnitÃ©s
verify.token.date=Date du code compteur

# jacciedt: 2019-11-21 | Planio 7362
energybalancing.error.duplicate.selected.meters=Le compteur sÃ©lectionnÃ© est dÃ©jÃ  ajoutÃ© Ã  la liste.

# zachv: 2019-11-21 | Planio 7916
tariff.field.free.units.title=UnitÃ©s gratuites mensuelles
tariff.field.percent_charge.title=Frais en pourcentage
tariff.field.percent_charge.add=Ajouter frais en pour cent
tariff.field.cyclic_charge.title=Frais cycliques
tariff.field.cyclic_charge.add=Ajouter frais cycliques
tariff.field.non_accruing_monthly.name=Frais mensuels non cumulÃ©s
tariff.field.non_accruing_monthly.name.help=Ne sâapplique quâau cycle MENSUEL. Les frais mensuels non cumulÃ©s ne sont facturÃ©s que pour le mois au cours duquel une transaction est effectuÃ©e. Les mois prÃ©cÃ©dents au cours desquels aucun frais mensuel n'a Ã©tÃ© prÃ©levÃ© (par exemple, du fait de lâabsence d'achat ou de la prÃ©sence de codes compteur gratuits) ne seront pas facturÃ©s rÃ©troactivement.
tariff.field.unit_charge.title=Frais unitaires
tariff.field.unit_charge.add=Ajouter frais unitaires
tariff.field.unit_charge.name=Nom des frais
tariff.field.unit_charge.name.help=Nom de ces frais qui apparaÃ®tra sur le reÃ§u du client.
tariff.field.unit_charge.is_percent=Pourcentage
tariff.field.unit_charge.is_percent.help=Indique si facturÃ© en tant que pourcentage du prix unitaire ou sous la forme de frais monÃ©taires par unitÃ©.
tariff.field.unit_charge.is_taxable=Soumis Ã  taxe
tariff.field.unit_charge.is_taxable.help=Indique si la taxe est appliquÃ©e ou non Ã  ces frais.
tariff.field.unit_charge=Frais unitaires
tariff.field.unit_charge.help=La valeur monÃ©taire Ã  facturer par unitÃ© achetÃ©e. Il s'agit d'un supplÃ©ment par rapport au prix unitaire de base.
tariff.field.unit_charge_percent=Pourcentage du prix unitaire
tariff.field.unit_charge_percent.help=Ces frais seront appliquÃ©s en tant que pourcentage du prix unitaire. S'il y a des blocs, le prix du bloc sera utilisÃ©.
tariff.error.unit_charge.name=Saisir un nom pour les frais.
tariff.error.unit_charge.positive_not_zero=Doit correspondre Ã  une valeur positive et non Ã  zÃ©ro.

# jacciedt: 2019-11-12 | Planio 7814
customer.auxaccount.increase.debt=Augmenter DETTE deÂ :
customer.auxaccount.decrease.debt=RÃ©duire DETTE deÂ :
customer.auxaccount.increase.refund=Augmenter REMBOURSEMENT deÂ :
customer.auxaccount.decrease.refund=RÃ©duire REMBOURSEMENT deÂ :
customer.auxaccount.error.amount.negative=Le montant ne peut pas Ãªtre nÃ©gatif
customer.auxaccount.error.tax.negative=La taxe ne peut pas Ãªtre nÃ©gative
customer.auxaccount.error.adjustment=SÃ©lectionner Augmenter ou RÃ©duire
customer.auxaccount.error.balance.refund=Le solde ne peut Ãªtre nÃ©gatif ou Ã©gal Ã  zÃ©ro

# patrickm: 2019-11-02 | Planio #7801
customer.search.listbox.label=Recherche client par
customer.search.listbox.item_agr_ref=RÃ©f. de contrat
customer.search.listbox.item_id_num=NumÃ©ro ID
customer.search.listbox.item_surname=Nom de famille

# jacciedt: 2019-10-24 | Planio 7812
customer.auxaccount.functions.as=Fonctions en tant que

# thomasn: 2019-10-17 | planio-5133
link.blockingtype=Types de blocage
blockingtype.title=Type de blocage
blockingtype.name=Nom
blockingtype.panel.error.missingvalues=L'une des valeurs ({0}, {1}, {2}, {3})\ndoit Ãªtre dÃ©finie OU il doit s'agir d'un bloc complet.
blockingtype.title.add=Ajouter type de blocage
blockingtype.title.update=Mettre Ã  jour type de blocage
blockingtype.form.typename=Nom
blockingtype.form.typename.help=Nom du type de blocage
blockingtype.form.units=UnitÃ©s par jour
blockingtype.form.units.help=UnitÃ©s maximums autorisÃ©es par jour
blockingtype.form.complete=Complet
blockingtype.form.complete.help=En cas de bloc complet, aucune vente ne sera autorisÃ©e.
blockingtype.form.vends=Nombre de ventes
blockingtype.form.vends.help=Ventes maximums autorisÃ©es avant bloc complet
blockingtype.form.amount=Montant maximum
blockingtype.form.amount.help=Montant maximum autorisÃ© avant bloc complet
blockingtype.form.message=Message
blockingtype.form.message.help=Message avec dÃ©tails du bloc affichÃ© Ã  lâattention de lâutilisateur. Utiliser {remaining_vends} pour afficher les ventes rÃ©siduelles avant bloc complet. Utiliser {remaining_amount} pour afficher le montant rÃ©siduel avant bloc complet. Utiliser {max_units_per_day} pour afficher les unitÃ©s maximums autorisÃ©es par jour. Utiliser {max_amount_per_day} pour afficher le montant maximum autorisÃ© par jour. Utiliser {reason_fixed} pour afficher le motif du bloc. Remarque : les crochets indiquent les variables du systÃ¨me.
blockingtype.error.save=Impossible de sauvegarder le type de blocage.
blockingtype.error.save.duplicate=Impossible d'enregistrer le type de blocage, un autre type de mise bloc portant le mÃªme nom existe dÃ©jÃ .
blockingtype.error.update=Impossible de mettre Ã  jour le type de blocage.
blockingtype.error.update.duplicate=Impossible de mettre Ã  jour le type de blocage, un autre type de blocage portant le mÃªme nom existe dÃ©jÃ .
blockingtype.msg.error.variables=Les crochets ne sont autorisÃ©s que pour les variables systÃ¨me valides, {max_units_per_day}, {max_amount_per_day}, {remaining_amount}, {remaining_vends} ou {reason_fixed}.
blockingtype.msg.error.units=Vous devez inclure le modÃ¨le d'unitÃ©s, {max_units_per_day}, dans votre message.
blockingtype.msg.error.units.undefined=ModÃ¨le d'unitÃ©s {max_units_per_day} non autorisÃ©, Ã  moins que {0} ait Ã©tÃ© dÃ©fini.
blockingtype.msg.error.vends=Vous devez inclure le modÃ¨le de nombre de ventes, {remaining_vends}, dans votre message.
blockingtype.msg.error.vends.undefined=ModÃ¨le de ventes {remaining_vends} non autorisÃ©, Ã  moins que {0} ait Ã©tÃ© dÃ©fini.
blockingtype.msg.error.amount=Vous devez inclure le modÃ¨le de montant, {remaining_amount}, dans votre message.
blockingtype.msg.error.amount.undefined=ModÃ¨le de montant {remaining_amount} non autorisÃ©, Ã  moins que {0} ait Ã©tÃ© dÃ©fini.
blockingtypes.header=Types de blocage
blockingtypes.title=Types de blocage actuels

# jacciedt: 2019-10-10 | Planio 7514
meter.mrid.external.unique.validation=LâID unique de ce compteur est dÃ©jÃ  utilisÃ©

# jacciedt: 2019-09-17 | Planio 5823
demo.addmeterreadings.earliest.reading=RelevÃ© prÃ©cÃ©dent
demo.addmeterreadings.latest.reading=Dernier relevÃ©
demo.addmeterreadings.zero.checkbox.text=Ajouter relevÃ©s Ã  zÃ©ro
demo.addmeterreadings.zero.form.title=RelevÃ©s Ã  zÃ©ro
demo.addmeterreadings.consecutive=ConsÃ©cutif
demo.addmeterreadings.random=AlÃ©atoire
demo.addmeterreadings.percentage.instances=Pourcentage dâinstances
demo.addmeterreadings.missing.checkbox.text=Ajouter relevÃ©s manquants
demo.addmeterreadings.missing.form.title=RelevÃ©s manquants
demo.addmeterreadings.algorithm.logic=Logique de l'algorithme
demo.addmeterreadings.delete=Supprimer les relevÃ©s d'intervalle existants
demo.addmeterreadings.delete.all=Tout
demo.addmeterreadings.delete.selected=Plage de date sÃ©lectionnÃ©e
demo.addmeterreadings.append=Annexe
demo.addmeterreadings.link=[DÃMO] Ajouter relevÃ©s de compteur
demo.addmeterreadings.header=Ajouter relevÃ©s de compteur
demo.addmeterreadings.title=Ajouter relevÃ©s de compteur
demo.addmeterreadings.title.criteria.register=CritÃ¨res de lecture de registre
demo.addmeterreadings.reading.variants=Choisir une variante de relevÃ©
demo.addmeterreadings.delete.register=Supprimer les lectures de registre existantes
demo.addmeterreadings.error.misc.start=Cette date de dÃ©but doit Ãªtre postÃ©rieure Ã  la date de dÃ©but principale.
demo.addmeterreadings.error.misc.end=Cette date de fin doit Ãªtre antÃ©rieure Ã  la date de fin principale.
demo.addmeterreadings.error.instances.required=Le champ Instances est obligatoire
demo.addmeterreadings.error.instances.format=Le champ Instances doit Ãªtre numÃ©rique
demo.addmeterreadings.error.instances.range=Le champ Instances doit Ãªtre un nombre entier compris entre 0 et 100.
demo.addmeterreadings.error.mdc.channel=Aucun canal MDC sÃ©lectionnÃ©

# renciac: 2019-09-19 | Planio 7656
import.generic.start.label=DÃ©but des donnÃ©es
error.field.enckey.max=La clÃ© de chiffrement du compteur ne doit pas dÃ©passer 255 caractÃ¨res maximum.
error.field.powerlimitlabel.max=La limite de puissance LABEL ne doit pas dÃ©passer 255 caractÃ¨res maximum.
button.import.extract.fail=Ãchec de lâextraction
import.upload.num.failed.upload.label=Nombre de tÃ©lÃ©chargements ayant Ã©chouÃ©
import.items.unsuccessful.uploads.reminder=\nRAPPELÂ : Ce fichier comportait {0} Ã©lÃ©ments non tÃ©lÃ©chargÃ©s correctement. Ils sont ignorÃ©s pour la suite du traitement et doivent Ãªtre recrÃ©Ã©s manuellement dans un nouveau fichier et tÃ©lÃ©chargÃ©s Ã  nouveau.
import.upload.completed=Fichier tÃ©lÃ©chargÃ©. 
bulk.upload.file.error=Erreur lors de lâimportation du fichier
error.field.customvarchar1.max=Customvarchar1 UP peut avoir un maximum de 255 caractÃ¨res.
error.field.customvarchar2.max=Customvarchar2 UP peut avoir un maximum de 255 caractÃ¨res.
error.field.phonecontact1.max=Contact TÃ©lÃ©phone1 doit comporter moins de {max} caractÃ¨res.
error.field.phonecontact2.max=Contact TÃ©lÃ©phone2 doit comporter moins de {max} caractÃ¨res.
error.field.notificationemail.max=Adresse E-mail de Notification Compte Client doit comporter moins de {max} caractÃ¨res.
error.field.notificationphone.max=TÃ©lÃ©phone de Notification Compte Client doit comporter moins de {max} caractÃ¨res.
import.file.item.view.still.busy=L'importation de ce fichier est en cours. Aucune autre action ne peut Ãªtre rÃ©alisÃ©e tant que lâimportation nâest pas terminÃ©e ou interrompue.
import.file.item.view.still.busy.stopped=Instruction d'arrÃªt d'importation Ã©mise. L'importation sera interrompue aprÃ¨s le lot actuel.
import.file.view.upload.still.busy=Le tÃ©lÃ©chargement de ce fichier est en cours. 
button.stop.import.all=ArrÃªter l'importation
import.file.stopped=L'importation de ce fichier a Ã©tÃ© interrompue.
import.file.stopped.instruction=Instruction d'arrÃªt d'importation Ã©mise. L'importation sera interrompue aprÃ¨s le lot actuel.

# renciac: 2019-09-16 | Planio 6715
bulk.upload.idNumber=NumÃ©ro ID

# thomasn: 2019-10-09 | Planio 6286
usagepoint.unblocking.enter.reason=Saisir un motif de dÃ©blocage
usagepoint.unblocking.select.reason=SÃ©lectionner un motif de dÃ©blocage

# thomasn: 2019-09-09 | planio-6287 Add reason when re-activating a UP
usagepoint.hist.status.reason=Motif du statut

# jacciedt: 2019-09-10 | Planio 7490
meter.clearreverseflag=Effacer le drapeau d'annulation
meter.disabletriplimit=DÃ©sactiver la limite de dÃ©clenchement
meter.setcurrentlimit=DÃ©finir la limite actuelle
meter.issue.token.description.help=Saisir une description pour ce {0}.

# robertf: 2019-09-10 | Planio 7571
customer.txn.reason=Motif d'action
customer.auxaccount.history.title=Historique du compte auxiliaire
customer.auxaccount.history.filter.title=Historique du compte auxiliaire pour : {0}
customer.auxaccount.history.filter.discription=Modifications prÃ©cÃ©dentes effectuÃ©es sur le compte auxiliaireÂ : {0}
customer.auxaccount.history.table.header.datemodified=Date modifiÃ©e
customer.auxaccount.history.table.header.user=Utilisateur
customer.auxaccount.history.table.header.action=Action
customer.auxaccount.history.table.header.type=Type
customer.auxaccount.history.table.header.accountname=Nom du compte
customer.auxaccount.history.table.header.balance=Solde
customer.auxaccount.history.table.header.priority=PrioritÃ©
customer.auxaccount.history.table.header.chargeschedule=Calendrier de frais
customer.auxaccount.history.table.header.freeissue=Ãmission gratuite
customer.auxaccount.history.table.header.status=Statut
customer.auxaccount.history.table.header.updatereason=Mettre Ã  jour motif
customer.auxaccount.history.table.header.createreason=CrÃ©er motif
customer.title.auxaccounts.history.selector.description=SÃ©lectionner compte auxiliaire pour afficher lâhistorique.

# jacciedt: 2019-08-14 | Planio 7341
import.account.number=NumÃ©ro du compte
import.arrears.balance=Solde des arriÃ©rÃ©s
import.debtor.balance=Solde des dÃ©biteurs
import.edit.account.number.update.success=Les donnÃ©es du numÃ©ro de compte {0} ont Ã©tÃ© mises Ã  jour.
import.edit.generic.update.success=DonnÃ©es mises Ã  jour.

# jacciedt: 2019-07-18 | Planio 6240
meter.select.store.add=Magasin rÃ©gional

# jacciedt: 2019-08-15 | Planio 7310
transaction.history.graph.yaxis.label2=Nombre dâunitÃ©s

# jacciedt: 2019-08-22 | Planio 6738
tou.thin.error.tax.positive=La taxe doit avoir une valeur positive.
register.reading.tax.positive=La taxe doit avoir une valeur positive.

# jacciedt: 2019-07-29 | Planio 7197
error.field.validity.email=L'une des adresses e-mail nâest pas valide.
reprint.warning.line.1=AVERTISSEMENTÂ !!! IL SâAGIT DâUNE RÃIMPRESSION
reprint.warning.line.2=d'un code compteur Ã©mis le {0}
reprint.warning.line.3=FACTURE FISCALE (COPIE)
reprint.warning.line.4=RÃ©imprimÃ© leÂ : {0}
reprint.warning.line.5=RÃ©imprimÃ© parÂ : {0}
reprint.credit.vend.tax.invoice=Vente de crÃ©dit - Facture fiscale
reprint.util.name=RÃ©g. Nom
reprint.util.dist.id=RÃ©g. Dist. ID
reprint.util.vat.no=RÃ©g. NÂ° de TVA
reprint.util.address=RÃ©g. Adresse
reprint.issued=Ãmis
reprint.token.tech=Tech. code compteur
reprint.alg=Alg.
reprint.sgc=SGC
reprint.krn=KRN
reprint.your.resource.token=Votre code compteur {0}
reprint.standard.token=Code compteur standard
reprint.receipt.nr=ReÃ§u nÂ°
reprint.free.basic.resource=Code compteur de base gratuit {0}
reprint.debt.items=ÃlÃ©ments de dette
reprint.fixed.items=ÃlÃ©ments fixes
reprint.total.vat.excl=Total (HT)
reprint.total.vat.incl=Total (TTC)
reprint.print=Imprimer
reprint.deposit=DÃ©pÃ´t
reprint.save.to.pdf=Enregistrer au format PDF
reprint.electricity=ÃlectricitÃ©
reprint.water=Eau
reprint.gas=Gaz

# jacciedt: 2019-08-20 | Planio 7364
error.supplygroup.server=Dupliquer le code du groupe d'approvisionnement et le numÃ©ro de rÃ©vision de la clÃ©. Saisir des valeurs uniques.

# jacciedt: 2019-08-15 | Planio 7449
location.field.address.line.2=Ligne 2
location.field.address.line.3=Ligne 3

# jacciedt: 2019-08-01 | Planio 7368
location.field.address.line.1=Ligne 1
customer.phone.1=NumÃ©ro de tÃ©lÃ©phone 1
customer.phone.2=NumÃ©ro de tÃ©lÃ©phone 2

# jacciedt: 2019-06-19 | Planio 7024
timezone.warning=RemarqueÂ : Tous les onglets doivent Ãªtre fermÃ©s avant que le fuseau horaire puisse Ãªtre modifiÃ©.

# jacciedt: 2019-07-15 | Planio 7244
error.field.specialactionsname.range=Le nom de l'action doit comporter entre {min} et {max} caractÃ¨res.

# jacciedt: 2019-07-16 | Planio 7148
unit.kiloliter.symbol=kl
unit.cubicmeter.symbol=m\u00B3
meter.units=UnitÃ©s ({0})

# robertf: 2019-07-08 | Planio 6247
transaction.history.column.header.stdunits=UnitÃ©s standards
transaction.history.column.header.fbeunits=UnitÃ©s de base gratuites
transaction.history.column.header.stdtoken=Total code compteur standard
transaction.history.column.header.fixedamt=Total coÃ»ts fixes
transaction.history.column.header.auxamt=Total paiement auxiliaire

# thomasn: 2019-07-01 | Planio-6288
usagepoint.blocking.info.message=Ce point d'utilisationÂ a Ã©tÃ© bloquÃ© par {0} le {1} 
usagepoint.blocking.info.message.reason= pour le motif suivantÂ : <br/> {0}
usagepoint.hist.blocking.name=Type de blocage
usagepoint.hist.blocking.reason=Motif de blocage

# renciac: 2019-06-26 | Planio 6291
bulk.upload.powerlimit.key=Limite de puissance
meter.powerlimit.key.error.integer=La valeur de la limite de puissance doit Ãªtre un nombre entier
meter.powerlimit.key.error.not.configured=Aucun paramÃ¨tre de limite de puissance n'est configurÃ© dans les paramÃ¨tres de l'application.
meter.powerlimit.key.error.invalid=Cette limite de puissance nâest pas configurÃ©e dans les paramÃ¨tres de lâapplication.

error.field.usagepointgroups.required.group=Le groupe de points dâutilisationÂ requis manqueÂ : {0} 
error.field.usagepointgroups.missing.group=HiÃ©rarchie incomplÃ¨teÂ : champ de groupe de points d'utilisation manquant pour le type de point d'utilisation : {0}
error.field.usagepointgroups.invalid.group=Nom de groupe de points d'utilisation non valide {0} pour le type de point d'utilisationÂ : {1}
error.field.usagepointgroups.incomplete.group=Niveau de hiÃ©rarchie incomplet dans le groupe de points d'utilisationÂ : {0} 

bulk.upload.invalid.locationgroups.not.configured=Les groupes de localisation ne sont pas configurÃ©s pour ce systÃ¨me
usage.point.location.group.generate.label=Groupe de localisation de points d'utilisation
error.field.uplocationgroups.required=Le groupe de localisation de points dâutilisationÂ requis manque
error.field.uplocationgroups.invalid.group=Nom de groupe de localisation non valide {0} pour le groupe de localisation de points d'utilisation
error.field.uplocationgroups.incomplete.group=Niveau de hiÃ©rarchie incomplet dans le groupe de localisation de points d'utilisation
error.field.uplocationgroups.first.level.required=Le groupe de localisation de points d'utilisation est obligatoire. Au moins le premier niveau de la hiÃ©rarchie doit Ãªtre complÃ©tÃ©.

customer.location.group.generate.label=Groupe de localisation client
error.field.custlocationgroups.required=Le groupe de localisation client requis manque. 
error.field.custlocationgroups.invalid.group=Nom de groupe de localisation non valide {0} pour le groupe de localisation client
error.field.custlocationgroups.incomplete.group=Niveau de hiÃ©rarchie incomplet dans le groupe de localisation client
error.field.custlocationgroups.first.level.required=Le groupe de localisation client est obligatoire. Au moins le premier niveau de la hiÃ©rarchie doit Ãªtre complÃ©tÃ©.

location.field.address.suburb.name=Nom banlieue

# jacciedt: 2019-06-21 | Planio 6359
billingdet.error.save.duplicate=Impossible d'enregistrer le facteur de facturation, un autre facteur de facturation portant le mÃªme nom existe dÃ©jÃ .

# renciac: 2019-05-29 | Planio 6237
meter.freeissue.currency=Vente dâurgence

# renciac: 2019-04-25 | Planio 6235
customer.id.partial.search=Il n'y a pas d'ID client qui correspond exactement. Recherche avancÃ©e en cours....
error.customer.load=Impossible d'afficher le client.
search.get.total.label=Compter rÃ©sultats totaux des critÃ¨res sÃ©lectionnÃ©s
search.count.label=Comptage des rÃ©sultats totaux des critÃ¨res sÃ©lectionnÃ©s....

# renciac: 2019-04-17 | Planio 6234
customer.id.error.noentry=Saisir le numÃ©ro ID client ou le % pour tous
metersearch.error.nometer=Saisir un numÃ©ro de compteur ou % pour tous
usagepoint.error.none=Saisir un nom de point dâutilisation ou % pour tous
customer.error.noentry=Saisir un nom de famille client ou % pour tous
customer.agreement.error.noentry=Saisir un numÃ©ro d'accord ou % pour tous
customer.account.error.noentry=Saisir un numÃ©ro de compte ou % pour tous

# renciac: 2019-03-25 | Planio 5961
import.upload.header=TÃ©lÃ©chargement/importation fichier
import.upload.file.upload.title=TÃ©lÃ©chargement fichier
import.filetype.select.file.help=SÃ©lectionnez le type de fichier Ã  tÃ©lÃ©charger et Ã  importer.
import.filetype.select.labeltext=Type de fichier
import.upload.filetype.none=Veuillez sÃ©lectionner le type de fichier Ã  tÃ©lÃ©charger 
import.upload.filename.txt=Nom de fichier sÃ©lectionnÃ©={0}
import.upload.file.select.labeltext=SÃ©lectionner le fichier Ã  tÃ©lÃ©charger
import.upload.select.file.help=SÃ©lectionner un fichier de mouvements contenant les informations Ã  tÃ©lÃ©charger dans le systÃ¨me
import.upload.csv.button=TÃ©lÃ©charger le fichier
import.upload.workspace.heading=TÃ©lÃ©chargement fichier et importation donnÃ©es
link.file.import=TÃ©lÃ©charger et importer les fichiers
import.upload.filetype.error=Type de fichier non valide. Contacter lâÃ©quipe Support.
import.upload.file.none=Aucun fichier n'a Ã©tÃ© sÃ©lectionnÃ© pour tÃ©lÃ©chargement
import.upload.file.error=Erreur lors du tÃ©lÃ©chargement du fichier. Contacter lâÃ©quipe Support.
import.selected.items.non=Aucun Ã©lÃ©ment nâa Ã©tÃ© sÃ©lectionnÃ© pour importation.
import.upload.uploaded.files.title=Fichiers tÃ©lÃ©chargÃ©s pour importation des donnÃ©es
import.upload.file.name.label=Nom du ficher
import.upload.num.items.label=ÃlÃ©ments num
import.upload.startdate.label=DÃ©but du tÃ©lÃ©chargement
import.upload.enddate.label=Fin du tÃ©lÃ©chargement
import.upload.last.imported.by.label=DerniÃ¨re importation par
import.upload.detail=DÃ©tail
import.upload.open.label=Ouvrir

import.file.items.header=ÃlÃ©ments du fichier
button.import.selected=Importation sÃ©lectionnÃ©e
button.import.all=Importer tout
import.items.title=ÃlÃ©ments Ã  importer
import.select.label=SÃ©lectionner
import.upload.successful.label=TÃ©lÃ©chargement effectuÃ©
import.upload.date.label=Date dâimportation
import.num.attempts.label=Importations num
import.last.successful.label=Importation effectuÃ©e
import.meter.label=Compteur
import.up.label=Installation
import.agrref.label=Contrat
import.comment.label=Commentaire
import.itemdata.label=DonnÃ©es
import.upload.username.label=TÃ©lÃ©charger utilisateur
import.last.start.label=DÃ©but derniÃ¨re importation
import.last.end.label=Fin derniÃ¨re importation
import.items.file.detail.header=DÃ©tails du fichier
import.items.edit.header=Afficher/modifier Ã©lÃ©ments du fichier
import.cancel.edit.item.confirm=En cas dâannulation, les modifications apportÃ©es ci-dessus seront abandonnÃ©es. ContinuerÂ ?
import.edit.item.update.success=Les donnÃ©es du client {0}, compteur {1} ont Ã©tÃ© mises Ã  jour.
import.edit.item.update.non=Les donnÃ©es nâont pas changÃ©. Aucune mise Ã  jour nâest nÃ©cessaire.

# thomasn: 2019-02-18 | Planio 6223
customer.idnumber.help=Saisir le numÃ©ro PiÃ¨ce d'identitÃ© du client.
customer.idnumber=NÂº PiÃ¨ce d'identitÃ©
error.field.idnumber.max=Le numÃ©ro ID doit comporter moins de {max} caractÃ¨res.
customer.idnumber.column=NumÃ©ro ID
search.customer.idnumber=NumÃ©ro ID

#  renciac: 2019-01-28 | Planio 6425
# Removed: usagepoint.deactivate.info.message=Ce point d'utilisationÂ a Ã©tÃ© dÃ©sactivÃ© par {0} le {1} pour le motif suivantÂ : <br/> {2}
usagepoint.deactivate.info.message=Ce point d'utilisationÂ a Ã©tÃ© dÃ©sactivÃ© par {0} le {1} 
usagepoint.deactivate.info.message.reason= pour le motif suivantÂ : <br/> {0}

# rfowler: 2019-02-08 | Planio 6141
search.customer.phone1.number=NÂ° de tÃ©lÃ©phone 1
search.customer.phone2.number=NÂ° de tÃ©lÃ©phone 2
search.customer.phone.number=NÂ° de tÃ©lÃ©phone
search.customer.custom.textfield1=Champ personnalisÃ© 1

search.location.header=Recherche de localisation
search.location.erf.number=NumÃ©ro Erf
search.location.building.name=Nom du bÃ¢timent
search.location.suite.number=NumÃ©ro de la suite
search.location.address1=Ligne dâadresse 1
search.location.address2=Ligne dâadresse 2
search.location.address3=Ligne dâadresse 3
search.location.type=Type de recherche de localisation
search.location.type.label=Localisation client/point d'utilisation
search.location.type.customer=Localisation client
search.location.type.usagepoint=Localisation point d'utilisation

# joelc: 2018-01-10 | planio-6324:Samoa - Reason for reactivation of usage point
error.field.reasonname.range=Le nom du motif est obligatoire.
error.field.reasontext.range=Le texte du motif est obligatoire.
specialaction.auto.deactivate.usagepoint=DÃ©sactivÃ© car le point d'utilisation n'est plus complet (client ou compteur manquant) 
specialaction.auto.activate.usagepoint=ActivÃ© aprÃ¨s avoir complÃ©tÃ© les donnÃ©es de point d'utilisationÂ requises.

#  zachv: 2019-01-02 | Planio 5936
# Removed: ndp.schedule.abandon.activation.change=L'activation du programme a changÃ©. Si vous voulez conserver le paramÃ¨tre, sÃ©lectionner Non et enregistrer/mettre Ã  jour d'abord. Abandonner le paramÃ¨treÂ ?
question.close.tabs.dirty=Tous les onglets seront fermÃ©s, mais certaines modifications ne seront pas sauvegardÃ©es. Voulez-vous abandonner ces modificationsÂ ? 

# joelc: 2018-12-12 | planio-6324:Samoa - Reason for reactivation of usage point
usagepoint.activate.enter.reason=Saisir un motif pour cette activation
usagepoint.activate.select.reason=SÃ©lectionner un motif pour cette activation
special.action.reason.error.save.duplicate=Ce motif a dÃ©jÃ  Ã©tÃ© ajoutÃ©.

# renciac: 2018-12-06 | planio-6282
error.field.value.boolean=La valeur doit indiquer vrai ou faux
error.field.value.location.level=Le type du groupe de localisation nâest PAS obligatoire. Ce paramÃ¨tre ne peut pas Ãªtre dÃ©fini comme vrai.

# thomasn: 2018-11-29 | planio-5296
auxaccount.upload.invalid.duplicate=Dupliquer CompteAux (Nomcompte & RÃ©fcontrat), cette combinaison existe dÃ©jÃ Â !.

#RobertF 2018-11-21 Planio-6142 : [MMA] CoCT - Transaction History Bar Graph
transaction.history.graph.title=Historique des transactions
transaction.history.graph.description=Transactions par mois pour les 12 derniers mois.
transaction.history.graph.xaxis.label= Mois
transaction.history.graph.yaxis.label= Nombre de transactions
transaction.history.graph.series.label= Compte de transactions

# thomasn: 2018-11-12 | planio-6168
customer.title.mr=Mr.
customer.title.mrs=Mme.
customer.title.ms=Melle
customer.title.miss=Mademoiselle
customer.title.doc=Dr.
customer.title.prof=Prof.
customer.title.sir=Monsieur

customer.email=Adresse mail

# joelc: 2018-10-29 | planio-6105: usage point meter download and print for Mayotte
print.customer.contract=TÃ©lÃ©charger contrat client
print.customer.contract.auxtype=Type aux
print.customer.contract.auxname=Nom du compte
print.customer.contract.principleamount=Montant principal
print.customer.contract.balance=Solde
print.customer.contract.status=Statut
print.customer.contract.signature=Signature client
print.customer.contract.signature.date=Date 

# thomasn: 2018-10-17 | Planio 5296
auxaccount.upload.balanceType=Type de solde
auxaccount.upload.invalid.balance.amount=Le montant du solde doit Ãªtre positif
auxaccount.upload.invalid.balancetype=Type solde non valide doit correspondre Ã  dette/remboursement.

# robertf: 2018-10-09 | Planio 5955
meter.txn.user.ref=RÃ©f. utilisateur
engineering.token.user.reference.txtbx.label=RÃ©fÃ©rence utilisateur
engineering.token.user.reference.txtbx.label.help=Saisir la rÃ©fÃ©rence de l'utilisateur pour le problÃ¨me d'ingÃ©nierie du code compteur.

# Patrickm: 2018-11-09 | planio-6192: GIS data upload for BVM
link.metadata.upload=TÃ©lÃ©charger les mÃ©tadonnÃ©es
metadata.upload.heading=TÃ©lÃ©chargement des mÃ©tadonnÃ©es
metadata.upload.data.title=Importer les mÃ©tadonnÃ©es
metadata.upload.description=SÃ©lectionner le fichier JSON contenant les mÃ©tadonnÃ©es Ã  importer dans le systÃ¨me de gestion des compteurs.
metadata.upload.error.object.creation=Erreur lors de la crÃ©ation de l'objet {0}. Contacter lâÃ©quipe SupportÂ !
metadata.upload.select.file.help=SÃ©lectionner un fichier contenant les mÃ©tadonnÃ©es au format json spÃ©cifiÃ© pour les importer dans le systÃ¨me.
metadata.upload.button=TÃ©lÃ©charger les mÃ©tadonnÃ©es
metadata.lat.label=Latitude
metadata.lon.label=Longitude
metadata.lon.help=MÃ©tadonnÃ©es de longitude pour ce groupe UP
metadata.lat.help=MÃ©tadonnÃ©es de latitude pour ce groupe UP
metadata.gis.saved=Informations GIS du groupe enregistrÃ©esÂ : {0}
metadata.gis.error.invalid.lat=Valeur de latitude non valide. CoordonnÃ©es hors de la plage des coordonnÃ©es GIS valides.
metadata.gis.error.invalid.lon=Valeur de latitude non valide. CoordonnÃ©es hors de la plage des coordonnÃ©es GIS valides.

# Patrick: 2017-12-14 | planio-5041 : Add configurable switch to device stores to determine the response of meters in the store to polling requests
devicestore.field.store_vendors_meter=Le magasin d'appareils conserve les compteurs qui ont Ã©tÃ© transfÃ©rÃ©s Ã  un autre fournisseur.
devicestore.field.store_vendors_meter_help=Indique si le magasin d'appareils conserve les compteurs qui ont Ã©tÃ© transfÃ©rÃ©s Ã  un autre fournisseur.
devicestore.field.store_vendors_meter_help2=Les champs '{0}' et '{1}' s'excluent mutuellement. Par consÃ©quent, vous ne pouvez pas avoir un message personnalisÃ© et cocher la case
devicestore.field.custom_message=Message de rÃ©ponse personnalisÃ© du magasin dâappareils
devicestore.field.custom_message_help=Le message Ã  afficher aux utilisateurs lorsqu'ils recherchent des compteurs stockÃ©s dans ce magasin dâappareils.
devicestore.meters.save.dialog=Ce compteur sera sauvegardÃ© dans le compteur '{0}'. Veuillez confirmer cette opÃ©ration.
devicestore.meters.move.dialog=Ce compteur sera dÃ©placÃ© dans le compteur '{0}'. Voulez-vous rÃ©aliser cette opÃ©rationÂ ?
devicestore.meters.fetch.dialog=Vous rÃ©cupÃ©rez un compteur chez un autre fournisseur. Voulez-vous poursuivre cette opÃ©rationÂ ?

# thomasn: 2018-10-23 | planio-5956 [MMA] Add an alphanumeric SAP reference number when removing or replacing a meter on a usage point and display it in the history tables
usagepoint.hist.reason=Retrait de compteur/moteur de rÃ©affectation

# robertf: 2018-10-01 | Planio 5954
meter.txn.powerlimits=Limite de puissance
meter.powerlimit.units.w=Limite de puissance (W)

# Renciac: 2018-09-04 | planio-5466 : Add manual reversal
button.vend.reversal=Annuler vente
vend.reversal.confirm=Confirmer annulation vente/rechargementÂ ?
vend.reversal.connection.error=Aucune rÃ©ponse reÃ§ue du service. Veuillez actualiser la page.
vend.reversal.error=Erreur vente/rechargementÂ : {0}
vend.reversal.fail=Seule la derniÃ¨re vente peut Ãªtre annulÃ©e.
vend.reversal.success=Annulation de la vente effectuÃ©e. RÃ©f origine= {0}, RÃ©f annulation={1}
vend.trans.already.reprinted=Cette transaction a Ã©tÃ© rÃ©imprimÃ©e {0} fois. RÃ©imprimÃ©e pour la premiÃ¨re fois le {1} par {2}. \n
vend.reprint.null.token=Le code compteur est nul. RÃ©impression impossible. 
vend.reversed=La vente a Ã©tÃ© annulÃ©e.
meter.txn.reprint.date=DerniÃ¨re rÃ©impression
meter.noshow.token=RÃ©imprimer pour code compteur 
vend.reprint.user.not.known=Inconnu

# thomasn: 2018-09-04 | Planio 5476
readings.table.receiptnum=NumÃ©ro de reÃ§u

# thomasn: 2018-09-03 | Planio 5296
customer.auxaccount.amount.pos=Un solde montant indique un REMBOURSEMENT
customer.auxaccount.amount.neg=Un montant nÃ©gatif indique une DETTE
customer.auxaccount.title=Compte auxiliaire
customer.auxaccount.balance.type.debt=Dette
customer.auxaccount.balance.type.refund=Remboursement
customer.auxaccount.balance.type.error.required=Vous devez en sÃ©lectionner un.
customer.auxaccount.error.balance=Le solde ne peut Ãªtre nÃ©gatif

# Thomas: 2018-08-29 | planio-5475 Time of use calendar allows 00:00 as end time for day profile but marks as incomplete
calendar.assign.period.end.maximum=La valeur de fin de journÃ©e est 23:59
calendar.assign.period.end.help=L'heure Ã  laquelle la pÃ©riode se termine. La valeur de fin de journÃ©e est 23:59

# Renciac: 2018-07-26 | planio-5451 Enabling STS 6
meter.token.code3=Code compteur 3
meter.token.code4=Code compteur 4
base.date.label=Date de baseÂ : 
base.date.label.help=Date de base utilisÃ©e pour la gÃ©nÃ©ration des codes compteur STS6 pour ce compteur.
meter.three.tokens=Trois codes compteur de changement de clÃ© sont nÃ©cessaires
meter.three.tokens.help=Pour le code d'algorithme STS = 07, certains compteurs peuvent stocker les informations STS sur le compteur et nÃ©cessitent un troisiÃ¨me code compteur ModificationclÃ© pour fournir les informations.
meter.three.tokens.error=Le paramÃ¨tre Trois codes compteur est uniquement applicable au code d'algorithme STS = 07 
meter.clear.tid=RÃ©initialiser TID 

# zachv: 2018-08-22
tariff.field.samoa.debt_charge=Charge de dette
tariff.field.samoa.debt_charge.help=FacturÃ© par unitÃ©
tariff.field.samoa.energy_charge=Frais dâÃ©nergie
tariff.field.samoa.energy_charge.help=FacturÃ© par unitÃ©

# robertf: 2018-07-27: #5347 Community Group used feature indicator
usagepointgroups.indicator.thresholds.tooltip = Le groupe a des seuils de compte client personnalisÃ©s
usagepointgroups.indicator.ndp.tooltip = Le groupe dispose d'un calendrier NDP personnalisÃ©

# zachv: 2018-06-26
tariff.field.kenya.monthly = Frais mensuels fixes
tariff.field.kenya.monthly.help = Frais fixes appliquÃ©s par mois.
tariff.field.kenya.fuel = Frais de carburant
tariff.field.kenya.fuel.help = Tarif variable par kWh, publiÃ© mensuellement par KPLC. La TVA sâajoute ces frais.
tariff.field.kenya.forex = Taux de change
tariff.field.kenya.forex.help = Ajustement pour fluctuation du taux de change (FERFA). Tarif variable par kWh, publiÃ© mensuellement par KPLC. 
tariff.field.kenya.inflation = Ajustement pour inflation
tariff.field.kenya.inflation.help = Tarif variable par kWh, publiÃ© mensuellement par KPLC.
tariff.field.kenya.erc = PrÃ©lÃ¨vement ERC
tariff.field.kenya.erc.help = Taux par kWh.
tariff.field.kenya.rep = PrÃ©lÃ¨vement REP
tariff.field.kenya.rep.help = Pourcentage du taux de base
tariff.field.kenya.warma = PrÃ©lÃ¨vement WARMA
tariff.field.kenya.warma.help = Tarif variable par kWh, publiÃ© mensuellement par KPLC.

# RobertF 4th July 2018. Planio 5714
bulk.upload.enc.key= ClÃ© de chiffrement
meter.enc.key.error=Le modÃ¨le de compteur requiert une clÃ© de chiffrement de compteur.

#RobertF June 15, 2017 Planio-5787 : Panel with the buying index chart
dashboard.buying.index.graph.title=Indice dâachat
dashboard.buying.index.graph.month.description=Indice dâachatÂ : Compteurs en service/compteurs actifs (%)
dashboard.buying.index.graph.xaxis.label=Mois
dashboard.buying.index.graph.yaxis.label=Indice dâachat

# zachv: 2018-05-04
meter.models.field.needs.encryption.key = NÃ©cessite une clÃ© de chiffrement
meter.models.field.needs.encryption.key.help = Indique si une clÃ© de chiffrement doit Ãªtre saisie pour ce modÃ¨le de compteur.
meter.encryptionkey=ClÃ© de chiffrement
meter.encryptionkey.help=ClÃ© de chiffrement pour ce compteur, par exemple pour dÃ©chiffrer les donnÃ©es reÃ§ues d'une collecteur de donnÃ©es de compteur
meter.encryptionkey.error=Le modÃ¨le de compteur requiert une clÃ© de chiffrement de compteur.
meter.metermodelchange.remove_fields.question=En cas de changement de modÃ¨le de compteur, les champs suivants seront effacÃ©sÂ : {0}. ContinuerÂ ? 
meter.model.unset.encryption.key.error=Impossible de modifier l'exigence relative Ã  la clÃ© de chiffrementÂ âÂ il existe dÃ©jÃ  des compteurs avec des clÃ©s de chiffrement utilisant ce modÃ¨le de compteur.

# Thomas: 2018-05-10 | planio-5502 : MDC messages Override Button needs info message
mdc.txn.override.help=Un message de contournement sera envoyÃ© en tant que message prioritaire. Il passera avant les autres messages en attente sur le compteur et certains contrÃ´les de validation ne seront pas appliquÃ©s, comme la validation des pÃ©riodes de non-dÃ©connexion et l'ordre des messages.

# Thomas: 2018-04-16 | planio-5495 : OpenWayTransaltor, send text message to meter display
meter.models.field.message.display=Prend en charge lâaffichage des messages;
meter.models.field.message.display.help=Indique si le modÃ¨le de compteur prend en charge lâaffichage des messages sur le compteur. Par exemple, message de solde bas 

# zachv: 2018-04-09 | planio-5512 : MeterMng to support reading multiplier for mdc channel
channel.field.reading_multiplier=Multiplicateur relevÃ©
channel.field.reading_multiplier.help=Multiplicateur appliquÃ© au relevÃ© pour le standardiser dans le type de donnÃ©es adÃ©quat. Par exemple, un compteur d'eau peut Ã©mettre des relevÃ©s en impulsions nÃ©cessitant un multiplicateur (tel que 0,5 impulsion par litre). Non pris en charge par tous les composants mdc

# Patrickm: 2018-03-22 | planio-5438 : Display meter location on Map
meter.location=Localisation compteur
usagepoint.location=Localisation point d'utilisation

# Renciac: 2018-02-28 | Planio 5380 : Non_billable meters
customer.agreement.billable.help=RÃ©gler sur Faux (non cochÃ©) pour les contrats client relatifs aux compteurs intelligents destinÃ©s Ã  mesurer l'utilisation uniquement, et non pas destinÃ©s Ã  l'achat. Les relevÃ©s de ces compteurs sont tÃ©lÃ©chargÃ©s et utilisÃ©s pour les graphiques d'utilisation sur, par exemple, le site Web EnergyInsight.  
customer.agreement.billable=Facturable
customer.agreement.billable.setting.check=Vous avez paramÃ©trÃ© la valeur facturable du contrat client sur {0}. Veuillez confirmer la valeur. Si cette valeur est rÃ©glÃ©e sur Vrai, ce compte peut Ãªtre rechargÃ©Â ; si elle est rÃ©glÃ©e sur Faux, il sert purement Ã  mesurer la consommation.
bulk.upload.billable=Facturable
error.field.upload.billable.invalid=Lâoption Facturable peut uniquement Ãªtre Vrai ou Faux.  

# Renciac: 2018-01-24 | planio-5210 : PayTypeDiscounts & Add vat inclusive / exclusive to help messages
tariff.field.percent_charge.help=Frais en pourcentage facultatifs qui seraient retirÃ©s du montant offert comme premiÃ¨re Ã©tape du calcul tarifaire. (TTC).
tariff.field.discount.help=Remise Ã  appliquer par type de paiement. (TTC). Laisser vide si aucune remise n'est applicable pour un type de paiement.
tariff.field.discount.blockThin.help=Remise Ã  appliquer par type de paiement. (HT). Laisser vide si aucune remise n'est applicable pour un type de paiement.
tariff.field.unitprice.help=Prix par kWh (HT).
tariff.field.block.help=Indiquer le prix unitaire (hors taxes) et le seuil de huit blocs au maximum ci-dessous. Laisser les champs Prix unitaire et Seuil vides pour les blocs non utilisÃ©s.
tariff.field.unitprice.namibia.help=Prix par kWH (hors impÃ´t & HT)
tou.thin.field.monthlydemand.help=PrÃ©ciser le montant des frais de la demande mensuelle. (HT).
tou.thin.field.servicecharge.help=Indiquer le montant du frais de service. (HT).
tou.thin.field.charges.help=Saisir un taux de tarif pour chacune des saisons disponibles, la pÃ©riode et les combinaisons de type de lecture. (HT).
tou.thin.field.charges.specialday.help=Saisir un taux de tarif pour chacun des jours spÃ©ciaux ci-dessous. (HT).
register.reading.rates.help=Collecter un tarif pour chacun des facteurs de facturation sÃ©lectionnÃ©s. (HT).

# rfowler : 2018-01-23 : Planio 4756
bulk.upload.file.no.meterupdata=Aucun compteur/point d'utilisationÂ prÃ©sent dans le fichier de tÃ©lÃ©chargement csv.

# Patrick: 2017-11-27 | planio-5127 : Capture and display power limit of a meter
meter.power_limit.instructions=Pour codes compteur de limite de puissanceÂ :\n1. Ouvrir le panneau du compteur.\n2. Dans le bloc d'informations Limite de puissance, sÃ©lectionner la limite de puissance dans la boÃ®te Ã  suggestions Limite de puissance.\n3. Cela permet dâajouter une nouvelle boÃ®te de liste avec des options de limite de puissanceÂ âÂ sÃ©lectionner ce dont vous avez besoin.\n4. Lors de l'enregistrement du compteur, une fenÃªtre contextuelle s'affiche pour permettre la saisie de dÃ©tails supplÃ©mentaires.
meter.power_limit.container_label=Informations sur la limite de puissance
meter.power_limit.token.generate=GÃ©nÃ©rer des codes compteur de limite de puissanceÂ ?
meter.power_limit.token.generate.help=Si le compteur doit Ãªtre mis Ã  jour pour correspondre aux nouveaux dÃ©tails de la limite de puissance, un code compteur de limite de puissance doit Ãªtre gÃ©nÃ©rÃ©. Si l'enregistrement est mis Ã  jour pour correspondre aux dÃ©tails du compteur, il n'est pas nÃ©cessaire de gÃ©nÃ©rer des codes compteur.
tokens.power_limit.no_gen=Ne pas gÃ©nÃ©rer de code compteur de limite de puissance
tokens.power_limit.gen=GÃ©nÃ©rer des codes compteur de limite de puissance

# joelc 11 January 2018, Planio 4630
grouptree.search=Rechercher

# joelc 3 January 2018, Planio 4627
error.group.contains.usagepoint=Le groupe contient actuellement des points d'utilisation et ne peut pas Ãªtre supprimÃ©. 

# joelc 13 December 2017, Planio 4631
group.error.name.nonunique=Le nom du groupe doit Ãªtre unique.

# 2017-11-20 | planio-5134 : Supplier Group Code validation issue
error.field.supplygroupcode.size=Le code doit comporter {0} chiffres.

# RobertF 23 October 2017, Planio 4755
bulk.upload.gencsvtemplate.title=GÃ©nÃ©rer un modÃ¨le de tÃ©lÃ©chargement
bulk.upload.gencsvtemplate.subtitle= Des champs obligatoires ont Ã©tÃ© sÃ©lectionnÃ©s et ne peuvent pas Ãªtre modifiÃ©s.
bulk.upload.file.button.gentemplate=GÃ©nÃ©rer un modÃ¨le
bulk.upload.template.required.first=InfoÂ : Obligatoire
bulk.upload.template.required=Obligatoire
bulk.upload.template.sts.required=Obligatoire pour les compteurs STS
bulk.upload.recordstatus=Actif (vierge/non)
bulk.upload.installationdate.format=Date dâinstallation (dd-MM-aaaa HH:mm:ss)
bulk.upload.file.button.gentemplate.description=SÃ©lectionner les champs dont vous avez besoin et gÃ©nÃ©rer votre modÃ¨le de tÃ©lÃ©chargementÂ :

# joelc 13 November 2017, Planio 4636
meter.use.existing.instructions=Copier les groupes depuis le compteur existant
meter.not.in.groups=Le compteur nâa Ã©tÃ© affectÃ© Ã  aucun groupe
meter.copy.selected.groups=Copier les groupes sÃ©lectionnÃ©s sur l'Ã©cran principal

# Rencia 29 August 2017, Planio 4928
meter.attached.to.up=Le compteur {0} est liÃ© au point d'utilisationÂ {1}

# Thomas 7th August 2017. Planio 4815
tariff.field.minvendamount.lbl=Montant min. de vente
tariff.field.minvendamount.help=Le montant minimum de vente DOIT Ãªtre indiquÃ© si le tarif permet la vente de moins d'une unitÃ©. Cela permet dâÃ©viter qu'un contrÃ´le soit effectuÃ© pour vÃ©rifier que la vente porte sur au moins UNE unitÃ© entiÃ¨re.

# Patrick | July 19, 2017 | planio-4648: When save new Aux acc, confirm status
auxaccount.checkbox.active.status=Votre compte auxiliaire nâest pas actif par dÃ©faut. Voulez-vous activer ce compteÂ ?

# Thomas 18th July 2017 Planio 4353 MDCTrans Override UI
mdc.txn.override.lbl=Contournement
mdc.txn.override.none=Aucun(e)
mdc.txn.override.all=Tout
mdc.txn.override=Contournement

#Thomas 17th July 2017 Date validation Planio-4644
error.field.datetime.invalid=Format de date non valide. Format attendu ({0})

#Rencia 24 May 2017 Improve Cell phone validation
customer.trans.upload.invalid.up.no.meter=Aucun compteur nâest liÃ© au point d'utilisation
customer.trans.upload.invalid.up.not.active=Le point d'utilisationÂ nâest pas actif

#RobertF July 14, 2017 Planio-4421 : Meter Management: Panel to monitor vending activity
dashboard.vending.activity.graph.title=ActivitÃ© de vente
dashboard.vending.activity.graph.description=Total des ventes au cours du dernier intervalle de 15 minutes
dashboard.vending.activity.graph.xaxis.label= Ventes
dashboard.vending.activity.graph.yaxis.label= Intervalles de 15 minutes (cliquer pour rÃ©initialiser le zoom)

### July 13, 2017 : RobertF : Indicator Dashboard Panel ###
dashboard.key.indicator.indicator.total.sales=Ventes Totales
dashboard.key.indicator.indicator.tooltip.total.sales=Ventes Totales
dashboard.key.indicator.indicator.transactions=Transactions
dashboard.key.indicator.indicator.tooltip.transactions=Montant des transactions qui se produisent pour donner des ventes totales
dashboard.key.indicator.indicator.transacting.meters=Transactions des compteurs
dashboard.key.indicator.indicator.tooltip.transacting.meters=Nombre de compteurs effectuÃ©s pour donner des ventes totales
dashboard.key.indicator.indicator.new.meters.installed=Nouveaux compteurs installÃ©s
dashboard.key.indicator.indicator.tooltip.new.meters.installed=Nouveaux compteurs qui ont Ã©tÃ© assignÃ©s Ã  des points d'utilisation
dashboard.key.indicator.indicator.new.active.meters.installed=Nouveaux compteurs ACTIFS installÃ©s
dashboard.key.indicator.indicator.tooltip.new.active.meters.installed=Nouveaux compteurs associÃ©s aux points d'utilisation et actifs
dashboard.key.indicator.indicator.total.meters.usage.points=Compteurs Total (Points d'utilisation)
dashboard.key.indicator.indicator.tooltip.total.meters.usage.points=Nombre total de compteurs attribuÃ©s aux points d'utilisation
dashboard.key.indicator.indicator.total.active.meters.usage.points=Compteurs ACTIFS totaux (Points d'utilisation)
dashboard.key.indicator.indicator.tooltip.total.active.meters.usage.points=Nombre total de compteurs assignÃ©s aux points d'utilisation et actifs
dashboard.key.indicator.indicator.total.meters.device.store=Nombre TOTAL de compteurs (Magasin central)
dashboard.key.indicator.indicator.tooltip.total.meters.device.store=Nombre total de compteurs dans le magasin d'appareils

#RobertF June 22, 2017 Planio-4420 : Panel with the sales per resource chart
dashboard.sales.per.resource.graph.title=Ventes par ressource
dashboard.sales.per.resource.graph.day.description=Ventes de ressources par jour
dashboard.sales.per.resource.graph.month.description=Ventes de ressources par mois
dashboard.sales.per.resource.graph.xaxis.day.label=Date (cliquer sur le graphique pour afficher le mois)
dashboard.sales.per.resource.graph.xaxis.month.label=Date (cliquer sur le graphique pour afficher le jour)
dashboard.sales.per.resource.graph.yaxis.label=Ventes totales

# Thomas 21 June 2017 UP Blocking
usagepoint.field.blocking.help=SÃ©lectionner le type de bloc pour le point d'utilisation.
usagepoint.field.blocking.label=Type de blocage
usagepoint.field.blocking.type.default=Non mis en bloc
usagepoint.blocking.enter.reason=Saisir un motif de blocage
usagepoint.blocking.select.reason=SÃ©lectionner un motif de blocage

#RobertF June 2, 2017 Planio-4419 : Panel with the count regarding the Owner and Building usage point groups added over time
dashboard.groups.added.graph.title=Groupes de points d'utilisation ajoutÃ©s
dashboard.groups.added.graph.day.description=Groupes ajoutÃ©s par jour
dashboard.groups.added.graph.month.description=Groupes ajoutÃ©s par mois
dashboard.groups.added.graph.xaxis.day.label=Date (Cliquez sur le graphique pour voir le mois)
dashboard.groups.added.graph.xaxis.month.label=Date (Cliquez sur le graphique pour voir le jour)
dashboard.groups.added.graph.yaxis.label=Groupes ajoutÃ©s

# Joel 30 May 2017  Centian Data display planio 4429
meter.centian.header=Information sur le compteur Centian
meter.centian.kwh.credit.remaining=kWh CrÃ©dit restant:
meter.centian.currency.credit.remaining=UnitÃ© monÃ©taire de crÃ©dit restant:
meter.centian.number.disconnections=Le nombre de dÃ©connexions:
meter.centian.tamper.detected=Les Ã©tats de sabotage dÃ©tectÃ©s:
meter.centian.tamper.none=Aucun Ã©tat de sabotage n'a Ã©tÃ© dÃ©tectÃ©
meter.centian.tamper.updated=Date d'info rÃ©cupÃ©rÃ©e: 
meter.centian.tamper.overpower=Dessus du Puissance
meter.centian.tamper.overvoltage=Surtension
meter.centian.tamper.lowvoltage=Tension basse
meter.centian.tamper.overfrequency=Sur frÃ©quence
meter.centian.tamper.lowfrequency=FrÃ©quence Basse
meter.centian.tamper.reverseenergy=Inverser l'Ã©nergie
meter.centian.tamper.opencover=Couverture ouverte
meter.centian.tamper.magnettamper=DÃ©tection de sabotage magnÃ©tique
meter.centian.tamper.bypassearth=Contournement/Sabotage Terrestre 
meter.centian.tamper.sequenceerror=Erreur de sÃ©quence
meter.centian.tamper.overtemperature=TempÃ©rature excessive
meter.centian.tamper.lowtemperature=TempÃ©rature Basse
meter.centian.tamper.phaseunbalance=DÃ©sÃ©quilibre de phase
meter.centian.tamper.phasevoltageloss=Perte de tension de phase
meter.centian.tamper.tariffconfigerror=Erreur de configuration tarifaire
meter.centian.tamper.metrologyfail=Ãchec de la mÃ©trologie

meter.centian.current.tamper.status.header=Ãtat de sabotage pour le compteur
meter.centian.current.tamper.status.description=Des Ã©tats de sabotage avec une tique ont Ã©tÃ© dÃ©tectÃ©s sur ce compteur.
meter.centian.current.tamper.status.description.none=Aucun Ã©tat de sabotage n'a Ã©tÃ© dÃ©tectÃ© sur ce compteur.
meter.centian.current.tamper.status.updated=Ãtat de sabotage mis Ã  jour:

#Rencia 24 May 2017 Improve Cell phone validation
meter.online.bulk.customer.phone.help=Entrez le numÃ©ro de tÃ©lÃ©phone du client auquel un jeton d'Ã©mission gratuit sera envoyÃ© par SMS, le cas Ã©chÃ©ant. Les numÃ©ros SMS doivent Ãªtre en format international, en commenÃ§ant par un +.
messaging.recipient.help=Pour les SMS, doit utiliser les numÃ©ros de tÃ©lÃ©phone internationaux.
cellPhone.pattern.description=Le champ de numÃ©ro de tÃ©lÃ©phone pour sms doit Ãªtre en format international, Ã  commencer par un +. Vous pouvez utiliser des espaces blancs, des parenthÃ¨ses (), des hypensiques et des pÃ©riodes - ceux-ci sont Ã©liminÃ©s et le numÃ©ro de tÃ©lÃ©phone rÃ©sultant (sauf le +) doit comporter au moins 4 chiffres, maximum 25 de longueur, selon vos paramÃ¨tres rÃ©gionaux. Renvoyez l'espace rÃ©servÃ©.

### May 17, 2017 : RobertF : Admin dashboard workspace ###
admin.dashboard.title=Tableau de bord (Admin)

# Patrick | May 15, 2017 | planio-4443 - Updates for help message from field behaviour change
meter.algorithmcode.help=SÃ©lectionnez le code d'algorithme correct. Ce champ est requis pour l'activation - le compteur ne peut pas Ãªtre sauvegardÃ© Ã  moins qu'un code d'algorithme ne soit sÃ©lectionnÃ©.
meter.tokentechcode.help=SÃ©lectionnez le jeton de code tech correct. Ce champ est requis pour l'activation - le compteur ne peut pas Ãªtre sauvegardÃ© Ã  moins qu'un jeton de code tech soit sÃ©lectionnÃ©.
meter.supplygroupcode.help=Entrez le code de groupe d'approvisionnement actuel. Ce champ est requis pour l'activation - l'enregistrement ne peut pas Ãªtre enregistrÃ© sans lui.
meter.tariffindex.help=Entrez l'index tarifaire actuel. Ce champ est requis pour l'activation - le dossier ne peut pas Ãªtre enregistrÃ© sans lui.

#Thomas 8th May 2017 Tariff Date validation
tariff.error.startdate.unique=Date de dÃ©but reproduite. SpÃ©cifiez une date de dÃ©but unique.
tariff.error.startdate=La date de dÃ©but doit Ãªtre Ã  l'avenir et supÃ©rieure Ã  la date de dÃ©but du tarif actuellement actif.

### May 2, 2017 : RobertF : Indicator Dashboard Panel ###
dashboard.key.indicator.title=Indicateurs clef
dashboard.key.indicator.description=Indicateurs clÃ©s du systÃ¨me au cours de divers dÃ©lais.
dashboard.key.indicator.indicator=Indicateur
dashboard.key.indicator.value.today=Aujourd'hui
dashboard.key.indicator.value.monthtodate=Le mois courant
dashboard.key.indicator.value.lastmonth=Le mois dernier

#Rencia 24 April 2017 Meter Bulk Online Search / Capture : Free Issue Token
meter.online.bulk.free.issue.title=Jeton d'Ã©mission gratuit
meter.online.bulk.free.issue.generate=GÃ©nÃ©rer un jeton
meter.online.bulk.free.issue.sms.token=Jeton SMS au locataire
meter.online.bulk.free.issue.sms.token.help=SÃ©lectionnez ici si vous souhaitez que le jeton d'Ã©mission gratuit soit envoyÃ© au locataire?
meter.online.bulk.free.issue.check.sms.not.selected=Vous avez choisi de gÃ©nÃ©rer un jeton d'Ã©mission gratuit, mais PAS de l'envoyer par SMS.
meter.online.bulk.free.issue.sms.invalid.phone=Pour envoyer un jeton par SMS, doit avoir un numÃ©ro de tÃ©lÃ©phone portable valide 
meter.online.bulk.free.issue.invalid.units=Le nombre d'unitÃ©s de jeton d'Ã©mission gratuit doit Ãªtre numÃ©rique et supÃ©rieur Ã  zÃ©ro 
meter.online.bulk.free.issue.sms=Jeton d'Ã©mission gratuit {2}: NumÃ©ro de compteur: {0}  Jeton: {1}
meter.online.bulk.free.issue.token.null=Compteur a Ã©tÃ© ajoutÃ© au groupe mais Impossible de gÃ©nÃ©rer le jeton d'Ã©mission gratuit.
meter.online.bulk.free.issue.token.error=Compteur a Ã©tÃ© ajoutÃ© au groupe mais erreur de jeton: {0}
credit.token.link=Afficher les jetons de crÃ©dit
eng.token.link=Afficher les jetons d'ingÃ©nierie


#Rencia 21 April 2017 Meter Bulk Online Search / Capture : Edit / Remove buttons in table
meter.online.bulk.no.edit=Seuls certains champs sur les points d'utilisation ACTIF peuvent Ãªtre modifiÃ©s ici. Utilisez le lien pour accÃ©der Ã  la page de Point D'utilisation pour les autres.
meter.online.bulk.no.remove=Le point d'utilisation n'a pas de compteur Ã  supprimer. Utilisez le lien pour accÃ©der Ã  la page de Point D'utilisation.
button.clear.panel=Effacer Panneau
button.clear.groups=Effacer Groupes
online.bulk.panel.tariffindex.help=Entrez l'index tarifaire actuel. Ce champ est requis pour l'activation. Pour modifier un compteur existant, utilisez la page de Point D'utilisation.
online.bulk.panel.supplygroupcode.help=Entrez le code de groupe d'approvisionnement actuel. Ce champ est requis pour l'activation. Pour modifier un compteur existant, utilisez la page de Point D'utilisation.
error.field.breakerid.max=L'ID du disjoncteur ne doit pas dÃ©passer 100 caractÃ¨res maximum.
meter.online.bulk.meter.updated=Compteur {0} mise Ã  jour

### April 11, 2017 : Patrick : Send Reprint ###
button.send_reprint=Envoyer une rÃ©impression
messaging.type.sms=Sms
messaging.type.email=Email
messaging.recipient=Destinataire
messaging.message.type=Type de message
messaging.message.label=Message
messaging.token.reprint.email.subject=Demande de rÃ©impression de jeton
token.label=Jeton
error.field.required.recipient.email=L'adresse e-mail du destinataire est requise
error.field.required.recipient.phone=Le numÃ©ro de tÃ©lÃ©phone du destinataire est requis
error.field.validity.phone=Le numÃ©ro de tÃ©lÃ©phone est invalide.

notification.message.send.status.sms=Message SMS envoyÃ© avec succÃ¨s
notification.message.send.status.email=Message par email envoyÃ© avec succÃ¨s

messaging.txn.date=Date de la transaction
messaging.txn.meter_no=NumÃ©ro du compteur
messaging.txn.token=Jeton

#Rencia 31 March 2017 Meter Meter Bulk Online Search / Capture : Popup more information when click on meter in table
more.info=Plus D'information - Faites glisser ici
popup.label=Champ
popup.value=Valeur

#Rencia 26 March 2017 Meter Meter Bulk Online Search / Capture : Add pricing Structure Tariff Popup
online.bulk.panel.tariff.title=Tarif actuel
meter.online.bulk.add.group.title=Ajouter / Modifier les groupes

#Robertf 27 March 2017 Usage Point page check for valid installation date
error.field.startdate.invalid=La date de dÃ©but n'est pas une date valide. Format = {0}

#Rencia 26 March 2017 Meter Meter Bulk Online Search / Capture : Add New Group function & phone no
customer.phone=NumÃ©ro de TÃ©lÃ©phone


#Rencia 10 March 2017 Meter Bulk Online Search / Capture add group entry function
grouptype.field.layout.order=Ordre de mise en page
grouptype.field.layout.order.help=C'est l'ordre dans lequel la sÃ©lection des cases de groupe de point d'utilisation sont affichÃ©es sur la page. Si elle n'est pas entrÃ©e, la valeur par dÃ©faut sera APRÃS tous les numÃ©ros, dans l'ordre alphabÃ©tique.
grouptype.field.layout.order.error.numeric=L'ordre de mise en page doit Ãªtre un nombre entier numÃ©rique.
grouptype.field.layout.order.error.duplicate=L'ordre de mise en page est reproduite. Identique au groupe {0}. 
error.field.installdate.invalid=La date d'installation n'est pas une date valide. Format = {0}

#Rencia 27 February 2017 Meter Bulk Online Search / Capture v1
meter.online.bulk.header=Ajouter des compteurs au groupe/s
meter.online.bulk.title=SÃ©lectionnez ou Ajoutez un groupe/s
meter.online.bulk.installdate=Date D'installation
meter.online.bulk.select.meters.button=SÃ©lectionnez MÃ¨tres pour le groupe/s
meter.online.bulk.usagepoint.status=Ãtat
meter.online.bulk.search.no.results=Aucun rÃ©sultat de recherche correspondant n'a Ã©tÃ© trouvÃ©.
meter.online.bulk.add.meters.to.groups=Ajouter des compteurs au groupe/s
meter.online.bulk.button.add=Ajouter le compteur
meter.online.bulk.add.meter=Ajouter le compteur
meter.online.bulk.edit.meter=Modifier le compteur

online.bulk.panel.up.group.info.title=Informations sur le point d'utilisation
online.bulk.panel.customer.info.title=Informations client
online.bulk.panel.meter.help=Commencez Ã  taper le numÃ©ro du compteur, les compteurs qui commencent avec ces chiffres pour le magasin de dispositifs sÃ©lectionnÃ© apparaÃ®tront dans une liste dÃ©roulante. Cliquez sur un pour le sÃ©lectionner.
online.bulk.panel.select.store.help=Le magasin dont les compteurs seront sÃ©lectionnÃ©s. Si aucun n'a Ã©tÃ© sÃ©lectionnÃ©, les numÃ©ros de compteur de tous les magasins seront affichÃ©s dans les suggestions du compteur. Notez que lorsque le compteur est affectÃ© Ã  un point d'utilisation, il sera automatiquement retirÃ© du magasin.
online.bulk.panel.suite.no.text=UnitÃ©
online.bulk.panel.tenant.text=Locataire
online.bulk.panel.surname.help=Peut entrer le nom de famille du locataire. La sÃ©quence numÃ©rique sera par dÃ©faut. Ceci est un champ obligatoire - le point d'utilisation ne peut pas Ãªtre activÃ© sans lui.

online.bulk.panel.error.supply.grpcode.empty=Pour les compteurs STS, le code du groupe d'approvisionnement doit Ãªtre sÃ©lectionnÃ©
online.bulk.panel.error.algorithm.code.empty=Pour les compteurs STS, le code du groupe d'approvisionnement doit Ãªtre sÃ©lectionnÃ©
online.bulk.panel.error.token.tech.code.empty=Pour les compteurs STS, le code du jeton techn doit Ãªtre sÃ©lectionnÃ©
online.bulk.panel.error.tariff.indx.empty=Pour les compteurs STS, l'index tarifaire doit Ãªtre entrÃ© et la longueur maximale 2
online.bulk.panel.error.key.rev.indx.empty=Pour les compteurs STS, le Code de RÃ©vision ClÃ© doit Ãªtre entrÃ© et doit Ãªtre numÃ©rique
online.bulk.panel.error.ps.meter.model.empty=Le modÃ¨le du compteur est requis pour la sÃ©lection de la structure des prix
online.bulk.panel.error.model.new.pricingstructure.required=Le modÃ¨le du compteur ne prend pas en charge la structure des prix.
online.bulk.panel.error.meter.num.not.found=Le numÃ©ro du compteur n'est pas sur la base de donnÃ©es. 
online.bulk.panel.error.meter.already.linked=Le numÃ©ro du compteur est dÃ©jÃ  liÃ© au Point D'utilisation. 
online.bulk.panel.error.meter.linked.to.diff.store=Le numÃ©ro du compteur se trouve dans un magasin diffÃ©rent, {0}. 
online.bulk.meter.error.groups.not.selected=Pour sauvegarder un compteur, tous les groupes de points D'utilisation requis doivent Ãªtre sÃ©lectionnÃ©s.

question.confirm.continue.new=Capturez-vous comme nouveau compteur pour ce regroupement? 
question.confirm.continue.open.up.page=Ouvrez le onglet Point D'utilisation pour cela?
question.confirm.continue.save.anyway=Continuer et lier ce regroupement?

meter.key.revision.help=Entrez le numÃ©ro de RÃ©vision ClÃ© actuel. Ce champ est requis pour l'activation.
meter.key.revision=RÃ©vision ClÃ©

online.bulk.sts.meter.save.error=DÃ©pannage du compteur STS, codes non trouvÃ©s.
online.bulk.meter.save.error=DifficultÃ© de sauvegarder le compteur.

meter.sts.length=Les numÃ©ros de compteur STS doivent avoir entre 11 et 13 caractÃ¨res.

#Njigi 3 March 2017
usagepoint.charge.button.writeoff=Annuler les frais
usagepoint.charge.button.upchargeview=Afficher les frais des montants en suspens
usagepoint.charge.view.dialog.heading=Afficher les frais des montants en suspens
usagepoint.charge.no.data=Aucune donnÃ©e n'est disponible pour Ãªtre visionnÃ©e.
usagepoint.charge.view.dialog.nodate.filter=SÃ©lectionnez la date.
usagepoint.charge.view.dialog.invalid.date=La date sÃ©lectionnÃ©e doit Ãªtre aprÃ¨s la derniÃ¨re date cyclique affichÃ©e
usagepoint.charge.view.dialog.invalid.date2=La date sÃ©lectionnÃ©e ne peut pas Ãªtre Ã  l'avenir
usagepoint.charge.writeoff.trans.success=Annulation traitÃ© avec succÃ¨s.
usagepoint.charge.writeoff.trans.failure=Annulation traitÃ© a Ã©chouÃ©. Contactez le support.
chargewriteoff.save.error=L'enregistrement du dossier de l'annulation des frais a Ã©chouÃ©.

#Njigi 30 January 2017 ####
auxaccount.trans.upload=Envoi aux transactions auxiliaires
auxaccount.trans.upload.heading=Envoi aux transactions auxiliaires
auxaccount.trans.upload.auxaccountname=Nom de Compte Auxiliaire
auxaccount.trans.upload.agreementref=L'Accord Ref
auxaccount.trans.upload.data.title=Importation de l'opÃ©rations d'ajustement du solde de Compte Auxiliare
auxaccount.trans.upload.data.description=SÃ©lectionnez le fichier CSV contenant les transactions Auxiliaire pour l'importation dans le systÃ¨me de gestion des compteurs.
auxaccount.trans.upload.invalid.auxaccountname=Nom de compte Auxiliaire maximum 100 caractÃ¨res
auxaccount.trans.upload.invalid.auxaccount=Aucun Compte Auxiliaire avec le nom et L'accord de RÃ©fÃ©rence fournis existe
auxaccount.trans.upload.process.failed=Erreur systÃ¨me lors de la transaction:Nom du Compte Auxiliaire= {0}, Ref. D'accord= {1}. Essayez de renvoyer le fichier.
trans.bulk.upload.amt.incl.tax=Montant incl impÃ´t
trans.bulk.upload.amt.tax=Montant de l'impÃ´t
trans.bulk.upload.trans.date=Date de la transaction
trans.bulk.upload.account.ref=RÃ©fÃ©rence du Compte
trans.bulk.upload.comment=Commentaire
trans.bulk.upload.invalid.amt.incl.tax=Montant incl. l'impÃ´t n'est pas numÃ©rique
trans.bulk.upload.invalid.amt.tax=Le montant de l'impÃ´t n'est pas numÃ©rique
trans.bulk.upload.invalid.account.ref=RÃ©fÃ©rence du compte maximum de 100 caractÃ¨res
trans.bulk.upload.invalid.comment=Commentaire maximum 255 caractÃ¨res
trans.bulk.upload.invalid.our.ref=Notre rÃ©fÃ©rence maximum 100 caractÃ¨res (Renommer fichier)
trans.bulk.upload.trans.date.in.future=La date de la transaction doit Ãªtre vide (par dÃ©faut pour la date de traitement) ou correctement formatÃ©e

#Njigi 16 January 2016 ####
auxaccount.upload=TÃ©lÃ©chargement de Compte Auxiliaire
auxaccount.upload.heading=TÃ©lÃ©chargement de compte Auxiliaire
auxaccount.upload.data.title=Importer des donnÃ©es de compte Auxiliaires
auxaccount.upload.data.description=SÃ©lectionnez le fichier CSV contenant les donnÃ©es du compte auxiliaire pour l'importation dans le systÃ¨me de gestion des compteurs.
auxaccount.upload.errors=Erreurs
auxaccount.upload.identifierType=Type d'Identificateur
auxaccount.upload.identifier=Identifiant
auxaccount.upload.auxaccountname=Nom du Compte Auxiliaire
auxaccount.upload.auxtype=Nom de type Auxiliaire
auxaccount.upload.accountpriority=PrioritÃ© au compte
auxaccount.upload.chrgschdlname=Nom de frais de l'horaire
auxaccount.upload.principleamaount=Montant Principale
auxaccount.upload.balance=Balance
auxaccount.upload.customerref=Ref. De l'Accord client
auxaccount.upload.invalid.identifiertype=TypeIdentificateur doit  Ãªtre accordRef / NomPointUtilisation / numÃ©roCompteur
auxaccount.upload.invalid.identifier=Identificateur Invalide - pas dans la base de donnÃ©es
auxaccount.upload.invalid.agreement=Point d'Utilisation n'a pas de contrat client en place
auxaccount.upload.invalid.usagepoint.or.agreement=Compteur n'a pas de Point d'Utilisation ou le Point d'Utilisation n'a pas d'accord
auxaccount.upload.invalid.auxaccountname=Nom de compte Auxiliaire maximum 100 caractÃ¨res
auxaccount.upload.invalid.principleamaount=Le montant Principale n'est pas numÃ©rique
auxaccount.upload.invalid.balance=Le montant du solde n'est pas numÃ©rique
auxaccount.upload.successful.count=Total de {0} TÃ©lÃ©chargement de Compte Auxiliare traitÃ© avec succÃ¨s.

#Antonyo 12 January 2017  ####
#power.limit.edit.label.prompt=Nom
#power.limit.edit.label.help=Nom de la valeur limite de puissance. C'est l'affichage du texte
#power.limit.edit.value.prompt=Valeur
#power.limit.edit.value.help=Valeur de la limite de puissance. Cette valeur doit Ãªtre numÃ©rique
power.limit.add.button.prompt=Ajouter la limite de puissance
power.limit.table.label.header=Nom de limite de puissance
power.limit.table.value.header=Valeur limite de puissance
power.limit.edit.popup.header=Modifier la limite de puissance

#Patrick 02 December 2016 ####
user.custom.field.datatype.text=Texte
user.custom.field.datatype.numeric=NumÃ©rique
user.custom.field.datatype.date=Date
user.custom.field.datatype.boolean=Vrai/Faux
user.custom.field.datatype.list=Liste
button.done=TerminÃ©

usagepointgroup.custom.field.error.text=Une valeur est nÃ©cessaire pour ce champ
usagepointgroup.custom.field.error.list=Vous devez sÃ©lectionner un Ã©lÃ©ment de la liste
usagepointgroup.custom.field.error.date.empty=Une date valide est nÃ©cessaire. Format (yyyy-MM-dd HH:mm:ss)
usagepointgroup.custom.field.error.date.invalid=Format de date invalide. Format (aaaa-MM-dd HH:mm:ss)
usagepointgroup.custom.field.error.numeric=Un numÃ©ro valide doit Ãªtre fourni

button.view.usagepointgroup=Afficher DÃ©tails

usagepointgroup.custom.field.default.datatype.description=Le Type de donnÃ©es pour ce champ personnalisÃ© ou une liste sÃ©parÃ©e par des virgules pour les menus dÃ©roulants
usagepointgroup.hierarchy.no.additionalinfo=Aucune information supplÃ©mentaire pour ce groupe de PointUtilisation

appsettings.popup.button.add=Ajouter ÃlÃ©ment
appsettings.popup.table.add=Nouvel ÃlÃ©ment - Cliquez pour Modifier
appsettings.popup.table.title=Liste des ÃlÃ©ments
appsettings.popup.table.note.dups=Les Ã©lÃ©ments en copie seront supprimÃ©s lorsque TerminÃ©

appsetting.field.datatype=Type de DonnÃ©es
appsetting.field.datatype.help=Type de donnÃ©es pour le ParamÃ¨tre d'Application. Pour la liste des Types de DonnÃ©es, Les Ã©lÃ©ments de la liste seront rejetÃ©s lorsque le type de donnÃ©es est changÃ© en: Texte, NumÃ©rique, Date, or Vrai/Faux.
appsetting.field.datatype.listitems=Modifier ÃlÃ©ments

appsettings.list.duplicate.item=L'Ã©lÃ©ment en copie entrÃ©.

#Njigi 22 December 2016 ####
bulk.upload.custom.fields.get.error=Erreur de base de donnÃ©es obtenant des paramÃ¨tres d'application de champ personnalisÃ©s! Contactez le support.

#Njigi 28 November 2016 ####
bulk.upload.meterupload.heading=Masse TÃ©lÃ©chargement de compteurs
bulk.upload.meterupload.enddevicestorename=Nom du Magasin d'Appareils
bulk.upload.meterupload.data.title=DÃ©tails du Compteur d'Importation
bulk.upload.meterupload.description=SÃ©lectionnez le fichier CSV contenant les dÃ©tails du compteur pour l'importation dans le SystÃ¨me de Gestion des Compteurs.

#Special Actions - joelc 01/12/16
link.special.actions=Actions avec des raisons
link.special.action.reasons=Raisons SpÃ©ciales d'Action
button.viewreasons=Afficher la liste des raisons
special.action=Action spÃ©ciale
special.actions.header=Configurer des Actions SpÃ©ciales
special.actions.title=Mettre Ã  jour les Actions SpÃ©ciales
special.action.name=Nom de l'Action
special.action.name.help=Le nom de cette action spÃ©ciale.
special.action.reason.required=Requis
special.action.reason.required.help=Est-il nÃ©cessaire ou facultatif de fournir une raison
special.action.reason.input.type=Type d'EntrÃ©e
special.action.reason.input.type.help=Est-ce que la raison est donnÃ©e en tant que texte gratuit, est-elle sÃ©lectionnÃ©e Ã  partir d'une liste de raisons ou est-ce que c'est un texte gratuit et / ou sÃ©lectionnÃ© dans une liste 
special.action.description=Description de l'Action
special.action.description.help=Une description de l'action spÃ©ciale qui permet de fournir une raison pour laquelle l'action doit Ãªtre fournie
special.actions.field.name=Nom de l'action
special.actions.field.description=Description
special.actions.field.reason.required=Requis
special.actions.field.reason.input.type=Type d'EntrÃ©e
special.actions.reason.inputtype.freetext=Entrez la raison dans la zone de texte
special.actions.reason.inputtype.selected=SÃ©lectionnez la raison de la liste
special.actions.reason.inputtype.both=Entrez la raison ou sÃ©lectionnez la liste

special.action.reasons.header=CrÃ©er des raisons
special.action.reasons.title=Raisons de l'Action spÃ©ciale
special.action.reasons.title.add=Ajouter une nouvelle raison
special.action.reasons.title.update=RÃ©vision raison
special.action.reasons.field.name=Raison
special.action.reasons.field.description=Description de la raison
special.action.reasons.field.recordstatus=Actif
special.action.reasons.name=Raison
special.action.reasons.name.help=Entrez une raison possible pour effectuer cette action. Cela sera affichÃ© dans la liste dÃ©roulante de cette action. 
special.action.reasons.description=Description
special.action.reasons.description.help=DÃ©crivez ce que c'est pour cette raison.
special.action.reasons.active=Actif
special.action.reasons.active.help=Cette raison est-elle active?
special.action.reason=Raison
special.action.and.or=et/ou
special.action.enter.reason=Entrez une raison de cette action
special.action.select.reason=SÃ©lectionnez une raison pour cette action
error.special.action.reason.required=Une raison de cette action est requise     
usagepoint.deactivate.enter.reason=Entrez une raison pour cette dÃ©sactivation
usagepoint.deactivate.select.reason=SÃ©lectionnez une raison pour cette dÃ©sactivation

#Rencia 24 November 2016  ####
bulk.upload.pricingstructure.not.active=La structure de prix n'est pas actif.

#Rencia 16 November 2016  ####
error.field.active.invalid=L'actif doit Ãªtre vide (Implique OUI) ou non.
error.field.metermodelid.null=Le modÃ¨le du compteur est requis.
error.field.ststariffindex.max=L'indice tarifaire doit Ãªtre infÃ©rieur Ã  {0} caractÃ¨res.
error.field.servicelocationid.null=Lieu du service est requis
error.field.customerkindid.null=Type de client
error.field.customerid.null=L'ID du client est requis
error.field.accountbalance.null=Le solde du compte est requis.

#Bulk Upload - standard
bulk.upload.download.sample=TÃ©lÃ©charger un exemple de tableur.
bulk.upload.download.sample.button=TÃ©lÃ©charger comme .CSV
bulk.upload.file.select.labeltext=SÃ©lectionnez le fichier CSV pour tÃ©lÃ©charger
bulk.upload.select.file.help=SÃ©lectionnez un fichier contenant les informations dans le format csv spÃ©cifiÃ© pour l'importation dans le systÃ¨me
bulk.upload.csv.button=TÃ©lÃ©charger des donnÃ©es CSV
bulk.upload.process.button=TÃ©lÃ©chargement de Processus
bulk.upload.errors=Erreurs
bulk.upload.object.creation.error=Erreur lors de la crÃ©ation {0} objet. Contactez le support!
bulk.upload.file.action.unknown=Action de tÃ©lÃ©chargement de fichier inconnu, contacter Support
bulk.upload.file.none=Aucun fichier n'a Ã©tÃ© sÃ©lectionnÃ© pour Ãªtre tÃ©lÃ©chargÃ©
bulk.upload.invalid.filename=Nom de fichier incorrect - Un trait d'union ou une pÃ©riode manquant. Les noms de fichier sont attendus comme xxxxxx-rÃ©fÃ©rence.csv oÃ¹ xxxxxx est votre identifiant de fichier choisi, par exemple. CompteurTÃ©lÃ©charger, et <rÃ©fÃ©rence> est sauvegardÃ© comme 'notre RÃ©f' sur les transactions
bulk.upload.invalid.filename.changed=Le nom de fichier a changÃ© entre les Ã©tapes! Ãtait {0}; Ã  prÃ©sent {1}
bulk.upload.invalid.cannot.create.dir=ERREUR! Impossible de crÃ©er le rÃ©pertoire. Contactez Support.
bulk.upload.file.unrecognized.heading.error=Titre de colonne non reconnu dans le fichier de tÃ©lÃ©chargement: {0}
bulk.upload.table.heading.valid=DonnÃ©es de TÃ©lÃ©chargement Valides : Ã©chantillon des 15 premiÃ¨res lignes dans le fichier
bulk.upload.table.heading.errors=TÃ©lÃ©charger des donnÃ©es : Erreurs
bulk.upload.trans.validation.errors=Erreurs de validation trouvÃ©es. RÃ©parez et soumettre Ã  nouveau. Maximum 15 erreurs sont traitÃ©es Ã  tout moment
bulk.upload.invalid.unexpected.commas=Virgules Ã  l'intÃ©rieur des champs - ne peut pas identifier les champs sÃ©parÃ©s avec prÃ©cision 
bulk.upload.filename=Nom de fichier sÃ©lectionnÃ©={0}
bulk.upload.process.failed=Erreur systÃ¨me lors du tÃ©lÃ©chargement aprÃ¨s {0} dossiers. Essayez de renvoyer le fichier.
bulk.upload.active=Actif
#Bulk Upload Validation Errors
bulk.upload.invalid.required.field={0} Requis
bulk.upload.invalid.nonexisting.field={0} Pas sur la base de DonnÃ©es
bulk.upload.duplicate.field={0} existe dÃ©jÃ , soit sur la base de donnÃ©es, soit sur ce fichier de tÃ©lÃ©chargement
bulk.upload.invalid.field={0} est invalide
bulk.upload.invalid.parsedate=Ne peut pas analyser {0} - vÃ©rifier format
bulk.upload.file.process.error=Erreur lors du processus du fichier

#Bulk Upload - meter / Customer / Usage Point
bulk.upload.data.title.meter=Importer Compteur, Point Utilisation et DÃ©tails du client
bulk.upload.data.description.meter=SÃ©lectionnez le fichier CSV contenant le compteur et les dÃ©tails liÃ© pour l'importation dans le SystÃ¨me de Gestion des Compteurs.
bulk.upload.metertype=Type Compteur
bulk.upload.meternum=Num Compteur
bulk.upload.serialnum=Num sÃ©rie
bulk.upload.mrid=NumCompteur Externe
bulk.upload.metermodelname=ModÃ¨le Compteur
bulk.upload.enddevicestore=Magasin d'appareils
bulk.upload.breakerid=Id du Disjoncteur
bulk.upload.ststokentechcode=Code de Jeton Tech
bulk.upload.stsalgorithmcode=Code Algorithme
bulk.upload.stssupplygroupcode=Code de Groupe d'Approvisionnement
bulk.upload.stskeyrevisionnum=NumÃ©ro de RÃ©vision ClÃ©
bulk.upload.ststariffindex=Indice Tarifaire
bulk.upload.usagepointname=Nom du Point d'Utilisation
bulk.upload.installationdate=Date D'installation
bulk.upload.pricingstructurename=Structure des prix
bulk.upload.uperfnumber=PU Num ERF
bulk.upload.upstreetnum=PU Num de Rue
bulk.upload.upbuildingname=PU BÃ¢timent
bulk.upload.upsuitenum=PU Num Suite
bulk.upload.upaddressline1=PU Addr 1
bulk.upload.upaddressline2=PU Addr 2
bulk.upload.upaddressline3=PU Addr 3
bulk.upload.uplatitude=PU Latitude
bulk.upload.uplongitude=PU Longitude
bulk.upload.upcustomvarchar1=PU CaractÃ¨re PersonnalisÃ©1
bulk.upload.upcustomvarchar2=PU CaractÃ¨re PersonnalisÃ©2
bulk.upload.upcustomnumeric1=PU NumÃ©rique PersonnalisÃ©1
bulk.upload.upcustomnumeric2=PU NumÃ©rique PersonnalisÃ©2
bulk.upload.upcustomtimestamp1=PU Horodatage1
bulk.upload.upcustomtimestamp2=PU Horodatage2
bulk.upload.customerkind=Type de Client
bulk.upload.companyname=Nom de la Compagnie
bulk.upload.taxnum=Num d'impÃ´t
bulk.upload.firstnames=PrÃ©noms
bulk.upload.surname=Nom de famille
bulk.upload.initials=Initiales
bulk.upload.title=Titre
bulk.upload.email1=Email1
bulk.upload.email2=Email2
bulk.upload.phone1=TÃ©lÃ©phone1
bulk.upload.phone2=TÃ©lÃ©phone2
bulk.upload.custerfnumber=Cust Erf
bulk.upload.custstreetnum=Cust Num de Rue
bulk.upload.custbuildingname=Cust BÃ¢timent
bulk.upload.custsuitenum=Suite du Client
bulk.upload.custaddressline1=Cust Adr 1
bulk.upload.custaddressline2=Cust Adr 2
bulk.upload.custaddressline3=Cust Adr 3
bulk.upload.custlatitude=Latitude du Client
bulk.upload.custlongitude=Longitude du Client
bulk.upload.custcustomvarchar1=Cust CaractÃ¨re PersonnalisÃ©1
bulk.upload.custcustomvarchar2=Cust CaractÃ¨re PersonnalisÃ©2
bulk.upload.custcustomnumeric1=Cust NumÃ©rique PersonnalisÃ©1
bulk.upload.custcustomnumeric2=Cust NumÃ©rique PersonnalisÃ©2
bulk.upload.custcustomtimestamp1=Cust Horodatage PersonnalisÃ©1
bulk.upload.custcustomtimestamp2=Cust Horodatage PersonnalisÃ©2
bulk.upload.agreementref=Accord RÃ©f
bulk.upload.startdate=Date de DÃ©but de l'Accord
bulk.upload.accountname=Nom du compte
bulk.upload.accountbalance=Solde du Compte
bulk.upload.lowbalancethreshold=Solde de seuil bas 
bulk.upload.notificationemail=Notif Email
bulk.upload.notificationphone=Notif TÃ©lÃ©phone
bulk.upload.notifylowbalance=Notifier Solde Bas
#Invalid Meter bulk upload errors
bulk.upload.metertype.incompatible.stsinfo=Les dÃ©tails de cryptage STS sont entrÃ©s pour un type de compteur non-STS
bulk.upload.metertype.incompatible.metermodel=Le ModÃ¨le du Compteur n'est pas compatible avec le Type de Compteur
bulk.upload.invalid.pricingstructure.incompatible.metermodel=Structure de prix incompatible avec ModÃ¨le du Compteur
bulk.upload.customer.account.notfor.STS=Les compteurs STS ne doivent pas avoir d'Informations sur le Compte Client

#Rencia 11 November 2016  ####
tariff.field.namibia.neflevy=NEF ImpÃ´t
tariff.field.namibia.neflevy.help=Entrer l'impÃ´t du National Energy Fund
tariff.field.namibia.ecblevy=ECB ImpÃ´t
tariff.field.namibia.ecblevy.help=Entrer l'impÃ´t du Conseil de contrÃ´le de l'Ã©lectricitÃ© 
tariff.error.positive.or.zero=La valeur doit Ãªtre positive ou nulle

#Rencia 9 November 2016  ####
tariff.field.discount=Type de paiement RÃ©ductions
tariff.field.heading.paytype=Type de paiement
tariff.field.heading.discount=Remise %
tariff.field.heading.description=Description
tariff.error.discount=Remise % doit Ãªtre positif ou nul.
tariff.error.discount.descrip=Si la rÃ©duction % entrÃ©, et non zÃ©ro, la description est requise.

# Rencia 20 October 2016  ####
### New
tariff.field.cycle.name=Cycle
tariff.field.cycle.name.help=SÃ©lectionnez le cycle de facturation.
tariff.field.cost.help=Le montant hors impÃ´t Ã  rembourser selon la sÃ©lection de cycle.

### Took out "Monthly"
tariff.field.cost.name=Nom du CoÃ»t
tariff.field.cost.name.help=Le nom de ce coÃ»t qui finira par le reÃ§u du client.
tariff.field.cost=CoÃ»t Excl. ImpÃ´t

### Tariff Cycle Labels  NEW
tariff.cost.cycle.daily=Quotidien
tariff.cost.cycle.monthly=Mensuel
tariff.cost.cycle.error=Doit sÃ©lectionner un cycle valide.

### changed names
tariff.error.cyclic.charge.positive=Doit Ãªtre une valeur positive.
tariff.error.cyclic.charge=SpÃ©cifiez un coÃ»t pour le nom.
tariff.error.cyclic.charge.name=SpÃ©cifiez un nom pour le coÃ»t.

#Rencia 22 August 2016  ####
error.field.numeric.positive_not_zero=La valeur doit Ãªtre positive et ne pas Ãªtre nulle.
tariff.field.percent_charge_name=Nom du frais en pourcentage
tariff.field.percent_charge_name.help=Nom facultatif du pourcentage de frais qui sera affichÃ© sur le reÃ§u.
tariff.field.percent_charge=Frais en pourcentage
tariff.error.percent_charge.name=SpÃ©cifiez un nom pour le frais.
tariff.error.percent_charge.positive_not_zero=Doit Ãªtre une valeur positive et pas zÃ©ro.

##########################
#### General messages ####
##########################

application.default.title=iPay - Gestion Des Compteurs
application.title=Gestion Des Compteurs
workspace.usagepoint.information=Information et Services
unit.kilowatthour.symbol=kWh
unit.watts.symbol=W
unit.percent=%
changes_unsaved=* Les modifications non sauvegardÃ©es
menu.about_link=A propos de
application.info=PropulsÃ© par BizSwitch de iPay

###############
#### Error ####
###############
error.title=Erreur
error.general=Une erreur s'est produite et a Ã©tÃ© enregistrÃ©e. Veuillez informer L'administrateur du systÃ¨me
error.login=Votre tentative de connexion n'a pas rÃ©ussi. Veuillez rÃ©essayer.
error.denied=L'utilisateur {0} n'a pas la permission d'accÃ©der Ã  ce site.
error.accessdenied=Vous n'avez pas la permission d'accÃ©der Ã  cette fonction.
error.networkConnect=Une erreur s'est produite lors de la tentative de contact avec le serveur.
error.networkIO=Une erreur de communication sur le rÃ©seau s'est produite.
error.networkTimeout=Le serveur prend trop de temps pour rÃ©pondre.
error.networkUnknown=Une erreur inconnue s'est produite lors de la tentative de contact avec le serveur. Notifier l'administrateur.
error.delete=Impossible de supprimer
permission.denied=Permission refusÃ©e
permission.edit.denied=Modification de l'autorisation refusÃ©e
error.no.user=Aucun utilisateur actuel n'est disponible.
error.current.group=Groupe invalide spÃ©cifiÃ© pour le groupe actuel de l'utilisateur.
error.meter.accessdenied=Vous n'avez pas la permission d'accÃ©der Ã  ce compteur.
error.pricing.structure.accessdenied=Vous n'avez pas la permission de modifier l'allocation de la structure des prix.
error.pricing.structure.accessdenied.addmeter=Vous n'avez pas la permission de modifier l'allocation de la structure des prix, ne peut donc pas attacher le nouveau compteur ci-dessus Ã  ce point d'utilisation. Le compteur a Ã©tÃ© enregistrÃ© dans le magasin de pÃ©riphÃ©riques sÃ©lectionnÃ©.
error.meter.workspace.error=Erreur lors de l'ouverture de l'espace travail du compteur.
error.customer.workspace.error=Erreur lors de l'ouverture de l'espace travail du client.
error.usagepoint.workspace.error=Erreur d'ouverture de l'espace de travail du Point d'Utilisation.
error.no.user.assignedgroup=L'utilisateur actuel n'a pas de groupe assignÃ©.
error.login.locked=VÃ´tre compte a Ã©tÃ© bloquÃ©.
error.loginerror=Nom d'utilisateur ou mot de passe incorrect.
error.login.disabled=Votre compte a Ã©tÃ© dÃ©sactivÃ©.

###############
#### Login ####
###############
login.title=Connexion
login.form.title=Veuillez s'il vous plaÃ®t entrer votre nom d'utilisateur et votre mot de passe.
login.form.username=Nom d'utilisateur
login.form.password=Mot de passe
login.form.login=Connexion
login.form.remember_me=Souvenir de moi pendant deux semaines
login.form.password.forgotten=Mot de passe oubliÃ©?
password.form.instructions=Entrez l'adresse e-mail enregistrÃ©e de votre utilisateur.
password.email.invalid=Adresse e-mail invalide.
password.email.unknown.user=Utilisateur inconnu pour l'adresse email saisie.
password.multiple.users=Plusieurs utilisateurs correspondent Ã  l'adresse email saisie.
password.multiple.users.1=SÃ©lectionnez un ci-dessous et cliquez sur Envoyer.
password.reset=RÃ©initialiser le mot de passe. VÃ©rifiez votre adresse Ã©lectronique pour plus de dÃ©tails.
password.error.reset=Erreur: Impossible de rÃ©initialiser votre mot de passe.
adminuser.password.save.error=Erreur: Impossible d'enregistrer l'utilisateur mis Ã  jour.
email.password.reset.subject=RÃ©initialiser le mot de passe
password.form.email=Adresse Email:
password.form.submit=Soumettre

#########################
#### Password Change ####
#########################
password_change.title=Changement de mot de passe
password_change.form.title=Changer mon mot de passe
password_change.form.password1=Entrez un nouveau mot de passe:
password_change.form.password2=RÃ©-entrez le nouveau mot de passe:
password_change.form.submit=ValidÃ© le changement
password_change.success=Mot de passe changÃ© pour {0}
password_change.validate.equal=Les deux entrÃ©es de mot de passe ne sont pas Ã©gales
password_change.validate.ldap=Les utilisateurs authentifiÃ©s Ldap ne peuvent pas modifier leurs mots de passe. Contactez votre administrateur systÃ¨me Ldap.

########################################################################################################################
# GWT specific properties #
########################################################################################################################

## Errors
error.save=Erreur: {0} n'a pas pu Ãªtre enregistrÃ©.
error.field.required=Information requis.
error.field.is.required={0} est un champ obligatoire.
error.field.numeric.required={0} est un champ numÃ©rique requis.
error.no.selection=Aucune sÃ©lection valide n'a Ã©tÃ© faite.
error.data.null=Les donnÃ©es entrantes sont invalides.
error.datatype.null=Le {0} est invalide.
error.missing={0} {1} introuvable.
error.numeric.value=Entrez une valeur numÃ©rique valide.
error.token.retrieve=Impossible de rÃ©cupÃ©rer le jeton.
error.meter.load=Impossible d'afficher le compteur.
error.usagepoint.load=Impossible d'afficher le point d'utilisation.

# Field errors used by the Validation framework's annotations
error.field.id.null=L'ID est obligatoire
error.field.key.null=La clÃ© est nÃ©cessaire.
error.field.key.range=La clÃ© doit Ãªtre entre les {min} et {max} caractÃ¨res.
error.field.name.null=Le nom est requis
error.field.name.range=Le nom doit Ãªtre entre les {min} et {max} caractÃ¨res.
error.field.name.max=Le nom doit Ãªtre infÃ©rieur Ã  {max} caractÃ¨res.
error.field.value.null=Valeur est requise.
error.field.value.range=La valeur doit Ãªtre entre les {min} et {max} caractÃ¨res.
error.field.description.null=La description est obligatoire.
error.field.description.range=La description doit Ãªtre entre les {min} et {max} caractÃ¨res.
error.field.description.max=La description doit Ãªtre infÃ©rieure Ã  {max} caractÃ¨res.
error.field.recordstatus.null=L'Ãtat de l'enregistrement est requis.
error.field.contactname.null=Le nom du contact est requis.
error.field.contactname.range=Le nom du contact doit Ãªtre entre les {min} et {max} caractÃ¨res.
error.field.contactemail=Le courrier Ã©lectronique du contact doit Ãªtre une adresse Ã©lectronique valide.
error.field.contactemail.max=Le courrier Ã©lectronique de contact doit Ãªtre infÃ©rieur aux caractÃ¨res {max}.
error.field.taxref.max=La rÃ©fÃ©rence impÃ´t doit Ãªtre infÃ©rieure aux caractÃ¨res {max}.
error.field.contactnumber.max=Le numÃ©ro de contact doit Ãªtre infÃ©rieur aux {max} caractÃ¨res.
error.supplyserver=Le code du groupe d'approvisionnement et la combinaison du numÃ©ro de rÃ©vision doivent Ãªtre uniques.
error.field.supplygroupcode.null=Le code du groupe d'approvisionnement est requis.
error.field.keyrevisionnum.null=Le numÃ©ro de rÃ©vision est requis.
error.field.supplygroupcode.format=Entrez une valeur numÃ©rique. 
error.field.supplygroupcode.range=Le code doit Ãªtre infÃ©rieur Ã  7 chiffres.
error.field.calccontents.null=Calc Contenue est un champ obligatoire.
error.field.schedulename.range=Nom du planning doit Ãªtre comprise entre {min} et {caractÃ¨res max}.
error.minmax.range.auxchargeschedule=Le montant minimum doit Ãªtre infÃ©rieur au montant maximal.
error.field.meternum.null=Le numÃ©ro du compteur est un champ obligatoire.
error.field.meternum.range=Le numÃ©ro du compteur doit Ãªtre entre les {min} et {max} caractÃ¨res.
error.field.mrid.null=MRID est un champ obligatoire.
error.field.mrid.range=Le MRID doit Ãªtre entre les {min} et {max} caractÃ¨resr 
error.field.serialnum.max=Le numÃ©ro de sÃ©rie doit Ãªtre infÃ©rieur aux {max} caractÃ¨res.
error.field.meternumchecksum.max=Le seuil de contrÃ´le doit Ãªtre infÃ©rieur aux {max} caractÃ¨res.
error.field.ststokentechcode.max=Le code numerique du jeton doit Ãªtre infÃ©rieur aux {max} caractÃ¨res.
error.field.stsalgorithmcode.max=Code de l'algorithme doit Ãªtre infÃ©rieur Ã  {max} caractÃ¨res.
error.field.stsprevsupplygroupcode.max=Le code du groupe d'approvisionnement doit Ãªtre infÃ©rieur aux {max} caractÃ¨res.
error.field.stscurrtariffindex.max=L'indice tarifaire doit Ãªtre infÃ©rieur aux {max} caractÃ¨res.
error.field.addressline1.max=Chaque ligne d'adresse doit Ãªtre infÃ©rieure Ã  {max} caractÃ¨res.
error.field.addressline2.max=Chaque ligne d'adresse doit Ãªtre infÃ©rieure Ã  {max} caractÃ¨res.
error.field.addressline3.max=Chaque ligne d'adresse doit Ãªtre infÃ©rieure Ã  {max} caractÃ¨res.
error.field.city.max=La ville doit Ãªtre infÃ©rieure Ã  {max} caractÃ¨res.
error.field.province.max=La province doit Ãªtre infÃ©rieure Ã  {max} caractÃ¨res.
error.field.country.max=Le pays doit Ãªtre infÃ©rieur aux {max} caractÃ¨res.
error.field.postalcode.max=Le code postal doit Ãªtre infÃ©rieur Ã  {max} caractÃ¨res.
error.field.erfnumber.max=Le numÃ©ro Erf doit Ãªtre infÃ©rieur Ã  {max} caractÃ¨res.
error.field.streetnum.max=Le numÃ©ro de la rue doit Ãªtre infÃ©rieur aux {max} caractÃ¨res.
error.field.buildingname.max=Le nom du bÃ¢timent doit Ãªtre infÃ©rieur aux {max} caractÃ¨res.
error.field.suitenum.max=NumÃ©ro de la suite doit Ãªtre infÃ©rieur aux {max} caractÃ¨res.
error.field.surname.null=Le nom de famille est obligatoire.
error.field.agreementref.null=La rÃ©fÃ©rence de l'accord est un champ obligatoire.
error.field.agreementref.duplicate=RÃ©fÃ©rence d'accord en double {0}. SpÃ©cifiez une rÃ©fÃ©rence unique.
error.field.email1.max=L'adresse Ã©lectronique doit Ãªtre infÃ©rieure Ã  {max} caractÃ¨res.
error.field.email1=Adresse email invalide.
error.field.email2.max=L'adresse Ã©lectronique doit Ãªtre infÃ©rieure Ã  {max} caractÃ¨res.
error.field.email2=Adresse email invalide.
error.field.phone1.max=Le numÃ©ro de tÃ©lÃ©phone doit Ãªtre infÃ©rieur aux caractÃ¨res {max}.
error.field.phone2.max=Le numÃ©ro de tÃ©lÃ©phone doit Ãªtre infÃ©rieur aux caractÃ¨res {max}.
error.field.phone=NumÃ©ro de tÃ©lÃ©phone invalide, seulement 0-9, '+', espace, trait d'union, 'ext' et 'x' autorisÃ©s. 'Ext' ou 'x' doit Ãªtre suivi d'un numÃ©ro d'extension numÃ©rique.
error.field.phone2=Second numÃ©ro de tÃ©lÃ©phone invalide, seulement 0-9, '+', espace, trait d'union, 'ext' et 'x' autorisÃ©s. 'Ext' ou 'x' doit Ãªtre suivi d'un numÃ©ro d'extension numÃ©rique.
error.field.startdate.null=Date de dÃ©but est un champ obligatoire.
error.field.startdate.future=Date de dÃ©but ne peut pas Ãªtre Ã  l'avenir.
error.field.installdate.future=La date d'installation ne peut pas Ãªtre Ã  l'avenir.
error.powerlimit.invalid=SÃ©lectionnez une valeur valide pour limite de puissance (nÃ©cessaire)
error.priority.invalid=La prioritÃ© doit Ãªtre un nombre (1 est la plus haute prioritÃ©)
error.search.meter=Entrez un numÃ©ro de compteur valide.
error.search.customer=Entrez un terme de recherche valide.
error.field.touseasonname.null=Le nom de la saison est obligatoire.
error.field.touperiodname.null=Nom PÃ©riode est un champ obligatoire..
error.field.touperiodcode.null=Code PÃ©riode est un champ obligatoire.
error.field.title.max=Le titre doit Ãªtre infÃ©rieur aux {max} caractÃ¨res.
error.field.initials.max=Les initiales doivent Ãªtre infÃ©rieures aux {max} caractÃ¨res.
error.field.firstnames.max=Les prÃ©noms doivent Ãªtre infÃ©rieurs Ã  {max} caractÃ¨res.
error.field.surname.max=Le nom de famille doit Ãªtre infÃ©rieur aux {max} caractÃ¨res.
error.field.companyname.max=La sociÃ©tÃ© doit Ãªtre infÃ©rieure Ã  {max} caractÃ¨res.
error.field.taxnum.max=NumÃ©ro de taxe doit Ãªtre infÃ©rieur aux {max} caractÃ¨res.
error.field.agreementref.max=L'accord Ref doit Ãªtre infÃ©rieur aux {max} caractÃ¨res.
error.field.usagepoint.name.null=Le nom du point d'utilisation est requis
error.field.usagepoint.name.duplicate=nom en double {0} pour un point d'utilisation. SpÃ©cifiez un nom unique.

error.field.comment.max=Les commentaires doivent Ãªtre infÃ©rieurs aux {max} caractÃ¨res.
error.field.accountref.max=La rÃ©fÃ©rence du compte doit Ãªtre infÃ©rieure aux {max} caractÃ¨res.
error.field.ourref.null=Notre rÃ©fÃ©rence est requise.
error.field.ourref.range=Notre rÃ©fÃ©rence doit Ãªtre entre les caractÃ¨res {min} et {max}.
error.field.amtincltax.null=Le montant doit Ãªtre entrÃ©.
error.field.amttax.null=La taxe peut ne pas Ãªtre nulle. Entrez 0 si aucune taxe.
error.field.customvarchar.max=Le champ de texte personnalisÃ© doit Ãªtre infÃ©rieur Ã  {0} caractÃ¨res.

error.field.accountname.range=Le nom du compte doit Ãªtre infÃ©rieur aux caractÃ¨res {max}.

# Error headers, etc
error.validation.header=Veuillez corriger ces erreurs de validation:
error.validation.fields.header=Veuillez corriger ces erreurs d'entrÃ©e:
error.field.code.null=Le code est requis
error.field.code.range=Le code doit Ãªtre entre les caractÃ¨res {min} et {max}.
error.server.connection=Impossible de se connecter au serveur Web.
error.server=RÃ©ponse d'erreur reÃ§ue du serveur Web.
error.field.manufacturerid.null=Le fabricant est requis.
error.field.serviceresourceid.null=Une ressource de service est requise.
error.field.metertypeid.null=Le type du compteur est requis.
error.field.paymentmodeid.null=Le mode de paiement est requis.
error.field.accountname.null=Un nom de compte est requis.
error.field.accountname.duplicate=Nom du compte en double {0}. SpÃ©cifiez un nom unique.
error.field.taskschedulename.null=Le nom est requis.
error.field.taskschedulename.max=Le nom doit Ãªtre infÃ©rieur aux {max} caractÃ¨res.
error.field.cronexpression.null=Un horaire est requis.
error.field.cronexpression.range=L'horaire doit Ãªtre entre les caractÃ¨res {min} et {max}.
error.field.scheduledtaskid.null=Le type de tÃ¢che est requis.
error.field.taskschedulename.range=Le nom de l'horaire des tÃ¢ches est requis.
error.field.taskclassid.null=Le type de tÃ¢che est requis.
error.field.scheduledtaskname.range=Le nom doit Ãªtre entre les caractÃ¨res {min} et {max}.
error.field.taskscheduleid.null=Le calendrier des tÃ¢ches est requis.
error.messages.header=Les erreurs:

# Customer Account Threshold errors
error.field.lowbalance.null=Le bas solde doit Ãªtre entrÃ©
error.field.emergencycredit.null=Le crÃ©dit d'urgence doit Ãªtre entrÃ©
error.field.disconnect.null=DÃ©connecter doit Ãªtre entrÃ©
error.field.reconnect.null=La connexion doit Ãªtre entrÃ©e

# Questions
question.discard.changes=Voulez-vous supprimer les modifications actuelles?
question.discard.potential.changes=Si vous avez effectuÃ© des modifications et que vous n'avez pas encore enregistrÃ©, ils seront rejetÃ©s. Continuer?
option.yes=Annuler les modifications
option.no=Annuler
option.positive=Oui
option.negative=Non
question.delete=Voulez-vous supprimer l'Ã©lÃ©ment sÃ©lectionnÃ©?
dayprofile.question.delete=Voulez-vous supprimer l'Ã©lÃ©ment sÃ©lectionnÃ©? Cela Ã©liminera Ã©galement toutes les pÃ©riodes associÃ©es Ã  ce profil de jour.
question.confirm.installation.date.1=Confirmez que le compteur {0} a Ã©tÃ© installÃ© sur {1} Ã  {2}.
question.confirm.installation.date.2=Il est important que cette date et heure soient exactes.
question.confirm.link.to.customer=Client {0} est dÃ©jÃ  sur la page. Obtenir cet PointUsage ici vous assignera automatiquement ce client. Continuer?
question.close.tabs.1=Ne peut pas Ãªtre sauvegardÃ© alors que d'autres onglets, potentiellement susceptibles de faire rÃ©fÃ©rence Ã  cet Ã©lÃ©ment, sont ouverts.
question.close.tabs.2=Vous pouvez les fermer vous-mÃªme, puis essayer d'enregistrer Ã  nouveau - choisissez NON ou
question.close.tabs.3=Faut-il fermer automatiquement d'autres onglets pour vous (perdre des donnÃ©es non enregistrÃ©es) - choisir OUI?
option.confirm=Confirmer
option.continue=Continuer?

## Messages
message.saved={0} a Ã©tÃ© enregistrÃ©.

## Links
link.logout=DÃ©connexion
link.loggedin=Utilisateur:
link.group.change=Groupe de changement
link.group=Groupe:  
link.meters=Compteurs
link.customers=Les clients
link.groups=Groupes
link.menu=Menu
link.pricingstructure=Structure de prix
link.calendars=Calendriers
link.calendarsettings=ParamÃ¨tres du calendrier
link.auxchargeschedule=Horaire de charge auxiliaire
link.auxilliarytype=Type auxilliaire
link.supplygroup=Groupe d'approvisionnement
link.displaytokens=Jetons d'affichage
link.devicestores=Dispositifs
link.usergroup=Groupe d'accÃ¨s de l'utilisateur
link.accessgroups=Groupes d'accÃ¨s
link.search=Recherche
link.search.advanced=Recherche AvancÃ©e
link.search.meters.viewed=Derniers compteurs visionnÃ©s
link.search.meters.modified=Derniers compteurs modifiÃ©s
link.meter.readings=RelevÃ©s des compteurs
link.energybalancing=Ãquilibrage Ã©nergÃ©tique
link.energybalancing.meters=Compteurs d'Ã©quilibrage Ã©nergÃ©tique
link.analytics=Analytique
link.configuration=Configuration
link.tools=Outils
link.taskschedules=Horaires des tÃ¢ches
link.locationgroups=Groupes d'emplacement
link.about=Ã propos
link.appsettings=ParamÃ¨tres de l'application
link.global.ndp=NDP Globale
link.billingdet= Facteurs de Facturation

## Buttons ##
button.save=Sauvegarder
button.new=Nouveau
button.edit=Ãditer
button.back=Retour
button.cancel=Annuler
button.close=Fermer
button.select=SÃ©lectionner
button.delete=Effacer
button.viewentity=Voir l'entitÃ©
button.yes=Oui
button.no=Non
button.create=CrÃ©er
button.update=Mettre Ã  jour
button.viewtariffs=Afficher les tarifs
button.replacemeter=Remplacer le compteur
button.removemeter=Enlever le Compteur
button.displayunits=UnitÃ©s d'affichage
button.gettoken=Obtenir un jeton
button.saveaccount=Enregistrer le compte
button.addnew=Ajouter nouveau
button.editchargeschedule=Modifier l'horaire des frais
button.clear.group=Effacer le group
button.search=Recherche
button.clear=Effacer
button.view=Voir
button.add=Ajouter
button.remove=Retirer
button.export=Exporter
button.view.scheduledtasks=Afficher les tÃ¢ches planifiÃ©es
button.login=Connexion
button.logout=Quitter le site
button.set=Ensemble
button.show.inherited=Afficher les valeurs hÃ©ritÃ©es
button.send=Envoyer
button.viewtrans=Afficher les transactions

## Menus ##
menu.add=Ajouter
menu.update=Mettre Ã  jour
menu.delete=Effacer
menu.search=Recherche

## Record Statuses ##
status.active=actif
status.inactive=Inactive
status.deleted=Deleted

### Supply Group ###
supplygroups.header=Groupes d'approvisionnement
supplygroups.title=Groupes actuels d'approvisionnement
supplygroup.title=Groupe d'approvisionnement
supplygroup.name=Groupe d'approvisionnement
supplygroup.field.name=Nom
supplygroup.field.code=Code
supplygroup.field.keyrevisionnumber=NumÃ©ro de rÃ©vision clÃ©
supplygroup.field.keyexpirynumber=NumÃ©ro d'expiration clÃ©
supplygroup.field.status=Ãtat
supplygroup.field.active=actif
supplygroup.title.add=Ajouter un groupe d'approvisionnement
supplygroup.title.update=Mettre Ã  jour le groupe d'approvisionnement
supplygroup.error.save=Impossible de sauvegarder le groupe d'approvisionnement.

### Group Type ###
grouptypeshierarchies.title=Types de groupes et hiÃ©rarchies
grouptypes.header=Types de groupe
grouptypes.hierarchies.header=HiÃ©rarchies de groupe
grouptypes.title=Types de groupes actuels
grouptype.title=Type de groupe
grouptype.field.id=ID de type de groupe
grouptype.field.name=Nom
grouptype.field.name.help=Entrez le nom de ce type de groupe. Ceci est un champ obligatoire - l'enregistrement ne peut pas Ãªtre sauvegardÃ© sans celui-ci.
grouptype.field.description=La description
grouptype.field.description.help=Entrez une description de ce type de groupe.
grouptype.field.parent=Parent
grouptype.field.parent.help=Si ce type de groupe appartient Ã  une hiÃ©rarchie sous un autre type de groupe, sÃ©lectionnez ici le type de groupe parent.
grouptype.field.active=Actif
grouptype.field.active.help=Ce groupe est-il actif? Cochez la case pour activer ce type de groupe.
grouptype.field.status=Ãtat
grouptype.field.required=Champs obligatoires
grouptype.field.required.help=Pour les groupes de points d'utilisation, cela dÃ©termine si une sÃ©lection pour ce type de groupe est requise
grouptype.field.accessgroup=Groupe d'accÃ¨s
grouptype.field.locationgroup=Groupe de localisation
grouptype.field.access.location.group=AccÃ¨s / Emplacement
grouptype.field.feature=CaractÃ©ristiques
grouptype.field.available.feature=CaractÃ©ristiques disponibles
grouptype.field.assigned.feature=FonctionnalitÃ©s assignÃ©es
grouptype.field.available.feature.help=Si une caractÃ©ristique est marquÃ©e comme une fonction de sÃ©lection unique, elle ne peut Ãªtre sÃ©lectionnÃ©e que pour un seul type de groupe, une fois que le type de groupe a Ã©tÃ© utilisÃ© dans les donnÃ©es, cette affectation ne peut pas changer. 
grouptype.field.assigned.feature.help=FonctionnalitÃ©s attribuÃ©es Ã  ce type de groupe
grouptype.button.viewhierarchies=Afficher les hiÃ©rarchies
grouptype.error.noneselected=Aucun type de groupe sÃ©lectionnÃ© n'est sÃ©lectionnÃ©.
grouptype.error.parentcanthavehierarchy=Les types de groupe parent ne peuvent pas avoir de hiÃ©rarchies
grouptype.none=Group Type: Aucune sÃ©lection
grouptype.title.add=Ajouter un type de groupe
grouptype.title.update=Mettre Ã  jour le type de groupe
grouptype.error.save=Impossible de sauvegarder le type de groupe.
grouptype.error.accessgroup.users=Impossible de modifier le groupe d'accÃ¨s. Les utilisateurs existants sont affectÃ©s aux groupes d'accÃ¨s.
grouptype.error.accessgroup.customers=Impossible de modifier le groupe d'accÃ¨s. Il existe des clients existants utilisant les groupes.
grouptype.error.accessgroup.up=Impossible de modifier le groupe d'accÃ¨s. Il existe des points d'utilisation existants Ã  l'aide des groupes.
grouptype.error.accessgroup.pricing=Impossible de modifier le groupe d'accÃ¨s. Il existe des structures de prix existantes utilisant les groupes.
grouptype.error.accessgroup.aux=Impossible de modifier le groupe d'accÃ¨s. Il existe des horaires aux charge aux groupes.
grouptype.error.accessgroup.stores=Impossible de modifier le groupe d'accÃ¨s. Il existe des magasins d'appareils existants utilisant les groupes.
grouptype.error.name.duplicate=Le nom du type de groupe est dÃ©jÃ  utilisÃ©. Choisissez un autre nom.
grouptype.error.cannot.change.required=Impossible de modifier le paramÃ¨tre Â«requisÂ», groupe dÃ©jÃ  utilisÃ©.
grouptype.error.select.add.feature=SÃ©lectionnez une fonctionnalitÃ© Ã  ajouter.
grouptype.error.select.remove.feature=SÃ©lectionnez une fonction Ã  supprimer.
grouptype.error.cannot.set.feature.for.group=Cette fonctionnalitÃ© peut ne pas Ãªtre dÃ©finie pour ce type de groupe.
grouptype.error.cannot.remove.feature=Impossible de supprimer le paramÃ¨tre de fonctionnalitÃ©, le groupe dÃ©jÃ  utilisÃ©.
grouptype.error.save.feature=Impossible de sauvegarder le paramÃ¨tre de fonctionnalitÃ©.
grouptype.error.feature.not.multi.instance=Cette fonctionnalitÃ© ne peut Ãªtre attribuÃ©e qu'une seule fois, dÃ©jÃ  attribuÃ©e Ã  un autre groupe. 
grouptype.accessgroup.help=Le type de groupe d'accÃ¨s dÃ©termine quel type de groupe contrÃ´le l'accÃ¨s de l'utilisateur au site Web. Il est dÃ©fini une fois et ne peut pas Ãªtre modifiÃ©.
grouptype.locationgroup.help=Le type de groupe de localisation dÃ©termine le type de groupe qui contrÃ´le les donnÃ©es de localisation. Il est dÃ©fini une fois et ne peut pas Ãªtre modi

### Group Hierarchy ###
grouphierarchies.header=HiÃ©rarchies de groupe
grouphierarchies.title=HiÃ©rarchies de groupe actuelles
grouphierarchy.title=HiÃ©rarchie de groupe
grouphierarchy.field.id=ID de la hiÃ©rarchie de groupe
grouphierarchy.field.name=Nom
grouphierarchy.field.name.help=Entrez le nom de ce niveau dans la hiÃ©rarchie des groupes.
grouphierarchy.field.description=La description
grouphierarchy.field.active=Actif
grouphierarchy.field.parent=HiÃ©rarchie parentale
grouphierarchy.field.parent.none=Aucun
grouphierarchy.field.level=Niveau
grouphierarchy.delete.confirm=Ãtes-vous sÃ»r de vouloir supprimer la hiÃ©rarchie des groupes?
grouphierarchy.deleted=La hiÃ©rarchie des groupes a Ã©tÃ© supprimÃ©e avec succÃ¨s.
grouphierarchy.error.delete=Impossible de supprimer la hiÃ©rarchie des groupes.
grouphierarchy.error.delete.linked=Impossible de supprimer la hiÃ©rarchie de groupe telle qu'elle est utilisÃ©e.
grouphierarchy.error.save=Impossible de sauvegarder la hiÃ©rarchie du groupe.
grouphierarchy.error.update=Impossible de mettre Ã  jour l'Ã©lÃ©ment hiÃ©rarchique de groupe tel qu'il est utilisÃ©.
grouptype.current=Type de groupe actuel.
grouphierarchy.title.add=Ajouter une hiÃ©rarchie de groupe
grouphierarchy.title.update=Mettre Ã  jour le jury de groupe
grouphierarchy.error.unknown=HiÃ©rarchie de groupe inconnue.
grouphierarchy.error.access.root=Les donnÃ©es de hiÃ©rarchie du premier niveau du groupe d'accÃ¨s ne peuvent pas Ãªtre modifiÃ©es.

### Usage Point Groups ###
usagepointgroups.header=Groupes de points d'utilisation
usagepointgroups.title=Groupes actuels de points d'utilisation
usagepointgroups.instructions=SÃ©lectionnez un type de groupe:
usagepointgroup.title=Groupe de points d'utilisation
usagepointgroup.noselection.grouptype=Aucun type de groupe sÃ©lectionnÃ© n'est sÃ©lectionnÃ©.
usagepointgroup.field.id=ID de groupe de point d'utilisation
usagepointgroup.field.name=Nom
usagepointgroup.field.hierarchy=HiÃ©rarchie
usagepointgroup.help.grouptype=SÃ©lectionnez un type de groupe pour afficher ses groupes de points d'utilisation correspondants. Seuls les types de groupes avec les hiÃ©rarchies de groupe sont visibles ici.
usagepointgroup.delete.ask=Ãtes-vous sÃ»r de vouloir supprimer le groupe de points d'utilisation {0}?
usagepointgroups.help=Cliquez sur les icÃ´nes des donnÃ©es de l'arbre pour ajouter, modifier ou supprimer les donnÃ©es.
usagepointgroup.title.add=Ajouter un groupe de points d'utilisation
usagepointgroup.title.update=Mettre Ã  jour le groupe de points d'utilisation
usagepointgroup.error.entityid=Impossible de sauvegarder l'ID de l'entitÃ© GenGroup.

###Old Usage Point Group Page
usagepointgroup.field.description=Description
usagepointgroup.field.parent=Nom du parent
usagepointgroup.field.active=Actif
usagepointgroup.field.status=Ãtat
usagepointgroup.field.name.help=Entrez un nom pour le groupe - ce sera utilisÃ© pour l'identifier lors de l'ajout de points d'utilisation au groupe.
usagepointgroup.field.description.help=Entrez une description du groupe (facultatif).
usagepointgroup.field.parent.help=Si le groupe fait partie d'un groupe plus important, identifiez le groupe parent ici
usagepointgroup.field.status.help=Activer ou dÃ©sactiver ce groupe

## Groups
group.error.save=Impossible de sauvegarder le groupe.
group.error.delete=Impossible de supprimer le groupe.
group.error.delete.ap=Impossible de supprimer le groupe car il existe encore des points d'utilisation actifs.
group.error.entity.save=Impossible de lier les informations de contact au groupe.
group.error.threshold.save=Impossible de lier les informations de seuil au groupe.
group.error.ndp.schedule.save=Impossible de lier les informations NDP au groupe.
group.new.instructions=Entrez un nouveau
group.new.for=pour
group.current.none=Aucun groupe actuel n'a Ã©tÃ© dÃ©fini pour votre utilisateur.
group.delete.ask=Ãtes-vous sÃ»r de vouloir supprimer le {0} groupe?
groups.error.select.at.minimum=Au minimum, {0} doit Ãªtre sÃ©lectionnÃ©.

## Usage Point Workspace
usagepointworkspace.meter.saved.usagepoint.deactivation.failed=MÃ¨tre {0} EnregistrÃ©! (La dÃ©sactivation du point d'utilisation a Ã©chouÃ©!)
usagepointworkspace.meter.saved.usagepoint.deactivated=MÃ¨tre {0} enregistrÃ©! (Point d'utilisation dÃ©sactivÃ©!)
usagepointworkspace.meter.saved=MÃ¨tre {0} enregistrÃ©!
usagepointworkspace.meter.saved.attach.usagepoint=Compteur {0} sauvegardÃ©! Pour fixer ce compteur au point d'utilisation ci-dessous, sÃ©lectionnez la date d'installation et la structure de tarification, ainsi que l'activation.
usagepointworkspace.customer.saved.usagepoint.deactivation.failed=Client {0} EnregistrÃ©! (La dÃ©sactivation du point d'utilisation a Ã©chouÃ©!)
usagepointworkspace.customer.saved.usagepoint.deactivated=Client {0} enregistrÃ©! (Point d'utilisation dÃ©sactivÃ©!)
usagepointworkspace.customer.saved.usagepoint.failed=Client {0} sauvegardÃ© mais impossible de mettre Ã  jour le point d'utilisation! Contactez le support.
usagepointworkspace.customer.saved.usagepoint.updated=Client {0} sauvegardÃ© et le point d'utilisation mis Ã  jour.
usagepointworkspace.customer.saved=Client {0} enregistrÃ©!
usagepointworkspace.customer.saved.no.usage.point=Client {0} sauvegardÃ©, aucun point d'utilisation pour mettre Ã  jour.
usagepointworkspace.customer.unassigned.usagepoint.deactivation.failed=Le client {0} n'est plus affectÃ© au point d'utilisation {1} (La dÃ©sactivation du point d'utilisation a Ã©chouÃ©!)
usagepointworkspace.customer.unassigned.usagepoint.deactivated=Le client {0} n'est plus affectÃ© au point d'utilisation {1} (Point d'utilisation dÃ©sactivÃ©!)
usagepointworkspace.customer.assign.error.already.assigned=Le client est dÃ©jÃ  affectÃ© au point d'utilisation {0}
usagepointworkspace.customer.assign.error=Le client {0} n'est PAS affectÃ© au point d'utilisation {1} (la dÃ©sactivation du point d'utilisation a Ã©chouÃ©)
usagepointworkspace.customer.assigned.usagepoint.deactivated=Le client {0} est maintenant affectÃ© au point d'utilisation {1} (point d'utilisation dÃ©sactivÃ©)
usagepointworkspace.customer.assigned=Le client {0} est maintenant affectÃ© au point d'utilisation {1}
usagepointworkspace.error.meter.not.found=Compteurs introuvable
usagepointworkspace.error.meter.already.assigned=Le compteur {0} est dÃ©jÃ  affectÃ© au point d'utilisation {1}
usagepointworkspace.error.meter.unable.to.unassign=Impossible de ne pas assigner de courant au compteur
usagepointworkspace.error.meter.installdate.before.previous=La nouvelle date d'installation {0} ne peut pas Ãªtre AVANT la date d'installation du compteur prÃ©cÃ©dent {1}.
usagepointworkspace.error.meter.installdate.before.last.remove=Nouvelle date d'installation {0} ne peut pas Ãªtre AVANT la date de retrait du compteur prÃ©cÃ©dent {1}.
usagepointworkspace.error.meter.installdate.before.last.reading=Impossible de rÃ©affecter le point d'utilisation - la nouvelle date d'installation est avant la derniÃ¨re date de lecture du compteur actuel.
usagepointworkspace.error.meter.installdate.before.last.register.reading= Impossible de rÃ©attribuer le point d'utilisation - la nouvelle date d'installation est avant la derniÃ¨re date de lecture du registre du compteur actuel.
usagepointworkspace.error.meter.removedate.before.last.reading= Impossible d'enlever le compteur du point d'utilisation maintenant - la derniÃ¨re date de lecture du compteur actuel est supÃ©rieure.
usagepointworkspace.error.meter.removedate.before.last.register.reading= Impossible d'enlever le compteur du point d'utilisation maintenant - la derniÃ¨re date de lecture du registre du compteur actuel est supÃ©rieure.
usagepointworkspace.meter.assigned=Le compteur {0} est maintenant affectÃ© au point d'utilisation {1}
usagepointworkspace.meter.assigned.usagepoint.deactivated=Compteur {0} est maintenant assignÃ© au point d'utilisation {1} (Le point d'utilisation n'est pas actif)
usagepointworkspace.meter.removed=Compteur {0} a Ã©tÃ© supprimÃ© du point d'utilisation {1}
usagepointworkspace.meter.add.usagepoint.to.join.customer=Ajouter le point d'utilisation (ci-dessous) pour lier le compteur {0} et le client {1} l'un Ã  l'autre
usagepointworkspace.meter.add.usagepoint.to.join =Ajouter le point d'utilisation (ci-dessous) pour lier ce compteur Ã  un point d'utilisation
usagepointworkspace.assign.activate.usage.point.question=Point d'utilisation n'est pas actif pour le moment. Voulez-vous l'activer maintenant?
usagepointworkspace.assign.usage.point.activated=Point d'utilisation activÃ©.
usagepointworkspace.save.usage.point.inactive=Le point d'utilisation n'est pas actif.
usagepointworkspace.meter.customer.assigned=Le Compteur {0}, Le Client {2} sont maintenant assignÃ©s au point d'utilisation {1}

### Usage Point ###
usagepoint.groups.title=Groupes de points d'utilisation
usagepoint.info.title=Informations sur le point d'utilisation
usagepoint.title=Point d'utilisation
usagepoint.add.new=Ajouter le point d'utilisation
usagepoint.show.info=Afficher l'information sur le point d'utilisation
usagepoint.showing.info=Affichage de l'information sur le point d'utilisation
usagepoint.field.active.help=Ce point d'utilisation est-il actif? Cette option est dÃ©sactivÃ©e jusqu'Ã  ce qu'un compteur actif et un client soient liÃ©s Ã  ce point d'utilisation.
usagepoint.field.activated_date.help=DÃ©finissez la date Ã  laquelle ce point d'utilisation a Ã©tÃ© activÃ©. Cela ne peut Ãªtre rÃ©glÃ© qu'une seule fois. Ce champ est requis pour l'activation. Bien que l'enregistrement puisse Ãªtre enregistrÃ© sans lui, le point d'utilisation ne peut Ãªtre activÃ© que s'il est rempli correctement.
usagepoint.field.active=Actif
usagepoint.field.activated_date=Date activÃ©e
usagepoint.field.meter.installation_date=Date / heure de l'installation du compteur
usagepoint.field.meter.installation_date.meter.required=Un compteur doit Ãªtre installÃ© avant de dÃ©finir la date d'installation.
usagepoint.field.meter.installation_date.help=La date et l'heure oÃ¹ le compteur a Ã©tÃ© installÃ© Ã  ce point d'utilisation. Cela ne peut Ãªtre rÃ©glÃ© qu'une fois par mÃ¨tre. Ce champ est requis et un compteur doit Ãªtre affectÃ© avant de dÃ©finir la date d'installation.
usagepoint.field.name.help=Entrez le nom de ce point d'utilisation. Ceci est un champ obligatoire - l'enregistrement ne peut pas Ãªtre sauvegardÃ© sans celui-ci.
usagepoint.field.name=Nom du point d'utilisation
usagepoint.field.pricingstructure.help=SÃ©lectionnez la structure de prix. Ceci est un champ obligatoire - l'enregistrement ne peut pas Ãªtre sauvegardÃ© sans celui-ci.
usagepoint.field.lastmdcconnectcontrol=Dernier contrÃ´le GDC Connect
usagepoint.field.group.help=Ajouter ce point d'utilisation Ã  un groupe
usagepoint.field.group=Groupes de points d'utilisation
usagepoint.required.text=*== Obligatoire - Un compteur associÃ© est Ã©galement requis
usagepoint.required.activation.text=** == Obligatoire pour l'activation - Un compteur actif et un client sont Ã©galement requis pour l'activation
usagepoint.name.required=Le nom du point d'utilisation est requis
usagepoint.pricingstructure.required=Structure de prix requise
usagepoint.pricingstructure.change.illegal=La structure des prix ne peut pas Ãªtre modifiÃ©e maintenant. Aucun compteur n'est attachÃ©. RÃ©initialiser le choix.
usagepoint.save.error=L'Ã©chec du point d'utilisation a Ã©chouÃ©.
usagepoint.save.errors=Point d'utilisation non enregistrÃ©. Corrigez les erreurs affichÃ©es.
usagepoint.save.errors.meter.required=L'Ã©chec du point d'utilisation a Ã©chouÃ©. Un compteur associÃ© est requis
usagepoint.saved=UsagePoint {0} enregistrÃ©.
usagepoint.changes.cleared=Les modifications ont Ã©tÃ© effacÃ©es.
usagepoint.no.meter=Un mÃ¨tre doit Ãªtre sauvegardÃ© avant d'ajouter un point d'utilisation.
usagepoint.txn.history=Historique des transactions
usagepoint.txn.history.description=Transactions clients antÃ©rieures pour ce point d'utilisation
usagepoint.txn.meterreadings=RelevÃ©s des compteurs
usagepoint.history=Historique des points d'utilisation
usagepoint.history.description=Les modifications prÃ©cÃ©dentes apportÃ©es Ã  ce point d'utilisation (les modifications sont mises en surbrillance)
usagepoint.reports=Rapports pour le point d'utilisation
usagepoint.recharge.history=MÃ©thodes d'utilisation Historique de recharge
usagepoint.retailers=Commerces Ã  proximitÃ©
usagepoint.reports.general=Rapports gÃ©nÃ©raux pour les points d'utilisation
usagepoint.meter.reports=Rapports pour le point d'utilisation
usagepoint.coords=Ajoutez les coordonnÃ©es Latitude et Longitude au point d'utilisation, afin de voir les dÃ©taillants voisins.
usagepoint.txn.reference=RÃ©fÃ©rence
usagepoint.txn.receipt=NumÃ©ro de rÃ©ception
usagepoint.txn.date=Date
usagepoint.txn.meter=Compteur
usagepoint.txn.customer=Client
usagepoint.txn.type=Type
usagepoint.txn.client=Client
usagepoint.txn.term=Terme
usagepoint.txn.ref=RÃ©fÃ©rence
usagepoint.txn.revref=Reflet d'inversion
usagepoint.txn.isreversed=RenversÃ©
usagepoint.txn.amt=Montant
usagepoint.hist.serial=En sÃ©rie
usagepoint.hist.date=Date
usagepoint.hist.customer=Client
usagepoint.hist.datemod=Date Modifiee
usagepoint.hist.byuser=Par l'utilisateur
usagepoint.hist.user=Utilisateur
usagepoint.hist.action=Action
usagepoint.hist.status=Ãtat
usagepoint.hist.name=Nom
usagepoint.hist.meter=Compteur
usagepoint.hist.custagree=Contrat client
usagepoint.hist.service=Emplacement du service
usagepoint.recharge.title=Rechargement de point d'utilisation
usagepoint.recharge.date=Date
usagepoint.recharge.currency=R
usagepoint.recharge.kwh=kWh
usagepoint.history.filter=Filtre
usagepoint.txn.filter=Filtre
usagepoint.group.required=Groupe requis
usagepoint.onegroup.required=Au moins un groupe doit Ãªtre sÃ©lectionnÃ©
usagepoint.group.error.delete=Impossible de supprimer le groupe de points d'utilisation.
usagepoint.group.error.save=Impossible de sauvegarder le groupe du point d'utilisation.
usagepoint.group.no.value=Aucune valeur dÃ©finie
usagepoint.calculate.tariff=Calculer le tarif
usagepoint.calculate.tariff.error=Une erreur s'est produite lors du calcul du tarif. 
usagepoint.calculate.tariff.connection.error=Aucune rÃ©ponse reÃ§ue du service.
usagepoint.calculate.tariff.ok=Tarification calculÃ©e avec succÃ¨s
usagepoint.recharge.chart.title=Recharges de point d'utilisation
usagepoint.recharge.chart.subtitle=Recharge des montants
usagepoint.recharge.chart.xtitle=Date Heure
usagepoint.recharge.chart.ytitle=CoÃ»t
usagepoint.recharge.chart.price=Prix
usagepoint.recharge.chart.purchaseprice=Prix d'achat
usagepoint.error.meterandcustomer.required=Un compteur actif et un client sont requis pour l'activation
usagepoint.meter.installed.at=Le compteur {0} a Ã©tÃ© installÃ© sur {1} Ã  {2}
usagepoint.installation.date.required=La date et l'heure d'installation du compteur sont nÃ©cessaires.
usagepoint.name.instructions=Recherche par Nom du point utilisation
usagepoint.partial.search=Pas de correspondance exacte avec le point d'utilisation pour {0}. Faire une recherche avancÃ©e...
usagepoint.fetch=Chercher un point d'utilisation
usagepoint.fetch.help=Chercher un point d'utilisation existant.
usagepoint.assigned=Ce point d'utilisation dispose dÃ©jÃ  d'un compteur ou d'un client qui lui est assignÃ©. Ne sont pas admissibles Ã  un Cherche.
usagepoint.name.instr=Entrez le nom du point d'utilisation
usagepoint.find=Trouver le point d'utilisation
usagepoint.fetch.duplicate=Point dâutilisation {0} dÃ©jÃ  sur la page.
usagepoint.install.date.required=La date et l'heure auxquelles le compteur ci-dessus a Ã©tÃ© installÃ© Ã  ce point d'utilisation {0}.
usagepoint.new.pricingstructure.required=Structure d'Ã©valuation du point d'utilisation sÃ©lectionnÃ© {1} incompatible avec Compteur {0}.
usagepoint.error.new.installdate.before.removal=La date d'installation ne peut pas Ãªtre AVANT la date de la derniÃ¨re suppression sur le point d'utilisation: {0}
usagepoint.error.new.installdate.before.removaldate.meter=La date d'installation ne peut pas Ãªtre AVANT la derniÃ¨re date de retrait du compteur: {0}
usagepoint.saved.linked.meter=LiÃ© au Compteur {0}.
usagepoint.saved.linked.customer= LiÃ© au Client {0}.
moxiechart.abbrev.month.categories="Jan", "FÃ©v", "Mar", "Avr", "Mai", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "DÃ©c"

### Group Entity ###
groupentity.header=Informations sur le groupe
groupentity.title=Informations de contact
groupentity.contact.title=Contact
groupentity.field.contact.name=Nom du Contact
groupentity.field.contact.number=Numero du Contact
groupentity.field.contact.email=Email du Contact 
groupentity.field.contact.address=Adresse de contact
groupentity.field.contact.taxref=Contactez la rÃ©fÃ©rence ImpÃ´t
groupentity.error.save=Impossible de sauvegarder les informations du groupe.

### Group Thresholds ###
groupthreshold.title=Informations sur les seuils du compte client
groupthreshold.meter.disconnect.text=Seuil de dÃ©connexion
groupthreshold.meter.disconnect.help=Seuil au-dessous duquel le client sera dÃ©connectÃ©.
groupthreshold.emergency.credit.text=Seuil de crÃ©dit d'urgence
groupthreshold.emergency.credit.help=Seuil au-dessous duquel le solde du client entraÃ®nera une demande de dÃ©connexion et d'activation au mdc s'il est pris en charge.
groupthreshold.meter.reconnect.text=Reconnecter le seuil
groupthreshold.meter.reconnect.help=Seuil au-dessus duquel le client sera reconnectÃ©.
groupthreshold.low.balance.text=Seuil d'avertissement de faible Ã©quilibre
groupthreshold.low.balance.help=Seuil pour alerter le client d'un faible solde dans son compte.
groupthreshold.global.source.label=ParamÃ¨tres globaux
groupthreshold.parent.source.label=ParamÃ¨tres parentaux
groupthreshold.children.change.alert=Des hiÃ©rarchies infÃ©rieures sans mÃªme seuils seront mises Ã  jour avec ces modifications. Continuer?
groupthreshold.null.thresholds.alert=Les seuils laissÃ©s en blanc seront dÃ©sactivÃ©s. Continuer?
groupthreshold.error.save.thresholds=Impossible de sauvegarder les informations de seuil.
groupthreshold.revert.parent.global=Ãtes-vous sÃ»r de supprimer? Ce noeud va maintenant hÃ©riter de {0} seuil ParamÃ¨tres.
groupthreshold.error.disconnect.greater.emergency.credit=Le dÃ©connexion doit Ãªtre infÃ©rieur ou Ã©gal au crÃ©dit d'urgence
groupthreshold.error.disconnect.greater.reconnect=DÃ©connecter doit Ãªtre plus petit ou Ã©gal Ã  Reconnecter
groupthreshold.error.emergency.credit.greater.low.balance=Le crÃ©dit d'urgence doit Ãªtre infÃ©rieur ou Ã©gal au faible solde

### Global Non-Disconnect Periods ###
global.ndp.tab.heading=NDP Globale
global.ndp.heading=PÃ©riodes globales de non-dÃ©connexion
global.ndp.none=Il n'y a pas de calendrier NDP Globale, appuyez sur le bouton CrÃ©er.
global.ndp.schedule.new.added=Le calendrier global NDP a Ã©tÃ© crÃ©Ã©.
global.ndp.schedule.activation.saved=Calendrier NDP mondial enregistrÃ©.

### Group Non-Disconnect Periods ###
ndp.active.instruction=Un calendrier NDP peut Ãªtre crÃ©Ã© et fonctionnÃ©, mais ne sera appliquÃ© que s'il est actif. L'horaire ne peut Ãªtre activÃ© que si au moins un jour NDP avec des heures a Ã©tÃ© entrÃ©. Soit une saison avec un jour ou un jour spÃ©cial.
ndp.schedule.title=Calendrier du NPD
ndp.schedule.active=Actif
ndp.schedule.active.help= Ce programme est-il actif? Cochez la case pour activer cette programmation - ne peut Ãªtre effectuÃ©e que lorsque au moins un jour NDP a Ã©tÃ© saisi.
ndp.schedule.delete.button=Supprimer L'horaire
ndp.disclosurePanel.title=PÃ©riodes de non-dÃ©connexion
ndp.seasons.title=Saisons
ndp.season.day.title=Jours de saison
ndp.season.day.description=Entrez une date de dÃ©but et de fin pour la saison, suivie des heures NDP par jour de la semaine.
ndp.assign.season.start=Date de dÃ©but
ndp.assign.season.start.help=Entrez la date de dÃ©but de la saison
ndp.assign.season.end=Date de fin
ndp.assign.season.end.help=Entrez la date de fin de saison
ndp.per.day.title=NDP par jour
ndp.days.of.week=Jour de la semaine
ndp.assign.dayperiod.start=Heure de dÃ©but
ndp.assign.dayperiod.start.help=Le moment oÃ¹ le NPD commence
ndp.assign.dayperiod.start.hour=Heure
ndp.assign.dayperiod.start.minute=Minute
ndp.assign.dayperiod.end=Heure de fin
ndp.assign.dayperiod.end.help=Le moment oÃ¹ le NPD finit
ndp.assign.dayperiod.end.hour=Heure
ndp.assign.dayperiod.end.minute=Minute
ndp.assign.dayperiod.saved=Le temps NDP a Ã©tÃ© sauvegardÃ©.
ndp.assign.dayperiod.deleted=Le temps NDP a Ã©tÃ© supprimÃ©.
ndp.assign.dayperiod.error.end.before.start=L'heure de fin ne peut pas Ãªtre avant l'heure de dÃ©but
ndp.assign.dayperiod.error.time.already.assigned=Le temps sÃ©lectionnÃ© a dÃ©jÃ  Ã©tÃ© assignÃ©.
ndp.assign.dayperiod.nulls=Toutes les valeurs de dÃ©but et de fin doivent Ãªtre saisies. Ne peut Ãªtre vide.
ndp.days.title=Jours NDP
ndp.add.season.button=Ajouter Saison
ndp.assign.season.error.end.before.start=Fin ne peut pas Ãªtre avant DÃ©but
ndp.assign.season.error.date.already.assigned=Ces dates se chevauchent avec une autre saison.
ndp.assign.season.error.time.already.assigned=Le temps sÃ©lectionnÃ© a dÃ©jÃ  Ã©tÃ© assignÃ©.
ndp.weekdays=Lundi,Mardi,Mercredi,Jeudi,Vendredi,Samedi,Dimanche
ndp.children.change.alert=Les hiÃ©rarchies infÃ©rieures sans NDP seront Ã©galement mises Ã  jour avec ces modifications. Continuer?
ndp.error.save.schedule=Impossible de sauvegarder les informations NDP Schedule.
ndp.abort.save=Vous avez choisi de ne pas sauvegarder ce calendrier NPD.
ndp.error.save.season=Impossible de sauvegarder la saison NDP.
ndp.error.save.day=Impossible de sauvegarder les temps de jours NDP.
ndp.revert.parent.global=Ãtes-vous sÃ»r de supprimer? Ce noeud va maintenant hÃ©riter {0} ParamÃ¨tres NDP.
ndp.children.delete.alert=Les hiÃ©rarchies infÃ©rieures avec le mÃªme programme NDP seront Ã©galement renvoyÃ©es vers {0} ParamÃ¨tres NDP. Continuer?
ndp.error.delete.day.profiles=Impossible de supprimer les profils de jour NDP.
ndp.error.delete.season=Impossible de supprimer une saison NDP.
ndp.error.delete.special.days=Impossible de supprimer les journÃ©es spÃ©ciales du NPD.
ndp.error.delete.schedule=Impossible de supprimer l'annexe NDP.
ndp.new.season.button=Ajouter une nouvelle Saison
ndp.new.special.day.button=Ajouter un jour spÃ©cial
ndp.special.day.column.heading=Jour
ndp.no.change.continue=Rien n'a changÃ©. Continuer?
ndp.day.panel.changed=Des changements ont Ã©tÃ© apportÃ©s Ã  cette saison. Abandon ces changements et continuez avec l'annulation?
ndp.schedule.activation.no.change=Pas de changement d'Ã©tat actif.
ndp.schedule.new.added=Un nouveau programme NDP a Ã©tÃ© ajoutÃ© Ã  ce groupe et aux sous-groupes pertinents.
ndp.schedule.activation.saved=Calendrier NDP sauvegarÃ©.
ndp.schedule.deleted=Le programme NDP a Ã©tÃ© supprimÃ© de ce groupe et des sous-groupes pertinents.
ndp.season.saved=Saison NDP enregistrÃ©e.
ndp.day.profile.confirm.delete=Confirmer la suppression du profil du jour pour {0}?
ndp.day.profile.confirm.delete.and.deactivate=Suppression du jour Profil pour {0} EntraÃ®nera une dÃ©sactivation du programme NPD lors de la mise Ã  jour de la saison, car c'est le seul temps NPD pour ce programme. Continuer?
ndp.season.confirm.delete=Confirmer la suppression de la saison {0}
ndp.season.deleted=Saison NDP supprimÃ©e.
ndp.season.confirm.delete.and.deactivate=Suppression de la saison {0} EntraÃ®nera la dÃ©sactivation du programme NDP, car il contient le (s) seul (s) temps (s) NDP pour ce programme. Continuer?
ndp.special.day.confirm.delete=Confirmer la suppression du jour spÃ©cial {0}
ndp.special.day.confirm.delete.and.deactivate=Suppression d'une journÃ©e spÃ©ciale {0} EntraÃ®nera la dÃ©sactivation du programme NDP, car il contient le (s) seul (s) temps (s) NDP pour ce programme. Continuer?
ndp.special.day.deleted=Jour spÃ©cial supprimÃ©.
ndp.special.day.title=Jours spÃ©cials
ndp.special.day.time.title= Temps de Jour spÃ©cial NDP
ndp.special.day.description=Entrez le jour et le mois du Jour spÃ©cial, suivis des heures NDP pour ce jour.
ndp.assign.special.day=Date spÃ©ciale du jour
ndp.assign.special.day.help=Entrez le jour et le mois du jour spÃ©cial.
ndp.assign.special.day.duplicate=Date du jour spÃ©ciale en copie.
ndp.special.day.panel.changed=Des changements ont Ã©tÃ© apportÃ©s Ã  ce jour spÃ©cial. Abandon les changements et continuez avec l'annulation?
ndp.special.day.times.confirm.delete.and.deactivate=Suppression des temps NDP pour {0} entraÃ®nera la dÃ©sactivation du programme NDP lors de la mise Ã  jour du jour spÃ©cial, car c'est le seul temps NPD pour cet horaire. Continuer?
ndp.special.day.saved=Jour spÃ©cial NDP sauvegardÃ©.
ndp.error.save.special.day=Impossible de sauver le Jour SpÃ©cial NDP.
ndp.global.none.found=Il n'y a pas de calendrier global de NPD. Allez crÃ©er un dans la section de configuration.
ndp.inherited.global.none=Il n'y a pas d'agenda Global NDP ou il n'est pas encore actif. Aucune pÃ©riode NDP globale ne peut Ãªtre hÃ©ritÃ©e!

### Customer Agreement ###
customeragreement.title=Contrat client

### Pricing Structures ###
pricingstructures.header=Structures de tarification
pricingstructures.title=Structures de tarification actuelles
pricingstructure.title=Structure de prix
pricingstructure.title.new=Nouvelle structure de tarification
pricingstructure.title.edit=Modifier la structure des prix
pricingstructure.field.name=Nom
pricingstructure.field.description=La description
pricingstructure.field.status=Ãtat
pricingstructure.field.active=Actif
pricingstructure.field.name.help=Le nom de cette structure de prix doit Ãªtre unique
pricingstructure.field.description.help=Une description de cette structure tarifaire
pricingstructure.field.active.help=Que la structure de prix soit active ou non.
pricingstructure.field.type=Type
pricingstructure.field.startdate=Date de dÃ©but
pricingstructure.field.tariffs=# Tarifs
pricingstructure.error.save=Impossible d'enregistrer la structure de prix.
pricingstructure.field.serviceresource.help=S'il n'y a pas de tarifs, vous pouvez sÃ©lectionner la ressource de service pour la structure de prix.
pricingstructure.field.metertype.help=S'il n'y a pas de tarifs, vous pouvez sÃ©lectionner le type de compteur pour la structure de prix.
pricingstructure.field.paymentmode.help=S'il n'y a pas de tarifs, vous pouvez modifier le mode de paiement pour la structure de tarification.
pricingstructure.tariffs.prt.none=Il n'y a pas de types de tarifs disponibles pour la ressource de service de la structure de prix actuelle, le type de compteur et la combinaison de mode de paiement.
pricingstructure.tariffs.ui.none=Aucun formulaire de tarif disponible entrÃ© pour Ãªtre enregistrÃ©.
pricingstructure.error.tariff.load=Erreur lors du chargement des donnÃ©es du tarif. Impossible d'afficher les champs du tarif et les valeurs correspondantes.
pricingstructure.name.duplicate=Duplique le nom {0} pour une structure de tarification. SpÃ©cifiez un nom unique.
pricingstructure.error.active=La structure des prix n'a pas de tarif actuel. Ne peut pas Ãªtre actif.
pricingstructure.error.deactivate=La structure des prix est utilisÃ©e. Impossible de dÃ©sactiver.

### Tariffs ###
tariffs.header=Tarifs
tariffs.title=Tarifs actuels
tariff.title=Tarif
tariff.title.add=Ajouter un nouveau tarif
tariff.title.edit=Modifier le tarif
tariff.title.view=Afficher le tarif
tariff.field.name=Nom
tariff.field.name.help=Nom de ce tarif, doit Ãªtre unique
tariff.field.description=La description
tariff.field.description.help=Une description de ce tarif
tariff.field.startdate=Date de dÃ©but
tariff.field.startdate.help=La date Ã  laquelle ce tarif sera activÃ©. Aucun autre tarif ne peut commencer Ã  cette date et il doit s'agir d'une date ultÃ©rieure.
tariff.title.type=Type de tarif DÃ©tails
tariff.field.type=Type
tariff.field.unitprice=Prix unitaire
tariff.field.tax=Pourcentage de la taxe
tariff.field.tax.help=Pourcentage de 14%
tariff.field.free.units.descrip=Description des unitÃ©s gratuites mensuelles
tariff.field.free.units=UnitÃ©s gratuites mensuelles
tariff.field.free.units.help=UnitÃ©s qui sont dÃ©livrÃ©es gratuitement.
tariff.field.groupthreshold=Seuil de groupe
tariff.field.groupthreshold.help=Le seuil en kWh d'utilisation du groupe pour le mois aprÃ¨s lequel le prix de seuil prend effet
tariff.field.thresholdprice=Prix de seuil
tariff.field.thresholdprice.help=Prix par kWh (aprÃ¨s avoir franchi le seuil)
tariff.field.price=Prix
tariff.field.baseprice=Prix de Base
tariff.field.threshold=Seuil
tariff.field.threshold.help=Prix par kWh
tariff.field.step1=Ãtape 1
tariff.field.step2=Ãtape 2
tariff.field.step3=Ãtape 3
tariff.error.numeric.value=La valeur doit Ãªtre numÃ©rique
tariff.error.save=Impossible de sauvegarder le tarif.
tariff.error.save.duplicate=Impossible d'enregistrer le tarif, un autre tarif avec le mÃªme nom existe dÃ©jÃ .
tariff.error.load=Impossible de charger des donnÃ©es tarifaires.
tariff.field.block=Blocs
tariff.field.block.single=Bloc
tariff.error.freeunits.positive=Doit Ãªtre une valeur positive.
tariff.error.freeunits.decimal.limit=Les unitÃ©s ne peuvent avoir qu'une dÃ©cimale
tariff.blocks.error=Les blocs ont besoin d'un prix unitaire et des valeurs seuils valides.
tariff.blocks.error.incomplete=Les blocs ont besoin d'un prix unitaire pour une valeur seuil.
tariff.blocks.error.last=Seul le bloc final ne devrait pas avoir de seuil.
tariff.blocks.error.last.none=Le bloc final ne devrait pas avoir de seuil.
tariff.blocks.error.increasing.thresholds=Les seuils pour un bloc doivent Ãªtre supÃ©rieurs Ã  zÃ©ro et supÃ©rieurs au seuil du bloc prÃ©cÃ©dent.
tariff.blocks.error.none=Des blocs sont nÃ©cessaires.
tariff.tou.thinsmart.season=Saison
tariff.tou.thinsmart.period=PÃ©riode
tariff.tou.thinsmart.readingtype=Type de lecture du compteur
tariff.tou.thinsmart.rate=Taux
tariff.tou.thinsmart.chargeunits=UnitÃ©s
tariff.tou.thinsmart.specialday=Jour spÃ©cial
tarif.adv.settings.header=RÃ©glages avancÃ©s
tariff.field.pricesymbol=Symbole de la monnaie
tariff.field.pricesymbol.help=Le symbole monÃ©taire Ã  utiliser pour le prix.
tariff.field.unitsymbol=Unit Symbol
tariff.field.unitsymbol.help=Le symbole des unitÃ©s monÃ©taires Ã  utiliser pour le prix.
tariff.field.amountrounding=Mode arrondissement
tariff.field.amountrounding.help=Le mode arrondissement pour les montants.
tariff.field.amountprecision=QuantitÃ© de prÃ©cision
tariff.field.amountprecision.help=La prÃ©cision pour les montants.
tariff.field.unitsrounding=Arrondi de l'unitÃ©
tariff.field.unitsrounding.help=Le mode arrondissement Ã  utiliser pour les unitÃ©s.
tariff.field.unitsprecision=PrÃ©cision de l'unitÃ©
tariff.field.unitsprecision.help=La prÃ©cision pour les unitÃ©s.
tariff.field.taxrounding=Arrondissement des impÃ´ts
tariff.field.taxrounding.help=Le mode arrondissement de l'impÃ´t.
tariff.field.taxprecision=PrÃ©cision impÃ´t
tariff.field.taxprecision.help=La prÃ©cision Ã  utiliser pour l'impÃ´t.
tariff.error.field.pricesymbol=Un symbole de devise est requis.
tariff.error.field.unitsymbol=Le symbole de l'unitÃ© est requis.
tariff.error.field.amountrounding=Le mode arrondissement est requis.
tariff.error.field.unitsrounding=Le mode arrondissement est requis.
tariff.error.field.taxrounding=Le mode arrondissement est requis.
tariff.error.field.amountprecision=La prÃ©cision doit Ãªtre une valeur positive.
tariff.error.field.unitsprecision=La prÃ©cision doit Ãªtre une valeur positive.
tariff.error.field.taxprecision=La prÃ©cision doit Ãªtre une valeur positive.
tariff.readonly=Le tarif a commencÃ© et est en lecture seule.

### Tou Calendar ###
calendar.settings.header=ParamÃ¨tres du calendrier
calendar.settings.title=ParamÃ¨tres du calendrier
calendar.season.title=Configuration des saisons
calendar.season.current.title=Les saisons actuelles
calendar.season.description=Ajouter ou mettre Ã  jour les saisons.
calendar.season.field.name=Nom
calendar.season.field.active=Actif
calendar.season.add=Ajouter saison
calendar.season.update=Mise Ã  jour de la saison
calendar.season.field.name.help=Entrez un nom pour cette saison. Ceci est un champ obligatoire.
calendar.season.field.active.help=L'Ã©tat d'activitÃ© actuel de cet Ã©lÃ©ment
season.error.update=La saison n'a pas pu Ãªtre mise Ã  jour.
season.error.save=La saison n'a pas pu Ãªtre enregistrÃ©e.
season.error.delete=La saison est en cours d'utilisation et ne peut pas Ãªtre supprimÃ©e.
calendar.season.deleted={0} la saison a Ã©tÃ© supprimÃ©e avec succÃ¨s.
calendar.period.deleted={0} la pÃ©riode a Ã©tÃ© supprimÃ©e avec succÃ¨s.

calendar.period.title=PÃ©riodes de configuration
calendar.period.current.title=PÃ©riodes actuelles
calendar.period.description=Ajouter et mettre Ã  jour les pÃ©riodes (p. Ex. Pointe, Creuse, Standard).
calendar.period.field.name=Nom
calendar.period.field.code=Code
calendar.period.field.active=Actif
calendar.period.add=Ajouter une pÃ©riode
calendar.period.update=PÃ©riode de mise Ã  jour
calendar.period.field.name.help=Entrez un nom pour cette pÃ©riode. Ceci est un champ obligatoire.
calendar.period.field.code.help=Entrez un code pour cette pÃ©riode. Il s'agit d'un code abrÃ©gÃ© unique pour la pÃ©riode. Ceci est un champ obligatoire.
calendar.period.field.active.help=L'Ã©tat d'activitÃ© actuel de cet Ã©lÃ©ment
period.error.save=La pÃ©riode n'a pas pu Ãªtre enregistrÃ©e.
period.error.delete=La pÃ©riode est utilisÃ©e et ne peut pas Ãªtre supprimÃ©e.

calendars.title=Calendriers
calendars.heading=Calendriers de configuration
calendars.description=Les calendriers actuels sont rÃ©pertoriÃ©s, les ajouter ou les modifier en utilisant les formulaires ci-dessous
calendar.field.name=Nom du calendrier
calendar.field.name.help=Entrez un nom pour ce calendrier.
calendar.field.description=La description
calendar.field.description.help=Entrez une description de ce calendrier.
calendar.field.active=Actif
calendar.field.active.help=L'Ã©tat d'activitÃ© actuel de ce calendrier
calendar.title=Calendrier
calendar.add=Ajouter un nouveau calendrier
calendar.update=Calendrier de mise Ã  jour
calendar.changes.cleared=Changements de calendrier effacÃ©s
calendar.save.errors=Impossible d'enregistrer le calendrier
calendar.complete=AchevÃ©e
calendar.incomplete=Incomplet
calendar.optional=Optionnel
calendar.duplicate=Nom du calendrier en double. Choisissez un nom unique.

calendar.assign.season.heading=Attribuer des dates Ã  des saisons pour le calendrier
calendar.assign.season.title=Dates de la saison
calendar.assign.season.description=Entrez une date de dÃ©but et de fin pour la saison sÃ©lectionnÃ©e.
calendar.assign.season.form.heading=Affecter des dates
calendar.assign.season=Saison
calendar.assign.season.help=SÃ©lectionner une Saison
calendar.assign.season.start=Date de dÃ©but
calendar.assign.season.start.help=Entrez la date de dÃ©but de la saison
calendar.assign.season.end=Date de fin
calendar.assign.season.end.help=Entrez la date de fin de saison
calendar.assign.season.deleted=Les dates de la saison ont Ã©tÃ© supprimÃ©es.
calendar.assign.season.error.end.before.start=Fin ne peut pas Ãªtre avant DÃ©marrer
calendar.assign.season.error.date.already.assigned=La date est dÃ©jÃ  attribuÃ©e Ã  une saison
calendar.assign.season.error.select.season=SÃ©lectionnez une saison dans la liste

calendar.assign.period.title=Profil du jour
calendar.assign.period.description=Affectez les heures du jour aux PÃ©riodes
calendar.assign.period=PÃ©riode 
calendar.assign.period.help=SÃ©lectionnez une pÃ©riode
calendar.assign.period.start=Heure de dÃ©but  
calendar.assign.period.start.help=Le moment oÃ¹ la pÃ©riode commence   
calendar.assign.period.start.hour=Heure  
calendar.assign.period.start.minute=Minute    
calendar.assign.period.end=Heure de fin  
calendar.assign.period.end.hour=Heure   
calendar.assign.period.end.minute=Minute    
calendar.assign.period.saved=La pÃ©riode a Ã©tÃ© enregistrÃ©e.
calendar.assign.period.cleared=Les changements de pÃ©riode ont Ã©tÃ© effacÃ©s.
calendar.assign.period.deleted=La pÃ©riode a Ã©tÃ© supprimÃ©e.
calendar.assign.period.error.end.before.start=L'heure de fin ne peut pas Ãªtre avant l'heure de dÃ©but
calendar.assign.period.error.time.already.assigned=L'heure sÃ©lectionnÃ©e a dÃ©jÃ  Ã©tÃ© attribuÃ©e.
calendar.assign.period.nulls=Toutes les valeurs de dÃ©but et de fin doivent Ãªtre entrÃ©es. Ne peut Ãªtre vide.  

calendar.dayprofiles.heading=Configurer les profils de jour pour le calendrier
calendar.dayprofiles.title=Profils de jour
calendar.dayprofiles.description=CrÃ©ez des jours qui sont divisÃ©s en pÃ©riodes spÃ©cifiques.

calendar.dayprofile.field.name=Nom de profil
calendar.dayprofile.field.code=Code
calendar.dayprofile.field.active=Actif
calendar.dayprofile.field.name.help=Entrez un nom pour ce jour de profil
calendar.dayprofile.field.code.help=Entrez un code abrÃ©gÃ© pour ce jour de profil
calendar.dayprofile.field.active.help=L'Ãtat de l'activitÃ© actuelle de ce jour
calendar.dayprofile.error.save=Impossible d'enregistrer un profil de jour
calendar.dayprofile.error.first.unassign=Impossible de supprimer un profil de jour qui est actuellement affectÃ© Ã  une saison de calendrier. DÃ©signez d'abord le profil du jour, puis supprimez.
calendar.dayprofile.error.special.day.first.unassign=Impossible de supprimer un jour de profil qui est actuellement affectÃ© Ã  une journÃ©e spÃ©ciale. Supprimez d'abord le jour spÃ©cial, puis supprimez le profil du jour. 
calendar.dayprofile.deleted=Profil du jour supprimÃ©
calendar.dayprofile.saved=Changements de profil jour enregistrÃ©s
calendar.dayprofile.cleared=Changements de profil jour effacÃ©s

calendar.assign.dayprofile.heading=Affecter des profils de jour au calendrier
calendar.assign.dayprofile.description=Pour chaque saison qui a Ã©tÃ© assignÃ©e au calendrier, spÃ©cifiez le profil du jour pour chaque jour de la semaine.
calendar.assign.dayprofile.cleared=Affectez les changements de profil journalisÃ©s effacÃ©s.
calendar.assign.dayprofile.saved=Affectez les changements de profil jour enregistrÃ©s.
calendar.assign.dayprofile.error.save=Erreur lors de l'attribution des profils de jour

calendar.specialday.heading=Jours spÃ©ciaux de configuration pour le calendrier
calendar.specialday.title=Configuration des jours spÃ©ciaux
calendar.specialday.description=Affecter des profils de jour Ã  des jours spÃ©cifiques
calendar.specialday.field.name=Nom du jour spÃ©cial
calendar.specialday.field.name.help=Un nom unique pour le jour.
calendar.specialday.field.active=Actif
calendar.specialday.field.active.help=L'Ã©tat d'activitÃ© actuel de ce jour spÃ©cial
calendar.specialday.field.day=journÃ©e
calendar.specialday.field.month=Mois
calendar.specialday.field.dayprofile=Profil du jour
calendar.specialday.field.dayprofile.help=SÃ©lectionnez le profil du jour
calendar.specialday.field.dayprofile.error=Le profil du jour doit Ãªtre saisi pour le jour spÃ©cial.
calendar.specialday.add=Ajouter un jour spÃ©cial
calendar.specialday.update=Mise Ã  jour du jour spÃ©cial
calendar.special.day.deleted=JournÃ©e spÃ©ciale supprimÃ©e.   
calendar.specialday.error.date.already.assigned.to=Cette date a dÃ©jÃ  Ã©tÃ© attribuÃ©e Ã  un jour spÃ©cial

calendar.readOnly=Remarque: Ce calendrier ne peut pas Ãªtre mis Ã  jour car il est dÃ©jÃ  utilisÃ© par les structures de prix suivantes: {0}

### Aux Charge Schedule ###
auxchargeschedules.header=Horaires de charge auxiliaire
auxchargeschedules.title=Horaires de charge auxiliaire actuels
auxchargeschedule.title=Horaire de charge auxiliaire
auxchargeschedule.title.add=Ajouter un programme de frais auxiliaire
auxchargeschedule.title.update=Mettre Ã  jour le programme de frais auxiliare
auxchargeschedule.field.name=Nom
auxchargeschedule.field.name.help=Le nom de ce calendrier doit Ãªtre unique et nÃ©cessaire
auxchargeschedule.field.status=Ãtat
auxchargeschedule.field.minamount=Montant min.
auxchargeschedule.field.minamount.help=Saisissez le montant minimum requis pour ce tarif
auxchargeschedule.field.maxamount=Montant maximal
auxchargeschedule.field.maxamount.help=Entrez le montant maximal autorisÃ© Ã  Ãªtre chargÃ© par ce calendrier
auxchargeschedule.field.vendportion=Portion de vente
auxchargeschedule.field.vendportion.help=Entrez le pourcentage d'une vente qui sera utilisÃ© pour calculer la charge.
auxchargeschedule.field.currportion=Portion actuelle
auxchargeschedule.field.currportion.help=Entrez le pourcentage du solde restant qui sera utilisÃ© pour calculer la charge.
auxchargeschedule.field.active=Actif
auxchargeschedule.field.active.help=L'Ãtat d'activitÃ© actuel de cet Ã©lÃ©ment
auxchargeschedule.error.save=Impossible d'enregistrer le programme de charge auxiliaire.
auxchargeschedule.error.duplicate=Duplique le nom {0} pour un programme de frais auxiliaire. SpÃ©cifiez un nom unique.
auxchargeschedule.nocharge.error=Une portion de vente, une partie courante ou un montant public doit Ãªtre dÃ©finie

### Auxilliary Type ###
auxilliarytypes.header=Types auxiliaires
auxilliarytypes.title=Types auxiliaires actuels
auxillarytype.title.add=Ajouter un type auxiliaire
auxillarytype.title.update=Mise Ã  jour du type auxiliaire
auxillarytype.title=Type auxiliaire
auxtype.field.name=Nom
auxtype.field.description=La description
auxtype.field.status=Ãtat
auxtype.field.active=Actif
auxtype.field.name.help=Le nom de ce type auxiliaire doit Ãªtre unique
auxtype.field.description.help=Description de ce type auxiliaire
auxtype.field.active.help=L'Ãtat d'activitÃ© actuel de cet Ã©lÃ©ment
auxtype.error.save=Impossible de sauvegarder le type auxiliaire.
auxtype.error.save.duplicate=Impossible d'enregistrer le type auxiliaire, un autre type auxiliaire avec le mÃªme nom existe dÃ©jÃ .
auxtype.error.update=Impossible de mettre Ã  jour le type auxiliaire.
auxtype.error.update.duplicate=Impossible de mettre Ã  jour le type auxiliaire, un autre type auxiliaire avec le mÃªme nom existe dÃ©jÃ .

### Device Store ###
devicestores.header=Dispositifs
devicestores.title=Magasins de dispositifs actuels
devicestore.title.add=Ajouter un magasin d'appareils
devicestore.title.update=Mettre Ã  jour le magasin d'appareils
devicestore.title=Dispositifs
devicestore.field.name=Nom
devicestore.field.description=La description
devicestore.field.name.help=Nom de ce magasin d'appareils (doit Ãªtre unique)
deviceStore.name.duplicate=Duplique le nom {0} pour un magasin d'appareils. SpÃ©cifiez un nom unique.
devicestore.field.description.help=Description de ce magasin d'appareils
devicestore.field.active=Actif
devicestore.field.active.help=L'Ãtat d'activitÃ© actuel de ce magasin d'appareils
devicestore.location.title=Emplacement de la boutique dispositif
devicestore.error.save=Impossible d'enregistrer le magasin de dispositifs.
devicestore.error.update=Impossible de mettre Ã  jour le magasin de dispositifs.
devicestore.button.addmeters=Ajouter des compteurs Ã  la banque de dispositifs sÃ©lectionnÃ©e
devicestore.meters=Compteurs actuels
devicestore.meters.in=Compteurs actuels dans
devicestore.meters.description=MÃ¨tres actuellement dans ce magasin d'appareils.
devicestore.meters.header=Compteur Dispositifs
devicestore.meters.title=Appareils de stockage de dispositifs actuels
devicestore.history=Historique du magasin dispositif
devicestore.history.description=Les modifications prÃ©cÃ©dentes apportÃ©es Ã  ce magasin d'appareils (les modifications sont mises en surbrillance)
devicestore.user=Utilisateur
devicestore.date=Date
devicestore.date.mod.column=Date modifiÃ©e
devicestore.user.by.column=Par utilisateur
devicestore.action.column=Action
devicestore.status.column=Ãtat
devicestore.name.column=Nom
devicestore.description.column=La Description
devicestore.button.importmeters=Compteurs d'importation dans le magasin de dispositifs sÃ©lectionnÃ©
devicestore.import.meters.header=Importer des compteurs dans le magasin de dispositifs {0}

### Meter ###
meter.title=Compteur
meter.number.instructions=Recherche par numÃ©ro de compteur
meter.add=Ajouter un nouveau compteur
meter.add.new=Ajouter un nouveau compteur
meter.or=ou
meter.then=puis
meter.fetch.number=Chercher Compteur
meter.specify.install.date=SpÃ©cifiez la date d'installation
meter.open=Compteur ouvert
meter.open.newtab=Ouvrez le mÃ¨tre remplacÃ© dans un nouvel onglet.
meter.assign=Compteur de rÃ©cupÃ©ration
meter.attach=Fixez le compteur au point d'utilisation
meter.info.title=Information du compteur
meter.required.text=* \= Obligatoire
meter.required.activation.text=** \= Obligatoire pour l'activation
meter.show.info=Afficher les informations du compteur
meter.showing.info=Affichage de l'information du compteur
meter.active.help=Ce compteur est-il actif? Cette option est dÃ©sactivÃ©e jusqu'Ã  ce que toutes les informations requises soient enregistrÃ©es.
meter.active=Actif
meter.replace.help=Remplacez le compteur actuel sur ce point d'utilisation par un autre compteur.
meter.replace=Remplacer le compteur
meter.remove.help=Retirez le compteur actuel de ce point d'utilisation. DÃ©sactivera le point d'utilisation. Peut l'accÃ©der Ã  nouveau via Recherche avancÃ©e.
meter.remove=Supprimer le compteur du Point D'utilisation
meter.assign.from.store.help=Obtenez un compteur Ã  partir du magasin.
meter.assign.from.store=DÃ©tecter le compteur depuis le magasin central
meter.date.install.missing=Entrez la nouvelle date d'installation.
meter.select.meter.model=SÃ©lectionnez le modÃ¨le du compteur
meter.select.meter.model.help=SÃ©lectionnez le modÃ¨le du compteur. Cela dÃ©terminera les dÃ©tails nÃ©cessaires au compteur.
meter.select.metertype=Type de compteur
meter.select.metertype.help=SÃ©lectionnez le modÃ¨le du compteur. Cela dÃ©terminera les dÃ©tails nÃ©cessaires au compteur.
meter.number.help=Entrez le numÃ©ro du compteur. Ceci est un champ obligatoire - l'enregistrement ne peut pas Ãªtre sauvegardÃ© sans celui-ci.
meter.number=NumÃ©ro du compteur
meter.number.optional.help=Entrez un numÃ©ro de compteur spÃ©cifique, si vous en avez un.
meter.number.optional= NumÃ©ro de compteur (optionnel)
meter.iso.help=Entrez le compteur ISO.
meter.iso=ISO
meter.checksum.help=Entrez la somme de contrÃ´le du compteur.
meter.checksum=Somme de contrÃ´le 
meter.serialnumber.help=Entrez le numÃ©ro de sÃ©rie du compteur.
meter.serialnumber=NumÃ©ro de sÃ©rie
meter.breakerid=Id du Disjoncteur
meter.breakerid.help=Le modÃ¨le du compteur nÃ©cessite un identifiant du disjonteur, entrez ici.
meter.breakerid.error=Le modÃ¨le du compteur nÃ©cessite un identifiant du disjoncteur.
meter.stsinfo=Informations STS
meter.algorithmcode=Code d'algorithme
meter.tokentechcode=Code jeton tech 
meter.supplygroupcode=Code actuel du groupe d'approvisionnement
meter.tariffindex=Indice tarifaire actuel
meter.save.errors=Le compteur n'est pas enregistrÃ©. Corrigez les erreurs.
meter.saved=MÃ¨tre enregistrÃ©.
meter.changes.cleared=Les modifications ont Ã©tÃ© effacÃ©es.
meter.enter.number=Entrez le numÃ©ro du compteur
meter.powerlimit=Limite de puissance
meter.powerlimit.help=SÃ©lectionnez la limite de puissance requise
meter.description=La description
meter.description.help=Entrez une description
meter.token.active=Le point d'utilisation doit Ãªtre actif pour obtenir ce jeton
meter.token.code=Code jeton
meter.token.code1=Code jeton 1
meter.token.code2=Code jeton 2
meter.title.enginerringtokens=Jetons d'ingÃ©nierie
meter.issue.engineeringtoken=Ãmettre un nouveau jeton d'ingÃ©nierie
meter.engineeringtoken.history=Historique des jetons d'ingÃ©nierie
meter.engineeringtoken.description=Les jetons d'ingÃ©nierie prÃ©cÃ©dents gÃ©nÃ©rÃ©s pour ce compteur
meter.select.tokentype=SÃ©lectionnez le type de jeton
meter.select.tokentype.help=SÃ©lectionnez un type de jeton dans la liste ci-dessous.
meter.txn.history=Historique des transactions
meter.txn.history.description=Les transactions client antÃ©rieures pour ce compteur
meter.history=Histoire du compteur
meter.history.description=Modifications prÃ©cÃ©dentes apportÃ©es Ã  ce compteur (les changements sont mis en Ã©vidence)
meter.reports=Rapports pour compteur
meter.recharge.history=Historique de la recharge du compteur
meter.retailers=Commerces Ã  proximitÃ©
meter.reports.general=Rapports gÃ©nÃ©raux pour les compteurs
meter.reports.meter=Rapports pour compteur
meter.freeissue.units=Emission gratuite - UnitÃ©s
meter.cleartamper=Effacer Tamper
meter.clearcredit=Effacer credit
meter.clearcredit.all=Effacer tout crÃ©dit
meter.clearcredit.elec=CrÃ©dit d'Ã©lectricitÃ© clair
meter.clearcredit.type.description=Type de crÃ©dit
meter.clearcredit.type.help=SÃ©lectionnez le type de crÃ©dit qui doit Ãªtre effacÃ©
meter.coords=Ajoutez les coordonnÃ©es Latitude et Longitude au point d'utilisation pour ce compteur, afin de voir les dÃ©taillants proches.
meter.user=L'utilisateur
meter.serial=En sÃ©rie
meter.date=Date
meter.date.mod.column=Date modifiÃ©e
meter.user.by.column=Par utilisateur
meter.action.column=Action
meter.status.column=Ãtat
meter.number.column=Numero Compteur
meter.serial.column=En sÃ©rie 
meter.uniqueid.column=Identifiant unique
meter.unique.external.column=ID unique externe
meter.techtoken.column=TT
meter.alg.column=Alg
meter.supplygroup.column=SG
meter.keyrevision.column=KR
meter.tariffindex.column=TI
meter.enddevicestore.column=le magasin
meter.units.kwh=UnitÃ©s (kWh)
meter.units.kwh.help=Entrez le nombre d'unitÃ©s kWh requises.
meter.units.watts=UnitÃ©s (Watts)
meter.units.watts.help=Entrez la limite de phase maximale en watts.
meter.currency=Devise
meter.currency.help=Entrez la valeur de la devise requise.
meter.free.description=La Description
meter.free.description.help=Entrez une description pour ce numÃ©ro gratuit.
meter.setphase=DÃ©finir la phase maximale
meter.setphase.description=La Description
meter.setphase.description.help=Entrez une description pour ce jeton de phase dÃ©finie.
meter.token.error=Impossible de rÃ©cupÃ©rer le jeton. VÃ©rifiez les erreurs.
meter.error.units=Entrez une valeur valide pour le nombre d'unitÃ©s requis
meter.error.amount=Entrez une valeur valide pour le montant requis
meter.txn.type=Type
meter.txn.token.type=Type de jeton
meter.txn.receipt=Le reÃ§u
meter.txn.token=Jeton
meter.txn.amount=Montant Incl impÃ´t
meter.txn.tax=ImpÃ´t
meter.txn.units=UnitÃ©s
meter.txn.tariff=Tarif
meter.txn.date=Date
meter.txn.ref=RÃ©fÃ©rence
meter.txn.receiptnum=NumÃ©ro de reÃ§u
meter.txn.customer=Client
meter.txn.client=Client
meter.txn.term=Terme
meter.txn.revref=Reflet d'inversion
meter.txn.isreversed=RenversÃ©
meter.txn.amount.column=Montant
meter.txn.usagepoint=Point d'utilisation
meter.txn.user=Utilisateur
meter.txn.description=La description
meter.clear.description=La description
meter.clear.description.help=Entrez une description pour cette sabotage.
meter.changekey=Changement clÃ©
meter.changekey.instructions=Pour les jetons de changement de clÃ©:\n1. Ouvrez le panneau du compteur.\n2. Dans le bloc d'informations STS, modifiez le code du groupe d'approvisionnement actuel.\n3. Cela ajoutera une nouvelle liste avec les options de changement de clÃ© - sÃ©lectionnez ce dont vous avez besoin.\n4. Lors de la sauvegarde du compteur, une boÃ®te contextuelle affichera d'autres dÃ©tails.
meter.new.supplygroupcode.help=Entrez le nouveau code de groupe d'approvisionnement.
meter.new.supplygroupcode=Code du nouveau groupe d'approvisionnement
meter.old.supplygroupcode=Ancien code du groupe d'approvisionnement
meter.new.tariffindex=Nouvel indice tarifaire
meter.old.tariffindex=Ancien indice tarifaire
meter.new.keyrevisionnum=Nouveau numÃ©ro de rÃ©vision clÃ©
meter.old.keyrevisionnum=Ancien numÃ©ro de rÃ©vision clÃ©
meter.new.tariffindex.help=Entrez le nouvel indice tarifaire.
meter.txn.filter=Filtre
mdc.txn.show.connect.disconnect.only=Afficher le dÃ©connexion de connexion uniquement
mdc.txn.show.balance.messages.only=Afficher uniquement les messages d'Ã©quilibre
meter.engtoken.filter=Filtre
meter.history.filter=Filtre
meter.generate.keychange=GÃ©rer des jetons de changement de clÃ©?
meter.generate.keychange.help=Si le compteur doit Ãªtre mis Ã  jour pour correspondre aux nouveaux dÃ©tails du STS, il faut gÃ©nÃ©rer des tokens de changement de clÃ©. Si l'enregistrement est mis Ã  jour pour correspondre aux dÃ©tails du compteur, il n'est pas nÃ©cessaire de gÃ©nÃ©rer des jetons.
meter.luhncheck.failed=NumÃ©ro de compteur incorrect (vÃ©rification Luhn Ã©chouÃ©e)
meter.error.alreadyexists=Un compteur avec ce numÃ©ro existe dÃ©jÃ .
meter.keychange.none=Ne gÃ©nÃ¨re pas de jetons de changement de clÃ©
meter.keychange.now=GÃ©nÃ©rer des jetons de changement de clÃ© maintenant
meter.keychange.flag=DÃ©finir pour gÃ©nÃ©rer des jetons de changement de clÃ© avec la prochaine vente
meter.pending.keychange=* Changement de clÃ© en attente:
meter.warning.sg.transactions=REMARQUE: Il y a dÃ©jÃ  plus de 3 transactions sur ce compteur.
meter.error.save=Impossible de sauvegarder le compteur.
stsmeter.error.save=Impossible de sauvegarder le compteur STS.
meter.select.store.move=DÃ©placez le compteur actuel vers le magasin suivant:
meter.select.store.help=Le compteur actuel doit Ãªtre ajoutÃ© Ã  un magasin. Lorsqu'il est affectÃ© Ã  un point d'utilisation, il sera automatiquement retirÃ© du magasin. Ceci est un champ obligatoire - l'enregistrement ne peut pas Ãªtre sauvegardÃ© sans celui-ci.
meter.message.type=Type de message
meter.message.type.help=Envoyer un message pour le compteur 
meter.assigned.to.usagepoint=Le compteur est actuellement affectÃ© au point d'utilisation:
meter.error.invalid=Compteur invalide spÃ©cifiÃ©.
meter.error.invalid.datemanu=La date de fabrication n'est pas valide.
meter.error.cannot.activate=Impossible d'activer
meter.error.required.for.activation=NÃ©cessaire pour l'activation
meter.partial.search=Pas de compteur correspondant. Faire une recherche avancÃ©e ...
meter.install.date.required=La date et l'heure oÃ¹ le nouveau compteur a Ã©tÃ© installÃ© au point d'utilisation {0}. 
meter.installed.at=Meter a Ã©tÃ© installÃ© sur {0} Ã  {1}.
meter.connect.disconnect.error=Une erreur s'est produite lors de l'envoi des instructions du compteur: {0}
meter.connect.disconnect.connection.error=Aucune rÃ©ponse reÃ§ue. Service non disponible. Veuillez informer votre administrateur systÃ¨me.
meter.connect.disconnect.ok.mdc000=La commande Meter {0} a Ã©tÃ© complÃ©tÃ©e avec succÃ¨s. RÃ©fÃ©rence = {1}.
meter.connect.disconnect.ok.mdc010=La commande Meter {0} est acceptÃ©e. RÃ©fÃ©rence = {1}.
meter.connect.disconnect.ok.mdc011=Le message du compteur {0} a Ã©tÃ© mis en file d'attente. RÃ©fÃ©rence = {1}.
meter.manufacturer.code.length=Code Fabricant
meter.manufacturer.code.length.help=Longueur du code du fabricant, 2 chiffres ou 4 chiffres
meter.2digit.manufacturer.code=Code Ã  2 chiffres
meter.4digit.manufacturer.code=Code Ã  4 chiffres
meter.found.incorrect.group=Compteur trouvÃ© mais en groupe diffÃ©rent pour utilisateur
usagepoint.found.incorrect.group=Point d'utilisation trouvÃ© mais en groupe diffÃ©rent pour l'utilisateur
meter.attach.cancelled=Processus d'attachement annulÃ©. Le Compteur {0} a Ã©tÃ© crÃ©Ã© dans le magasin de pÃ©riphÃ©riques, mais pas assignÃ© Ã  ce point d'utilisation.
meter.attach.instructions=Fixez le compteur au point d'utilisation
meter.attach.cancel.button=Annuler le processus d'attachement

### Customer ###
customer.title=Client
customer.search.instructions=Recherche du client par:
customer.add=Ajouter un nouveau client
customer.add.new=Ajouter un nouveau client
customer.info.title=Informations client
customer.show.info=Afficher l'information du client
customer.showing.info=Affichage de l'information du client
customer.active.help=Est-ce que ce client est actif? Cette option est dÃ©sactivÃ©e jusqu'Ã  ce que toutes les informations client requises soient enregistrÃ©es.
customer.active=Actif
customer.unassign=DÃ©sassignez le client du point d'utilisation
customer.assign=Affecter le point de l'utilisateur au point d'utilisation
customer.assign.short=Affecter un client
customer.assign.help=Affectez un client existant Ã  ce point d'utilisation. 
customer.unassign.help=Supprimez le client actuel de ce point d'utilisation.
customer.fetch=Chercher un Client
customer.fetch.help=Rechercher un client existant
customer.fetch.duplicate=Client {0} dÃ©jÃ  sur la page.
customer.open=Client ouvert
customer.field.title.help=Entrez le titre du client (p. Ex., M., Mme, Dr).
customer.field.title=Titre
customer.initials.help=Entrez les initiales du client.
customer.initials=Initiales
customer.firstnames.help=Entrez les prÃ©noms du client.
customer.firstnames=PrÃ©noms
customer.surname.help=Entrez le nom de famille du client. Ceci est un champ obligatoire - le client ne peut pas Ãªtre enregistrÃ© sans lui.
customer.surname=Nom de famille
customer.surname.instr=Entrez le nom de famille du client
customer.company.help=Entrez le nom de la sociÃ©tÃ©.
customer.company=Nom de la compagnie
customer.tax.help=Entrez le numÃ©ro de l'impÃ´t du client.
customer.tax=NumÃ©ro d'identification fiscale
customer.emails.help=Entrez les adresses e-mail du client.
customer.phones.help=Entrez les numÃ©ros de tÃ©lÃ©phone du client.
customer.address.physical=Adresse physique
customer.agreement=Contrat client
customer.agreementref.help=Entrez la rÃ©fÃ©rence de l'accord avec le client. Ceci est un champ obligatoire - le client ne peut pas Ãªtre enregistrÃ© sans lui.
customer.agreementref=Ref. D'accord
customer.startdate.help=Entrez la date d'entrÃ©e en vigueur de l'accord avec le client. Bien que l'enregistrement puisse Ãªtre enregistrÃ© sans lui, le client ne peut pas Ãªtre activÃ© Ã  moins qu'il ne soit rempli correctement.
customer.startdate=Date de dÃ©but
customer.freeissue.help=SÃ©lectionnez le compte auxiliaire Ã  utiliser avec les jetons d'Ã©mission gratuits, oÃ¹ la valeur du jeton sera rÃ©cupÃ©rÃ©e en ajoutant le montant au compte sÃ©lectionnÃ©.
customer.freeissue=Compte auxiliaire gratuit
customer.required=* \= Obligatoire
customer.required.activation=** \= Obligatoire pour l'activation
customer.save.errors=Le client n'est pas enregistrÃ©. Corrigez les erreurs affichÃ©es.
customer.sync.accbal.error=Une erreur s'est produite lors de la synchronisation du solde du compte par rapport au compteur. RÃ©fÃ©rence = {0}
customer.sync.accbal.connection.error=Aucune rÃ©ponse reÃ§ue du service.
customer.sync.accbal.ok.mdc000=Synchroniser le solde du compte avec succÃ¨s. RÃ©fÃ©rence = {0}
customer.sync.accbal.ok.mdc010=Synchroniser la commande de balance de compte acceptÃ©e. RÃ©fÃ©rence = {0}
customer.sync.accbal.ok.mdc011=Le message de synchronisation de compte de synchronisation a Ã©tÃ© mis en file d'attente. RÃ©fÃ©rence = {0}
customer.changes.cleared=Les modifications ont Ã©tÃ© effacÃ©es.
customer.auxaccount.adjust=Ajuster le compte auxiliaire
customer.title.find=Trouver un client
customer.assigned=Impossible d'attribuer le client, dÃ©jÃ  affectÃ©.
customer.auxaccount=Compte Auxiliaire: {0}
customer.auxaccount.addedit=Comptes Auxiliaires
customer.auxaccount.active=Actif
customer.auxaccount.active.help=Ce compte est-il actif? Cette option est dÃ©sactivÃ©e jusqu'Ã  ce que toutes les informations de compte requises soient enregistrÃ©es.
customer.auxaccount.type.help=SÃ©lectionnez le type de compte auxiliaire que vous ajoutez.
customer.auxaccount.balance=Balance
customer.auxaccount.balance.help=Entrez le solde actuel de ce compte.
customer.auxaccount.balance.pos=Un solde positif indique un REMBOURSEMENT
customer.auxaccount.balance.neg=Un solde nÃ©gatif indique une DETTE
customer.auxaccount.priority.help=Entrez la prioritÃ© pour ce compte (1 est la prioritÃ© absolue).
customer.auxaccount.chargeschedule.help=SÃ©lectionnez le calendrier des charges.
customer.auxaccount.txn.history=Historique des transactions des comptes auxiliaires pour : {0}.
customer.auxaccount.txn.description=Transactions antÃ©rieures pour ce compte auxiliaire
customer.title.auxaccounts=Comptes Auxiliaires
customer.title.auxaccounts.current=Comptes Auxiliaires Actuels
customer.title.auxaccounts.description=Affichez et Ã©ditez les comptes auxiliaires actuels et crÃ©ez de nouveaux comptes auxiliaires.
customer.title.txnhistory=Historique des transactions de compte
customer.title.history=Historique des clients
customer.title.reports=Rapports pour le client
customer.title.generalreports=Rapports gÃ©nÃ©raux pour les clients
customer.title.chargescheduledetails=DÃ©tails pour l'horaire des charges auxiliaires
customer.title.chargeschedule=Horaire de charge
customer.chargeschedule.startdate=Date de dÃ©but
customer.chargeschedule.vendpor=Vente Portion
customer.chargeschedule.currpor=Portion actuelle
customer.chargeschedule.minamt=Montant min.
customer.chargeschedule.maxamt=Montant maximal
customer.chargeschedule.status=Ãtat
customer.auxaccount.filter=Filtre
customer.auxaccount.date=Date
customer.auxaccount.edit=modifier
customer.auxaccount.name=Nom du compte
customer.auxaccount.name.help=Entrez un nom pour ce compte.
customer.auxaccount.type=Type
customer.auxaccount.type.column=Type
customer.auxaccount.priority=PrioritÃ©
customer.auxaccount.chargeschedule=Horaire de charge
customer.auxaccount.status=Ãtat
customer.auxaccount.freeissue=Exempt
customer.auxaccount.add=Ajouter un compte auxiliaire
customer.auxaccount.update=Mettre Ã  jour le compte auxiliaire
customer.auxaccount.error.save=Impossible de sauvegarder le compte auxiliaire.
customer.auxaccount.error.id=Impossible d'appliquer l'ajustement du compte auxiliaire par rapport au compte client. Contactez le support!
customer.auxaccount.error.unique.priority=La prioritÃ© est requise et doit Ãªtre unique. Ne peut Ãªtre infÃ©rieur ou Ã©gal Ã  zÃ©ro.
customer.freeissue.error.save=Impossible de mettre Ã  jour l'accord du client. 
customer.partial.search=Pas de correspondance exacte avec le client. Faire une recherche avancÃ©e ...
customer.agreement.partial.search=Pas d'accord de correspondance exacte. Faire une recherche avancÃ©e ...
customer.account.partial.search=Pas de correspondance exacte au nom du compte. Faire une recherche avancÃ©e ...

customer.date.mod.column=Date modifiÃ©e
customer.user.by.column=Par utilisateur
customer.action.column=Action
customer.status.column=Ãtat
customer.title.column=Titre
customer.initials.column=Initiales
customer.firstnames.column=PrÃ©noms
customer.surname.column=Nom de famille
customer.company.column=Compagnie
customer.email1.column=Email
customer.email2.column=Email 2
customer.phone1.column=TÃ©lÃ©phone 
customer.phone2.column=TÃ©lÃ©phone 2

customer.user=Utilisateur
customer.date=Date
customer.history=Historique des clients
customer.history.description=Les modifications prÃ©cÃ©dentes apportÃ©es Ã  ce client (les changements sont mis en Ã©vidence)
customer.history.filter=Filtre

customer.agreement.user=Utilisateur
customer.agreement.date=Date
customer.agreement.history=Historique de l'accord client
customer.agreement.history.description=Modifications prÃ©cÃ©dentes apportÃ©es Ã  cet accord client (les changements sont mis en Ã©vidence)
customer.agreement.history.filter=Filtre
customer.agreement.date.mod.column=Date modifiÃ©e
customer.agreement.user.by.column=Date modifiÃ©e
customer.agreement.action.column=Action
customer.agreement.status.column=Ãtat
customer.agreement.customer.column=Client
customer.agreement.ref.column=Ref. D'accord
customer.agreement.start.column=Date de dÃ©but
customer.agreement.freeaux.column=Compte Aux gratuit
customer.error.save=Impossible de sauvegarder le client.
customeraggreement.error.save=Impossible de sauvegarder l'accord client.

customer.account=Compte client
customer.account.name=Nom du compte
customer.account.name.help=Entrez un nom pour ce compte
customer.account.balance=Solde du compte
customer.account.balance.help=Le solde du compte courant
customer.account.sync=Synchroniser l'Ã©quilibre
customer.account.sync.help=Synchroniser le solde du compte avec le solde du compteur
customer.account.low.balance.threshold=Seuil minimal de consommation
customer.account.low.balance.threshold.help=Lorsque le solde du compte atteint ce seuil, un message sera envoyÃ© au client.
customer.account.credit.limit=Limite de crÃ©dit
customer.account.credit.limit.help=La limite de crÃ©dit autorisÃ©e sur ce compte
customer.account.note=Un compte client n'est nÃ©cessaire que si la structure de prix choisie pour le point d'utilisation (ci-dessous) l'exige.
customer.account.notification.email=Adresse Ã©lectronique de notification
customer.account.notification.email.help=Une adresse e-mail Ã  laquelle les notifications liÃ©es au compte peuvent Ãªtre envoyÃ©es (par exemple, lorsque le seuil de balance bas a Ã©tÃ© atteint)
customer.account.notification.phone=NumÃ©ro de tÃ©lÃ©phone de la notification
customer.account.notification.phone.help=Un numÃ©ro de tÃ©lÃ©phone auquel les notifications liÃ©es au compte peuvent Ãªtre envoyÃ©es (par exemple, lorsque le seuil de balance bas a Ã©tÃ© atteint)
customeraccount.error.save=Impossible de sauvegarder le compte client.

customer.txn.filter=Filtre
customer.txn.history=Historique des transactions de compte
customer.txn.description=OpÃ©rations de compte antÃ©rieures pour cette convention client
customer.txn.ent.date=Date entrÃ©e
customer.txn.user=Utilisateur entrÃ©
customer.txn.name=Client
customer.txn.agreement.ref=Ref. D'accord
customer.txn.trans.type=Type de transaction
customer.txn.trans.date=Date de la transaction
customer.txn.comment=Commentaire
customer.txn.amt=Montant incluant l'impÃ´t
customer.txn.tax=L'impÃ´t
customer.txn.bal=Balance
customer.txn.input=Ajuster le compte
customer.txn.acc.ref=RÃ©fÃ©rence du compte
customer.txn.our.ref=Notre rÃ©fÃ©rence
customer.txn.amt.incl.tax=Montant incl. ImpÃ´t
customer.txn.successful.adjustment=Compte rÃ©ussi et nouveau solde de compte
customer.txn.no.agreement=Il n'y a PAS d'accord client pour ajuster
customer.txn.error.amt.and.tax.zero=Le montant et l'impÃ´t ne peuvent pas Ãªtre zÃ©ro
customer.txn.error.update=Erreur lors de la mise Ã  jour du solde du compte client
customer.txn.error.insert=Erreur lors de l'insertion de la nouvelle transaction d'ajustement
customer.txn.error.no.usagepoint=Point d'utilisation introuvable
customer.txn.notification.failure=Impossible d'envoyer une notification d'ajustement du compte. Veuillez informer votre administrateur systÃ¨me.
customer.txn.send.email.failure=Impossible d'envoyer un compte d'ajustement de compte au client. Veuillez informer votre administrateur systÃ¨me.

# Customer transaction upload
customer.trans.upload= TÃ©lÃ©charger la Transaction
customer.trans.upload.heading=TÃ©lÃ©chargement des transactions d'ajustement du solde du compte client
customer.trans.upload.data.title=Importation des transactions d'ajustement du solde du compte client
customer.trans.upload.data.description=SÃ©lectionnez le fichier CSV contenant les transactions du client pour l'importation dans le systÃ¨me de gestion des compteurs.
customer.trans.upload.file.help=SÃ©lectionnez un fichier contenant des informations de transaction client dans le format csv spÃ©cifiÃ© pour l'importation dans le systÃ¨me
customer.trans.upload.file=SÃ©lectionnez le fichier de transaction
customer.trans.upload.csv.button=TÃ©lÃ©charger des donnÃ©es CSV
customer.process.trans.button=Transactions de processus
customer.trans.upload.identifierType=Type de Identificateur
customer.trans.upload.identifier=Identificateur
customer.trans.upload.amt.incl.tax= Montant incl impÃ´t
customer.trans.upload.amt.tax=Montant ImpÃ´t
customer.trans.upload.trans.date=Date de Transaction
customer.trans.upload.account.ref=RÃ©fÃ©rence du compte
customer.trans.upload.comment=Commentaire
customer.trans.upload.errors=Erreurs
customer.trans.upload.table.heading.errors=Transactions : Erreurs
customer.trans.upload.Bizswitch.down=Aucune rÃ©ponse reÃ§ue. Le service BizSwitch n'est pas disponible. Veuillez informer votre administrateur systÃ¨me.
customer.trans.upload.table.heading.valid=Transactions valides: Ã©chantillon des 15 premiÃ¨res lignes dans le fichier
customer.trans.upload.invalid.cannot.create.dir=ERREUR! Impossible de crÃ©er le rÃ©pertoire. Contactez Support.
customer.trans.upload.filename= Nom de fichier sÃ©lectionnÃ© ={0}
customer.trans.upload.invalid.filename=Nom de fichier incorrect - Un trait d'union ou une pÃ©riode manquant. Nom de fichier attendu comme TransCompte -rÃ©fÃ©rence.csv oÃ¹ <rÃ©fÃ©rence> est sauvegardÃ© comme Â«notre RefÂ» sur les transactions
customer.trans.upload.invalid.filename.changed=Le nom de fichier a changÃ© entre les Ã©tapes! Ãtait {0}; maintenant {1}
customer.trans.upload.invalid.unexpected.commas=Commas Ã  l'intÃ©rieur des champs - ne peut pas identifier prÃ©cisÃ©ment les champs distincts
customer.trans.upload.invalid.identifiertype=typeIdentificateur doit Ãªtre NomPointUtilisation / NomCompte / NumÃ©roCompteur
customer.trans.upload.invalid.identifier=Identifiant non valide - pas dans la base de donnÃ©es
customer.trans.upload.invalid.agreement=Usage Point n'a pas de contrat client en place
customer.trans.upload.invalid.usagepoint.or.agreement=Le Compteur n'a pas de point d'utilisation ou le point d'utilisation n'a pas d'accord
customer.trans.upload.invalid.usagepoint=Le compte client n'appartient pas Ã  un point d'utilisation
customer.trans.upload.invalid.amt.incl.tax=Montant incl. lâImpÃ´t n'est pas numÃ©rique
customer.trans.upload.invalid.amt.tax=Le montant de lâ impÃ´t n'est pas numÃ©rique
customer.trans.upload.invalid.trans.date=La date de transaction doit Ãªtre soit vide (par dÃ©faut Ã  la date de processus) ou formatÃ© comme aaaa-mm-dd hh:mm:ss, example: 2015-09-23 22:14:55
customer.trans.upload.invalid.account.ref=RÃ©fÃ©rence du compte maximum de 100 caractÃ¨res
customer.trans.upload.invalid.comment=Commentaire maximum 255 caractÃ¨res
customer.trans.upload.invalid.duplicate=La transaction de typeIdentificateur en copie: {0}, identificateur: {1}. Les deux ciblent le mÃªme compte client.
customer.trans.upload.file.action.unknown=Action de tÃ©lÃ©chargement de fichier inconnu, contacter Support
customer.trans.upload.file.none=Aucun fichier n'a Ã©tÃ© sÃ©lectionnÃ© pour Ãªtre tÃ©lÃ©chargÃ©
customer.trans.upload.file.error=Erreur lors du tÃ©lÃ©chargement du fichier
customer.trans.upload.file.process.error=Erreur lors du processus du fichier
customer.trans.upload.successful.counts=Total de {0} transactions en lot - {1} ont Ã©tÃ© traitÃ©es avec succÃ¨s, {2} Ã©taient des doublons et ont Ã©tÃ© ignorÃ©s dans cette course.
customer.trans.upload.process.failed=Erreur systÃ¨me lors de la transaction:typeIdentificateur= {0}, identificateur= {1}, compteRef= {2}. Essayez de renvoyer le fichier.
customer.trans.upload.trans.validation.errors= Erreurs de validation trouvÃ©es. RÃ©parez-vous et re-soumettre. Maximum 15 erreurs sont traitÃ©es Ã  tout moment

### Location ###
location.field.erfnumber=NumÃ©ro Erf
location.field.erfnumber.help=Entrez le numÃ©ro ERF de cet endroit
location.field.address=Adresse
location.field.address.help=Entrez l'adresse de cet endroit
location.field.city=Ville
location.field.city.help=Entrez la ville de cet endroit
location.field.province=Province
location.field.province.help=Entrez la province de cet endroit
location.field.country=Pays
location.field.country.help=SÃ©lectionnez le pays de cet endroit
location.field.postalcode=Code postal
location.field.postalcode.help=Entrez le code postal de cet endroit
location.field.lat=Latitude
location.field.lat.help=Entrez latitude de ce lieu
location.field.long=Longitude
location.field.long.help=Entrez longitude de ce lieu
location.latitude.invalid=Latitude est invalide.
location.longitude.invalid=Longitude est invalide.
location.field.streetnumber=NumÃ©ro de rue
location.field.streetnumber.help=Entrez le numÃ©ro de rue
location.field.buildingname=Nom du bÃ¢timent
location.field.buildingname.help=Entrez le nom du bÃ¢timent
location.field.suitenumber=NumÃ©ro de Suite
location.field.suitenumber.help=Entrez le numÃ©ro d'appartement
location.error.save=Impossible de sauvegarder le lieu.
location.user=Utilisateur
location.date=Date
location.history=Localisation Histoire
location.history.description=Modifications prÃ©cÃ©dentes apportÃ©es Ã  cette adresse.
location.history.date.mod.column=Date de modification
location.history.user.by.column=Par utilisateur
location.history.action.column=Action
location.history.status.column=Ãtat
location.history.address1.column=Adresse
location.history.address2.column=
location.history.address3.column=   
location.history.erfnumber.column=NumÃ©ro Erf   
location.history.latitude.column=Latitude    
location.history.longitude.column=Longitude   
location.history.group.column=Groupe   
location.history.streetnum.column=NumÃ©ro de rue   
location.history.buildingname.column=BÃ¢timent  
location.history.suitenum.column=NumÃ©ro de Suite 
location.history.physical.address=Historique d'adresse Physique 

## Display Tokens
displaytokens.title=Afficher Jetons
display.initiate=Initier compteur d'essai
display.testload=1. Tester le commutateur de charge
display.testdisplay=2. Tester les dispositifs d'affichage paiement d'information du compteur
display.totals=3. Afficher kWh totaux cumulÃ©s de registre d'Ã©nergie
display.krn=4. Affichez le KRN
display.ti=5. Affichez TI
display.testreader=6. Testez le dispositif de lecteur de jetons
display.powerlimit=7. Afficher la limite de puissance maximale
display.tamper=8. Afficher l'Ãtat de sabotage
display.consumption=9. Affichage de la consommation d'Ã©nergie
display.version=10. Afficher la version du logiciel
display.phase=11. Affichage limite de puissance de dÃ©sÃ©quilibre de phase

## Change Group
changegroup.change=Changer de Groupe
changegroup.username=Utilisateur
changegroup.current=Groupe actuel
usergroup.field.grouphierarchy=Niveau de Groupe
changegroup.set=Mettre en place Groupe 
changegroup.select=Parcourez les groupes de contrÃ´le d'accÃ¨s disponibles disponibles ci-dessous et sÃ©lectionnez un groupe.
changegroup.available=Groupes disponibles
changegroup.error.group.none=SÃ©lectionnez un groupe valide.
changegroup.error.same=Le groupe sÃ©lectionnÃ© est le mÃªme groupe que votre groupe actuel.
changegroup.assigned=AccÃ¨s AssignÃ© Group Utilisateur
changegroup.group.changed=Votre groupe actuel a Ã©tÃ© changÃ©.
changegroup.group.cleared=Votre groupe actuel a Ã©tÃ© effacÃ©.
changegroup.error.clear=Vous ne pouvez pas effacer votre groupe que vous avez un groupe assignÃ© actuellement.
changegroup.current.details=DÃ©tails actuels
changegroup.username.help=Commencez Ã  taper le nom d'utilisateur de l'utilisateur et sÃ©lectionnez l'utilisateur appropriÃ© dans la liste des utilisateurs.
changegroup.warning=Note: Modification de votre groupe aura besoin de actualiser et / ou la fermeture de tous les onglets d'espace de travail qui sont liÃ©s Ã  un groupe.

## User Group
usergroup.title=Groupe AccÃ¨s d'utilisateur
usergroup.header=Groupe AccÃ¨s d'utilisateur
usergroup.title.current=Groupe AccÃ¨s d'utilisateur actuel
usergroup.label.help=SÃ©lectionnez le groupe d'accÃ¨s que l'utilisateur fera partie pour la visualisation et la modification des donnÃ©es.
usergroup.error.user=Utilisateur non valide spÃ©cifiÃ©.
usergroup.error.group=Groupe non valide spÃ©cifiÃ©.
usergroup.error.save=Impossible de sauvegarder le groupe de l'utilisateur.
usergroup.error.update=Impossible de mettre Ã  jour le groupe de l'utilisateur.
usergroup.field.user=Utilisateur
usergroup.field.usergroup=Groupe d'accÃ¨s assignÃ©
usergroup.title.add=Ajouter un Groupe d'accÃ¨s d'utilisateur
usergroup.title.update=Mise Ã  jour Groupe AccÃ¨s d'utilisateur/s
usergroup.instructions=Chaque utilisateur peuvent Ãªtre assignÃ© Ã  un groupe d'accÃ¨s. Le groupe d'accÃ¨s de l'utilisateur dÃ©termine quelles donnÃ©es sont disponibles Ã  l'utilisateur de visualiser et de modifier.
usergroup.confirm.clear=Etes-vous sÃ»r de vouloir effacer le groupe d'accÃ¨s de l'utilisateur?
usergroup.clear.yourself=Impossible de limiter le groupe d'accÃ¨s de votre propre utilisateur pendant que vous Ãªtes connectÃ© Ã  cette session.
usergroup.cleared=Le groupe d'accÃ¨s de l'utilisateur a Ã©tÃ© effacÃ©.
usergroup.error.usergroup.none=Il n'y a pas de groupe d'accÃ¨s actuel pour l'utilisateur.
usergroup.error.delete=Impossible d'effacer le groupe d'accÃ¨s de l'utilisateur.
usergroup.no.accessgroup=Aucun type de groupe d'accÃ¨s n'a encore Ã©tÃ© fixÃ©e pour l'accÃ¨s aux donnÃ©es. Utilisez la page types de groupe pour dÃ©finir un type de groupe d'accÃ¨s.
usergroup.accessgroup.none=Impossible d'assigner le groupe d'accÃ¨s d'un utilisateur car il n'y a pas de groupes disponibles pour le contrÃ´le d'accÃ¨s de l'utilisateur.

## Access Groups
accessgroups.title=Groupe d'accÃ¨s d'utilisateurs
accessgroup.access.label=Type de Groupe:
accessgroups.title.current=Groupes d'accÃ¨s actuels
accessgroup.access.instructions=Votre groupe d'accÃ¨s assignÃ© actuellement ne peuvent Ãªtre mis Ã  jour, ainsi que l'un de ses sous-groupes. Nouveaux sous-groupes de votre groupe d'accÃ¨s peuvent Ã©galement Ãªtre ajoutÃ©s. S'il n'y a pas de donnÃ©es liÃ©es, vous pouvez Ã©galement supprimer des sous-groupes de niveau infÃ©rieur.

locationgroups.title=Localisation des Groupes
locationgroup.instructions=Localisation des groupes sont utilisÃ©s pour les donnÃ©es de localisation de groupe dans une hiÃ©rarchie.
locationgroups.title.current=Localisation actuel des Groupes

## Advanced Search
search.advanced.title=Recherche avancÃ©e
search.advanced.header=Recherche avancÃ©e
search.advanced.instructions=Entrez une partie ou les donnÃ©es de recherche complets dans les diffÃ©rents champs ci-dessous et cliquez sur le bouton Rechercher pour afficher vos rÃ©sultats. 
search.advanced.results.header=RÃ©sultats avancÃ©e de la recherche
search.meter.number=NumÃ©ro du compteur  
search.meter.model=ModÃ¨le du Compteur
search.meter.no.usage.point=Compteurs sans point d'utilisation 
search.type=Type de recherche
search.type.agreement=Accord du Type de recherche
search.startswith=Commence par
search.contains=Contient
search.customer.name=PrÃ©nom
search.customer.surname=Nom
search.customer.title=Titre
search.customer.agreement=Accord
search.customer.agreement.ref=RÃ©fÃ©rence
search.customer.no.usage.point=Les clients sans point d'utilisation
search.account=Compte
search.account.name=PrÃ©nom
search.usagepoint.name=PrÃ©nom
search.meter=Recherche Compteur 
search.customer=Recherche Client 
search.usagepoint=Recherche Point Usage 
search.usage.point.no.customer=Point Usage non clients
search.usage.point.no.meter=Points d'utilisation sans compteur
search.error.no.criteria=Aucun critÃ¨re de recherche valide a Ã©tÃ© spÃ©cifiÃ©.
search.no.results=Aucun rÃ©sultat de recherche correspondant n'a Ã©tÃ© trouvÃ©.
search.meter.result=Compteur
search.meter.model.result=ModÃ¨le Compteur
search.customer.result=Client
search.usagepoint.result=Point Usage
search.pricingStructure.result=Structure de prix
search.paymentMode.result=Mode de paiement
search.name=PrÃ©nom
search.no.meter=Pas de compteur correspondant exactement a Ã©tÃ© trouvÃ©. Effectuer la recherche en utilisant le numÃ©ro de compteur partiel: {0}.

meterreadings.title=Lectures de compteurs
meterreadings.chart.title.single=Lectures de compteurs
meterreadings.chart.title.balancing=Ãnergie Ãquilibrage
meterreadings.header.graph=Graphe
meterreadings.title.graph=Graphe de Compteur Lectures 
meterreadings.error.none=Commencez Ã  taper un numÃ©ro de compteur et sÃ©lectionnez le compteur correspondant des compteurs.
meterreadings.error.none.selected=SÃ©lectionnez un compteur valide Ã  partir des compteurs disponibles.
meterreadings.start=Date de dÃ©but
meterreadings.end=Date de fin
meterreadings.error.dates=DÃ©but doit Ãªtre avant la date de fin.
meterreadings.error.type=SÃ©lectionnez un type de lecture.
meterreadings.type=Type de lecture
meterreadings.error.start=Date de dÃ©but est nÃ©cessaire.
meterreadings.error.start.format=Date de dÃ©but doit correspondre {0}.
meterreadings.error.end=Date de fin est nÃ©cessaire.
meterreadings.error.end.format=Date de fin doit correspondre Ã  {0}.
meterreadings.noreadings=Pas de relevÃ©s de compteurs correspondants ont Ã©tÃ© trouvÃ©s.
meterreadings.chart.title=Lectures de compteurs
meterreadings.chart.subtitle=Utilisation kWh 
meterreadings.chart.ytitle=kWh
meterreadings.chart.xtitle=Date et heure
meterreadings.series.name=kWh
meterreadings.graph.type=Type de graphe
meterreadings.type.graph.single=Compteur Simple
meterreadings.type.graph.balancing=Ãnergie Ãquilibrage
meterreadings.error.type.reading.unknown=Type de lecture est inconnue.
meterreadings.balancing=Compteur Super
meterreadings.error.balancing=Compteur Super  est requise.
meterreadings.error.type.graph=Type de graphe est nÃ©cessaire.
meterreadings.meter.super=Lectures du compteur Super
meterreadings.meter.totals=Sous-compteur total
meterreadings.meter.field.super=Compteur Super
meterreadings.meter.field.subs=Sous compteurs
meterreadings.header.table=Rapport
meterreadings.title.table=Rapport de Lectures des Compteurs
meterreadings.table.meter=Compteur
meterreadings.table.reading=Lecture
meterreadings.table.start=Heure de dÃ©but
meterreadings.table.end=Heure de fin
meterreadings.report.type=Type de rapport
meterreadings.label.supermeter.total=Compteur Super
meterreadings.label.singlemeters.total=Sous compteurs
meterreadings.report.results=Lectures de compteurs

energybalancing.header=Ãnergie Ãquilibrage
energybalancing.title=Ãnergie Ãquilibrage
energybalancing.start=Date de dÃ©but
energybalancing.end=Date de fin
energybalancing.variation=Variation
energybalancing.error.start=Date de dÃ©but est nÃ©cessaire.
energybalancing.error.end=Date de fin est nÃ©cessaire.
energybalancing.error.dates=Date de dÃ©but doit Ãªtre antÃ©rieure Ã  la date de fin.
energybalancing.error.percent=DiffÃ©rence doit Ãªtre une valeur pourcentage positive.
energybalancing.error.readingtype=RelevÃ© de compteur de type est nÃ©cessaire.
energybalancing.supermeter=Compteur Super
energybalancing.supermeter.reading=Compteur Super total
energybalancing.submeters.total=Sous Compteurs total
energybalancing.none=Aucune mesure d'Ã©quilibrage Ã©nergÃ©tique correspondante n'a Ã©tÃ© trouvÃ©e.
energybalancing.view.graph=Voire Graphe

energybalancing.meter.header=Compteurs d'Ãnergie Ãquilibrage
energybalancing.meter.title=Compteur d'Ãnergie Ãquilibrage
energybalancing.meter.super=Ãquilibrage du numÃ©ro compteur
energybalancing.meter.sub=Sous numÃ©ro compteur
energybalancing.meter.subs=Sous-compteurs sÃ©lectionnÃ©s
energybalancing.meter.super.help=Entrez un numÃ©ro de compteur et sÃ©lectionnez le compteur correspondant.
energybalancing.meter.sub.help=Entrez un numÃ©ro de compteur et sÃ©lectionnez le compteur correspondant. Puis cliquez sur le bouton> pour le sÃ©lectionner comme un sous compteur.  
energybalancing.meter.subs.help=Tous les compteurs sÃ©lectionnÃ©s seront sauvegardÃ©s en tant que sous-compteurs du compteur d'Ã©quilibrage actuel.
energybalancing.meter.instructions=SÃ©lectionnez un compteur pour le compteur d'Ã©quilibrage. SÃ©lectionner et ajouter d'autres sous compteurs ou supprimer des sous compteurs existants.
energybalancing.error.super=Un compteur d'Ã©quilibrage sÃ©lectionnÃ© valide est nÃ©cessaire.
energybalancing.error.sub.selected=Sous compteurs valides sÃ©lectionnÃ©s sont nÃ©cessaires.
energybalancing.error.super.id=Compteur Ã©quilibrant id invalid.
energybalancing.error.sub.ids=Sous compteur id(s) invalid.
energybalancing.error.save=Impossible de sauvegarder l'Ã©quilibrage / sous compteur.
energybalancing.meter.error.no.sub=SÃ©lectionnez un sous compteur en premier.
energybalancing.meter.error.same.meter=Le sous compteur ne peut pas Ãªtre le compteur d'Ã©quilibrage..
energybalancing.meter.save=Ãquilibrage et sous compteurs sauvegardÃ©s.
energybalancing.confirm.delete=Etes-vous sÃ»r de vouloir supprimer ce compteur d'Ã©quilibrage et tous ses sous-compteurs?
energybalancing.meter.deleted=Ãquilibrage et sous compteurs ont Ã©tÃ© supprimÃ©s.
energybalancing.error.delete.none=Pas de compteurs d'Ã©quilibrage qui existent pour le compteur spÃ©cifiÃ©.

meterrecharge.chart.title=Recharges de Compteur 
meterrecharge.chart.subtitle=Montants de Recharge
meterrecharge.chart.xtitle=Date Heure
meterrecharge.chart.ytitle=CoÃ»t
meterrecharge.chart.price=Prix
meterrecharge.chart.purchaseprice=Prix d'achat

# Manufacturer
meter.manufacturers=Fabricant des compteurs
meter.manufacturers.title=Actuel Fabricants des compteurs
meter.manufacturers.title.add=Ajouter Fabricant de compteur
meter.manufacturers.title.update=Mise Ã  jour Fabricant de compteur
meter.manufacturer.name=Fabricant
meter.manufacturers.field.name=Nom
meter.manufacturers.field.description=Description
meter.manufacturers.field.active=Actif
meter.manufacturers.field.status=Ãtat
meter.manufacturers.field.name.help=Nom unique du fabricant.
meter.manufacturers.field.description.help=La description du fabricant.
meter.manufacturers.field.active.help=Seuls les fabricants actifs peuvent Ãªtre utilisÃ©s.
meter.manufacturer.name.duplicate=Nom en copie pour un fabricant: {0}.

# Mdc (Meter Data Collector)
meter.mdc=Lecteur de donnÃ©es du compteur
meter.mdc.title=Lecteur de donnÃ©es du compteur actuel
meter.mdc.title.add=Ajouter Lecteur de donnÃ©es du compteur
meter.mdc.title.update=Mise Ã  jour Lecteur de donnÃ©es du compteur
meter.mdc.name=Lecteur de donnÃ©es du compteur
meter.mdc.field.name=Nom
meter.mdc.field.description=Description
meter.mdc.field.active=Actif
meter.mdc.field.status=Ãtat
meter.mdc.field.value=Valeur
meter.mdc.field.name.help=Le nom unique de mdc.
meter.mdc.field.description.help=La description de mdc.
meter.mdc.field.value.help=UtilisÃ© dans les messages communiquant avec mdc.
meter.mdc.field.active.help=Seul mdc actif peut Ãªtre utilisÃ©.
meter.mdc.name.duplicate=Nom en copie pour mdc: {0}.
meter.mdc.value.duplicate=Valeur en copie pour mdc: {0}.
mdc.txn.messages=Messages MDC 
mdc.txn.message=Message dÃ©tails MDC -  Tirer Ici
mdc.txn.messages.description=Messages Mdc pour ce compteur
mdc.txn.ref=RÃ©fÃ©rence
mdc.txn.meter=Compteur
mdc.txn.usagepoint=Point Usage
mdc.txn.customer=Client
mdc.txn.reqreceived=Demande reÃ§ue
mdc.txn.reqsent=Demande EnvoyÃ©s
mdc.txn.reqtype=Type de demande
mdc.txn.controltype=Type de contrÃ´le
mdc.txn.cmdaccrec=Commande AcceptÃ©e
mdc.txn.params=Params
mdc.txn.recdate=RÃ©ponse reÃ§ue
mdc.txn.repcount=RÃ©pÃ©titions
mdc.txn.status=Ãtat
mdc.txn.timecompld=Temps TerminÃ©
mdc.txn.client=Client
mdc.txn.term=Term
mdc.txn.refreceived=RÃ©f ReÃ§u
mdc.txn.identifier=Identifiant
mdc.txn.identifiertype=Type d'identificateur
mdc.txn.rescodereceived=Code de rÃ©ponse
mdc.txn.scheduledate=Date plannifier
mdc.txn.success=SuccÃ¨s
mdc.txn.pending=En attente
mdc.txn.discarded=SupprimÃ©s
mdc.txn.successful=SuccÃ¨s
mdc.txn.failed=ÃchouÃ©
mdc.txn.popup.label=DonnÃ©es
mdc.txn.popup.value=Valeur
mdc.txn.send.message.title=Envoyer un message Ã  mdc
mdc.button.viewchannels=Afficher les canaux
mdc.error.noneselected=Aucun Mdc actuel sÃ©lectionnÃ©
mdc.txn.connect=Connecter
mdc.txn.disconnect= DÃ©connecter
mdc.txn.disconnect.enable=DÃ©connecter_Activer

# Mdc Channel
channel.header=Cannaux
channel.title=Canaux MDC actuels
channel.value.duplicate=Valeur en double pour un canal mdc: {0}
channel.field.titlename=Canal Mdc
channel.field.value=Valeur
channel.field.name=Nom
channel.field.billingdet=Facteur de Facturation
channel.field.status=Ãtat
channel.title.add=Ajouter Canal Mdc
channel.title.update=Mettre Ã  jour canal Mdc
channel.field.value.help=Entrez l'identificateur de canal.
channel.field.descrip=Description
channel.field.billingdetname=Facteur de Facturation
channel.field.billingdetname.channel.help=SÃ©lectionnez Facteur de Facturation. Peut Ãªtre vide SI les lectures de chaÃ®nes ne doivent pas Ãªtre utilisÃ©es pour la facturation.
channel.field.meter.reading.type=Type de lecture du compteur
channel.field.maxsize=Taille maximale de lecture
channel.field.active.help=L'Ã©tat d'activitÃ© actuel de cette chaÃ®ne
channel.field.active=Actif
channel.billingdet.confirm=Vous n'avez pas sÃ©lectionnÃ© un dÃ©terminant de facturation qui implique que cette chaÃ®ne n'est pas utilisÃ©e pour la facturation. Continuer?

#Mdc Channel Initial Readings
channel.readings.header=Assigner les lectures de chaÃ®nes initiales pour le compteur:
channel.readings.meter.model=ModÃ¨le du Compteur:
channel.readings.mdc=MDC:
channel.readings.pricing.structure=Structure des prix:
channel.readings.installdate=Date dâinstallation:
channel.readings.table.heading=Lecture Initiale
channel.readings.reading.error=Canal d'Erreur {0} : La valeur de lecture doit Ãªtre une valeur numÃ©rique positive infÃ©rieure ou Ã©gale Ã  la taille maximale {1}

# Billing Determinant
billingdet.tab.label=Fact de Facturation
billingdet.header=Facteurs de Facturation
billingdet.title=DÃ©terminants de facturation actuels
billingdet.name=Facteur de Facturation
billingdet.title.add=Ajouter Facteur de Facturation
billingdet.title.update=Mettre Ã  jour Facteur de Facturation
billingdet.field.name=Nom
billingdet.field.name.help=Nom de Facteur de Facturation
billingdet.field.description=Description
billingdet.field.description.help=Description de Facteur de Facturation
billingdet.active=Actif
billingdet.active.help=VÃ©rifiez pour activer, doit Ãªtre actif pour Ãªtre utilisÃ©.
billingdet.error.save=Impossible de sauvegarder le Facteur de Facturation.
billingdet.error.update=Impossible de mettre le Facteur de Facturation.

# Meter Model
meter.models=ModÃ¨les de compteurs
meter.models.title=ModÃ¨les de compteurs actuels
meter.models.name=ModÃ¨le de Compteur
meter.models.field.manufacturer=Fabricant
meter.models.field.manufacturer.help=SÃ©lectionnez le fabricant correct pour le modÃ¨le de compteur.
meter.models.field.name=Nom
meter.models.field.description=Description
meter.models.field.active=Actif
meter.models.field.status=Ãtat
meter.models.field.name.help=Nom unique du modÃ¨le de compteur.
meter.models.field.description.help=La description du modÃ¨le de compteur.
meter.models.field.active.help=Seulement modÃ¨les de compteurs  actifs peut Ãªtre utilisÃ©.
meter.models.field.resource=Service Ressource
meter.models.field.resource.help=SÃ©lectionnez la ressource de service correct pour le modÃ¨le de compteur.
meter.models.field.metertype=Type de compteur
meter.models.field.metertype.help=SÃ©lectionnez le type de compteur correct pour le modÃ¨le de compteur.
meter.models.field.toa=Support jeton transmission direct
meter.models.field.toa.help=Indique si ce modÃ¨le prend en charge l'envoi de jetons tels que jetons sts directement sur le compteur par l'intermÃ©diaire d'un rÃ©seau.
meter.models.field.mdc=Lecteur de donnÃ©es du compteur
meter.models.field.mdc.help=Associer ce modÃ¨le de compteur avec meter data collector.
meter.models.field.paymentmodes=Modes de paiement
meter.models.field.paymentmodes.help=Utilisez la touche Ctrl pour sÃ©lectionner plusieurs modes de paiement pour le modÃ¨le de compteur.
meter.models.title.add=Ajouter ModÃ¨le compteur
meter.models.title.update=Mise Ã  jour du ModÃ¨le compteur 
meter.models.paymentmodes.required=Au moins un mode de paiement est nÃ©cessaire.
meter.models.name.duplicate=Nom en copie pour un modÃ¨le de compteur: {0}.
meter.models.field.balance.sync=Prise en charge de la balance de synchronisation
meter.models.field.balance.sync.help=Indique si ce modÃ¨le de compteur prend en charge la synchronisation de l'Ã©quilibre.
meter.models.field.needs.breaker.id=Besoin Id du disjoncteur
meter.models.field.needs.breaker.id.help=Indique si le compteur nÃ©cessite un identifiant de disjoncteur.
meter.model.unset.breaker.id.error=Impossible de modifier l'exigence de l'identifiant du disjoncteur - il existe dÃ©jÃ  des compteurs avec DisjoncteurId utilisant ce modÃ¨le de compteur.

ptr.serviceresource=Service Ressource
ptr.metertype=Type de compteur
ptr.paymentmode=Mode de paiement

tou.thin.field.calendar=Calendrier
tou.thin.field.calendar.help=SÃ©lectionnez le calendrier pertinent pour le tarif.
tou.thin.field.monthlydemand=Demande mensuelle de frais
tou.thin.field.monthlydemandtype=Demande mensuelle du Type de lecture
tou.thin.field.monthlydemandtype.help=SpÃ©cifiez la demande mensuelle du Type de lecture de charge Ã  utiliser pour la charge.
tou.thin.field.servicecharge=Frais de service quotidien
tou.thin.field.servicecharge.descrip=Description
tou.thin.field.servicecharge.descrip.help=Entrez une description d'Ã©lÃ©ment de ligne pour la charge de service.
tou.thin.field.servicechargecycle=Cycle de Frais de service
tou.thin.field.servicechargecycle.help=Lorsque le frais de service doit Ãªtre appliquÃ©.
tou.thin.field.enablereadingtypes=Activer les lectures de types de compteurs
tou.thin.field.enablereadingtypes.help=SÃ©lectionnez les types de lecture Ã  appliquer pour ce tarif.
tou.thin.charges.button=Saisie les frais
tou.thin.field.charges=Frais
tou.thin.field.charges.specialday=Frais des Jours spÃ©cials
tou.thin.change.calendar=Etes-vous sÃ»r de vouloir annuler toutes vos charges et dÃ©finir un nouveau calendrier?
tou.thin.error.no.calendar=SÃ©lectionnez un calendrier.
tou.thin.error.no.types=SÃ©lectionnez au moins un type.
tou.thin.error.no.tax=ImpÃ´t est nÃ©cessaire.
tou.thin.monthlydemandtype.required=Type de lecture est nÃ©cessaire.
tou.thin.monthlydemand.required=Frais est nÃ©cessaire.
tou.thin.monthlydemand.positive=Frais doit Ãªtre positif.
tou.thin.monthlydemand.invalid=Frais doit Ãªtre une valeur numÃ©rique valide.
tou.thin.cycle.required=Cycle est nÃ©cessaire.
tou.thin.servicecharge.invalid=Frais de service doit Ãªtre une valeur numÃ©rique valide.
tou.thin.tax.invalid=ImpÃ´t doit Ãªtre une valeur numÃ©rique valide.
tou.thin.servicecharge.required=Frais de service est nÃ©cessaire.
tou.thin.servicecharge.positive=Frais de service doit Ãªtre positif.
tou.thin.no.tariff.data.available=Pas de donnÃ©es tarifaires disponibles pour Ãªtre mis Ã  jour et sauvegardÃ©es.
tou.thin.charges.none=Frais doivent Ãªtre capturÃ©s pour le tarif.
tou.thin.charges.invalid=Taux de frais doivent Ãªtre des valeurs numÃ©riques positives, valides.
tou.thin.specialdayscharges.none=Jour spÃ©cial doivent Ãªtre capturÃ©s pour le tarif.
tou.thin.specialdayscharges.invalid=Taux de jour spÃ©ciaux doivent Ãªtre des valeurs numÃ©riques positives, valides.
tou.thin.rates.none=Aucun taux entrÃ©
tou.thin.rates.invalid=Les taux doivent Ãªtre positifs et ne pas Ãªtre zÃ©ro

# Register Reading Tariffs
register.reading.billing.determinant.title=Facteurs de Facturation
register.reading.billing.determinant.help=SÃ©lectionnez les dÃ©terminants de facturation pour lesquels les taux de capture de cette structure de prix
register.reading.rates.button=Capturez les Taux
register.reading.rates.title=Taux
register.reading.billing.determinant.column.title=Facteur de Facturation
register.reading.error.no.tax=ImpÃ´t est requis.
register.reading.rates.none= Aucun taux entrÃ©
register.reading.rates.invalid=Les taux doivent Ãªtre positifs et ne pas Ãªtre zÃ©ro
register.reading.error.no.billingdet=SÃ©lectionnez au moins un Facteur de Facturation.
register.reading.no.tariff.data.available=Aucune donnÃ©e tarifaire disponible pour Ãªtre mise Ã  jour et sauvegardÃ©e.
register.reading.billing.det.change=Les dÃ©terminants de facturation dans la liste sÃ©lectionnÃ©e ne sont pas les mÃªmes que les frais courants pour ce tarif. Les frais prÃ©cÃ©dents seront supprimÃ©s, tous les frais doivent Ãªtre rÃ©inscrits. Continuer?
register.reading.servicecharge.descrip.required=La description des Frais de Service est requise.
register.reading.cannot.change.charge=Le tarif dÃ©jÃ  actif, ne peut pas changer les taux.

# Register Reading Information tab
register.reading.txn.label=Lectures de registre
register.reading.txn.description=Enregistrement Lectures pour ce compteur pour la pÃ©riode sÃ©lectionnÃ©e
register.reading.txn.timestamp=Horodatage
register.reading.txn.channel=Canal
register.reading.txn.determinant=Facteur
register.reading.txn.readingtype=Type de lecture du compteur
register.reading.txn.readingvalue=Valeur
register.reading.txn.error.enddate=La date de fin doit Ãªtre Ã©gale ou supÃ©rieure Ã  la date de dÃ©but.
register.reading.txn.filter=Filtrer
register.reading.txn.noreadings=Aucune indication de registre correspondante n'a Ã©tÃ© trouvÃ©e.

# AppSetting
appsetting.header=ParamÃ¨tres d'application
appsetting.title=ParamÃ¨tres actuels d'application 
appsetting.title.update=Mise Ã  jour paramÃ¨tre d'application
appsetting.name=ParamÃ¨tre  d'application
appsetting.field.name=Nom
appsetting.field.value=Valeur
appsetting.field.description=Description
appsetting.field.name.help=Nom du paramÃ¨tre d'application.
appsetting.field.value.help=La valeur Ã  appliquer pour ce paramÃ¨tre.
appsetting.field.description.help=Description du paramÃ¨tre d'application.
appsetting.name.duplicate=Nom en copie pour paramÃ¨tre d'application: {0}.
appsetting.error.new=ParamÃ¨tre de lâapplication {0} pas trouvÃ©. Veuillez contacter votre Administrateur SystÃ¨me.
appsetting.error.disconnect.greater.emergency.credit=Le crÃ©dit d'urgence doit Ãªtre supÃ©rieur ou Ã©gal Ã  DÃ©connectÃ©.
appsetting.error.disconnect.greater.reconnect=Le reconnexion doit Ãªtre supÃ©rieur ou Ã©gal Ã  DÃ©connectÃ©.
appsetting.error.disconnect.greater.both=La dÃ©connexion doit Ãªtre infÃ©rieure ou Ã©gale Ã  ReconnectÃ© ET infÃ©rieure ou Ã©gale Ã  un CrÃ©dit d'urgence
appsetting.error.maxvend.smaller.minvend=Le montant maximum de la vente doit Ãªtre supÃ©rieur ou Ã©gal au montant minimum de la vente
appsetting.error.emergency.credit.greater.low.balance=Le crÃ©dit d'urgence doit Ãªtre infÃ©rieur ou Ã©gal au faible solde.
appsetting.error.emergency.credit.errors=Le crÃ©dit d'urgence doit Ãªtre supÃ©rieur ou Ã©gal Ã  Disconnect ET infÃ©rieur ou Ã©gal Ã  Faible Solde.
appsetting.error.invalid.custom.status=L'Ã©tat du domaine personnalisÃ© n'est pas valide! Doit Ãªtre l'un OPTIONNEL, REQUIS OU NON DISPONIBLE.

demo.addmeterreadings.title.criteria=CritÃ¨res de lectures de compteurs
demo.addmeterreadings.interval=Intervalle de Lecture du compteur
demo.addmeterreadings.readingtypes=Types de lectures du compteur
demo.addmeterreadings.tariffcalc=Envoyez tariffCalcRequÃªte aprÃ¨s lectures
demo.addmeterreadings.error.paymentmode=Mode de paiement est nÃ©cessaire.
demo.addmeterreadings.error.meter=Entrez un numÃ©ro de compteur et sÃ©lectionnez l'une de la sÃ©lection disponible.
demo.addmeterreadings.error.meter.select=S'il vous plaÃ®t sÃ©lectionner un compteur de la sÃ©lection disponible.
demo.addmeterreadings.no.usagepoint1=Compteur n'a pas Point d'utilisation
demo.addmeterreadings.no.usagepoint2=Tariff Calc RequÃªte pas possible.
demo.addmeterreadings.no.usagepoint3=Impossible de lire le point d'utilisation pour le compteur. Veuillez informer le Support.
demo.addmeterreadings.error.start=Commencement est nÃ©cessaire.
demo.addmeterreadings.error.end=La fin est nÃ©cessaire.
demo.addmeterreadings.error.dates=DÃ©but doit Ãªtre avant la fin.
demo.addmeterreadings.error.end.future=La fin doit Ãªtre avant aujourd'hui.
demo.addmeterreadings.error.interval=Lecture Intervalle est nÃ©cessaire.
demo.addmeterreadings.error.types=Au moins un type de lecture est nÃ©cessaire.
demo.addmeterreadings.invalid.input=EntrÃ©e non valide pour ajouter des lectures de compteur.
demo.addmeterreadings.error.insert=Erreur insertion de la lecture du compteur.
demo.addmeterreadings.error.insert.fact=Erreur insertion du fait de lecture de compteur.
demo.addmeterreadings.error.insert.timeDim=Erreur insertion deTimeDim pour fait nouveau compteur de lecture.
demo.addmeterreadings.error.duplicates=Compteur a des lectures compteur existants pour la pÃ©riode en cours. Modifier date pÃ©riode ou choisir de supprimer les letures de compteurs existants.
demo.addmeterreadings.error.duplicate.Facts=Compteur a de lectures Faits compteur  pour la pÃ©riode en cours existant. Modifier date de pÃ©riode pou choisir de supprimer les lectures de compteurs existants. 
demo.addmeterreadings.minutes.15=15 minutes
demo.addmeterreadings.minutes.30=30 minutes
demo.addmeterreadings.minutes.60=60 minutes

demo.addsupermeterreadings.link=[DEMO] Ajouter les rÃ©sultats de lectures de compteur d'Ã©quilibrage
demo.addsupermeterreadings.header=Ajouter les rÃ©sultats de lectures de compteur d'Ã©quilibrage
demo.addsupermeterreadings.title=Ãquilibrage de lectures compteur
demo.addsupermeterreadings.title.criteria=CritÃ¨res de l'Ãquilibrage de lectures compteur
demo.addsupermeterreadings.readingtype=Type de lecture de compteur 
demo.addsupermeterreadings.variation=Variation de lecture de compteur 
demo.addsupermeterreadings.variations=Variations de compteur d'Ã©quilibrage
demo.addsupermeterreadings.variations.help=Ãquilibrer les lectures de compteurs de compteurs correspondra au total de ses sous compteurs. Vous pouvez spÃ©cifier les heures de la journÃ©e et un pourcentage qui sera utilisÃ© pour rÃ©duire mÃ¨tre la lecture du compteur d'Ã©quilibrage de sorte qu'il se distingue de ses sous compteurs.
demo.addsupermeterreadings.hour=Heure
demo.addsupermeterreadings.supermeter=Compteur Ãquilibrage 
demo.addsupermeterreadings.super.delete.readings=Supprimer les lectures existant du compteur d' Ã©quilibrage 
demo.addsupermeterreadings.subs.regen.readings=Re-gÃ©nÃ©rer Lectures de sous compteurs
demo.addsupermeterreadings.error.supermeter=Compteur Ãquilibrage est nÃ©cessaire.
demo.addsupermeterreadings.error.type=Type de Lecture de compteur est nÃ©cessaire.
demo.addsupermeterreadings.hour.required=Heure du jour est nÃ©cessaire.
demo.addsupermeterreadings.percentage.required=Pourcentage est nÃ©cessaire.
demo.addsupermeterreadings.variation.duplicate=Heure est dÃ©jÃ  utilisÃ© dans une variante existante.
demo.addsupermeterreadings.invalid.input=EntrÃ©e non valide pour gÃ©nÃ©rer des lectures de compteur du compteur d'Ã©quilibrage.
demo.addsupermeterreadings.invalid.input.submeters=Pas de sous compteurs disponibles pour compteur d'Ã©quilibrage.
demo.addsupermeterreadings.success=Lectures de compteurs d'Ãquilibrage ont Ã©tÃ© ajoutÃ©es avec succÃ¨s.
demo.addsupermeterreadings.error.super.duplicates=Compteur d'Ã©quilibrage a de lecture des compteurs existants pour la pÃ©riode en cours. Modifier la date pÃ©riode ou choisir de supprimer les lectures de compteurs d'Ã©quilibrage existant.
demo.addsupermeterreadings.error.sub.duplicates=Il y a des lectures de compteurs existants pour la pÃ©riode en cours. Modifier la date de pÃ©riode ou sÃ©lectionnez pour rÃ©gÃ©nÃ©rer les lectures de compteurs.

export.error=Erreur: Impossible d'exporter des donnÃ©es avec succÃ¨s.
export.error.nodata=Erreur: Aucune donnÃ©e Ã©tait disponible pour l'exportation.
export.error.nofile=Erreur: Le nom de fichier n'a pas pu Ãªtre dÃ©terminÃ©e.
export.field.meter=Compteur
export.field.metertype=Type de compteur
export.field.date=Date de lecture
export.field.start=Heure de dÃ©but
export.field.end=Heure de fin
export.field.reading=Lecture
export.denied=AccÃ¨s refusÃ©. Vous devez Ãªtre connectÃ© pour accÃ©der Ã  cette fonctionnalitÃ©.
export.denied.group=AccÃ¨s refusÃ©. Vous ne faites pas partie du mÃªme groupe que le compteur.
export.singlemeter.filename.prefix=LecturesCompteurs-Compteur
export.energybalancing.filename.prefix=CompteurÃquilibrag-CompteurSuper

taskschedule.title.add=Ajouter un planification des tÃ¢ches
taskschedule.title.update=Mettre Ã  jour un planning de tÃ¢ches
taskschedule.header=Listes de tÃ¢ches actuelles
taskschedule.title=Horaires des tÃ¢ches
taskschedule.title.single=Horaire des tÃ¢ches
taskschedule.type=Horaire des tÃ¢ches
taskschedule.name=Nom de l'horaire des tÃ¢ches
taskschedule.name.help=Indiquez un nom pour identifier votre horaire de tÃ¢che.
taskschedule.active=Actif
taskschedule.active.help=Que le calendrier des tÃ¢ches est actif et se dÃ©roulera comme prÃ©vu.
taskschedule.schedule=Date/Heure de tÃ¢che
taskschedule.schedule.help=SpÃ©cifiez lorsque le calendrier de tÃ¢che sera exÃ©cutÃ©e en sÃ©lectionnant une option appropriÃ©e et remplir les champs.
taskschedule.status=Ãtat
taskschedule.scheduledtask=Type de tÃ¢che
taskschedule.schedule.daily=Une fois par jour
taskschedule.schedule.weekly=Une fois par semaine
taskschedule.schedule.monthly=Une fois par mois
taskschedule.schedule.repeatedly=RÃ©pÃ©tition par Minutes / Heures
taskschedule.hours=Heures
taskschedule.minutes=Minutes
taskschedule.time=Temps
taskschedule.day=Jour de la semaine
taskschedule.daymonth=Jour du mois
taskschedule.days=Dimanche, lundi, mardi, mercredi, jeudi, vendredi, samedi
taskschedule.error.time=Le temps est un champ obligatoire.
taskschedule.error.day=Jour est nÃ©cessaire.
taskschedule.error.daymonth=Jour est nÃ©cessaire.
taskschedule.users=Utilisateurs de TÃ¢ches
taskschedule.error.no.user=Rechercher un utilisateur d'abord, puis les ajouter en tant qu'utilisateur de la tÃ¢che.
taskschedule.error.taskusers=Au moins un utilisateur de tÃ¢che ou un client est nÃ©cessaire.
taskschedule.error.customer.notselected=Le nom du client ne correspond pas au nom du client sÃ©lectionnÃ©. Tapez un nom de famille Ã  sÃ©lectionnÃ© un client valide. 
taskschedule.user.noemail=L'utilisateur ne possÃ¨de pas d'adresse e-mail valide. Utilisateurs de tÃ¢ches ont besoin d'avoir une adresse e-mail valide pour recevoir des tÃ¢ches planifiÃ©es emails.
taskschedule.meter.single=Compteur Simple
taskschedule.meter.super=Compteur Super
taskschedule.meter.readingtype=Type de lecture compteur
taskschedule.timeperiods=PÃ©riode de temps
taskschedule.class.error.singlemeter.none=Compteur Simple est nÃ©cessaire.
taskschedule.class.error.singlemeter.select=Commencer en tapant un numÃ©ro de compteur et sÃ©lectionnez un compteur de la liste.
taskschedule.class.error.type=Type de Lecture de compteur est nÃ©cessaire.
taskschedule.class.error.time=PÃ©riode de temps est nÃ©cessaire.
taskschedule.class.error.supermeter=Compteur Super est nÃ©cessaire.
taskschedule.class.error.hours=Heures doit Ãªtre un numÃ©ro positif.
taskschedule.taskclass.previous.day=Jour prÃ©cÃ©dent
taskschedule.taskclass.previous.week=Semaine prÃ©cÃ©dente
taskschedule.taskclass.previous.month=Mois prÃ©cÃ©dent
taskschedule.error.taskschedule.save=Erreur lors de l'enregistrement de la Planification de tÃ¢che.
taskschedule.error.taskschedule.none=Pas de Planification de tÃ¢che spÃ©cifiÃ©.
taskschedule.error.scheduledtask.save=Erreur lors de l'enregistrement de la tÃ¢che planifiÃ©e.
taskschedule.error.scheduledtaskuser.delete=Erreur lors de la suppression de l'utilisateur pour la tÃ¢che planifiÃ©e.
taskschedule.error.scheduledtaskuser.save=Erreur lors de l'enregistrement de l'utilisateur pour la tÃ¢che planifiÃ©e.
taskschedule.error.scheduling=Erreur lors de la planification de la planification des tÃ¢ches.
taskschedule.error.descheduling=Erreur lors du dÃ©sÃ©lection de plannification des tÃ¢ches existante
taskschedule.every=Chaque
taskschedule.of=NumÃ©ro de
taskschedule.repeatedly.error.number=Un chiffre positif est nÃ©cessaire.
taskschedule.repeatedly.error.units=SÃ©lectionnez un type d'intervalle.
taskschedule.all.supermeters=Tous les compteurs Super

scheduledtask.title=TÃ¢ches planifiÃ©es
scheduledtask.header=Actuel TÃ¢ches planifiÃ©es
scheduledtask.type=TÃ¢che planifiÃ©e
scheduledtask.field.name=Nom de la tÃ¢che planifiÃ©e
scheduledtask.field.name.help=Indiquez un nom pour identifier votre horaire de travail.
scheduledtask.field.class=Type de tÃ¢che
scheduledtask.field.class.help=SÃ©lectionnez le type de tÃ¢che Ã  exÃ©cuter sur la date et l'heure prÃ©vue.
scheduledtask.title.add=Ajouter une tÃ¢che planifiÃ©e
scheduledtask.title.update=Mettre Ã  jour une tÃ¢che planifiÃ©e
scheduledtask.previous.hours=Heures prÃ©cÃ©dentes
scheduledtask.customer.name=Client
scheduledtask.customer.name.help=Tapez le nom d'un client et sÃ©lectionnez le client correspondant qui devrait Ãªtre liÃ©e Ã  cette tÃ¢che planifiÃ©e.
scheduledtask.error.delete.none.selected=TÃ¢che Non programmÃ© est sÃ©lectionnÃ©.
scheduledtask.delete.confirm=Etes-vous sÃ»r de vouloir supprimer la tÃ¢che planifiÃ©e?
scheduledtask.error.delete.none=Aucune tÃ¢che planifiÃ©e est spÃ©cifiÃ©e.
scheduledtask.error.delete=Impossible de supprimer la tÃ¢che planifiÃ©e.
scheduledtask.deleted=La tÃ¢che planifiÃ©e a Ã©tÃ© supprimÃ©e avec succÃ¨s.

scheduledtask.email.subject.taskschedule=Gestion Des Compteurs - planification des tÃ¢ches:
scheduledtask.email.subject.scheduledtask= TÃ¢che: 
scheduledtask.email.message.taskschedule=Planification de tÃ¢che: 
scheduledtask.email.message.scheduledtask=TÃ¢che: 
scheduledtask.email.message.meter=Compteur: 
scheduledtask.email.message.start=Date DÃ©but: 
scheduledtask.email.message.end=Date de fin: 
scheduledtask.email.message=Veuillez trouver ci-joint la sortie de votre tÃ¢che.
scheduledtask.email.message.supermeter=Compteur Super: 

password.change.header=Changer mot de passe
password.old=Mot de passe actuel
password.new=Nouveau mot de passe
password.confirm=Confirmer le nouveau mot de passe
password.old.required=Mot de passe actuel est nÃ©cessaire.
password.new.required=Nouveau mot de passe est nÃ©cessaire.
password.confirm.required=Confirmer un nouveau mot de passe est nÃ©cessaire.
password.new.nonmatching=Les mots de passe nouveaux et confirmÃ©s ne correspondent pas.
password.changed=Mot de passe a Ã©tÃ© changÃ© avec succÃ¨s.
password.expiry=Votre mot de passe expire dans in {0} jour(s).
password.ldap=Votre utilisateur est authentifiÃ© via LDAP donc incapable de changer le mot de passe ici.
password.login.expired=Vous devez changer votre mot de passe, car il a expirÃ©.
password.login.reset=Vous devez changer votre mot de passe, car il a Ã©tÃ© rÃ©initialisÃ©.
password.user.change=Vous devez changer votre mot de passe afin de pouvoir utiliser l'application.
password.locked=Votre compte a Ã©tÃ© bloquÃ©.
password.validate.current=Mot de passe actuel incorrect.
password.validation.minUppercase=Mot de passe doit avoir au moins {0} CaractÃ¨re majuscule(s).
password.validation.minDigits=Mot de passe doit avoir au moins {0} chiffres.
password.validation.minLength=Mot de passe doit avoir une longueur minimum de {0} chiffres.
password.validation.maxLength=Mot de passe doit avoir une longueur maximum de {0} chiffres.
password.validation.numHistory=Vous ne pouvez pas utiliser un mot de passe qui correspond Ã  l'un des derniers {0} ceux.
password.required=Vous ne pouvez pas utiliser un mot de passe qui correspond Ã  l'un des derniers
username.required=Nom d'utilisateur est nÃ©cessaire.
logged.in=Vous avez Ã©tÃ© connectÃ© avec succÃ¨s.
login.session.timedout=Votre session a expirÃ©. S'il vous plaÃ®t vous connecter Ã  nouveau pour continuer Ã  utiliser le site.

# Dashboard
dashboard.title=Tableau de bord 
dashboard.meter.count.title=Nombre de compteurs
dashboard.meter.count.description=Le nombre actuel de compteurs dans le systÃ¨me, selon le modÃ¨le de compteur.
dashboard.meter.model.name=ModÃ¨le de compteur
dashboard.meter.count=#

## Custom fields
user.custom.fields.title=Les champs personnalisÃ©s
user.custom.fields.error.get=Erreur de base de donnÃ©es pour obtenir des paramÃ¨tres d'application de champ personnalisÃ©s pour {0}! Contactez le Support.
user.custom.fields.error.unknown.setting=ParamÃ¨tre d'application de champ personnalisÃ© {0} pas pris en charge. Informer le Support.
user.custom.field.status.optional=OPTIONNEL
user.custom.field.status.required=REQUIS
user.custom.field.status.unavailable=NON DISPONIBLE

#Import Data
import.data.header=Importer les donnÃ©es
import.meters.title=Importer les compteurs
import.data.metermodel.help=Si un modÃ¨le de compteur n'est pas spÃ©cifiÃ© dans le fichier en cours d'importation, le modÃ¨le sÃ©lectionnÃ© dans la liste dÃ©roulante sera utilisÃ© Ã  la place
import.data.metermodel=ModÃ¨le de compteur par dÃ©faut
import.data.file.help=SÃ©lectionnez un fichier contenant des informations de compteur dans le format csv spÃ©cifiÃ© pour l'importation dans le systÃ¨me  
import.data.file=Fichier des compteurs sÃ©lectionnÃ©s
import.data.button=Importer les compteurs 
import.meters.heading=Importer compteurs au magasin dispositif
import.meters.description=SÃ©lectionnez le fichier CSV contenant des informations de compteur pour l'importation dans le systÃ¨me de gestion de compteur. <br/> SÃ©lectionnez un modÃ¨le de compteur par dÃ©faut dans la liste dÃ©roulante. Il sera utilisÃ© quand un modÃ¨le de compteur est pas spÃ©cifiÃ© dans le fichier importÃ©

timezone.change=Changer le fuseau horaire
timezone.header=Mettre en place l'actuel fuseau horaire
timezone.label=Fuseau horaire

############################################################
#### IpayXml Messages for account balance notifications ####
############################################################
# The defaultAccountAdjustmentProcessor.notification messages have the following arguments:
# arg0=customer.title
# arg1=customer.initials
# arg2=customer.firstnames
# arg3=customer.surname
# arg4=meter number
# arg5=customer agreement ref
# arg6=usage point name
# arg7=account balance
# arg8=low balance threshold
# arg9=emergency credit threshold
# arg10=disconnect threshold
# arg11=reconnect threshold
# currency format example for account balance: {7,number,currency}

defaultAccountAdjustmentProcessor.notification.disconnect.email.subject=Le solde du compte s''est Ã©coulÃ© pour {6}
defaultAccountAdjustmentProcessor.notification.disconnect.email.message=ChÃ¨re Client,\n\nVotre compteur sera dÃ©connectÃ©.\n\nL''Ã©tat de votre compte est: \n Solde du Compte: {7,number,currency}\n Seuil de notification du bas niveau de bilan: {8,number,currency}\n\nCordialement,\nÃquipe de soutien
defaultAccountAdjustmentProcessor.notification.disconnect.sms.message=Solde pour {6} s''est Ã©puisÃ© et sera dÃ©connectÃ©. Le solde est {7,number,currency} ââCordialement, Ã©quipe de soutien

defaultAccountAdjustmentProcessor.notification.lowBalance.email.subject=Solde du compte faible pour {6}
defaultAccountAdjustmentProcessor.notification.lowBalance.email.message=ChÃ¨re Client,\n\nL''Ã©tat de votre compte est: \n Solde du Compte: {7,number,currency}\n Seuil de notification du bas niveau de bilan: {8,number,currency}\n\nCordialement,\nÃquipe de soutien
defaultAccountAdjustmentProcessor.notification.lowBalance.sms.message=Solde pour {6} est faible. Solde est {7,number,currency} Cordialement, Ãquipe de soutien

#database actions
db.action.update=Mettre Ã  jour
db.action.insert=InsÃ©rer
db.action.delete=Effacer

#MDC controltypes
controltype.connect=CONNECTÃ
controltype.disconnect=DÃCONNECTÃ
controltype.disconnect.enable=DÃCONNECTÃ_ACTIVER
controltype.pan.display=PAN_AFFICHAGE
controltype.sync.balance=SYNC_SOLDE
controltype.adjust.balance=AJUSTER_SOLDE
controltype.power.limit=LIMITE DE PUISSANCE
