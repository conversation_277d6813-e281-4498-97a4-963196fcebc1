################
### Formats ####
################
# Notes:
# A blank space is \u0020

# Currency formats
currency.symbol=â¬
unit.symbol=c
currency.pattern=#,##0.00
currency.separator.grouping=,
currency.separator.decimal=.

# Decimal number formats
decimal.pattern=#,##0.#####
decimal.separator.grouping=\u0020
decimal.separator.decimal=.

# Date and Time formats
datetime.pattern=dd/MM/yyyy HH:mm:ss.S
date.pattern=dd/MM/yyyy
time.pattern=HH:mm:ss

# Cell Phone Number Format
# eg. [messages.properties: ] cellPhone.pattern.description = Le champ du numÃ©ro de tÃ©lÃ©phone pour les sms doit Ãªtre au format international, c'est-Ã -dire commencer par un +. Vous pouvez utiliser des espaces, des parenthÃ¨ses (), des tirets et des pointsÂ âÂ ceux-ci sont supprimÃ©s et le numÃ©ro de tÃ©lÃ©phone rÃ©sultant (sans le +) doit Ãªtre composÃ© de 11 chiffres. Pas de caractÃ¨res alphabÃ©tiques.
cellphone.pattern=^\\+{1}[0-9]{4,25}$
cellphone.placeholder=+229123456789
