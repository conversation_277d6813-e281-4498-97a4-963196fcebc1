################
### Formats ####
################
# Notes:
# A blank space is \u0020

# Currency formats
currency.symbol=$
unit.symbol=c
currency.pattern=#,##0.00
currency.separator.grouping=,
currency.separator.decimal=.

# Decimal number formats
decimal.pattern=#,##0.#####
decimal.separator.grouping=\u0020
decimal.separator.decimal=.

# Date and Time formats
datetime.pattern=dd/MM/yyyy HH:mm:ss.S
date.pattern=dd/MM/yyyy
time.pattern=HH:mm:ss

# Cell Phone Number Format
# eg. [messages.properties: ] cellPhone.pattern.description = The phone number field for smses must be in international format, i.e. start with a +. You may use whitespaces, parentheses (), hypens and periods - these are stripped out and the resulting phone number (excl. the +) must minimum 4 digits, maximum 25 in length, depending on your locale. Please refer the placeholder.
cellphone.pattern=^\\+{1}[0-9]{4,25}$
cellphone.placeholder=+27123456789